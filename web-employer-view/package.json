{"name": "web-employer-view", "version": "1.0.0", "scripts": {"dev": "vue-cli-service serve --open", "dev:test": "vue-cli-service serve --open --mode dev_test", "dev:prod": "vue-cli-service serve --open --mode dev_prod", "build:dev": "vue-cli-service build --mode prod_dev", "build:test": "vue-cli-service build --mode prod_test", "build:prod": "vue-cli-service build --mode production", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"axios": "0.18.1", "clipboard": "^2.0.6", "core-js": "^3", "dayjs": "^1.8.17", "js-cookie": "2.2.0", "jsencrypt": "^3.0.0-rc.1", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "pdfjs-dist": "^2.13.216", "tinymce": "^5.8.2", "vue": "2.6.10", "vue-i18n": "^8.15.0", "vue-router": "3.0.6", "vue-sticky": "^3.3.4", "vuex": "3.1.0"}, "devDependencies": {"@babel/core": "7.0.0", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "chalk": "2.4.2", "connect": "3.6.6", "element-ui": "^2.13.2", "eslint": "5.15.3", "eslint-plugin-vue": "5.2.2", "html-webpack-plugin": "3.2.0", "mockjs": "1.0.1-beta3", "runjs": "^4.3.2", "sass": "^1.27.1", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.13.2", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}