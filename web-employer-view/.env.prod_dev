# just a flag
NODE_ENV = 'production'

# base api
VUE_APP_BASE_API = 'http://10.10.10.104:8080'

VUE_APP_FLAG = 'dev'
VUE_APP_URL = 'http://10.10.10.104/dev/employer/'
VUE_APP_FILE_URL = 'http://10.10.10.107:8888/'


# vue-cli uses the VUE_CLI_BABEL_TRANSPILE_MODULES environment variable,
# to control whether the babel-plugin-dynamic-import-node plugin is enabled.
# It only does one thing by converting all import() to require().
# This configuration can significantly increase the speed of hot updates,
# when you have a large number of pages.
# Detail:  https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/babel-preset-app/index.js
