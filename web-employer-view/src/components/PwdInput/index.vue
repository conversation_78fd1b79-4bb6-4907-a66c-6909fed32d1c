<template>
  <div class="pwd-input-box">
    <input
      class="pwd-input"
      ref="input"
      :autofocus="autofocus"
      :value="pwd"
      @input="handleInput"
      @focus="onFocus"
      @blur="onBlur"
    />
    <ul
      class="pwd-input-list"
      :class="{'pwd-input-active': isFocus}"
      @click="handleUlClick">
      <li
        class="pwd-item"
        v-for="(item, index) in length"
        :key="index">
        <div
          class="pwd-item-show"
          v-show="pwd[item-1] !== undefined"></div>
      </li>
    </ul>
  </div>
</template>

<script>

export default {
  name: "pwd-input",
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    length: {
      type: Number,
      default: 6
    },
    autofocus: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      pwd: '',
      isFocus: false,
    }
  },
  methods: {
    focus() {
      this.getInput().focus()
    },
    getInput() {
      return this.$refs.input
    },
    handleInput(e) {
      let pwd = e.target.value.replace(/[^(\d)]/g, '')
      pwd = pwd.slice(0, this.length)
      this.getInput().value = this.pwd = pwd
      this.$emit('input', pwd)
    },
    onFocus(e) {
      this.isFocus = true
      this.$emit('focus', e)
    },
    onBlur(e) {
      this.isFocus = false
      this.$emit('blur', e)
    },
    handleUlClick() {
      this.focus()
    },
    clear() {
      this.pwd = ''
      this.$emit('input', '')
    }
  },
}
</script>

<style
  scoped
  lang="scss">
.pwd-input-box {
  position: relative;
  overflow: hidden;
}

.pwd-input {
  position: absolute;
  left: -99999px;
  top: -99999px;
  width: 0;
  height: 0;
}

.pwd-input-list {
  display: inline-block;
  margin: 0;
  padding: 5px;
  border: 1px solid #ccc;
  border-radius: 5px;
  list-style: none;
  transition: all .2s linear;
  line-height: 24px;


  .pwd-item {
    display: inline-block;
    vertical-align: middle;
    height: 24px;
    width: 24px;
    text-align: center;
    padding: 0;
    border-right: 1px solid #ccc;

    &:last-child {
      border-right: none;
    }

  }

  .pwd-item-show {
    position: relative;
    height: 100%;

    &::before {
      content: '●';
      display: block;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

.pwd-input-active {
  box-shadow: 0 0 6px 2px $mainColor;
}
</style>
