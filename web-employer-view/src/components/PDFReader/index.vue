<template>
  <div
    class="pdf-reader-box"
    :style="readerStyle"
  >
    <div
      class="pdf-func-box"
      v-if="showFunc"
    >
      <i
        class="el-icon-minus pdf-icon"
        @click="prevPage"
      ></i>
      <div class="pdf-page">{{ activePage }} / {{ numPages }}</div>
      <i
        class="el-icon-plus pdf-icon"
        @click="nextPage"
      ></i>
    </div>
    <div class="pdf-reader-view">
      <Page
        ref="page"
        v-for="item in numPages"
        :key="item"
        :page="item"
        :pdf="pdf"
        @vp-change="onVpChange"
      >
        <slot
          v-if="isInit"
          :name="'mark-' + item"
          slot="mark"
        ></slot>
      </Page>
    </div>
  </div>
</template>

<script>
import { debounce } from "@/utils";
import Page from "./Page";

export default {
  name: "PDFReader",
  components: {
    Page,
  },
  props: {
    src: {
      type: String,
      default: "",
    },
    width: {
      type: [Number, String],
      default: "700",
    },
    height: {
      type: [Number, String],
      default: "",
    },
    showFunc: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      pdf: null,
      numPages: 0,
      activePage: 0,

      canvasWidth: 0,

      isInit: false, // 首次渲染是否成功

      heightList: [], // 每页高度
    };
  },
  computed: {
    canvas() {
      return this.$refs.canvas;
    },
    readerStyle({ width, height }) {
      return {
        position: "relative",
        width: width + "px",
        height: height + "px",
        margin: "0 auto",
        background: "#ddd",
      };
    },
  },
  mounted() {
    this.tempCount = 0;
    this.loadPDF();

    // 针对滚动时改变activePage
    if (this.showFunc) {
      window.addEventListener("scroll", debounce(this.scrollHandle, 50), {
        passive: true,
      });
    }
  },
  watch: {
    activePage(val) {
      this.$emit("page-change", val);
    },
  },
  methods: {
    async loadPDF() {
      if (!this.src) {
        setTimeout(this.loadPDF, 100);
        return;
      }
      let PDFJS = require("pdfjs-dist/legacy/build/pdf.min.js");
      PDFJS.workerSrc = require("pdfjs-dist/legacy/build/pdf.worker.entry.js");
      const pdfLoadingTask = await PDFJS.getDocument({
        url: this.src,
        cMapUrl: "https://cdn.jsdelivr.net/npm/pdfjs-dist@2.6.347/cmaps/",
        cMapPacked: true,
      });
      this.pdf = await pdfLoadingTask.promise;
      this.numPages = this.pdf.numPages;
      this.activePage = 1;
    },
    prevPage() {
      if (!this.isInit || this.activePage == 1) return;
      this.activePage--;
      this.scrollTo(this.activePage);
    },
    nextPage() {
      if (!this.isInit || this.activePage == this.numPages) return;
      this.activePage++;
      this.scrollTo(this.activePage);
    },
    scrollTo(page) {
      let activePageElm = this.$refs.page[page - 1].$el;
      let y = activePageElm.offsetTop;
      window.scroll({
        top: y,
        behavior: "smooth",
      });
    },
    scrollHandle() {
      if (!this.isInit) return;
      let height = document.documentElement.scrollTop;
      if (height < this.heightList[0]) {
        this.activePage = 1;
        return;
      }
      for (let i = 0; i < this.heightList.length; i++) {
        if (height > this.heightList[i] && height <= this.heightList[i + 1]) {
          this.activePage = i + 2;
          break;
        }
      }
    },
    onVpChange({ vp, page }) {
      this.$set(this.heightList, page, vp.height);
      this.$emit("vp-change", { vp, page });
      this.tempCount++;
      if (this.tempCount === this.numPages) {
        this.isInit = true;
        this.$emit("init-finish", { numPages: this.numPages });

        this.heightList.shift();

        for (let i = 1; i < this.heightList.length; i++) {
          this.heightList[i] =
            Math.floor(
              this.heightList[i - 1] * 100 + this.heightList[i] * 100
            ) / 100;
        }
      }
    },
  },
};
</script>

<style
  lang="scss"
  scoped>
.pdf-func-box {
  position: sticky;
  top: 0;
  display: flex;
  align-items: center;
  width: 100%;
  height: 50px;
  padding: 16px 0;
  border-bottom: 1px solid #ddd;
  background: #fff;
  font-size: 16px;
  z-index: 10;

  .pdf-icon {
    flex: 1;
    text-align: center;
  }
}

.pdf-canvas-container {
  position: relative;
  margin: 0 auto;

  .mark {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
  }
}

.pdf-reader-box {
  position: relative;
  margin: 0 auto;
  background: #ddd;
}

</style>
