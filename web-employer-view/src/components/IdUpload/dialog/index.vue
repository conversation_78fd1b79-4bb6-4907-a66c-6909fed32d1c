<template>
  <el-dialog
    :visible.sync="visible"
    title="上传身份证"
    :before-close="close"
  >
    <el-form label-width="100px">
      <el-form-item label="证件照：">
        <id-upload
          :frontUrl.sync="frontUrl"
          :backUrl.sync="backUrl"
        ></id-upload>
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <el-button @click="confirm" type="primary">确认</el-button>
      <el-button @click="close">取消</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import IdUpload from '../index'
  export default {
    name: 'id-upload-dialog',
    components: {
      IdUpload
    },
    data() {
      return {
        visible: false,

        frontUrl: '',
        backUrl: '',
      }
    },
    methods: {
      toggle() {
        this.visible = !this.visible
      },
      close() {
        this.toggle()
        this.frontUrl = '';
        this.backUrl = '';
      },
      confirm() {
        const { frontUrl, backUrl } = this;
        if (!frontUrl || !backUrl) {
          return this.$message.error('请上传身份证图片')
        }
        this.$emit('change', {
          frontUrl,
          backUrl,
        });
        this.close();
      }
    },
  }
</script>

<style scoped lang="scss">

</style>
