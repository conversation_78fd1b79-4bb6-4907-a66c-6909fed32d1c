<template>
  <div v-if="!selfForm">
    <div class="color-gray">{{labelBack}}</div>
    <el-upload
      v-toggle="idCardBackUrl"
      key="idCardBackUrl"
      class="upload-demo"
      :action="baseUrl + '/file/upload'"
      :headers="uploadHeader"
      list-type="picture-card"
      accept=".png,.gif,.jpg,.bmp"
      :limit="1"
      :file-list="idCardBackUrl"
      :before-upload="beforeUpload"
      :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardBackUrl')"
      :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardBackUrl')"
      :on-preview="handlePreview"
    >
      <i class="el-icon-plus avatar-uploader-icon"></i>
      <template v-slot:tip>
        <el-image v-show="showExample && idCardBackUrl.length == 0" :src="backDemo" style="height: 148px; margin-left: 8px; vertical-align: top;"></el-image>
      </template>
    </el-upload>
    <div class="color-gray">{{labelFront}}</div>
    <el-upload
      v-toggle="idCardFrontUrl"
      key="idCardFrontUrl"
      class="upload-demo"
      :action="baseUrl + '/file/upload'"
      :headers="uploadHeader"
      list-type="picture-card"
      accept=".png,.gif,.jpg,.bmp"
      :limit="1"
      :file-list="idCardFrontUrl"
      :before-upload="beforeUpload"
      :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardFrontUrl')"
      :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardFrontUrl')"
      :on-preview="handlePreview"
    >
      <i class="el-icon-plus avatar-uploader-icon"></i>
      <template v-slot:tip>
        <el-image v-show="showExample && idCardFrontUrl.length == 0" :src="frontDemo" style="height: 148px; margin-left: 8px; vertical-align: top;"></el-image>
      </template>
    </el-upload>
  </div>
  <div v-else>
    <el-form-item :label="labelBack">
      <el-upload
        v-toggle="idCardBackUrl"
        key="idCardBackUrl"
        class="upload-demo"
        :action="baseUrl + '/file/upload'"
        :headers="uploadHeader"
        list-type="picture-card"
        accept=".png,.gif,.jpg,.bmp"
        :limit="1"
        :file-list="idCardBackUrl"
        :before-upload="beforeUpload"
        :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardBackUrl')"
        :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardBackUrl')"
        :on-preview="handlePreview"
      >
        <i class="el-icon-plus avatar-uploader-icon"></i>
        <template v-slot:tip>
          <el-image v-show="showExample && idCardBackUrl.length == 0" :src="backDemo" style="height: 148px; margin-left: 8px; vertical-align: top;"></el-image>
        </template>
      </el-upload>
    </el-form-item>
    <el-form-item :label="labelFront">
      <el-upload
        v-toggle="idCardFrontUrl"
        key="idCardFrontUrl"
        class="upload-demo"
        :action="baseUrl + '/file/upload'"
        :headers="uploadHeader"
        list-type="picture-card"
        accept=".png,.gif,.jpg,.bmp"
        :limit="1"
        :file-list="idCardFrontUrl"
        :before-upload="beforeUpload"
        :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardFrontUrl')"
        :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardFrontUrl')"
        :on-preview="handlePreview"
      >
        <i class="el-icon-plus avatar-uploader-icon"></i>
        <template v-slot:tip>
          <el-image v-show="showExample && idCardFrontUrl.length == 0" :src="frontDemo" style="height: 148px; margin-left: 8px; vertical-align: top;"></el-image>
        </template>
      </el-upload>
    </el-form-item>
  </div>
</template>

<script>
  import backDemo from '@/assets/example-2.png'
  import frontDemo from '@/assets/example-3.png'

  export default {
    name: 'id-upload',
    props: {
      frontUrl: {
        type: String,
        default: '',
      },
      backUrl: {
        type: String,
        default: '',
      },
      labelFront: {
        type: String,
        default: '国徽面',
      },
      labelBack: {
        type: String,
        default: '人像面',
      },
      selfForm: { // 是否在el-form表单内
        type: Boolean,
        default: false
      },
      showExample: {
        type: Boolean,
        default: true
      }
    },
    data() {
      return {
        idCardFrontUrl: [],
        idCardBackUrl: [],

        backDemo,
        frontDemo,
      }
    },
    watch: {
      frontUrl: {
        immediate: true,
        async handler(val) {
          if (!this.idCardFrontUrl.length && val) {
            const frontUrlMsg = await this.formatFileMsg(val)
            this.idCardFrontUrl = [{
              url: frontUrlMsg.fileUrl,
              origin: val,
            }];
          } else if (!val) {
            this.idCardFrontUrl = []
          }
        }
      },
      backUrl: {
        immediate: true,
        async handler(val) {
          if (!this.idCardBackUrl.length && val) {
            const backUrlMsg = await this.formatFileMsg(val)
            this.idCardBackUrl = [{
              url: backUrlMsg.fileUrl,
              origin: val,
            }];
          } else if (!val) {
            this.idCardBackUrl = [];
          }
        }
      },
    },
    methods: {
      beforeUpload(file) {
        this.$emit('beforeUpload')
        return this.validateUploadFile({
          img: true,
          size: 6,
        })(file);
      },
      async handleSuccess(response, file, fileList, param) {
        param = param.replace('idCard', '');
        const start = param.slice(0, 1).toLowerCase();
        const end = param.slice(1);
        const event = `update:${start + end}`;
        this.$emit(event, response.data);
      },
      handleRemove(file, fileList, param) {
        this[param] = fileList;
        param = param.replace('idCard', '');
        const start = param.slice(0, 1).toLowerCase();
        const end = param.slice(1);
        const event = `update:${start + end}`;
        this.$emit(event, '');
        this.$emit('remove');
      },
      async handlePreview(file) {
        let url = file.response ? file.response.data : file.origin
        const fileMsg = await this.formatFileMsg(url);
        this.$preview(fileMsg.fileUrl);
      }
    },
  }
</script>

<style scoped lang="scss">

</style>
