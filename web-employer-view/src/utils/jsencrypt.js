import { JSEncrypt } from 'jsencrypt';

export const encryptParam = param => {
  const jsencrypt = new JSEncrypt();
  if (!param.publicKey) {
    return ''
  }
  jsencrypt.setPublicKey(param.publicKey);
  const RSA_encrypt = jsencrypt.encrypt(param.pwd);
  return RSA_encrypt;
};

let LOING_RSA_PUBLIC_KEY = '';
if (process.env.VUE_APP_FLAG === 'production') {
  LOING_RSA_PUBLIC_KEY = 
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwquhsGwHEtWHrf9lk56ZkVqaD25goEuq7UGrQUqUejVwPrFVHu9PtnkOzKPdiAGLVBUF+DmRDW6J62y/O9zBgWKxd9udkXygxhhXhs5swK+VVFt27lQ1AR6g/N0t9HCoejscLY732Ftx5tdmtB7Pfwdx2+0YffQTRkQLsQKeWcwIDAQAB';
} else {
  LOING_RSA_PUBLIC_KEY = 
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2/fpV6Up2/o1JcU16FU3tOUCs9pAoOHyBER+tM19TVWvegn1lmMOsvSvUCiK0fSrwofX2GUV2HSrpxfPjGt4wliAj6mZM8rABy2Y5JirWmJAjyOarcwjj9Ka9Q7im7J9I6sKMs5UWNyUx732oJzsCJ84wa2E4AZvFHwgPBIGgTwIDAQAB';
}


export const encryptFileParam = param => {
  const jsencrypt = new JSEncrypt();
  jsencrypt.setPublicKey(LOING_RSA_PUBLIC_KEY);
  const RSA_encrypt = jsencrypt.encrypt(param);
  return RSA_encrypt;
};

