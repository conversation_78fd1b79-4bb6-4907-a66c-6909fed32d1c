import request from '@/utils/request.js';

// 查询全部角色
export function getAllRoles() {
	return request({
		url: "/role/listAll",
		method: "get"
	});
}

// 分页查询角色
export function listRolePage(data) {
	return request({
		url: "/role/listPage",
		method: "post",
		data
	});
}

// 新增角色
export function addRole(data) {
	return request({
		url: "/role/add",
		method: "post",
		data
	});
}

// 更新角色
export function editRole(data) {
	return request({
		url: "/role/edit",
		method: "post",
		data
	});
}

// 删除角色
export function deleteRole(data) {
	return request({
		url: "/role/delete",
		method: "post",
		data
	});
}

// 查询角色关联的菜单
export function listRoleFunction(data) {
	return request({
		url: "/role/listRoleFunction",
		method: "get",
		params: data
	});
}

// 为角色分配功能
export function assignFunction(data) {
	return request({
		url: "/role/assignFunction",
		method: "post",
		data
	});
}