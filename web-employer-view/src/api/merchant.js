import request from '@/utils/request.js';
import { axiosRequest } from '@/utils/request'

// 获取商户信息
export function getMerchant() {
  return request({
    url: '/merchantEmployer/getMerchant',
    method: 'get'
  })
}

// 主题认证
export function merchantAuth(data) {
  return request({
    url: '/merchantEmployer/mainAuth',
    method: 'post',
    data
  })
}

/* 更新主体认证信息 */
export function updateMerchantMain(data) {
  return request({
    url: '/merchantEmployer/updateMain',
    method: 'post',
    data
  })
}

/* 更新银行卡信息 */
export function updateBankCardInfo(data) {
  return request({
    url: '/merchantEmployer/updateBankAccount',
    method: 'post',
    data
  })
}


// 获取岗位信息
export function getEmployerPosition() {
  return request({
    url: '/employerPosition/listPosition',
    method: 'get'
  })
}

// 获取失败信息
export function getErrorMsg() {
  return request({
    url: '/merchantEmployer/getApprovalFlow',
    method: 'get'
  })
}

// 认证失败回显
export function getBackMainInfo() {
  return request({
    url: '/merchantEmployer/mainEditAuth',
    method: 'get'
  })
}

// 获取商户对应代征主体（已开启）
export function getMainstayList(params) {
  return request({
    url: '/order/listOpenMainstayByEmployerNo',
    method: 'get',
    params
  })
}

// 获取商户，产品对应代征主体（已开启）
export function getMainstayListWithProduct(params) {
  return request({
    url: '/order/listOpenMainstayByEmployerNoAndProductNo',
    method: 'get',
    params
  })
}

// 获取商户对应代征主体（开启的）
export function getAllMainstayList() {
  return request({
    url: '/order/listAllMainstayByEmployerNo',
    method: 'get'
  })
}

// 获取商户供应商
export function listAllMainstayList() {
  return request({
    url: '/merchantEmployer/listAllMainstayByEmployerNo',
    method: 'get'
  })
}

// 开启状态的发放方式
export function getOpenChannelType(params) {
  return request({
    url: '/order/listOpenChannelTypeByEmployerNo',
    method: 'get',
    params
  })
}

// 重新提交认证审核
export function merchantAuthAgain(data) {
  return request({
    url: '/merchantEmployer/mainAuthApprovalEdit',
    method: 'post',
    data
  })
}

// 查询代征主体余额
export function getAmount(data) {
  return request({
    url: '/merchantEmployer/getAmountByMainstayNoAndEmployerNo',
    method: 'post',
    data,
  })
}

// 获取密钥前的短信
export function sendSmsCode(data) {
  return request({
    url: '/employerSecret/sendSmsCode',
    method: 'get',
    params: data
  })
}

// 获取公私钥
export function getSecret(data) {
  return request({
    url: '/employerSecret/getSecret',
    method: 'get',
    params: data
  })
}

// 更新商户公钥
export function updateSecret(data) {
  return request({
    url: '/employerSecret/updateSecret',
    method: 'post',
    data
  })
}

// 获取商户开票信息
export function getInvoiceInfo() {
  return request({
    url: '/merchantInvoiceInfo/getInvoiceInfo',
    method: 'get'
  })
}

// 更新开票信息
export function updateInvoiceInfo(data) {
  return request({
    url: '/merchantInvoiceInfo/updateInvoiceInfo',
    method: 'post',
    data
  })
}

// 获取合作信息
export function getEmployerCooperate() {
  return request({
    url: '/merchantEmployer/getEmployerCooperate',
    method: 'get'
  })
}

// 获取商户邮寄信息
export function getExpressInfo() {
  return request({
    url: '/merchantExpressInfo/getExpressInfo',
    method: 'get',
  })
}

// 更新商户邮寄信息
export function updateExpressInfo(data) {
  return request({
    url: '/merchantExpressInfo/updateExpressInfo',
    method: 'post',
    data
  })
}

// 检查是否显示认证查看按钮
export function chenkAuthBtn() {
  return axiosRequest({
    url: '/merchantEmployer/getApprovalFlow',
    method: 'get'
  }, { silence: true })
}

// 获取商户开票类目
export function getInvoiceList(params) {
  return request({
    url: '/merchantEmployer/getInvoiceList',
    method: 'get',
    params
  })
}

// 获取支付通道列表
export function getPayChannels() {
  return request({
    url: '/payChannel/listChannels',
    method: 'post',
  })
}


// 查询商户在所有代征主体下的余额
export function getAmountInMainstay(data) {
  return request({
    url: '/merchantEmployer/getAmountByEmployerNoAnyMainstayNo',
    method: 'post',
    data
  })
}

// 商户在代征主体下的通道信息
export function getMerchantEmployer(data, params) {
  return request({
    url: '/merchantEmployer/listByEmployerNoAndMainstayNo',
    method: 'post',
    params,
    data
  })
}

// 更新授权回调地址
export function updateNotifyUrl(data, params) {
  return request({
    url: '/employerSecret/updateNotifyUrl',
    method: 'post',
    params,
    data,
  })
}

// 获取回调地址信息
export function getNotifyInfo(params) {
  return request({
    url: '/employerSecret/getNotifyInfo',
    method: 'get',
    params,
  })
}

// 余额提醒更新
export function balanceNotifyUpdate(data) {
  return request({
    url: '/employerNotify/updateConfig',
    method: 'post',
    data
  })
}

// 获取余额提醒配置列表
export function listNotifyConfig(data) {
  return request({
    url: '/employerNotify/listNotifyConfig',
    method: 'post',
    data
  })
}

// 单条余额提醒设置
export function getConfig(data) {
  return request({
    url: '/employerNotify/getConfig',
    method: 'post',
    data,
  })
}

// 报价单查询
export function getQuote(params) {
  return request({
    url: '/merchantEmployer/listQuoteByEmployerNo',
    method: 'get',
    params
  })
}

// 获取银行卡信息
export function getBankAccount() {
  return request({
    url: '/merchantEmployer/getBankAccount',
    method: 'get'
  })
}

export function getMainstayAccount(params) {
  return request({
    url: '/merchantEmployer/getMainstayAccount',
    method: 'get',
    params
  })
}


// 根据代征主体编号获取岗位信息
export function listPositionWithMainstayNo(params) {
  return request({
    url: '/employerPosition/listPositionWithMainstayNo',
    method: 'get',
    params,
  })
}


export function getQuoteInfo() {
  return request({
    url: '/merchantEmployer/getQuoteInfo',
    method: 'get',
  })
}

// 根据供应商获取创客汇报价单
export function getCkhQuoteByMainstayNo(params) {
  return request({
    url: '/quote/getCkhQuoteByMainstayNo',
    method: 'get',
    params
  })
}

export function listAcctDetail(data) {
  return request({
    url: '/accountRecord/listAcctDetail',
    data,
    method: 'post'
  })
}

export function exportCkAcctDeta(data) {
  return request({
    url: '/accountRecord/exportCkAcctDetail',
    data,
    method: 'post'
  })
}
