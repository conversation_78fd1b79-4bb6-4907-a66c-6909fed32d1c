import request from '@/utils/request.js';

// 消息列表
export function getNotifyList(data) {
  return request({
    url: '/notification/listNotificationRecordFullInfo',
    method: 'post',
    data,
  })
}

// 更新消息状态
export function updateNotifyStatus(data) {
  return request({
    url: '/notification/updateNotificationDetailReadStatus',
    method: 'post',
    data,
  })
}

// 获取消息
export function getNotify(params) {
  return request({
    url: '/notification/notificationInfoById',
    method: 'get',
    params
  });
}


// 首页通知
export function getIndexNotify() {
  return request({
    url: '/index/notifyList',
    method: 'get',

    slient: true,
  })
}

// 首页动态
export function getIndexMessage() {
  return request({
    url: '/index/dynamicList',
    method: 'get',
    
    slient: true,
  })
}
