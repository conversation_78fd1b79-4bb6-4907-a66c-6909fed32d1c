import request from '@/utils/request.js';

// 提交发放名单
export function uploadOrderList(data) {
  return request({
    url: '/order/batchOrderUpload',
    method: 'post',
    data,
  })
}


// 发放名单列表
export function getOrderList(data) {
  return request({
    url: '/order/listOrderPage',
    method: 'post',
    data
  })
}

// 订单明细列表
export function getOrderDetail(data) {
  return request({
    url: '/order/listOrderItemPage',
    method: 'post',
    data
  })
}

// 订单明细列表
export function listOrderItemPageForEmployer(data) {
  return request({
    url: '/order/listOrderItemPageForEmployer',
    method: 'post',
    data
  })
}

// 挂起订单列表
export function getHangList(data) {
  return request({
    url: '/order/listHangOrderItemPage',
    method: 'post',
    data
  })
}

// 开始发放：发放明细概况
export function getOrderByBatchNo(data) {
  return request({
    url: '/order/getOrderByBatchNo',
    method: 'post',
    data
  })
}

// 开始发放：发放表格
export function getDeliverList(data) {
  return request({
    url: '/order/listOrderItemByBatchNoPage',
    method: 'post',
    data
  })
}

// 取消发放
export function cancelOrderGrant(data) {
  return request({
    url: '/order/cancelBatchOrderGrant',
    method: 'post',
    data
  })
}

// 确认发放
export function confirmOrderGrant(data) {
  return request({
    url: '/order/confirmBatchOrderGrant',
    method: 'post',
    data
  })
}

// 开始发放：发放明细页（根据不同状态）
export function getOrderListByStatus(data) {
  return request({
    url: '/order/listOrderItemByBatchNoAndStatus',
    method: 'post',
    data
  })
}

// 导出发放页面订单明细
export function exportOrderItem(data) {
  return request({
    url: '/order/exportOrderItemByBatchNoAndStatus',
    method: 'post',
    data
  })
}

// 导出订单明细
export function exportOrderList(data) {
  return request({
    url: '/order/exportOrderItem',
    method: 'post',
    data
  })
}

// 导出挂起订单
export function exportHangOrder(data) {
  return request({
    url: '/order/exportHangOrderItem',
    method: 'post',
    data
  })
}

// 查余额
export function getAmount(data) {
  return request({
    url: '/order/getAmount',
    method: 'post',
    data
  })
}

// 发放订单表格总条数
export function countOrder(data) {
  return request({
    url: '/order/countOrder',
    method: 'post',
    data,
  })
}

// 订单明细总条数
export function countOrderItem(data) {
  return request({
    url: '/order/countOrderItem',
    method: 'post',
    data,
  })
}
// 订单明细总条数
export function countOrderItemForEmployer(data) {
  return request({
    url: '/order/countOrderItemForEmployer',
    method: 'post',
    data,
  })
}

// 发放订单明细统计
export function sumOrderItem(data) {
  return request({
    url: '/order/sumOrderItem',
    method: 'post',
    data,
  })
}

// 批量下载凭证
export function batchDownloadCertificateFile(data, type) {
  return request({
    url: `/certificate/batchDownloadCertificateFile/${ type }`,
    method: 'post',
    data,
  })
}

// 导出
export function exportExcel(data, type) {
  return request({
    url: `/order/exportExcel/${ type }`,
    method: 'post',
    data
  })
}

// 提现记录
export function getWithdrawRecord(data) {
  return request({
    url: '/order/listWithdrawRecordPage',
    method: 'post',
    data
  })
}

// 提现
export function withdraw(data) {
  return request({
    url: '/order/withdraw',
    method: 'post',
    data
  })
}

// 导出提现记录
export function exportWithdrawRecord(data) {
  return request({
    url: '/order/exportWithRecord',
    method: 'post',
    data,
  })
}

// 获取失败详情
export function getFailItemByBatchVo(data) {
  return request({
    url: '/order/getFailItemByBatchVo',
    method: 'post',
    data,
  })
}

// 导出失败详情
export function exportFailItemByBatchVo(data) {
  return request({
    url: '/order/exportFailItemByBatchVo',
    method: 'post',
    data,
  })
}


// 删除
export function deleteOrder(data) {
  return request({
    url: '/order/deleteOrder',
    method: 'post',
    data
  })
}

// 获取产品列表
export function listAllProduct() {
  return request({
    url: '/order/listAllProduct',
    method: 'get'
  })
}

// 发放明细签约
export function signByOrderItem(platTrxNo) {
  return request({
    url: `/grant/signByOrderItem/${ platTrxNo }`,
    method: 'post'
  })
}

// 发放明细上传身份
export function uploadIdCard(data) {
  return request({
    url: '/grant/uploadIdCard',
    method: 'post',
    data
  })
}

// 取消发放
export function rejectOrder({ platTrxNo }) {
  return request({
    url: `/grant/rejectOrderItem/${ platTrxNo }`,
    method: 'post',
  })
}


// 提交外部订单提交发放名单
export function uploadOfflineOrderList(data) {
  return request({
    url: '/offlineOrder/batchOrderUpload',
    method: 'post',
    data,
  })
}

// 提交支付回单上传
export function uploadWorkerBill(data) {
  return request({
    url: '/offlineOrder/uploadWorkerBillPath',
    method: 'post',
    data
  })
}

// 外部订单确认发放
export function confirmBatchOrder(data) {
  return request({
    url: '/offlineOrder/confirmBatchOrderGrant',
    method: 'post',
    data
  })
}

// 外部发放名单
export function offlineOrderList(data) {
  return request({
    url: '/offlineOrder/listOrderPage',
    method: 'post',
    data
  })
}

// 外部发放订单条数
export function offlineOrderCount(data) {
  return request({
    url: '/offlineOrder/countOrder',
    method: 'post',
    data
  })
}

// 外部订单明细
export function offlineOrderItemList(data) {
  return request({
    url: '/offlineOrder/listOrderItemPage',
    method: 'post',
    data
  })
}

// 外部订单明细统计
export function offlineSumOrderItem(data) {
  return request({
    url: '/offlineOrder/sumOrderItem',
    method: 'post',
    data
  })
}

// 外部订单明细删除
export function deleteOfflineOrderItem(data) {
  return request({
    url: '/offlineOrder/deleteOrderItem',
    method: 'post',
    data
  })
}

// 外部订单删除
export function deleteOfflineOrder(data) {
  return request({
    url: '/offlineOrder/deleteOrder',
    method: 'post',
    data
  })
}

// 外部订单取消发放
export function cancelOfflineOrder(data) {
  return request({
    url: '/offlineOrder/cancelBatchOrderGrant',
    method: 'post',
    data
  })
}

// 外部开始发放：发放明细概况
export function offlineGetOrderByBatchNo(data) {
  return request({
    url: '/offlineOrder/getOrderByBatchNo',
    method: 'post',
    data
  })
}

// 外部开始发放：发放表格
export function offlineGetDeliverList(data) {
  return request({
    url: '/offlineOrder/listOrderItemByBatchNoPage',
    method: 'post',
    data
  })
}

// 开始发放：发放明细页（根据不同状态）
export function offlineGetOrderListByStatus(data) {
  return request({
    url: '/offlineOrder/listOrderItemByBatchNoAndStatus',
    method: 'post',
    data
  })
}

// 外部导出订单明细
export function exportOfflineOrderList(data) {
  return request({
    url: '/offlineOrder/exportOrderItem',
    method: 'post',
    data
  })
}


// 获取失败详情
export function getOfflineFailItemByBatchVo(data) {
  return request({
    url: '/offlineOrder/getFailItemByBatchVo',
    method: 'post',
    data,
  })
}

// 外部订单明细总条数
export function countOfflineOrderItem(data) {
  return request({
    url: '/offlineOrder/countOrderItem',
    method: 'post',
    data,
  })
}

// 外部订单明细签约
export function signByOfflineOrderItem(platTrxNo) {
  return request({
    url: `/offlineOrder/signByOrderItem/${ platTrxNo }`,
    method: 'post'
  })
}

// 外部订单导出失败详情
export function exportOfflineFailItemByBatchVo(data) {
  return request({
    url: '/offlineOrder/exportFailItemByBatchVo',
    method: 'post',
    data,
  })
}

// 外部订单发放导出
export function exportOfflineOrderItem(data) {
  return request({
    url: '/offlineOrder/exportOrderItemByBatchNoAndStatus',
    method: 'post',
    data
  })
}
