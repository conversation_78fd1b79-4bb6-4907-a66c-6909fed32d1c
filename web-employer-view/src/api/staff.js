import request from '@/utils/request.js';

// 分页查询员工
export function listStaffPage(data) {
    return request({
        url: "/staff/listPage",
        method: "post",
        data
    });
}

// 新增员工
export function addStaff(data) {
    return request({
        url: "/staff/add",
        method: "post",
        data
    });
}

// 根据员工id查询员工信息
export function getStaffById(data) {
    return request({
        url: "/staff/getById",
        method: "get",
        params: data
    });
}

// 修改员工信息
export function editStaff(data) {
    return request({
        url: "/staff/edit",
        method: "post",
        data
    });
}

// 删除员工
export function deleteStaff(data) {
    return request({
        url: "/staff/delete",
        method: "post",
        params: data
    });
}