import request from '@/utils/request.js';

// 获取雇员信息
export function getJobworker(data) {
  return request({
    url: '/worker/listPage',
    data,
    method: 'post'
  })
}

export function employ(ids, event) {
  return request({
    url: `/worker/employ/${ids}?event=${event}`,
    method: 'POST'
  })
}


export function findById(id) {
  return request({
    url: `/worker/findById/${id}`,
    method: 'get'
  })
}

export function approved(data) {
  return request({
    url: `/worker/approved`,
    method: 'post',
    data
  })
}

// 任务列表
export function taskList(data) {
  return request({
    url: '/job/page',
    method: 'post',
    data,
  })
}

export function taskListOnGrant(data) {
  return request({
    url: '/job/listJobListOnGrant',
    method: 'post',
    data,
  })
}

export function addJob(data) {
  return request({
    url: '/job/add',
    method: 'post',
    data,
  })
}

export function jobDetail({id}) {
  return request({
    url: `/job/getById/${id}`,
    method: 'get'
  })
}

export function getByJobId(jobId) {
  return request({
    url: `/job/getByJobId?jobId=${jobId}`,
    method: 'get'
  })
}

export function deleteJob({id}) {
  return request({
    url: `/job/delete/${id}`,
    method: 'post'
  })
}

export function uploadMember(data) {
  return request({
    url: '/job/uploadJobMember',
    method: 'post',
    data,
  })
}

// 雇员身份信息
export function workerInfo(params) {
  return request({
    url: '/worker/getIdCardByIDCardNoMd5',
    method: 'post',
    params
  })
}

export function updateJob(data) {
  return request({
    url: `/job/update`,
    method: 'post',
    data
  })
}

export function finishTask({id}) {
  return request({
    url: `/job/completeJob/${id}`,
    method: 'post'
  })
}

export function getJobList(data) {
  return request({
    url: `/job/getJobList`,
    method: 'post',
    data
  })
}

export function completeJobWorker(ids) {
  return request({
    url: `/worker/completeJobWorker/${ids}`,
    method: 'POST'
  })
}

export function batchApprovedSuccess(ids) {
  return request({
    url: `/worker/batchApprovedSuccess/${ids}`,
    method: 'POST'
  })
}

export function batchUpdateStatus(ids, data) {
  return request({
    url: `/worker/batchUpdateStatus/${ids}`,
    method: 'POST',
    data
  })
}


export function submit(data) {
  return request({
    url: `/worker/submit`,
    method: 'POST',
    data
  })
}

export function getIdCardByPhone(phone) {
  return request({
    url: `/worker/submit?phone=${phone}`,
    method: 'POST',
  })
}


export function deleteWorker(id) {
  return request({
    url: `/worker/delete/${id}`,
    method: 'POST',
  })
}

// 上传雇员身份证
export function addIDCardImages(data) {
  return request({
    url: '/worker/addIDCardImages',
    method: 'post',
    data
  })
}


// 获取商户已签约用户
export function getSignedMembers(params) {
  return request({
    url: '/job/getSignedMembers',
    method: 'get',
    params
  })
}

//指派人员到任务
export function addSignedMember(data) {
  return request({
    url: '/job/addSignedMember',
    method: 'post',
    data
  })
}

// 生成任务二维码
export function getAppQrCode(id) {
  return request({
    url: `/job/getAppQrCode/${id}`,
    method: 'get'
  })
}
