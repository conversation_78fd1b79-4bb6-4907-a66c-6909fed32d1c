import request from '@/utils/request.js';

/* 电子签约记录 */
export function getSignList(data) {
  return request({
    url: '/sign/listPage',
    method: 'post',
    data
  })
}

// 导出电子签约
export function exportSignRecord(data) {
  return request({
    url: '/sign/exportSignRecord',
    method: 'post',
    data,
  })
}

// 上传签约文件
export function uploadSignFile(data) {
  return request({
    url: '/sign/importSignRecord',
    method: 'post',
    data
  })
}

// 补发短信
export function resendMsg(data) {
  return request({
    url: '/sign/resend',
    method: 'post',
    data
  })
}

// 三要素修改
export function modifySignInfo(data) {
  return request({
    url: '/sign/modify',
    method: 'post',
    data
  })
}


// 上传图片
export function addSignImage (data) {
  return request({
    url: '/sign/addSignImages',
    method: 'post',
    data
  })
}
