import request from '@/utils/request.js';

// 获取安全设置信息
export function getSafeSetting() {
  return request({
    url: "/safe/getSafeSetting",
    method: "get"
  });
}

// 获取是否设置支付密码
export function getTradePwdSetting() {
  return request({
    url: "/safe/getTradePwdSetting",
    method: "get"
  })
}

// 修改支付密码
export function changeTradePwd(data) {
  return request({
    url: "/safe/changeTradePwd",
    method: "post",
    data
  });
}

// 获取商户负责人
export function getLeader() {
  return request({
    url: "/safe/getLeader",
    method: "get"
  })
}

// 更换商户负责人
export function changeLeader(data) {
  return request({
    url: "/safe/changeLeader",
    method: "post",
    data
  })
}

// 获取常用邮箱
export function getEmail() {
  return request({
    url: "/safe/getEmail",
    method: "get"
  })
}

// 更换常用邮箱，发送邮箱验证码
export function changeEmailVerifyCode(data) {
  return request({
    url: "/safe/changeEmailVerifyCode",
    method: "post",
    data
  })
}

// 更换常用邮箱
export function changeEmail(data) {
  return request({
    url: "/safe/changeEmail",
    method: "post",
    data
  })
}

// 生成密钥对
export function genMerchantKeypair() {
  return request({
    url: '/employerSecret/genMerchantKeypair',
    method: 'post',
  })
}
