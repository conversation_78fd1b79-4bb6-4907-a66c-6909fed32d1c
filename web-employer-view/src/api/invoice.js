import request from '@/utils/request'

// 获取申请记录
export function getInvoiceList(data) {
  return request({
    url: '/invoice/listPage',
    method: 'post',
    data,
  })
}

// 获取交易时间段起始时间
export function getBeginDate(data) {
  return request({
    url: '/invoice/getApplyTradeCompleteDayBegin',
    method: 'get',
    params: data,
  })
}

// 查询待开票批次订单
export function getIssueOrder(data) {
  return request({
    url: '/invoice/listWaitIssueOrderPage',
    method: 'post',
    data,
  })
}

// 查询待开票批次订单
export function listWaitIssueOrderItemPage(data) {
  return request({
    url: '/invoice/listWaitIssueOrderItemPage',
    method: 'post',
    data,
  })
}

// 查询待开票金额
export function getWaitIssueAmount(data) {
  return request({
    url: '/invoice/getWaitIssueAmount',
    method: 'post',
    data
  })
}

// 查询待开票金额
export function getOrderItemWaitIssueAmount(data) {
  return request({
    url: '/invoice/getOrderItemWaitIssueAmount',
    method: 'post',
    data
  })
}

export function sumWaitCkhInvoiceAmount(data) {
  return request({
    url: '/invoice/sumWaitCkhInvoiceAmount',
    method: 'post',
    data
  })
}

// 申请发票
export function applyInvoice(data) {
  return request({
    url: '/invoice/applyInvoice',
    method: 'post',
    data,
  })
}

export function applyItemInvoice(data) {
  return request({
    url: '/invoice/applyItemInvoice',
    method: 'post',
    data,
  })
}

// 根据流水号获取发票申请记录详细信息
export function getByTrxNo(data) {
  return request({
    url: '/invoice/getByTrxNo',
    method: 'get',
    params: data
  })
}

// 建议开票类目
export function getInvoiceInfoWithMainstayNo(params) {
  return request({
    url: '/merchantInvoiceInfo/getInvoiceInfoWithMainstayNo',
    method: 'get',
    params
  })
}

export function getMainstayListByProduct(params) {
  return request({
    url: '/invoice/listOpenMainstayByEmployerNoAndProduct',
    method: 'get',
    params,
  })
}

export function getAllMainstayListByProduct(params) {
  return request({
    url: '/invoice/listAllMainstayByEmployerNoAndProduct',
    method: 'get',
    params,
  })
}

export function listMchProduct(params) {
  return request({
    url: '/merchantEmployer/listMchProduct',
    method: 'get',
    params: params
  })
}

// 查询普通发票批次详情
export function listInvoiceDetail(data) {
  return request({
    url: '/invoice/invoiceRecordDetailPage',
    method: 'post',
    data
  })
}

// 预约开票设置
export function listBookConfig() {
  return request({
    url: '/invoice/listBookConfig',
    method: 'get'
  })
}

export function deleteBookConfig(data) {
  return request({
    url: '/invoice/deleteBookConfig',
    method: 'post',
    data
  })
}

export function applyBookConfig(data) {
  return request({
    url: '/invoice/applyBookConfig',
    method: 'post',
    data
  })
}

export function exportInvoceInfo(data) {
  return request({
    url: '/invoice/exportInvoceInfo',
    method: 'post',
    data,
  })
}

export function offlineInvoiceApplyProcessing() {
  return request({
    url: '/invoice/offlineInvoiceApplyProcessing',
    method: 'post'
  })
}

export function calculateServiceFee(data) {
  return request({
    url: '/invoice/calculateServiceFee',
    method: 'post',
    data
  })
}

export function applyOffline(data) {
  return request({
    url: '/invoice/applyOffline',
    method: 'post',
    data
  })
}

// 完税证明列表查询
export function getTaxCertList(data) {
  return request({
    url: '/taxCertificate/listPage',
    method: 'post',
    data
  })
}

export function preCheckApplyInvoice(data) {
  return request({
    url: '/invoice/preCheckApplyInvoice',
    method: 'post',
    data
  })
}
