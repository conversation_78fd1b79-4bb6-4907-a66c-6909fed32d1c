import request from '@/utils/request.js';

// 充值操作
export function recharge(data) {
  return request({
    url: '/order/recharge',
    method: 'post',
    data
  })
}

// 充值记录
export function getRechargeRecord(data) {
  return request({
    url: '/recharge/listRecharge',
    method: 'post',
    data
  })
}

// 导出充值记录列表
export function getRecordList(data) {
  return request({
    url: '/recharge/exportRechargeRecord',
    method: 'post',
    data
  })
}

// 批量导出
export function getMultiRecordList(data) {
  return request({
    url: '/recharge/exportRechargeRecordZip',
    method: 'post',
    data
  })
}

// 获取银行卡充值信息
export function getBankChannelInfo(params) {
  return request({
    url: '/payChannel/getByChannelNo',
    method: 'get',
    params
  })
}
