import request from '@/utils/request.js';

// 自由职业者发单数据
export function analyzeFreelance(data) {
  return request({
    url: '/analyze/freelanceList',
    method: 'post',
    data,
  })
}
// 自由职业者统计数据
export function countFreelance(data) {
  return request({
    url: '/analyze/countFreelance',
    method: 'post',
    data,
  })
}

// 上传身份证
export function uploadIdCard(data) {
  return request({
    url: '/analyze/uploadIdCard',
    method: 'post',
    data,
  })
}

// 签约
export function sign(data) {
  return request({
    url: '/analyze/sign',
    method: 'post',
    data,
  })
}
