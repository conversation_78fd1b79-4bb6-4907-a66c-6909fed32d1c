<template>
  <el-dialog
    :title="disabled ? '查看权限' : '分配权限'"
    :visible.sync="show"
    :close-on-click-modal="false"
    top="10px"
    @close="closeForm"
  >
    <el-input
      placeholder="输入关键字进行过滤"
      v-model="filterText"
    />
    <el-tree
      ref="assignTree"
      :data="assignTreeData"
      row-key="id"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      :default-expand-all="true"
      :filter-node-method="filterNode"
      v-slot="{data:menu}"
    >

      <div class="treeNode">
        <el-checkbox
          class="menuNode"
          @change="checked=>selectChange(menu,checked)"
          v-model="assignedInfo[menu.id]"
          :key="menu.id"
          :label="menu.id"
          :disabled="disabled"
        >
          {{menu.name}}
        </el-checkbox>

        <div
          class="actionNode"
          v-if="menu.actions"
        >
          <el-checkbox
            @change="checked=>selectChange(action,checked)"
            v-model="assignedInfo[action.id]"
            v-for="action in menu.actions"
            :key="action.id"
            :label="action.id"
            :disabled="disabled"
          >{{action.name}}
          </el-checkbox>
        </div>
      </div>
    </el-tree>

    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="closeForm">取 消</el-button>
      <el-button
        type="primary"
        @click="doAssignFunction"
        v-if="!disabled"
      >确 定</el-button>
    </div>
  </el-dialog>

</template>

<script>
import { listRoleFunction, assignFunction } from '@/api/role';
import { getAllFunctions } from '@/api/function'

const buildAssignTree = function (pFunction, pidGroup, subMenuArr, subActionArr) {
  let pid = (pFunction && pFunction.id) || 0
  if (!pidGroup[pid]) {
    return;
  }
  pidGroup[pid].forEach(f => {
    f.children = []
    f.actions = []
    if (f.type === 1) {
      subMenuArr.push(f)
      if (pidGroup[f.id]) {
        buildAssignTree(f, pidGroup, f.children, f.actions)
      }
    } else {
      subActionArr.push(f)
    }
    f.parent = pFunction;
  })
}

export default {
  name: "assignFunctionForm",
  data() {
    return {
      filterText: '',
      functions: [],
      assignedInfo: [],
      roleId: undefined,
      show: false,
    }
  },
  watch: {
    filterText(val) {
      this.$refs.assignTree.filter(val);
    }
  },
  props: {
    type: {
      type: String,
      default: 'edit'
    },
  },
  methods: {
    initAndShow(roleId) {
      this.roleId = roleId;
      let p1 = getAllFunctions();
      let p2 = listRoleFunction({ roleId: this.roleId });
      Promise.all([p1, p2])
        .then(([{ data: functions }, { data: assignedFunctions }]) => {
          this.functions = functions;
          assignedFunctions.forEach(f => this.assignedInfo[f.id] = true)
          this.show = true;
        });
    },

    filterNode(value, menu) {
      if (!value) {
        return true;
      }
      return menu.name.indexOf(value) !== -1 || menu.actions.some(p => p.name.indexOf(value) !== -1)
    },

    recursiveUp(currentFunction, checked) {
      if (!currentFunction) {
        return;
      }
      if (checked) {
        this.assignedInfo[currentFunction.id] = true;
        this.recursiveUp(currentFunction.parent, checked)
      }
    },
    recursiveDown(currentFunction, checked) {
      currentFunction.children.forEach(c => {
        this.assignedInfo[c.id] = checked;
        this.recursiveDown(c, checked);
      })
      currentFunction.actions.forEach(c => {
        this.assignedInfo[c.id] = checked;
      })
    },

    selectChange(func, checked) {
      this.recursiveUp(func, checked)
      this.recursiveDown(func, checked)
    },

    doAssignFunction() {
      let assignedIds = []
      this.assignedInfo.forEach((v, i) => {
        if (v === true) {
          assignedIds.push(i)
        }
      });
      assignFunction({
        roleId: this.roleId,
        functionIds: assignedIds
      }).then(({ data }) => {
        this.$message.success(data);
      }).finally(() => {
        this.show = false;
      })
    },

    closeForm() {
      this.functions = [];
      this.roleId = undefined;
      this.assignedInfo = [];
      this.show = false;
    }
  },

  computed: {
    assignTreeData() {
      let pidGroup = []
      this.functions.forEach(f => {
        if (!pidGroup[f.parentId]) {
          pidGroup[f.parentId] = [f]
        } else {
          pidGroup[f.parentId].push(f)
        }
      })
      let subMenuArr = []
      buildAssignTree(null, pidGroup, subMenuArr, [])
      return subMenuArr
    },
    disabled() {
      return this.type == 'view'
    }
  },
}
</script>

<style lang="scss" scoped>
  ::v-deep .el-dialog {
    width: 800px;

    .el-input {
      width: 240px;
      margin-bottom: 16px;
    }

    .el-dialog__body {
      max-height: 600px;
      overflow-y: auto;

    }
    .el-tree {
      // border-bottom: 1px solid #ccc;
      .el-tree-node__content {
        height: auto;
        align-items: flex-start;
        // border: 1px solid #ccc;
        border-bottom: 0px;

      }
      .el-tree-node__expand-icon {
        padding: 8px;
        padding-left: 0;
        margin-top: 4px;
      }

      .el-checkbox {
        margin-bottom: 4px;
        margin-top: 4px;
      }

      .treeNode {
        display: flex;

        .menuNode {
          display: inline-block;
          width: 150px;
          margin-right: 64px;
          white-space: normal;
          margin-top: 4px;

          .el-checkbox__label {
            font-size: 14px;
          }
        }

        .el-checkbox.is-disabled.is-checked .el-checkbox__label {
          color: #000;
        }

        .actionNode {
          width: 500px;
          white-space: normal;

          .el-checkbox {
            margin-top: 4px;
            margin-bottom: 4px;
            margin-right: 24px;
          }
        }
      }
    }
  }
</style>
