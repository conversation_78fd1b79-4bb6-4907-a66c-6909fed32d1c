<template>
  <div class="merchant-page-container">
    <div
      class="header-container"
      v-sticky="{ zIndex: 100, stickyTop: 0 }">
      <el-steps
        :active="activeStep"
        align-center
        class="el-steps-container">
        <el-step title="填写主体信息"></el-step>
        <el-step title="填写经营信息"></el-step>
        <el-step title="填写账户信息"></el-step>
        <el-step title="审核与公示"></el-step>
      </el-steps>
    </div>
    <div class="page-container">
      <div class="content-container">
        <!--step_1-->
        <el-form
          ref="form_1"
          :model="form_1"
          :rules="rules"
          label-width="300px"
          class="step_1"
          v-show="activeStep == 1">
          <!--企业信息-->
          <div class="company_info">
            <p class="subTitle">企业信息</p>
            <div>
              <el-form-item
                label="营业执照"
                prop="businessLicenseFileUrl"
                ref="businessLicenseFileUrl">
                <el-upload
                  v-toggle="form_1.businessLicenseFileUrl"
                  class="upload-demo"
                  data-type="businessLicenseFileUrl"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  list-type="picture-card"
                  accept=".png,.gif,.jpg,.bmp,.pdf"
                  :limit="1"
                  :file-list="businessLicenseFileUrlShow"
                  :before-upload="validateUploadFile({img: true, pdf: true, size: 6})"
                  :on-remove="(file, fileList) => handleRemove(file, fileList, 'businessLicenseFileUrl')"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'businessLicenseFileUrl')">
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <template v-slot:file="{file}">
                    <div
                      class="fileBg"
                      v-if="isPdf(file.name)">
                      <span class="file-name">{{ file.name }}</span>
                      <span class="el-upload-list__item-actions">
                        <span
                          class="el-upload-list__item-delete"
                          @click="handleFileRemove(file, 'businessLicenseFileUrl')"
                        >
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </template>
                  <div
                    slot="tip"
                    class="el-upload__tip">
                    请上传彩色原件或加盖公司公章的复印件，小于6M，文件格式为pdf、bmp、png、jpeg、jpg或gif。
                    <el-popover
                      trigger="click"
                      width="300">
                      <el-button
                        type="text"
                        slot="reference">
                        查看示例
                      </el-button>
                      <div>
                        <img
                          class="example-image"
                          src="@/assets/example-1.png"
                        >
                      </div>
                    </el-popover>
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item
                label="企业名称："
                prop="mchName">
                <el-input
                  v-model="form_1.mchName"
                  disabled></el-input>
              </el-form-item>
              <el-form-item
                label="统一社会信用代码："
                prop="taxNo">
                <el-input v-model="form_1.taxNo"></el-input>
                <p class="color-gray form-tip">请输入营业执照上18位统一社会信用代码</p>
              </el-form-item>
              <el-form-item
                label="注册地址："
                prop="registerAddrDetail">
                <address-select
                  :province.sync="form_1.registerAddrProvince"
                  :city.sync="form_1.registerAddrCity"
                  :town.sync="form_1.registerAddrTown"
                  :default-address="defaultAddress"
                ></address-select>
                <el-input v-model="form_1.registerAddrDetail"></el-input>
                <p class="color-gray form-tip">
                  与营业执照登记住所一致
                </p>
              </el-form-item>
              <el-form-item
                label="注册资本（万）："
                prop="registerAmount">
                <el-input v-model="form_1.registerAmount"></el-input>
              </el-form-item>
              <el-form-item
                label="经营范围："
                prop="managementScope">
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="form_1.managementScope">
                </el-input>
                <p class="color-gray form-tip">
                  与企业工商营业执照上一致
                </p>
              </el-form-item>
              <el-form-item
                label="营业期限："
                prop="managementValidityDateType">
                <el-radio-group v-model="form_1.managementValidityDateType">
                  <el-radio
                    v-for="item in $dict('ValidityDateTypeEnum')"
                    :key="item.code"
                    :label="Number(item.code)">{{ item.desc }}
                  </el-radio>
                </el-radio-group>
                <date-picker
                  v-if="$dictCode('ValidityDateTypeEnum',form_1.managementValidityDateType).desc == '区间有效'"
                  type="daterange"
                  :isShowFastTime="false"
                  :timeRange="[form_1.managementTermBegin || '', form_1.managementTermEnd || '']"
                  :use-option="false"
                  @change="val => getTimeRange(val, 'managementTerm')"
                ></date-picker>
                <div
                  v-if="$dictCode('ValidityDateTypeEnum',form_1.managementValidityDateType).desc == '长期有效'"
                >
                  <el-date-picker
                    type="date"
                    v-model="form_1.managementTermBegin"
                    placeholder="选择起始时间"
                    value-format="yyyy-MM-dd"
                  ></el-date-picker>
                </div>
              </el-form-item>
            </div>
          </div>
          <!--法定代表人证件-->
          <p class="subTitle">法定代表人信息</p>
          <div class="company_info">
            <el-form-item
              label="证件类型："
              prop="certificateType">
              <el-select
                clearable
                v-model="form_1.certificateType">
                <el-option
                  v-for="item in $dict('CertificateTypeEnum')"
                  :key="item.code"
                  :value="Number(item.code)"
                  :label="item.desc"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="证件照："
              prop="idFileType"
              ref="idFileType">
              <el-radio-group v-model="form_1.idFileType">
                <el-radio label="100">彩色原件</el-radio>
                <el-radio label="101">复印件</el-radio>
              </el-radio-group>
              <div v-show="form_1.idFileType == '100'">
                <p>人像面
                  <el-popover
                    trigger="click"
                    width="300">
                    <el-button
                      type="text"
                      slot="reference">
                      查看示例
                    </el-button>
                    <div>
                      <img
                        class="example-image"
                        src="@/assets/example-2.png"
                      >
                    </div>
                  </el-popover>
                </p>
                <el-upload
                  v-toggle="form_1.idCardHeadFileUrl"
                  key="idCardHeadFileUrl"
                  class="upload-demo"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  list-type="picture-card"
                  accept=".png,.gif,.jpg,bmp"
                  :limit="1"
                  :file-list="idCardHeadFileUrlShow"
                  :before-upload="beforeAvatarUpload"
                  :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardHeadFileUrl')"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardHeadFileUrl', 'idFileType')">
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
                <p>国徽面
                  <el-popover
                    trigger="click"
                    width="300">
                    <el-button
                      type="text"
                      slot="reference">
                      查看示例
                    </el-button>
                    <div>
                      <img
                        class="example-image"
                        src="@/assets/example-3.png"
                      >
                    </div>
                  </el-popover>
                </p>
                <el-upload
                  v-toggle="form_1.idCardEmblemFileUrl"
                  key="idCardEmblemFileUrl"
                  class="upload-demo"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  list-type="picture-card"
                  accept=".png,.gif,.jpg,bmp"
                  :limit="1"
                  :file-list="idCardEmblemFileUrlShow"
                  :before-upload="beforeAvatarUpload"
                  :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardEmblemFileUrl')"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardEmblemFileUrl', 'idFileType')">
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </div>
              <div v-show="form_1.idFileType == '101'">
                <p>加盖公司公章的法人身份证复印件
                  <el-popover
                    trigger="click"
                    width="300">
                    <el-button
                      type="text"
                      slot="reference">
                      查看示例
                    </el-button>
                    <div>
                      <img
                        class="example-image"
                        src="@/assets/idCardCopyFileExample.png"
                      >
                    </div>
                  </el-popover>
                </p>
                <el-upload
                  v-toggle="form_1.idCardCopyFileUrl"
                  key="idCardCopyFileUrl"
                  class="upload-demo"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  list-type="picture-card"
                  accept=".png,.gif,.jpg,bmp"
                  :limit="1"
                  :file-list="idCardCopyFileUrlShow"
                  :before-upload="beforeAvatarUpload"
                  :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardCopyFileUrl')"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardCopyFileUrl', 'idFileType')">
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </div>
            </el-form-item>
          </div>
          <div class="bankCard_info">
            <el-form-item
              label="法定代表人姓名："
              prop="legalPersonName">
              <el-input v-model="form_1.legalPersonName"></el-input>
            </el-form-item>
            <el-form-item
              label="证件号码："
              prop="certificateNumber"
              :rules="certificateRules">
              <el-input v-model="form_1.certificateNumber"></el-input>
            </el-form-item>
            <el-form-item
              label="证件有效期："
              prop="certificateValidityDateType">
              <el-radio-group v-model="form_1.certificateValidityDateType">
                <el-radio
                  v-for="item in $dict('ValidityDateTypeEnum')"
                  :key="item.code"
                  :label="Number(item.code)">{{ item.desc }}
                </el-radio>
              </el-radio-group>
              <date-picker
                v-if="$dictCode('ValidityDateTypeEnum',form_1.certificateValidityDateType).desc == '区间有效'"
                type="daterange"
                :isShowFastTime="false"
                :use-option="false"
                @change="val => getTimeRange(val, 'certificateTerm')"
                :timeRange="[form_1.certificateTermBegin || '', form_1.certificateTermEnd || '']">
              </date-picker>

              <div v-else-if="$dictCode('ValidityDateTypeEnum',form_1.certificateValidityDateType).desc == '长期有效'">
                <el-date-picker
                  v-model="form_1.certificateTermBegin"
                  type="date"
                  placeholder="请选择起始时间"
                  :value-format="'yyyy-MM-dd'"
                ></el-date-picker>
              </div>
            </el-form-item>
          </div>
        </el-form>

        <!--step_2-->
        <el-form
          ref="form_2"
          :model="form_2"
          :rules="rules"
          label-width="300px"
          class="step_2"
          v-show="activeStep == 2">
          <!--联系人信息-->
          <p class="subTitle">联系人信息</p>
          <div class="company_info">
            <el-form-item
              label="负责人姓名："
              prop="contactName">
              <el-input
                v-model="form_2.contactName"
                :disabled="!editAccess"></el-input>
              <p class="color-gray form-tip">
                请填写公司运营负责人
              </p>
            </el-form-item>
            <el-form-item
              label="负责人手机号："
              prop="contactPhone">
              <el-input
                v-model="form_2.contactPhone"
                :disabled="!editAccess"></el-input>
            </el-form-item>
            <el-form-item
              label="客服电话："
              prop="servicePhone">
              <el-input v-model="form_2.servicePhone"></el-input>
              <p class="color-gray form-tip">
                请注意填写格式，举例（座机：0660-********；手机：***********；400电话：**********
              </p>
            </el-form-item>
          </div>

          <!--经营信息-->
          <div class="bankCard_info">
            <p class="subTitle">经营信息</p>
            <div>
              <el-form-item
                label=" 企业简称："
                prop="shortName">
                <el-input v-model="form_2.shortName"></el-input>
              </el-form-item>
              <el-form-item
                label="实际经营地址："
                prop="managementAddrDetail">
                <address-select
                  :province.sync="form_2.managementAddrProvince"
                  :city.sync="form_2.managementAddrCity"
                  :town.sync="form_2.managementAddrTown"
                  :default-address="defaultManageAddress"
                ></address-select>
                <el-input v-model="form_2.managementAddrDetail"></el-input>
              </el-form-item>
              <el-form-item
                label="门头照片"
                prop="doorPhotoFileUrl"
                ref="doorPhotoFileUrl">
                <el-upload
                  v-toggle="form_2.doorPhotoFileUrl"
                  class="upload-demo"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  list-type="picture-card"
                  accept=".png,.gif,.jpg,bmp"
                  :limit="1"
                  :file-list="doorPhotoFileUrlShow"
                  :before-upload="beforeAvatarUpload"
                  :on-remove="(file, fileList) => handleRemove(file, fileList, 'doorPhotoFileUrl')"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'doorPhotoFileUrl')">
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <div
                    slot="tip"
                    class="el-upload__tip">最多1张，单张照片不超过6M。
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item
                label="办公内景照片"
                prop="workIndoorFileUrl"
                ref="workIndoorFileUrl">
                <el-upload
                  v-toggle="form_2.workIndoorFileUrl"
                  class="upload-demo"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  list-type="picture-card"
                  accept=".png,.gif,.jpg,bmp"
                  :limit="1"
                  :file-list="workIndoorFileUrlShow"
                  :before-upload="beforeAvatarUpload"
                  :on-remove="(file, fileList) => handleRemove(file, fileList, 'workIndoorFileUrl')"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'workIndoorFileUrl')">
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <div
                    slot="tip"
                    class="el-upload__tip">最多1张，单张照片不超过6M。
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item
                label="前台照片"
                prop="receptionFileUrl"
                ref="receptionFileUrl">
                <el-upload
                  v-toggle="form_2.receptionFileUrl"
                  class="upload-demo"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  list-type="picture-card"
                  accept=".png,.gif,.jpg,bmp"
                  :limit="1"
                  :file-list="receptionFileUrlShow"
                  :before-upload="beforeAvatarUpload"
                  :on-remove="(file, fileList) => handleRemove(file, fileList, 'receptionFileUrl')"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'receptionFileUrl')">
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <div
                    slot="tip"
                    class="el-upload__tip">最多1张，单张照片不超过6M。
                  </div>
                </el-upload>
              </el-form-item>
            </div>
          </div>

          <div class="invoice_info">
            <p class="subTitle">开票信息</p>
            <el-form-item
              label="纳税人类型："
              prop="taxPayerType">
              <el-radio-group v-model="form_2.taxPayerType">
                <el-radio
                  v-for="(item, index) in $dict('TaxPayerTypeEnum')"
                  :key="index"
                  :label="Number(item.code)">
                  {{ item.desc }}
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item
              label="开票地址："
              prop="invoiceAddress">
              <el-input v-model="form_2.invoiceAddress"></el-input>
            </el-form-item>
            <el-form-item
              label="联系电话："
              prop="invoicePhone">
              <el-input v-model="form_2.invoicePhone"></el-input>
            </el-form-item>
            <el-form-item
              label="开票银行："
              prop="invoiceBankName">
              <el-input v-model="form_2.invoiceBankName"></el-input>
            </el-form-item>
            <el-form-item
              label="开票账户："
              prop="invoiceAccountNo">
              <el-input v-model="form_2.invoiceAccountNo"></el-input>
            </el-form-item>
          </div>

          <!-- 企业主要人员 -->
          <div class="personnels_info">
            <p class="subTitle">
              企业主要人员
              <el-button
                type="text"
                @click="addPeople"
              >增加
              </el-button>
            </p>
            <el-table :data="form_2.personnels">
              <el-table-column
                label="姓名"
                prop="name"
              >
                <template v-slot="{row, $index, column}">
                  <div v-if="$index == 0">
                    {{ row[column.property] }}
                  </div>
                  <div v-else>
                    <el-input
                      v-model="row[column.property]"
                      clearable
                    ></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="身份证号码"
                prop="idCardNumber"
              >
                <template v-slot="{row, $index, column}">
                  <div v-if="$index == 0">
                    {{ row[column.property] }}
                  </div>
                  <div v-else>
                    <el-input
                      v-model="row[column.property]"
                      clearable
                    ></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="职务"
                prop="position">
                <template v-slot="{row, $index, column}">
                  <div v-if="$index == 0">
                    法人
                  </div>
                  <div v-else>
                    <el-input
                      v-model="row[column.property]"
                      clearable
                    ></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template v-slot="{$index}">
                  <el-button
                    type="text"
                    @click="deletePeople($index)"
                    style="color: #f00;"
                    v-if="$index > 0"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form>

        <!--step_3-->
        <el-form
          ref="form_3"
          :model="form_3"
          :rules="rules"
          label-width="300px"
          class="step_3"
          v-show="activeStep == 3">
          <!--银行卡信息-->
          <p class="subTitle">银行卡信息</p>
          <div class="bankCard_info">
            <div>
              <el-form-item
                label="银行卡号："
                prop="accountNo">
                <el-input v-model="form_3.accountNo"></el-input>
              </el-form-item>
              <el-form-item
                label="联行号："
                prop="bankChannelNo">
                <el-input
                  v-model="form_3.bankChannelNo"
                  prefix-icon="el-icon-search"
                  @click.native="searchBankNumberInfo">
                </el-input>
              </el-form-item>
              <el-form-item
                label="开户银行："
                prop="bankName">
                <el-input
                  v-model="form_3.bankName"
                  disabled></el-input>
              </el-form-item>
            </div>
          </div>

          <!-- <div class="accounts_info">
            <p class="subTitle">账户信息</p>
            <div
              v-for="(item, index) in mainstayList"
              :key="index"
            >
              <el-form-item label="代征主体：">{{ item.mainstayMchName }}</el-form-item>
              <el-form-item label="开通发放方式：" prop="accounts" ref="accounts">
                <template v-for="(i, key) in $dict('ChannelTypeEnum')">
                  <el-checkbox
                    v-model="channelSelect[item.mainstayMchNo][key]"
                    :key="key"
                    :true-label="1"
                    :false-label="0"
                    @change="onChannelTypeChange($event, i, index)"
                  >
                    {{ i.desc }}
                  </el-checkbox>
                </template>
              </el-form-item>
              <div>
                <el-form-item
                  v-for="(account, num) in form_3.accounts[item.mainstayMchNo]"
                  :key="num"
                  :label="$dictCode('ChannelTypeEnum', account.channelType).desc + '通道'"
                >
                  <el-select
                    v-if="account"
                    v-model="account['payChannelNo']"
                    @change="handlePayChannelChange(account, $event)"
                  >
                    <el-option
                      v-for="(i, key) in payChannel[account.channelType - 1]"
                      :key="key"
                      :value="i.payChannelNo"
                      :label="i.payChannelName"
                    ></el-option>
                  </el-select>
                  <p class="color-gray form-tip">请选择提供{{ $dictCode('ChannelTypeEnum', account.channelType).desc }}发放服务的第三方支付机构</p>
                </el-form-item>
              </div>
            </div>
          </div> -->
          <el-footer class="footer-container">
            <div class="protocal-container">
              <el-checkbox v-model="protocalCheck">
                <span class="el-checkbox-label">
                  我已仔细阅读并同意
                </span>
              </el-checkbox>
              <span
                class="protocal-link"
                @click="getProtocal(1)"
              >《汇聚支付分账服务协议》</span>
              <span
                class="protocal-link"
                @click="getProtocal(2)"
              >《平台商户入驻框架协议》</span>
            </div>
            <el-button
              type="primary"
              @click="nextStep"
              :disabled="!protocalCheck"
            >提交
            </el-button>
            <el-button @click="activeStep--">上一步</el-button>
          </el-footer>
        </el-form>

        <el-footer
          class="form-btn-group"
          v-show="activeStep < 3">
          <el-button
            type="primary"
            @click="nextStep"
          >
            {{ activeStep == 2 ? '提交' : '保存并下一步' }}
          </el-button>
          <el-button
            @click="backStep"
          >{{ activeStep == 1 ? '取消' : '上一步' }}
          </el-button>
        </el-footer>

        <!--step_4-->
        <div
          class="step_4"
          v-show="activeStep >= 4">
          <div v-if="resultStatus == 'pend'">
            <svg-icon icon-class="waiting"></svg-icon>
            <p class="result-title">认证审核中</p>
            <p>资料审核中，审核结果将在2个工作日内通知您</p>
            <div class="result-detail">
              <p class="result-detail-title">主体信息认证</p>
              <p>工作流水号：{{ result.commonFlowId }}</p>
              <p>发起人：{{ result.submitName }}</p>
              <p>创建时间：{{ result.createTime }}</p>
            </div>
          </div>
          <div v-else-if="resultStatus == 'fail'">
            <svg-icon icon-class="fail"></svg-icon>
            <p class="result-title">认证失败</p>
            <div class="result-detail">
              <p class="result-detail-title">主体信息认证</p>
              <p>工作流水号：{{ errMsg.commonFlowId }}</p>
              <p>发起人：{{ errMsg.initiatorName }}</p>
              <p>创建时间：{{ errMsg.createTime }}</p>
            </div>
          </div>
          <div class="footer-container">
            <el-button
              v-if="resultStatus == 'pend'"
              type="primary"
              @click="goInfo">返回
            </el-button>
            <el-button
              v-else-if="resultStatus == 'fail'"
              type="primary"
              @click="goEdit">去修改
            </el-button>
          </div>
        </div>
      </div>

      <!--  表格弹窗  -->
      <bank-number-info
        ref="bankNumberInfo"
        @closeDialog="closeBankNumberInfoDialog">
      </bank-number-info>
    </div>

    <!--审批意见-->
    <flow-opinion
      :visible.sync="flowVisible"
      @change="flowConfirm"
    ></flow-opinion>

  </div>
</template>

<script>
import { getErrorMsg, getInvoiceList, getPayChannels, getQuote, merchantAuth } from '@/api/merchant'

import { validateParams } from '@/utils/validate'
import { getCityList, getProvinceList, getTownList } from '@/api/common'
import BankNumberInfo from '@/components/BankNumberInfo/index';
import VueSticky from 'vue-sticky'
import '@/styles/merchant-form.scss'
import FlowOpinion from '@/components/Flow/FlowOpinion'
import { editBusinessData } from '@/api/flow';
import { clearVoid, toPromise } from "@/utils";


export default {
  name: 'merchantAuth',
  components: {
    BankNumberInfo,
    FlowOpinion
  },
  directives: {
    'sticky': VueSticky,
  },
  data() {
    const _self = this;
    return {
      isEdit: false,
      isShowTip: false,
      activeStep: 1,
      industryList: [], // 行业类别
      industryListOptions: [],
      workCategoryList: [], // 工作类目
      workCategoryOptions: [], // 工作类目树
      mainstayList: [], // 代征主体
      provinceList: [],
      managementCityList: [],
      managementTownList: [],
      registerCityList: [],
      registerTownList: [],

      businessLicenseFileUrlShow: [], // el-upload插件绑定数组xxxShow，作用于回显图片
      receptionFileUrlShow: [],
      workIndoorFileUrlShow: [],
      doorPhotoFileUrlShow: [],
      idCardCopyFileUrlShow: [],
      idCardEmblemFileUrlShow: [],
      idCardHeadFileUrlShow: [],
      form_1: {
        // step 1
        mchNo: this.$route.query.mchNo,
        mchName: '',
        taxNo: '',
        registerAddrProvince: '',
        registerAddrCity: '',
        registerAddrTown: '',
        registerAddrDetail: '',
        registerAmount: '',
        managementScope: '',
        managementValidityDateType: '',
        managementTermBegin: '',
        managementTermEnd: '',
        businessLicenseFileUrl: '',
        certificateType: '',
        idFileType: '',
        idCardHeadFileUrl: '', // 人像面
        idCardEmblemFileUrl: '', // 国徽面
        idCardCopyFileUrl: '',
        legalPersonName: '',
        certificateNumber: '',
        certificateValidityDateType: '',
        certificateTermBegin: '',
        certificateTermEnd: '',
      },
      form_2: {
        // step 3
        contactName: '',
        contactPhone: '',
        servicePhone: '',

        shortName: '',
        managementAddrProvince: '',
        managementAddrCity: '',
        managementAddrTown: '',
        managementAddrDetail: '',
        doorPhotoFileUrl: '', // 门头照
        workIndoorFileUrl: '',
        receptionFileUrl: '',

        // 开票信息
        taxPayerType: '',
        invoiceAddress: '',
        invoiceBankName: '',
        invoiceAccountNo: '',
        invoicePhone: '',
        personnels: []
      },
      form_3: {
        accountNo: '',
        bankChannelNo: '',
        bankName: '',
        accounts: {},
      },
      invoiceCategoryList: [],
      form: {},
      rules: {
        // step 1
        mchName: [
          { required: true, message: "请输入企业名称", trigger: "blur" },
          // {validator: validateParams({type: 'Chinese', msg: '请输入正确的企业名称'}), trigger: 'blur'},
          {
            validator: validateParams({ type: 'Length', max: '30', msg: '企业名称最多可输入15个字' }),
            trigger: 'blur'
          },
        ],
        taxNo: [
          { required: true, message: '请输入统一社会信用代码' },
          {
            validator: validateParams({
              type: 'Reg',
              pattern: /^[a-zA-Z0-9]{1,20}$/,
              msg: '统一社会信用代码最多可输入20个字'
            }),
            trigger: 'blur'
          }
        ],
        registerAddrDetail: [
          { required: true, message: '请选择注册地址', trigger: 'blur' },
        ],
        registerAmount: [
          { required: true, message: "请输入注册资本", trigger: "blur" },
          { validator: validateParams({ type: 'Number', msg: '请输入正确的金额' }), trigger: 'blur' },
          {
            validator: validateParams({ type: 'Length', max: '20', msg: '注册资本最多可输入20个字' }),
            trigger: 'blur'
          },
        ],
        managementScope: [
          { required: true, message: "请输入经营范围", trigger: "blur" },
          {
            validator: validateParams({ type: 'Length', max: '500', msg: '经营范围最多可输入500个字' }),
            trigger: 'blur'
          },
        ],
        managementValidityDateType: [
          { required: true, message: "请选择营业期限", trigger: "change" },
          {
            validator: function (rule, value, callback) {
              if (value == 1) {
                if (!_self.form_1.managementTermBegin || !_self.form_1.managementTermEnd) {
                  callback(new Error('请选择营业期限'))
                } else {
                  callback()
                }
              } else {
                if (!_self.form_1.managementTermBegin) {
                  callback(new Error('请选择营业期限'))
                } else {
                  callback()
                }
              }
            },
            trigger: 'blur'
          }
        ],

        businessLicenseFileUrl: [
          { required: true, message: "请上传营业执照", trigger: "change" },
          { validator: validateParams({ type: 'Length', min: 1 }) }
        ],

        accountNo: [
          { required: true, message: "请输入银行卡号", trigger: "blur" },
        ],
        bankChannelNo: [
          { required: true, message: "请输入联行号", trigger: "change" },
        ],
        bankName: [
          { required: true, message: "请输入开户银行", trigger: "change" },
        ],

        // step 2
        certificateType: [
          { required: true, message: "请选择证件类型", trigger: "change" },
        ],
        idFileType: [
          { required: true, message: "请上传证件照", trigger: "change" },
          {
            validator: function (rule, value, callback) {
              if (value == '100') {
                if (_self.form_1.idCardHeadFileUrl.length == 0 || _self.form_1.idCardEmblemFileUrl.length == 0) {
                  callback(new Error('请上传证件照'))
                } else {
                  callback()
                }
              } else if (value == '101') {
                if (_self.form_1.idCardCopyFileUrl.length == 0) {
                  callback(new Error('请上传证件照'))
                } else {
                  callback()
                }
              }
            }, trigger: 'change'
          }
        ],

        legalPersonName: [
          { required: true, message: "请输入法定代表人姓名", trigger: "blur" },
          { validator: validateParams({ type: 'Chinese', msg: '请输入正确的姓名' }), trigger: 'blur' },
          { validator: validateParams({ type: 'Length', max: '30', msg: '姓名最多可输入15个字' }), trigger: 'blur' },
        ],
        // certificateNumber: [
        //   {required: true, message: "请输入证件号码", trigger: "blur"},
        //   {validator: validateParams({type: 'IdCard', msg: '请输入正确的证件号码'}), trigger: 'blur'},
        // ],
        certificateValidityDateType: [
          { required: true, message: "请选择证件有效期", trigger: "blur" },
          {
            validator: function (rule, value, callback) {
              if (value == 1) {
                if (!_self.form_1.certificateTermBegin || !_self.form_1.certificateTermEnd) {
                  callback('请选择证件有效期')
                } else {
                  callback()
                }
              } else if (value == 2) {
                if (!_self.form_1.certificateTermBegin) {
                  callback('请选择证件有效期')
                } else {
                  callback()
                }
              }
            }, trigger: 'blur'
          }
        ],

        // step 3
        contactName: [
          { required: true, message: "请输入负责人姓名", trigger: "blur" },
          { validator: validateParams({ type: 'Chinese', msg: '请输入正确的姓名' }), trigger: 'blur' },
          { validator: validateParams({ type: 'Length', max: '15', msg: '姓名最多可输入15个字' }), trigger: 'blur' },
        ],
        contactPhone: [
          { required: true, message: "请输入负责人手机号码", trigger: "blur" },
          { validator: validateParams({ type: 'Phone', msg: '请输入正确的手机号码' }), trigger: 'blur' },
        ],
        servicePhone: [
          { required: true, message: "请输入客服电话", trigger: "blur" },
          {
            validator: validateParams({ type: 'Reg', pattern: /[0-9-]+/, msg: '请输入正确的客服电话' }),
            trigger: 'blur'
          },
        ],

        shortName: [
          { required: true, message: "请输入企业简称", trigger: "blur" },
          { validator: validateParams({ type: 'Chinese', msg: '请输入正确的企业简称' }), trigger: 'blur' },
          {
            validator: validateParams({ type: 'Length', max: '15', msg: '企业简称最多可输入15个字' }),
            trigger: 'blur'
          },
        ],
        managementAddrDetail: [
          { required: true, message: "请选择实际经营地址", trigger: 'blur' },
        ],
        doorPhotoFileUrl: [
          { required: true, message: '请上传公司照片' },
          { validator: validateParams({ type: 'Length', min: 1 }) }
        ],
        workIndoorFileUrl: [
          { required: true, message: '请上传公司照片' },
          { validator: validateParams({ type: 'Length', min: 1 }) }
        ],
        receptionFileUrl: [
          { required: true, message: '请上传公司照片' },
          { validator: validateParams({ type: 'Length', min: 1 }) }
        ],
        bizPlatformName: [
          { validator: validateParams({ type: 'Null', otherRule: 'Chinese', msg: '请输入中文' }), trigger: 'blur' },
          {
            validator: validateParams({ type: 'Null', otherRule: 'Length', max: '15', msg: '名称最多可输入15个字' }),
            trigger: 'blur'
          }
        ],
        taxPayerType: [
          { required: true, message: '请选择纳税人类型', trigger: 'change' },
        ],
        registerAddrInfo: [
          { required: true, message: '请输入单位注册地址及电话', trigger: 'blur' },
        ],
        invoiceAddress: [
          { required: true, message: '请输入开票地址', trigger: 'blur' },
        ],
        invoiceBankName: [
          { required: true, message: '请输入开票银行', trigger: 'blur' },
        ],
        invoiceAccountNo: [
          { required: true, message: '请输入开票银行账户', trigger: 'blur' },
        ],
        invoicePhone: [
          { required: true, message: '请输入开票联系电话', trigger: 'blur' },
        ],
        accounts: [
          { required: true, message: '请选择开通的发放方式', trigger: 'blur' },
          {
            validator: (rule, val, cb) => {
              let result = true;
              if (Object.keys(_self.form_3.accounts).length == 0) {
                result = false;
              }
              for (let mch in _self.form_3.accounts) {
                let accountList = _self.form_3.accounts[mch];
                if (accountList.length == 0) {
                  result = false;
                }
                for (let i = 0; i < accountList.length; i++) {
                  if (!accountList[i].payChannelNo) {
                    result = false;
                    break;
                  }
                }
                if (!result) {
                  break;
                }
              }
              if (!result) {
                cb(new Error('请选择通道'))
              } else {
                cb();
              }
            }, trigger: 'blur'
          }
        ]
      },
      // step 3
      result: '',

      // 认证状态记录
      resultStatus: 'pend',

      editAccess: false, // 是否可以编辑负责人

      errMsg: {
        id: '',
        createTime: '',
        approvalOpinion: '',
        initiatorName: '',
      },
      protocalCheck: false,

      flowVisible: false, // 审批意见显示控制
      flowRemark: '',

      channelSelect: {},
      bankChannel: [],
      alipayChannel: [],
      wxChannel: [],

      defaultAddress: '',
      defaultManageAddress: '',
    }
  },
  computed: {
    payChannel() {
      return [
        this.bankChannel,
        this.alipayChannel,
        this.wxChannel,
      ]
    },
    certificateRules() {
      if (this.$dictCode('CertificateTypeEnum', this.form_1.certificateType).desc == '身份证') {
        return [
          { required: true, message: "请输入证件号码", trigger: "blur" },
          { validator: validateParams({ type: 'IdCard', msg: '请输入正确的证件号码' }), trigger: 'blur' },
        ]
      } else {
        return [
          { required: true, message: "请输入证件号码", trigger: "blur" },
          { validator: validateParams({ type: 'Length', msg: '请输入正确的证件号码', min: 1 }), trigger: 'blur' },
        ]
      }
    },
    processId() {
      return this.$route.query.processId || '';
    },
    taskId() {
      return this.$route.query.taskId || '';
    },
  },
  watch: {
    activeStep: {
      immediate: true,
      handler(val) {
        if (val == 3) {
          if (!this.form_2.taxNo) {
            this.form_2.taxNo = this.form_1.taxNo;
          }
          if (!this.form_2.invoiceAddress) {
            this.form_2.invoiceAddress = this.form_1.registerAddrProvince + this.form_1.registerAddrCity + this.form_1.registerAddrTown + this.form_1.registerAddrDetail
          }
          if (!this.form_2.invoiceAccountNo) {
            this.form_2.invoiceAccountNo = this.form_1.accountNo
          }
          if (!this.form_2.invoiceBankName) {
            this.form_2.invoiceBankName = this.form_1.bankName
          }
        }
      }
    },
  },
  async mounted() {
    this.getPayChannelList();
    getInvoiceList({ mchNo: this.$route.query.mchNo || this.userData.mchNo }).then(res => {
      res.data.forEach(item => {
        this.invoiceCategoryList.push(...item.invoiceCategoryList)
      })
    });
    if (sessionStorage.getItem('mainInfo')) {
      this.form = Object.assign(this.form, JSON.parse(sessionStorage.getItem('mainInfo')));
      // 处理图片
      this.businessLicenseFileUrlShow = this.form.businessLicenseFileUrl
        ? [{
          url: this.fileUrl + this.form.businessLicenseFileUrl,
          name: this.isPdf(this.form.businessLicenseFileUrl) ? '营业执照.pdf' : ''
        }]
        : [];
      this.idCardHeadFileUrlShow = this.form.idCardHeadFileUrl ? [{ url: this.fileUrl + this.form.idCardHeadFileUrl }] : [];
      this.idCardEmblemFileUrlShow = this.form.idCardEmblemFileUrl ? [{ url: this.fileUrl + this.form.idCardEmblemFileUrl }] : [];
      this.idCardCopyFileUrlShow = this.form.idCardCopyFileUrl ? [{ url: this.fileUrl + this.form.idCardCopyFileUrl }] : [];
      this.doorPhotoFileUrlShow = this.form.doorPhotoFileUrl ? [{ url: this.fileUrl + this.form.doorPhotoFileUrl }] : [];
      this.workIndoorFileUrlShow = this.form.workIndoorFileUrl ? [{ url: this.fileUrl + this.form.workIndoorFileUrl }] : [];
      this.receptionFileUrlShow = this.form.receptionFileUrl ? [{ url: this.fileUrl + this.form.receptionFileUrl }] : [];
      if (this.form.registerAddrProvince) {
        this.defaultAddress = this.form.registerAddrProvince + '/' + this.form.registerAddrCity + '/' + this.form.registerAddrTown;
        this.defaultManageAddress = this.form.managementAddrProvince + '/' + this.form.managementAddrCity + '/' + this.form.managementAddrTown;
      }

      if (this.form.idCardCopyFileUrl) {
        this.form.idFileType = '101';
      } else if (this.form.idCardHeadFileUrl) {
        this.form.idFileType = '100';
      }

      if (this.processId) {
        this.isEdit = true;
      } else {
        this.isEdit = false;
      }
    }
    if (this.$route.query.result == 'fail') {
      this.activeStep = 4;
      this.resultStatus = 'fail';
      this.getApprovalFlow()
    }
    if (sessionStorage.getItem('bankInfo')) {
      let form = JSON.parse(sessionStorage.getItem('bankInfo'));
      this.isEdit = true;
      this.$set(this.form, 'accountNo', form.accountNo);
      this.$set(this.form, 'bankChannelNo', form.bankChannelNo);
      this.$set(this.form, 'bankName', form.bankName);
    }
    await this.getQuote();

    for (let i = 1; i <= 3; i++) {
      let form = 'form_' + i;
      for (let p in this[form]) {
        if (p in this.form) {
          this[form][p] = this.form[p];
        }
      }
    }
  },
  destroyed() {
    sessionStorage.removeItem('mainInfo');
    sessionStorage.removeItem('bankInfo');
  },
  methods: {
    getProvinceList() {
      getProvinceList().then(response => {
        this.provinceList = response.data;
      })
    },
    async getCityList(code, resetItems, val) {
      let form = this['form_' + this.activeStep]
      form[code + 'AddrProvince'] = val[1];
      const response = await getCityList({
        provinceNo: val[0]
      });
      this[code + 'CityList'] = response.data;
      resetItems.forEach(item => {
        form[item] = ''
      })
      return this[code + 'CityList']
    },
    async getTownList(code, resetItems, val) {
      let form = this['form_' + this.activeStep]
      form[code + 'AddrCity'] = val[1];
      const response = await getTownList({
        cityNo: val[0]
      });
      this[code + 'TownList'] = response.data;
      resetItems.forEach(item => {
        form[item] = ''
      })
      return this[code + 'TownList']
    },
    searchBankNumberInfo() {
      this.$refs.bankNumberInfo.search();
      this.$refs.bankNumberInfo.isShow = true;
    },
    closeBankNumberInfoDialog(item) {
      let form = this['form_' + this.activeStep]
      if (item) {
        this.$set(form, 'bankChannelNo', item.bankChannelNo)
        this.$set(form, 'bankName', item.bankName)
      }
    },
    getTimeRange(val, param) {
      let form = this['form_' + this.activeStep]
      form[param + 'Begin'] = val[0];
      form[param + 'End'] = val[1];
    },
    beforeAvatarUpload(file) {
      const isTrueType = ['image/jpeg', 'image/png', 'image/bmp', 'image/gif'].includes(file.type);
      const isLt6M = file.size / 1024 / 1024 < 6;

      if (!isTrueType) {
        this.$message.error('上传图片只能是 bmp、png、jpeg、jpg或gif 格式!');
      }
      if (!isLt6M) {
        this.$message.error('上传图片大小不能超过 6MB!');
      }
      return isTrueType && isLt6M;
    },
    handleRemove(file, fileList, param) {
      let form = this['form_' + this.activeStep]
      form[param] = fileList;
    },
    handleSuccess(response, file, fileList, param, ref) {
      let form = this['form_' + this.activeStep]
      form[param] = fileList;

      const refs = ref || param;
      this.$refs[refs].clearValidate()
    },
    async nextStep() {
      if (this.activeStep == 3) {
        this.$refs.form_3.validate(async (valid, obj) => {
          if (!valid) {
            for (let key in obj) {
              this.$message.error(obj[key][0].message);
              return;
            }
          }
          if (!this.flowVisible) {
            this.flowVisible = true;
            return;
          }
          this.flowVisible = false;
          let formData = { ...this.form, ...this.form_1, ...this.form_2, ...this.form_3 };
          // 遍历上传图片的fileUrl，拼装数据
          formData.businessLicenseFileUrl && (formData.businessLicenseFileUrl = (formData['businessLicenseFileUrl'][0].response && formData['businessLicenseFileUrl'][0].response.data) || formData.businessLicenseFileUrl);
          formData.idCardHeadFileUrl && (formData.idCardHeadFileUrl = (formData['idCardHeadFileUrl'][0].response && formData['idCardHeadFileUrl'][0].response.data) || formData.idCardHeadFileUrl);
          formData.idCardEmblemFileUrl && (formData.idCardEmblemFileUrl = (formData['idCardEmblemFileUrl'][0].response && formData['idCardEmblemFileUrl'][0].response.data) || formData.idCardEmblemFileUrl);
          formData.idCardCopyFileUrl && (formData.idCardCopyFileUrl = (formData['idCardCopyFileUrl'][0].response && formData['idCardCopyFileUrl'][0].response.data) || formData.idCardCopyFileUrl);
          formData.doorPhotoFileUrl && (formData.doorPhotoFileUrl = (formData['doorPhotoFileUrl'][0].response && formData['doorPhotoFileUrl'][0].response.data) || formData.doorPhotoFileUrl);
          formData.workIndoorFileUrl && (formData.workIndoorFileUrl = (formData['workIndoorFileUrl'][0].response && formData['workIndoorFileUrl'][0].response.data) || formData.workIndoorFileUrl);
          formData.receptionFileUrl && (formData.receptionFileUrl = (formData['receptionFileUrl'][0].response && formData['receptionFileUrl'][0].response.data) || formData.receptionFileUrl);

          delete formData.idFileType;

          clearVoid(formData);

          if (this.isEdit) {
            const { data } = await editBusinessData({
              extInfo: JSON.stringify(formData),
              commonFlowId: this.processId,
              taskId: this.taskId,
              remark: this.flowRemark,
            });
            data && this.$message.success(data);
            this.$router.push({
              path: '/audit/detailProcess',
              query: {
                processId: this.processId,
                taskId: this.taskId,
              }
            })
          } else {
            const { data } = await merchantAuth({
              extObj: formData,
              participant: {},
              condition: {},
              remark: this.flowRemark,
            });
            this.activeStep++;
            this.result = data;
          }
        });
      } else {
        let form = this.$refs['form_' + this.activeStep];
        const [err, _] = await toPromise(form.validate())
        if (err) return;
        this.activeStep++;
        if (this.activeStep == 2) {
          this.form_2.personnels.splice(0, 1, {
            name: this.form_1.legalPersonName,
            idCardNumber: this.form_1.certificateNumber,
            position: '法人',
            isLegal: true,
          })
        }
      }
    },
    goEdit() {
      this.activeStep = 1;
      this.resultStatus = 'pend';
      this.getBackMainInfo();
    },
    async getApprovalFlow() {
      const { data } = await getErrorMsg();
      this.errMsg = data.approvalFlow;
    },
    async getBackMainInfo() {
      this.form = Object.assign({}, JSON.parse(this.errMsg.extInfo));
      this.businessLicenseFileUrlShow = this.form.businessLicenseFileUrl
        ? [{
          url: this.fileUrl + this.form.businessLicenseFileUrl,
          name: this.isPdf(this.form.businessLicenseFileUrl) ? '营业执照.pdf' : ''
        }]
        : [];
      this.idCardHeadFileUrlShow = this.form.idCardHeadFileUrl ? [{ url: this.fileUrl + this.form.idCardHeadFileUrl }] : [];
      this.idCardEmblemFileUrlShow = this.form.idCardEmblemFileUrl ? [{ url: this.fileUrl + this.form.idCardEmblemFileUrl }] : [];
      this.idCardCopyFileUrlShow = this.form.idCardCopyFileUrl ? [{ url: this.fileUrl + this.form.idCardCopyFileUrl }] : [];
      this.doorPhotoFileUrlShow = this.form.doorPhotoFileUrl ? [{ url: this.fileUrl + this.form.doorPhotoFileUrl }] : [];
      this.workIndoorFileUrlShow = this.form.workIndoorFileUrl ? [{ url: this.fileUrl + this.form.workIndoorFileUrl }] : [];
      this.receptionFileUrlShow = this.form.receptionFileUrl ? [{ url: this.fileUrl + this.form.receptionFileUrl }] : [];
      if (this.form.managementValidityDateType) {
        this.form.managementValidityDateType += ''
      }
      if (this.form.certificateValidityDateType) {
        this.form.certificateValidityDateType += ''
      }
      if (this.form.certificateType) {
        this.form.certificateType += ''
      }
      this.form.idFileType = this.form.idCardCopyFileUrl ? '101' : '100';
      this.form.businessLicenseFileUrl = this.businessLicenseFileUrlShow;

      // 需要重新获取省市地列表
      if (this.provinceList.length > 0) {
        await this.reGetAddressList('Province', 'register');
        await this.reGetAddressList('Province', 'management');
      }
      for (let i = 1; i <= 3; i++) {
        let form = 'form_' + i;
        for (let p in this[form]) {
          if (p in this.form) {
            this[form][p] = this.form[p];
          }
        }
      }
    },
    async reGetAddressList(next, type, parent) {
      let temp = parent ? parent : [];
      let lowerNext = next.toLowerCase()
      let list = next == 'Province' ? this[lowerNext + 'List'] : this[type + next + 'List'];
      if (list.length <= 0) {
        list = await this['get' + next + 'List'](type, [], temp);
      }
      // 拿到对应的上级列表项
      for (let item of list) {
        if (this.form[type + 'Addr' + next] == item[lowerNext + 'Name']) {
          temp = [item[lowerNext + 'No'], item[lowerNext + 'Name']];
          break;
        }
      }
      if (next == 'Province') {
        await this.reGetAddressList('City', type, temp);
      }
      if (next == 'City') {
        await this.reGetAddressList('Town', type, temp);
      }
    },
    getProtocal(index) {
      let doc;
      const a = document.createElement('a');
      if (index == 1) {
        doc = require('@/assets/doc/protocal.docx');
        a.download = '汇聚支付分账服务协议.docx';
      } else {
        doc = require('@/assets/doc/protocal2.docx');
        a.download = '平台商户入驻框架协议.docx';
      }
      a.href = doc;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    },
    backStep() {
      if (this.activeStep == 1) {
        this.goInfo()
      } else {
        this.activeStep--
      }
    },
    handleInvoiceChange(val) {
      for (let i = 0; i < this.invoiceCategoryList.length; i++) {
        if (val == this.invoiceCategoryList[i].invoiceCategoryCode) {
          this.form_2.defaultInvoiceCategoryName = this.invoiceCategoryList[i].invoiceCategoryName;
          break;
        }
      }
    },
    handleFileRemove(file, param) {
      this['form_' + this.activeStep][param] = this['form_' + this.activeStep][param].filter(item => {
        return item.uid !== file.uid;
      });
      this[param + 'Show'] = this['form_' + this.activeStep][param]
    },
    flowConfirm(remark) {
      this.flowRemark = remark;
      this.nextStep();
    },
    goInfo() {
      this.$router.push(this.permissionToPath('pms:info:view'));
    },
    onChannelTypeChange(val, type, i) {
      const channelType = Number(type.code);
      this.$refs.accounts[i].clearValidate();
      this.channelSelect[this.mainstayList[i].mainstayMchNo].splice(channelType - 1, 1, val);
      if (!this.form_3.accounts[this.mainstayList[i].mainstayMchNo]) {
        this.$set(this.form_3.accounts, this.mainstayList[i].mainstayMchNo, []);
      }
      if (val == 1) {
        this.form_3.accounts[this.mainstayList[i].mainstayMchNo].push({
          "channelType": channelType,
          "channelName": type.desc,
          "payChannelNo": "",
          payChannelName: ''
        })
      } else {
        this.form_3.accounts[this.mainstayList[i].mainstayMchNo] = this.form_3.accounts[this.mainstayList[i].mainstayMchNo].filter((item => {
          return item.channelType != channelType;
        }))
      }
    },
    async getPayChannelList() {
      const { data } = await getPayChannels();
      data.data.forEach(item => {
        item.channelType && item.channelType.forEach(i => {
          if (i == 1) {
            this.bankChannel.push(item)
          } else if (i == 2) {
            this.alipayChannel.push(item)
          } else if (i == 3) {
            this.wxChannel.push(item)
          }
        })
      })
    },
    async getQuote() {
      const { data } = await getQuote();
      const tempMap = {};
      data.forEach(item => {
        if (!tempMap[item.mainstayMchNo]) {
          tempMap[item.mainstayMchNo] = true;
          this.mainstayList.push({ mainstayMchNo: item.mainstayMchNo, mainstayMchName: item.mainstayMchName });
          this.$set(this.channelSelect, item.mainstayMchNo, [0, 0]);
        }
        if (this.processId) {
          let accountInfo = this.form.accounts[item.mainstayMchNo];
          accountInfo.forEach((i, index) => {
            if (i.payChannelNo !== '') {
              this.channelSelect[item.mainstayMchNo].splice(i.channelType - 1, 1, 1);
            }
          })
        }
      });
    },
    handlePayChannelChange(account, val) {
      if (!val) {
        account.payChannelName = '';
      } else {
        let payChannel = this.payChannel[account.channelType - 1];
        for (let i = 0; i < payChannel.length; i++) {
          if (payChannel[i].payChannelNo == val) {
            account.payChannelName = payChannel[i].payChannelName;
          }
        }
      }
    },
    // 增加董监高
    addPeople() {
      this.form_2.personnels.push({
        name: '',
        idCardNumber: '',
        position: '',
        isLegal: false,
      })
    },
    // 删除董监高
    deletePeople(index) {
      this.form_2.personnels.splice(index, 1)
    },

  }
}
</script>

<style
  lang="scss"
  scoped>
.page-container {
  .header-container {
    margin-bottom: 40px;
    padding: 20px 0;
    background: #fff;
  }

  .content-container {
    padding-top: 20px;
    padding-left: 20px;
    padding-right: 20px;
    background: #fff;

    .el-form {
      width: 800px;
      margin: 0 auto;
    }

    .hgroup {
      text-align: center;
    }

    .example-image {
      float: left;
      width: 148px;
      height: 148px;
      margin-right: 20px;
    }

    .footer-container {
      text-align: center;
    }
  }

  .subTitle {
    position: relative;
    margin-left: 30px;
    margin-bottom: 20px;
    padding-left: 30px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;

    &:before {
      position: absolute;
      content: '';
      left: 10px;
      width: 4px;
      height: calc(100% - 10px);
      background: #20a0ff;
    }
  }

  .step_1 {
    .job-box {
      padding-bottom: 50px;
    }
  }

  .step_4 {
    padding: 50px 0;
    text-align: center;

    p {
      margin-bottom: 20px;
    }

    svg {
      width: 100px;
      height: 100px;
    }
  }

  .protocal-container {
    margin: 20px 0
  }

  .protocal-link {
    cursor: pointer;
    color: $mainColor;
  }

  .el-checkbox-label {
    color: #000;
  }
}
</style>
