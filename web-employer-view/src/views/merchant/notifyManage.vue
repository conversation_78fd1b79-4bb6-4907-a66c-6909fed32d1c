<template>
  <div class="page-container">
    <div v-if="step == 1">
      <el-form label-width="150px">
        <el-form-item label="商户编号：">
          {{ userData.mchNo }}
        </el-form-item>
        <el-form-item label="负责人手机号：">
          {{ leaderData.phone | phoneEncrypt }} ({{ leaderData.name | nameEncrypt }})
        </el-form-item>
        <el-form-item label="短信验证码：">
          <el-input
            v-model="valifyForm.code"
            @input="handleChange"
            ref="smsInput"
            style="width: 400px;"
          >
            <template slot="append">
              <el-button
                class="sms-btn"
                :disabled="!leaderData.phone || counter !== null"
                @click="sendCode"
              >
                {{ btnContent }}
              </el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <div v-else-if="step == 2">
      <warning-block>注：请如实准确填写授权回调地址，否则所有责任（包括但不限于赔偿损失）概由贵司负责</warning-block>
      <el-form
        :model="form[0]"
        label-width="150px"
        ref="form1"
        :rules="rules">
        <el-form-item
          label="发放结果通知："
          prop="notifyStatus">
          <el-switch
            v-model="form[0].notifyStatus"
            :active-value="100"
            :inactive-value="101"
          ></el-switch>
          <p class="color-gray">开启后，当商户使用后台批量发放功能，订单发放完成后平台将异步通知发放结果至指定回调地址。商户端收到此通知后，返回字符串{"resp_code":"success"}表达成功</p>
        </el-form-item>
        <el-form-item
          label="回调地址类型："
          prop="notifyUrlType">
          <el-select
            v-model="form[0].notifyUrlType"
            clearable
          >
            <el-option
              label="http"
              value="HTTP"></el-option>
            <el-option
              label="https"
              value="HTTPS"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="回调地址："
          prop="notifyUrl">
          <el-input
            v-model="form[0].notifyUrl"
            type="textarea"
            :rows="5"
            placeholder="输入地址例：http://auth.example.com/callback"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-divider></el-divider>
      <el-form
        :model="form[1]"
        label-width="150px"
        ref="form2"
        :rules="rules">
        <el-form-item
          label="充值结果通知："
          prop="notifyStatus">
          <el-switch
            v-model="form[1].notifyStatus"
            :active-value="100"
            :inactive-value="101"
          ></el-switch>
          <p class="color-gray">开启后，当商户充值完成，平台将异步通知充值结果至指定回调地址。商户端收到此通知后，返回字符串{"resp_code":"success"}表达成功</p>
        </el-form-item>
        <el-form-item
          label="回调地址类型：">
          <el-select
            v-model="form[1].notifyUrlType"
            clearable
          >
            <el-option
              label="http"
              value="HTTP"></el-option>
            <el-option
              label="https"
              value="HTTPS"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="回调地址：">
          <el-input
            v-model="form[1].notifyUrl"
            type="textarea"
            :rows="5"
            placeholder="输入地址例：http://auth.example.com/callback"
          ></el-input>
        </el-form-item>
      </el-form>
      <div class="form-btn-group">
        <el-button
          type="primary"
          @click="confirm">确定
        </el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getLeader } from '@/api/safeSetting'
import { getNotifyInfo, updateNotifyUrl } from '@/api/merchant';
import { toPromise } from "@/utils";
import { sendSmsCode } from "@/api/common";


export default {
  name: "notify-manage",
  data() {
    return {
      form: [],
      rules: {
        notifyUrlType: [{ required: true, message: '请选择回调地址类型', trigger: 'change' }],
        notifyUrl: [{ required: true, message: '未填写授权回调地址', trigger: 'blur' }],
      },
      step: 1,

      leaderData: {},
      valifyForm: {
        phone: '',
        code: '',
        mchNo: '',
      },
      counter: null,

      btnContent: '获取验证码'
    }
  },
  async mounted() {
    const { data: leaderData } = await getLeader();
    this.leaderData = leaderData;
    this.valifyForm.phone = this.leaderData.phone;
    this.valifyForm.mchNo = this.userData.mchNo;
  },
  methods: {
    async sendCode() {
      const { data } = await sendSmsCode({
        phone: this.leaderData.phone,
        mchNo: this.userData.mchNo
      });
      data && this.$message.success(data);
      this.$refs.smsInput.focus();
      this.changeCount(59);
    },
    changeCount(second) {
      this.counter && clearTimeout(this.counter);
      if (second == 0) {
        this.btnContent = '获取验证码';
        this.counter = null;
      } else {
        this.btnContent = second + '秒后重新获取';
        this.counter = setTimeout(() => {
          this.changeCount(--second);
        }, 1000);
      }
    },
    async handleChange(val) {
      if (val.length >= 6) {
        await this.getNotifyInfo();
        this.step = 2;
        this.counter && clearTimeout(this.counter);
        this.counter = null;
        this.btnContent = '获取验证码';
      }
    },
    async getNotifyInfo() {
      const { data } = await getNotifyInfo(this.valifyForm);
      const ret = []
      if (Array.isArray(data)) {
        const len = data.length || 2
        for (let i = 0; i < len; i++) {
          ret[i] = data[i] || this.getDefaultForm(i)
        }
      }
      this.form = ret
    },
    cancel() {
      this.$refs.form1.resetFields();
      this.$refs.form2.resetFields();
      this.valifyForm.code = '';
      this.step = 1;
    },
    async confirm() {
      const [err] = await toPromise(Promise.all([
        this.$refs.form1.validate(),
        this.$refs.form2.validate(),
      ]))
      if (err) {
        return this.$message.error('请补充信息')
      }
      const { data } = await updateNotifyUrl({ notify: this.form });
      data && this.$message.success(data);
    },
    getDefaultForm(type) {
      return {
        notifyType: type === 0 ? 100 : 101,
        notifyUrlType: '',
        notifyUrl: '',
        notifyStatus: 101,
      }
    }
  },
}
</script>

<style
  lang="scss"
  scoped>

.page-container {
  padding-bottom: 100px;

  .sms-btn {
    background: $mainColor;
    color: #fff;
  }
}

</style>
