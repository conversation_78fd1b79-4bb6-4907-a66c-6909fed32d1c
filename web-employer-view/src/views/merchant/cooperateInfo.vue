<template>
  <el-card>
    <div slot="header">合作信息</div>
      <div class="box-wrapper">
        <div class="flex-container">
          <div class="flex-wrapper search-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">商户编号</span>
              {{ cooperationInfo.mchNo }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">商户名称</span>
              {{ cooperationInfo.mchName }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">品牌名称</span>
              {{ cooperationInfo.branchName }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">企业行业类别</span>
              {{ cooperationInfo.industryTypeName }}
            </div>
          </div>
          <div class="flex-wrapper search-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">预计用工人数</span>
              {{ cooperationInfo.workerNum }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">预期可实现C端签约率区间</span>
              {{ $dictCode('SignRateLevelEnum', cooperationInfo.signRateLevel).desc }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者单人月经营所得9.7万以下占比</span>
              {{ cooperationInfo.workerMonthIncomeRate }}
            </div>
          </div>
          <div class="flex-wrapper search-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">月流水预估</span>
              {{ cooperationInfo.monthMoneySlip }}万元
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">是否可提供服务记录及经营所得计算明细</span>
              {{ $dictCode('ProvideIncomeDetailTypeEnum', cooperationInfo.signRateLevel).desc }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">公司自有业务平台名称</span>
              {{ cooperationInfo.bizPlatformName }}
            </div>
          </div>
          <div class="flex-wrapper search-wrapper">
            <div class="flex-vertical-item flex-item-half"><span class="flex-vertical-label">公司网站</span>
              {{ cooperationInfo.companyWebsite }}
            </div>
          </div>
          <div class="flex-wrapper search-wrapper" v-for="(item, index) in cooperationInfo.positionVoList" :key="index">
            <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者的工作场所</span>
              {{ $dictCode('WorkPlaceEnum', item.workplaceCode).desc }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者服务类型</span>
              {{ item.workCategoryName }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">建议发票类目</span>
              <div class="invoice-category-list">
                <el-tooltip
                  v-for="(cate, key) in item.jsonEntity.invoiceCategoryList"
                  :key="key"
                  :content="cate.invoiceCategoryName"
                  :disabled="calculateDisAble(cate.invoiceCategoryName)">
                  <el-tag type="info" class="invoice-cate-item">
                    {{ cate.invoiceCategoryName }}
                  </el-tag>
                </el-tooltip>
              </div>
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">企业从事业务</span>
              {{ item.businessDesc }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者服务描述</span>
              {{ item.serviceDesc }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者服务所得计算规则</span>
              {{ item.chargeRuleDesc }}
            </div>
          </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item flex-vertical-main">
                <span class="flex-vertical-label">补充信息：</span>
                <div class="clearfix">
                  <span
                    class="flex-vertical-content flex-func-link"
                    v-for="(item, index) in cooperationInfo.supplementFileUrls"
                    :key="index"
                    v-preview="fileUrl + item"
                  >点击查看
                  </span>
                </div>
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item flex-vertical-main">
                <span class="flex-vertical-label">公司宣传文件：</span>
                <div>
                  <el-link
                    class="flex-vertical-content"
                    v-for="(item, index) in cooperationInfo.cooperateFileList"
                    :key="index"
                    :href="fileUrl + item"
                    type="primary"
                    target="_blank">点击查看</el-link>
                </div>
              </div>
            </div>
          <!-- <el-table class="content-container" :data="cooperationInfo.quoteVoList">
            <el-table-column label="id" prop="mainstayMchNo">

            </el-table-column>
            <el-table-column label="代征主体" prop="mainstayMchName">

            </el-table-column>
            <el-table-column label="商户费率" prop="rate">
              <template v-slot="{row}">
                <div v-if="row.formulaType == 0 || row.formulaType == 2">
                  {{ row.rate }} %
                </div>
              </template>
            </el-table-column>
            <el-table-column label="固定金额" prop="fixedFee">
              <template v-slot="{row}">
                <div v-if="row.formulaType == 1 || row.formulaType == 2">
                  {{ row.fixedFee | moneyFormat }}
                </div>
              </template>
            </el-table-column>
          </el-table> -->
          <template v-if="zxhInfo.length">
            <p class="subTitle">智享汇产品报价单</p>
            <el-table
              :data="zxhInfo"
              :span-method="spanMethod"
            >
              <el-table-column label="产品">
                <el-table-column label="产品名称">
                  <template v-slot="{row}">
                    {{ row.productNo }} <br>
                    {{ row.productName }}
                  </template>
                </el-table-column>
                <el-table-column label="供应商">
                  <template v-slot="{row}">
                    {{ row.mainstayMchNo }} <br>
                    {{ row.mainstayMchName }}
                  </template>
                </el-table-column>
                <el-table-column label="岗位类别">
                  <template v-slot="{row}">
                    <el-tooltip
                      v-for="(item, index) in row.positionNameList"
                      :key="index"
                      :content="item"
                    >
                      <el-tag type="info">
                        {{ item }}
                      </el-tag>
                    </el-tooltip>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column>
                <template v-slot:header>
                  报价
                  <el-tooltip content="如有阶梯计费、单笔金额等特殊计费需求，可使用「编辑计费」功能">
                    <i class="el-icon-warning"></i>
                  </el-tooltip>
                </template>
                <el-table-column label="公式类型">
                  <template v-slot="{row}">
                    {{ $dictCode('FormulaEnum', row.quoteData.formulaType).desc }}
                  </template>
                </el-table-column>
                <el-table-column label="固定金额">
                  <template v-slot="{row}">
                    <div v-if="row.quoteData.formulaType == 1 || row.quoteData.formulaType == 2">
                      {{ row.quoteData.fixedFee }}
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="比例">
                  <template v-slot="{row}">
                    <div v-if="row.quoteData.formulaType == 0 || row.quoteData.formulaType == 2">
                      {{ row.quoteData.rate }} %
                    </div>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="特殊计费规则">
                <template v-slot="{row}">
                  <div
                    v-for="(item, index) in row.ruleParam"
                    :key="index"
                  >
                    {{ $dictCode('ProductFeeSpecialRuleTypeEnum', item.specialRuleType).desc }}
                    {{ $dictCode('CompareTypeEnum', item.compareType).desc }}
                    {{ item.value }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="状态">
                <template v-slot="{row}">
                  <el-tag :type="row.status == 100 ? '' : 'info'">
                    {{ $dictCode('QuoteStatusEnum', row.status).desc }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </template>
          <template v-if="ckhInfo.length">
            <p class="subTitle">创客汇产品报价单</p>
            <el-table :data="ckhInfo">
              <el-table-column label="产品">
                <el-table-column label="产品名称">
                  <template v-slot="{row}">
                    {{ row.productNo }} <br>
                    {{ row.productName }}
                  </template>
                </el-table-column>
                <el-table-column label="供应商">
                  <template v-slot="{row}">
                    {{ row.mainstayMchNo }} <br>
                    {{ row.mainstayMchName }}
                  </template>
                </el-table-column>
                <el-table-column label="岗位类别">
                  <template v-slot="{row}">
                    <el-tooltip
                      v-for="(item, index) in row.positionNameList"
                      :key="index"
                      :content="item"
                    >
                      <el-tag type="info">
                        {{ item }}
                      </el-tag>
                    </el-tooltip>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="个税">
                <el-table-column label="个税类型" prop="taxTypeDesc">
                  <template v-slot="{row, column}">
                    {{ row.merchantCkhQuote && row.merchantCkhQuote[column.property] }}
                  </template>
                </el-table-column>
                <el-table-column label="个税承担方">
                  <template v-slot="{row}">
                    {{ row.merchantCkhQuote && $dictCode('TaxPayerEnum', row.merchantCkhQuote.taxPayer).desc }}
                  </template>
                </el-table-column>
                <el-table-column label="增值税税率 %">
                  <template v-slot="{row}">
                    {{ row.merchantCkhQuote && row.merchantCkhQuote.addedTaxRatePct }}
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="服务费比例 %" prop="serviceFeeRatePct">
                <template v-slot="{row, column}">
                  {{ row.merchantCkhQuote && row.merchantCkhQuote[column.property] }}
                </template>
              </el-table-column>
              <el-table-column label="服务费结算模式" prop="balancedMode">
                <template v-slot="{row, column}">
                  {{ row.merchantCkhQuote && $dictCode('BalancedEnum', row.merchantCkhQuote[column.property]).desc }}
                </template>
              </el-table-column>
              <el-table-column label="状态">
                <template v-slot="{row}">
                  <el-tag :type="row.status == 100 ? '' : 'info'">
                    {{ $dictCode('QuoteStatusEnum', row.status).desc }}
                  </el-tag>
                </template>
              </el-table-column>
            </el-table>
          </template>

        </div>
      </div>
  </el-card>
</template>

<script>
  import { getEmployerCooperate, getQuoteInfo } from '@/api/merchant'
  export default {
    data() {
      return {
        cooperationInfo: {},
        quoteInfo: [],
      }
    },
    computed: {
      zxhInfo() {
        let ret = []
        let list = this.quoteInfo.filter(item => item.productNo === 'ZXH')
        list.forEach((item, index) => {
          const length = item.quoteRateList ? item.quoteRateList.length : 0;
          if (item.quoteRateList) {
            item.quoteRateList.forEach((quote, index) => {
              quote.ruleParam = JSON.parse(quote.ruleParam);
              ret.push({
                ...item,
                quoteData: quote,
                rowspan: index == 0 ? length : null
              })
            })
          }
        })
        return ret
      },
      ckhInfo() {
        return this.quoteInfo.filter(item => item.productNo === 'CKH')
      }
    },
    mounted() {
      this.getEmployerCooperate();
      this.getQuoteInfo();
    },
    methods: {
      async getEmployerCooperate() {
        const { data } = await getEmployerCooperate();
        this.cooperationInfo = data;
      },
      calculateDisAble(name) {
        return name.length <= 20
      },
      async getQuoteInfo() {
        const { data } = await getQuoteInfo()
        this.quoteInfo = data;
      },
      spanMethod({ row, column, rowIndex, columnIndex }) {
        let ret = {};
        if (columnIndex == 0 || column.property == 'operate') {
          // 合并第一列
          if (row.rowspan !== null) {
            ret = {
              rowspan: row.rowspan,
              colspan: 1,
            };
          } else {
            ret = {
              rowspan: 0,
              colspan: 0,
            }
          }
          return ret;
        }
      },
    }

  }
</script>

<style lang="scss" scoped>
  .el-card {
    .invoice-category-list .el-tag{
      max-width: 90%;
      margin-right: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

</style>
