<template>
  <div>
    <div class="box-container">
      <div class="search-container">
        <div class="flex-container">
          <div class="flex-wrapper search-wrapper">
            <div class="flex-item">
              <span class="flex-item__label">服务商名称：</span>
              <el-input
                v-model="searchForm.mainstayName"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">账单状态：</span>
              <el-select
                v-model="searchForm.status"
                filterable
                clearable>
                <el-option
                  v-for="(item, index) in $dict('FeeOrderStatus')"
                  :key="index"
                  :label="item.desc"
                  :value="item.code"
                ></el-option>
              </el-select>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">付款模式：</span>
              <el-select
                v-model="searchForm.balancedMode"
                filterable
                clearable
              >
                <el-option
                  v-for="(item, index) in $dict('BalancedEnum')"
                  :key="index"
                  :label="item.desc"
                  :value="item.code"
                ></el-option>
              </el-select>
            </div>
          </div>
          <div class="search-wrapper flex-wrapper">
            <div class="flex-item-main">
              <span class="flex-item__label">账单日期：</span>
              <date-picker
                ref="datepicker"
                type="date"
                picker="separate"
                :start-time.sync="searchForm.startDate"
                :end-time.sync="searchForm.endDate"
              ></date-picker>
            </div>
          </div>
          <div class="flex-wrapper search-wrapper">
            <div class="search-btn-group">
              <el-button
                type="primary"
                size="small"
                @click="handleQuery"
              >查询
              </el-button
              >
              <el-button
                type="primary"
                size="small"
                @click="clearField"
              >重置
              </el-button>
              <el-button
                type="text"
                @click="getExportList"
              >查看已导出列表
              </el-button>
            </div>
          </div>
        </div>
      </div>
      <el-tabs
        v-model="searchForm.feeSource"
        @tab-click="handleQuery">
        <el-tab-pane
          v-for="item in $dict('FeeSourceEnum')"
          :key="item.code"
          :label="item.desc"
          :name="item.code"></el-tab-pane>
      </el-tabs>

      <div class="content-container">
        <div>
          共生成{{ statistics.total }}条账单，已付款
          {{ statistics.successCount }} 条，未付款
          {{ statistics.unPayCount }} 条
        </div>
        <div class="statistics-container">
          <el-card class="box-card">
            <div>已发放金额合计</div>
            <div class="statistics-money">
              {{ "￥" + $format.toCurrency(statistics.orderNetAmount) }}
            </div>
          </el-card>
          <el-card class="box-card">
            <div>服务费金额合计</div>
            <div class="statistics-money">
              {{ "￥" + $format.toCurrency(statistics.feeAmount) }}
            </div>
          </el-card>
          <el-card class="box-card">
            <div>个税代缴金额合计</div>
            <div class="statistics-money">
              {{ "￥" + $format.toCurrency(statistics.taxAmount) }}
            </div>
          </el-card>
          <el-card class="box-card">
            <div>未付款金额合计</div>
            <div class="statistics-money">
              {{ "￥" + $format.toCurrency(statistics.unPayAmount) }}
            </div>
          </el-card>
        </div>
        <el-table :data="dataList">
          <el-table-column
            label="账单日期"
            prop="createDate"
            width="100"
          ></el-table-column>
          <el-table-column
            label="服务商"
            prop="mainstayName"
            width="150"
          ></el-table-column>
          <el-table-column
            label="发放批次数"
            prop="orderBatchCount"
            width="100"
          ></el-table-column>
          <el-table-column
            label="订单明细数"
            prop="orderItemCount"
            width="100"
          ></el-table-column>
          <el-table-column
            label="发放成功订单数"
            prop="successCount"
            width="120"
          ></el-table-column>
          <el-table-column
            label="发放失败订单数"
            prop="failCount"
            width="120"
          ></el-table-column>
          <el-table-column
            label="已发放金额合计"
            prop="orderNetAmount"
            width="120"
          ></el-table-column>
          <el-table-column
            label="个税合计"
            prop="taxAmount"
            width="100"
          ></el-table-column>
          <el-table-column
            label="服务费合计"
            prop="feeAmount"
            width="100"
          ></el-table-column>
          <el-table-column
            label="付款模式"
            prop="balancedMode"
          >
            <template v-slot="{ row }">
              {{ $dictCode("BalancedEnum", row.balancedMode).desc || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="账单状态"
            prop="status"
          >
            <template v-slot="{ row }">
              {{ $dictCode("FeeOrderStatus", row.status).desc || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="receiptUrl"
            width="150"
          >
            <template slot="header">
              <div class="table-header">
                付款凭证
                <el-tooltip
                  content="线上结算账单在付款完成15-20分钟后生成回单"
                  placement="top"
                >
                  <i class="el-icon-info table-icon"></i>
                </el-tooltip>
              </div>
            </template>
            <template v-slot="{ row }">
              <el-button
                v-if="row.status == 400 && !(row.balancedMode == 100 && row.receiptUrl == null)"
                type="text"
                @click="download(row)"
              >下载回单
              </el-button>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            label="付款完成时间"
            prop="completeTime"
            width="180"
          ></el-table-column>
          <el-table-column
            label="操作"
            width="200"
          >
            <template v-slot="{ row }">
              <el-button
                v-if="(row.balancedMode == 101 && row.status == 100)"
                type="text"
                @click="payShow(row)"
              >发起付款
              </el-button>
              <el-button
                v-if="row.balancedMode == 100 && row.status == 300"
                type="text"
                @click="rePayShow(row)"
              >重新付款
              </el-button>
              <el-button
                type="text"
                @click="exportOrderItem(row)"
              >导出明细
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-footer class="pagination-container">
          <el-pagination
            v-if="dataList.length > 0"
            :total="totalRecord"
            :current-page="pageCurrent"
            :page-size="pageSize"
            :page-sizes="[10, 50]"
            layout="total, sizes, prev, pager, next, slot, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
            <!-- <span @click="forceSearch" class="force-next-btn">下一页</span> -->
          </el-pagination>
        </el-footer>
      </div>
    </div>
    <el-dialog
      title="付款确认"
      :close-on-click-modal="false"
      :visible.sync="confirmShow"
      width="70%"
      @close="confirmColse"
    >
      <div
        v-if="isLoad"
        class="comfirm-box"
      >
        <div class="comfirm-item">
          <div>
            服务费金额：
            {{ "￥" + $format.toCurrency(feeItem.serviceItem.payAmount) }}
          </div>
          <div v-if="feeItem.serviceItem.status == 400">
            <svg-icon
              icon-class="payed"
              class-name="icon"
            ></svg-icon>
          </div>
          <div v-else>
            <div class="comfirm-text">
              <el-button
                type="primary"
                size="small"
                :loading="servicePaying"
                @click="repayService(feeItem.serviceItem.feeItemNo)"
              >确认支付
              </el-button
              >
            </div>
            <div class="comfirm-text">
              <el-alert
                :title="feeItem.serviceItem.failReason"
                type="error"
                :closable="false"
              >
              </el-alert>
            </div>
          </div>
        </div>
        <div class="line">
          <el-divider direction="vertical"></el-divider>
        </div>
        <div class="comfirm-item">
          <div>
            个税金额：{{ "￥" + $format.toCurrency(feeItem.taxItem.payAmount) }}
          </div>
          <div v-if="feeItem.taxItem.status == 400">
            <svg-icon
              icon-class="payed"
              class-name="icon"
            ></svg-icon>
          </div>
          <div v-else>
            <div class="comfirm-text">
              <el-button
                type="primary"
                size="small"
                :loading="taxPaying"
                @click="repayTax(feeItem.taxItem.feeItemNo)"
              >确认支付
              </el-button>
            </div>
            <div class="comfirm-text">
              <el-alert
                :title="feeItem.taxItem.failReason"
                type="error"
                :closable="false"
              ></el-alert>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      class="off-line-dialog"
      title="线下付款确认单"
      :close-on-click-modal="false"
      :visible.sync="offLineShow"
      width="70%"
      @close="offColse"
    >
      <div v-if="offLoad">
        <el-alert
          title="请自行打款至服务商对公账户并上传转账凭证，服务费与个税请分开两次转账"
          type="success"
          :closable="false"
        >
        </el-alert>
        <div class="offline-box">
          <div class="offline-form">
            <div class="offline-form-item">
              <div class="offline-form-label">收款账户：</div>
              <div class="offline-form-value">
                {{ offLineItem.bankAccount.accountName }}
              </div>
            </div>
            <div class="offline-form-item">
              <div class="offline-form-label">收款账号：</div>
              <div class="offline-form-value">
                {{ offLineItem.bankAccount.accountNo }}
              </div>
            </div>
            <div class="offline-form-item">
              <div class="offline-form-label">开户行：</div>
              <div class="offline-form-value">
                {{ offLineItem.bankAccount.bankName }}
              </div>
            </div>
            <div class="offline-form-item" v-if="offLineItem.bankAccount.bankChannelNo">
              <div class="offline-form-label">银联号：</div>
              <div class="offline-form-value">
                {{ offLineItem.bankAccount.bankChannelNo }}
              </div>
            </div>
          </div>
        </div>
        <div>
          <div class="offline-form">
            <div class="offline-form-item">
              <div class="rule-form-label">账单日期：</div>
              <div class="rule-form-value">{{ offLineItem.createData }}</div>
            </div>
            <el-form
              :model="ruleForm"
              :rules="rules"
              ref="ruleForm"
              label-width="100px"
              class="demo-ruleForm"
              label-position="top">
              <div class="form-box">
                <div class="form-context">
                  <div class="offline-form-item">
                    <div class="rule-form-label">服务费金额：</div>
                    <div class="rule-form-value form-price">
                      {{ offLineItem.serviceItem.payAmount }}
                    </div>
                  </div>
                  <el-form-item
                    label="服务费付款凭证上传："
                    :label-width="formLabelWidth"
                    prop="serviceUrls">
                    <file-upload
                      :max="3"
                      :urls.sync="ruleForm.serviceUrls"
                      accept=".png,.jpg,.jpeg"
                      @change="onServiceChange">
                    </file-upload>
                  </el-form-item>
                </div>
                <div
                  v-if="offLineItem.taxItem.status != 400"
                  class="form-context">
                  <div class="offline-form-item">
                    <div class="rule-form-label">个税金额：</div>
                    <div class="rule-form-value form-price">
                      {{ offLineItem.taxItem.payAmount }}
                    </div>
                  </div>
                  <el-form-item
                    label="个税付款凭证上传："
                    :label-width="formLabelWidth"
                    prop="taxUrls"
                  >
                    <file-upload
                      :max="3"
                      :urls.sync="ruleForm.taxUrls"
                      accept=".png,.jpg,.jpeg"
                      @change="onTaxChange"
                    >
                    </file-upload>
                  </el-form-item>
                </div>
              </div>
            </el-form>
          </div>
        </div>
      </div>
      <div
        slot="footer"
        class="dialog-footer">
        <el-button @click="offColse">取 消</el-button>
        <el-button
          :loading="sumLoadding"
          type="primary"
          @click="submit">确 定
        </el-button>
      </div>
    </el-dialog>

    <export-record ref="exportRecord"></export-record>
  </div>
</template>

<script>
import {
  complete,
  exportOrderItem,
  getByItemNo,
  getOffLineItem,
  getStatistics,
  listPage,
  payFee,
  selectOrderItem,
} from "@/api/bill";
import FileUpload from "@/components/FileUpload";
import { getToken } from "@/utils/loginToken";
import ExportRecord from "@/components/ExportRecord";

export default {
  components: {
    FileUpload,
    ExportRecord,
  },
  data() {
    return {
      searchForm: {
        feeSource: '100'
      },
      totalRecord: 0,
      pageSize: 10,
      pageCurrent: 1,
      dataList: [],
      statistics: {
        total: 0,
        successCount: 0,
        unPayCount: 0,
        feeAmount: 0,
        taxAmount: 0,
        orderNetAmount: 0,
        unPayAmount: 0,
      },
      formLabelWidth: "100px",

      confirmShow: false,
      feeItem: {},
      isLoad: false,
      offLoad: false,
      servicePaying: false,
      taxPaying: false,
      offLineShow: false,
      ruleForm: {},
      rules: {
        serviceUrls: [
          { required: true, message: "请上传服务费凭证", trigger: "change" },
        ],
        taxUrls: [
          { required: true, message: "请上传个税凭证", trigger: "change" },
        ],
      },
      serviceUrls: [],
      taxUrls: [],
      offLineItem: {},
      sumLoadding: false,
    };
  },
  created() {
    console.log(this.feeItem);
    this.getList();
    this.getData();
  },
  methods: {
    onServiceChange(files) {
      this.ruleForm.serviceUrls = files.map((e) => {
        return e.replace(this.fileUrl, "");
      });
    },
    onTaxChange(files) {
      this.ruleForm.taxUrls = files.map((e) => {
        return e.replace(this.fileUrl, "");
      });
    },
    checkStatus() {
      if (
        this.feeItem.taxItem.status == 400 &&
        this.feeItem.serviceItem.status == 400
      ) {
        this.confirmColse();
        this.handleQuery();
        this.confirmShow = false;
      }
    },
    async repayService(feeItemNo) {
      this.servicePaying = true;
      const res = await payFee(feeItemNo);
      if (res.data.bankPayStatus == 100) {
        this.$message.success("支付成功");
      } else {
        this.$message.error(`支付失败：${ res.data.bizMsg }`);
      }
      this.servicePaying = false;
      const { data } = await getByItemNo(feeItemNo);
      this.feeItem.serviceItem = data;
      this.checkStatus();
    },
    async repayTax(feeItemNo) {
      this.taxPaying = true;
      const res = await payFee(feeItemNo);
      if (res.data.bankPayStatus == 100) {
        this.$message.success("支付成功");
      } else {
        this.$message.error(`支付失败：${ res.data.bizMsg }`);
      }
      this.taxPaying = false;
      const { data } = await getByItemNo(feeItemNo);
      this.feeItem.taxItem = data;
      console.log(this.feeItem);
      this.checkStatus();
    },
    confirmColse() {
      this.feeItem = {
        feeSource: '100'
      };
      this.isLoad = false;
      this.servicePaying = false;
      this.taxPaying = false;
    },
    offColse() {
      this.serviceUrls = [];
      this.taxUrls = [];
      this.ruleForm = {};
      this.offLineItem = {};
      this.offLoad = false;
      this.offLineShow = false;
    },
    submit() {
      console.log(this.ruleForm);
      this.$refs["ruleForm"].validate(async (valid) => {
        if (valid) {
          this.sumLoadding = true;
          this.offLineItem.serviceItem.cetificateUrl = JSON.stringify(
            this.ruleForm.serviceUrls
          );
          this.offLineItem.taxItem.cetificateUrl = JSON.stringify(
            this.ruleForm.taxUrls
          );
          const { data } = await complete({
            serviceItem: this.offLineItem.serviceItem,
            taxItem: this.offLineItem.taxItem,
          });
          this.$message.success(data);
          this.sumLoadding = false;
          this.offColse();
          this.handleQuery();
        }
      });
    },
    async payShow(row) {
      this.offLineShow = true;
      const { data } = await getOffLineItem(row.feeBatchNo);
      this.offLineItem = data;
      this.offLoad = true;
    },
    async rePayShow(row) {
      this.confirmShow = true;
      const { data } = await selectOrderItem(row.feeBatchNo);
      this.feeItem = data;
      this.isLoad = true;
    },
    async getData() {
      const { data } = await getStatistics(this.searchForm);
      this.statistics = data;
    },
    forceSearch() {
      this.pageCurrent++;
      this.getList();
    },
    handleQuery() {
      this.pageCurrent = 1;
      this.getList();
      this.getData();
      // console.log(this.searchForm);
    },
    async getList() {
      const { data } = await listPage({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      });
      this.dataList = data.data;
      this.totalRecord = data.totalRecord;
    },
    clearField() {
      this.searchForm = {};
      this.$refs.datepicker.clearTime();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.handleQuery();
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.getList();
    },
    download(row) {
      window.open(
        this.baseUrl +
        `/download/feeOrder/certificateFile?feeBatchNo=${
          row.feeBatchNo
        }&x-token=${ getToken() }`
      );
    },
    async exportOrderItem(row) {
      const { data } = await exportOrderItem(row.feeBatchNo);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord("40");
    },
  },
};
</script>

<style scoped>
.statistics-container {
  display: flex;
}

.box-card {
  flex: 1;
  margin: 1rem;
  justify-content: space-between;
}

.statistics-money {
  font-size: 30px;
}

.comfirm-box {
  display: flex;
  justify-content: space-between;
}

.line ::v-deep .el-divider {
  background: #999;
}

.line ::v-deep .el-divider--vertical {
  width: 1.5px;
  height: 100%;
}

.comfirm-item {
  /* display: flex;
  justify-content: center;
  align-items: center; */
  padding: 30px;
  flex: 1;
}

.comfirm-text {
  margin-top: 2rem;
}

.icon {
  font-size: 100px;
  line-height: 0;
}

.off-line-dialog ::v-deep .el-dialog__header {
  border-bottom: 1px solid #ebebeb;
}

.off-line-dialog ::v-deep .el-dialog {
  margin-bottom: 15vh;
}

.offline-form-item {
  padding: 10px 0;
  display: flex;
  /* align-items: center; */
}

.offline-form {
  padding: 10px;
}

.offline-form-label {
  width: 100px;
  text-align: right;
}

.offline-form-value {
  margin-left: 1rem;
  width: 70%;
}

.offline-box {
  margin-top: 1rem;
  background: #f2f2f2;
}

/* .offline-batch-box{
  margin-top: 1rem;
} */
.form-box {
  display: flex;
}

.form-context {
  flex: 1;
}

.table-header {
  display: flex;
  align-items: center;
}

.table-icon {
  margin-left: 3px;
  cursor: pointer;
}

/* .form-price{} */
</style>
