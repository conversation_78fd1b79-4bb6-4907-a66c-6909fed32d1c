<template>
  <div class="box-container">
    <div v-if="step === 1">
      <div class="search-container clearfix flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="search-item fl">
            <el-select
              clearable
              v-model="searchForm.mainstayMchNo"
              @change="handleMainstayChange">
              <el-option
                v-for="(item, index) in mainstayList"
                :key="index"
                :label="item.mainstayName"
                :value="item.mainstayNo"></el-option>
            </el-select>
            <el-select
              clearable
              v-model="searchForm.applyType">
              <el-option
                v-for="(item, index) in $dict('ApplyTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"></el-option>
            </el-select>
            <div class="flex-item">
              <span class="flex-item__label">岗位类目：</span>
              <el-select
                v-model="searchForm.workCategoryCode"
                style="margin-right: 8px"
                @change="handleMainstayChange">
                <el-option
                  v-for="(item, index) in workCategoryOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </div>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="search-item fr">
            <date-picker
              v-model="timeRange"
              type="date"
              ref="datepicker"
              :is-show-fast-time="false"
              :timeDisable="-1"
              :clearable="true"
              picker="separate"
              :start-disable="true"
              :useOptions="true"
              :start-time.sync="searchForm.tradeCompleteDayBegin"
              :end-time.sync="searchForm.tradeCompleteDayEnd"
              @change="handleTimeChange"
            >
              <el-button
                slot="append"
                type="primary"
                @click="() => {search(true) && getWaitIssueAmount() }">查询
              </el-button>
            </date-picker>
          </div>
        </div>
      </div>

      <warning-block>
        <p class="warning-title">发票申请说明：</p>
        <p>1、有多个可申请发票的流水时，请您合并申请开票</p>
        <p>2、按发放金额开票，打款成功 1 个自然日后可开票</p>
        <p class="warning-title">专用发票申请说明：</p>
        <p>1、每个商户每月小于20万元的发票申请只能申请一次，请尽量合并申请开票</p>
        <p>2、大于等于20万元发票申请无限制，但不允许拆分成多张小于20万元的专用发票后申请开票</p>
      </warning-block>

      <div class="content-container">
        <div class="func-container">
          <el-checkbox
            v-model="checkSinglePage"
            disabled>单页全选
          </el-checkbox>
          <el-checkbox
            v-model="checkAllPage"
            @change="toggleSelection"
            disabled>批量全选
          </el-checkbox>
        </div>
        <el-table
          :data="response.data"
          @selection-change="handleSelectionChange"
          ref="tableData"
          row-key="id">
          <el-table-column
            type="selection"
            :reserve-selection="true"
            :selectable="disAbleSelect">
          </el-table-column>
          <el-table-column label="产品名称">智享汇</el-table-column>
          <el-table-column label="开票备注">委托代征</el-table-column>
          <el-table-column
            label="岗位信息"
            prop="workCategoryName"></el-table-column>
          <el-table-column
            label="平台批次号"
            prop="platBatchNo"></el-table-column>
          <el-table-column
            label="出款完成时间"
            prop="completeTime"></el-table-column>

          <!--          <el-table-column label="流水类型">-->
          <!--            <template v-slot="{row}">-->
          <!--              &lt;!&ndash; {{ row.applyType == 1 ? '发放流水' : '充值流水' }} &ndash;&gt;-->
          <!--              发放流水-->
          <!--            </template>-->
          <!--          </el-table-column>-->
          <el-table-column
            label="实发金额"
            width="150"
            prop="successNetAmount">
            <template v-slot="{row}">
              {{ row.successNetAmount | moneyFormat }}
            </template>
          </el-table-column>

          <el-table-column
            label="完成时间"
            width="150"
            prop="completeTime"></el-table-column>

          <el-table-column
            label="申请开票金额"
            width="150">
            <template v-slot="{row}">
              {{ (Number(row.successFee) + Number(row.successNetAmount)) | moneyFormat }}
            </template>
          </el-table-column>

          <el-table-column
            label="代征主体"
            prop="mainstayName">
            <template v-slot="{row}">
              {{ row.mainstayName }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>


    <section v-else-if="step === 2">
      <div class="section-title">确认邮寄地址</div>
      <div class="address-list">
        <div class="address-item active">
          <div class="address-name">
            {{ expressInfo.consignee }}
          </div>
          <div class="address-detail">
            <p>{{ expressInfo.province }} {{ expressInfo.city }} {{ expressInfo.county }} {{ expressInfo.address }}</p>
            <p>{{ expressInfo.telephone }}</p>
          </div>
        </div>
      </div>
      <div class="section-title">确认开票信息</div>
      <div class="form-container">
        <el-form
          label-width="200px"
          :rules="rules"
          ref="form"
          :model="submitForm">
          <el-form-item
            label="发票类型："
            prop="invoiceType">
            <el-radio-group v-model="submitForm.invoiceType">
              <el-radio
                v-for="(item, index) in $dict('InvoiceTypeEnum')"
                :key="index"
                :label="item.code">
                {{ item.desc }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="开票类目："
            prop="invoiceCategoryCode">
            <el-select v-model="submitForm.invoiceCategoryCode">
              <el-option
                v-for="(item, index) in invoiceInfo.invoiceCategoryVoList"
                :key="index"
                :label="item.invoiceCategoryName"
                :value="item.invoiceCategoryCode"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item
            label="开票金额："
            class="issue-amount">
            {{ submitForm.invoiceAmount | moneyFormat }}
          </el-form-item>
          <el-form-item label="企业名称：">
            {{ invoiceInfo.mchName }}
          </el-form-item>
          <el-form-item label="纳税人识别号：">
            {{ invoiceInfo.taxNo }}
          </el-form-item>
          <el-form-item label="单位注册地址及电话：">
            {{ invoiceInfo.registerAddrInfo }}
          </el-form-item>
          <el-form-item label="开户行及帐号：">
            {{ invoiceInfo.bankName }} {{ invoiceInfo.accountNo }}
          </el-form-item>
          <el-form-item label="开票说明：">
            <el-input
              type="textarea"
              rows="5"
              v-model="submitForm.remark"
              maxlength="200"
              show-word-limit></el-input>
          </el-form-item>
        </el-form>
      </div>
    </section>

    <section v-else>
      <div class="result-container">
        <img src="@/assets/success.png">
        <p class="result-title">操作成功</p>
        <p class="result-tip">已提交开票申请，平台会在 5 个工作日内受理并邮寄至贵公司</p>
        <div class="footer-container">
          <el-button
            type="primary"
            @click="goRecord">查看申请记录
          </el-button>
          <el-button
            type="text"
            @click="goApply">继续申请
          </el-button>
        </div>
      </div>
    </section>

    <el-footer class="pagination-container">
      <el-pagination
        v-if="step === 1"
        background
        :page-size="10"
        :total="response.totalRecord"
        layout="total, prev, pager, next"
        :current-page.sync="pageCurrent"
        @current-change="search() && getWaitIssueAmount()"
      ></el-pagination>

      <div
        class="form-btn-group clearfix"
        v-if="step <= 2">
        <p class="fl">
          申请开票总金额：
          <template v-if="preAmount > 0">
            <!-- 有预开票信息 -->
            <!-- 开票总金额 >= 当前可预开金额，显示 开票总金额 - 当前可预开金额-->
            <span v-if="invoiceAmount >= preAmount" class="issue-amount">{{ (invoiceAmount - preAmount) | moneyFormat }}</span>
            <!-- 否则均显示 申请开票金额 -->
            <span v-else class="issue-amount">{{ invoiceAmount | moneyFormat }}</span>
          </template>
          <template v-else>
            <!-- 无预开票信息 -->
            <span class="issue-amount">{{ invoiceAmount | moneyFormat }}</span>
          </template>
          <span class="issue-pre-amount-tips" v-if="preAmountTips">
            <el-tooltip effect="dark" placement="top">
              <div slot="content">
                当前已预开金额：
                {{ preAmount | moneyFormat}}
              </div>
              <i class="el-icon-warning"></i>
            </el-tooltip>
            {{ preAmountTips }}
          </span>
        </p>
        <el-button
          v-if="step === 1"
          :disabled="selection.length === 0 || !canApplyInvoice"
          :loading="loading"
          class="fr"
          type="primary"
          @click="nextStep">
          申请开票
        </el-button>
        <div
          class="fr"
          v-else-if="step === 2">
          <el-button
            type="primary"
            @click="nextStep"
            :loading="loading">提交申请
          </el-button>
          <el-button @click="cancel">取消</el-button>
        </div>
      </div>
    </el-footer>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { getEmployerPosition, getExpressInfo } from '@/api/merchant'
import {
  applyInvoice,
  getAllMainstayListByProduct,
  getBeginDate,
  getInvoiceInfoWithMainstayNo,
  getIssueOrder,
  getWaitIssueAmount,
  preCheckApplyInvoice
} from '@/api/invoice'

export default {
  data() {
    return {
      // step1
      product: { productName: '智享汇', productCode: 'ZXH' },
      timeRange: [],
      checkSinglePage: false,
      checkAllPage: true,
      response: {
        data: [],
        totalRecord: 0
      },
      pageCurrent: 1,
      pageSize: 10,
      mainstayList: [],
      mainstayMap: {},
      searchForm: {
        productNo: 'ZXH',
        amountType: 1, //1=订单总额 2=实发金额 3=服务费
        mainstayMchNo: '',
        applyType: '',
      },
      selection: [],


      step: 1,

      selectAddressIndex: 0,

      rules: {
        invoiceType: [
          { required: true, message: '请选择发票类型', trigger: 'blur' },
        ],
        invoiceCategoryCode: [
          { required: true, message: '请选择发票类目', trigger: 'blur' },
        ]
      },

      invoiceAmount: 0,

      // step2
      expressInfo: {},
      invoiceInfo: {},
      invoiceCategoryMap: {},

      submitForm: {},


      loading: false,

      workCategoryOptions: [],
      workCategoryMap: {},

      preAmount: 0,
      preAmountTips: '',
      canApplyInvoice: true,
    }
  },
  async mounted() {
    this.searchForm.applyType = this.$dict('ApplyTypeEnum')[0].code;
    this.init();
  },
  methods: {
    async init() {
      await Promise.all([
        this.getPositionList(),
        this.getMainstayList(),
      ])
      await this.getBeginDate();
      Promise.all([
        this.search(true),
        this.getWaitIssueAmount(),
      ])
    },
    async getPositionList() {
      const { data } = await getEmployerPosition();
      this.workCategoryOptions = data.map((e) => {
        return {
          label: e.workCategoryName,
          value: e.workCategoryCode,
        };
      });
      data.forEach(item => {
        this.workCategoryMap[item.workCategoryCode] = item.workCategoryName;
      })
      let item = this.workCategoryOptions[0]
      if (item) {
        this.searchForm.workCategoryCode = item['value']
      }
    },
    async getMainstayList() {
      let param = {
        productNo: 'ZXH'
      }
      const { data } = await getAllMainstayListByProduct(param);
      data.forEach(item => {
        this.mainstayMap[item.mainstayNo] = item.mainstayName
      })
      this.mainstayList = data;
      let item = this.mainstayList[0]
      if (item) {
        this.searchForm.mainstayMchNo = item['mainstayNo']
      }
    },
    async handleMainstayChange(val) {
      if (val) {
        await this.getBeginDate();
        // this.search(true);
        // this.getWaitIssueAmount();
      }
    },
    async getBeginDate() {
      let { data } = await getBeginDate({
        mainstayMchNo: this.searchForm.mainstayMchNo,
        workCategoryCode: this.searchForm.workCategoryCode,
        productNo: this.searchForm.productNo,
        amountType: this.searchForm.amountType
      })
      let now = dayjs().endOf('day').subtract(1, 'day').format('YYYY-MM-DD');
      data = dayjs(data).endOf('day').format('YYYY-MM-DD');
      this.$refs.datepicker.changeTime([data, now]);
    },
    async search(initFlag) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      const { data } = await getIssueOrder({
        ...this.searchForm,
        pageCurrent: this.pageCurrent,
        pageSize: this.pageSize
      })
      this.response = data;
      this.selection = [];
      this.toggleSelection(this.checkAllPage)
    },
    async getWaitIssueAmount() {
      const { data } = await getWaitIssueAmount({
        ...this.searchForm,
        pageCurrent: this.pageCurrent,
        pageSize: this.pageSize
      });
      this.invoiceAmount = data || 0;
      this.preCheckApplyInvoice()
    },
    async preCheckApplyInvoice () {
      const result = await preCheckApplyInvoice({
        mainstayMchNo: this.searchForm.mainstayMchNo,
        workCategoryCode: this.searchForm.workCategoryCode,
        productNo: this.searchForm.productNo,
      })
      const invoicePreAmount = result.data.invoicePreAmount;
      this.canApplyInvoice = true
      if (invoicePreAmount && invoicePreAmount > 0) {
        this.preAmount = Math.round(invoicePreAmount * 100) / 100;
        // 若申请开票金额＜当前预开票金额，则显示提示“已预开发票，交易流水未补足，暂无法申请开票”；
        if (this.invoiceAmount < invoicePreAmount) {
          this.preAmountTips = '已预开发票，交易流水未补足，暂无法申请开票'
          this.canApplyInvoice = false
        } else {
          // 若申请开票金额≥当前预开票金额，则显示提示“已预开发票，本次实际开票金额将扣除当前已预开金额”
          this.preAmountTips = '已预开发票，本次实际开票金额将扣除当前已预开金额'
        }
      } else {
        this.preAmountTips = ''
        this.preAmount = 0
      }
    },
    handleSelectionChange(val) {
      this.selection = val;
    },
    disAbleSelect(row) {
      return !this.checkAllPage;
    },
    toggleSelection(val) {
      this.response.data.forEach((row) => {
        this.$refs.tableData.toggleRowSelection(row, val);
      })
    },
    handleTimeChange(val) {
      if (this.searchForm.tradeCompleteDayBegin && this.searchForm.tradeCompleteDayEnd) {
        this.search(true)
        this.getWaitIssueAmount()
      }
    },
    async nextStep() {
      this.loading = true;
      try {
        if (this.step === 1) {
          const [{ data }, { data: invoiceInfo }] = await Promise.all([
            getExpressInfo(),
            getInvoiceInfoWithMainstayNo({
              mainstayNo: this.searchForm.mainstayMchNo,
              workCategoryCode: this.searchForm.workCategoryCode
            })
          ]);
          if (data) {
            this.expressInfo = data;
          }
          this.$set(this, 'submitForm',
            {
              ...this.searchForm,
              mainstayMchName: this.mainstayMap[this.searchForm.mainstayMchNo],
              invoiceCategoryCode: invoiceInfo.invoiceCategoryVoList[0]?.invoiceCategoryCode,
              invoiceCategoryName: invoiceInfo.invoiceCategoryVoList[0]?.invoiceCategoryName,
              invoiceAmount: Math.round((this.invoiceAmount - this.preAmount) * 100) / 100,
              invoiceType: '2',
              remark: '',
            }
          )
          this.invoiceInfo = invoiceInfo;
          this.invoiceInfo.invoiceCategoryVoList.forEach(item => {
            this.invoiceCategoryMap[item.invoiceCategoryCode] = item.invoiceCategoryName;
          })
          this.step++;
        } else if (this.step === 2) {
          this.submitForm.invoiceCategoryName = this.invoiceCategoryMap[this.submitForm.invoiceCategoryCode];
          this.submitForm.workCategoryName = this.workCategoryMap[this.submitForm.workCategoryCode];
          const response = await this.applyInvoice();
          if (response) {
            this.step++;
          }
        }
      } finally {
        this.loading = false
      }
    },
    async applyInvoice() {
      const valid = await this.$refs.form.validate().catch(_ => false);
      if (valid) {
        this.submitForm.productNo = this.product.productCode;
        this.submitForm.productName = this.product.productName;
        const { data } = await applyInvoice(this.submitForm);
        this.$message.success(data);
        return data;
      } else {
        return false
      }
    },
    cancel() {
      this.step = 1;
      this.init();
    },
    goRecord() {
      this.$router.push('/finance/invoice/issueRecord');
    },
    async goApply() {
      this.step = 1;
      await this.$nextTick();
      this.init();
    },
  }
}
</script>

<style
  lang="scss"
  scoped>
.box-container {
  .search-container {
    margin-bottom: 16px;
  }

  .search-item {
    margin: 0 10px;
  }

  .form-btn-group {
    position: fixed;
    z-index: 100;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 8px 100px 8px 300px;
    background: #fff;
    text-align: center;
    box-shadow: 0 -3px 3px 1px #ddd;
  }

  .section-title {
    margin-top: 16px;
    margin-bottom: 16px;
    font-size: 20px;
  }

  .issue-amount {
    color: #f00;
  }

  .issue-pre-amount-tips {
    margin-left: 16px;
    i {
      color: #E6A23C;
    }
  }

  .address-list {
    margin-bottom: 32px;
  }

  .address-item {
    position: relative;
    display: inline-block;
    width: 250px;
    height: 200px;
    margin-right: 16px;
    padding: 20px;
    border: 2px solid $borderColor;
    overflow: hidden;
    cursor: pointer;

    &.active {
      border-color: $mainColor;

      &::after {
        content: '';
        display: block;
        position: absolute;
        right: 0;
        bottom: 0;
        width: 40px;
        height: 40px;
        background: $mainColor;
        transform: translate(50%, 50%) rotate(45deg)
      }

    }

    .address-name {
      padding-top: 12px;
      padding-bottom: 12px;
      @include bottom-border;
    }

    .address-detail {
      padding-top: 16px;
    }
  }

  .form-container {
    width: 800px;
  }

  .result-container {
    padding: 32px;
    text-align: center;
  }
}
</style>
