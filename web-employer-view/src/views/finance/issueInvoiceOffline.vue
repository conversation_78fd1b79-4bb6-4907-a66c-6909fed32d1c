<template>
  <div class="box-container">
    <el-steps
      :active="activeStep"
      finish-status="success">
      <el-step title="填写开票信息"></el-step>
      <el-step title="确认开票信息">确认开票信息</el-step>
      <el-step title="支付服务费">支付服务费</el-step>
      <el-step title="完成">完成</el-step>
    </el-steps>

    <el-form label-width="180px">
      <div v-if="activeStep==0">
        <div style="margin-top: 20px">
          <div>
            <p
              class="subTitle"
            >任务</p>
          </div>
          <div style="margin: 20px 40px 0;">
            <el-row>
              <el-col :span="8">
                <div>
                  <span
                    class="flex-item__label"
                    style="width: fit-content">请选择关联任务 *：</span>
                  <br>
                  <el-select
                    v-model="form_1.jobId"
                    @change="handleJobChange">
                    <el-option
                      v-for="item in jobList"
                      :key="item.jobId"
                      :label="item.jobName"
                      :value="item.jobId"></el-option>
                  </el-select>
                </div>
              </el-col>
              <el-col :span="8">
                <div class="grid-content bg-purple-light">
                  <span
                    class="flex-item__label"
                    style="width: fit-content">选择代征主体*：</span>
                  <br>
                  <el-select
                    v-model="form_1.mainstayNo"
                    @change="handleMainstayChange">
                    <el-option
                      v-for="item in mainstayList"
                      :key="item.mainstayNo"
                      :label="item.mainstayName"
                      :value="item.mainstayNo"></el-option>
                  </el-select>
                </div>
              </el-col>
              <el-col :span="8">
                <div>
                  <span
                    class="flex-item__label"
                    style="width: fit-content">开票类目*：</span>
                  <br>
                  <el-select
                    v-model="form_1.invoiceCategoryCode"
                    :loading="catLoading"
                    v-on:visible-change="listInvoiceCategory"
                    ref="invoiceCatRef">
                    <el-option
                      v-for="item in invoiceInfo.invoiceCategoryVoList"
                      :key="item.invoiceCategoryCode"
                      :label="item.invoiceCategoryName"
                      :value="item.invoiceCategoryCode"></el-option>
                  </el-select>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>

        <div style="margin-top: 20px">
          <div>
            <p
              class="subTitle"
            >开票人员名单</p></div>
          <div style="margin-top: 20px">
            <el-table
              :data="workerRecords"
              :height="tableHeight">
              <el-table-column
                label="姓名"
                prop="workerName"></el-table-column>
              <el-table-column
                label="手机号"
                prop="workerPhone"></el-table-column>
              <el-table-column
                label="身份证"
                prop="workerIdcard"
                width="180">
                <template v-slot="{ row }">
                  <div>{{ row.workerIdcard || "-" }}</div>
                  <el-button
                    v-if="row.workerIdcard && row.authStatus == 100"
                    type="text"
                    @click="workerInfo(row)"
                  >查看身份证
                  </el-button>
                </template>
              </el-table-column>
              <el-table-column
                label="认证状态"
                prop="authStatus">
                <template v-slot="{ row }">
                  {{ $dictCode("WorkerAuthStatus", row.authStatus).desc || "-" }}
                </template>
              </el-table-column>
              <el-table-column
                label="签约状态"
                prop="signStatus">
                <template v-slot="{ row }">
                  <span v-if="row.signStatus">已签约</span>
                  <span v-else>未签约</span>
                </template>
              </el-table-column>
              <el-table-column
                label="任务状态"
                prop="jobStatus">
                <template v-slot="{ row, column }">
                  {{ $dictCode("JobWorkerStatusEnum", row[column.property]).desc }}
                </template>
              </el-table-column>
              <el-table-column
                label="开票金额"
                prop="invoiceAmount">
                <template v-slot="{ row, column }">
                  <el-input
                    v-if="row.authStatus == '100' && row.signStatus"
                    :value="row.invoiceAmount"
                    @input="handleRateInput(row, 'invoiceAmount', $event, 0)"></el-input>
                  <span v-else>
                    未认证/未签约 <br>无法开票
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template v-slot="{row}">
                  <el-button
                    type="text"
                    v-on:click="deleteItem(row)">删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
        <div>
          <p
            class="subTitle"
          >支付回单</p>
          <div>
            <el-row>
              <el-col :span="10">
                <div>
                  <el-upload
                    class="upload-demo"
                    drag
                    :limit="1"
                    :action="baseUrl + '/file/upload'"
                    :headers="uploadHeader"
                    accept=".zip"
                    :file-list="workerBillFileList"
                    :before-upload="validateUploadFile({ addition: ['.zip']})"
                    :on-remove="(file, fileList) => handleRemove(file, fileList, 'workerBillFilePath', 'workerBillFileList')"
                    :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'workerBillFilePath', 'workerBillFileList')"
                    multiple>
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                    <div
                      class="el-upload__tip"
                      slot="tip">支持扩展名：.zip
                    </div>
                  </el-upload>
                </div>
              </el-col>
              <el-col :span="14">
                <div
                  style="text-align: left">
              <span>
                <p style="font-weight: bolder">
                  <i
                    style="color: #0080ff"
                    class="el-icon-info"></i>支付回单上传规则：</p>
                <p>1、请将付款回单按“姓名_身份证”或者“姓名_身份证_*”格式命名,</p>
                <p> 如:【张三_440182199001011234.jpg, 张三_440182199001011234_xxx.jpg】</p>
                <p>2、将所有重命名后的付款回单添加至压缩包</p>
                <p>3、上传压缩包到此页面</p>
              </span>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      <div v-if="activeStep==1">
        <div class="section-title subTitle">确认邮寄地址</div>
        <div class="address-list">
          <div class="address-item active">
            <div class="address-name">
              {{ expressInfo.consignee }}
            </div>
            <div class="address-detail">
              <p>{{ expressInfo.province }} {{ expressInfo.city }} {{ expressInfo.county }} {{ expressInfo.address }}</p>
              <p>{{ expressInfo.telephone }}</p>
            </div>
          </div>
        </div>
        <div class="section-title subTitle">确认开票信息</div>
        <div class="form-container">
          <el-form-item
            label="发票类型："
            prop="invoiceType">
            <el-radio-group
              v-model="invoiceType"
              disabled>
              <el-radio :label=1>增值税普通发票</el-radio>
              <el-radio :label=2>增值税专用发票</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="开票类目：">
            <span>{{ invoiceCategoryMap[form_1.invoiceCategoryCode] }}</span>
          </el-form-item>

          <el-form-item
            label="开票金额："
            class="issue-amount">
            <span style="color: #f00">
              {{ form_1.invoiceAmount| moneyFormat }}
            </span>
          </el-form-item>
          <el-form-item label="企业名称：">
            {{ invoiceInfo.mchName }}
          </el-form-item>
          <el-form-item label="纳税人识别号：">
            {{ invoiceInfo.taxNo }}
          </el-form-item>
          <el-form-item label="单位注册地址及电话：">
            {{ invoiceInfo.registerAddrInfo }}
          </el-form-item>
          <el-form-item label="开户行及帐号：">
            {{ invoiceInfo.bankName }} {{ invoiceInfo.accountNo }}
          </el-form-item>
          <el-form-item label="开票说明：">
            <el-input
              type="textarea"
              rows="5"
              v-model="form_1.remark"
              maxlength="200"
              show-word-limit></el-input>
          </el-form-item>
        </div>
      </div>
      <div v-if="activeStep==2">
        <div style="width: 45%; margin: 0 auto">
          <el-form-item label="商户名：">
            {{ invoiceInfo.mchName }}
          </el-form-item>
          <el-form-item label="代征主体：">
            {{ mainstayMap[form_1.mainstayNo] }}
          </el-form-item>
          <div class="bold-money-container">
            <el-form-item
              label="开票金额：">
            <span class="bold-money">
              {{ form_1.invoiceAmount| moneyFormat }}
            </span>
            </el-form-item>
            <el-form-item
              label="需支付服务费：">
            <span class="bold-money">
              {{ form_1.serviceFeeAmount | moneyFormat }}
            </span>
            </el-form-item>
          </div>
          <el-form-item label="需支付个税：">
            0
          </el-form-item>
          <el-form-item label="支付方式：">
            <el-radio-group v-model="payMethod">
              <el-radio :label="0">线下转账</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 汇款信息-->
          <div class="invoice-remit-info">
            <p class="invoice-remit-title">
              <i
                class="el-icon-info"
                style="color: #0080ff">
              </i>您需自行汇款至以下账户，汇款成功后上传凭证信息</p>
            <el-form-item
              label-width="80px"
              label="户名：">{{ mainstayAccount.accountName }}
            </el-form-item>
            <el-form-item
              label-width="80px"
              label="账号：">{{ mainstayAccount.accountNo }}
            </el-form-item>
            <el-form-item
              label-width="80px"
              label="银行：">{{ mainstayAccount.bankName }}
            </el-form-item>
            <!--<el-form-item label-width="80px" label="支行：">123</el-form-item>-->
            <!--<el-form-item label-width="80px" label="开户地：">123</el-form-item>-->
            <el-form-item
              label-width="80px"
              label="联行号：">{{ mainstayAccount.bankChannelNo }}
            </el-form-item>
          </div>

          <el-form-item label="*支付凭证：">
            <el-upload
              class="upload-demo"
              :action="baseUrl + '/file/upload'"
              :headers="uploadHeader"
              accept=".doc,.docx,.pdf,.jpg,.png"
              :file-list="serviceFeeBillFileList"
              :before-upload="validateUploadFile({img: true, pdf: true, word: true})"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 'serviceFeeBillPath', 'serviceFeeBillFileList')"
              :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'serviceFeeBillPath', 'serviceFeeBillFileList')"
              multiple
              :limit="1">
              <el-button
                size="small"
                type="primary">点击上传
              </el-button>
              <div
                slot="tip"
                class="el-upload__tip">支持扩展名：.doc .docx .pdf .jpg .png
              </div>
            </el-upload>
          </el-form-item>
        </div>
      </div>

      <div v-if="activeStep == 3">
        <div class="text-center invoice-result-container">
          <img
            class="issue-result-img"
            src="@/assets/success.png"
            alt="success"
          />
          <h1 class="issue-result-title">提交成功</h1>
          <p class="issue-result-desc">
            您的开票信息已提交，预计将在5个工作日内开出（月末会有延迟）
          </p>
          <p class="issue-result-desc">
            您可以在“开票记录”中查看开票进度
          </p>
          <router-link to="/finance/invioce/issueInvoiceCkh">
            <el-button>返回</el-button>
          </router-link>
        </div>
      </div>
    </el-form>


    <el-footer
      class="form-btn-group"
      v-if="activeStep!=3">
      <el-button @click="backStep">{{ activeStep == 0 ? '取 消' : '上一步' }}</el-button>
      <el-button
        type="primary"
        plain
        v-if="activeStep == 2"
        @click="payLater">稍后支付
      </el-button>
      <el-button
        type="primary"
        @click="nextStep"
        :disabled="loading">{{ activeStep == 2 ? '提 交' : '下一步' }}
      </el-button>
    </el-footer>
  </div>
</template>

<script>
import '@/styles/merchant-form.scss'
import { getExpressInfo, getMainstayAccount } from '@/api/merchant'
import { getJobworker, taskListOnGrant, workerInfo } from '@/api/jobworker.js'
import {
  applyOffline,
  calculateServiceFee,
  getInvoiceInfoWithMainstayNo,
  getMainstayListByProduct,
  offlineInvoiceApplyProcessing
} from '@/api/invoice.js'

export default {
  name: "issueInvoiceOffline",
  data() {
    return {
      activeStep: 0,
      loading: false,
      catLoading: false,
      workerBillFilePath: null,
      form_1: {
        workerBillFilePath: null,
        invoiceAmount: 0,
        serviceFeeAmount: 0,
        invoiceCategoryCode: null,
      },
      workerBillFileList: [],
      serviceFeeBillFileList: [],
      invoiceType: 1,
      jobList: [],
      mainstayList: [],
      mainstayMap: {},
      invoiceInfo: {
        invoiceCategoryVoList: []
      },
      workerRecords: [],
      expressInfo: {},
      invoiceCategoryMap: {},
      mainstayAccount: {},
      payMethod: 0,
      applyCode: null
    };
  },
  computed: {
    tableHeight() {
      return this.workerRecords.length < 5 ? '' : 450
    }
  },
  async mounted() {
    this.init()
  },
  methods: {
    async init() {
      await Promise.all([
        this.getJobList(),
        this.getMainstayList(),
      ])
      let { data } = await offlineInvoiceApplyProcessing()
      let proccessingData = data
      if (!proccessingData) {
        this.activeStep = 0
      } else {
        this.form_1.jobId = proccessingData.jobId
        this.form_1.employerName = proccessingData.employerName
        this.form_1.employerNo = proccessingData.employerNo
        this.form_1.invoiceCategoryCode = proccessingData.invoiceCategoryCode
        this.form_1.invoiceCategoryName = proccessingData.invoiceCategoryName
        this.form_1.mainstayName = proccessingData.mainstayMchName
        this.form_1.mainstayNo = proccessingData.mainstayMchNo
        this.payMethod = 0
        this.form_1.serviceFeeAmount = proccessingData.serviceFeeAmount
        this.form_1.workerBillFilePath = proccessingData.workerBillFilePath
        this.form_1.serviceFeeBillPath = proccessingData.serviceFeeBillPath
        this.workerBillFilePath = proccessingData.workerBillFilePath
        this.form_1.invoiceWorkerItems = proccessingData.invoiceWorkerItems
        this.workerRecords = proccessingData.invoiceWorkerItems

        this.workerBillFileList.push({ name: '支付回单', url: proccessingData.workerBillFilePath })

        if (proccessingData.serviceFeeBillPath) {
          this.serviceFeeBillFileList.push({
            name: '支付凭证',
            url: proccessingData.serviceFeeBillPath
          })
        }

        this.form_1.invoiceWorkerItems.forEach(it => {
          this.form_1.invoiceAmount += Number(it.invoiceAmount)
        })
        {
          const { data } = await getExpressInfo()
          this.expressInfo = data
        }


        this.jobList.forEach(it => {
          if (it.jobId == proccessingData.jobId) {
            this.form_1.workCategoryCode = it.workCategoryCode
          }
        })

        {
          const { data } = await getInvoiceInfoWithMainstayNo({
            mainstayNo: this.form_1.mainstayNo,
            workCategoryCode: this.form_1.workCategoryCode
          })
          this.invoiceInfo = data;

          this.invoiceInfo.invoiceCategoryVoList.forEach(it => {
            this.invoiceCategoryMap[it.invoiceCategoryCode] = it.invoiceCategoryName
          })
        }

        {
          const { data, code } = await calculateServiceFee(
            { mainstayNo: this.form_1.mainstayNo, invoiceAmount: this.form_1.invoiceAmount }
          )
          this.form_1.serviceFeeAmount = data
          if (code !== 20000) return
          this.activeStep = 2
        }

        {
          this.getMainstayAccount()
        }
      }


    },
    async getJobList() {
      const { data } = await taskListOnGrant({})
      this.jobList = data
    },
    handleRemove(file, fileList, param, ref) {
      this[param] = this.form_1[param] = null;
      this[ref] = fileList
    },
    handleSuccess(response, file, fileList, param, ref) {
      this[param] = this.form_1[param] = fileList[0].response.data;
      this[ref] = fileList
    },
    async getMainstayList() {
      let param = {
        status: 100,
        productNo: 'CKH'
      }
      const { data } = await getMainstayListByProduct(param);
      data.forEach(item => {
        this.mainstayMap[item.mainstayNo] = item.mainstayName
      })
      this.mainstayList = data;
    },
    async nextStep() {

      if (this.activeStep == 0) {
        if (!this.form_1.jobId || !this.form_1.mainstayNo || !this.form_1.invoiceCategoryCode || !this.workerRecords.length) {
          this.$message.warning("请填入完整信息")
          return false;
        }
        if (!this.form_1.workerBillFilePath) {
          this.$message.warning("请上传付款凭证文件")
          return false;
        }
        for (const index in this.workerRecords) {
          let it = this.workerRecords[index]
          if (it.authStatus == 100 && it.signStatus) {
            if (it.invoiceAmount == null || it.invoiceAmount == '' || it.invoiceAmount == 0) {
              this.$message.warning("请填写开票金额")
              return false;
            }
          } else {
            this.$message.warning("请确保所有收款人均已认证和签约")
            return false;
          }
        }
      }
      if (this.activeStep == 2) {
        if (!this.form_1.serviceFeeBillPath) {
          this.$message.warning("请上传付款凭证文件")
          return false;
        }
        const { code } = await this.pay(true);
        if (code !== 20000) return
      }
      if (this.activeStep == 0) {
        this.workerRecords.forEach(it => {
          this.form_1.invoiceAmount += Number(it.invoiceAmount)
        })
        const { data } = await getExpressInfo()
        this.expressInfo = data
      }
      if (this.activeStep == 1) {
        const { data, code } = await calculateServiceFee(
          { mainstayNo: this.form_1.mainstayNo, invoiceAmount: this.form_1.invoiceAmount }
        )
        if (code !== 20000) return
        this.form_1.serviceFeeAmount = data
        this.getMainstayAccount()
      }
      this.activeStep++
    },
    async payLater() {
      const { code } = await this.pay(false)
      if (code === 20000) {
        await this.$confirm('支付完成后可继续完成开票申请', '保存成功，请稍后上传服务费支付凭证', {
          type: 'success',
          showClose: false,
          showCancelButton: false,
          closeOnClickModal: false,
        })
        this.$router.push('/finance/invioce/issueInvoiceCKH');
      }
    },
    async pay(payed) {

      let invoiceWorkerItems = this.workerRecords.filter(it => {
        return it.signStatus && it.authStatus
      })

      this.form_1.invoiceCategoryName = this.invoiceCategoryMap[this.form_1.invoiceCategoryCode]
      this.form_1.invoiceType = 1
      this.form_1.mainstayMchNo = this.form_1.mainstayNo
      this.form_1.mainstayMchName = this.mainstayMap[this.form_1.mainstayNo]
      this.form_1.payed = payed
      this.form_1.invoiceWorkerItems = invoiceWorkerItems
      const { data, code } = await applyOffline(this.form_1)

      return { data, code }
    },
    async backStep() {
      this.activeStep = this.activeStep - 1

      if (this.activeStep == 0) {
        this.form_1.invoiceAmount = 0
      }
      if (this.activeStep < 0) {
        this.$router.push('/finance/invioce/issueInvoiceCKH')
      }
    },
    handleJobChange(e) {
      this.form_1.invoiceCategoryCode = null
      this.getWorkerRecord();
    },
    handleMainstayChange(e) {
      this.form_1.invoiceCategoryCode = null
      this.getWorkerRecord();
    },
    async listInvoiceCategory(e) {
      if (e) {
        if (!this.form_1.jobId) {
          this.$message.warning("请先选择任务");
          return false;
        }
        if (!this.form_1.mainstayNo) {
          this.$message.warning("请先选择代征主体")
          return false;
        }
        this.catLoading = true
        var workCategoryCode = null;
        this.jobList.forEach(it => {
          if (it.jobId == this.form_1.jobId) {
            workCategoryCode = it.workCategoryCode
          }
        })

        const { data } = await getInvoiceInfoWithMainstayNo({
          mainstayNo: this.form_1.mainstayNo,
          workCategoryCode: workCategoryCode
        })
        this.invoiceInfo = data;

        this.invoiceInfo.invoiceCategoryVoList.forEach(it => {
          this.invoiceCategoryMap[it.invoiceCategoryCode] = it.invoiceCategoryName
        })
        this.catLoading = false
      }

    },
    async getWorkerRecord() {
      if (this.form_1.jobId && this.form_1.mainstayNo) {
        const { data } = await getJobworker({
          jobId: this.form_1.jobId,
          mainstayNo: this.form_1.mainstayNo,
          current: 1,
          size: 99999
        });
        this.workerRecords = data.records
      }
    },
    async getMainstayAccount() {
      const { data } = await getMainstayAccount({ mainstayNo: this.form_1.mainstayNo })
      this.mainstayAccount = data
    },
    deleteItem(row) {
      this.workerRecords = this.workerRecords.filter(it => {
        return it.id != row.id
      })
    },
    async workerInfo({ workerIdCardMd5 }) {
      const { data } = await workerInfo({
        workerIdCardMd5: workerIdCardMd5,
      });
      this.$preview([
        this.fileUrl + data.idCardBackUrl,
        this.fileUrl + data.idCardFrontUrl,
      ]);
    },

  }
}
</script>

<style
  lang="scss"
  scoped>

.bold-money {
  font-weight: bolder;

  &-container {
    background: #ecf8ff;
  }
}

.address-list {
  margin: 10px 0 32px;
}

.address-item {
  position: relative;
  display: inline-block;
  width: 250px;
  height: 200px;
  margin-right: 16px;
  padding: 20px;
  border: 2px solid $borderColor;
  overflow: hidden;
  cursor: pointer;

  &.active {
    border-color: $mainColor;

    &::after {
      content: '';
      display: block;
      position: absolute;
      right: 0;
      bottom: 0;
      width: 40px;
      height: 40px;
      background: $mainColor;
      transform: translate(50%, 50%) rotate(45deg)
    }

  }

  .address-name {
    padding-top: 12px;
    padding-bottom: 12px;
    @include bottom-border;
  }

  .address-detail {
    padding-top: 16px;
  }
}

// 汇款信息样式
.invoice-remit-info {
  padding: 16px;
  border: 1px dashed purple;

  .invoice-remit-title {
    font-weight: 500;
  }
}

// 结果页样式
.invoice-result {
  &-container {
    margin: 50px 0 0;
  }

  &-img {
    width: 100px;
  }

  &-title {
    font-size: 30px;
    font-weight: 500;
  }

  &-desc {
    font-size: 24px;
  }
}

.subTitle {
  margin-top: 20px;
  font-weight: bold;
}
</style>
