<template>
  <div class="page-container">
    <warning-block>温馨提示：请及时维护最新的邮寄地址，以保证您能及时收到相应票据。</warning-block>

    <div class="content-container">
      <el-form ref="form" :model="form" label-width="200px" :disabled="shouldEdit" :rules="rules">
        <el-form-item label="邮寄地址：" :rules="rules.addressDetail">
          <address-select
            ref="address"
            v-model="addressDetail"
            :default-address="defaultAddress"/>
        </el-form-item>
        <el-form-item prop="address" label="详细地址">
          <el-input type="textarea" v-model="form.address"></el-input>
        </el-form-item>

        <el-form-item label="收件人姓名：" prop="consignee">
          <el-input v-model="form.consignee"></el-input>
        </el-form-item>

        <el-form-item label="联系电话：" prop="telephone">
          <el-input v-model="form.telephone"></el-input>
        </el-form-item>
      </el-form>
      <div class="form-btn text-center">
        <el-button @click="confirm" :type="btnType">{{btnWord}}</el-button>
        <el-button @click="cancel" v-if="!shouldEdit">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getExpressInfo, updateExpressInfo } from '@/api/merchant'
import { validateParams } from '@/utils/validate'
export default {
  data() {
    return {
      addressDetail: [],

      form: {
      },

      shouldEdit: true,
      defaultAddress: '',

      rules: {
        addressDetail: [
          { required: true, trigger: 'blur', message: '请选择邮寄地址'},
        ],
        telephone: [
          { required: true, trigger: 'blur', message: '请输入联系电话'},
          { validator: validateParams({ type: 'Phone', msg: '请输入正确格式的联系电话'}), trigger: 'blur' }
        ],
        consignee: [
          { required: true, trigger: 'blur', message: '请输入收件人姓名'},
        ],
        address: [
          { required: true, trigger: 'blur', message: '请输入详细地址'}
        ]

      }
    }
  },
  computed: {
    btnType() {
      return this.shouldEdit ? '' : 'primary';
    },
    btnWord() {
      return this.shouldEdit ? '编辑' : '提交';

    }
  },
  mounted() {
    this.getExpressInfo();
  },
  methods: {
    async getExpressInfo() {
      const data = await getExpressInfo().then((response) => {
        return response.data || {};
      }).catch(err => {
        this.shouldEdit = false;
        return {}
      })
      this.$set(this, 'form', data);
      data.province && (this.defaultAddress += `${data.province}`) && (this.addressDetail.push(['', data.province]));
      data.city && (this.defaultAddress += `/${data.city}`) && (this.addressDetail.push(['', data.city]));
      data.county && (this.defaultAddress += `/${data.county}`) && (this.addressDetail.push(['', data.county]));
    },
    async confirm() {
      if (this.shouldEdit) {
        return this.shouldEdit = false;
      }
      for (let p in this.form) {
        if (typeof this.form[p] == 'string') {
          this.form[p] = this.form[p].trim();
        }
      }
      let result = await this.$refs.form.validate().catch(() => false)
      let form = {...this.form};
      const addressSection = ['province', 'city', 'county'];
      addressSection.forEach((item, index) => {
        form[item] = this.addressDetail[index] ? this.addressDetail[index][1] : '';
      })
      if (!this.addressDetail.length) {
        this.$message.error('请选择邮寄地址')
        result = false;
      }
      if (result) {
        const { data } = await updateExpressInfo(form);
        this.$message.success(data);
        this.shouldEdit = true;
      }
    },
    cancel() {
      this.shouldEdit = true;
      this.$refs.form.clearValidate();
      this.getExpressInfo();
    }
  }
}
</script>

<style lang="scss" scoped>
</style>