<template>
  <div>
    <div class="box-container">
      <div class="form-subtitle">发票信息</div>
      <el-form :model="form">
        <el-row>
          <el-col :span="8">
            <el-form-item label="发票流水号：">
              {{ form.trxNo }}
              <span
                class="func-content"
                v-clipboard="form.trxNo"
              >复制</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="受票方：">
              {{ form.employerMchName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发票类型：">
              {{ $dictCode('InvoiceTypeEnum', form.invoiceType).desc }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="开票总金额：">
              {{ form.invoiceAmount | moneyFormat }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开票类目：">
              {{ form.invoiceCategoryName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开票方：">
              {{ form.mainstayMchName }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="纳税人识别号：">
              {{ form.taxNo }}
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="单位注册地址及电话：">
              {{ form.registerAddrInfo }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="开票银行：">
              {{ form.bankName }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开票账号：">
              {{ form.accountNo }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item label="备注：">
              {{ form.remark || '-' }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="box-container">
      <el-tabs>
        <el-tab-pane label="申请记录">
          <el-form :model="form">
            <el-row>
              <el-col :span="8">
                <el-form-item label="所属产品：">
                  {{ form.productName }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="订单来源：">
                  {{ $dictCode('InvoiceSourceEnum', form.source).desc }}
                </el-form-item>
              </el-col>
              <el-col
                :span="8"
                v-if="form.jobId">
                <el-form-item label="开票关联任务：">
                  {{ form.jobName }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="申请方式：">
                  {{ $dictCode('ApplyTypeEnum', form.applyType).desc }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="流水类型：">
                  {{ form.applyType == 1 ? '发放流水' : '充值流水' }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="流水数据：">
                  时间段 {{ form.tradeCompleteDayBegin }} - {{ form.tradeCompleteDayEnd }}
                </el-form-item>
              </el-col>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="创建时间：">{{ form.createTime }}</el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="完成时间：">{{ form.completeTime || '-' }}</el-form-item>
                </el-col>
              </el-row>
            </el-row>

            <div class="form-subtitle">快递信息</div>
            <el-row>
              <el-col :span="8">
                <el-form-item label="快递单号：">
                  {{ form.expressNo }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="收件人姓名：">
                  {{ form.expressConsignee }}
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="联系电话：">
                  {{ form.expressTelephone }}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col>
                <el-form-item label="邮寄地址：">
                  {{ form.province }} {{ form.city }} {{ form.county }} {{ form.address }}
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>

        <el-tab-pane
          label="已申请发票"
          v-if="form.productNo === 'ZXH' || form.productNo === 'CEP' || form.category === 1">
          <template v-if="form.invoiceFileUrlList && form.invoiceFileUrlList.length > 0">
            <div class="form-invoice-link">
              <el-button
                v-for="(item, index) in form.invoiceFileUrlList"
                :key="index"
                type="text"
                style="margin-right: 10px;"
                @click="beforeDownloadFile(item, '发票文件')"
              >点击下载文件
              </el-button>
            </div>
            <div
              style="width: 80%; margin: 0 auto;"
              v-for="(item, index) in formatInvoiceFileUrlList"
              :key="index"
            >
              <pdf-reader
                :src="item.url"
                v-if="isPdf(item.origin)"
              ></pdf-reader>
              <img
                v-else
                :src="item.url"
                alt="已申请发票"
              >
            </div>
          </template>
          <div
            class="text-center"
            v-else
          >
            <p class="empty-tip">
              暂未上传影像版发票，如有疑问请联系客户经理
            </p>
          </div>
        </el-tab-pane>

        <el-tab-pane
          label="开票清单"
          v-if="form.category === 2 && form.productNo === 'CKH'">
          <el-button
            type="primary"
            v-on:click="exportDetail">导出excel
          </el-button>
          <el-button
            type="primary"
            v-on:click="downloadInvoiceBatch">批量下载发票
          </el-button>

          <el-table :data="list">
            <el-table-column
              label="发票类型"
              prop="invoiceType"
            >
              <template v-slot="{row, column}">
                {{ $dictCode('InvoiceTypeEnum', row[column.property]).desc }}
              </template>
            </el-table-column>
            <el-table-column
              label="姓名"
              prop="receiveNameDesensitize"></el-table-column>
            <el-table-column
              label="身份证号"
              prop="receiveIdCardNoDesensitize"
              width="180"></el-table-column>
            <el-table-column
              label="收款账号"
              prop="receiveAccountNoDesensitize"
              width="150"></el-table-column>
            <el-table-column
              label="手机号"
              prop="receivePhoneNoDesensitize"
              width="150"></el-table-column>
            <el-table-column
              label="开票金额"
              prop="invoiceAmount">
              <template v-slot="{row, column}">
                <div class="text-left">{{ row[column.property] | moneyFormat }}</div>
              </template>
            </el-table-column>
            <el-table-column
              label="状态"
              prop="invoiceStatus">
              <template v-slot="{row, column}">
                <el-tag :type="getTagType(row[column.property])">
                  {{ $dictCode('InvoiceStatusEnum', row[column.property]).desc }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template v-slot="{row}">
                <el-button
                  v-if="row.invoiceFileUrlList && row.invoiceFileUrlList.length>0 && (row.invoiceStatus == 2 || row.invoiceStatus == 3)"
                  type="text"
                  @click="downloadInvoice(row)">下载发票
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-footer class="pagination-container">
            <el-pagination
              layout="total, sizes, prev, pager, next, jumper"
              :current-page.sync="page.current"
              :page-size.sync="page.size"
              :page-sizes="[10, 50]"
              :total="page.total"
              @current-change="searchList()"
              @size-change="searchList(true)"
            ></el-pagination>
          </el-footer>

        </el-tab-pane>

        <!--<el-tab-pane-->
        <!--  label="附件"-->
        <!--  v-if="form.productNo === 'CKH'">-->
        <!--  <template v-if="form.serviceFeeBillPath">-->
        <!--    <div class="subTitle">服务费支付凭证</div>-->
        <!--    <el-button-->
        <!--      class="issue-zip-btn"-->
        <!--      type="text"-->
        <!--      @click="downloadZip('serviceFeeBillPath', '服务费支付凭证')">下载-->
        <!--    </el-button>-->
        <!--  </template>-->
        <!--  <template v-if="form.workerBillFilePath">-->
        <!--    <div class="subTitle">支付回单</div>-->
        <!--    <el-button-->
        <!--      class="issue-zip-btn"-->
        <!--      type="text"-->
        <!--      @click="downloadZip('workerBillFilePath', '支付回单')">下载-->
        <!--    </el-button>-->
        <!--  </template>-->
        <!--</el-tab-pane>-->
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { getByTrxNo, listInvoiceDetail } from '@/api/invoice'
import PDFReader from '@/components/PDFReader'

export default {
  components: {
    'pdf-reader': PDFReader
  },
  data() {
    return {
      form: {},
      list: [], // 普票清单
      page: {
        current: 1,
        size: 10,
        total: 0,
      },
      formatInvoiceFileUrlList: [],
    }
  },
  computed: {
    trxNo() {
      return this.$route.query ? this.$route.query.trxNo : ''
    }
  },
  watch: {
    "form.invoiceFileUrlList": {
      async handler(val) {
        if (val && val.length > 0) {
          this.formatInvoiceFileUrlList = []
          for (let i = 0; i < val.length; i++) {
            const item = val[i]
            const fileMsg = await this.formatFileMsg(item)
            this.formatInvoiceFileUrlList.push({
              url: fileMsg.fileUrl,
              origin: item
            })
          }
        }
      },
      immediate: true,
      deep: true,
    }
  },
  mounted() {
    if (this.trxNo) {
      this.getByTrxNo();
    }
  },
  methods: {
    async getByTrxNo() {
      const { data } = await getByTrxNo({
        trxNo: this.trxNo
      });
      this.form = data;
      if (this.form.invoiceType == 1) {
        this.searchList(true)
      }
    },
    async searchList(init) {
      if (init) {
        this.page.current = 1;
        this.list = [];
      }
      const { data } = await listInvoiceDetail({
        invoiceTrxNo: this.trxNo,
        ...this.page,
      })
      this.list = data.records;
      this.page.total = data.total;
    },
    downloadInvoice({ receiveIdCardNo }) {
      window.open(`${ this.baseUrl }/download/downloadInvoiceSingle?invoiceTrxNo=${ this.trxNo }&platTrxNo=${ receiveIdCardNo }&x-token=${ this.uploadHeader['X-Token'] }`)
    },
    downloadInvoiceBatch({ invoiceTrxNo, invoiceFileUrl }) {
      window.open(`${ this.baseUrl }/download/downloadInvoiceBatch?invoiceTrxNo=${ this.trxNo }&x-token=${ this.uploadHeader['X-Token'] }`)
    },
    getTagType(status) {
      switch (status) {
        case 1:
          return 'primary';
        case 3:
          return 'success';
        case 4:
          return 'danger';
        default:
          return 'info'
      }
    },
    exportDetail() {
      window.open(`${ this.baseUrl }/download/exportInvoiceDetail?invoiceTrxNo=${ this.trxNo }&x-token=${ this.uploadHeader['X-Token'] }`)
    },
    async beforeDownloadFile (url, name) {
      const fileMsg = await this.formatFileMsg(url)
      const suffix = fileMsg.origin.split('.').pop()
      this.downloadFile(fileMsg.fileUrl, name, suffix)
    },
    async downloadZip(param, name) {
      let url = this.form[param]
      if (!url) return
      const fileMsg = await this.formatFileMsg(url)
      const suffix = fileMsg.origin.split('.').pop()
      this.downloadFile(fileMsg.fileUrl, name, suffix)
    },
  }
}
</script>

<style
  lang="scss"
  scoped>
.box-container {
  .el-form-item {
    margin-bottom: 10px;
  }

  .form-subtitle {
    margin: 16px 0 10px;
    font-size: 18px;
    font-weight: bolder;
  }

}

.issue-zip-btn {
  padding-left: 36px;
}
</style>
