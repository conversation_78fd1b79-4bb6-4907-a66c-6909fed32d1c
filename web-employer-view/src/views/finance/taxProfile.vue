<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">代征主体：</span>
          <el-select
            v-model="searchForm.mainstayNo"
            clearable>
            <el-option
              v-for="item in mainstayList"
              :key="item.id"
              :label="item.mainstayName"
              :value="item.mainstayNo"></el-option>
          </el-select>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            :start-time.sync="searchForm.createBeginDate"
            :end-time.sync="searchForm.createEndDate"
            type="datetime"
            picker="separate"
            ref="datepicker"
          />
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">税款所属时期：</span>
          <date-picker
            :start-time.sync="searchForm.dateBegin"
            :end-time.sync="searchForm.dateEnd"
            :is-show-fast-time="false"
            :auto-fix="false"
            dayjs-format="YYYY-MM-DD"
            type="month"
            picker="separate"
            ref="monthpicker"
          />
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)">查询
        </el-button>
        <el-button
          type="text"
          @click="resetField">清空筛选条件
        </el-button>
      </div>
    </div>
    <div class="content-container">
      <el-table :data="list">
        <el-table-column
          label="税款所属时期"
          width="240">
          <template v-slot="{row}">
            {{ row.dateBegin }} 到 {{ row.dateEnd }}
          </template>
        </el-table-column>
        <el-table-column
          label="用工企业"
          width="150">
          <template v-slot="{row}">
            {{ row.employerNo }} <br> {{ row.employerName }}
          </template>
        </el-table-column>
        <el-table-column
          label="开票方"
          prop="mainstayName"></el-table-column>
        <el-table-column
          label="备注"
          prop="remark"></el-table-column>
        <el-table-column
          label="创建时间"
          width="180"
          prop="createTime"></el-table-column>
        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button
              v-if="row.certificateFilePath.length > 0"
              type="text"
              class="red-btn"
              @click="downloadCert(row)">下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-footer class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :page-sizes="[10, 30, 50]"
        :total="page.total"
        :page-size.sync="page.size"
        :current-page.sync="page.current"
        @size-change="search(true)"
        @current-change="search()" />
    </el-footer>
  </div>
</template>


<script>
import { listAllMainstayList } from "@/api/merchant";
import { getTaxCertList } from "@/api/invoice";


export default {
  name: 'TaxProfile',
  data() {
    return {
      searchForm: {},
      mainstayList: [],
      list: [],
      page: {
        current: 1,
        totalRecord: 0,
        size: 10,
      },
    }
  },
  mounted() {
    this.getMainstayList()
    this.search()
  },
  methods: {
    async getMainstayList() {
      const { data } = await listAllMainstayList()
      this.mainstayList = data
    },
    async search(init) {
      if (init === true) {
        this.page.pageCurrent = 1
      }
      const { data } = await getTaxCertList({
        ...this.searchForm,
        ...this.page,
      })
      this.list = data.records || []
      this.page.total = data.total || 0
    },
    resetField() {
      this.searchForm = {}
      this.$refs.datepicker.clearTime()
      this.$refs.monthpicker.clearTime()
    },
    async downloadCert({ certificateFilePath }) {
      for (let i = 0; i < certificateFilePath.length; i++) {
        const item = certificateFilePath[i]
        const fileMsg = await this.formatFileMsg(item)
        const suffix = item.split('.').pop()
        this.downloadFile(fileMsg.fileUrl, '完税证明', suffix)
      }
    },


  }
}
</script>
