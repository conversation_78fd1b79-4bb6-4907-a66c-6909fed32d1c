<template>
  <div class="issue-wrapper">
    <router-link
      v-for="(item, index) in productList"
      :key="index"
      class="issue-box"
      :to="item.path"
    >
      <div class="issue-icon">
        <svg-icon
          :icon-class="item.icon"
          :iconStyle="{width: '1.3em'}"
        />
      </div>
      <div>
        <i
          class="el-icon-success"
          style="color: #0f0;"
        ></i>
        已开通
      </div>
      <p class="issue-title">{{ item.label }}</p>
      <p
        v-html="item.desc"
        class="issue-desc"
      ></p>
      <div class="issue-operate text-right func-content">{{ item.text }}</div>
    </router-link>
  </div>
</template>

<script>
  import { listMchProduct } from "../../api/invoice";
  export default {
    name: 'IssueInvoiceList',
    data() {
      return {
        productRoute: {
          ZXH: {
            label: '智享汇',
            desc: '适用于批量个人经营项目的开票，开票类型为<span class="text-underline">增值税专用发票</span>',
            path: '/finance/invioce/issueInvoiceZxh',
            icon: 'zxh',
            text: '申请开票>>'
          },
          CKH: {
            label: '创客汇',
            desc: '适用于零散个人经营项目的开票，开票类型为<span class="text-underline">增值税专用发票</span>、<span class="text-underline">增值税普通发票</span>',
            path: '/finance/invioce/issueInvoiceCkh',
            icon: 'ckh',
            text: '申请开票>>'
          },
          JKH: {
            label: '聚客汇',
            desc: '适用于个人独立劳务项目的开票，开票类型为<span class="text-underline">增值税专用发票</span>',
            path: '/finance/invioce/issueInvoiceJkh',
            icon: 'jkh',
            text: '请联系销售开通'
          },
          CEP: {
            label: '差额普',
            desc: '适用于批量个人经营项目的开票，开票类型为<span class="text-underline">增值税普通发票、增值税专用发票</span>',
            path: '/finance/invioce/issueInvoiceCep',
            icon: 'cep',
            text: '申请开票>>'
          }
        },
        productList: []
      }
    },
    mounted() {
      this.listMchProduct();
    },
    methods: {
      async listMchProduct() {
        const { data } = await listMchProduct()
        this.productList = data.map(it => {
          let route = this.productRoute[it.productNo]
          return { ...route, ...it }
        })
        if (this.productList.length === 1) {
          const product = this.productList[0]
          if (product.productNo in this.productRoute) {
            this.$router.replace(product.path)
          }
        }
      }
    }
  }
</script>

<style lang="scss" scope>
  .issue-wrapper {
    display: flex;
    justify-content: space-evenly;
  }
  .issue-box {
    text-align: center;
    cursor: pointer;
    padding: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    background: #fff;
    width: calc(100% / 3);

    .issue-title {
      font-size: 20px;
      font-weight: bold;
    }

    .issue-icon {
      font-size: 150px;
    }
    .issue-desc {
      font-size: 13px;
    }
    .issue-operate {
      margin: 50px 0 0;
    }
  }
</style>
