<template>
  <div class="page-container">
    <warning-block>温馨提示：如需修改开票信息，请联系客户经理。</warning-block>

    <div class="content-container">
      <el-form
        label-width="200px"
        :rules="rules"
        ref="form"
        :model="invoiceInfo"
      >
        <el-form-item label="企业名称：">
          <el-input
            disabled
            v-model="invoiceInfo.mchName"
          ></el-input>
        </el-form-item>

        <el-form-item label="纳税人类型：">
          <el-radio-group
            v-model="invoiceInfo.taxPayerType"
            disabled
          >
            <el-radio
              v-for="(item, index) in $dict('TaxPayerTypeEnum')"
              :key="index"
              :label="Number(item.code)"
            >
              {{ item.desc }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="纳税人识别号：">
          <el-input
            disabled
            v-model="invoiceInfo.taxNo"
          ></el-input>
        </el-form-item>

        <el-form-item label="单位注册地址及电话：">
          <el-input
            type="textarea"
            disabled
            v-model="invoiceInfo.registerAddrInfo"
            :rows="5"
          ></el-input>
        </el-form-item>

        <el-form-item label="开户行及账号：">
          <el-input
            disabled
            v-model="invoiceInfo.bankName"
          ></el-input>
          <el-input
            disabled
            v-model="invoiceInfo.accountNo"
          ></el-input>
        </el-form-item>

        <el-form-item label="发票类目：">
          <el-tag
            type="info"
            v-for="(item, key) in invoiceInfo.invoiceCategoryVoList"
            :key="key"
          >
            {{ item.invoiceCategoryName }}
          </el-tag>
        </el-form-item>

        <div class="form-btn text-center">
          <!-- <el-button @click="confirm" :type="btnType">{{btnWord}}</el-button> -->
          <el-button
            @click="cancel"
            v-if="!shouldEdit"
          >取消</el-button>
        </div>

      </el-form>
    </div>

    <p class="subTitle">预约开票规则配置</p>
    <el-button
      type="primary"
      @click="open()"
    >新增配置</el-button>
    <el-table :data="configList">
      <el-table-column
        label="开票周期"
        prop="applyPeriod"
      >
        <template>
          每月
        </template>
      </el-table-column>
      <el-table-column
        label="固定日期"
        prop="applyDate"
      >
        <template v-slot="{row}">
          {{ `${row.applyDate}日` }}
        </template>
      </el-table-column>
      <el-table-column
        label="代征主体"
        prop="mainstayName"
      ></el-table-column>
      <el-table-column
        label="开票模式"
        prop="applyType"
      >
        <template v-slot="{row}">
          {{ applyTypeName }}
        </template>
      </el-table-column>
<!--      <el-table-column
        label="开票类目"
        prop="invoiceCategoryName"
      ></el-table-column> -->
      <el-table-column label="操作">
        <template v-slot="{row}">
          <el-button
            type="text"
            @click="open(row)"
          >编辑</el-button>
          <el-button
            type="text"
            @click="deleteConfig(row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :visible.sync="visible"
      :before-close="close"
      title="预约开票"
      top="20px"
    >
      <el-form
        ref="form"
        class="form"
        :model="form"
        :rules="rules"
        label-position="top"
      >
        <el-form-item
          label="开票周期"
          prop="applyPeriod"
        >
          <el-tag type="info">每月</el-tag>
        </el-form-item>
        <el-form-item
          label="固定日期"
          prop="applyDate"
        >
          <el-select v-model="form.applyDate">
            <el-option
              v-for="(item, index) in dates"
              :key="index"
              :value="item"
              :label="item + '日'"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="代征主体"
          prop="mainstayNo"
        >
          <el-select
            v-model="form.mainstayNo"
            clearable
            filterable
            v-on:change="mainstayChange"
          >
            <el-option
              v-for="(item, index) in mainstayList"
              :key="index"
              :value="item.mainstayNo"
              :label="item.mainstayName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="发票类型"
          prop="invoiceType"
        >
          <el-radio-group v-model="form.invoiceType">
            <el-radio
              v-for="(item, index) in $dict('InvoiceTypeEnum')"
              :key="index"
              :label="Number(item.code)"
            >{{ item.desc }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="开票模式"
          prop="applyType"
        >
          <!-- <el-tag type="info">按发放金额开票</el-tag> -->
          <el-tag type="info">
            {{ applyTypeName }}
          </el-tag>
        </el-form-item>

<!--        <el-form-item
          label="开票类目"
          prop="invoiceCategoryCode"
          v-if="invoiceInfo.invoiceCategoryVoList"
        >
          <el-select
            v-model="form.invoiceCategoryCode"
            clearable
            filterable
          >
            <el-option
              v-for="(item, index) in invoiceInfo.invoiceCategoryVoList"
              :key="index"
              :value="item.invoiceCategoryCode"
              :label="item.invoiceCategoryName"
            ></el-option>
          </el-select>
        </el-form-item> -->
      </el-form>

      <template v-slot:footer>
        <el-button
          type="primary"
          :loading="formLoading"
          @click="save"
        >保存</el-button>
        <el-button @click="close">取消</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
  import { getInvoiceInfo, updateInvoiceInfo, getMainstayList } from '@/api/merchant'
  import { listBookConfig, deleteBookConfig, applyBookConfig } from '@/api/invoice'
  import { toPromise } from '@/utils'
  const getDates = () => {
    let i = 1;
    let dates = []
    while (i <= 31) {
      dates.push(i)
      i++
    }
    return dates
  }
  export default {
    data() {
      return {
        shouldEdit: true,
        invoiceInfo: {},

        categoryMap: {},

        visible: false,
        form: {
          mainstayNo: '',
          mainstayName: '',
          productNo: '',
          applyPeriod: 100,
          applyDate: '',
          applyType: 1,
          invoiceType: '', // 创客汇2
          invoiceCategoryCode: '',
          invoiceCategoryName: '',
        }, // 预约配置
        configList: [], // 预约配置列表
        editRow: {},
        dates: getDates(),
        mainstayList: [],
        formLoading: false,
        rules: {
          mainstayNo: [{ required: true, message: '请选择代征主体', trigger: 'blur' }],
          applyDate: [{ required: true, message: '请选择日期', trigger: 'blur' }],
          invoiceType: [{ required: true, message: '请选择发票类型', trigger: 'change' }]
        },
        showInvoiceType: false,
      }
    },
    computed: {
      btnType() {
        return this.shouldEdit ? '' : 'primary';
      },
      btnWord() {
        return this.shouldEdit ? '编辑' : '提交';
      },
      applyTypeName() {
        if (this.form.productNo && this.form.productNo === 'CEP') {
          return '按发放金额、服务费分别开票'
        } else {
          return this.$dictCode('ApplyTypeEnum', this.form.applyType).desc
        }
      },
    },
    watch: {
      'form.mainstayNo': {
        handler(val) {
          let curr = this.mainstayList.find(item => item.mainstayNo === val)
          if (curr) {
            this.form.mainstayName = curr.mainstayName
            this.form.productNo = curr.productNo
            this.showInvoiceType = (curr.productNo === 'ZXH' || curr.productNo === 'CEP');
            if (!this.showInvoiceType) {
              this.form.invoiceType = 2
            }
          } else {
            this.form.mainstayName = ''
          }
        }
      }
    },
    mounted() {
      this.getInvoiceInfo();
      this.getConfigList();
      this.getMainstayList();
    },
    methods: {
      async getInvoiceInfo() {
        const { code, data } = await getInvoiceInfo();
        this.invoiceInfo = data || {};
        this.invoiceInfo.invoiceCategoryVoList.map(item => {
          this.$set(this.categoryMap, item.invoiceCategoryCode, item.invoiceCategoryName)
        })
      },
      handleChange(val) {
        this.invoiceInfo.defaultInvoiceCategoryName = this.categoryMap[val];
      },
      async confirm() {
        if (this.shouldEdit) {
          return this.shouldEdit = false;
        }
        const valid = await this.$refs.form.validate().catch(_ => { return false });
        if (valid) {
          const { data } = await updateInvoiceInfo(this.invoiceInfo);
          this.$message.success(data);
          this.getInvoiceInfo();
          this.shouldEdit = true;
        }
      },
      cancel() {
        this.$refs.form.clearValidate();
        this.shouldEdit = true;
        this.getInvoiceInfo();
      },
      async getConfigList() {
        const { data } = await listBookConfig()
        this.configList = data
      },
      async getMainstayList() {
        const { data } = await getMainstayList()
        this.mainstayList = data.filter(it=>{
          return it.productNo === 'ZXH' || it.productNo === 'CEP';
        })
      },
      open(row) {
        this.visible = true;
        if (row) {
          this.$nextTick(() => {
            this.edit(row)
          })
        }
      },
      edit(row) {
        for (let p in this.form) {
          this.form[p] = row[p]
        }
        this.form.id = row.id || undefined
        this.visible = true
      },
      async deleteConfig(row) {
        let [err] = await toPromise(this.$confirm('操作不可逆，是否确认删除这条数据？', '提示', { type: 'warning' }))
        if (err) return;
        const { data } = await deleteBookConfig({ id: row.id })
        data && this.$message.success(data)
        this.getConfigList()
      },
      async save() {
        let [err] = await toPromise(this.$refs.form.validate())
        if (err) return;
        this.formLoading = true;
        const [error, res] = await toPromise(applyBookConfig(this.form))
        this.formLoading = false;
        if (error) return;
        this.$message.success('新增成功')
        this.close()
        this.getConfigList()
      },
      close() {
        this.$refs.form.resetFields();
        this.showInvoiceType = false;
        this.visible = false
      },
      mainstayChange(mainstayNo) {
        let curr = this.mainstayList.find(item => item.mainstayNo === mainstayNo)
        if (curr && curr.productNo === 'CKH') {
          this.$message.warning("不支持创客汇产品的供应商预约开票")
          this.form.mainstayNo = null;
          return false;
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .page-container {
    .el-tag {
      margin-right: 4px;
    }
  }

  .form {
    margin: 20px;
    .el-form-item {
      margin-bottom: 8px;
    }
  }
</style>
