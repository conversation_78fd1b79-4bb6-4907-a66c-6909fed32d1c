<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">发票状态：</span>
            <el-radio-group v-model="searchForm.invoiceStatus">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button
                v-for="(item, index) in $dict('InvoiceStatusEnum')"
                :key="index"
                :label="item.code">
                {{ item.desc }}
              </el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">发票流水号：</span>
            <el-input
              clearable
              v-model="searchForm.trxNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">代征主体：</span>
            <el-select
              clearable
              v-model="searchForm.mainstayMchNo">
              <el-option
                v-for="(item, index) in mainStayList"
                :key="index"
                :label="item.mainstayName"
                :value="item.mainstayNo"></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">产品名称：</span>
            <el-select
              clearable
              v-model="searchForm.productNo">
              <el-option
                v-for="(item, index) in productList"
                :key="index"
                :label="item.productName"
                :value="item.productNo"></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">发票类型：</span>
            <el-select
              clearable
              v-model="searchForm.invoiceType">
              <el-option
                v-for="(item, index) in $dict('InvoiceTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code">
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">岗位类目：</span>
            <el-select
              clearable
              v-model="searchForm.workCategoryCode">
              <el-option
                v-for="(item, index) in workCategoryOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">订单来源：</span>
            <el-select
              v-model="searchForm.source"
              clearable>
              <el-option
                v-for="item in $dict('InvoiceSourceEnum')"
                :key="item.code"
                :value="item.code"
                :label="item.desc"></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
<!--          <div class="flex-item">-->
<!--            <span class="flex-item__label">金额类型：</span>-->
<!--            <el-select-->
<!--              v-model="searchForm.amountType"-->
<!--              clearable>-->
<!--              <el-option-->
<!--                v-for="item in $dict('InvoiceAmountTypeEnum')"-->
<!--                :key="item.code"-->
<!--                :value="item.code"-->
<!--                :label="item.desc"></el-option>-->
<!--            </el-select>-->
<!--          </div>-->
          <div class="flex-item">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              :start-time.sync="searchForm.createTimeBegin"
              :end-time.sync="searchForm.createTimeEnd"
              :is-show-fast-time="false"
              type="month"
              ref="datepicker1"
              picker="separate"
              style="width: 100px"
            />
          </div>
          <div class="flex-item">
            <span class="flex-item__label">完成时间：</span>
            <date-picker
              :start-time.sync="searchForm.completeTimeBegin"
              :end-time.sync="searchForm.completeTimeEnd"
              :is-show-fast-time="false"
              type="month"
              ref="datepicker2"
              picker="separate"
              style="width: 100px"
            />
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="search(true)">查询
            </el-button>
            <el-button @click="exportInvoceInfo">导出</el-button>
            <el-button
              type="text"
              @click="getExportList">查看已导出列表
            </el-button>
            <el-button
              type="text"
              @click="resetField">清空筛选条件
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="content-container">
      <el-table :data="response.data">
        <el-table-column
          label="发票流水号"
          width="120"
          prop="trxNo"></el-table-column>
        <el-table-column
          label="产品名称"
          width="80"
          prop="productName"></el-table-column>
        <el-table-column
          label="创建时间/完成时间"
          width="160">
          <template v-slot="{row}">
            {{ row.createTime }} <br /> {{ row.completeTime }}
          </template>
        </el-table-column>

        <el-table-column
          label="开票方"
          width="150"
          prop="mainstayMchName"></el-table-column>
        <el-table-column
          label="岗位类目"
          width="120"
          prop="workCategoryName"></el-table-column>
        <el-table-column
          label="发票类型"
          width="130">
          <template v-slot="{row}">
            {{ $dictCode('InvoiceTypeEnum', row.invoiceType).desc }}
            <el-tag
              size="small"
              type="success"
              v-if="row.category === 1 && row.productNo === 'CKH'">服务费
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="开票类目"
          width="150"
          prop="invoiceCategoryName"></el-table-column>

        <el-table-column
          label="开票总金额"
          width="150">
          <template v-slot="{row}">
            {{ row.invoiceAmount | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column
          label="申请方式"
          width="130">
          <template v-slot="{row}">
            {{ $dictCode('ApplyTypeEnum', row.applyType).desc }}
          </template>
        </el-table-column>

        <el-table-column
          label="订单来源"
          width="100">
          <template v-slot="{row}">
            <el-tag
              effect="plain"
              :type="row.source === 1 ? 'primary' : 'danger'">
              {{ $dictCode('InvoiceSourceEnum', row.source).desc }}
            </el-tag>
          </template>
        </el-table-column>


        <el-table-column
          label="状态"
          width="100">
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.invoiceStatus)">{{ $dictCode('InvoiceStatusEnum', row.invoiceStatus).desc }}</el-tag>
          </template>
        </el-table-column>

<!--        <el-table-column-->
<!--          label="反馈"-->
<!--          prop="errorDesc"-->
<!--          show-overflow-tooltip>-->
<!--        </el-table-column>-->

<!--        <el-table-column-->
<!--          label="金额类型"-->
<!--          width="130">-->
<!--          <template v-slot="{row}">-->
<!--            {{ $dictCode('InvoiceAmountTypeEnum', row.amountType).desc }}-->
<!--          </template>-->
<!--        </el-table-column>-->

        <el-table-column
          label="操作"
          fixed="right">
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="goDetail(row)">查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :current-page.sync="pageCurrent"
        :page-size.sync="pageSize"
        :page-sizes="[10, 50]"
        :total="response.totalRecord"
        @current-change="search()"
        @size-change="search(true)"
      ></el-pagination>
    </el-footer>
    <export-record ref="exportRecord"></export-record>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { getEmployerPosition, listAllMainstayList } from '@/api/merchant.js'
import ExportRecord from '@/components/ExportRecord'
import { exportInvoceInfo, getInvoiceList, listMchProduct } from '@/api/invoice'

export default {
  components: {
    ExportRecord
  },
  data() {
    return {
      response: {
        data: [],
        totalRecord: 0,
      },

      pageCurrent: 1,
      pageSize: 10,

      searchForm: {
        invoiceStatus: '',
        amountType: null,
      },

      createTimeRange: [],

      mainStayList: [],
      productList: [],
      workCategoryOptions: []
    }
  },
  mounted() {
    this.getPositionList();
    listAllMainstayList().then(({ data }) => {
      this.mainStayList = data
    });
    listMchProduct().then(({ data }) => {
      this.productList = data
    })
    // 默认设置三个月
    this.$refs.datepicker.changeTime([
      dayjs().subtract(3, 'month').startOf('date').format('YYYY-MM-DD HH:mm:ss'),
      dayjs().endOf('date').format('YYYY-MM-DD HH:mm:ss'),
    ])
    this.search();
  },
  methods: {
    async search(initFlag) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      const { data } = await getInvoiceList({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      })
      this.response.data = data.data;
      this.response.totalRecord = data.totalRecord;
    },
    async getPositionList() {
      const { data } = await getEmployerPosition();
      this.workCategoryOptions = data.map((e) => {
        return {
          label: e.workCategoryName,
          value: e.workCategoryCode,
        };
      });
    },
    resetField() {
      this.$refs.datepicker1.clearTime();
      this.$refs.datepicker2.clearTime();
      this.searchForm = {
        invoiceStatus: '',
      };
    },
    getTagType(status) {
      switch (status) {
        case 1:
          return 'primary';
        case 3:
          return 'success';
        case 4:
          return 'danger';
        default:
          return 'info'
      }
    },
    goDetail(data) {
      this.$router.push({
        path: '/finance/issueInvoice/issueDetail',
        query: {
          trxNo: data.trxNo || ''
        }
      });
    },
    async exportInvoceInfo() {
      const { data } = await exportInvoceInfo(this.searchForm);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(this.$dictFlag('ReportTypeEnum', 'MERCHANT_INVOICE_RECORD_INFO_EXPORT').code);
    },
  }
}
</script>

<style
  lang="scss"
  scoped>

</style>
