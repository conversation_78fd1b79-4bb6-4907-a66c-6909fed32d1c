<template>
  <el-main>
    <div class="task-data-box">
      <div class="task-data-user">
        您好, {{ $store.getters.userData.operator.name }}
      </div>
      <div v-if="taskData" class="task-data-count">
        <div class="task-data-list">
          <div class="task-data-item text-center">
            <div class="task-data-title">待审批流程</div>
            <div class="task-data-num">
              {{ taskData.flowCount }}
            </div>
          </div>
          <div class="task-data-item text-center">
            <div class="task-data-title ">待处理工单</div>
            <div class="task-data-num">
              {{ taskData.workCount }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="clearfix">
      <div class="notify-message-box fr">
        <div v-if="notifyData.notifyList" class="nm-box">
          <div class="box-title">
            消息通知
            <router-link
              to="/notify/list"
              class="notify-btn"
            >
              <el-button type="text">
                更多
              </el-button>
            </router-link>
          </div>
          <div class="nm-content">
            <div
              class="nm-item notify-item"
              v-for="(item, index) in notifyData.notifyList"
              :key="index"
              @click="openNotify(item.id)"
            >
              <div
                class="nm-item-content flex-wrapper"
                style="align-items: center"
              >
                <svg-icon icon-class="broadcast"></svg-icon>
                <span
                  style="margin-left: 12px"
                  class="flex-item"
                >
                  {{ item.notificationTitle }}
                </span>
                <span>{{ item.publishTime }}</span>
              </div>
            </div>
          </div>
        </div>
        <div v-if="notifyData.dynamicList" class="nm-box">
          <div class="box-title">最新动态</div>
          <div class="nm-content">
            <div
              class="nm-item"
              v-for="(item, index) in notifyData.dynamicList"
              :key="index"
            >
              <div class="nm-item-content">
                <span style="margin-left: 12px">
                  {{ item }}
                </span>
              </div>
            </div>

          </div>
        </div>
      </div>
      <div class="main-content-box">
        <div class="main-box">
          <!-- 数据 -->
          <div v-if="monthData" class="month-data-box">
            <div class="month-data-title-box">
              <span class="month-data-title">
                本月数据总览
              </span>
              <span class="color-gray">更新时间：{{ monthData.date }}</span>
            </div>
            <div class="month-data-count">
              <div class="month-data-block">
                <div class="month-data-item">发放金额：{{ monthData.orderAmount | moneyFormat }}</div>
                <div class="month-data-item">发放笔数：{{ monthData.orderCount }}</div>
              </div>
              <div class="month-data-block">
                <div class="month-data-item">开票金额：{{ monthData.invoiceAmount | moneyFormat }}</div>
                <div class="month-data-item">开票笔数：{{ monthData.invoiceCount }}</div>
              </div>
              <div class="month-data-block">
                <div class="month-data-item">充值金额：{{ monthData.chargeAmount | moneyFormat }}</div>
                <div class="month-data-item">充值笔数：{{ monthData.chargeCount }}</div>
              </div>
            </div>
          </div>
          <!-- 快捷方式 -->
          <div v-if="fastList && fastList.length" class="flex-wrapper fast-box">
            <div
              class="flex-item fast-item"
              v-for="(item, index) in fastList"
              :key="index"
              @click="fastClick(item.path)"
            >
              <div class="fast-item-icon">
                <i :class="item.icon"></i>
              </div>
              <div class="fast-item-name">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      :show-close="false"
      :visible.sync="visible"
      :title="dialogContent.notificationTitle"
      top="2vh"
    >
      <div class="notify-content" v-html="dialogContent.notificationContent"></div>
      <template #footer>
        <el-button @click="visible = false" type="primary">确定</el-button>
      </template>
    </el-dialog>

    <el-dialog
      :show-close="false"
      :visible.sync="authDialogVisiable"
      title="商户未认证"
      top="2vh"
    >
      <span>商户暂未提交资质认证信息，资质认证通过后即可使用完整功能</span>
      <template #footer>
        <el-button @click="authDialogVisiable = false" type="info">暂不认证</el-button>
        <el-button @click="goMchAuth" type="primary">去认证</el-button>
      </template>
    </el-dialog>
  </el-main>
</template>

<script>
  import { getTaskData, getMonthData } from '@/api/index'
  import { getNotify } from '@/api/notify'
  import { getMerchant } from '@/api/merchant.js'
  export default {
    data(vm) {
      return {
        // fastList: [
        //   { name: '充值', path: '/account/recharge', icon: 'el-icon-money', },
        //   { name: '提现', path: '/account/withdraw', icon: 'el-icon-s-promotion' },
        //   { name: '提交发放名单', path: '/trade/order/upload', icon: 'el-icon-upload' },
        //   { name: '订单明细', path: '/trade/order/detail', icon: 'el-icon-tickets' },
        //   { name: '申请开票', path: '/finance/invioce/issueInvoice', icon: 'el-icon-document-add' },
        //   { name: '开票记录', path: '/finance/invoice/issueRecord', icon: 'el-icon-document-copy' },
        // ], // 快捷列表

        taskData: null,
        monthData: null,

        visible: false, // 弹窗显示
        dialogContent: {}, // 弹窗内容
        authDialogVisiable:false,
        merchantInfo:{}
      }
    },
    computed: {
      notifyData() {
        return this.$store.getters.notifyData
      },
      fastList() {
        const list = [
          { name: '充值', path: '/account/recharge', icon: 'el-icon-money', },
          { name: '提现', path: '/account/withdraw', icon: 'el-icon-s-promotion' },
          { name: '提交发放名单', path: '/trade/order/upload', icon: 'el-icon-upload' },
          { name: '订单明细', path: '/trade/order/detail', icon: 'el-icon-tickets' },
          { name: '申请开票', path: '/finance/invioce/issueInvoice', icon: 'el-icon-document-add' },
          { name: '开票记录', path: '/finance/invoice/issueRecord', icon: 'el-icon-document-copy' },
        ]
        if (
          !this.$store.getters.userData 
          || !this.$store.getters.userData.functions 
          || !this.$store.getters.userData.functions.length
        ) {
          return []
        }

        const urlList = this.$store.getters.userData.functions.map(item => item.url)
        console.log({ urlList })
        return list.filter(item => urlList.includes(item.path))

      }
    },
    mounted() {
      if(this.$route.query.from==0){
        this.getMerchant()
      }
      this.getData()
    },
    methods: {
      fastClick(path) {
        this.$router.push({
          path
        }, () => {
          this.$nextTick(() => {
            this.$store.dispatch('toggleSideBar', false)
          })
        })
      },
      async openNotify(id) {
        const { data } = await getNotify({ id })
        if (data) {
          const { notificationContent, notificationTitle } = data
          this.toggleNotify(data)
        }
      },
      async getMerchant(){
        const {data} =await getMerchant()
        console.log(data)
        if(data && data.authStatus==102){
          this.merchantInfo=data
          this.authDialogVisiable=true
        }
      },
      goMchAuth(){
        sessionStorage.setItem('mainInfo', JSON.stringify(this.merchantInfo));
        this.authDialogVisiable=false
        this.$router.push(`/merchant/auth?mchNo=${this.merchantInfo.mchNo}`)
      },
      // 获取数据
      async getData() {
        const [{ data: taskData }, { data: monthData }] = await Promise.all([
          getTaskData(),
          getMonthData(),
        ])
        this.taskData = taskData
        this.monthData = monthData
      },
      toggleNotify(data) {
        this.visible = !this.visible;
        this.dialogContent = data;
      },
    }
  }
</script>

<style lang="scss" scoped>
  .el-main {
    // 任务
    .task-data {
      &-box {
        display: flex;
        align-items: center;
        background: #fff;
        margin-bottom: 32px;
        padding: 32px 16px;
      }
      &-user {
        font-size: 30px;
        font-weight: bold;
        flex: 1 0;
      }
      &-list {
        display: flex;
        align-items: center;
      }
      &-item {
        margin: 0 20px;
      }
      &-title {
        color: #666;
        font-size: 15px;
      }
      &-num {
        font-size: 25px;
      }
    }
    // 月数据
    .month-data {
      &-box {
        margin-bottom: 32px;
        background: #fff;
      }
      &-title-box {
        padding: 8px 16px;
        border-bottom: 1px solid #ccc;
      }
      &-title {
        font-size: 20px;
        font-weight: bold;
      }
      &-count {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
      }
      &-item {
        margin: 8px 0;
      }
    }
    // 消息通知
    .nm-box {
      background: #fff;

      .box-title {
        position: relative;
        padding: 20px;
        border-bottom: 1px solid #ddd;
        font-weight: bold;
      }

      .notify-btn {
        @include position-tb-center(absolute);
        right: 20px;
      }

      .nm-content {
        width: 400px;
        margin-bottom: 16px;
      }
      .nm-item-content {
        padding: 4px 10px;
      }
      .notify-item:hover {
        cursor: pointer;
        color: $mainColor;
      }
    }

    // 主要面板
    .main-content-box {
      padding-right: 16px;
      overflow: hidden;
    }

    // 快捷方式
    .fast-box {
      padding: 16px 0;
      cursor: pointer;
      background: #fff;
    }
    .fast-item {
      flex-direction: column;
      &-icon {
        font-size: 25px;
      }
    }
  }
</style>
