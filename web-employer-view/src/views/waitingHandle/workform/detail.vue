<template>
  <div class="workform-page-container">
    <div class="flex-container">
      <h2>{{ flow.businessKey }}</h2>
      <div class="flex-wrapper">
        <div class="flex-item">
          <span class="flex-label">发起人：</span>
          <span class="flex-item__content">{{ flow.initiatorName }}</span>
        </div>
        <div class="flex-item">
          <span class="flex-label">流程主题：</span>
          <span class="flex-item__content">
            {{ flow.flowTopicName }}
          </span>
        </div>
      </div>
      <div class="flex-wrapper">
        <div class="flex-item">
          <span class="flex-label">创建时间：</span>
          <span class="flex-item__content">
            {{ flow.createTime }}
          </span>
        </div>
        <div class="flex-item">
          <span class="flex-label">最后跟进时间：</span>
          <span class="flex-item__content">
            {{ flow.updateTime }}
          </span>
        </div>
      </div>
      <div
        class="flex-wrapper"
        v-if="flow.referenceNo"
      >
        <div class="flex-item">
          <span class="flex-label">流程主体：</span>
          <span
            class="flex-item__content func-content"
            @click="clickFlowObj(flow.referenceNo)"
          >{{ flow.referenceName }} ({{ flow.referenceNo }})</span>
        </div>
      </div>
      <div class="flex-wrapper">
        <div class="flex-item flex-item-main">
          <span class="flex-label">流程备注：</span>
          <span class="flex-item__content">
            {{ flow.remark }}
          </span>
        </div>
      </div>
    </div>

    <div class="box-container">
      <div class="worklist-form-box">
        <div class="text-center">
          <h1>{{ info.title }}</h1>
          <h6>{{ info.desc }}</h6>
        </div>
        <el-form
          v-if="info.component"
          :model="form"
          :rules="rules"
          label-position="right"
          label-width="120px"
          ref="form"
          :disabled="actionType == 'VIEW'"
        >
          <parse-item
            v-for="(item, index) in info.component"
            :key="index"
            :element="item"
            :form="form"
            @change="({ field, value }) => onChange(field, value, index)"
          ></parse-item>


        </el-form>
        <div
          class="workform-btn-box"
          v-loading="loading"
        >
          <el-button
            class="work-form-btn"
            type="info"
            @click="cancel"
          >取消
          </el-button>
          <template v-if="actionType == 'EDIT'">
            <el-button
              class="work-form-btn"
              type="primary"
              @click="save"
            >保存
            </el-button>
            <el-button
              class="work-form-btn"
              type="primary"
              @click="operate(100)"
            >提交
            </el-button>
          </template>
        </div>
      </div>
      <opinion-dialog
        :visible.sync="visible"
        @close="onConfirm"
      ></opinion-dialog>
    </div>
  </div>
</template>

<script>
import opinionDialog from '../opinionDialog';
import { editBusinessData, getFlowInfo, submitTask } from '@/api/flow'
import parseItem from './parse/item'
import { toPromise } from '@/utils'

const customRuleMap = {
  'sign-upload': function (item, rule, form) {
    if (Array.isArray(item.field)) {
      rule[item.field[0]] = [{
        required: true,
        message: item.title + '不能为空',
        trigger: 'blur'
      }]
      rule[item.field[1]] = [{
        required: true,
        validator(rule, value, callback) {
          if (form[item.field[0]] == 1) {
            if (value && value.length > 0) {
              callback()
            } else {
              callback(new Error('请上传文件'))
            }
          } else {
            callback()
          }
        },
      }]
    }
  }
}
const generateRule = (item, rule, form) => {
  if (item.elemType === 'custom') {
    customRuleMap[item.type](item, rule, form)
  } else {
    rule[item.field] = [{
      required: true,
      message: item.title + '不能为空',
      trigger: 'blur'
    }]
  }
}
export default {
  name: "worklistFormDetail",
  components: {
    parseItem,
    opinionDialog,
  },
  data() {
    return {
      flow: {},
      info: {},
      form: {},
      rules: {},
      visible: false,
      loading: false,
    }
  },
  computed: {
    commonFlowId() {
      return this.$route.query.commonFlowId || ''
    },
    taskId() {
      return this.$route.query.taskId || ''
    },
    actionType() {
      return this.$route.query.actionType || 'EDIT'
    },

  },
  mounted() {
    this.getWorkForm()
  },
  methods: {
    buildFormAndRule() {
      let rules = {}
      this.info.component.forEach(item => {
        if (Array.isArray(item.field)) {
          item.elemType = 'custom'
          this.$set(this.form, item.field[0], item.value)
          this.$set(this.form, item.field[1], item.urls)
        } else if (item.field) {
          this.$set(this.form, item.field, item.value)
        }
        generateRule(item, rules, this.form)
      })
      this.rules = rules
      setTimeout(() => {
        this.$refs.form.clearValidate()
      }, 0);
    },
    async getWorkForm() {
      const { data } = await getFlowInfo({
        commonFlowId: this.commonFlowId,
        taskId: this.taskId,
      })
      this.flow = data
      this.info = JSON.parse(data.extInfo)
      this.buildFormAndRule()
    },
    onChange(field, value, index) {
      if (this.info.component[index].elemType === 'custom') {
        if (field == this.info.component[index].field[0]) {
          this.info.component[index].value = value
        } else {
          this.info.component[index].urls = value
        }
      } else {
        this.info.component[index].value = value
      }
      this.form[field] = value
    },
    cancel() {
      this.$router.push('/audit/workform')
    },
    async save() {
      let [err] = await toPromise(this.$refs.form.validate())
      if (err) return;
      await this.editBusinessData()
      this.cancel()
    },
    async editBusinessData(act) {
      this.loading = true
      await editBusinessData({
        commonFlowId: this.commonFlowId,
        taskId: this.taskId,
        extInfo: JSON.stringify(this.info),
        remark: act ? act.opnion : '',
      })
      this.loading = false
      this.$message.success('提交成功')
    },
    async operate(type) {
      let [err] = await toPromise(this.$refs.form.validate());
      if (err) {
        return this.$message.error('请补充信息')
      }
      ;
      [err] = await toPromise(this.$confirm(
        '是否确认提交该工单？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }))
      if (err) return;
      this.actType = type
      this.onConfirm()
    },
    async onConfirm() {
      this.loading = true;
      await this.editBusinessData()
      const {
        commonFlowId,
        taskId,
        actType
      } = this
      const { data } = await submitTask({
        commonFlowId,
        taskId,
        status: actType,
        condition: {
          transfer: 0
        }
      })
      data && this.$message.success(data)
      this.loading = false;
      this.cancel()
    },
  }
}
</script>

<style
  lang="scss"
  scoped>
.workform-page-container {
  padding-bottom: 50px;

  .flex-container {
    padding: 8px 0 32px 4px;
  }

  .flex-item {
    max-width: 500px;
  }

  .flex-wrapper {
    margin-top: 16px;
  }

  .flex-label {
    flex: 0 0 100px;
  }
}

.worklist-form-box {
  .workform-btn-box {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 16px 0;
    background: #fff;
    text-align: right;
    z-index: 100;
  }

  .work-form-btn {
    margin-right: 16px;
  }
}
</style>
