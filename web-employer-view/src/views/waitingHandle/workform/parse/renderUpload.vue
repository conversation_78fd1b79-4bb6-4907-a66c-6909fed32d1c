<template>
  <file-upload
    list-type="text"
    :urls.sync="element.value"
    :max="20"
    :options="{pdf: true, word: true}"
    @change="onChange"
    v-bind="$attrs.prop"
  >
    <el-button
      type="primary"
      size="mini"
    >
      点击上传
    </el-button>
    <div v-if="element.descText" slot="tip">{{ element.descText }}</div>
  </file-upload>
</template>

<script>
  import FileUpload from '@/components/FileUpload'
  export default {
    name: 'renderUpload',
    components: {
      FileUpload
    },
    props: [
      'element'
    ],
    methods: {
      onChange() {
        this.$emit('change', this.element.value)
      },
      downloadTemp(e) {
        e.stopPropagation();
        this.downloadFile(this.fileUrl + this.element.templateFileUrl)
      }
    }
  }
</script>

<style>
</style>