<template>
  <div class="box-container">
    <el-tabs v-model="activeTab">
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="index"
        :label="item.label"
        :name="String(index)"
      ></el-tab-pane>
    </el-tabs>

    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">流程状态：</span>
          <el-select clearable v-model="searchForm.status">
            <el-option
              v-for="item in $dict('FlowStatus')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">发起人：</span>
          <el-input v-model="searchForm.initiatorName" placeholder="模糊查询" clearable></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">流程名称：</span>
          <el-input v-model="searchForm.flowTopicName" placeholder="模糊查询" clearable></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">流程开始时间：</span>
          <date-picker
            ref="datepicker"
            type="datetime"
            picker="separate"
            :is-show-fast-time="false"
            :start-time.sync="searchForm.beginDate"
            :end-time.sync="searchForm.endDate"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper">
        <div class="search-btn-group">
          <el-button
            type="primary"
            @click="search"
          >搜索</el-button>
        </div>
      </div>
    </div>

    <div class="content-container">
      <el-table :data="list">
        <el-table-column label="序号" type="index" :index="getIndex"></el-table-column>
        <el-table-column label="流程名称" prop="flowTopicName"></el-table-column>
        <el-table-column label="流程ID" prop="commonFlowId" width="100"></el-table-column>
        <el-table-column label="发起人" prop="initiatorName"></el-table-column>

        <el-table-column label="流程状态" prop="status">
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.status)">
              {{ $dictCode('FlowStatus', row.status).desc }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="流程开始时间" prop="createTime">
          <template v-slot="{row, column}">
            <p v-html="renderTime(row[column['property']])"></p>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          min-width="70px"
          fixed="right"
        >
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="goDetail(row)"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10,50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </el-footer>
  </div>
</template>

<script>
  import { getHandleList, getSendList, getTodoList } from '@/api/flow';
  export default {
    name: "FlowManage",

    data() {
      return {
        activeTab: 0,

        tabs: [
          {
            label: '待我审批的',
            api: getTodoList,
          },
          {
            label: '我收到的审批',
            api: getHandleList,
          },
          {
            label: '我发出的审批',
            api: getSendList,
          },
        ],

        pageSize: 10,
        pageCurrent: 1,
        totalRecord: 0,

        searchForm: {
          status: '',
          initiatorName: '',
          beginDate: '',
          endDate: '',
        },

        list: []
      }
    },
    watch: {
      activeTab: {
        handler() {
          this.handleTabChange()
        }
      }
    },
    methods: {
      handleTabChange() {
        this.search(true);
      },
      async search(init) {
        if (init) {
          this.pageCurrent = 1;
        }
        const api = this.tabs[this.activeTab]['api'];

        const { data } = await api({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        });

        this.totalRecord = data.totalRecord;
        this.list = data.data;
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1
      },
      goDetail(row) {
        this.$router.push({
          path: '/audit/detailProcess',
          query: {
            processId: row.commonFlowId,
            taskId: row.taskId || '',
          }
        })
      },
      getTagType(status) {
        switch(Number(status)) {
          case 100:
            return 'success'
          case 102:
            return 'danger'
          default:
            return
        }
      },
    },
  }
</script>

