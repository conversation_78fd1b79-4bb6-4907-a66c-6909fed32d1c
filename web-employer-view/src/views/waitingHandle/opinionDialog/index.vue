<template>
  <el-dialog
    :visible.sync="visible"
    title="审核意见"
    :before-close="close">

    <el-input
      v-model="approvalOpinion"
      type="textarea"
      max="100"
      rows="8"
      show-word-limit
      maxlength="100">
    </el-input>

    <template v-slot:footer>
      <el-button @click="closeDialog">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      approvalOpinion: ''
    }
  },
  methods: {
    closeDialog() {
      this.approvalOpinion = '';
      this.$emit('close');
      this.close();
    },
    handleConfirm() {
      this.$emit('close', {
        action: 'confirm',
        opnion: this.approvalOpinion
      })
      this.close();
    },
    close() {
      this.approvalOpinion = '';
      this.$emit('update:visible', false);
      this.$emit('close');
    }
  }
}
</script>

<style>

</style>