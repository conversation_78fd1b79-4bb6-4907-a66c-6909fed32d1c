<template>
  <el-dialog
    :visible="visible"
    :before-close="close"
    title="发起签约"
    append-to-body
  >
    <el-form
      ref="form"
      :model="form"
      :rules="signRules"
      label-width="150px"
    >
      <warning-block>将向该用户发送电子签约短信，请确认用户手机号</warning-block>
      <el-form-item label="姓名：">
        <el-input
          disabled
          v-model="form.receiveName"
        ></el-input>
      </el-form-item>
      <el-form-item label="证件号：">
        <el-input
          disabled
          v-model="form.idCard"
        ></el-input>
      </el-form-item>
      <el-form-item label="手机号：" prop="phone">
        <el-input
          clearable
          v-model="form.phone"
        ></el-input>
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <el-button type="primary" @click="confirm">确认</el-button>
      <el-button @click="close">取消</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import WarningBlock from '@/components/WarningBlock'
  import { validateParams } from '@/utils/validate'
  import { toPromise } from '@/utils'
  export default {
    name: 'sign-dialog',
    components: {
      WarningBlock
    },
    data() {
      return {
        visible: false,
        form: {},
        signRules: {
          phone: [
            { required: true, message: '请输入手机号', trigger: 'blur' },
            { validator: validateParams({ type: 'Phone', msg: '请输入正确的手机号' }), trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      close() {
        this.visible = false;
        this.$refs.form.resetFields();
      },
      async confirm() {
        const [err] = await toPromise(this.$refs.form.validate())
        if (err) return;
        this.$emit('change', { ...this.form });
        this.close();
      }
    },
  }
</script>

<style scoped lang="scss">

</style>
