<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">姓名：</span>
          <el-input
            clearable
            v-model="form.receiveName"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">证件号码：</span>
          <el-input
            clearable
            v-model="form.idCard"
          ></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">代征主体：</span>
          <el-input
            clearable
            v-model="form.mainstayName"
          ></el-input>
        </div>
        <div class="flex-item-main">
          <span class="flex-item__label">账单月份：</span>
          <date-picker
            :start-time.sync="form.beginDate"
            :end-time.sync="form.endDate"
            :is-show-fast-time="false"
            :auto-fix="false"
            ref="datePicker"
            picker="separate"
            type="month"
          >
          </date-picker>
        </div>
      </div>

      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询</el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件</el-button>
      </div>
    </div>

    <div class="func-container">
      <el-button
        type="text"
        @click="() => { showResult = !showResult }"
      >
        统计查询结果
        <i
          class="el-icon-instance"
          :class="{ 'el-icon-arrow-up': showResult, 'el-icon-arrow-down': !showResult }"
        ></i>
      </el-button>
      <div v-if="showResult">
        <div>
          自由职业者
          <span style="color: #409EFF;">{{ countData.freelanceCount || 0 }}</span>
          个，接单数量
          <span style="color: #409EFF;">{{ countData.orderCount || 0 }}</span>
          个；
        </div>
        <div>
          实发金额汇总
          <span style="color: #FBB84E">{{ countData.orderItemNetAmountSum || 0 }}</span>
          元。
        </div>
      </div>

    </div>

    <div class="content-container">
      <el-table
        :data="list"
        @sort-change="onSortChange"
        @filter-change="onFilterChange"
      >
        <el-table-column
          label="姓名"
          prop="receiveName"
        ></el-table-column>
        <el-table-column
          label="证件号码"
          prop="receiveIdCardNo"
          width="180"
        >
          <template v-slot="{ row }">
            {{ row.receiveIdCardNo }}
            <div
              v-if="row.idCardBackUrl"
              @click="openIdCard(row)"
              class="func-content"
            >查看证件照片</div>
            <div
              v-if="row.idCardCopyUrl"
              @click="openIdCopy(row)"
              class="func-content"
            >查看证件复印件</div>
          </template>
        </el-table-column>
        <el-table-column
          label="月份"
          prop="createDate"
        ></el-table-column>
        <el-table-column
          label="用工企业"
          prop="employer"
          width="180"
        >
          <template v-slot="{ row }">
            <div
              class="func-content"
              @click="goOrderDetail(row)"
            >
              {{ row.employerName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="代征主体">
          <template v-slot="{ row }">
            <div
              class="func-content"
              @click="goOrderDetail(row, { mainstayNo: row.mainstayNo, receiveIdCardNo: row.receiveIdCardNo })"
            >
              {{ row.mainstayName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="接单数量"
          width="250"
        >
          <template v-slot="{ row }">
            <span
              class="func-content"
              @click="goOrderDetail(row, { employerNameLike: row.employerName, mainstayNo: row.mainstayNo, receiveIdCardNo: row.receiveIdCardNo })"
            >
              {{ row.conditionOrder }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="实发金额"
          sortable="custom"
          width="120"
          prop="orderItemNetAmount"
        >
          <template v-slot="{ row }">
            <div class="text-right">
              ￥{{ row.orderItemNetAmount }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="电子签约">
          <template v-slot="{ row }">
            <el-tag :type="row.signRecord == 1 ? 'success' : 'danger'">
              {{ row.signRecord == 1 ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          width="150"
          label="身份证复印件"
          :filters="[
            { text: '已上传', value: 1 },
            { text: '未上传', value: 0 },
          ]"
          prop="idCard"
          column-key="hasUploadIdCard"
        >
          <template v-slot="{ row }">
            <el-tag :type="row.idCard == 1 ? 'success' : 'danger'">
              {{ row.idCard == 1 ? '已上传' : '未上传' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
        >
          <template v-slot="{ row }">
            <el-button
              type="text"
              @click="startSign(row)"
              v-if="row.signRecord !== 1"
            >发起签约</el-button>
            <el-button
              type="text"
              @click="uploadId(row)"
            >上传身份证</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </div>

    <upload-img
      ref="uploadDialog"
      :signItem="editRow"
      :use-data-api="true"
      @change="search()"
    ></upload-img>

    <sign-dialog
      ref="signDialog"
      @change="onSign"
    ></sign-dialog>
  </div>
</template>

<script>
  import dayjs from 'dayjs'
  import { analyzeFreelance, countFreelance, sign } from '@/api/data';
  import SignDialog from "../component/SignDialog";
  import UploadImg from '@/views/sign/Components/UploadImg'

  export default {
    name: 'freelance-data',
    components: {
      SignDialog,
      UploadImg,
    },
    data() {
      return {
        form: {},

        totalRecord: 0,
        pageCurrent: 1,
        pageSize: 10,

        list: [],
        countData: {},
        showResult: false,

        editRow: {},

        sortObj: {
          order: '',
          sortColumn: '',
        }
      }
    },
    mounted() {
      const beginDate = dayjs().format('YYYY-MM');
      const endDate = dayjs().format('YYYY-MM');
      this.$refs.datePicker.changeTime([beginDate, endDate]);
      this.search(true);
    },
    methods: {
      async search(init) {
        if (init === true) {
          this.pageCurrent = 1;
        }
        const [{ data: { data: list, totalRecord } }, { data: countData }] = await Promise.all([
          analyzeFreelance({
            ...this.form,
            ...this.sortObj,
            pageCurrent: this.pageCurrent,
            pageSize: this.pageSize,
          }),
          countFreelance({
            ...this.form
          })
        ]);
        this.list = list;
        this.totalRecord = totalRecord;
        this.countData = countData;
      },
      resetForm() {
        this.form = {};
        this.$refs.datePicker.clearTime()
      },
      uploadId(row) {
        this.editRow = row;
        this.$refs.uploadDialog.open()
      },
      startSign(row) {
        this.editRow = row;
        this.$refs.signDialog.form.receiveName = row.receiveName;
        this.$refs.signDialog.form.idCard = row.receiveIdCardNo;
        this.$refs.signDialog.visible = true;
        // if (row.phone == 0) {
        // } else {
        //   const form = {
        //     receiveName: row.receiveName,
        //     idCard: row.receiveIdCardNo
        //   }
        //   this.onSign(form)
        // }
      },
      async onSign(form) {
        const formData = {
          ...form,
          mainstayNo: this.editRow.mainstayNo,
          employerNo: this.editRow.employerNo,
        };
        const { data } = await sign(formData);
        data && (this.$message.success(data));
        this.search();
      },
      goOrderDetail(row, param) {
        this.$router.push({
          path: '/trade/order/detail',
          query: {
            createBeginDate: dayjs(row.createDate).subtract(1, 'month').startOf('month').format('YYYY-MM-DD') + ' 00:00:00',
            createEndDate: dayjs(row.createDate).endOf('month').format('YYYY-MM-DD') + ' 23:59:59',
            completeBeginDate: dayjs(row.createDate).startOf('month').format('YYYY-MM-DD') + ' 00:00:00',
            completeEndDate: dayjs(row.createDate).endOf('month').format('YYYY-MM-DD') + ' 23:59:59',
            orderItemStatusList: ['100'],
            ...param,
          }
        })
      },
      onSortChange({ prop, order }) {
        this.sortObj = {
          order: order.replace('ending', ''),
          sortColumn: prop
        };
        this.search()
      },
      async openIdCard(row) {
        let urls = [];
        if (row.idCardBackUrl && row.idCardFrontUrl) {
          urls = [
            row.idCardBackUrl,
            row.idCardFrontUrl,
          ];
        }
        if (row.cerFaceUrl) {
          urls.push(row.cerFaceUrl);
        }
        const formatUrls = []
        for (let i = 0; i < urls.length; i++) {
          const fileMsg = await this.formatFileMsg(urls[i])
          formatUrls.push(fileMsg.fileUrl)
        }
        this.$preview(formatUrls);
      },
      async openIdCopy(row) {
        let urls = [];
        if (row.idCardCopyUrl) {
          urls = [row.idCardCopyUrl];
        }
        const formatUrls = []
        for (let i = 0; i < urls.length; i++) {
          const fileMsg = await this.formatFileMsg(urls[i])
          formatUrls.push(fileMsg.fileUrl)
        }
        this.$preview(formatUrls);
      },
      openUploadDialog(row) {
        this.editRow = row
        this.$refs.uploadDialog.open()
      },
      onFilterChange(filters) {
        let keys = Object.keys(filters)
        keys.forEach(key => {
          this.form[key] = filters[key][0]
        })
        this.search()
      },

    }
  }
</script>

<style scoped lang="scss">
</style>
