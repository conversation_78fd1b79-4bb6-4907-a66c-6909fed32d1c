<template>
  <div class="box-container">
    <el-select
      v-model="mainstayNo"
      @change="search"
    >
      <el-option
        v-for="(item, index) in mainstayList"
        :key="index"
        :value="item.mainstayNo"
        :label="item.mainstayName"
      ></el-option>
    </el-select>

    <div class="content-container">
      <el-table :data="list">
        <el-table-column label="提醒账号">
          <template v-slot="{row}">
            {{ row.mainstayNo }} <br> {{ row.mainstayName}}
          </template>
        </el-table-column>
        <el-table-column label="提醒条件">
          <template v-slot="{row}">
            余额小于 {{ row.notifyAmount }}
          </template>
        </el-table-column>
        <el-table-column label="提醒时间段" width="150">
          <template v-slot="{row}">
            {{ row.notifyTimeStart | hourAndMin }} - {{ row.notifyTimeEnd | hourAndMin }}
          </template>
        </el-table-column>
        <el-table-column label="提醒方式">
          <template v-slot="{row}">
            {{ $dictCode('EmployerNotifyEnum', row.notifyType).desc }}
          </template>
        </el-table-column>
        <el-table-column label="接收人" width="250">
          <template v-slot="{row}">
            <div v-for="(item, index) in row.receiveAccount" :key="index">
              {{ item }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template v-slot="{row}">
            <el-tag :type="row.status ? 'success' : 'danger' ">
              {{ row.status ? '启用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template v-slot="{row}">
            <span class="func-content" @click="goNotify(row)">设置</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
  import { listNotifyConfig } from '@/api/merchant';
  import { getAllMainstayList } from '@/api/merchant';
  export default {
    name: 'BalanceNotifyManage',
    data() {
      return {
        list: [],
        mainstayNo: '',
        mainstayList: [],
      }
    },
    mounted() {
      this.mainstayNo = this.$route.query.mainstayNo;
      getAllMainstayList().then(res => this.mainstayList = res.data);
      this.search();
    },
    methods: {
      async search() {
        const { data } = await listNotifyConfig({
          mainstayNo: this.mainstayNo
        });
        this.list = data;
        this.list.forEach(item => {
          item.receiveAccount = JSON.parse(item.receiveAccount)
        })
      },
      goNotify(row) {
        this.$router.push({
          path: '/account/balance/notify',
          query: {
            mainstayNo: row.mainstayNo,
            mainstayName: row.mainstayName,
            action: 'EDIT',
          }
        })
      }
    },
    filters: {
      hourAndMin(val) {
        return val.replace(/(\w+):\d+$/, '$1')
      }
    }
  }
</script>

<style scoped lang="scss">

</style>
