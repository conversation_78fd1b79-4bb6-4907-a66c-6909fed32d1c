<template>
  <div style="padding-bottom: 64px;">
    <warning-block color="#000" backColor="#E6F7FF" borderColor="#91D5FF" iconColor="#1890FF">
      余额阈值指对代征主体账户余额的汇总计算，不区分发放方式。更多余额提醒设置请查看 <span class="func-content" @click="goNotifyManage">[余额提醒管理]</span>
    </warning-block>
    <div class="page-container">
      <el-form label-width="150px">
        <p class="subTitle">提醒设置</p>
        <el-form-item label="账户：">
          {{ form.mainstayName }} ({{form.mainstayNo}})
        </el-form-item>
        <el-form-item label="提醒条件：">
          余额小于
          <el-input
            placeholder="设置金额"
            style="width: 300px"
            size="small"
            :value="form.notifyAmount"
            @input="handleRateInput(form, 'notifyAmount', $event)"
          ></el-input>
        </el-form-item>
        <el-form-item label="提醒方式：">
          {{ $dictCode('EmployerNotifyEnum', form.notifyType).desc }}
        </el-form-item>
        <el-form-item label="提醒时间段：">
          自然日的
          <el-time-select
            :picker-options="timeOptions"
            placeholder="选择时间"
            size="small"
            v-model="form.notifyTimeStart"
          >

          </el-time-select> -
          <el-time-select
            :picker-options="timeOptions"
            placeholder="选择时间"
            size="small"
            v-model="form.notifyTimeEnd"
          ></el-time-select>
        </el-form-item>

        <el-form-item label="状态：">
          <el-switch
            active-text="启用"
            inactive-text="停用"
            v-model="form.status"
          ></el-switch>
        </el-form-item>

        <p class="subTitle">接收人信息</p>
        <el-form-item label="邮箱：">
          <div
            v-for="(item, index) in form.receiveAccount"
            :key="index"
          >
            <el-input
              v-model="form.receiveAccount[index]"
              style="width: 400px; margin-bottom: 16px;"
              ref="input"
              clearable
            ></el-input>
            <span class="func-content" v-if="index > 0" @click="deleteAccount(index)">删除</span>
          </div>

          <p style="margin-top: -10px" class="func-content" v-show="form.receiveAccount.length < 3" @click="addAccount">
            <i class="el-icon-plus"></i>
            添加更多邮箱
          </p>
        </el-form-item>

        <div class="form-btn-group">
          <el-button type="primary" @click="submit">提交</el-button>
          <el-button @click="back">返回</el-button>
        </div>

      </el-form>
    </div>
  </div>
</template>

<script>
  import { getSafeSetting } from "@/api/safeSetting"
  import { balanceNotifyUpdate, getConfig } from '@/api/merchant';
  export default {
    name: 'BalanceNotify',
    data() {
      return {
        form: {
          mainstayNo: '',
          mainstayName: '',
          notifyType: 1,
          notifyAmount: '',
          notifyTimeStart: '',
          notifyTimeEnd: '',
          notifyTimes: 1,
          receiveAccount: [''],
          status: false,
        },
        rules: {

        },
        timeOptions: {
          start: '00:00',
          end: '23:00',
          step: '00:30'
        },
      }
    },
    computed: {
      mainstayNo() {
        return this.$route.query.mainstayNo || ''
      },
      mainstayName() {
        return this.$route.query.mainstayName || ''
      },
      action() {
        return this.$route.query.action || ''
      },
    },
    async mounted() {
      this.form.mainstayNo = this.mainstayNo;
      this.form.mainstayName = this.mainstayName;
      if (this.action === 'ADD') {
        await getSafeSetting().then(({data}) => {
          this.form.receiveAccount.splice(0, 1, data.email)
        });
      }
      const { data } = await getConfig({
        mainstayNo: this.mainstayNo
      });
      if (data) {
        for (let p in this.form) {
          if (data.hasOwnProperty(p)) {
            this.form[p] = data[p]
          }
        }
        this.form.notifyTimeStart = this.form.notifyTimeStart.replace(/(\w+):\d+$/, '$1');
        this.form.notifyTimeEnd = this.form.notifyTimeEnd.replace(/(\w+):\d+$/, '$1');
        this.form.receiveAccount = JSON.parse(this.form.receiveAccount);
      }
      if (this.form.receiveAccount.length === 0) {
        this.form.receiveAccount.push('')
      }
    },
    methods: {
      goNotifyManage() {
        this.$router.push({
          path: '/account/balance/notifyManage',
          query: {
            mainstayNo: this.mainstayNo,
          }
        })
      },
      async addAccount() {
        this.form.receiveAccount.push('');
        await this.$nextTick();
        let active = this.$refs.input[this.form.receiveAccount.length - 1];
        let scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
        let top = active.$el.getBoundingClientRect().top + scrollTop;
        window.scrollTo(0, top)
      },
      deleteAccount(index) {
        this.form.receiveAccount.splice(index, 1)
      },
      async submit() {
        let start = this.form.notifyTimeStart;
        let end = this.form.notifyTimeEnd;
        if (start > end) {
          return this.$message.error('请选择正确的时间范围')
        }
        const { data } = await balanceNotifyUpdate({
          ...this.form,
          notifyTimeStart: this.form.notifyTimeStart + ":00",
          notifyTimeEnd: this.form.notifyTimeEnd + ":00",
        });
        data && this.$message.success(data);
        this.goNotifyManage()
      },
      back() {
        this.$router.back();
      }
    },
  }
</script>

<style scoped lang="scss">

</style>
