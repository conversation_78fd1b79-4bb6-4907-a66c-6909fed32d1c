<template>
  <div class="page-container">
    <el-form
      :model="form"
      label-width="150px"
      :rules="rules"
      ref="form">
      <el-form-item label="商户名称：">
        {{ userData.mchName }}
      </el-form-item>
      <el-form-item
        label="代征主体："
        prop="mainstayNo">
        <el-select
          v-model="form.mainstayNo"
          @change="handleMainstayChange">
          <el-option
            v-for="(item, index) in mainstayList"
            :key="index"
            :label="item.mainstayName"
            :value="item.mainstayNo"
            :disabled="item.status == 101"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="账户类型：">
        <el-radio-group
          v-model="form.channelType"
          @change="handleChannelTypeChange">
          <el-radio
            v-for="(item, index) in employerChannels"
            :key="index"
            :label="item.channelType">
            {{ $dictCode('ChannelTypeEnum', item.channelType).desc }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="可用余额："><span class="color-warning">{{ amount }}</span> 元</el-form-item>
      <el-form-item
        label="充值方式："
        v-if="isALIPAY">
        <el-radio-group
          v-model="form.type"
          @change="handleRechargeTypeChange">
          <el-radio
            v-for="(item, index) in $dict('AlipayIncomeTypeEnum')"
            :key="index"
            :label="Number(item.code)"
          >
            {{ item.desc }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="充值金额："
        prop="rechargeAmount"
        v-show="isALIPAY && form.type === 100">
        <el-input
          style="width: 300px;"
          :value="form.rechargeAmount"
          @input="handleRateInput(form, 'rechargeAmount', $event)"
        ></el-input>
        <p class="form-tip color-gray">
          本次充值最低限额为 <span class="color-warning">0.01</span> 元
        </p>
      </el-form-item>
      <el-form-item label="充值流程：">
        <p v-if="isALIPAY && form.type === 100">
          你需登录本公司的企业支付宝账户，汇款 {{ form.rechargeAmount | moneyFormat('') }} 元至记账本账户，汇款成功并经审核通过后到账<br>
          <span class="color-red">请在新打开的支付宝页面完成充值，充值完成前请不要关闭此窗口</span>
        </p>
        <p v-else>
          你需使用本公司同名的银行账户进行转账充值<br>
          收款行选择：<span class="color-red">{{ bankInfo && bankInfo.bankName }}</span>，收款账户支行选择：<span class="color-red">{{ bankInfo && bankInfo.subBankName }}</span>，否则无法收到资金。
          <span class="color-red">（重要！{{ channelTypeBankMap[form.payChannelNo] }}是唯一收款网点，请务必填对，选择错误的开户行/联行号将导致错汇的问题）</span>
        </p>
      </el-form-item>
      <el-form-item v-if="isALIPAY && form.type === 100">
        <el-button
          type="primary"
          :loading="loading"
          @click="submit">
          登录到支付宝充值
        </el-button>
      </el-form-item>
    </el-form>

    <el-form
      label-width="150px"
      :model="bankInfo"
      ref="bankInfo">
      <el-form-item v-if="bankInfo && bank_visible">
        <div class="bank-info-container">
          <div
            class="bank-info-item"
            v-for="(val, key) in bankTitleMap"
            :key="key">
            <template v-if="val !== 'joinBankNo' || bankInfo.payChannelNo !== 'CMB'">
              <div class="bank-info bank-info-title">{{ key }}</div>
              <div class="bank-info bank-info-detail">
                {{ bankInfo[val] }}
              </div>
              <div class="bank-info-operate">
                <span
                  class="func-content"
                  v-clipboard="bankInfo[val]">复制</span>
              </div>
            </template>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <div class="form-tip-block">
      常见问题：
      <div>
        1、什么是转账充值？
      </div>
      <div>
        商户可通过转账方式完成对应通道账户的充值。平台会为商户的每个通道类型提供独立的「专属账户/记账本」，商户从任一同名银行账户/支付宝账户向其对应的「专属账户/记账本」转账，即可完成充值。
      </div>
      <div>
        2、使用银行卡通道的转账充值时，选不到支行选项怎么办？
      </div>
      <div>
        若您在银行转账页面无法找到收款行，您可以搜索关键词“支付”尝试，先找到“支付机构备付金集中存管账户”或“支付机构”，再通过网点找到对应支行账户。
      </div>
      <div>
        3、转账充值的到账时间？
      </div>
      <div>
        转账完成后，工作时间最晚2小时到账，非工作时间预计最晚下个工作日到账（具体以银行为准）。
      </div>
      <div>
        4、转账充值成功后如何查询？
      </div>
      <div>
        充值成功后，商户可以在账户余额、充值记录中查看。
      </div>
    </div>


    <el-dialog
      :visible.sync="alipay_visible"
      title="登录支付宝充值"
      :before-close="() => { alipay_visible = false }"
      :close-on-click-modal="false"
      :show-close="false"
    >
      请在新打开的支付宝页面完成充值，充值完成前请不要关闭此窗口。
      <template slot="footer">
        <el-button
          type="primary"
          @click="goRecord">查看充值记录
        </el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import { getBankChannelInfo, recharge } from '@/api/recharge';
import { getMerchantEmployer, listAllMainstayList } from '@/api/merchant';
import { getAmount } from '@/api/order';
import { toPromise } from "@/utils";

export default {
  name: 'RechargePage',
  data() {
    return {
      form: {
        mainstayNo: '',
        mainstayName: '',
        channelType: '',
        rechargeAmount: '',
        payChannelNo: '',
        rechargeType: '1',
        type: 100
      },
      mainstayList: [],

      loading: false,

      amount: '',

      rules: {
        rechargeAmount: [
          { required: true, message: '请输入充值金额', trigger: 'blur' },
          {
            validator: (rule, value, cb) => {
              if (value >= 0.01) {
                cb()
              } else {
                cb('本次充值最低限额为 0.01 元')
              }
            }, trigger: 'blur'
          }
        ],
        mainstayNo: [
          { required: true, message: '请选择代征主体', trigger: 'change' }
        ]
      },

      employerChannels: [], // 根据供应商编号查出的可用通道
      showPay: false,

      alipay_visible: false,
      bank_visible: false,

      bankTitleMap: {
        '户名：': 'accountName',
        '账号：': 'accountNo',
        '银行：': 'bankName',
        '支行：': 'subBankName',
        '开户地：': 'bankAddress',
        '联行号：': 'joinBankNo',
      },
      bankInfo: {},

      // 支付通道对应银行
      channelTypeBankMap: {
        JOINPAY: '‘广州汇聚支付-备付金账户’',
        CMB: '‘代征主体-招商银行账户’',
        WXPAY: '‘财付通一备付金账户’',
        ALIPAY: '‘支付宝一备付金账户’'
      },
      rawChannelList: {}
    }
  },
  computed: {
    activeChannel() {
      return this.rawChannelList[this.form.channelType]
    },
    isALIPAY() {
      return this.activeChannel && this.activeChannel.payChannelNo === 'ALIPAY'
    },
  },
  mounted() {
    listAllMainstayList().then(res => this.mainstayList = res.data)
  },
  methods: {
    async handleMainstayChange(val) {
      if (!val) {
        return this.form.mainstayName = '';
      }
      for (let i = 0; i < this.mainstayList.length; i++) {
        if (val == this.mainstayList[i].mainstayNo) {
          this.form.mainstayName = this.mainstayList[i].mainstayName;
          break;
        }
      }
      const { data } = await getMerchantEmployer(null, {
        mainstayNo: val,
        employerNo: this.userData.mchNo
      });
      this.employerChannels = [];
      if (data.channelList) {
        this.rawChannelList = data.channelList
        for (let i in data.channelList) {
          if (data.channelList[i].status == 100) {
            this.employerChannels.push(data.channelList[i])
          }
        }
      }
      this.bank_visible = false;
      this.form.channelType = '';
      this.form.payChannelNo = '';
      this.form.type = 100;
      this.amount = '';
      this.checkBalance();
    },
    async checkBalance() {
      if (this.form.channelType === '' && this.employerChannels[0]) {
        this.form.channelType = this.employerChannels[0].channelType;
        this.form.payChannelNo = this.employerChannels[0].payChannelNo;
      }
      this.loading = true;
      const { data } = await getAmount({
        mainstayNo: this.form.mainstayNo,
        payChannelNo: this.form.payChannelNo,
        channelType: this.form.channelType,
      }).finally(() => {
        this.loading = false;
      });
      this.amount = data.amount;
      if (this.form.payChannelNo !== 'ALIPAY') {
        this.getBankInfo()
      }
    },
    async submit() {
      const [err] = await toPromise(this.$refs.form.validate())
      if (err) return
      this.getResult()
    },
    async getBankInfo(val) {
      val = val || this.form.type
      const form = {
        mainstayNo: this.form.mainstayNo,
        channelNo: this.form.payChannelNo,
        channelType: this.form.channelType,
      }
      if (this.form.type === 101) {
        form.type = val
      }
      const { data: bankInfo } = await getBankChannelInfo(form).finally(() => {
        this.loading = false
      });
      this.bankInfo = bankInfo;
      this.bank_visible = val === this.form.type;
      this.showPay = true
    },
    async getResult() {
      this.loading = true;
      const { data } = await recharge(this.form).finally(() => {
        this.loading = false
      });
      if (this.form.payChannelNo == 'ALIPAY') {
        // 支付宝
        data && (window.open(data));
        this.alipay_visible = true;
      } else if (data == 'success') {
        // 银行卡
        // const { data: bankInfo } = await getBankChannelInfo({
        //   mainstayNo: this.form.mainstayNo,
        //   channelNo: this.form.payChannelNo
        // }).finally(() => { this.loading = false });
        // this.bankInfo = bankInfo;
        // this.bank_visible = true;
      }
    },
    async handleChannelTypeChange(val) {
      if (val) {
        this.form.payChannelNo = this.employerChannels.filter(channel => channel.channelType == val)[0]['payChannelNo'];
        await this.checkBalance();
      } else {
        this.employerChannels = [];
      }
      this.$nextTick(() => {
        this.bank_visible = !this.isALIPAY
        if (this.isALIPAY) {
          this.handleRechargeTypeChange(this.form.type)
        }
      })
    },
    handleRechargeTypeChange(val) {
      this.bank_visible = false
      this.form.rechargeAmount = null
      if (val === 101) {
        this.getBankInfo(val)
      }
    },
    goRecord() {
      this.alipay_visible = false;
      this.$router.push('/account/chargeRecord')
    },
  },
}
</script>

<style
  scoped
  lang="scss">
.color-red {
  color: #f00;
}

.form-tip-block {
  margin: 64px;
  padding: 32px;
  color: #aaa;
  background: rgb(240, 245, 242);

  div {
    line-height: 1.8;
  }
}

.bank-info-container {
  border: 1px solid $borderColor;
}

.bank-info-item {
  display: flex;
  width: 100%;
}

.bank-info {
  flex-grow: 1;
  text-align: center;
}

.bank-info-title {
  flex-grow: 0;
  width: 100px;
  text-align: right;
}

.bank-info-operate {
  width: 240px;
  text-align: center;
}
</style>
