<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">订单号：</span>
          <el-input
            clearable
            v-model="searchForm.rechargeOrderId"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">代征主体：</span>
          <el-select
            clearable
            v-model="searchForm.mainstayNo"
          >
            <el-option
              v-for="(item, index) in mainstayList"
              :key="index"
              :label="item.mainstayName"
              :value="item.mainstayNo"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">订单状态：</span>
          <el-select
            clearable
            v-model="searchForm.rechargeStatus"
          >
            <el-option
              v-for="(item, index) in $dict('RechargeStatusEnum')"
              :key="index"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">账户类型：</span>
          <el-select
            clearable
            v-model="searchForm.channelType"
          >
            <el-option
              v-for="(item, index) in $dict('ChannelTypeEnum')"
              :key="index"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            ref="datepicker"
            type="datetime"
            picker="separate"
            :start-time.sync="searchForm.createBeginDate"
            :end-time.sync="searchForm.createEndDate"
          ></date-picker>
        </div>
      </div>

      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)">查询
        </el-button>
        <el-button @click="exportMultiList">批量下载</el-button>
        <el-button @click="exportList">导出</el-button>
        <el-button
          type="text"
          @click="getExportList">查看已导出列表
        </el-button>
        <el-button
          type="text"
          @click="resetForm">清空筛选条件
        </el-button>
      </div>
    </div>

    <div class="content-container">
      <el-table :data="list">
        <el-table-column
          label="订单号/通道单号"
          prop="rechargeOrderId"
          width="120">
          <template v-slot="{row}">
            {{ row.rechargeOrderId }}<br>
            {{ row.channelTrxNo }}
          </template>
        </el-table-column>

        <el-table-column
          label="创建时间/支付时间"
          prop="createTime"
          width="180">
          <template v-slot="{row}">
            {{ row.createTime }}<br>
            {{ row.transPayTime }}
          </template>
        </el-table-column>

        <el-table-column
          label="账户类型(支付通道)"
          prop="channelType">
          <template v-slot="{row}">
            {{ $dictCode('ChannelTypeEnum', row.channelType).desc }}
            ({{ row.channelName }})
          </template>
        </el-table-column>

        <el-table-column
          label="收款方名称"
          width="120">
          <template v-slot="{row}">
            {{ row.payeeName }}
          </template>
        </el-table-column>
        <el-table-column
          label="收款方账号"
          width="120">
          <template v-slot="{row}">
            {{ row.payeeIdentity }}<br>
          </template>
        </el-table-column>

        <el-table-column
          label="付款方名称"
          width="120">
          <template v-slot="{row}">
            {{ row.payerName }}
          </template>
        </el-table-column>

        <el-table-column
          label="付款方账号"
          width="120">
          <template v-slot="{row}">
            {{ row.payerBankName }}<br>
            {{ row.payerIdentity }}
          </template>
        </el-table-column>

        <el-table-column label="代征主体">
          <template v-slot="{row}">
            {{ row.mainstayNo }}<br>{{ row.mainstayName }}
          </template>
        </el-table-column>

        <el-table-column
          width="130"
          label="充值金额"
          prop="rechargeAmount">
          <template v-slot="{row}">
            <p class="text-right">{{ row.rechargeAmount | moneyFormat }}</p>
          </template>
        </el-table-column>

        <el-table-column label="充值类型">
          <template v-slot="{row}">
            {{ $dictCode('RechargeTypeEnum', row.rechargeType).desc }}
          </template>
        </el-table-column>

        <el-table-column label="订单状态">
          <template v-slot="{row}">
            <el-tag :type="getStatusType(row.rechargeStatus)">
              {{ $dictCode('RechargeStatusEnum', row.rechargeStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="电子回单">
          <template v-slot="{row}">
            <span
              class="func-content"
              v-if="row.receiptUrl"
              @click="checkReceiptUrl(row.receiptUrl)">查看回单</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        v-if="list"
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>

    <export-record ref="exportRecord"></export-record>
  </div>
</template>

<script>
import { getAllMainstayList } from '@/api/merchant';
import { getMultiRecordList, getRechargeRecord, getRecordList } from '@/api/recharge';
import ExportRecord from '@/components/ExportRecord';

export default {
  name: "ChargeRecord",
  components: {
    ExportRecord
  },
  data() {
    return {
      searchForm: {
        rechargeOrderId: '',
        employerNo: '',
        employerNameLike: '',
        mainstayNo: '',
        channelType: '',
        rechargeStatus: '',
        createBeginDate: '',
        createEndDate: '',
      },

      mainstayList: [],

      list: [],

      totalRecord: 0,
      pageSize: 10,
      pageCurrent: 1,
    }
  },
  mounted() {
    getAllMainstayList().then(res => {
      this.mainstayList = res.data
    })
    this.searchForm.employerNo = this.userData.mchNo;
    this.search()
  },
  methods: {
    async search(init) {
      if (init) {
        this.pageCurrent = 1;
      }
      const { data } = await getRechargeRecord({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent
      })
      this.list = data.data;
      this.totalRecord = data.totalRecord
    },
    resetForm() {
      this.searchForm = {
        rechargeOrderId: '',
        employerNo: this.userData.mchNo,
        employerNameLike: '',
        mainstayNo: '',
        channelType: '',
        rechargeStatus: '',
      };
      this.$refs.datepicker.clearTime()
    },
    getStatusType(status) {
      switch (Number(status)) {
        case 0:
          return 'primary';
        case 1:
          return 'success';
        case 2:
          return 'danger';
        default:
          return ''
      }
    },
    async exportList() {
      await getRecordList(this.searchForm);
      this.$message.success('操作成功')
    },
    async exportMultiList() {
      await getMultiRecordList(this.searchForm);
      this.$message.success('操作成功')
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('23')
    },
    async checkReceiptUrl(url) {
      const fileMsg = await this.formatFileMsg(url)
      if (this.isPdf(url)) {
        window.open(fileMsg.fileUrl)
      } else {
        this.$preview(fileMsg.fileUrl)
      }
    },
  },
}
</script>

<style
  scoped
  lang="scss">

</style>
