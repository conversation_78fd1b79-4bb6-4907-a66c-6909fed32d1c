<template>
  <div class="page-container">
    <el-form label-width="150px" :model="form" ref="form">
      <el-form-item label="商户名称：">
        {{ userData.mchName }}
      </el-form-item>
      <el-form-item label="账户类型：">
        代征主体通道 | {{ $dictCode('ChannelTypeEnum', form.channelType).desc }}
      </el-form-item>
      <el-form-item label="充值金额：">
        {{ form.rechargeAmount }} 元
      </el-form-item>
      <!--<el-form-item label="充值方式：">-->
        <!--<el-radio-group v-model="form.rechargeType">-->
          <!--<el-radio-->
            <!--v-for="item in $dict('RechargeTypeEnum')"-->
            <!--:key="item.code"-->
            <!--:label="item.code"-->
            <!--:disabled="item.code != 1"-->
          <!--&gt;-->
            <!--{{ item.desc }}-->
          <!--</el-radio>-->
        <!--</el-radio-group>-->
        <!--<p>本次充值最低限额为 <span class="color-red">0.01</span> 元</p>-->
      <!--</el-form-item>-->

      <el-form-item label="充值流程：">
        <p v-if="form.payChannelNo == 'JOINPAY'">
          你需使用本公司同名的银行账户进行转账充值，汇款 {{ form.rechargeAmount }} 元至以下账户，汇款成功并经审核通过后到账<br>
          收款行选择：<span class="color-red">{{ bankInfo && bankInfo.bankName }}</span>，收款账户支行选择：<span class="color-red">{{ bankInfo && bankInfo.subBankName }}</span>，否则无法收到资金。
          <span class="color-red">（重要！‘广州汇聚支付-备付金账户’是唯一收款网点，请务必填对，选择错误的开户行/联行号将导致错汇的问题）</span>
        </p>
        <p v-if="form.payChannelNo == 'ALIPAY'">
          你需登录本公司的企业支付宝账户，汇款 {{form.rechargeAmount}} 元至记账本账户，汇款成功并经审核通过后到账<br>
          <span class="color-red">请在新打开的支付宝页面完成充值，充值完成前请不要关闭此窗口</span>
        </p>
      </el-form-item>

      <el-form-item v-if="bankInfo && bank_visible">
        <div class="bank-info-container">
          <div class="bank-info-item" v-for="(val, key) in bankTitleMap" :key="key">
            <div class="bank-info bank-info-title">{{ key }}</div>
            <div class="bank-info bank-info-detail">
              {{ bankInfo[val] }}
            </div>
            <div class="bank-info-operate">
              <span class="func-content" v-clipboard="bankInfo[val]">复制</span>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <div class="form-tip-block">
      常见问题：
      <div>
        1、什么是转账充值？
      </div>
      <div>
        商户可通过转账方式完成对应通道账户的充值。平台会为商户的每个通道类型提供独立的「专属账户/记账本」，商户从任一同名银行账户/支付宝账户向其对应的「专属账户/记账本」转账，即可完成充值。
      </div>
      <div>
        2、使用银行卡通道的转账充值时，选不到支行选项怎么办？
      </div>
      <div>
        若您在银行转账页面无法找到收款行，您可以搜索关键词“支付”尝试，先找到“支付机构备付金集中存管账户”或“支付机构”，再通过网点找到对应支行账户。
      </div>
      <div>
        3、转账充值的到账时间？
      </div>
      <div>
        转账完成后，工作时间最晚2小时到账，非工作时间预计最晚下个工作日到账（具体以银行为准）。
      </div>
      <div>
        4、转账充值成功后如何查询？
      </div>
      <div>
        充值成功后，商户可以在账户余额、充值记录中查看。
      </div>
    </div>


    <el-dialog
      :visible.sync="alipay_visible"
      title="登录支付宝充值"
      :before-close="() => { alipay_visible = false }"
      :close-on-click-modal="false"
      :show-close="false"
    >
      请在新打开的支付宝页面完成充值，充值完成前请不要关闭此窗口。
      <template slot="footer">
        <el-button type="primary" @click="goRecord">查看充值记录</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script>
  import { recharge, getBankChannelInfo } from '@/api/recharge';

  export default {
    name: 'Cashier',
    data() {
      return {
        form: {},
        loading: false,

        alipay_visible: false,
        bank_visible: false,

        bankTitleMap: {
          '户名：': 'accountName',
          '账号：': 'accountNo',
          '银行：': 'bankName',
          '支行：': 'subBankName',
          '开户地：': 'bankAddress',
          '联行号：': 'joinBankNo',
        },

        bankInfo: {},
      }
    },
    mounted() {
      this.form = JSON.parse(sessionStorage.getItem('rechargeForm'));
      this.$set(this.form, 'rechargeType', '1');
      this.submit()
    },
    beforeDestroy() {
      sessionStorage.removeItem('rechargeForm')
    },
    methods: {
      goRecord() {
        this.alipay_visible = false;
        this.$router.push('/account/chargeRecord')
      },
      async submit() {
        this.loading = true;
        const { data } = await recharge(this.form).finally(() => { this.loading = false });
        if (this.form.payChannelNo == 'ALIPAY') {
          // 支付宝
          data && (window.open(data));
          this.alipay_visible = true;
        } else if (data == 'success') {
          // 银行卡
          const { data: bankInfo } = await getBankChannelInfo({
            mainstayNo: this.form.mainstayNo,
            channelNo: this.form.payChannelNo
          }).finally(() => { this.loading = false });
          this.bankInfo = bankInfo;
          this.bank_visible = true;
        }
      },
    },
  }
</script>

<style scoped lang="scss">

  .page-container {
    .color-red {
      color: #f00;
    }

    .form-tip-block {
      margin: 64px;
      padding: 32px ;
      color: #aaa;
      background: rgb(240, 245, 242);

      div {
        line-height: 1.8;
      }
    }

    .bank-info-container {
      border: 1px solid $borderColor;
    }
    .bank-info-item {
      display: flex;
      width: 100%;
    }
    .bank-info {
      flex-grow: 1;
      text-align: center;
    }
    .bank-info-title {
      flex-grow: 0;
      width: 100px;
      text-align: right;
    }

    .bank-info-operate {
      width: 240px;
      text-align: center;
    }

  }

</style>
