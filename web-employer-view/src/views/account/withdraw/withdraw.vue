<template>
  <div class="page-container">
    <el-form
      :model="form"
      label-width="150px"
      :rules="rules"
      ref="form"
    >
      <el-form-item label="商户名：">{{ userData.mchName }}</el-form-item>

      <el-form-item
        label="代征主体："
        prop="mainstayNo"
      >
        <el-select
          v-model="form.mainstayNo"
          clearable
          @change="handleMainstayChange"
        >
          <el-option
            v-for="(item, index) in mainstayList"
            :key="index"
            :label="item.mainstayName"
            :value="item.mainstayNo"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        label="账户类型："
        prop="channelType"
      >
        <template v-if="form.mainstayNo">
          <el-radio-group
            v-model="selectChannel"
            @change="handleChannelChange"
          >
            <template v-for="(item, index) in channelMap">
              <el-radio
                v-if="item.payChannelNo && item.payChannelNo !== 'WXPAY'"
                :key="index"
                :label="item"
              >
                {{ $dictCode('ChannelTypeEnum', item.channelType).desc }}
              </el-radio>
            </template>
          </el-radio-group>
        </template>
      </el-form-item>

      <el-form-item label="可用余额：">
        <span class="color-warning">
          <!--{{ mainstayMoneyMap[form.mainstayNo] ? mainstayMoneyMap[form.mainstayNo][form.channelNo] : '' }}-->
          {{ balanceAmount }}
        </span> 元
      </el-form-item>

      <el-form-item
        label="提现金额："
        prop="amount"
      >
        <el-input
          style="width: 300px"
          :value="form.amount"
          @input="handleRateInput(form, 'amount', $event)"
          autocomplete="off"
          clearable
        >
          <span slot="append">元</span>
        </el-input>
        <span
          class="func-content"
          @click="withdrawAll"
        >全部提现</span>
        <p class="color-gray form-tip">本次提现最低限额为 <span class="color-warning">0.1</span> 元</p>
        <p
          class="form-tip"
          v-show="moneyLack"
        >可提现金额不足，本次无法提现</p>
      </el-form-item>

      <el-form-item label="到账账户：">
        <div class="account-type-item">
          <div class="account-type-name">
            <template v-if="form.channelNo === 'JOINPAY' || form.channelNo === 'JOINPAY_JXH' || form.channelNo === 'CMB'">
              <svg-icon
                iconClass="payicon_withdraw"
                :iconStyle="{fontSize: '20px'}"
              >
              </svg-icon>
              <div class="account-name-detail account-name">银行卡</div>
              <div class="account-name-detail color-gray">{{ bankAccount.bankName }}</div>
              <div class="account-name-detail color-gray">{{ bankAccount.accountNo }}</div>
            </template>
            <template v-else-if="form.channelNo === 'ALIPAY'">
              <svg-icon
                iconClass="alipay"
                :iconStyle="{fontSize: '20px'}"
              >
              </svg-icon>
              <div class="account-name-detail account-name">支付宝</div>
              <div class="account-name-detail color-gray">{{ userData.mchName }}</div>
              <div class="account-name-detail color-gray">****</div>
            </template>
          </div>
        </div>
      </el-form-item>

      <el-form-item
        label="支付密码："
        prop="payPasswd"
      >
        <el-input
          type="password"
          autocomplete="new-password"
          name="payPasswd"
          style="width: 300px"
          v-model="form.payPasswd"
        ></el-input>
      </el-form-item>

      <el-form-item label="备注：">
        <el-input
          type="textarea"
          :rows="4"
          v-model="form.remark"
        ></el-input>
      </el-form-item>

      <el-form-item>
        <el-button
          type="primary"
          @click="withdraw"
          :loading="loading"
        >提现
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getBankAccount, getMainstayList, getMerchantEmployer } from '@/api/merchant'
import { encryptParam } from '@/utils/jsencrypt';
import { getKey } from '@/utils/publicKey'
import { getAmount, withdraw } from '@/api/order';


export default {
  name: 'WithdrawPage',
  data() {
    return {
      form: {
        employerNo: '',
        mainstayNo: '',
        merchantType: 100,
        // 支付宝通道
        channelType: '',
        channelNo: '',

        amount: '',
        remark: '',
        payPasswd: '',
      },

      rules: {
        mainstayNo: [{ required: true, trigger: 'change', message: '请选择代征主体' }],
        channelType: [{ required: true, trigger: 'blur', message: '请选择账户类型' }],
        amount: [{ required: true, trigger: 'blur', message: '请输入提现金额' }],
        payPasswd: [{ required: true, trigger: 'blur', message: '请输入支付密码' }],
      },

      mainstayList: [],
      mainstayMoneyMap: {},
      channelMap: {
        1: {},
        2: {},
        3: {},
      }, // 商户在代征主体下通道账户信息

      moneyLack: false,
      loading: false,

      selectChannel: {},
      balanceAmount: '',

      bankAccount: {}, // 银行卡信息
    }
  },
  async mounted() {
    this.form.employerNo = this.userData.mchNo;
    await this.getMainstayList();
  },
  methods: {
    async getMainstayList() {
      const { data } = await getMainstayList();
      this.mainstayList = data;
      this.mainstayList.forEach(item => {
        this.$set(this.mainstayMoneyMap, item.mainstayNo, {})
      })
    },
    async getAmount() {
      // let promise = [];
      // for (let i = 0; i < this.mainstayList.length; i++) {
      //   promise.push(getAmount({
      //     mainstayNo: this.mainstayList[i].mainstayNo
      //   }))
      // };
      // const response = await Promise.all(promise);
      // response.forEach((item, index) => {
      //   this.mainstayMoneyMap[this.mainstayList[index].mainstayNo] = item.data;
      // })
      const { data } = await getAmount({
        mainstayNo: this.form.mainstayNo,
        channelType: this.form.channelType,
        payChannelNo: this.form.channelNo,
      });
      this.balanceAmount = data.amount || 0;
    },
    withdrawAll() {
      if (isNaN(this.balanceAmount)) {
        // 通道异常等
      } else {
        this.form.amount = this.balanceAmount;
      }
    },
    async handleMainstayChange(mainstayNo) {
      this.form.channelType = '';
      this.form.channelNo = '';
      this.balanceAmount = ''
      if (!mainstayNo) {
        // 修改到账账户
        this.form.amount = '';
        return;
      }
      try {
        const { data } = await getMerchantEmployer(null, {
          mainstayNo,
          employerNo: this.form.employerNo
        });
        this.channelMap = data.channelList
        this.getBankAccount()
      } catch {
        this.channelMap = {
          1: {},
          2: {},
          3: {},
        }
      }
    },
    async getBankAccount() {
      const { data } = await getBankAccount();
      this.bankAccount = data
    },
    async withdraw() {
      const valid = await this.$refs.form.validate().catch(_ => false);
      if (!valid) return;
      this.loading = true;
      let pwd = this.form.payPasswd;
      const { data } = await withdraw({
        ...this.form,
        payPasswd: encryptParam({
          pwd,
          publicKey: getKey(),
        })
      }).finally(() => {
        this.loading = false;
      });
      this.$message.success('操作成功');
      this.$router.push({
        path: '/account/withdrawRecord',
        query: {
          withdrawNo: data.withdrawNo
        }
      })
    },
    handleChannelChange({ channelType, payChannelNo }) {
      this.form.channelType = channelType;
      this.form.channelNo = payChannelNo;
      this.getAmount()
    }
  },
}
</script>

<style
  scoped
  lang="scss">
.page-container {
  .account-type-item {
    display: inline-block;
    width: 250px;
    padding: 8px;
    border: 1px solid $borderGray;
  }

  .account-type-name {
    position: relative;
    padding-left: 24px;

    .account-name-detail {
      margin-bottom: 8px;
      line-height: normal;
      font-size: 12px;
    }

    .account-name {
      font-size: 15px;
    }

    .svg-icon {
      position: absolute;
      top: 0;
      left: 0;
    }
  }
}
</style>
