<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">订单号：</span>
          <el-input
            clearable
            v-model="searchForm.withdrawNo"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">代征主体：</span>
          <el-select
            clearable
            v-model="searchForm.mainstayNo"
          >
            <el-option
              v-for="(item, index) in mainstayList"
              :key="index"
              :label="item.mainstayName"
              :value="item.mainstayNo"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">订单状态：</span>
          <el-select
            v-model="searchForm.withdrawStatus"
            clearable
          >
            <el-option
              v-for="item in $dict('WithdrawStatusEnum')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            ref="datepicker"
            type="datetime"
            picker="separate"
            :start-time.sync="searchForm.createBeginDate"
            :end-time.sync="searchForm.createEndDate"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button type="primary" @click="search(true)">查询</el-button>
        <el-button @click="exportList">导出</el-button>
        <el-button type="text" @click="getExportList">查看已导出列表</el-button>
        <el-button type="text" @click="resetForm">清空筛选条件</el-button>
      </div>
    </div>

    <div class="content-container">
      <el-table :data="list">
        <el-table-column label="订单号" prop="withdrawNo" width="200"></el-table-column>

        <el-table-column label="创建时间" prop="createTime">
          <template v-slot="{row, column}">
            <p v-html="renderTime(row[column['property']])"></p>
          </template>
        </el-table-column>
        <el-table-column label="完成时间" prop="updateTime">
          <template v-slot="{row, column}">
            <p v-html="renderTime(row[column['property']])"></p>
          </template>
        </el-table-column>

        <el-table-column label="账户类型" prop="channelType">
          <template v-slot="{row}">
            {{ $dictCode('ChannelTypeEnum', row.channelType).desc}}({{ row.channelName }})
          </template>
        </el-table-column>

        <el-table-column label="代征主体" prop="mainstayNo">
          <template v-slot="{row}">
            {{ row.mainstayNo }}<br>{{ row.mainstayName }}
          </template>
        </el-table-column>

        <el-table-column label="提现金额" prop="amount">
          <template v-slot="{row}">
            <p class="fr">{{row.amount}}</p>
          </template>
        </el-table-column>

        <el-table-column label="备注" prop="remark"></el-table-column>
        <el-table-column label="错误信息" prop="errorMsg" width="180"></el-table-column>

        <el-table-column label="订单状态" prop="withdrawStatus" fixed="right">
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.withdrawStatus)">
              {{ $dictCode('WithdrawStatusEnum', row.withdrawStatus).desc}}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        v-if="list"
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>

    <export-record ref="exportRecord"></export-record>

  </div>
</template>

<script>
  import { getAllMainstayList } from '@/api/merchant';
  import { getWithdrawRecord, exportWithdrawRecord } from '@/api/order';
  import ExportRecord from '@/components/ExportRecord';

  export default {
    name: 'WithdrawRecord',
    components: {
      ExportRecord
    },
    data() {
      return {
        searchForm: {
          withdrawNo: '',
          withdrawStatus: '',
          mainstayNo: '',
          createBeginDate: '',
          createEndDate: '',
        },

        list: [],


        mainstayList: [],

        totalRecord: 0,
        pageSize: 10,
        pageCurrent: 1,
      }
    },
    mounted() {
      getAllMainstayList().then(res => { this.mainstayList = res.data });
      this.searchForm.withdrawNo = this.$route.query.withdrawNo || '';
      this.search();
    },
    methods: {
      async search(init) {
        if (init) {
          this.pageCurrent = 1;
        }
        const { data } = await getWithdrawRecord({
          ...this.searchForm,
          pageCurrent: this.pageCurrent,
          pageSize: this.pageSize
        });
        this.list = data.data;
        this.totalRecord = data.totalRecord;
      },
      resetForm() {
        this.searchForm = {
          withdrawNo: '',
          withdrawStatus: '',
          mainstayNo: '',
          createBeginDate: '',
          createEndDate: '',
        };
        this.$refs.datepicker.clearTime()
      },
      async exportList() {
        const { data } = await exportWithdrawRecord(this.searchForm);
        data && this.$message.success(data);
      },
      getExportList() {
        this.$refs.exportRecord.isShow = true;
        this.$refs.exportRecord.getExportRecord('25')
      },
      getTagType(status) {
        switch (Number(status)) {
          case 0:
            return '';
          case 1:
            return 'success';
          case 2:
            return 'danger';
          default:
            return ''
        }
      },
    },
  }
</script>

<style scoped lang="scss">

</style>
