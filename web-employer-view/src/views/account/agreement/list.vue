<template>
  <div class="box-container">

    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">协议名称：</span>
            <el-input v-model="form.topicLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">签署方：</span>
            <el-select
              v-model="form.signerNoList"
              clearable
              multiple
              filterable
            >
              <el-option
                v-for="(item, key) in mainstayList"
                :key="key"
                :label="item.mainstayName"
                :value="item.mainstayNo"
              ></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">协议状态：</span>
            <el-select
              clearable
              v-model="form.status"
            >
              <el-option
                v-for="item in $dict('AgreementStatusEnum')"
                :key="item.code"
                :value="item.code"
                :label="item.desc"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">发起时间：</span>
            <date-picker
              picker="separate"
              type="datetime"
              ref="datePicker1"
              :start-time.sync="form.beginDate"
              :end-time.sync="form.endDate"
            ></date-picker>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">完成时间：</span>
            <date-picker
              picker="separate"
              type="datetime"
              ref="datePicker2"
              :start-time.sync="form.beginFinishTime"
              :end-time.sync="form.endFinishTime"
            ></date-picker>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">签署截止时间：</span>
            <date-picker
              picker="separate"
              type="datetime"
              ref="datePicker3"
              :use-option="false"
              :start-time.sync="form.beginDeadLine"
              :end-time.sync="form.endDeadLine"
            ></date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="search(true)"
            >查询
            </el-button>
            <el-button
              type="text"
              @click="getExportList"
            >查看导出列表
            </el-button>
            <el-button
              type="text"
              @click="resetForm"
            >清空筛选条件
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="func-container">
      <el-button
        @click="exportFile"
        :disabled="selection.length === 0"
      >批量下载
      </el-button>
    </div>

    <div class="content-container">
      <el-table
        class="content-main"
        :data="agreementObj.data"
        v-if="agreementObj.data"
        @selection-change="handleSelect"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column
          label="协议名称"
          min-width="100px"
          prop="topic"
        ></el-table-column>
        <el-table-column
          label="签署方"
          width="150"
        >
          <template v-slot="{row}">
            <div
              class="signer-item"
              v-for="(item, index) in row.signerList"
              :key="index"
            >
              {{ $dictCode('AgreementSignerTypeEnum', item.signerType).desc }}：{{ item.signerMchName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="签署状态">
          <template v-slot="{row}">
            <div
              class="signer-item"
              v-for="(item, index) in row.signerList"
              :key="index"
            >
              {{ $dictCode('AgreementSignerStatusEnum', item.status).desc }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="协议负责人"
          width="150"
        >
          <template v-slot="{row}">
            <div
              class="signer-item"
              v-for="(item, index) in row.signerList"
              :key="index"
            >
              {{ item.signerName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="手机号"
          width="130"
        >
          <template v-slot="{row}">
            <div
              class="signer-item"
              v-for="(item, index) in row.signerList"
              :key="index"
            >
              {{ item.signerPhone }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="签署截止时间"
          width="120"
          prop="deadline"
        >
          <template v-slot="{row}">
            <p v-html="renderTime(row.deadline)"></p>
          </template>
        </el-table-column>
        <el-table-column
          label="协议到期时间"
          width="120"
          prop="expireTime"
        >
          <template v-slot="{row}">
            <p v-html="renderTime(row.expireTime)"></p>
          </template>
        </el-table-column>
        <el-table-column
          label="发起时间"
          width="120"
          prop="createTime"
        >
          <template v-slot="{row}">
            <p v-html="renderTime(row.createTime)"></p>
          </template>
        </el-table-column>
        <el-table-column
          label="完成时间"
          width="120"
          prop="finishTime"
        >
          <template v-slot="{row}">
            <p v-html="renderTime(row.finishTime)"></p>
          </template>
        </el-table-column>

        <el-table-column
          label="协议状态"
          min-width="70px"
          prop="status"
        >
          <template v-slot="{row}">
            {{ $dictCode('AgreementStatusEnum', row.status).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="150px"
          fixed="right"
        >
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="download(row)"
              v-if="row.status == 100"
            >下载
            </el-button>
            <el-button
              type="text"
              @click="sign(row)"
              v-if="row.status == 103 && row.signType == 100"
            >签署
            </el-button>
            <el-button
              type="text"
              @click="openOriginFile(row)">查看初始协议
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="agreementObj"
          ref="pagination"
          :total="agreementObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
      <ExportRecord ref="exportRecord"></ExportRecord>
    </div>
  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import { exportFile, getAgreementPage, getSignUrl } from '@/api/agreement'
import { getMainstayList } from "@/api/merchant";


export default {
  name: 'AgreementManage',
  components: {
    ExportRecord
  },
  data(vm) {
    return {
      form: {
        topicLike: '',
        signerNoList: [],
        beginDate: '',
        endDate: '',
        status: '',
        beginDeadLine: '',
        endDeadLine: '',
        beginFinishTime: '',
        endFinishTime: ''
      },
      pageCurrent: 1,
      pageSize: 10,
      agreementObj: {},
      mainstayList: [],
      staffList: [],
      selection: [],
    }
  },
  mounted() {
    this.getMainstayList()
    this.search();
  },
  methods: {
    getMainstayList() {
      getMainstayList().then(res => this.mainstayList = res.data || [])
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('48');
    },
    resetForm() {
      this.form = {}
      this.$refs.datePicker1.clearTime();
      this.$refs.datePicker2.clearTime();
      this.$refs.datePicker3.clearTime();
    },
    async search(init) {
      if (init === true) {
        this.pageCurrent = 1;
      }
      const {data} = await getAgreementPage({
        pageCurrent: this.pageCurrent,
        pageSize: this.pageSize,
        ...this.form
      })
      this.agreementObj = data;
    },
    async download(row) {
      // downloadArchiveFile({id: data.id}).then(({data}) => {
      //   this.$message.success(data);
      // })
      let file = row.fileList.find(item => item.type == 101)
      if (file) {
        const fileMsg = await this.formatFileMsg(file.fileUrl)
        const name = file.fileName.split('.').slice(0, -1).join('.')
        const suffix = file.fileUrl.split('.').pop()
        this.downloadFile(fileMsg.fileUrl, name, suffix)
      } else {
        this.$message.error('没有对应文件')
      }
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    handleSelect(val) {
      this.selection = val
    },
    async sign({signType, id}) {
      if (signType === 100) {
        const {data} = await getSignUrl({id})
        if (data && typeof data === 'string') {
          window.open(data)
        }
      } else {
        this.$alert('此协议已转线下签署，如有疑问请联系销售经理', '注意', {
          type: 'warning',
          confirmButtonText: '知道了',
          showClose: false
        })
      }
    },
    async exportFile() {
      await exportFile({
        idList: this.selection.map(item => item.id)
      })
      this.$message.success('操作成功，请查看导出列表')
    },
    async openOriginFile({fileList}) {
      let file = fileList.filter(f => f.type === 100)[0]
      if (file) {
        const fileMsg = await this.formatFileMsg(file.fileUrl)
        window.open(fileMsg.fileUrl)
      } else {
        this.$message.error('初始协议文件缺失')
      }
    }
  }
}
</script>

<style
  lang="scss"
  scoped
>
.signer-item {
  &:first-child {
    border-bottom: 1px solid #ddd;
  }
}
</style>
