<template>
  <el-dialog
    :visible.sync="visible"
    :before-close="close"
    title="切换通道账户"
  >
    <template v-for="item in $dict('ChannelTypeEnum')">
      <div
        v-loading="loading"
        :key="item.code"
        v-if="item.code in accountRecord">
        <p class="subTitle">{{ item.desc }}通道 - 默认出款账户</p>
        <el-button
          v-for="channel in accountRecord[item.code]"
          :key="channel.id"
          :type="channel.isCurrent ? 'primary': 'info'"
          plain
          @click="changeChannel(channel)">
          {{ channel.title }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { changeAccount, getAccountRecord } from "@/api/account";
import { toPromise } from "@/utils";

export default {
  name: 'SwitchChannelDialog',
  props: {
    channelData: {
      type: Object,
      default: () => ({})
    },
  },
  data() {
    return {
      visible: false,
      accountRecord: {},
      loading: false,
    }
  },
  methods: {
    open() {
      this.visible = true
      this.$nextTick(this.getAccountRecord)
    },
    close(done) {
      this.visible = false
      this.accountRecord = {}
    },
    // 获取商户账户通道信息
    async getAccountRecord() {
      const { data } = await getAccountRecord({
        mainstayNo: this.channelData.mainstayNo
      })
      this.accountRecord = data
    },
    async changeChannel(channel) {
      if (channel.isCurrent || this.loading) return
      let [err, _] = await toPromise(this.$confirm('<p>您正在切换默认出款账户</p><p style="color: red;">确认要继续吗？</p>', '提示', {
        dangerouslyUseHTMLString: true,
        type: 'warning',
        confirmButtonText: '继续'
      }))
      if (err) return
      this.loading = true
      let [error] = await toPromise(changeAccount({
        id: channel.id
      }))
      this.loading = false
      if (error) return
      this.$message.success('切换账户成功')
      this.finish()
    },
    finish() {
      this.$emit('change')
      this.close()
    }
  }
}
</script>

<style
  lang="scss"
  scoped>
.subTitle {
  margin: 16px 0;
}
</style>
