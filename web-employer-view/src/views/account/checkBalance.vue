<template>
  <el-main>
    <p class="title-container">{{ userData.mchName }}</p>
    <div class="header-block">
      <div class="header-container header-overview">
        <div class="header-title">资产总览</div>
        <el-divider></el-divider>
        <div class="header-content">
          <div>总可用金额（元）</div>
          <div class="account-balance">{{ totalAmount | moneyFormat }}
            <span
              v-if="checkError"
              style="font-size: 13px; color: #f00"
            >（备注：通道查询异常，请稍后重新查询）</span>
          </div>
        </div>
        <div class="header-btn">
          <el-button
            type="primary"
            @click="goRecharge"
          >充值
          </el-button>
          <el-button
            type="primary"
            @click="goWithdraw"
          >提现
          </el-button>
        </div>
      </div>
      <div class="header-container header-account">
        <div class="header-title">账户信息</div>
        <el-divider></el-divider>
        <div class="account-content-mch">
          <p>商户名：{{ userData.mchName }}</p>
        </div>
        <el-divider></el-divider>
        <div class="account-content">
          <div class="account-content-item text-center">
            <div class="account-content-icon">
              <svg-icon icon-class="shieldDone"></svg-icon>
            </div>
            <div>认证信息</div>
          </div>
          <div class="account-content-item text-center">
            <div class="account-content-icon">
              <svg-icon icon-class="document"></svg-icon>
            </div>
            <div>协议信息</div>
          </div>
          <div class="account-content-item text-center">
            <div class="account-content-icon">
              <svg-icon icon-class="shieldDone"></svg-icon>
            </div>
            <div>发放方式</div>
          </div>
        </div>
      </div>
    </div>

    <div class="content-container">
      <el-tabs
        v-model="activeTab"
        @tab-click="onTabChange">
        <el-tab-pane
          v-for="(item, index) in tabList"
          :key="index"
          :name="String(index)"
          :label="item"
        ></el-tab-pane>
      </el-tabs>
      <template v-if="activeTab === '0'">
        <p class="title-container">账户余额查询</p>
        <el-select
          v-model="mainstayNo"
          clearable
          @change="checkBalance"
        >
          <el-option
            v-for="(item, index) in mainstayList"
            :key="index"
            :value="item.mainstayNo"
            :label="item.mainstayName"
          ></el-option>
        </el-select>
        <el-table
          :data="list"
          :span-method="spanMethod"
        >
          <el-table-column label="账户">
            <template v-slot="{row}">
              {{ row.mainstayNo }}<br>{{ row.mainstayName }}
            </template>
          </el-table-column>

          <el-table-column
            label="银行卡通道|可用余额（元）"
            width="200"
            prop="bankAmount"
          >
            <template v-slot="{row, column}">
              <div :class="{'text-center': row.mainstayNo === yishui.code }">
                <p
                  class="color-warning"
                  v-if="!isNaN(row[column['property']])"
                >
                  {{ row['bankAmount'] | moneyFormat }}
                  <span v-if="row.mainstayNo === yishui.code">（已扣除服务费）</span>
                </p>
                <p
                  v-else-if="row[column['property']] == '异常'"
                  style="color: #f00"
                >
                  {{ row[column['property']] }}
                </p>
                <p
                  v-else
                  style="color: #ccc"
                >
                  {{ row[column['property']] }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="支付宝通道|可用余额（元）"
            width="200"
            prop="aliPayAmount"
          >
            <template v-slot="{row, column}">
              <p
                class="color-warning"
                v-if="!isNaN(row[column['property']])"
              >
                {{ row['aliPayAmount'] | moneyFormat }}
              </p>
              <p
                v-else-if="row[column['property']] == '异常'"
                style="color: #f00"
              >
                {{ row[column['property']] }}
              </p>
              <p
                v-else
                style="color: #ccc"
              >
                <span
                  class="func-content"
                  @click="submitChannel(row)">(立即开通)</span>
                {{ row[column['property']] }}
              </p>
            </template>
          </el-table-column>
          <el-table-column
            label="微信通道|可用余额（元）"
            width="200"
            prop="weixinAmount"
          >
            <template v-slot="{row, column}">
              <p
                class="color-warning"
                v-if="!isNaN(row[column['property']])"
              >
                {{ row['weixinAmount'] | moneyFormat }}
              </p>
              <p
                v-else-if="row[column['property']] == '异常'"
                style="color: #f00"
              >
                {{ row[column['property']] }}
              </p>
              <p
                v-else
                style="color: #ccc"
              >
                {{ row[column['property']] }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="可抵扣金额（元）"></el-table-column>

          <el-table-column
            label="状态"
            prop="status">
            <template v-slot="{row}">
              {{ $dictCode('UseOrUnuseEnum', row.status).desc }}
            </template>
          </el-table-column>

          <el-table-column label="操作">
            <template v-slot="{row}">
              <el-button
                type="text"
                @click="openChannelDialog(row)">
                切换账户
              </el-button>
              <el-button
                type="text"
                @click="goNotify(row)"
              >余额提醒
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template v-else>
        <div class="flex-wrapper">
          <div class="flex-item-main">
              <span
                class="flex-item__label"
                style="width:120px">请选择支付通道：</span>
            <el-select
              v-model="searchForm.payChannelNo"
              style="width: 100px"
              placeholder="选择支付通道"
              clearable
              filterable>
              <el-option
                v-for="item in payChannelList"
                :key="item.payChannelNo"
                :value="item.payChannelNo"
                :label="item.payChannelName"></el-option>
            </el-select>
            <el-select
              v-model="searchForm.channelType"
              placeholder="选择通道类型"
              clearable
              filterable>
              <el-option
                v-for="item in selectedChannelTypeList"
                :key="item"
                :value="item"
                :label="$dictCode('ChannelTypeEnum', item).desc"></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <el-input
              v-model="searchForm.trxNo"
              placeholder="请输入流水号"
              clearable></el-input>
          </div>
          <div class="flex-item">
            <el-select
              v-model="searchForm.accountNo"
              placeholder="全部账户"
              clearable
              filterable>
              <el-option
                v-for="(item, index) in mainstayList"
                :key="index"
                :label="item.mainstayName"
                :value="item.mainstayNo"
              ></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <el-select
              v-model="searchForm.altSource"
              placeholder="全部收支来源"
              clearable
              filterable>
              <el-option
                v-for="(item, index) in $dict('AltSourceEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"
              ></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <el-select
              v-model="searchForm.altType"
              placeholder="全部收支类型"
              clearable
              filterable>
              <el-option
                v-for="(item, index) in $dict('AltTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <date-picker
              type="datetime"
              picker="separate"
              :start-time.sync="searchForm.createTimeBegin"
              :end-time.sync="searchForm.createTimeEnd"
            >
              <template v-slot:append>
                <el-button
                  type="primary"
                  @click="search(true)">查询
                </el-button>
                <el-button @click="exportDetail">导出明细</el-button>
                <el-button @click="getExportRecord">查看导出</el-button>
              </template>
            </date-picker>
          </div>
        </div>
        <el-table
          :data="list"
          key="detail">
          <el-table-column
            label="创建时间"
            prop="createTime"
            width="180"
          ></el-table-column>
          <el-table-column
            label="平台流水号/通道流水号"
            width="180">
            <template v-slot="{row}">
              {{ row.trxNo }} <br> {{ row.channelTrxNo }}
            </template>
          </el-table-column>
          <el-table-column
            label="账户"
            prop="accountName"
            width="180"
          >
            <template v-slot="{row}">
              {{ row.accountNo }}<br>{{ row.accountName }}
            </template>
          </el-table-column>
          <el-table-column
            label="支付通道"
            prop="payChannelName"
            width="120"
          >
            <template v-slot="{row}">
              {{ row.payChannelName }}({{ $dictCode('ChannelTypeEnum', row.channelType).desc }})
            </template>
          </el-table-column>
          <el-table-column
            label="动账金额">
            <template v-slot="{row}">
              {{ row.altAmount | moneyFormat }}
            </template>
          </el-table-column>
          <el-table-column label="收支来源">
            <template v-slot="{row}">
              {{ $dictCode('AltSourceEnum', row.altSource).desc }}
            </template>
          </el-table-column>
          <el-table-column
            label="来源说明"
            prop="altDesc">
          </el-table-column>
          <el-table-column label="收支类型">
            <template v-slot="{row}">
              {{ $dictCode('AltTypeEnum', row.altType).desc }}
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            ref="pagination"
            :total="page.total"
            :current-page.sync="page.current"
            :page-sizes="[10,50]"
            :page-size.sync="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="search(true)"
            @current-change="search()"
          >
          </el-pagination>
        </div>

      </template>
    </div>

    <switch-channel-dialog
      ref="channelDialog"
      :channel-data="editRow"
      @change="checkBalance"></switch-channel-dialog>

    <!--导出dialog-->
    <export-record ref="exportRecord"></export-record>
  </el-main>
</template>

<script>
import {
  exportCkAcctDeta,
  getAmountInMainstay,
  getPayChannels,
  listAcctDetail,
  listAllMainstayList
} from '@/api/merchant';
import { submitChannel } from '@/api/levy';
import SwitchChannelDialog from "./component/SwitchChannelDialog.vue";
import ExportRecord from "@/components/ExportRecord/index.vue";

export default {
  name: 'merchantPayAccountCheck',
  components: { SwitchChannelDialog, ExportRecord },
  data(vm) {
    return {
      searchForm: {},
      activeTab: '0',
      tabList: ['账户余额', '收支明细'],
      payChannelList: [],
      list: [],

      mainstayNo: '',
      totalAmount: 0,

      mainstayList: [],

      yishui: vm.$dictFlag('SpecialSupplier', 'YISHUI'),

      checkError: false, // 标记是否查询有误

      editRow: {}, // 选中切换通道行
      page: {
        current: 1,
        total: 0,
        size: 10,
      }
    }
  },
  computed: {
    mchNo() {
      return this.$route.query.mchNo
    },
    selectedChannelTypeList() {
      const res = this.payChannelList.find(item => {
        return item.payChannelNo === this.searchForm.payChannelNo
      })
      return res?.channelType || []
    }
  },
  mounted() {
    listAllMainstayList().then(res => this.mainstayList = res.data);
    this.checkBalance();
    this.getPayChannelList()
  },
  methods: {
    goRecharge() {
      this.$router.push('/account/recharge')
    },
    goWithdraw() {
      this.$router.push('/account/withdraw')
    },
    search(init) {
      if (init === true) {
        this.page.current = 1
      }
      if (this.activeTab === '0') {
        this.checkBalance()
      } else {
        this.checkDetail()
      }
    },
    async checkBalance() {
      const { data } = await getAmountInMainstay({
        mainstayNo: this.mainstayNo,
      });
      if (this.activeTab === '0') {
        this.list = data.amountList;
        this.calculateAmount()
      }
    },
    calculateAmount() {
      let temp = 0
      for (let i = 0; i < this.list.length; i++) {
        if (this.list[i].mainstayNo === this.yishui.code) {
          temp += isNaN(this.list[i]['aliPayAmount']) ? 0 : parseFloat(this.list[i]['aliPayAmount']) * 100
        } else {
          ['bankAmount', 'aliPayAmount', 'weixinAmount'].forEach(item => {
            if (this.list[i][item] === '异常') this.checkError = true
            let money = isNaN(this.list[i][item]) ? 0 : parseFloat(this.list[i][item]) * 100;
            temp += money;
          })
        }

      }
      this.totalAmount = (temp / 100).toFixed(2)
    },
    goNotify(row) {
      this.$router.push({
        path: '/account/balance/notify',
        query: {
          mainstayNo: row.mainstayNo,
          mainstayName: row.mainstayName,
          action: 'ADD',
        }
      })
    },
    spanMethod({ row, column }) {
      if (row.mainstayNo === this.yishui.code) {
        // 易税跨表处理
        if (column.property === 'bankAmount') {
          return {
            rowspan: 1,
            colspan: 2
          }
        } else if (column.property === 'aliPayAmount') {
          return [0, 0]
        }
      }
    },
    async submitChannel({ mainstayNo, mainstayName }) {
      try {
        await this.$confirm(`
            <div style="text-align: left;">
              <p>请确认贵司已开通企业支付宝账户，并在点击「立即开通」后跳转支付宝后台，<strong>使用手机端登录同名企业支付宝账户进行扫码开通。</strong></p>
              <p>本次开通后，平台将基于支付宝资金共管授权的产品（安全发），为贵司提供独立的「专属记账本」，用于承载与本次灵活用工场景下的转账需求。</p>
              <p>请注意，此操作仅开通与灵活用工业务合作相关的记账本，在满足业务场景条件下达到安全隔离资金的效果。不在专属记账本内的企业支付宝资金，与本次授权无关。</p>
            </div>
          `, '专属记账本开通确认', {
          dangerouslyUseHTMLString: true,
          center: true,
          confirmButtonText: '立即开通',
          closeOnClickModal: false,
        })
      } catch {
        return
      }
      const { data } = await submitChannel({
        employerNo: this.userData.mchNo,
        employerName: this.userData.mchName,
        mainstayNo,
        mainstayName,
        payChannelNo: 'ALIPAY',
        payChannelName: '支付宝',
        channelType: 2,
        merchantType: '100',
      })
      if (data) {
        if (/^http(s)?/.test(data)) {
          window.open(data)
          try {
            await this.$confirm('请在新页面扫描完成开通之后重新刷新页面', '提示', {
              confirmButtonText: '确定',
              closeOnClickModal: false,
              type: 'info',
              showCancelButton: false,
              showClose: false,
            })
          } finally {
            location.reload()
          }
        } else {
          // 已经签约的情况
          this.list = []
          this.$message.error('已经签约，正在重新查询，请稍后')
          this.checkBalance()
        }
      }
    },
    openChannelDialog(row) {
      this.editRow = JSON.parse(JSON.stringify(row))
      this.$refs.channelDialog.open(true)
    },
    async checkDetail() {
      const { data: { records, total } } = await listAcctDetail({
        ...this.searchForm,
        size: this.page.size,
        current: this.page.current
      })
      if (this.activeTab === '1') {
        this.list = records
        this.page.total = total
      }
    },
    async getPayChannelList() {
      const { data: { data } } = await getPayChannels({
        pageCurrent: 1,
        pageSize: 100
      })
      this.payChannelList = data
    },
    onTabChange() {
      this.list = []
      this.search(true)
    },
    async exportDetail() {
      await exportCkAcctDeta(this.searchForm)
      this.$message.success('导出成功')
    },
    getExportRecord() {
      this.$refs.exportRecord.open('60')
    },
  },
}
</script>

<style
  scoped
  lang="scss">
.title-container {
  font-size: 25px;
}

.header-block {
  display: flex;
}

.header-container {
  background: #fff;

  .header-title {
    padding: 24px 24px 0 24px;
    font-size: 20px;
    font-weight: bolder;
  }

  .header-content {
    padding: 0 24px 40px;
  }

  &.header-overview {
    flex: 0.7;
  }

  &.header-account {
    flex: 0.3;
    margin-left: 16px;
  }
}

.header-account {
  .account-content-mch {
    padding: 0 24px;
  }

  .account-content {
    display: flex;
  }

  .account-content-item {
    flex: 1;
    width: calc(100% / 3);
    margin-bottom: 8px;
  }

  .account-content-icon {
    display: inline-block;
    background: $mainColor;
    margin-bottom: 4px;
    border-radius: 8px;

    .svg-icon {
      width: 50px;
      height: 45px;
      padding: 5px;
    }
  }
}

.header-overview {
  .header-btn {
    padding: 0 24px;
  }
}

.content-container {
  margin-top: 32px;

  .el-table {
    margin-top: 16px;
  }
}

.account-balance {
  font-size: 30px;
  color: #ffa500;
}
</style>
