<template>
  <div class="page-container">
    <warning-block>
      <p class="warning-title">上传签约文件说明</p>
      <p>1、您可以批量上传用户信息进行签约，同一批次最多支持1000条，同一身份证号不能重复上传</p>
      <p>2、上传后系统会初步验证用户信息，并显示信息校验状态。信息校验成功，用户将收到系统短信，按短信提示完成后续步骤</p>
    </warning-block>

    <el-form
      style="margin-left: 32px; margin-top: 32px"
      ref="form"
      :model="form"
      :rules="rules"
    >
      <div style="color: #444; margin: 16px 0;">请选择代征主体</div>
      <el-form-item prop="mchNo">
        <el-select
          v-model="form.mchNo"
          clearable
        >
          <el-option
            v-for="(item, index) in mainstayList"
            :key="index"
            :label="item.mainstayName"
            :value="item.mainstayNo"
          ></el-option>
        </el-select>
        <span
          class="func-content"
          @click="downTemplate"
        >下载预签约模板</span>
      </el-form-item>
      <div style="color: #444; margin: 16px 0;">上传填好的预签约文件</div>
      <el-form-item prop="file">
        <el-upload
          class="upload-demo"
          action=""
          :auto-upload="false"
          :headers="uploadHeader"
          accept=".xlsx,.xls"
          :limit="1"
          :on-change="handlerChange"
        >
          <el-button type="primary">选择需要上传的文件</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item>
        <el-radio-group v-model="form.sms">
          <el-radio :label="1">短信通知用户签约</el-radio>
          <el-radio :label="0">暂不发送短信</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          @click="submit"
          style="margin-top: 32px;"
          :loading="uploadLoading"
        >上传</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import { uploadSignFile } from '@/api/sign';
  import { getAllMainstayList } from '@/api/merchant';
  import templateFile from '@/assets/signTemplate.xlsx';
  export default {
    name: "uploadSignFile",

    data() {
      return {
        mainstayList: [],

        form: {
          mchNo: '',
          file: '',
          sms: 1,
        },
        rules: {
          mchNo: [{ required: true, trigger: 'change', message: '请选择代征主体' }],
          file: [{ required: true, trigger: 'change', message: '请上传文件' }],
        },
        uploadLoading: false,
      }
    },
    mounted() {
      getAllMainstayList().then(res => this.mainstayList = res.data)
    },
    methods: {
      handlerChange(file) {
        this.form.file = file.raw
      },
      async submit() {
        const valid = await this.$refs.form.validate().catch(_ => false);
        if (!valid) return;
        const formData = new FormData();
        for (let p in this.form) {
          formData.append(p, this.form[p])
        }
        this.uploadLoading = true;
        const { data } = await uploadSignFile(formData).finally(() => { this.uploadLoading = false });
        data && this.$message.success(data);
        this.$router.push({
          path: '/trade/sign/signInfo'
        })
      },
      downTemplate() {
        const a = document.createElement('a');
        a.href = templateFile;
        a.download = '预签约模板.xlsx';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      }
    },
  }
</script>

<style lang="scss" scoped>
</style>
