<template>
  <el-dialog
    :visible="show"
    :before-close="close"
    title="上传文件"
  >
    <el-form
      :model="form"
      label-width="200px"
      ref="form">

      <el-form-item label="证件类型：">
        <el-radio-group v-model="form.idCardType">
          <el-radio
            v-for="item in $dict('IdCardTypeEnum')"
            :key="item.code"
            :label="Number(item.code)"
          >
            {{ item.desc }}
          </el-radio>
        </el-radio-group>
        <id-upload
          v-if="form.idCardType == 100"
          :front-url.sync="form.idCardFrontUrl"
          :back-url.sync="form.idCardBackUrl"
          @before-upload="loading = true"
          @success="loading = false"
          @remove="loading = false"
        ></id-upload>
        <file-upload
          v-if="form.idCardType == 101"
          accept=".jpg,.jpeg,.png,.bmp,.gif"
          :url.sync="form.idCardCopyFileUrl"
          :max="1"
        >
          <template v-slot:tip>
            <img
              style="width: 112px;"
              src="@/assets/idCardCopyFileExample.png"
              alt="">
          </template>
        </file-upload>
      </el-form-item>

      <el-form-item label="半身照">
        <el-upload
          v-toggle="form.cerFaceUrl"
          class="upload-demo"
          :action="baseUrl + '/file/upload'"
          :headers="uploadHeader"
          :file-list="form.cerFaceUrl"
          :before-upload="handleBeforeUpload"
          :on-remove="handleRemove('cerFaceUrl')"
          :on-success="(response, file, fileList) => handleSuccess('cerFaceUrl', response, file, fileList)"
          :on-preview="onPreview"
          accept=".bpm,.png,.jpg,.jpeg,.gif"
          list-type="picture-card"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <el-button
        @click="onConfirm"
        type="primary"
        :loading="loading">确认
      </el-button>
      <el-button @click="close">取消</el-button>
    </template>

  </el-dialog>
</template>

<script>
import { addSignImage } from '@/api/sign';
import { uploadIdCard } from '@/api/data';
import IdUpload from '@/components/IdUpload'
import FileUpload from '@/components/FileUpload'

export default {
  name: "upload-img",
  props: {
    signItem: {
      type: Object,
      default: () => ({})
    },
    useDataApi: {
      type: Boolean,
      default: false
    },
  },
  components: {
    IdUpload,
    FileUpload,
  },
  data() {
    return {
      show: false,
      form: {
        idCardType: '',
        idCardFrontUrl: '',
        idCardBackUrl: '',
        idCardCopyFileUrl: '',
        cerFaceUrl: [],
      },
      loading: false,
    }
  },
  methods: {
    open() {
      this.show = true;
      this.$nextTick(async () => {
        if (this.signItem.idCardFrontUrl) {
          this.form.idCardFrontUrl = this.signItem.idCardFrontUrl
        }
        if (this.signItem.idCardBackUrl) {
          this.form.idCardType = 100
          this.form.idCardBackUrl = this.signItem.idCardBackUrl
        }
        if (this.signItem.idCardCopyUrl) {
          if (this.form.idCardType === '') this.form.idCardType = 101;
          this.form.idCardCopyFileUrl = this.signItem.idCardCopyUrl
        }
        this.form.cerFaceUrl = []
        if (this.signItem.cerFaceUrl) {
          const fileMsg = await this.formatFileMsg(this.signItem.cerFaceUrl)
          this.form.cerFaceUrl.push({
            url: fileMsg.fileUrl,
            origin: this.signItem.cerFaceUrl,
          })
        }
      })

    },
    close() {
      this.show = false;
      this.form = {
        idCardType: '',
        idCardFrontUrl: '',
        idCardBackUrl: '',
        idCardCopyFileUrl: '',
        cerFaceUrl: [],
      }
    },
    handleRemove(param) {
      return (file, fileList) => {
        this.form[param] = fileList;
        this.loading = false;
      }
    },
    async handleSuccess(param, response, file, fileList) {
      let formatFileList = []
      for (let i = 0; i < fileList.length; i++) {
        const item = fileList[i];
        const url = item.response ? item.response.data : item.origin
        const fileMsg = await this.formatFileMsg(url);
        formatFileList.push({
          url: fileMsg.fileUrl,
          origin: url,
          uid: item.uid,
          name: item.name,
        });
      }
      this.form[param] = formatFileList;
      this.loading = false;
    },
    async onPreview(file) {
      const url = file.response ? (file.response.data) : file.origin
      const fileMsg = await this.formatFileMsg(url)
      this.$preview(fileMsg.fileUrl)
    },
    async onConfirm() {
      const form = JSON.parse(JSON.stringify(this.form));
      if (form.cerFaceUrl.length > 0) {
        form.cerFaceUrl = form.cerFaceUrl[0].response ? form.cerFaceUrl[0].response.data : form.cerFaceUrl[0].origin
      } else {
        form.cerFaceUrl = null
      }
      form.signId = this.signItem.signId || this.signItem.id;
      if (!this.useDataApi) {
        await addSignImage(form);
      } else {
        form.id = this.signItem.id
        await uploadIdCard(form)
      }
      this.$message.success('上传成功')
      this.$emit('change');
      this.close()
    },
    handleBeforeUpload(file) {
      this.loading = true;
      return this.validateUploadFile({ img: true, size: 10 })(file)
    }
  },
}
</script>

<style
  lang="scss"
  scoped>

</style>
