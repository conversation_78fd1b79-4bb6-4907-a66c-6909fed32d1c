<template>
  <el-dialog
    :visible.sync="visible"
    :before-close="close"
    title="修改三要素"
  >
    <el-form label-width="130px" :model="form" ref="form" :rules="rules">
      <warning-block>
        <p>修改后系统会初步验证用户信息，若信息校验通过将会给用户发送电子签约的系统短信</p>
      </warning-block>
      <el-form-item label="姓名：" prop="receiveName">
        <el-input clearable v-model="form.receiveName" :placeholder="info.receiveName"></el-input>
      </el-form-item>
      <el-form-item label="证件号码：" prop="receiveIdCardNo">
        <el-input clearable v-model="form.receiveIdCardNo" :placeholder="info.receiveIdCardNo"></el-input>
      </el-form-item>
      <el-form-item label="预签约手机号：" prop="receivePhoneNo">
        <el-input clearable v-model="form.receivePhoneNo" :placeholder="info.receivePhoneNo"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="confirm">确定</el-button>
      <el-button @click="close">取消</el-button>
    </template>
  </el-dialog>
</template>

<script>
  export default {
    name: "modify-sign-info",
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      info: {
        type: Object,
        default: () => ({})
      }
    },
    watch: {
      visible(val) {
        if (!val) {
          this.$refs.form.clearValidate();
          this.$refs.form.resetFields();
        } else {
          this.form.id = this.info.id;
        }
      }
    },
    data() {
      return {
        form: {
          id: '',
          receiveIdCardNo: '',
          receivePhoneNo: '',
          receiveName: '',
        },
        rules: {
          receiveIdCardNo: [{required: true, message: '请输入证件号码', trigger: 'blur'}],
          receivePhoneNo: [{required: true, message: '请输入预签约手机号', trigger: 'blur'}],
          receiveName: [{required: true, message: '请输入姓名', trigger: 'blur'}],
        }
      }
    },
    methods: {
      close() {
        this.$emit('update:visible', false)
      },
      async confirm() {
        const valid = await this.$refs.form.validate().catch(_ => false);
        if (!valid) return;
        this.$emit('change', {...this.form});
        this.close();
      }
    },
  }
</script>

<style lang="scss" scoped>

</style>
