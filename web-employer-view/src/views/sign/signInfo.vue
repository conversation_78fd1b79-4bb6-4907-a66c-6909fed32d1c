<template>
  <div class="box-container">
    <el-button
      type="primary"
      class="create-btn"
      @click="goUploadFile">上传预签约文件
    </el-button>

    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-half">
          <span class="flex-item__label">签约状态：</span>
          <el-radio-group
            v-model="searchForm.signStatus"
            @change="search(true)">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button
              v-for="(item, index) in $dict('SignStatusEnum')"
              :key="index"
              :label="item.code">
              {{ item.desc }}
            </el-radio-button>
          </el-radio-group>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">代征主体：</span>
          <el-select
            v-model="searchForm.mainstayNo"
            clearable>
            <el-option
              v-for="(item, index) in mainstayList"
              :key="index"
              :label="item.mainstayName"
              :value="item.mainstayNo"></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">姓名：</span>
          <el-input v-model="searchForm.receiveName"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">证件号码：</span>
          <el-input v-model="searchForm.receiveIdCardNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">手机号：</span>
          <el-input v-model="searchForm.receivePhoneNo"></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">更新时间：</span>
          <date-picker
            :start-time.sync="searchForm.createBeginDate"
            :end-time.sync="searchForm.createEndDate"
            ref="timepicker"
            picker="separate"
            type="datetime"
            fast-time="thirtyDay">
          </date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)">查询
        </el-button>
        <el-button
          @click="exportSignRecord"
          v-permission="'sign:signRecord:view'">导出
        </el-button>
        <el-button
          type="text"
          @click="getExportList">查看已导出列表
        </el-button>
        <el-button
          type="text"
          @click="resetField">清空筛选条件
        </el-button>
      </div>
    </div>

    <el-main class="content-container">
      <el-table :data="response.data">
        <el-table-column
          type="index"
          :index="getIndex"
          label="序号"></el-table-column>
        <el-table-column
          label="创建时间"
          width="150"
          prop="createTime"></el-table-column>
        <el-table-column
          label="更新时间"
          width="150"
          prop="updateTime"></el-table-column>
        <el-table-column
          label="姓名"
          prop="receiveName"></el-table-column>
        <el-table-column
          label="证件号码"
          width="180"
          prop="receiveIdCardNo"></el-table-column>
        <el-table-column
          label="预签约手机号"
          width="150"
          prop="receivePhoneNo"></el-table-column>

        <el-table-column
          label="信息校验状态"
          prop="infoStatus"
          width="120">
          <template v-slot="{row}">
            【{{ $dictCode('SuccessFailCodeEnum', row.infoStatus).desc }}】{{ row.infoStatus == '101' ? row.errMsg : '' }}
          </template>
        </el-table-column>

        <el-table-column
          label="签约状态"
          width="100">
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.signStatus)">
              {{ $dictCode('SignStatusEnum', row.signStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="签约类型"
          width="100">
          <template v-slot="{row}">
            {{ $dictCode('ChannelSignTypeEnum', row.signType).desc }}
          </template>
        </el-table-column>

        <el-table-column
          label="代征主体ID/名称"
          width="140">
          <template v-slot="{row}">
            {{ row.mainstayNo }} <br /> {{ row.mainstayName }}
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          fixed="right">
          <template v-slot="{row}">
            <el-button
              v-if="row.fileUrl"
              type="text"
              @click="openFile(row.fileUrl)">
              查看文件
            </el-button>
            <el-button
              type="text"
              v-if="row.infoStatus == 101"
              @click="openModify(row)"
            >
              修改三要素
            </el-button>
            <el-button
              type="text"
              v-else-if="row.signStatus == 300 || row.signStatus == 400"
              @click="openResend(row)"
              :loading="resendLoading"
            >
              补发短信
            </el-button>
            <el-button
              type="text"
              @click="openUploadImg(row)"
              v-if="row.signStatus == 100 || row.signStatus == 400"
            >上传身份证文件
            </el-button>
          </template>
        </el-table-column>

      </el-table>
    </el-main>

    <el-footer class="pagination-container">
      <el-pagination
        :total="response.totalRecord"
        :current-page.sync="pageCurrent"
        :page-size.sync="pageSize"
        :page-sizes="[10, 50]"
        @size-change="search(true)"
        @current-change="search()"
        background
        layout="total, sizes, prev, pager, next, jumper"
      ></el-pagination>
    </el-footer>

    <el-dialog
      ref="resendMsgDialog"
      title="补发短信"
      :visible.sync="visible"
    >
      <p>同一身份证号每天至多发送 3 次签约短信，若当天已发送过短信则需要 24 小时后才可重试。</p>
      <p>确认要补发短信吗？</p>
      <div slot="footer">
        <el-button
          type="primary"
          @click="resendMsg"
          :loading="resendLoading">确定
        </el-button>
        <el-button @click="closeResend">取消</el-button>
      </div>
    </el-dialog>

    <modify-sign-info
      :visible.sync="modifyVisible"
      :info="rowInfo"
      @change="modifySignInfo"></modify-sign-info>

    <export-record ref="exportRecord" />

    <upload-img
      ref="uploadDialog"
      :signItem="rowInfo"
      @change="search()"
    ></upload-img>
  </div>
</template>

<script>
import { listAllMainstayList } from '@/api/merchant'
import { exportSignRecord, getSignList, modifySignInfo, resendMsg } from '@/api/sign'
import ExportRecord from '@/components/ExportRecord';
import ModifySignInfo from './Components/ModifySignInfo'
import UploadImg from './Components/UploadImg'

export default {
  components: {
    ExportRecord,
    ModifySignInfo,
    UploadImg,
  },
  data() {
    return {
      mainstayList: [],

      searchForm: {
        signStatus: '',
        mainstayNo: '',
        receiveName: '',
        receiveIdCardNo: '',
        receivePhoneNo: '',
        employerNo: '',
        createBeginDate: '',
        createEndDate: '',
      },

      response: {
        data: [],
        totalRecord: 0,
      },

      pageSize: 10,
      pageCurrent: 1,

      visible: false,
      modifyVisible: false,

      rowInfo: {},

      resendLoading: false,
    }
  },
  mounted() {
    listAllMainstayList().then(res => {
      this.mainstayList = res.data
    })
    this.search();
  },
  methods: {
    async search(initFlag) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      const { data } = await getSignList({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      });
      this.response.data = data.data;
      this.response.totalRecord = data.totalRecord;
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    resetField() {
      this.searchForm = {
        signStatus: '',
        mainstayNo: '',
        receiveName: '',
        receiveIdCardNo: '',
        receivePhoneNo: '',
        employerNo: '',
        createBeginDate: '',
        createEndDate: '',
      };
      this.$refs.timepicker.clearTime();
    },
    async exportSignRecord() {
      const { data } = await exportSignRecord(this.searchForm);
      data && this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('12');
    },
    getTagType(status) {
      switch (Number(status)) {
        case 100:
          return 'success';
        case 200:
          return 'danger';
        case 400:
          return 'info';
        default:
          return ''
      }
    },
    async openFile(url) {
      const fileMsg = await this.formatFileMsg(url);
      window.open(fileMsg.fileUrl)
    },
    goUploadFile() {
      this.$router.push('/trade/sign/upload')
    },
    openResend(row) {
      this.rowInfo = row;
      this.visible = true;
    },
    async resendMsg() {
      this.resendLoading = true;
      try {
        const { data } = await resendMsg({ id: this.rowInfo.id });
        data && this.$message.success(data);
        this.closeResend();
        this.search();
      } finally {
        this.resendLoading = false;
      }
    },
    closeResend() {
      this.visible = false;
    },
    openModify(row) {
      this.rowInfo = row;
      this.modifyVisible = true;
    },
    async modifySignInfo(form) {
      const { data } = await modifySignInfo(form);
      data && this.$message.success(data);
      this.search();
    },
    openUploadImg(row) {
      this.rowInfo = row
      this.$refs.uploadDialog.open();
    }
  }
}
</script>

<style>

</style>
