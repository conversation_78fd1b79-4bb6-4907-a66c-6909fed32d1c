<template>
  <div class="box-container">
    <el-button
      class="create-btn"
      type="primary"
      @click="create"
    >发布任务</el-button>
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">任务编号：</span>
          <el-input
            v-model="searchForm.jobId" placeholder="模糊查询"
            clearable
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">任务名称：</span>
          <el-input
            v-model="searchForm.jobName"
            clearable placeholder="模糊查询"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">岗位：</span>
          <el-cascader
            filterable
            clearable
            data-index="index"
            :options="industryListOptions"
            :props="{
              expandTrigger: 'hover',
              emitPath: false,
              value: 'industryTypeCode',
              label: 'industryTypeName',
              leaf: 'industryTypeCode',
            }"
            v-model="searchForm.industryCode"
          >
            <template v-slot="{ data }">
              <el-tooltip
                :content="data.industryTypeName"
                :disabled="data.industryTypeName.length < 14"
              >
                <span class="cascader-content-item">
                  {{ data.industryTypeName }}
                </span>
              </el-tooltip>
            </template>
          </el-cascader>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">接单模式：</span>
          <el-select
            v-model="searchForm.acceptMode"
            clearable
          >
            <el-option
              v-for="(item, index) in $dict('AcceptModeEnum')"
              :key="index"
              :label="item.desc"
              :value="item.code"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            ref="datepicker"
            type="datetime"
            picker="separate"
            :start-time.sync="searchForm.createTimeBegin"
            :end-time.sync="searchForm.createTimeEnd"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询</el-button>
        <el-button @click="resetForm">重置</el-button>
      </div>
    </div>
    <el-tabs
      v-model="activeName"
      @tab-click="handleTabChange"
    >
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="index"
        :label="item.desc"
        :name="item.code"
      ></el-tab-pane>
    </el-tabs>

    <el-table :data="list">
      <el-table-column
        label="任务编号"
        prop="jobId"
        width="180"
      ></el-table-column>
      <el-table-column
        label="任务名称"
        prop="jobName"
        width="120"
      ></el-table-column>
      <el-table-column
        label="行业/岗位"
        prop="industryName"
        width="150"
      ></el-table-column>
      <el-table-column label="任务日期">
        <template v-slot="{ row }">
          {{ row.jobAvalDateType == 100 ? "长期" : `${row.jobStartDate}~${row.jobFinishDate}` }}
        </template>
      </el-table-column>
      <el-table-column
        label="接单模式"
        prop="acceptMode"
      >
        <template v-slot="{ row, column }">
          {{ $dictCode("AcceptModeEnum", row[column.property]).desc }}
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        prop="jobStatus"
      >
        <template v-slot="{ row, column }">
          <el-tag :type="getTagType(row[column.property])">
            {{ $dictCode("JobStatusEnum", row[column.property]).desc }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="招收人数"
        prop="workerNum"
      ></el-table-column>
      <el-table-column
        label="已录用人数"
        prop="employedNum"
      ></el-table-column>
      <el-table-column
        label="创建时间"
        prop="createTime"
        width="120"
      >
        <template v-slot="{ row }">
          <p v-html="renderTime(row.createTime)"></p>
        </template>
      </el-table-column>

      <el-table-column
        fixed="right"
        label="操作"
        width="200"
      >
        <template v-slot="{ row }">
          <el-button
            class="job-btn"
            v-if="row.jobStatus == 100"
            type="text"
            @click="edit(row)"
          >编辑</el-button>
          <el-button
            class="job-btn"
            v-if="row.jobStatus == 200 || row.jobStatus == 300"
            type="text"
            @click="detail(row)"
          >任务详情</el-button>
          <el-button
            class="job-btn"
            v-if="row.jobStatus == 200"
            type="text"
            @click="complete(row)"
          >完成任务</el-button>
          <el-button
            class="job-btn"
            v-if="row.jobStatus == 400"
            type="text"
            @click="republish(row)"
          >重新发布</el-button>
          <el-button
            v-if="row.jobStatus == 100"
            type="text"
            style="color: red"
            @click="deleteItem(row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="page.total"
        :page-sizes="[10, 50]"
        :current-page.sync="page.current"
        :page-size.sync="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </el-footer>
  </div>
</template>

<script>
  import { listAllIndustryType } from '@/api/business'
  import { taskList, deleteJob, finishTask } from "@/api/jobworker";
  import { toPromise, convert } from "@/utils";
  export default {
    data(vm) {
      return {
        searchForm: {
          jobStatus: "ALL",
        },
        page: {
          current: 1,
          size: 10,
          total: 0,
        },
        list: [],
        tabs: [],
        activeName: "ALL",

        industryList: [],
        industryListOptions: [],
      };
    },
    mounted() {
      this.listAllIndustryType();
      this.getTabs();
      this.search(true);
    },
    methods: {
      create() {
        this.$router.push({
          path: "/job/task/create",
        });
      },
      edit({ id, jobId }) {
        this.$router.push({
          path: `/job/task/create?jobId=${jobId}`,
        });
      },
      detail({ id, jobId }) {
        this.$router.push({
          path: "/job/worker",
          query: {
            id,
            jobId,
          },
        });
      },
      async complete(row) {
        const [err] = await toPromise(
          this.$confirm("此操作无法撤销，是否确认完成该任务？", "提示", {
            type: "warning",
          })
        );
        if (err) return;
        const { code, data } = await finishTask(row);
        if (code == 20000) {
          this.$message.success("操作成功");
          this.search(true);
        }
      },
      republish({ id, jobId }) {
        this.$router.push({
          path: `/job/task/create?jobId=${jobId}&flag=true`,
        });
      },
      getTabs() {
        this.tabs.push({ code: "ALL", desc: "全部" });
        this.$dict("JobStatusEnum").forEach((e) => {
          this.tabs.push({ code: e.code, desc: e.desc });
        });
      },
      async search(init) {
        if (init) {
          this.page.current = 1;
        }
        if (this.activeName == "ALL") {
          this.searchForm.jobStatus = undefined;
        }
        const {
          data: { records, total },
        } = await taskList({
          ...this.page,
          ...this.searchForm,
        });
        this.list = records;
        this.page.total = total;
      },
      resetForm() {
        this.searchForm = {};
        this.$refs.datepicker.clearTime();
      },
      handleTabChange(tab) {
        this.searchForm.jobStatus = this.activeName;
        this.search(true);
      },
      getTagType(type) {
        switch (type) {
          case 100:
            return "info";
          case 300:
            return "success";
          case 400:
            return "danger";
          default:
            return "";
        }
      },
      async deleteItem({ id }) {
        const [err] = await toPromise(
          this.$confirm("此操作无法撤销，是否确认删除该任务？", "提示", {
            type: "warning",
          })
        );
        if (err) return;
        await deleteJob({ id });
        this.search();
      },
      async listAllIndustryType() {
        const { data } = await listAllIndustryType()
        this.industryList = data;
        this.industryListOptions = convert(data, 0);
      }
    },
  };
</script>

<style scoped>
  .job-btn {
    margin-right: 5px;
  }
</style>
