<template>
  <div class="box-container page-container">
    <div class="info-box">
      <div class="clearfix">
        <el-button
          el-button
          type="primary"
          class="fr"
          @click="finishTask"
          v-if="task.jobStatus == 200"
          >完成任务</el-button
        >
      </div>
      <div class="info-title">任务信息</div>
      <div class="info-wrapper" v-if="task">
        <div class="info-item">
          <span class="info-label">任务编号：</span>
          <span class="info-value">{{ task.jobId }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">任务名称：</span>
          <span class="info-value">{{ task.jobName }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">行业/岗位：</span>
          <span class="info-value">{{ task.industryName }}/{{ task.workCategoryName }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">任务标签：</span>
          <span class="info-value">
            <el-tag
              class="info-tag"
              v-for="(item, index) in task.tags"
              :key="index"
              >{{ item }}</el-tag
            >
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">任务日期：</span>
          <span class="info-value">
            {{
              task.jobAvalDateType == 100
                ? $dictCode("JobAvalDateTypeEnum", task.jobAvalDateType).desc
                : `${task.jobStartDate} - ${task.jobFinishDate} `
            }}
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">工作时段：</span>
          <span class="info-value">
            {{ `${task.jobTimeStart} - ${task.jobTimeEnd}` }}
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">任务地点：</span>
          <span class="info-value">
            {{
              `${task.jobProvinceName} ${task.jobCityName} ${task.jobAreaName}`
            }}
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">详细地址：</span>
          <span class="info-value">
            {{ task.jobAddress }}
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">任务报酬：</span>
          <span class="info-value" v-if="task.rewardType == 100">
            {{ task.rewardAmount }} /
            {{ $dictCode("PayTypeEnum", task.payType).desc }}
          </span>
          <span class="info-value" v-if="task.rewardType == 200"> 面议 </span>
        </div>
        <div class="info-item">
          <span class="info-label">接单模式：</span>
          <span class="info-value">
            {{ $dictCode("AcceptModeEnum", task.acceptMode).desc }}
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">任务可见性：</span>
          <span class="info-value">
            {{ $dictCode("JobVisibilityEnum", task.scope).desc }}
          </span>
        </div>
      </div>
    </div>
    <div class="info-box">
      <div class="info-title">任务要求</div>
      <div class="info-wrapper" v-if="task">
        <div class="info-item">
          <span class="info-label">性别要求：</span>
          <span class="info-value">{{
            $dictCode("WorkerGenderEnum", task.workerGender).desc
          }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">年龄要求：</span>
          <span class="info-value">{{
            task.workerAgeLimitType == 100
              ? `${task.workerAgeLimitMin} - ${task.workerAgeLimitMax}`
              : "不限"
          }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">学历要求：</span>
          <span class="info-value">{{
            $dictCode("EduBackgroundEnum", task.eduBackground).desc
          }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">技能要求：</span>
          <span class="info-value">{{ task.professionalSkill }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">任务描述：</span>
          <span class="info-value">{{ task.jobDescribe }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">交付标准：</span>
          <span class="info-value">{{ task.deliveryStandard }}</span>
        </div>
      </div>
    </div>
    <!-- <div class="info-box">
      <div class="info-title">人员管理</div>
      <div
        class="info-wrapper"
        v-if="task"
      >
        <el-table :data="task.jobWorkerRecords">
          <el-table-column
            label="姓名"
            prop="workerName"
          ></el-table-column>
          <el-table-column
            label="手机号"
            prop="workerPhone"
          ></el-table-column>
          <el-table-column
            label="身份证"
            prop="workerIdcard"
          >
            <template v-slot="{row}">
              <div>{{row.workerIdcard}}</div>
              <el-button
                type="text"
                @click="workerInfo(row)"
              >查看身份证</el-button>
            </template>
          </el-table-column>
          <el-table-column label="接单模式">
            {{ $dictCode('AcceptModeEnum', task.acceptMode).desc }}
          </el-table-column>
          <el-table-column
            label="任务状态"
            prop="jobStatus"
          >
            <template v-slot="{row, column}">
              {{ $dictCode('JobStatusEnum', row[column.property]).desc }}
            </template>
          </el-table-column>
          <el-table-column
            label="交付状态"
            prop="deliveryStatus"
          >
            <template v-slot="{row, column}">
              {{ $dictCode('DeliveryStatusEnum', row[column.property]).desc }}
            </template>
          </el-table-column>
          <el-table-column
            label="结算状态"
            prop="settleStatus"
          >
            <template v-slot="{row, column}">
              {{ $dictCode('SettleStatusEnum', row[column.property]).desc }}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template v-slot="{row}">
              <el-button
                @click="goToWorker(row)"
                type="text"
              >详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div> -->
  </div>
</template>

<script>
import { jobDetail, workerInfo, finishTask } from "@/api/jobworker";
import { toPromise } from "@/utils";
export default {
  data() {
    return {
      task: {},
    };
  },
  computed: {
    id() {
      return this.$route.query.id;
    },
  },
  mounted() {
    if (!this.id) {
      return this.$router.replace("/job/task");
    }
    this.getDetail();
  },
  methods: {
    async getDetail() {
      const { data } = await jobDetail({ id: this.id });
      if (!!data.jobTag) {
        data.tags = JSON.parse(data.jobTag);
      }
      this.task = data;
    },
    goToWorker(row) {
      this.$router.push({
        path: "/job/worker",
        query: {
          workerName: row.workerName,
          workerPhone: row.workerPhone,
          jobId: row.jobId,
        },
      });
    },
    async workerInfo({ workerIdCardMd5 }) {
      const { data } = await workerInfo({
        workerIdCardMd5: workerIdCardMd5,
      });
      this.$preview([
        this.fileUrl + data.idCardBackUrl,
        this.fileUrl + data.idCardFrontUrl,
      ]);
    },
    async finishTask() {
      let [err] = await toPromise(
        this.$confirm("此操作不可逆，是否继续？", "提示", {
          type: "warning",
        })
      );
      if (err) return;
      await finishTask({ id: this.id });
    },
  },
};
</script>

<style lang="scss" scoped>
.info-box {
  margin: 16px 0 32px;
}
.info-title {
  font-size: 20px;
  font-weight: bolder;
}
.info-wrapper {
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #ddd;
  .info-item {
    width: 30%;
    margin: 16px 8px;
  }
}
.info-tag {
  margin-right: 10px;
}
</style>
