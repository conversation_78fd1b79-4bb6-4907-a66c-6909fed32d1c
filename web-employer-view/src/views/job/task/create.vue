<template>
  <div class="page-container box-container">
    <!-- <el-steps align-center :active="active" finish-status="success">
      <el-step title="配置任务信息"></el-step>
      <el-step title="邀请接单人员"></el-step>
      <el-step title="确认发布"></el-step>
    </el-steps> -->
    <el-button
      v-if="!form1.jobId && active === 1"
      type="primary"
      @click="fastForm">一键填充任务信息
    </el-button>
    <div
      class="form-box form_1"
      v-show="active == 1">
      <el-form
        ref="form1"
        :model="form1"
        label-width="120px"
        :rules="rules">
        <p class="subTitle">任务信息</p>
        <el-form-item
          label="接单模式："
          prop="acceptMode">
          <el-radio-group v-model="form1.acceptMode">
            <el-radio
              v-for="(item, index) in $dict('AcceptModeEnum')"
              :key="index"
              :label="parseInt(item.code)"
            >
              {{ item.desc }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="任务可见性："
          prop="scope">
          <el-radio-group v-model="form1.scope">
            <el-radio
              v-for="(item, index) in $dict('JobVisibilityEnum')"
              :key="index"
              :label="parseInt(item.code)"
            >
              {{ item.desc }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="行业类型："
          prop="industryCode">
          <el-cascader
            :disabled="isShort"
            style="width: 300px"
            filterable
            clearable
            data-index="index"
            :options="industryListOptions"
            :props="{
              expandTrigger: 'hover',
              emitPath: false,
              value: 'industryTypeCode',
              label: 'industryTypeName',
              leaf: 'industryTypeCode',
            }"
            v-model="form1.industryCode"
            @change="
              (e) =>
                handleChange(e, {
                  listName: 'industryList',
                  prop: 'industryTypeCode',
                  field: 'industryName',
                  listProp: 'industryTypeName',
                })
            "
          >
            <template v-slot="{ data }">
              <el-tooltip
                :content="data.industryTypeName"
                :disabled="data.industryTypeName.length < 14"
              >
                <span class="cascader-content-item">
                  {{ data.industryTypeName }}
                </span>
              </el-tooltip>
            </template>
          </el-cascader>
        </el-form-item>
        <el-form-item
          label="岗位类目："
          prop="workCategoryCode">
          <el-select
            v-model="form1.workCategoryCode"
            style="margin-right: 8px"
            @change="workCategoryChange">
            <el-option
              v-for="(item, index) in workCategoryOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="任务名称："
          prop="jobName">
          <el-input
            v-model="form1.jobName"
            clearable></el-input>
        </el-form-item>
        <el-form-item
          label="任务标签："
          prop="jobTag">
          <el-input
            v-model="form1.jobTag"
            clearable
            placeholder="请输入标签并用空格隔开"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="任务日期："
          prop="jobAvalDateType">
          <el-select
            v-model="form1.jobAvalDateType"
            style="margin-right: 8px">
            <el-option
              v-for="(item, index) in $dict('JobAvalDateTypeEnum')"
              :key="index"
              :label="item.desc"
              :value="parseInt(item.code)"
            ></el-option>
          </el-select>
          <div
            style="display: inline-block"
            v-if="form1.jobAvalDateType == 200"
          >
            <date-picker
              :is-show-fast-time="false"
              type="date"
              picker="separate"
              :start-time.sync="form1.jobStartDate"
              :end-time.sync="form1.jobFinishDate"
              :use-option="false"
            ></date-picker>
          </div>
        </el-form-item>
        <el-form-item
          label="工作时段："
          prop="jobTimeEnd">
          <el-time-picker
            v-model="form1.jobTimeStart"
            value-format="HH:mm:ss"
            :picker-options="{
              selectableRange: '00:00:00 - 23:59:59',
            }"
            :default-value="timeDefaultShow(1)"
          >
          </el-time-picker>
          -
          <el-time-picker
            v-model="form1.jobTimeEnd"
            value-format="HH:mm:ss"
            :picker-options="{
              selectableRange: '00:00:00 - 23:59:59',
            }"
            :default-value="timeDefaultShow(2)"
          >
          </el-time-picker>
        </el-form-item>
        <el-form-item
          label="任务地点："
          prop="jobAreaName">
          <address-select
            :disabled="isShort"
            :default-address="(isShort || jobId) ?
              (form1.jobProvinceName + '/' + form1.jobCityName + '/' + form1.jobAreaName) : ''"
            :province.sync="form1.jobProvinceName"
            :city.sync="form1.jobCityName"
            :town.sync="form1.jobAreaName"
            @change="onAddressChange"
          ></address-select>
        </el-form-item>
        <el-form-item
          label="详细地址："
          prop="jobAddress">
          <el-input
            :disabled="isShort"
            clearable
            v-model="form1.jobAddress"></el-input>
        </el-form-item>
        <el-form-item
          label="招收人数："
          prop="workerNum">
          <el-input
            clearable
            :value="form1.workerNum"
            @input="handleNumberInput(form1, 'workerNum', $event)"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="报酬类型："
          prop="rewardType">
          <el-select
            v-model="form1.rewardType"
            :disabled="isShort">
            <el-option
              v-for="(item, index) in $dict('RewardType2Enum')"
              :key="index"
              :label="item.desc"
              :value="parseInt(item.code)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form1.rewardType == 100"
          label="金额："
          prop="rewardAmount"
        >
          <el-input
            style="width: 300px"
            clearable
            v-model="form1.rewardAmount"
          ></el-input>
          /
          <el-select v-model="form1.payType">
            <el-option
              v-for="(item, index) in $dict('PayTypeEnum')"
              :key="index"
              :label="item.desc"
              :value="parseInt(item.code)"
            ></el-option>
          </el-select>
        </el-form-item>

        <!-- 任务要求 -->
        <p class="subTitle">任务要求</p>
        <el-form-item
          label="性别："
          prop="workerGender">
          <el-radio-group v-model="form1.workerGender">
            <el-radio
              v-for="(item, index) in $dict('WorkerGenderEnum')"
              :key="index"
              :label="parseInt(item.code)"
            >
              {{ item.desc }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="年龄："
          prop="workerAgeLimitType">
          <el-select
            v-model="form1.workerAgeLimitType"
            style="margin-right: 8px"
          >
            <el-option
              v-for="(item, index) in $dict('WorkerAgeLimitTypeEnum')"
              :key="index"
              :label="item.desc"
              :value="parseInt(item.code)"
            ></el-option>
          </el-select>
          <template v-if="form1.workerAgeLimitType == 100">
            <el-input
              style="width: 200px"
              v-model="form1.workerAgeLimitMin"
              clearable
            ></el-input
            >
            至
            <el-input
              style="width: 200px"
              v-model="form1.workerAgeLimitMax"
              clearable
            ></el-input>
          </template>
        </el-form-item>
        <el-form-item
          label="学历："
          prop="eduBackground">
          <el-select v-model="form1.eduBackground">
            <el-option
              v-for="(item, index) in $dict('EduBackgroundEnum')"
              :key="index"
              :label="item.desc"
              :value="parseInt(item.code)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="专业技能：">
          <el-input
            clearable
            v-model="form1.professionalSkill"></el-input>
        </el-form-item>
        <el-form-item
          label="任务描述："
          prop="jobDescribe">
          <el-input
            type="textarea"
            v-model="form1.jobDescribe"
            :rows="5"
            :disabled="isShort"
          ></el-input>
        </el-form-item>
        <el-form-item label="交付标准：">
          <el-input
            type="textarea"
            v-model="form1.deliveryStandard"
            :rows="5"
            :disabled="isShort"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>

    <div v-if="active == 2">
      <div class="text-center result-box">
        <svg-icon
          icon-class="waiting"
          style="font-size: 100px"></svg-icon>
        <p class="result-title">任务发布成功</p>
        <p>等待平台审批</p>
        <div class="result-detail">
          <p>任务名称：{{ result.jobName }}</p>
          <p>
            任务日期：{{ result.jobAvalDateType == 100 ? "长期" : `${ result.jobStartDate } - ${ result.jobFinishDate }` }}
          </p>
          <p>招收人数：{{ result.workerNum }}</p>
          <!-- <p>创建时间：{{ result.createTime }}</p> -->
          <p>更新时间：{{ result.updateTime }}</p>
        </div>
      </div>
    </div>

    <div class="form-btn-group">
      <el-button
        v-if="active == 1"
        type="primary"
        :loading="loading"
        @click="nextStep"
      >发布
      </el-button>
      <router-link
        to="/job/task"
        v-if="active == 2">
        <el-button>返回任务列表</el-button>
      </router-link>
    </div>

    <el-dialog
      :visible.sync="visible"
      :show-close="false"
      title="任务发布须知"
    >
      <div>本服务用于匹配与您发布任务条件相符的自由职业者，自由职业者实际所承接的任务须合法合规，不得违反法律法规及社会公序良俗（包括但不限于涉嫌洗钱、逃税、漏税、传销、赌博、暴力、淫秽、恐怖、非法集资等行为）</div>
      <div
        class="text-center"
        style="margin-top: 16px">
        <el-button @click="visible = false">取 消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="confirm">我接受
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { convert, toPromise } from "@/utils";
import { validateParams } from "@/utils/validate";
import { listAllIndustryType } from "@/api/business";
import { addJob, getByJobId, updateJob } from "@/api/jobworker";
import { getEmployerCooperate, getEmployerPosition } from "@/api/merchant";

export default {
  name: "taskCreate",
  data(vm) {
    return {
      isShort: false, // 是否一键填充
      merchantInfo: null,
      active: 1,
      form1: {},
      rules: {
        industryCode: [
          { required: true, message: "请选择行业类别", trigger: "change" },
        ],
        workCategoryCode: [
          { required: true, message: "请选择岗位类目", trigger: "change" },
        ],
        jobName: [
          { required: true, message: "请输入任务名称", trigger: "blur" },
        ],
        jobAvalDateType: [
          { required: true, message: "请选择任务日期", trigger: "change" },
          {
            validator: function (rule, val, cb) {
              if (val == 100) {
                cb();
              } else {
                if (vm.form1.jobStartDate && vm.form1.jobFinishDate) {
                  cb();
                } else {
                  cb(new Error("请选择任务日期"));
                }
              }
            },
            trigger: "blur",
          },
        ],
        jobTimeEnd: [
          { required: true, message: "请选择工作时段", trigger: "blur" },
          {
            validator: (rule, val, cb) => {
              if (vm.form1.jobTimeEnd && vm.form1.jobTimeStart) {
                cb();
              } else {
                cb(new Error("请选择工作时段"));
              }
            },
            trigger: "blur",
          },
        ],
        jobAreaName: [
          { required: true, message: "请选择任务地点", trigger: ["blur", "change"] },
        ],
        jobAddress: [
          { required: true, message: "请输入详细地址", trigger: "blur" },
        ],
        rewardType: [
          { required: true, message: "请选择报酬类型", trigger: "change" },
        ],
        rewardAmount: [
          { required: true, message: "请输入金额", trigger: "blur" },
          {
            validator: validateParams({
              type: "Number",
              msg: "请输入正确格式金额",
            }),
            trigger: "blur",
          },
        ],
        workerGender: [
          { required: true, message: "请选择性别", trigger: ["blur", "change"] },
        ],
        workerAgeLimitType: [
          { required: true, message: "请选择年龄", trigger: "change" },
          {
            validator: (rule, val, cb) => {
              if (val == 200) {
                return cb();
              }
              let regex = /^[1-9]\d*|0$/;

              if (!(vm.form1.workerAgeLimitMin && vm.form1.workerAgeLimitMax)) {
                cb(new Error("请输入年龄"));
              } else if (
                !regex.test(vm.form1.workerAgeLimitMin) ||
                !regex.test(vm.form1.workerAgeLimitMax)
              ) {
                cb(new Error("请输入正确的人数"));
              } else if (
                vm.form1.workerAgeLimitMin > vm.form1.workerAgeLimitMax
              ) {
                cb(new Error("最大人数不能小于最小人数"));
              } else {
                cb();
              }
            },
            trigger: "change",
          },
        ],
        workerNum: [
          { required: true, message: "请输入招收人数", trigger: "blur" },
        ],
        eduBackground: [
          { required: true, message: "请选择学历", trigger: "change" },
        ],
        jobDescribe: [
          { required: true, message: "请输入任务描述", trigger: "blur" },
        ],
        acceptMode: [
          { required: true, message: "请选择接单模式", trigger: "change" },
        ],
        scope: [
          { required: true, message: "请选择任务可见性", trigger: "change" },
        ],
      },

      industryList: [],
      industryListOptions: [],
      workCategoryList: [],
      workCategoryOptions: [],
      show: false,
      uploadFiles: [],

      loading: false,
      result: {},

      visible: false
    };
  },
  computed: {
    jobId() {
      return this.$route.query.jobId || ''
    }
  },
  mounted() {
    this.getData();
    if (this.jobId) {
      this.getDetail();
    }
    this.getPositionList();
  },
  methods: {
    workCategoryChange(val) {
      let item = this.workCategoryOptions.find(item => item.value = val)
      this.form1.workCategoryName = item.label
    },
    async getPositionList() {
      const { data } = await getEmployerPosition();
      this.workCategoryOptions = data.map((e) => {
        return {
          label: e.workCategoryName,
          value: e.workCategoryCode,
        };
      });
    },
    async getDetail() {
      const { data } = await getByJobId(this.jobId);
      if (this.$route.query.flag) {
        data.jobStatus = 100;
      }
      let tag = JSON.parse(data.jobTag);
      tag && (data.jobTag = tag.join(" "));
      this.form1 = data;
    },
    getData() {
      listAllIndustryType().then(({ data }) => {
        this.industryList = data;
        this.industryListOptions = convert(data, 0);
      });
      // listAllWorkCategory().then(({ data }) => {
      //   this.workCategoryList = data;
      //   this.workCategoryOptions = convert(data, 0);
      // });
    },
    handleChange(val, { listName, prop, field, listProp }) {
      for (let item of this[listName]) {
        if (val == item[prop]) {
          this.$set(this.form1, field, item[listProp]);
          break;
        }
      }
    },
    onAddressChange(val) {
      this.$set(this.form1, "jobProvinceNo", val[0][0]);
      this.$set(this.form1, "jobCityNo", val[1][0]);
      this.$set(this.form1, "jobAreaNo", val[2][0]);
    },
    async nextStep() {
      let form = this.$refs[`form${ this.active }`];
      const [err] = await toPromise(form.validate());
      if (err) {
        return;
      }
      if (this.active == 1) {
        this.visible = true
        return
      }
      this.active++;
    },
    timeDefaultShow(type) {
      let today = new Date();
      type == 1 ? today.setHours(0, 0, 0) : today.setHours(23, 59, 59);
      return today;
    },
    // 一键填充
    async fastForm() {
      if (!this.merchantInfo) {
        await this.getMerchantInfo()
      }
      this.isShort = true
      this.form1 = {
        "acceptMode": 200,
        "scope": 100,
        "industryCode": this.merchantInfo.industryTypeCode,
        "industryName": this.merchantInfo.industryTypeName,
        "workCategoryCode": this.merchantInfo.positionVoList[0].workCategoryCode,
        "workCategoryName": this.merchantInfo.positionVoList[0].workCategoryName,
        "jobAvalDateType": 100,
        "jobTimeStart": "00:00:00",
        "jobTimeEnd": "23:59:59",
        "jobAddress": this.merchantInfo.managementAddrDetail,
        "workerNum": this.merchantInfo.workerNum,
        "rewardType": 200,
        "workerGender": 2,
        "workerAgeLimitType": 200,
        "eduBackground": -1,
        "jobDescribe": this.merchantInfo.positionVoList[0].serviceDesc,
        "deliveryStandard": this.merchantInfo.positionVoList[0].serviceDesc,
        "jobProvinceName": this.merchantInfo.managementAddrProvince,
        "jobCityName": this.merchantInfo.managementAddrCity,
        "jobAreaName": this.merchantInfo.managementAddrTown,
      }
    },
    async getMerchantInfo() {
      const { data } = await getEmployerCooperate()
      this.merchantInfo = data
    },
    async confirm() {
      if (this.active == 1) {
        if (this.form1.jobId) {
          this.loading = true;
          const [error, result = {}] = await toPromise(updateJob(this.form1));
          this.loading = false;
          if (error) return;
          this.result = result.data;
        } else {
          this.loading = true;
          const [error, result = {}] = await toPromise(addJob({
            ...this.form1,
            auto: this.isShort
          }));
          this.loading = false;
          if (error) return;
          this.result = result.data;
        }
      }
      this.visible = false
      if (this.isShort) {
        this.$message.success('任务发布成功')
        return this.$router.push('/job/task')
      }
      this.active++;

    }
  },
};
</script>

<style
  lang="scss"
  scoped>
.form-box {
  margin: 20px 0;
}

.result-box {
  margin: 40px;
}
</style>
