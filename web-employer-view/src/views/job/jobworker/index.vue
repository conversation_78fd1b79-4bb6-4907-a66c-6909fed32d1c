<template>
  <div>
    <div
      class="job-header flex-wrapper"
      v-if="task">
      <div class="job-name">
        {{ task.jobName }}
        <el-tag style="margin-left: 8px">
          {{ $dictCode("JobStatusEnum", task.jobStatus).desc }}
        </el-tag>
      </div>
      <div>
        <el-button
          type="primary"
          @click="showMiniProgramCode">生成任务码
        </el-button>
        <el-button
          type="primary"
          @click="goInfo">任务信息
        </el-button>
        <el-button
          v-if="task.jobStatus == 200"
          type="primary"
          @click="finishTask"
        >完成任务
        </el-button>
      </div>
    </div>
    <div class="box-container">
      <div class="search-container">
        <div class="flex-container">
          <div class="flex-wrapper search-wrapper">
            <div class="flex-item">
              <span class="flex-item__label">姓名：</span>
              <el-input
                v-model="searchForm.workerName"
                placeholder="请输入姓名"
                clearable
              ></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">手机号：</span>
              <el-input
                v-model="searchForm.workerPhone"
                placeholder="请输入手机号"
                clearable
              ></el-input>
            </div>
          </div>
          <div
            v-if="activeName == 'ALL'"
            class="flex-wrapper search-wrapper">
            <div class="flex-item">
              <span class="flex-item__label">任务状态：</span>
              <el-select
                v-model="searchForm.jobStatus"
                filterable
                clearable>
                <el-option
                  v-for="(item, index) in $dict('JobWorkerStatusEnum')"
                  :key="index"
                  :label="item.desc"
                  :value="item.code"
                ></el-option>
              </el-select>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">交付状态：</span>
              <el-select
                v-model="searchForm.deliveryStatus"
                filterable
                clearable
              >
                <el-option
                  v-for="(item, index) in $dict('DeliveryStatusEnum')"
                  :key="index"
                  :label="item.desc"
                  :value="item.code"
                ></el-option>
              </el-select>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">结算状态：</span>
              <el-select
                v-model="searchForm.settleStatus"
                filterable
                clearable>
                <el-option
                  v-for="(item, index) in $dict('SettleStatusEnum')"
                  :key="index"
                  :label="item.desc"
                  :value="item.code"
                ></el-option>
              </el-select>
            </div>
          </div>

          <div class="flex-wrapper search-wrapper">
            <div class="search-btn-group">
              <el-button
                type="primary"
                @click="handleQuery"
              >查询
              </el-button>
              <el-button
                type="primary"
                @click="clearField"
              >重置
              </el-button>
              <el-button
                v-show="activeName == $dictFlag('JobWokerTagEnum', 'PENDING_SETTLEMENT').code"
                type="primary"
                @click="settle"
              >批量结算
              </el-button>
              <el-button
                v-show="activeName == $dictFlag('JobWokerTagEnum', 'PENDING_SETTLEMENT').code || activeName == $dictFlag('JobWokerTagEnum', 'SETTLED').code"
                @click="batchdownload('resFile')"
              >批量下载成果验收单
              </el-button>
              <el-button
                v-show="activeName == $dictFlag('JobWokerTagEnum', 'PENDING_SETTLEMENT').code || activeName == $dictFlag('JobWokerTagEnum', 'SETTLED').code"
                @click="batchdownload('devFile')"
              >批量下载交付明细表
              </el-button>

              <el-button
                @click="toggleUploadDialog"
              >批量导入人员
              </el-button>
              <el-button @click="openTransfer">指派已签约人员</el-button>
              <el-button
                v-show="activeName == $dictFlag('JobWokerTagEnum', 'WAIT_EMPLOY').code"
                type="primary"
                @click="handleWorker({}, true)"
              >批量录用
              </el-button>
              <el-button
                v-show="activeName == $dictFlag('JobWokerTagEnum', 'WAIT_EMPLOY').code"
                type="danger"
                @click="handleWorker({}, false)"
              >批量拒绝
              </el-button>
              <el-button
                v-show="activeName == $dictFlag('JobWokerTagEnum', 'PROCESSING').code"
                type="primary"
                @click="complete"
              >批量完成
              </el-button>
              <el-button
                v-show="activeName == $dictFlag('JobWokerTagEnum', 'TO_BE_CONFIRMED').code"
                type="primary"
                @click="approvedSuccess"
              >批量审核通过
              </el-button>
              <el-button
                v-show="activeName == $dictFlag('JobWokerTagEnum', 'PENDING_SETTLEMENT').code"
                @click="exportPendingList">导出待结算名单
              </el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="content-container">
        <el-tabs
          v-model="activeName"
          class="tabs"
          @tab-click="handleClick">
          <el-tab-pane
            v-for="(item, index) in tabs"
            :key="index"
            class="tab-item"
            :label="item.desc"
            :name="item.code"
          ></el-tab-pane>
        </el-tabs>
        <el-table
          :data="response.data"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            width="55" />
          <el-table-column
            label="任务名称"
            prop="jobName"
            width="120">
            <template v-slot="{ row, column }">
              {{ row[column.property] || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="姓名"
            prop="workerName"
            width="100">
            <template v-slot="{ row, column }">
              {{ row[column.property] || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="手机号"
            prop="workerPhone"
            width="180">
            <template v-slot="{ row, column }">
              {{ row[column.property] || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="身份证"
            prop="workerIdcard"
            width="180">
            <template v-slot="{ row }">
              <div
                v-if="row.workerIdcard != null"
                class="id-card-box">
                <div>{{ row.workerIdcard }}</div>
                <div>
                  <el-button
                    v-if="row.workerIdcard != null  && row.authStatus == 100"
                    type="text"
                    @click="getWorkerInfo(row)"
                  >查看证件照片
                  </el-button
                  >
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            label="认证状态"
            prop="authStatus">
            <template v-slot="{ row }">
              {{ $dictCode("WorkerAuthStatus", row.authStatus).desc || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="性别"
            prop="gender">
            <template v-slot="{ row }">
              {{ $dictCode("WorkerGenderEnum", row.gender).desc || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="学历"
            prop="eduBackground">
            <template v-slot="{ row }">
              {{ $dictCode("EduBackgroundEnum", row.eduBackground).desc || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="任务状态"
            prop="jobStatus">
            <template v-slot="{ row }">
              {{ $dictCode("JobWorkerStatusEnum", row.jobStatus).desc || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="交付状态"
            prop="deliveryStatus">
            <template v-slot="{ row }">
              {{ $dictCode("DeliveryStatusEnum", row.deliveryStatus).desc || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="结算状态"
            prop="settleStatus">
            <template v-slot="{ row }">
              {{ $dictCode("SettleStatusEnum", row.settleStatus).desc || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="接受任务时间"
            prop="jobAcceptTime"
            width="180"
          >
            <template v-slot="{ row, column }">
              {{ row[column.property] || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="完成任务时间"
            prop="jobFinishTime"
            width="180"
          >
            <template v-slot="{ row, column }">
              {{ row[column.property] || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            class-name="small-padding fixed-width"
            width="200"
            fixed="right"
          >
            <template v-slot="{row}">
              <div v-if="row.jobStatus == 0">
                <el-button
                  size="mini"
                  type="text"
                  style="color: red"
                  @click="cancel(row)"
                >取消邀请
                </el-button>
              </div>
              <el-button
                type="text"
                v-if="row.authStatus == 101"
                @click="openId(row)">上传身份证
              </el-button>
              <div v-if="row.jobStatus == 100">
                <el-button
                  size="mini"
                  type="text"
                  @click="handleWorker(row, true)"
                >通过
                </el-button>
                <el-button
                  style="color: red; margin-left: 8px"
                  size="mini"
                  type="text"
                  @click="handleWorker(row, false)"
                >拒绝
                </el-button>
              </div>
              <div
                v-if="
                  row.jobStatus == 300 &&
                  (row.deliveryStatus == 100 ||
                    row.deliveryStatus == 300)
                ">
                <el-button
                  size="mini"
                  type="text"
                  @click="submitResult(row)"
                >上传交付成果
                </el-button>
                <el-button
                  size="mini"
                  type="text"
                  @click="complete(row)"
                >完成任务
                </el-button>
              </div>
              <div
                class="table-btn-box"
                v-if="row.jobStatus >= 300 && (row.settleStatus == 200 || row.settleStatus == 100)"
              >
                <el-button
                  size="mini"
                  type="text"
                  @click="see(row)"
                >查看交付结果
                </el-button>
                <el-button
                  v-if="!!row.resultSignatureUrl"
                  size="mini"
                  type="text"
                  @click="downloadJobFile(row, 1)"
                >下载验收单
                </el-button>
                <el-button
                  v-if="!!row.deliverSignatureUrl"
                  size="mini"
                  type="text"
                  @click="downloadJobFile(row, 2)"
                >下载交付明细表
                </el-button>
              </div>
              <div>
                <el-button
                  v-if="row.deliveryStatus == 200"
                  size="mini"
                  type="text"
                  @click="toApproved(row)"
                >审核交付成果
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-footer class="pagination-container">
          <el-pagination
            v-if="response"
            :total="totalRecord"
            :current-page="pageCurrent"
            :page-size="pageSize"
            :page-sizes="[10, 50]"
            layout="total, sizes, prev, pager, next, slot, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </el-footer>
      </div>

      <el-drawer
        class="job-drawer"
        title="查看任务交付成果"
        :visible.sync="drawer"
        size="800px"
        @close="closeJob"
      >
        <div class="drawer-box">
          <div class="drawer-nav">
            <div
              v-for="(item, index) in navs"
              :key="index"
              :href="item.id"
              class="nav-item"
              :class="{ 'drawer-active': active == index }"
              @click="changeNav(index)"
            >
              {{ item.name }}
            </div>
          </div>
          <div class="drawer-contain">
            <div
              id="job"
              name="job"
              class="drawer-title">任务信息
            </div>
            <div class="drawer-form">
              <div class="drawer-form-item">
                <div class="drawer-form-label">任务编号：</div>
                <div class="drawer-form-value">{{ dataForm.jobId }}</div>
              </div>
              <div class="drawer-form-item">
                <div class="drawer-form-label">任务名称：</div>
                <div class="drawer-form-value">{{ dataForm.jobName }}</div>
              </div>
              <div class="drawer-form-item">
                <div class="drawer-form-label">行业/岗位：</div>
                <div class="drawer-form-value">{{ dataForm.industryName }}</div>
              </div>
              <div class="drawer-form-item">
                <div class="drawer-form-label">任务日期：</div>
                <div class="drawer-form-value">
                  {{
                    $dictCode("JobAvalDateTypeEnum", dataForm.jobAvalDateType)
                      .desc
                  }}
                </div>
              </div>
              <div class="drawer-form-item">
                <div class="drawer-form-label">任务报酬：</div>
                <div
                  class="drawer-form-value"
                  v-if="dataForm.rewardType == 100"
                >
                  {{ dataForm.rewardAmount }}/{{ $dictCode("PayTypeEnum", dataForm.payType).desc }}
                </div>
                <div
                  class="drawer-form-value"
                  v-if="dataForm.rewardType == 200"
                >
                  面议
                </div>
              </div>
              <div class="drawer-form-item">
                <div class="drawer-form-label">任务描述：</div>
                <div class="drawer-form-value">{{ dataForm.jobDescribe }}</div>
              </div>
              <div class="drawer-form-item">
                <div class="drawer-form-label">交付标准：</div>
                <div class="drawer-form-value">
                  {{ dataForm.deliveryStandard }}
                </div>
              </div>
            </div>
            <div
              id="worker"
              name="worker"
              class="drawer-title">交付物
            </div>
            <div class="drawer-form">
              <div class="drawer-form-item">
                <div class="drawer-form-label">工作结果：</div>
                <div class="drawer-form-value">
                  {{ dataForm.deliveryContent }}
                </div>
              </div>
              <div class="drawer-form-item">
                <div class="drawer-form-label">交付物：</div>
                <div class="drawer-form-value">
                  <el-upload
                    v-if="fileList.length != 0"
                    action=""
                    disabled
                    list-type="picture-card"
                    :on-preview="handlePictureCardPreview"
                    :file-list="fileList"
                    class="el-hide"
                  >
                  </el-upload>
                  <el-dialog
                    append-to-body
                    :visible.sync="dialogVisible"
                    :before-close="
                      () => {
                        dialogVisible = false;
                      }
                    "
                  >
                    <img
                      width="100%"
                      :src="dialogImageUrl"
                      alt="" />
                  </el-dialog>
                </div>
              </div>
            </div>
            <div
              v-if="dataForm.deliveryStatus == 200"
              class="drawer-footer">
              <el-button
                type="primary"
                size="small"
                @click="openApp(true)"
              >通过
              </el-button
              >
              <el-button
                type="danger"
                size="small"
                @click="openApp(false)"
              >驳回
              </el-button
              >
            </div>
          </div>
        </div>
      </el-drawer>

      <el-dialog
        title="上传交付成果"
        :close-on-click-modal="false"
        :visible.sync="dialogFormVisible"
      >
        <el-form
          :model="form"
          :rules="rules"
          ref="form">
          <el-form-item
            label="任务结果"
            :label-width="formLabelWidth"
            prop="deliveryContent"
          >
            <el-input
              class="form-width"
              type="textarea"
              v-model="form.deliveryContent"
              autocomplete="off"
              :rows="2"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="附件"
            :label-width="formLabelWidth"
            prop="attachment"
          >
            <file-upload
              :max="20"
              :urls.sync="urls"
              accept=".png,.jpg,.jpeg"
              @change="onChange"
            >
            </file-upload>
          </el-form-item>
        </el-form>
        <div
          slot="footer"
          class="dialog-footer">
          <el-button @click="close">取 消</el-button>
          <el-button
            type="primary"
            @click="submit">确 定
          </el-button>
        </div>
      </el-dialog>

      <el-dialog
        :visible.sync="show"
        :before-close="toggleUploadDialog"
        :close-on-click-modal="false"
        title="批量导入人员"
      >
        <div>
          下载
          <el-button
            type="text"
            @click="downloadTemplate"
          >批量导入模板
          </el-button
          >
          并按规范填写人员信息
        </div>
        <el-upload
          class="upload-block"
          drag
          :file-list="uploadFiles"
          :action="baseUrl + '/job/uploadJobMember'"
          :headers="uploadHeader"
          :on-success="handleUploadSuccess"
          :data="{ id: id }"
          :before-upload="
            validateUploadFile({
              img: false,
              pdf: false,
              word: false,
              addition: ['xlsx'],
              size: 5,
            })
          "
          accept=".xlsx"
          ref="upload"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">
            将文件拖到此处上传，或<em>点击上传</em>
          </div>
        </el-upload>
        支持.xlsx格式文件，大小不能超过5M
      </el-dialog>

      <el-dialog
        :title="title"
        :close-on-click-modal="false"
        :visible.sync="appShow"
      >
        <el-form
          :model="appform"
          :rules="apprules"
          ref="appform">
          <el-form-item
            label="备注"
            :label-width="formLabelWidth"
            prop="failReason"
          >
            <el-input
              class="form-width"
              type="textarea"
              v-model="appform.failReason"
              autocomplete="off"
              :rows="4"
            ></el-input>
          </el-form-item>
        </el-form>
        <div
          slot="footer"
          class="dialog-footer">
          <el-button @click="appclose">取 消</el-button>
          <el-button
            type="primary"
            @click="appsubmit">确 定
          </el-button>
        </div>
      </el-dialog>

      <id-upload-dialog
        ref="idUpload"
        @change="onUploadId">
      </id-upload-dialog>
    </div>


    <!--  穿梭框-->
    <el-dialog
      :visible.sync="showTransfer"
      title="指派已签约人员"
      :before-close="beforeTransferClose"
      width="800px"
    >
      <div class="text-center">
        <el-transfer
          style="text-align: left; display: inline-block"
          v-model="selectedMembers"
          filterable
          :data="memberList"
          :titles="['已签约用户列表', '已选择']"
          :button-texts="['取消选择', '选择']"
          :props="{
            key: 'key',
            label: 'namePhone'
          }"
          filter-placeholder="请输入姓名或手机号"
        >
          <template v-slot="{option}">
            <span>
              {{ option.name }} - {{ option.phone }}
            </span>
          </template>
        </el-transfer>
      </div>

      <template v-slot:footer>
        <el-button @click="showTransfer = false">取消</el-button>
        <el-button
          type="primary"
          :disabled="selectedMembers.length === 0"
          :loading="loading"
          @click="addSignedMember"
        >确认
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  addIDCardImages,
  addSignedMember,
  approved,
  batchApprovedSuccess,
  batchUpdateStatus,
  completeJobWorker,
  deleteWorker,
  employ,
  findById,
  finishTask,
  getAppQrCode,
  getJobworker,
  getSignedMembers,
  jobDetail,
  submit,
  workerInfo
} from "@/api/jobworker";
import FileUpload from "@/components/FileUpload";
import IdUploadDialog from "@/components/IdUpload/dialog";


import { toPromise } from "@/utils";
import { getToken } from "@/utils/loginToken";

export default {
  components: {
    IdUploadDialog,
    FileUpload,
  },
  data() {
    return {
      searchForm: {},
      response: {
        // 查询结果
        data: [],
      },
      totalRecord: 0,
      pageSize: 10,
      pageCurrent: 1,

      multiple: true,
      ids: [],
      // 非单个禁用
      single: true,
      tabs: [],
      activeName: "ALL",
      drawer: false,
      dataForm: {},
      fileList: [],
      dialogImageUrl: "",
      dialogVisible: false,
      navs: [
        {
          id: "#job",
          name: "任务信息"
        },
        {
          id: "#worker",
          name: "交付物"
        },
      ],
      active: 0,

      workerId: null,
      rules: {
        deliveryContent: [
          {
            required: true,
            message: "请输入备注",
            trigger: "blur"
          },
        ],
        attachment: [
          {
            required: true,
            message: "请选择附件",
            trigger: "change"
          },
        ],
      },
      dialogFormVisible: false,
      form: {
        deliveryContent: "",
        attachment: [],
      },
      formLabelWidth: "80px",
      appform: {
        failReason: "",
      },
      apprules: {
        failReason: [
          {
            required: true,
            message: "请输入备注",
            trigger: "blur"
          },
        ],
      },
      task: null,
      show: false,
      uploadFiles: [],
      urls: [],
      appShow: false,
      title: "",
      appEvent: null,
      appWorkerId: null,

      showTransfer: false, // 穿梭框显示控制
      memberList: [], // 已签约名单
      selectedMembers: [], // 选中的
      loading: false,
    };
  },
  computed: {
    id() {
      return this.$route.query.id || 0;
    },
    jobId() {
      return this.$route.query.jobId || 0;
    },
  },
  mounted() {
    this.getList();
    this.getTabs();
    this.getDetail();
  },
  methods: {
    appclose() {
      this.title = "";
      this.appWorkerId = null;
      this.appEvent = null;
      this.appform.failReason = "";
      this.appShow = false;
    },
    openApp(event) {
      if (event) {
        this.title = "审批通过";
      } else {
        this.title = "审批驳回";
      }
      let dict = this.$dictCode("ApproveEvent", event);
      this.appEvent = dict.flag;
      this.appShow = true;
    },
    async cancel(row) {
      let [err] = await toPromise(
        this.$confirm("此操作不可逆，是否继续？", "提示", {
          type: "warning",
        })
      );
      if (err) return;
      const { data } = await deleteWorker(row.id);
      this.$message.success(data);
      this.handleQuery();
    },
    onChange(files) {
      this.form.attachment = files.map((e) => {
        return e.replace(this.fileUrl, "");
      });
    },
    async submitResult(row) {
      this.workerId = row.id;
      if (row.deliveryStatus == 300) {
        const { data } = await findById(row.id);

        this.urls = JSON.parse(JSON.stringify(data.attachment));
        this.form = {
          deliveryContent: data.deliveryContent,
          attachment: JSON.parse(JSON.stringify(data.attachment)),
        };
      }
      this.dialogFormVisible = true;
    },
    submit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let params = {
            ...this.form,
            id: this.workerId,
          };
          submit(params).then((res) => {
            if (res.code == 20000) {
              this.$message.success("操作成功");
              this.handleQuery();
              this.close();
            }
          });
        }
      });
    },
    appsubmit() {
      this.$refs["appform"].validate((valid) => {
        if (valid) {
          let params = {
            id: this.appWorkerId,
            event: this.appEvent,
            failReason: this.appform.failReason,
          };
          approved(params).then((res) => {
            if (res.code == 20000) {
              this.$message.success("审批成功");
              this.appShow = false;
              this.drawer = false;
              this.handleQuery();
            }
          });
        }
      });
    },
    close() {
      this.form = {};
      this.urls = [];
      this.workerId = null;
      this.dialogFormVisible = false;
    },
    async toApproved(row) {
      this.drawer = true;
      this.appWorkerId = row.id;
      const { data } = await findById(row.id);
      this.dataForm = data;
      if (data.attachment != null) {
        let files = data.attachment;
        let fileResetList = [];
        for (let i = 0; i < files.length; i++) {
          const item = files[i];
          const fileMsg = await this.formatFileMsg(item);
          fileResetList.push({
            name: fileMsg.name,
            url: fileMsg.fileUrl
          });
        }
        this.fileList = fileResetList;
      }
    },
    changeNav(index) {
      this.active = index;
    },
    closeJob() {
      this.dataForm = {};
      this.fileList = [];
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    async see(row) {
      this.drawer = true;
      const { data } = await findById(row.id);
      this.dataForm = data;
      if (data.attachment != null) {
        let files = data.attachment;
        this.fileList = files.map((item) => {
          return {
            name: "",
            url: this.fileUrl + item,
          };
        });
      }
    },
    settle(row) {
      const ids = row.id || this.ids;
      if (row.id == undefined && this.ids.length == 0) {
        this.$message.error("请选择雇员");
        return;
      }
      let params = {
        column: "settle_status",
        value: this.$dictFlag("SettleStatusEnum", "SETTLED").code,
      };
      batchUpdateStatus(ids, params).then((res) => {
        if (res.code == 20000) {
          this.$message.success("操作成功");
          this.handleQuery();
        }
      });
    },
    approvedSuccess(row) {
      const ids = row.id || this.ids;
      if (row.id == undefined && this.ids.length == 0) {
        this.$message.error("请选择雇员");
        return;
      }
      batchApprovedSuccess(ids).then((res) => {
        if (res.code == 20000) {
          this.$message.success("操作成功");
          this.handleQuery();
        }
      });
    },
    async complete(row) {
      let [err] = await toPromise(
        this.$confirm("此操作不可逆，是否继续？", "提示", {
          type: "warning",
        })
      );
      if (err) return;
      const ids = row.id || this.ids;
      if (row.id == undefined && this.ids.length == 0) {
        this.$message.error("请选择雇员");
        return;
      }
      completeJobWorker(ids).then((res) => {
        if (res.code == 20000) {
          this.$message.success("操作成功");
          this.handleQuery();
        }
      });
    },
    batchdownload(flag) {
      const ids = this.ids;
      if (this.ids.length == 0) {
        this.$message.error("请选择雇员");
        return;
      }
      window.open(`${ this.baseUrl }/download/jobworker/${ flag }/${ ids }?x-token=${ getToken() }`);
    },
    async handleWorker(row, flag) {
      let [err] = await toPromise(
        this.$confirm("此操作不可逆，是否继续？", "提示", {
          type: "warning",
        })
      );
      if (err) return;
      const ids = row.id || this.ids;
      if (row.id == undefined && this.ids.length == 0) {
        this.$message.error("请选择雇员");
        return;
      }
      let dict = this.$dictCode("ApproveEvent", flag);
      employ(ids, dict.flag).then((res) => {
        if (res.code == 20000) {
          this.$message.success("操作成功");
          this.handleQuery();
        }
      });
    },
    getTabs() {
      this.tabs = this.$dict("JobWokerTagEnum");
    },
    handleClick() {
      this.ids = [];
      if (this.activeName == "ALL") {
        this.setStatus(null, null, null);
      } else {
        let str = this.activeName.split("::");
        let key = str[0];
        let value = parseInt(str[1]);
        if (key == "jobWorkerStatus") {
          this.setStatus(value, null, null);
        } else if (key == "deliveryStatus") {
          this.setStatus(null, value, null);
        } else {
          this.setStatus(null, null, value);
        }
      }
      this.getList();
    },
    setStatus(jobStatus, deliveryStatus, settleStatus) {
      this.searchForm["jobStatus"] = jobStatus;
      this.searchForm["deliveryStatus"] = deliveryStatus;
      this.searchForm["settleStatus"] = settleStatus;
    },
    clearField() {
      this.searchForm = {
        workerName: null,
        workerPhone: null,
        jobId: null,
      };
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleQuery() {
      this.pageCurrent = 1;
      this.getList();
    },
    forceSearch() {
      this.pageCurrent++;
      this.search();
    },
    async getList() {
      const { data } = await getJobworker({
        ...this.searchForm,
        jobId: this.$route.query.jobId,
        size: this.pageSize,
        current: this.pageCurrent,
      });
      this.response.data = data.records;
      this.totalRecord = data.total;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.handleQuery();
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.getList();
    },
    async getWorkerInfo({ workerIdCardMd5 }) {
      const { data } = await workerInfo({
        workerIdCardMd5: workerIdCardMd5,
      });
      let urls = [
        data.idCardBackUrl,
        data.idCardFrontUrl,
      ];
      const formatUrls = []
      for (let i = 0; i < urls.length; i++) {
        const fileMsg = await this.formatFileMsg(urls[i])
        formatUrls.push(fileMsg.fileUrl)
      }
      this.$preview(formatUrls);
    },
    async getDetail() {
      if (!this.id) return;
      const { data } = await jobDetail({ id: this.id });
      this.task = data;
    },
    goInfo() {
      this.$router.push({
        path: "/job/task/detail",
        query: {
          id: this.id,
        },
      });
    },
    async finishTask() {
      let [err] = await toPromise(
        this.$confirm("此操作不可逆，是否继续？", "提示", {
          type: "warning",
        })
      );
      if (err) return;
      const { data } = await finishTask({ id: this.id });
      this.$message.success(data);
      this.getDetail();
    },
    downloadTemplate() {
      let p = require("@/assets/template/job-hire.xlsx");
      this.downloadFile(p, "批量导入模板");
    },
    handleUploadSuccess({ data, message }) {
      this.$refs.upload.clearFiles();
      if (!data) {
        message && this.$alert(message);
        return;
      }
      data && this.$message.success(data);
      this.show = false;
      this.getList();
    },
    toggleUploadDialog() {
      this.show = !this.show;
    },
    deletePeople(index) {
      this.response.data.splice(index, 1);
    },
    downloadJobFile(row, flag) {
      let workerName = row.workerName;
      if (!row.workerName) {
        workerName = "未知";
      }
      let suffix = flag == 1 ? "平台成果验收单" : "平台交付明细表";
      let fileName = `${ workerName }_${ row.workerPhone }_${ suffix }.pdf`;
      let path = flag == 1 ? row.resultSignatureUrl : row.deliverSignatureUrl;
      window.open(`${ this.baseUrl }/download/fastdf?path=${ path }&fileName=${ fileName }&x-token=${ getToken() }`);
    },
    openId(row) {
      this.editRow = row
      this.$refs.idUpload.toggle()
    },
    async onUploadId({ frontUrl, backUrl }) {
      const { data } = await addIDCardImages({
        jobWorkerId: this.editRow.id,
        idCardFrontUrl: frontUrl,
        idCardBackUrl: backUrl,
      })
      data && this.$message.success(data)
      this.getList()
    },
    async getSignedMembers() {
      const { data } = await getSignedMembers({ mchNo: this.userData.mchNo })
      data.forEach((item, key) => {
        item.namePhone = `${ item.name }-${ item.phone }`
        item.key = key
      })
      this.memberList = data
    },
    openTransfer() {
      this.getSignedMembers()
      this.showTransfer = true
    },
    beforeTransferClose(done) {
      this.$nextTick(() => {
        this.memberList = []
      })
      done()
    },
    async addSignedMember() {
      const list = this.selectedMembers.map(item => this.memberList[item])
      let jobMembers = list.map(({ name, phone, idCard }) => {
        return {
          workerName: name,
          workerIdcard: idCard,
          workerPhone: phone
        }
      })
      this.loading = true
      await addSignedMember({
        id: this.id,
        jobMembers
      })
      this.loading = false
      this.$message.success('操作成功')
      this.showTransfer = false
    },
    async showMiniProgramCode() {
      const { data } = await getAppQrCode(this.id)
      if (data) {
        const fileMsg = await this.formatFileMsg(data)
        this.$preview(fileMsg.fileUrl, 0, '灵工人员可通过扫码加入任务，初次登录需完成实名认证后再次扫码', this.task.jobName)
      } else {
        this.$message.error('生成错误！请联系管理员')
      }
    },
    exportPendingList() {
      window.open(`${ this.baseUrl }/download/exporWorkerGrantList?jobId=${ this.jobId }&x-token=${ getToken() }`);
    }
  },
};
</script>

<style
  scoped
  lang="scss">
.job-header {
  margin: 0 0 10px;
  padding: 20px;
  background: #fff;
  font-size: 25px;
  font-weight: bolder;

  .job-name {
    flex: 1 0 auto;
  }
}

.upload-block {
  border: 1px solid #ddd;
  text-align: center;
  padding: 100px 0;

  .el-icon-upload {
    font-size: 40px;
  }
}

.drawer-box {
  display: flex;
  padding: 10px;
  // border-top: 1px solid #666;
}

.drawer-contain {
  padding: 20px;
  padding-left: 40px;
  width: 85%;
  position: relative;
}

.drawer-nav {
  width: 15%;
  font-size: 16px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 10px;
  cursor: pointer;
}

.drawer-title {
  font-size: 20px;
  font-weight: bold;
  position: relative;
}

.drawer-nav .drawer-active {
  position: relative;
  color: #409eff;
}

.drawer-nav .drawer-active::after {
  display: block;
  content: "";
  width: 2px;
  height: 100%;
  background: #409eff;
  position: absolute;
  top: 0;
  left: 0;
}

.drawer-form-item {
  padding: 10px 0;
  display: flex;
  /* align-items: center; */
}

.drawer-form {
  padding: 10px;
}

.drawer-form-label {
  width: 100px;
  text-align: right;
}

.drawer-form-value {
  margin-left: 1rem;
  width: 70%;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  position: fixed;
  bottom: 10px;
  right: 10px;
}

.el-hide ::v-deep .el-upload--picture-card {
  display: none;
}

.job-drawer ::v-deep .el-drawer__body {
  overflow: scroll;
}

.job-drawer ::v-deep .el-drawer__header {
  // border-bottom: 1px solid #666;
  margin: 0;
  padding-bottom: 32px;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.3);
}

.job-drawer ::v-deep .el-drawer__body::-webkit-scrollbar {
  width: 0 !important;
}

.table-btn-box {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
}
</style>
