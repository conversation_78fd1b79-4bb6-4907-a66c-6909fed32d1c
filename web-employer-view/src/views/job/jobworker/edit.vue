<template>
  <div class="page-container drawer-box">
    <div>
      <div class="drawer-title">审核任务交付成果</div>
      <div class="drawer-contain">
        <div id="job" class="drawer-title">任务信息</div>
        <div class="drawer-form">
          <div class="drawer-form-item">
            <div class="drawer-form-label">任务编号：</div>
            <div class="drawer-form-value">{{ dataForm.jobId }}</div>
          </div>
          <div class="drawer-form-item">
            <div class="drawer-form-label">任务名称：</div>
            <div class="drawer-form-value">{{ dataForm.jobName }}</div>
          </div>
          <div class="drawer-form-item">
            <div class="drawer-form-label">行业/岗位：</div>
            <div class="drawer-form-value">{{ dataForm.industryName }}</div>
          </div>
          <div class="drawer-form-item">
            <div class="drawer-form-label">任务日期：</div>
            <div class="drawer-form-value">
              {{
                $dictCode("JobAvalDateTypeEnum", dataForm.jobAvalDateType).desc
              }}
            </div>
          </div>
          <div class="drawer-form-item">
            <div class="drawer-form-label">任务报酬：</div>
            <div class="drawer-form-value" v-if="dataForm.rewardType == 100">
              {{ dataForm.rewardAmount
              }}/{{ $dictCode("PayTypeEnum", dataForm.payType).desc }}
            </div>
            <div class="drawer-form-value" v-if="dataForm.rewardType == 200">
              面议
            </div>
          </div>
          <div class="drawer-form-item">
            <div class="drawer-form-label">任务描述：</div>
            <div class="drawer-form-value">{{ dataForm.jobDescribe }}</div>
          </div>
          <div class="drawer-form-item">
            <div class="drawer-form-label">交付标准：</div>
            <div class="drawer-form-value">
              {{ dataForm.deliveryStandard }}
            </div>
          </div>
        </div>
        <div id="worker" class="drawer-title">交付物</div>
        <div class="drawer-form">
          <div class="drawer-form-item">
            <div class="drawer-form-label">工作结果：</div>
            <div class="drawer-form-value">
              {{ dataForm.deliveryContent }}
            </div>
          </div>
          <div class="drawer-form-item">
            <div class="drawer-form-label">交付物：</div>
            <div class="drawer-form-value">
              <el-upload
                v-if="fileList.length != 0"
                action=""
                disabled
                list-type="picture-card"
                :on-preview="handlePictureCardPreview"
                :file-list="fileList"
                class="el-hide"
              >
              </el-upload>
              <el-dialog :visible.sync="dialogVisible">
                <img width="100%" :src="dialogImageUrl" alt="" />
              </el-dialog>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="openApp(true)">通过</el-button>
      <el-button type="danger" @click="openApp(false)">驳回</el-button>
    </div>

    <el-dialog
      :title="title"
      :close-on-click-modal="false"
      :visible.sync="dialogFormVisible"
    >
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item
          label="备注"
          :label-width="formLabelWidth"
          prop="failReason"
        >
          <el-input
            class="form-width"
            type="textarea"
            v-model="form.failReason"
            autocomplete="off"
            :rows="4"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { findById, approved } from "@/api/jobworker";
export default {
  data() {
    return {
      dataForm: {},
      fileList: [],
      dialogImageUrl: "",
      dialogVisible: false,

      id: null,
      rules: {
        failReason: [
          { required: true, message: "请输入备注", trigger: "blur" },
        ],
      },
      dialogFormVisible: false,
      form: {
        failReason: "",
      },
      formLabelWidth: "80px",
      title: "",
      appEvent: null,
    };
  },
  mounted() {
    this.id = this.$route.query.id;
    this.getData();
  },
  methods: {
    cancel() {
      this.$router.push({
        path: "/job/worker",
        query: {
          jobId: this.dataForm.jobId,
        },
      });
    },
    close() {
      this.title = "";
      this.appEvent = null;
      this.form.failReason = "";
      this.dialogFormVisible = false;
    },
    submit() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let params = {
            id: this.id,
            event: this.appEvent,
            failReason: this.form.failReason,
          };
          approved(params).then((res) => {
            if (res.code == 20000) {
              this.$message.success("审批成功");
              this.$router.push({
                path: "/job/worker",
                query: {
                  jobId: this.dataForm.jobId,
                },
              });
            }
          });
        }
      });
    },
    openApp(event) {
      if (event) {
        this.title = "审批通过";
      } else {
        this.title = "审批驳回";
      }
      let dict = this.$dictCode("ApproveEvent", event);
      this.appEvent = dict.flag;
      this.dialogFormVisible = true;
    },
    async getData() {
      this.drawer = true;
      const { data } = await findById(this.id);
      this.dataForm = data;
      if (data.attachment != null) {
        let files = data.attachment;
        let fileResetList = [];
        for (let i = 0; i < files.length; i++) {
          const item = files[i];
          const fileMsg = await this.formatFileMsg(item);
          fileResetList.push({
            name: fileMsg.name,
            url: fileMsg.fileUrl
          });
        }
        this.fileList = fileResetList;
      }
      
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
  },
};
</script>

<style scoped>
.drawer-contain {
  padding: 20px;
}

.drawer-title {
  font-size: 20px;
  font-weight: bold;
  position: relative;
}
.drawer-form-item {
  padding: 10px 0;
  display: flex;
  /* align-items: center; */
}
.drawer-form {
  padding: 10px;
}
.drawer-form-label {
  width: 100px;
  text-align: right;
}
.drawer-form-value {
  margin-left: 1rem;
  width: 70%;
}
.el-hide /deep/ .el-upload--picture-card {
  display: none;
}
.drawer-box {
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.footer {
  display: flex;
  justify-content: flex-end;
}
</style>
