<template>
  <div class="page-container">
    <div class="header-container">
      <el-steps
        :active="activeStep"
        align-center
      >
        <el-step title="审核信息"></el-step>
        <el-step title="确认提交"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>
    <div class="content-container">
      <div v-show="activeStep == 1">
        <div class="batch-info-container">
          <div class="clearfix">
            {{ responseData.batchName }} | {{ $dictCode('OrderStatusEnum', responseData.batchStatus).desc }} | 合计{{ responseData.requestCount }}笔/{{ responseData.requestNetAmount | moneyFormat }}
            <div class="batch-button-group">
              <el-button
                type="text"
                @click="cancel"
              >取消发放
              </el-button>
              <el-button
                type="text"
                @click="getExportList"
              >查看已导出列表
              </el-button>
              <el-button @click="exportOrderItem">导出</el-button>
              <el-button
                type="primary"
                :disabled="responseTable.length === 0"
                @click="nextStep"
              >下一步
              </el-button>
            </div>
          </div>
          <div class="batch-info-graph">
            <div class="batch-graph success-graph">
              <p class="batch-graph-title">已受理订单（笔|元）</p>
              <p>{{ responseData.acceptedCount }} | {{ responseData.acceptedNetAmount }}</p>
            </div>
            <div class="batch-graph fail-graph">
              <p class="batch-graph-title">失败订单（笔|元）</p>
              <p>{{ responseData.failCount }} | {{ responseData.failNetAmount }}</p>
            </div>
            <div class="batch-graph hang-graph">
              <p class="batch-graph-title">挂单订单（笔|元）</p>
              <p>0 | 0</p>
            </div>
          </div>
        </div>

        <div class="batch-table-container">
          <el-table
            :data="responseTable"
            @filter-change="filterChange"
          >
            <el-table-column
              label="序号"
              type="index"
              width="50"
              :index="getIndex"
            ></el-table-column>
            <el-table-column
              label="商户订单号"
              prop="mchOrderNo"
            ></el-table-column>
            <el-table-column
              label="平台流水号"
              prop="platTrxNo"
            ></el-table-column>
            <el-table-column
              label="收款账户"
              prop="receiveAccountNo"
            ></el-table-column>
            <el-table-column
              label="姓名"
              prop="receiveName"
            ></el-table-column>
            <el-table-column
              label="身份证号"
              prop="receiveIdCardNo"
            ></el-table-column>
            <el-table-column
              label="手机号"
              prop="receivePhoneNo"
            ></el-table-column>

            <el-table-column
              label="实发金额"
              prop="orderItemNetAmount"
            >
              <template v-slot="{row}">
                {{ row.orderItemNetAmount | moneyFormat }}
              </template>
            </el-table-column>

            <el-table-column
              label="认证状态"
              prop="authStatus"
            >
              <template v-slot="{row, column}">
                {{ $dictCode('AuthStatusEnum', row[column.property]).desc }}
              </template>
            </el-table-column>
            <el-table-column
              label="签约状态"
              prop="signStatus"
            >
              <template v-slot="{row, column}">
                {{ $dictCode('SignStatusEnum', row[column.property]).desc }}
              </template>
            </el-table-column>

            <el-table-column
              label="状态"
              :filters="statusFilters"
              :filter-method="filterHandler"
              column-key="status-column"
            >
              <template v-slot="{row}">
                <div v-if="isOuter">
                  {{ $dictCode('OfflineOrderItemStatusEnum', row.orderItemStatus).desc }}
                </div>
                <div v-else>
                  {{ $dictCode('OrderItemStatusEnum', row.orderItemStatus).desc }}
                </div>
              </template>
            </el-table-column>

            <el-table-column
              label="错误提示"
              prop="errorDesc"
            ></el-table-column>

            <el-table-column
              label="操作"
              width="150">
              <template
                v-slot="{row, $index}">
                <div v-if="row.orderItemStatus != 200">
                  <el-button
                    v-if="row.authStatus == 102"
                    type="text"
                    @click="uploadId(row)">上传身份证
                  </el-button>
                  <el-button
                    v-if="row.signStatus == 300||row.signStatus == 400||row.signStatus == 600"
                    type="text"
                    @click="signByOrderItem(row)"
                  >发起签约
                  </el-button>
                  <el-button
                    v-if="isOuter"
                    type="primary"
                    size="small"
                    @click="onUpload($index)">
                    {{ (row.workerBillFilePath && row.workerBillFilePath.length > 0) ? '修改支付回单' : '上传支付回单' }}
                  </el-button>
                </div>
              </template>
            </el-table-column>

          </el-table>
        </div>
      </div>

      <div v-show="activeStep == 2">
        <div class="clearfix">
          {{ responseData.batchName }} | {{ $dictCode('OrderStatusEnum', responseData.batchStatus).desc }} | 合计{{ responseData.acceptedCount }}笔/{{ responseData.acceptedNetAmount | moneyFormat }}
          <div class="batch-button-group">
            <el-button
              type="text"
              @click="exportOrderItem"
            >导出
            </el-button>
            <el-button
              type="text"
              @click="cancel"
            >取消发放
            </el-button>
            <el-button
              type="primary"
              @click="nextStep"
            >确认提交
            </el-button>
          </div>
        </div>

        <warning-block>
          <p>温馨提示</p>
          <p>合计 {{ responseData.acceptedCount }} 笔，实发金额汇总 {{ responseData.acceptedNetAmount }} 元，代征主体服务费 {{ responseData.acceptedFee }} 元，共计 {{ responseData.acceptedOrderAmount }} 元</p>
          <p>账户余额 {{ amount }} 元</p>
          <div v-if="deliverInfo.productNo === 'CKH'">
            <p v-if="deliverInfo.balancedMode == 101">贵司服务费结算模式为线下结算，系统将于次日02:00生成服务费账单，请在「财务 > 服务费账单」查看并线下完成打款</p>
            <p v-else-if="deliverInfo.balancedMode == 100">贵司服务费结算模式为线上结算，系统将于次日02:00生成服务费账单并自动扣款，请预留足够余额</p>
          </div>
        </warning-block>

        <div class="batch-table-container">
          <el-table :data="responseTable">
            <el-table-column
              label="序号"
              type="index"
              width="50"
              :index="getIndex"
            ></el-table-column>
            <el-table-column
              label="商户订单号"
              prop="mchOrderNo"
            ></el-table-column>
            <el-table-column
              label="平台流水号"
              prop="platTrxNo"
            ></el-table-column>
            <el-table-column
              label="收款账户"
              prop="receiveAccountNo"
            ></el-table-column>
            <el-table-column
              label="姓名"
              prop="receiveName"
            ></el-table-column>
            <el-table-column
              label="身份证号"
              prop="receiveIdCardNo"
            ></el-table-column>
            <el-table-column
              label="手机号"
              prop="receivePhoneNo"
            ></el-table-column>

            <el-table-column
              label="实发金额"
              prop="orderItemNetAmount"
            >
              <template v-slot="{row}">
                {{ row.orderItemNetAmount | moneyFormat }}
              </template>
            </el-table-column>

            <el-table-column
              label="代征主体服务费"
              prop="orderItemFee"
            >
              <template v-slot="{row}">
                {{ row.orderItemFee | moneyFormat }}
              </template>
            </el-table-column>

            <el-table-column
              label="订单金额"
              prop="orderItemAmount"
            >
              <template v-slot="{row}">
                {{ row.orderItemAmount | moneyFormat }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <el-main v-if="activeStep == 3">
        <div class="result-container">
          <img
            src="@/assets/success.png"
            alt="success">
          <p class="result-title">确认发放成功</p>
          <p
            class="result-slogan"
            v-if="!isOuter">发放名单正在进行打款，可返回发放名单查看进度</p>
          <div class="result-detail clearfix">
            <p class="result-detail-title">批次详情</p>
            <div class="result-detail-left">
              <p>批次号：{{ platBatchNo }}</p>
              <p>已受理笔数：{{ responseData.acceptedCount }}笔</p>
              <p>实发金额汇总：{{ responseData.acceptedNetAmount }}（元）</p>
            </div>
            <div class="result-detail-right">
              <p>创建时间：{{ responseData.createTime }}</p>
              <p>请求笔数：{{ responseData.requestCount }}笔</p>
              <p>服务费：{{ responseData.acceptedFee }}（元）</p>
            </div>
          </div>
          <div class="result-button">
            <el-button
              type="primary"
              @click="goRoster"
            >返回发放名单
            </el-button>
          </div>
        </div>
      </el-main>

      <div
        class="pagination-container"
        v-if="activeStep < 3"
      >
        <el-pagination
          v-if="responseData"
          :total="totalRecord"
          :current-page="pageCurrent"
          :page-size="pageSize"
          :page-sizes="[10, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <export-record ref="exportRecord"></export-record>
    <pwd
      :visible.sync="visible"
      @confirm="confirm"
    />

    <id-upload-dialog
      ref="idUpload"
      @change="confirmUpload"
    />

    <order-bill-upload
      v-if="responseTable[editIndex]"
      ref="billUpload"
      title="上传支付回单"
      :files="editRow.workerBillFilePath"
      @change="handleUpload"></order-bill-upload>
  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import {
  cancelOfflineOrder,
  cancelOrderGrant,
  confirmBatchOrder,
  confirmOrderGrant,
  exportOfflineOrderItem,
  exportOrderItem,
  getAmount,
  getDeliverList,
  getOrderByBatchNo,
  getOrderListByStatus,
  offlineGetDeliverList,
  offlineGetOrderByBatchNo,
  offlineGetOrderListByStatus,
  signByOfflineOrderItem,
  signByOrderItem,
  uploadIdCard,
  uploadWorkerBill
} from '@/api/order'
import { getKey } from '@/utils/publicKey'
import { toPromise } from '@/utils'
import { encryptParam } from '@/utils/jsencrypt'
import Pwd from './Component/Pwd'
import IdUploadDialog from '@/components/IdUpload/dialog'
import OrderBillUpload from "@/views/trade/order/Component/OrderBillUpload";
import { getCkhQuoteByMainstayNo } from "@/api/merchant";

export default {
  components: {
    OrderBillUpload,
    ExportRecord,
    Pwd,
    IdUploadDialog,
  },
  data() {
    return {
      activeStep: 1,

      platBatchNo: '',

      responseData: {
        platBatchNo: '',
        batchName: '',
        batchStatus: '',
        requestCount: '',
        requestNetAmount: '',
        acceptedCount: '',
        acceptedNetAmount: '',
        acceptedFee: '',
        failCount: '',
        failNetAmount: '',
        hangCount: '',
        hangNetAmount: '',
        createTime: '',
        confirmTime: '',
        orderItemStatus: ''
      },
      responseTable: [],

      pageCurrent: 1,
      pageSize: 10,
      totalRecord: 0,

      filterStatusCode: '',

      visible: false,

      deliverInfo: {},
      amount: 0, // 余额

      editIndex: 0, // 当前编辑行
      editRow: {},
    }
  },
  computed: {
    statusFilters() {
      let dict = this.isOuter ? this.$dict('OfflineOrderItemStatusEnum') : this.$dict('OrderItemStatusEnum');
      let list = [];
      for (let i of dict) {
        list.push({
          text: i.desc,
          value: i.code
        })
      }
      return list;
    },
    isOuter() {
      return this.$route.query.isOuter === 'true' || this.$route.query.isOuter === true
    }
  },
  mounted() {
    this.platBatchNo = this.$route.query.platBatchNo || '';
    this.getOrderByBatchNo();
    this.getDeliverList();

    if (sessionStorage.getItem('deliverInfo')) {
      const data = JSON.parse(sessionStorage.getItem('deliverInfo'));
      this.deliverInfo = {
        mainstayNo: data.mainstayNo,
        payChannelNo: data.payChannelNo,
        channelType: data.channelType,
        productNo: data.productNo,
      }
      if (!this.isOuter) {
        this.getAmount();
      }
      if (this.deliverInfo.productNo === 'CKH') {
        this.getCkhQuoteByMainstayNo(this.deliverInfo.mainstayNo)
      }
    }
  },
  destroyed() {
    sessionStorage.removeItem('deliverInfo');
  },
  methods: {
    async getOrderByBatchNo() {
      const api = this.isOuter ? offlineGetOrderByBatchNo : getOrderByBatchNo
      const { data } = await api({
        platBatchNo: this.platBatchNo
      });
      this.responseData = Object.assign(this.responseData, data);
    },
    async getDeliverList(initFlag) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      let form = {
        platBatchNo: this.platBatchNo,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      }
      const api = this.isOuter ? offlineGetDeliverList : getDeliverList
      const { data } = await api(form);
      this.responseTable = data.data;
      this.totalRecord = data.totalRecord || 0;
    },
    async getOrderList(initFlag, code) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      let form = {
        platBatchNo: this.platBatchNo,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      }
      if (code) {
        form.orderItemStatus = code;
      }

      if (this.activeStep == 2) {
        form.orderItemStatus = this.$dictDesc('OrderItemStatusEnum', '已受理').code;
      }
      const api = this.isOuter ? offlineGetOrderListByStatus : getOrderListByStatus
      const { data } = await api(form);
      this.responseTable = data.data;
      this.totalRecord = data.totalRecord || 0;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDeliverList(true);
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.getDeliverList();
    },
    async cancel() {
      await this.$confirm('此操作将取消当前批次的发放，是否继续', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      const cancelApi = this.isOuter ? cancelOfflineOrder : cancelOrderGrant
      await cancelApi({
        platBatchNo: this.platBatchNo,
      })
      this.$message.success('取消成功')
      const path = this.isOuter ? '/trade/outer/roster' : '/trade/order/roster'
      this.$router.push(path)
    },
    async nextStep() {
      if (this.activeStep == 2) {
        if (this.isOuter) {
          // 外部订单不输入密码
          await confirmBatchOrder({ platBatchNo: this.platBatchNo })
          this.activeStep++;
        } else {
          this.visible = true
        }
      } else {
        if (this.isOuter && this.activeStep === 1) {
          for (let i = 0; i < this.responseTable.length; i++) {
            const row = this.responseTable[i]
            if (row.orderItemStatus === 400 && !row.workerBillFilePath) {
              return this.$message.error('请先上传支付回单')
            }
          }
        }
        this.activeStep++;
        this.getOrderList(true);
      }
    },
    goRoster() {
      const path = this.isOuter ? '/trade/outer/roster' : '/trade/order/roster'
      this.$router.push(path);
    },
    filterHandler(val, row) {
      return val == row['orderItemStatus'];
    },
    filterChange(val) {
      let list = val['status-column'];
      if (list.length > 1) {
        return this.$message('目前仅支持筛选一项');
      }
      this.filterStatusCode = list[0];
      this.getOrderList(true, list[0]);
    },
    async exportOrderItem() {
      let form = {
        platBatchNo: this.platBatchNo,
      }
      if (this.activeStep == 1) {
        if (this.filterStatusCode) {
          form.orderItemStatus = this.filterStatusCode
        }
      } else if (this.activeStep == 2) {
        form.orderItemStatus = this.$dictDesc('OrderItemStatusEnum', '已受理').code
      }
      const api = this.isOuter ? exportOfflineOrderItem : exportOrderItem
      const { data } = await api(form);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      if (this.isOuter) {
        this.$refs.exportRecord.getExportRecord('55')
      } else {
        this.$refs.exportRecord.getExportRecord(this.$dictFlag('ReportTypeEnum', 'TRADE_ORDER_ITEM_MER').code);
      }
    },
    async confirm(pwd) {
      const { data } = await confirmOrderGrant({
        platBatchNo: this.platBatchNo,
        tradePwd: encryptParam({
          publicKey: getKey(),
          pwd,
        })
      })
      this.$message.success(data);
      this.activeStep++;
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    async getAmount() {
      const { data } = await getAmount(this.deliverInfo);
      this.amount = data.amount || '';
    },
    async signByOrderItem(row) {
      const [err] = await toPromise(this.$confirm('是否确定发起签约？', '提示', { type: 'warning' }))
      if (err) return;
      const api = this.isOuter ? signByOfflineOrderItem : signByOrderItem
      const [error, res] = await toPromise(api(row.platTrxNo))
      if (error) return;
      const { data } = res
      data && this.$message.success(data)
      this.getOrderByBatchNo();
      this.getDeliverList();
      if (!this.isOuter) {
        this.getAmount()
      }
    },
    async uploadId(row) {
      this.uploadRow = row
      this.$refs.idUpload.toggle();
    },
    async confirmUpload({ frontUrl, backUrl }) {
      const { data } = await uploadIdCard({
        idCardFrontUrl: frontUrl,
        idCardBackUrl: backUrl,
        idCardType: 100,
        platTrxNo: this.uploadRow.platTrxNo
      })
      data && this.$message.success(data)
      this.getDeliverList();
    },
    onUpload(index) {
      this.editIndex = index
      this.editRow = JSON.parse(JSON.stringify(this.responseTable[this.editIndex]))
      this.$refs.billUpload.toggle(true)
    },
    async handleUpload(urls) {
      if (!urls) {
        // 关闭弹窗
        return this.editRow = {}
      }
      const { data } = await uploadWorkerBill({
        workerBillFilePath: urls,
        platTrxNo: this.responseTable[this.editIndex].platTrxNo
      })
      this.$message.success('上传成功')
      this.$refs.billUpload.toggle(false)
      this.responseTable.splice(this.editIndex, 1, data)
    },
    async getCkhQuoteByMainstayNo(mainstayNo) {
      const { data: { balancedMode } } = await getCkhQuoteByMainstayNo({
        mainstayNo
      })
      this.deliverInfo.balancedMode = balancedMode
    },
  }
}
</script>

<style
  lang="scss"
  scoped>
.page-container {
  .header-container {
    margin-bottom: 40px;
    padding: 20px 0;
    background: #fff;
  }

  .content-container {
    padding: 20px;
    background: #fff;
  }

  .batch-button-group {
    float: right;
  }

  .batch-info-graph {
    display: flex;
    margin-top: 24px;
  }

  .batch-graph {
    flex: 1;
    padding: 24px;
  }

  .batch-graph-title {
    margin-bottom: 16px;
    color: #000;
  }

  .success-graph {
    border: 1px solid #eee;
    border-top: 2px solid $mainColor;
    color: $mainColor;
  }

  .fail-graph {
    border: 1px solid #eee;
    border-top: 2px solid $errorColor;
    color: $errorColor;
  }

  .hang-graph {
    border: 1px solid #eee;
    border-top: 2px solid $warningColor;
    color: $warningColor;
  }

  .result-container {
    text-align: center;
  }

  .result-title {
    font-size: 30px;
    font-weight: 600;
  }

  .result-detail-title {
    font-weight: 600;
  }

  .result-slogan {
    color: #ccc;
  }

  .result-detail {
    width: 500px;
    margin: 40px auto;
    padding: 20px;
    text-align: left;
  }

  .result-detail-left {
    float: left;
  }

  .result-detail-right {
    float: right;
  }

  .el-table {
    margin-top: 20px;
  }
}
</style>
