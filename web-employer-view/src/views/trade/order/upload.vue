<template>
  <div class="page-container">
    <div class="header-container">
      <el-steps
        :active="activeStep"
        align-center>
        <el-step title="上传文件"></el-step>
        <el-step title="上传完成"></el-step>
      </el-steps>
    </div>

    <div class="content-container">
      <el-form
        class="step_1"
        v-show="activeStep == 1"
        :model="form"
        :rules="rules"
        ref="uploadForm"
      >
        <warning-block v-if="isOuter">智享汇暂不支持外部发放，请使用平台发放</warning-block>
        <el-form-item prop="productNo">
          <p class="form-item-label">请选择所属产品</p>
          <el-select
            v-model="form.productNo"
            @change="onProductChange">
            <el-option
              v-for="(item, index) in productList"
              :key="index"
              :label="item.productName"
              :value="item.productNo"
              :disabled="isOuter && item.productNo !== 'CKH'"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          prop="mainstayName"
          class="inline-form-item"
        >
          <p class="form-item-label">请选择代征主体
            <template v-if="!isOuter">和发放方式</template>
          </p>
          <el-select
            clearable
            v-model="form.mainstayNo"
            @change="changeMainstay"
          >
            <el-option
              v-for="(item, index) in mainstayList"
              :key="index"
              :label="item.mainstayName"
              :value="item.mainstayNo"
              :disabled="item.status == 101"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          prop="channelType"
          class="inline-form-item"
          v-if="!isOuter"
        >
          <el-select
            clearable
            v-model="form.channelType"
            :disabled="!form.mainstayNo">
            <el-option
              v-for="(type, key) in channelTypeList"
              :key="key"
              :label="$dictCode('ChannelTypeEnum', type).desc"
              :value="$dictCode('ChannelTypeEnum', type).code"
            >
            </el-option>
          </el-select>
          <el-button
            type="text"
            @click="downloadTemplate"
            v-if="form.channelType"
          >
            下载{{ $dictCode('ChannelTypeEnum', form.channelType).desc }}模板
          </el-button>
          <div
            class="form-channel-img"
            v-if="form.channelType">
            <div>本次发放由</div>
            <img
              :src="require('@/assets/logo_' + typeNoMap[form.channelType] + '.png')"
              alt="logo">
            <div>提供支付服务</div>
          </div>
        </el-form-item>
        <el-button
          v-if="isOuter"
          type="text"
          @click="downloadOuterTemplate">
          下载线下发放模板
        </el-button>
        <el-form-item
          prop="jobId"
          v-if="form.productNo == 'CKH'">
          <p class="form-item-label">请选择关联任务</p>
          <el-select
            v-model="form.jobId"
            @change="onJobChange">
            <el-option
              v-for="(item, index) in jobList"
              :key="index"
              :label="item.jobName"
              :value="item.jobId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          prop="workCategoryName"
          v-if="form.mainstayNo">
          <!-- <p class="form-item-label">请选择自由职业者服务类型</p> -->
          <p class="form-item-label">请选择岗位类目</p>
          <el-select
            clearable
            :disabled="form.productNo == 'CKH'"
            v-model="form.workCategoryName"
            @change="changeWorkCategory"
          >
            <el-option
              v-for="(item, index) in workCategoryList"
              :key="index"
              :label="item.workCategoryName"
              :value="item"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="serviceDesc">
          <p class="form-item-label">服务描述</p>
          <el-input
            disabled
            type="textarea"
            v-model="form.serviceDesc"
            :autosize="{ minRows: 5, maxRows: 5}"
            placeholder="为使落地供应商匹配到合适的自由职业者，请按实际描述个人具体需要做什么"
          >
          </el-input>
        </el-form-item>

        <el-form-item prop="file">
          <p class="form-item-label">上传填好的发放名单</p>
          <el-upload
            class="upload-demo"
            data-type="businessLicenseFileUrl"
            action=""
            :auto-upload="false"
            :headers="uploadHeader"
            accept=".xlsx,.xls"
            :limit="1"
            :file-list="fileList"
            :on-change="handlerChange"
          >
            <el-button size="small">选择需要上传的文件</el-button>
          </el-upload>
        </el-form-item>

        <div class="form-footer">
          <el-button
            type="primary"
            @click="confirmData"
            :loading="loading"
          >上传
          </el-button>
        </div>
      </el-form>

      <div
        class="step_2"
        v-show="activeStep == 2">
        <img
          src="@/assets/success.png"
          alt="">
        <p class="result-title">已上传</p>
        <div class="result-wrapper">
          <p class="result-detail">您的文件校验中，请返回发放名单查看批次受理状态，并对批次进行进一步操作</p>
          <p class="result-detail">批次号：{{ result.platBatchNo }}</p>
        </div>
        <div class="footer-container">
          <el-button
            type="primary"
            @click="goRoster"
          >返回发放名单
          </el-button>
          <el-button @click="continueUpload">继续上传</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { listAllProduct, uploadOfflineOrderList, uploadOrderList } from '@/api/order'
import { getMainstayListWithProduct, getOpenChannelType, listPositionWithMainstayNo } from '@/api/merchant'
import { taskListOnGrant } from '@/api/jobworker'

export default {
  name: 'tradeOrderRoster',
  data() {
    return {
      activeStep: 1,

      form: { // 上传表单
        workCategoryCode: '',
        workCategoryName: '',
        serviceDesc: '',
        // invoiceCategoryCode: '',
        // invoiceCategoryName: '',
        // invoiceCategoryList: [],
        mainstayNo: '',
        mainstayName: '',
        channelType: '',
        file: undefined,
        jobId: null,
        jobName: null,
      },

      workCategoryList: [],

      mainstayList: [],
      channelTypeList: [],

      fileList: [],

      rules: {
        workCategoryName: [
          { required: true, message: '请选择自由职业者服务类型', trigger: 'change' }
        ],
        serviceDesc: [
          { required: true, message: '请输入服务描述', trigger: ['blur', 'change'] }
        ],
        mainstayName: [
          { required: true, message: '请选择代征主体', trigger: 'change' }
        ],
        channelType: [
          { required: true, message: '请选择发放方式', trigger: 'blur' }
        ],
        file: [
          { required: true, message: '请选择上传文件' }
        ],
        invoiceCategoryCode: [
          { required: true, message: '请选择发票类目', trigger: 'change' }
        ],
        productNo: [
          { required: true, message: '请选择所属产品', trigger: 'change' }
        ],
      },

      productList: [], // 产品列表
      jobList: [], // 任务列表

      result: '',
      loading: false,
      typeNoMap: {}, // channelType映射channelNo
    }
  },
  computed: {
    mainstayMap() {
      let map = {}
      this.mainstayList.forEach(item => {
        map[item.mainstayNo] = item.mainstayName
      });
      return map
    },
    jobWorkCategoryMap() {
      let map = {};
      this.jobList.forEach(item => {
        map[item.jobId] = {
          workCategoryCode: item.workCategoryCode,
          workCategoryName: item.workCategoryName,
        }
      })
      return map
    },
    isOuter() {
      // 是否外部订单上传页面
      return this.$route.path === '/trade/outer/upload'
    }
  },
  watch: {
    'form.jobId': {
      handler(val) {
        if (!val) return;
        for (let item of this.jobList) {
          if (val === item.jobId) {
            this.form.jobName = item.jobName;
            break;
          }
        }
      }
    }
  },
  mounted() {
    this.getProduct();
    this.getJobList();
  },
  methods: {
    async getMainstayListWithProduct() {
      const { data } = await getMainstayListWithProduct({ status: 100, productNo: this.form.productNo });
      this.mainstayList = data;
      let item = this.mainstayList[0];
      if (item) {
        this.form.mainstayNo = item.mainstayNo;
        this.changeMainstay(item.mainstayNo)
      }
    },
    changeWorkCategory(value) {
      // this.form.invoiceCategoryCode = '';
      // this.form.invoiceCategoryName = '';
      this.form = Object.assign({}, this.form, value);
    },
    changeInvoiceCategory(value) {
      this.form.invoiceCategoryList.forEach(item => {
        if (item.invoiceCategoryCode == value) {
          this.form.invoiceCategoryCode = item.invoiceCategoryCode;
          this.form.invoiceCategoryName = item.invoiceCategoryName;
        }
      })
    },
    async changeMainstay(val) {
      if (!val) return;
      this.form.mainstayName = this.mainstayMap[val];
      const { data } = await getOpenChannelType({
        mainstayNo: this.form.mainstayNo
      });
      this.channelTypeList = []
      this.typeNoMap = {}
      data.forEach(item => {
        this.channelTypeList.push(String(item.channelType))
        this.typeNoMap[item.channelType] = item.channelNo
      })
      this.form.channelType = this.channelTypeList[0].channelType;
      this.clearForm()
      this.listPositionWithMainstayNo(val);
    },
    handlerChange(file, fileList) {
      this.form.file = fileList[0].raw
    },
    confirmData() {
      this.$refs.uploadForm.validate(valid => {
        if (valid) {
          this.uploadData();
        }
      })
    },
    async uploadData() {
      this.loading = true;
      let form = { ...this.form };
      delete form.invoiceCategoryList;
      let formData = new FormData();
      for (let p in form) {
        formData.append(p, form[p]);
      }
      const api = this.isOuter ? uploadOfflineOrderList : uploadOrderList
      const { data } = await api(formData);
      this.loading = false;
      this.result = data;
      this.activeStep++;
    },
    continueUpload() {
      this.$refs.uploadForm.resetFields();
      this.fileList = [];
      this.activeStep = 1;
    },
    goRoster() {
      const path = this.isOuter ? '/trade/outer/roster' : '/trade/order/roster'
      this.$router.push(path)
    },
    handleFileRemove(file) {
      this.fileList = this.fileList.filter(item => {
        return item.name !== file.name;
      })
    },
    downloadTemplate() {
      let type = this.$dictCode('ChannelTypeEnum', this.form.channelType).desc;
      let template;
      if (type == '支付宝') {
        template = require('@/assets/template/template-zfb.xlsx');
      } else if (type == '银行卡') {
        template = require('@/assets/template/template-bank.xlsx');
      } else {
        template = require('@/assets/template/template-wx.xlsx');
      }
      console.log('template', template)
      const a = document.createElement('a');
      a.download = `发放方式-${ type }模板.xlsx`;
      a.href = template;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    },
    cancel() {
      this.$router.push('/trade/order/roster');
    },
    async listPositionWithMainstayNo(mainstayNo) {
      const { data } = await listPositionWithMainstayNo({ mainstayNo });
      this.workCategoryList = data;
    },
    onProductChange(val) {
      if (!val) return;
      this.clearForm(true)
      this.getMainstayListWithProduct();
    },
    clearForm(clearMainstay = false) {
      if (clearMainstay) {
        this.form.mainstayNo = this.form.mainstayName = '';
      }
      this.form.workCategoryCode = this.form.workCategoryName = this.form.serviceDesc = '';
      this.form.jobId = this.form.jobName = '';
      this.$refs.uploadForm.clearValidate();
    },
    async onJobChange(val) {
      if (!val || this.workCategoryList.length == 0) return;
      this.form.workCategoryCode = this.form.workCategoryName = this.form.serviceDesc = '';
      await this.$nextTick()
      let currWorkCate = this.jobWorkCategoryMap[val]
      if (!currWorkCate) return;
      let hasCurr = this.workCategoryList.findIndex(item => item.workCategoryCode == currWorkCate.workCategoryCode)
      if (hasCurr !== -1) {
        this.form = Object.assign(this.form, { ...this.workCategoryList[hasCurr] })
      }
    },
    async getProduct() {
      const { data } = await listAllProduct();
      this.productList = data;
    },
    async getJobList() {
      const { data } = await taskListOnGrant({});
      this.jobList = data
    },
    downloadOuterTemplate() {
      const template = require('@/assets/template/template-offline.xlsx');
      const a = document.createElement('a');
      a.download = `发放方式-线下模板.xlsx`;
      a.href = template;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

    }
  }
}
</script>

<style
  lang="scss"
  scoped>
.page-container {
  .el-form {
    width: 800px;
  }

  .el-form-item {
    margin-bottom: 36px;
  }

  .inline-form-item {
    display: inline-block;
  }

  .form-item-label {
    position: relative;
    padding-left: 10px;
    font-weight: bold;

    &::before {
      content: "*";
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      color: #f00;
    }
  }

  .form-channel-img {
    position: absolute;
    left: 120%;
    top: 0;
    border: 1px solid #e9e9e9;
    padding: 8px 16px;
    white-space: nowrap;
    color: #333;
    user-select: none;
  }

  .step_2 {
    text-align: center;

    img {
      width: 100px;
    }
  }

  .item-work-category {
    .el-input {
      display: inline-block;
      width: 200px;
    }
  }

  .result-title {
    font-size: 30px;
    font-weight: 600;
  }

  .result-detail {
    color: #ddd;
  }

  .footer-container {
    margin-top: 40px;
  }
}
</style>
