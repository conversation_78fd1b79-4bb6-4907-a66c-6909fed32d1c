<template>
  <!--  上传支付回单-->
  <el-dialog
    :before-close="() => cancelUpload()"
    :visible.sync="showUpload"
    :close-on-click-modal="false"
    v-bind="$attrs">
    <div class="flex-wrapper">
      <div>支付回单：</div>
      <file-upload
        accept=".doc,.docx,.pdf,.jpg,.png"
        :max="99"
        :options="{word: true, pdf: true}"
        :urls.sync="urls">
        <div slot="tip">支持的文件类型：.doc .docx .pdf .jpg .png</div>
      </file-upload>
    </div>
    <template v-slot:footer>
      <el-button @click="cancelUpload">取消</el-button>
      <el-button
        type="primary"
        @click="confirmUpload"
        :loading="loading">确定
      </el-button>
    </template>
  </el-dialog>
</template>


<script>
import FileUpload from "@/components/FileUpload";

export default {
  name: "OrderBillUpload",
  components: {
    FileUpload,
  },
  props: {
    files: Array,
  },
  watch: {
    files: {
      immediate: true,
      handler(value) {
        if (!value || value.length === 0) return;
        this.urls = value
      }
    }
  },
  data() {
    return {
      loading: false,
      showUpload: false, // 上传支付回单弹窗显示
      urls: []
    }
  },
  methods: {
    confirmUpload() {
      if (this.urls.length === 0) {
        return this.$message.error('请上传回单文件')
      }
      this.$emit('change', this.urls)
    },
    cancelUpload() {
      this.toggle(false)
      this.$emit('change')
    },
    toggle(visible = false) {
      if (!visible) {
        this.urls = []
      }
      this.showUpload = visible
    },
  }
}
</script>
