<template>
  <el-dialog
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="close"
    width="500px">
    <div slot="title">确认发放</div>
    <el-form
      ref="form"
      :rules="rules"
      :model="form">
      <warning-block
        class="pwd-tip"
        style="margin-left: 0; margin-right: 0; font-size: 12px;">
        确认发放将向收款人发起打款，请谨慎操作。<br />请确保所上传的姓名、身份证号、收款账号等内容准确，若因非平台原因导致服务报酬发放错误的责任，由贵司自行承担。
      </warning-block>
      <el-form-item
        prop="pwd"
        label="支付密码">
        <pwd-input
          v-if="isSettingTradePwd"
          v-model="form.pwd"
          ref="pwdInput"
        ></pwd-input>
        <router-link
          v-else
          tag="div"
          to="/safe/tradePwdChange">
          <el-button type="text">
            您尚未设置支付密码，请点击设置
          </el-button>
        </router-link>
      </el-form-item>
    </el-form>
    <div slot="footer" class="footer-flex">
      <el-button
        type="text"
        @click="toChangeTradePwd"
      >设置支付密码</el-button>
      <div>
        <el-button
          type="primary"
          @click="confirm">确认发放
        </el-button>
        <el-button @click="close">取消</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import PwdInput from "@/components/PwdInput/index.vue";
import { getTradePwdSetting } from "@/api/safeSetting"

export default {
  components: { PwdInput },
  props: {
    visible: Boolean
  },
  data() {
    return {
      form: {
        pwd: '',
      },
      isSettingTradePwd: false,
      rules: {
        pwd: [
          { required: true, message: '请输入支付密码', validate: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getSafeSetting();
  },
  methods: {
    async getSafeSetting() {
      const { data } = await getTradePwdSetting()
      this.isSettingTradePwd = data.settingTradePwd;
    },
    toChangeTradePwd() {
      this.$router.push("/safe/tradePwdChange");
    },
    close() {
      this.form.pwd = '';
      this.$emit('update:visible', false);
      this.$nextTick(() => {
        this.$refs.pwdInput.clear()
      })
    },
    confirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('confirm', this.form.pwd);
          this.close();
        }
      })
    },
  }
}
</script>

<style
  lang="scss"
  scoped>
.el-form {
  width: 400px;

  .confirm-tip {
    color: #f00;
  }

  .pwd-tip ::v-deep .warning-content {
    line-height: 1.4;
  }
}
.footer-flex {
  display: flex;
  justify-content: space-between;
  align-items: center;

}
</style>
