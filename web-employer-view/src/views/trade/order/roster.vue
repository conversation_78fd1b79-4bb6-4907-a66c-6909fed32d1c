<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">批次号：</span>
            <el-input v-model="searchForm.mchBatchNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">批次名：</span>
            <el-input v-model="searchForm.batchNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">代征主体：</span>
            <el-select
              clearable
              v-model="searchForm.mainstayNo">
              <el-option
                v-for="(item, index) in mainstayList"
                :key="index"
                :label="item.mainstayName"
                :value="item.mainstayNo">
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div
            class="flex-item"
            v-if="!isOuter">
            <span class="flex-item__label">发放方式：</span>
            <el-select
              clearable
              v-model="searchForm.channelType">
              <el-option
                v-for="(item, index) in $dict('ChannelTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code">
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">所属产品：</span>
            <el-select
              clearable
              v-model="searchForm.productNo">
              <el-option
                v-for="(item, index) in productList"
                :key="index"
                :label="item.productName"
                :value="item.productNo">
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">岗位类目：</span>
            <el-select
              clearable
              v-model="searchForm.workCategoryCode"
              style="margin-right: 8px">
              <el-option
                v-for="(item, index) in workCategoryOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              type="datetimerange"
              ref="startPicker"
              v-model="createTimeRange"
              @change="getTimeRange('create', $event)">
            </date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="search(true)">查询
            </el-button>
            <el-button
              type="text"
              @click="clearField">清空搜索条件
            </el-button>
            <el-button
              type="text"
              @click="getExportRecord">查看已导出列表
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-radio-group
        v-model="searchForm.batchStatus"
        @change="statusChange">
        <el-radio-button label="">全部</el-radio-button>
        <template v-if="isOuter">
          <el-radio-button
            v-for="(item, index) in $dict('OfflineOrderStatusEnum')"
            :key="index"
            :label="item.code">
            {{ item.desc }}
          </el-radio-button>
        </template>
        <template v-else>
          <el-radio-button
            v-for="(item, index) in $dict('OrderStatusEnum')"
            :key="index"
            :label="item.code">
            {{ item.desc }}
          </el-radio-button>
        </template>
      </el-radio-group>

      <el-table :data="response.data">
        <el-table-column
          label="批次号"
          prop="platBatchNo"
          width="170"></el-table-column>
        <el-table-column
          label="批次名称"
          prop="batchName"
          width="170"></el-table-column>
        <el-table-column
          label="岗位类目"
          prop="workCategoryName"
          width="170"></el-table-column>
        <el-table-column
          label="所属产品"
          width="100">
          <template v-slot="{row}">
            {{ row.productNo }}<br>
            {{ row.productName }}
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          prop="createTime"
          width="180"></el-table-column>
        <el-table-column
          label="代征主体"
          prop="mainstayName"></el-table-column>
        <el-table-column
          label="订单金额"
          width="150">
          <template v-slot="{row}">
            {{ row.acceptedOrderAmount | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column
          label="发放方式"
          prop="channelType">
          <template
            v-slot="{row}"
            v-if="!isOuter">
            {{ $dictCode('ChannelTypeEnum', row.channelType).desc }}
          </template>
          <div v-else>
            线下发放
          </div>
        </el-table-column>

        <el-table-column
          label="批次状态"
          prop="batchStatus"
          width="120">
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.batchStatus)">
              {{ $dictCode('OrderStatusEnum', row.batchStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          fixed="right"
          width="180">
          <template
            v-slot="{row}">
            <el-button
              v-if="row.batchStatus == 103"
              @click="goDeliver(row)"
              type="text">
              开始发放
            </el-button>
            <el-button
              v-if="(row.batchStatus == 101 || row.batchStatus == 100 || row.batchStatus == 105) && hadPermission('order:listOrderItemPage:view')"
              @click="goDetail(row)"
              type="text">
              查看详情
            </el-button>
            <el-button
              v-if="row.batchStatus === 101"
              type="text"
              @click="deleteOrder(row)"
            >
              删除
            </el-button>
            <el-button
              v-if="row.batchStatus === 103"
              type="text"
              @click="cancelOrder(row)"
            >
              取消发放
            </el-button>

          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :page-sizes="[10, 50]"
        :total="totalRecord"
        @size-change="handleSizeChange"
        @current-change="handleCurrnetChange"
      >
        <span
          @click="forceSearch"
          class="force-next-btn">下一页</span>
      </el-pagination>
    </el-footer>

    <el-dialog
      :visible.sync="resultVisible"
      title="上传结果"
      width="800px"
    >
      <warning-block>
        <span style="color: #444">以下订单校验失败，您可以选择下载文件以查看错误订单，并重新上传</span>
      </warning-block>

      <el-table :data="resultData">
        <el-table-column
          label="错误行"
          prop="line"></el-table-column>
        <el-table-column
          label="收款账户"
          prop="receiveAccountNo"></el-table-column>
        <el-table-column
          label="姓名"
          prop="receiveName"></el-table-column>
        <el-table-column
          label="身份证"
          prop="receiveIdCardNo"
          width="150"></el-table-column>
        <el-table-column
          label="手机"
          prop="receivePhoneNo"
          width="120"></el-table-column>
        <el-table-column
          label="错误详情"
          prop="errorDesc"
          width="150"></el-table-column>
      </el-table>

      <el-footer class="pagination-container">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          background
          :page-sizes="[1, 10, 50]"
          :total="resultTotal"
          :current-page.sync="resultPageParam.pageCurrent"
          :page-size.sync="resultPageParam.pageSize"
          @current-change="() => getErrResult()"
          @size-change="getErrResult(true)"
        ></el-pagination>
      </el-footer>

      <div>
        <el-button
          type="primary"
          @click="goUpload">重新上传
        </el-button>
        <el-button @click="exportFailItemByBatchVo">下载文件</el-button>
      </div>

    </el-dialog>

    <export-record ref="exportRecord"></export-record>

  </div>
</template>

<script>
import { getEmployerPosition, listAllMainstayList } from '@/api/merchant'
import {
  cancelOfflineOrder,
  cancelOrderGrant,
  countOrder,
  deleteOfflineOrder,
  deleteOrder,
  exportFailItemByBatchVo,
  exportOfflineFailItemByBatchVo,
  getFailItemByBatchVo,
  getOfflineFailItemByBatchVo,
  getOrderList,
  offlineOrderList
} from '@/api/order'
import ExportRecord from '@/components/ExportRecord'
import { getProductList } from '@/api/common'
import { toPromise } from "@/utils";

export default {
  components: {
    ExportRecord
  },
  data() {
    return {
      searchForm: { // 搜索表单
        batchStatus: '',
        mchBatchNo: '',
        batchNameLike: '',
        channelType: '',
        mainstayNo: '',
        createBeginDate: '',
        createEndDate: '',
        productNo: '',
      },
      response: { // 查询结果
        data: [],
      },
      totalRecord: 0,
      pageSize: 10,
      pageCurrent: 1,

      productList: [],
      mainstayList: [],

      createTimeRange: [],
      completeTimeRange: [],
      workCategoryOptions: [],

      resultVisible: false, // 错误名单弹窗控制
      resultData: [], // 错误名单列表
      resultPageParam: {
        pageCurrent: 1,
        pageSize: 10,
      },
      resultTotal: 0,
    }
  },
  computed: {
    isOuter() {
      return this.$route.path === '/trade/outer/roster'
    }
  },
  mounted() {
    this.getProductList();
    this.getMainstayList();
    this.search();
    this.getPositionList();
  },
  methods: {
    async getPositionList() {
      const { data } = await getEmployerPosition();
      this.workCategoryOptions = data.map((e) => {
        return {
          label: e.workCategoryName,
          value: e.workCategoryCode,
        };
      });
    },
    clearField() {
      this.searchForm = {
        batchStatus: '',
        mchBatchNo: '',
        batchNameLike: '',
        channelType: '',
        mainstayNo: '',
        createBeginDate: '',
        createEndDate: '',
        productNo: '',
      }
      this.$refs.startPicker.clearTime();
    },
    getTimeRange(type, val) {
      this.searchForm[type + 'BeginDate'] = val && val[0];
      this.searchForm[type + 'EndDate'] = val && val[1];
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    handleCurrnetChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    async getProductList() {
      const { data } = await getProductList({ productType: 0, pageSize: 100 });
      this.productList = data.data;
    },
    async getMainstayList() {
      const { data } = await listAllMainstayList();
      this.mainstayList = data;
    },
    async search(initFlag) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      if (this.isOuter) {
        const { data } = await offlineOrderList({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent,
        })
        this.response = data;
        this.totalRecord = data.totalRecord
      } else {
        const [{ data }, { data: { totalRecord } }] = await Promise.all([
          getOrderList({
            ...this.searchForm,
            pageSize: this.pageSize,
            pageCurrent: this.pageCurrent,
          }),
          countOrder(this.searchForm)
        ])
        this.response = data;
        this.totalRecord = totalRecord;
      }
    },
    goDetail(data) {
      if (data.batchStatus == 105) {
        this.resultVisible = true;
        this.operateRow = data;
        this.getErrResult();
      } else {
        sessionStorage.setItem('createTime', data.createTime);
        const path = this.isOuter ? '/trade/outer/orderDetail' : '/trade/order/detail'
        this.$router.push({
          path,
          query: {
            platBatchNo: data.platBatchNo,
          }
        })
      }
    },
    goDeliver(data) {
      sessionStorage.setItem('deliverInfo', JSON.stringify(data))
      this.$router.push({
        path: '/trade/order/deliver',
        query: {
          platBatchNo: data.platBatchNo,
          isOuter: this.isOuter,
        }
      })
    },
    statusChange(val) {
      this.searchForm.batchStatus = val;
      this.search(true);
    },
    getTagType(status) {
      switch (status) {
        case 100:
          return 'success';
        case 101:
        case 105:
          return 'danger';
        default:
          return 'info'
      }
    },
    forceSearch() {
      this.pageCurrent++;
      this.search();
    },
    async getErrResult(init) {
      if (init) {
        this.resultPageParam.pageCurrent = 1;
      }
      const api = this.isOuter ? getOfflineFailItemByBatchVo : getFailItemByBatchVo
      const { data } = await api({
        ...this.resultPageParam,
        platBatchNo: this.operateRow.platBatchNo
      });
      this.resultData = data.data;
      this.resultTotal = data.totalRecord;
    },
    goUpload() {
      this.resultVisible = false;
      this.$router.push('/trade/order/upload')
    },
    async exportFailItemByBatchVo() {
      const api = this.isOuter ? exportOfflineFailItemByBatchVo : exportFailItemByBatchVo
      await api({
        platBatchNo: this.operateRow.platBatchNo
      });
      this.$message.success('操作成功，请到已导出列表查看')
    },
    getExportRecord() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(this.isOuter ? '56' : '27');
    },
    async deleteOrder({ platBatchNo, createTime }) {
      const [err] = await toPromise(this.$confirm('此操作将删除该条数据，是否继续？', '删除订单', {
        type: 'warning'
      }))
      if (err) return
      const api = this.isOuter ? deleteOfflineOrder : deleteOrder
      const { data } = await api({
        platBatchNo,
        createTime,
      });
      data && this.$message.success(data);
      this.search()
    },
    async cancelOrder({ platBatchNo }) {
      const [err] = await toPromise(this.$confirm(
        '此操作将取消当前批次的发放，是否继续？',
        '取消发放',
        {
          type: 'warning'
        }))
      if (err) return
      const cancelApi = this.isOuter ? cancelOfflineOrder : cancelOrderGrant
      const { data } = await cancelApi({
        platBatchNo,
      });
      data && this.$message.success(data);
      this.search()
    },

  }
}
</script>

<style
  lang="scss"
  scoped>
.box-container {
  padding: 20px 20px;
}
</style>
