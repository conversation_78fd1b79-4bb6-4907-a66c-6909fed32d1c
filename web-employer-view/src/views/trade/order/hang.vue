<template>
  <div class="box-container">
    <warning-block
      icon-color="#165DFF"
      back-color="#E8F3FF"
      color="#1D2129"
      font-size="14">
      <div class="warning-title">挂起订单说明</div>
      <ol class="order-list-container">
        <li>挂起订单是系统判定为异常而暂停发放，需要您提供资料进行判别的订单</li>
        <li>挂单原因请参考提示语，您提供相应资料则意味着贵司同意该笔订单的继续发放，系统将扣款下发，请勿重复提单或通过其他渠道下发，否则将可能造成资金损失</li>
        <li>挂起订单48小时未处理将自动拒绝</li>
      </ol>
    </warning-block>
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户订单号：</span>
            <el-input v-model="searchForm.mchOrderNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">平台流水号：</span>
            <el-input v-model="searchForm.platTrxNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">姓名：</span>
            <el-input v-model="searchForm.receiveName"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">发放方式：</span>
            <el-select
              clearable
              v-model="searchForm.channelType"
            >
              <el-option
                v-for="(item, index) in $dict('ChannelTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">收款账号：</span>
            <el-input v-model="searchForm.receiveAccountNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              type="datetimerange"
              ref="startPicker"
              v-model="createTimeRange"
              @change="getTimeRange('create', $event)"
            >
            </date-picker>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item search-btn-group">
            <el-button
              type="primary"
              @click="search(true)"
            >查询
            </el-button>
            <el-button @click="exportOrderItem">导出</el-button>
            <el-button
              type="text"
              @click="getExportList"
            >查看导出列表
            </el-button>
            <el-button
              type="text"
              @click="clearField"
            >清空搜索条件
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table :data="response.data">

        <el-table-column
          label="创建时间/完成时间"
          width="150"
        >
          <template v-slot="{row}">
            <p>{{ row.createTime }}<br>{{ row.completeTime }}</p>
          </template>
        </el-table-column>

        <el-table-column
          label="商户订单号/平台流水号"
          width="170"
        >
          <template v-slot="{row}">
            <p>{{ row.mchOrderNo }}<br>{{ row.platTrxNo }}</p>
          </template>
        </el-table-column>

        <el-table-column
          label="发放方式"
          prop="channelType"
        >
          <template v-slot="{row}">
            {{ $dictCode('ChannelTypeEnum', row.channelType).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="代征主体"
          prop="mainstayName"
        ></el-table-column>

        <el-table-column
          label="收款账户"
          prop="receiveAccountNo"
          width="170"
        ></el-table-column>
        <el-table-column
          label="姓名"
          prop="receiveName"
        ></el-table-column>
        <el-table-column
          label="身份证"
          prop="receiveIdCardNo"
          width="130"
        ></el-table-column>

        <el-table-column
          label="订单明细(总)金额"
          prop="orderItemAmount"
          width="150"
        >
          <template v-slot="{row}">
            {{ row.orderItemAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          label="实发金额"
          prop="orderItemNetAmount"
          width="150"
        >
          <template v-slot="{row}">
            {{ row.orderItemNetAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          label="服务费"
          prop="orderItemFee"
          width="150"
        >
          <template v-slot="{row}">
            {{ row.orderItemFee | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          label="失败原因"
          prop="errorDesc"
        ></el-table-column>
        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button
              v-if="row.authStatus == 102"
              type="text"
              @click="uploadId(row)"
            >上传身份证
            </el-button>
            <el-button
              v-if="row.signStatus == 300||row.signStatus == 400||row.signStatus == 600"
              type="text"
              @click="signByOrderItem(row)"
            >发起签约
            </el-button>
            <el-button
              type="text"
              @click="rejectOrder(row)">
              取消发放
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-footer class="pagination-container">
        <el-pagination
          v-if="response"
          :total="response.totalRecord"
          :current-page="pageCurrent"
          :page-size="pageSize"
          :page-sizes="[10, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </el-footer>
    </div>
    <export-record ref="exportRecord"></export-record>

    <id-upload-dialog
      ref="idUpload"
      @change="confirmUpload"
    />

  </div>
</template>

<script>
import IdUploadDialog from '@/components/IdUpload/dialog'
import ExportRecord from '@/components/ExportRecord'
import { getMainstayList } from '@/api/merchant'
import { exportHangOrder, getHangList, rejectOrder, signByOrderItem, uploadIdCard } from '@/api/order'
import { toPromise } from '@/utils'

export default {
  components: {
    ExportRecord,
    IdUploadDialog,
  },
  data() {
    return {
      searchForm: { // 搜索表单
        platTrxNo: '',
        channelType: '',
        mchOrderNo: '',
        receiveName: '',
        receiveAccountNo: '',
        createBeginDate: '',
        createEndDate: '',
      },
      response: { // 查询结果
        data: [],
        totalRecord: 0,
      },
      pageSize: 10,
      pageCurrent: 1,

      mainstayList: [],

      createTimeRange: [],
    }
  },
  mounted() {
    this.getMainstayList();
    if (this.$route.query.mchBatchNo) {
      this.searchForm.mchBatchNo = this.$route.query.mchBatchNo;
    }
    this.search()
  },
  methods: {
    async search(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      const {data} = await getHangList({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent
      });
      this.response = data;
    },
    clearField() {
      this.searchForm = {
        platTrxNo: '',
        channelType: '',
        mchOrderNo: '',
        receiveName: '',
        receiveAccountNo: '',
        createBeginDate: '',
        createEndDate: '',
      };
      this.$refs.startPicker.clearTime();
    },
    getTimeRange(type, val) {
      this.searchForm[type + 'BeginDate'] = val && val[0];
      this.searchForm[type + 'EndDate'] = val && val[1];
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    async getMainstayList() {
      const {data} = await getMainstayList();
      this.mainstayList = data;
    },
    async exportOrderItem() {
      const {data} = await exportHangOrder(this.searchForm);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(this.$dictFlag('ReportTypeEnum', 'TRADE_HANG_ORDER_MER').code);
    },
    async signByOrderItem(row) {
      const [err] = await toPromise(this.$confirm('是否确定发起签约？', '提示', {type: 'warning'}))
      if (err) return;
      const [error, res] = await toPromise(signByOrderItem(row.platTrxNo))
      if (error) return;
      const {data} = res
      data && this.$message.success(data)
      this.search()
    },
    async uploadId(row) {
      this.uploadRow = row
      this.$refs.idUpload.toggle();
    },
    async confirmUpload({frontUrl, backUrl}) {
      const {data} = await uploadIdCard({
        idCardFrontUrl: frontUrl,
        idCardBackUrl: backUrl,
        idCardType: 100,
        platTrxNo: this.uploadRow.platTrxNo
      })
      data && this.$message.success(data)
      this.search()
    },
    async rejectOrder({platTrxNo}) {
      const [err] = await toPromise(this.$confirm('是否确认取消发放？', '提示', {type: 'warning'}))
      if (err) return;
      const {data} = await rejectOrder({platTrxNo})
      data && this.$message.success(data)
      this.search()
    }
  },
}
</script>

<style
  lang="scss"
  scoped>
.box-container {
  padding: 20px 20px;
}

.order-list-container {
  padding-left: 5px;
  font-size: 14px;

  li {
    line-height: 1.6 !important;
  }
}

</style>
