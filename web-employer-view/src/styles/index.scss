@import './variables.scss';
@import './mixin.scss';
@import './extend.scss';
@import './transition.scss';
@import './element-ui.scss';
@import "./main-page.scss";
@import './font.scss';
@import './sidebar1.scss';

body {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  line-height: 1.5;
}

label {
  font-weight: 700;
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

img {
  max-width: 100%;
}

// main-container global css
.app-container {
  padding: 20px;
}

.flex-container { // 整个头部的容器
  overflow: hidden;
}

.flex-wrapper { // 整一行的容器（一行时可不用）
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
}

.flex-item { // 某一项的容器
  display: inline-flex;
  align-items: center;
  flex: 1;
  box-sizing: border-box;
  margin-right: 8px;
  max-width: 350px;
}

.flex-item-half {
  @extend .flex-item;
  max-width: 60% !important;
}

.flex-item-main {
  @extend .flex-item;
  max-width: 100% !important;
}

.flex-item__label {
  display: inline-block;
  width: 100px;
  text-align: right;
  font-size: $fontSizeMini;
}

.el-input, .el-select {
  flex: 1;
}

.flex-vertical-item {
  display: flex;
  flex: 1;
  flex-direction: column;
  margin-top: 32px;
  max-width: 25%;

  &.flex-vertical-main {
    max-width: 100%;
  }

  .flex-vertical-label {
    margin-bottom: 8px;
    font-size: $fontSizeMini;
    color: $fontGray;
  }

  .flex-func-link {
    margin-right: 4px;
    color: $mainColor;
    cursor: pointer;
  }
}

.openSidebar .flex-item {
  max-width: 300px;
}

.search-btn-group {
  float: left;
  padding-left: 100px;
}

.search-container {
  padding: 16px 0;
  background: $lightGray;

}

.search-wrapper {
  margin-top: 16px;

  &:first-child {
    margin-top: 0;
  }
}

.el-table {
  margin-bottom: 16px;

  ::v-deep .el-table__header > th {
    background: $lightGray;
  }

  .el-button {
    margin-left: 8px;
  }

  .el-button:first-child {
    margin-right: 0;
  }
}

.pagination-container {
  overflow: hidden;

  .el-pagination {
    float: right;
  }

  .force-next-btn {
    padding: 0 4px;
    cursor: pointer;
  }
}

.box-container {
  width: 100%;
  padding: 16px;
  margin-bottom: 16px;
  background: #fff;

  .create-btn {
    margin-bottom: 16px;
  }
}

.content-container {
  padding-top: 32px;
}


// 弹窗内表单
.el-dialog {

  .el-form-item {
    margin-bottom: 24px;
  }
}

.func-content {
  cursor: pointer;
  color: $mainColor;
}

.example-image {
  width: 250px;
}

// 空白内容提示
.empty-tip {
  color: $fontGray;
}

.color-gray {
  color: rgba(0, 0, 0, 0.447)
}

.color-warning {
  color: $warningColor;
}


.fileBg {
  position: relative;
  width: 100%;
  height: 100%;
  background: url("../assets/document.png") no-repeat;
  background-size: 100% 100%;
}

.fileBg .file-name {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 5px;
  border-top: 1px solid #999;
  background: #fff;
}

.subTitle {
  position: relative;
  padding-left: 30px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;

  &:before {
    position: absolute;
    content: '';
    left: 10px;
    width: 4px;
    height: calc(100% - 10px);
    background: #20a0ff;
    max-height: 36px;
  }
}

// tinymce 上传图片弹窗层级调整
.tox-tinymce-aux {
  z-index: 99999 !important;
}
