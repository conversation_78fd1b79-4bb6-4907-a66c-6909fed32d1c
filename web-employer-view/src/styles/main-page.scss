// 表单页面样式
.page-container {
  padding: 16px 0;
  background: #fff;

  .warning-container {
    margin-left: 16px;
    margin-right: 16px;
  }

  .header-container {
    padding: 32px 0;
  }

  .content-container {
    position: relative;
    margin-top: 32px;
    padding-bottom: 64px;
  }

  .el-form {
    width: 800px;
    margin: 0 auto;
  }

  .el-form-item {
    margin-bottom: 24px;
  }

  .el-form-item__label {
    text-align: right;
  }

  .el-form-item__content {

    .el-textarea {
      width: 80%;
    }
  }

  .form-btn-group {
    position: fixed;
    left: 106px;
    right: 0;
    bottom: 0;
    padding: 8px;
    background: #fff;
    text-align: center;
    box-shadow: 0px -3px 3px 1px #ddd;
    z-index: 10;
  }

  .form-tip {
    margin-top: 4px;
    margin-bottom: 0;
    font-size: 12px;
  }
}

.openSidebar .form-btn-group {
  left: 258px;
}

.result-detail {
  margin: 0 0 20px;
  padding: 20px;
  padding-left: 270px;
  text-align: left;
}

.result-title {
  font-size: 30px;
  font-weight: 600;
}

.result-detail-title {
  font-weight: 600;
}

.result-tip {
  color: #ccc;
}

.func-container {
  margin: 16px 0 0;
}
