// sidebar
$menuText:#bfcbd9;
$menuActiveText:#fff;
$subMenuActiveText:#000; //https://github.com/ElemeFE/element/issues/12951

$menuBg:#304156;
$menuHover:#666;

$subMenuBg:#1f2d3d;
$subMenuHover:#001528;
$subMenuActive: #f2f3f5;

$sideBarWidth: auto;

$leftSideBar_color: #444;

$borderColor: #ebedf0;
$borderGray: #ccc;

$mainColor: #409EFF;
$outerBgColor: #EFF2F8;
$interBgColor: #FFFFFF;
$lightGray: #F0F2F5;
$deepGray: yellow;
$activeGray: #656984;
$successColor: #79EE89;
$warningColor: #FBB84E;
$errorColor: #FA634F;
$lightYellow: #FFFFCD;

$fontColor: #333;
$fontColorLight: #555;
$fontSize: 16px;
$fontSizeMini: 14px;
$fontGray: #97a8be;
$fontNotActive: #c8c9cc;

$navHeight: 70px;


















// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
