import Vue from 'vue'
import Router from 'vue-router'
/* Layout */
import Layout from '@/layout'

Vue.use(Router)

export const constantRoutes = [
  {
    path: '/login',
    component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/login'),
    hidden: true
  },
  {
    path: '/retrievePwd',
    component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/retrievePwd'),
    hidden: true
  },
  {
    path: '/changePwd',
    component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/changePwd'),
    hidden: true
  },
  {
    path: '/selectMch',
    component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/selectMch'),
    hidden: true
  },
  {
    path: '/accountManagement',
    component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/accountManagement'),
    hidden: true
  },
  {
    path: '/setting',
    component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/setting'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/404'),
    hidden: true
  },
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/redirect')
      },
    ]
  },
  {
    path: '/',
    component: Layout,
    hidden: true,
    redirect: '/index',
    children: [
      {
        path: '/index',
        component: () => import(/* webpackChunkName: "constant-group" */ '@/views/index'),
        hidden: true,
      },
      {
        path: '/safe/leaderChange',
        component: () => import(/* webpackChunkName: "constant-group" */ '@/views/system/leaderChange')
      },
      {
        path: '/safe/emailChange',
        component: () => import(/* webpackChunkName: "constant-group" */ '@/views/system/emailChange')
      },
      {
        path: '/safe/tradePwdChange',
        component: () => import(/* webpackChunkName: "constant-group" */ '@/views/system/tradePwdChange')
      },
      {
        path: '/merchant/staffForm',
        component: () => import(/* webpackChunkName: "constant-group" */ '@/views/merchant/staffForm')
      },
      {
        path: '/merchant/auth',
        component: () => import(/* webpackChunkName: "constant-group" */ '@/views/merchant/auth')
      },
      {
        path: '/trade/order/deliver',
        component: () => import(/* webpackChunkName: "trade-group" */ '@/views/trade/order/deliver')
      },
      {
        path: '/finance/issueInvoice/issueDetail',
        component: () => import(/* webpackChunkName: "finance-group" */ "@/views/finance/issueDetail"),
      },
      {
        path: '/account/recharge/cashier',
        component: () => import(/* webpackChunkName: "account-group" */ '@/views/account/recharge/cashier'),
      },
      {
        path: '/trade/sign/upload',
        component: () => import(/* webpackChunkName: "sign-group" */ '@/views/sign/uploadSignFile')
      },
      {
        path: '/audit/detailProcess',
        component: () => import(/* webpackChunkName: "process-group" */ '@/views/waitingHandle/flowDetail'),
      },
      {
        path: '/account/balance/notify',
        component: () => import(/* webpackChunkName: "account-group" */ '@/views/account/balance/balanceNotify')
      },
      {
        path: '/account/balance/notifyManage',
        component: () => import(/* webpackChunkName: "account-group" */ '@/views/account/balance/notifyManage')
      },
      {
        path: '/audit/worklist/detail',
        component: () => import(/* webpackChunkName: "audit-group" */ '@/views/waitingHandle/workform/detail'),
      },
      {
        path: '/notify/list',
        component: () => import(/* webpackChunkName: "notify-group" */ '@/views/notify/list'),
      },
      {
        path: '/job/worker/edit',
        component: () => import(/* webpackChunkName: "job-group"*/ '@/views/job/jobworker/edit'),
        hidden: true,
        name: 'jobworkeredit',
        meta: {
          title: '任务审批',
          noCache: true,
        },
      },
      {
        path: '/job/task/create',
        component: () => import(/* webpackChunkName: "job-group"*/ '@/views/job/task/create'),
      },
      {
        path: '/job/task/detail',
        component: () => import(/* webpackChunkName: "job-group"*/ '@/views/job/task/detail'),
      },
      {
        path: '/job/worker',
        component: () => import(/* webpackChunkName: "job-group" */ '@/views/job/jobworker')
      },
      {
        path: '/finance/invioce/issueInvoiceCkh',
        component: () => import(/* webpackChunkName: "finace-group" */ '@/views/finance/issueInvoice_ckh')
      },
      {
        path: '/finance/invioce/issueInvoiceZxh',
        component: () => import(/* webpackChunkName: "finace-group" */ '@/views/finance/issueInvoice_zxh')
      },
      {
        path: '/finance/invioce/issueInvoiceCep',
        component: () => import(/* webpackChunkName: "finace-group" */ '@/views/finance/issueInvoice_cep')
      },
      {
        path: '/finance/invoice/issueInvoiceOffline',
        component: () => import(/* webpackChunkName: "finace-group" */ '@/views/finance/issueInvoiceOffline')
      },
    ]
  },
];

if (process.env.NODE_ENV === 'development') {
  constantRoutes.push({
    path: '/test',
    component: () => import('@/views/test/index.vue')
  })
}

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router

let routesInfo = {
  /* 系统 */
  "system:safe": () => import(/* webpackChunkName: "system-group" */ "@/views/system/safeSetting"),
  "system:notify:view": () => import(/* webpackChunkName: "system-group" */ "@/views/merchant/notifyManage"),

  /* 商户 */
  "pms:staff:view": () => import(/* webpackChunkName: "merchant-group" */ "@/views/merchant/staff"),
  "pms:role:view": () => import(/* webpackChunkName: "merchant-group" */ "@/views/merchant/role"),
  "pms:info:view": () => import(/* webpackChunkName: "merchant-group" */ "@/views/merchant/info"),
  "pms:merchant:auth": () => import(/* webpackChunkName: "merchant-group" */ "@/views/merchant/auth"),

  "pms:key:view": () => import(/* webpackChunkName: "merchant-group" */ '@/views/merchant/keyManage'),
  "merchantEmployer:plat:view": () => import(/* webpackChunkName: "merchant-group" */ '@/views/merchant/cooperateInfo'),

  /* 交易 */
  "order:batchOrderUpload:upload": () => import(/* webpackChunkName: "trade-group" */ "@/views/trade/order/upload"),
  "order:listOrderPage:view": () => import(/* webpackChunkName: "trade-group" */ "@/views/trade/order/roster"),
  "order:listOrderItemPage:view": () => import(/* webpackChunkName: "trade-group" */ "@/views/trade/order/detail"),
  "order:listHangOrderItemPage:view": () => import(/* webpackChunkName: "trade-group" */ "@/views/trade/order/hang"),
  "trade:order:receipt": () => import(/* webpackChunkName: "trade-group" */ "@/views/trade/order/receipt"),
  "trade:outer:upload": () => import(/* webpackChunkName: "trade-group" */ "@/views/trade/order/upload"),
  "trade:outer:roster": () => import(/* webpackChunkName: "trade-group" */ "@/views/trade/order/roster"),
  "trade:outer:order": () => import(/* webpackChunkName: "trade-group" */ "@/views/trade/order/detail"),

  /* 财务 */
  "finance:invoice:info": () => import(/*webpackChunkName: "finance-group" */ "@/views/finance/issueInfo"),
  "finance:invoice:address": () => import(/*webpackChunkName: "finance-group" */ "@/views/finance/mailAddress"),
  "invoice:apply": () => import(/*webpackChunkName: "finance-group" */ "@/views/finance/issueInvoice"),
  "invoice:view": () => import(/*webpackChunkName: "finance-group" */ "@/views/finance/issueRecord"),
  "invoice:tax": () => import(/* webpackChunkName: "finance-group" */ "@/views/finance/taxProfile"),

  /* 电子签约 */
  'sign:signRecord:view': () => import(/* webpackChunkName: "sign-group" */ '@/views/sign/signInfo'),

  /* 账户 */
  'account:chargeRecord:list': () => import(/* webpackChunkName: "account-group" */ "@/views/account/recharge/chargeRecord"),
  'account:recharge:view': () => import(/* webpackChunkName: "account-group" */ "@/views/account/recharge/recharge"),
  'account:overview:balance': () => import(/* webpackChunkName: "account-group" */ "@/views/account/checkBalance"),
  'account:withdraw:view': () => import(/* webpackChunkName: "account-group" */ '@/views/account/withdraw/withdraw'),
  'account:withdrawRecord:list': () => import(/* webpackChunkName: "agent-group" */ '@/views/account/withdraw/withdrawRecord'),
  'account:agreement': () => import(/* webpackChunkName: "agreement-group"*/ '@/views/account/agreement/list'),

  /* 待办 */
  'audit:process': () => import(/* webpackChunkName: "process-group" */ '@/views/waitingHandle/flow'),
  'audit:workform:view': () => import(/* webpackChunkName: "process-group" */ '@/views/waitingHandle/workform/list'),

  /* 数据 */
  'data:manage:person': () => import(/* webpackChunkName: "data-group"*/ '@/views/data/person/list'),

  /* 任务 */
  'job:task': () => import(/* webpackChunkName: "job-group" */ '@/views/job/task/list'),

  /**账单管理 */
  'bill:view': () => import(/* webpackChunkName: "bill-group" */ '@/views/bill/index'),

};

export const addRoutes = function (functions) {
  let menuRoute = {
    path: '/',
    component: Layout,
    children: []
  };
  functions.filter(f => f.type === 1).forEach(f => {
    menuRoute.children.push({
      component: routesInfo[f.permissionFlag],
      path: f.url,
      meta: {
        id: f.id,
        title: f.name,
        permissionFlag: f.permissionFlag
      }
    });
  });
  router.addRoutes([menuRoute]) // 添加路由
}
