import { getIndexMessage, getIndexNotify } from '@/api/notify'

const state = {
  messageCount: 0, // 未读消息数
  notifyList: null, // 消息列表
  dynamicList: null, // 动态列表
}

const mutations = {
  SET_MESSAGE_COUNT: (state, count) => {
    state.messageCount = count
  },
  SET_NOTIFY_LIST: (state, list) => {
    state.notifyList = list
  },
  SET_DYNAMIC_LIST: (state, list) => {
    state.dynamicList = list
  },
}

const actions = {
  async getNotifyList({ commit }) {
    try {
      const { data: { array, count } } = await getIndexNotify()
      commit('SET_NOTIFY_LIST', array)
      commit('SET_MESSAGE_COUNT', count)
      return array
    } catch (error) {
      commit('SET_NOTIFY_LIST', null)
      commit('SET_MESSAGE_COUNT', 0)
    }
  },
  async getMessageList({ commit }) {
    try {
      const { data } = await getIndexMessage()
      commit('SET_DYNAMIC_LIST', data)
    } catch (error) {
      commit('SET_DYNAMIC_LIST', null)
    }
  },
  async getNotifyAndMessage({ dispatch }) {
    const [array] = await Promise.all([
      dispatch('getNotifyList'),
      dispatch('getMessageList'),
    ])
    return array
  }
}

export default {
  state,
  mutations,
  actions,
}
