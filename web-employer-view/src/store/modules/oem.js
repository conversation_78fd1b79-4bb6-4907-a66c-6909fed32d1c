const state = {
  isLand: false, // 标识信息设置状态
  info: {},
}

const mutations = {
  SET_INFO(state, info) {
    state.info = info
    for (let prop in info) {
      let val = info[prop]
      if (val.then) {
        // 动态 import
        val.then(p => {
          state.info[prop] = p.default
        })
      } else if (typeof val === "string" && /url$/i.test(prop)) {
        state.info[prop] = (process.env.VUE_APP_FILE_URL || '') + val
      }
    }
    state.isLand = true
  }
}
const actions = {
  setOemInfo({ commit }, info) {
    if (!info) return;
    commit('SET_INFO', info)
  }
}

export default {
  state,
  mutations,
  actions,
}
