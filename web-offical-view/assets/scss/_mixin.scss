@mixin top-bottom-center($pos) {
  position: $pos;
  top: 50%;
  transform: translateY(-50%);
}

@mixin left-right-center($pos) {
  position: $pos;
  left: 50%;
  transform: translateX(-50%);
}

@mixin all-zero($pos) {
  position: $pos;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}

@mixin all-center($pos) {
  position: $pos;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
}


// 单行文字垂直居中
@mixin vertical-center($height) {
  height: $height;
  line-height: $height;
}