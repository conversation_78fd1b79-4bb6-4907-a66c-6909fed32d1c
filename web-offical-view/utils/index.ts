function getAssetsUrl(url: string) {
  return function (fileUrl: string): string {
    return new URL(`../assets/${url}/${fileUrl}`, import.meta.url).href
  }
}

const getImg = getAssetsUrl('img')
const getJs = getAssetsUrl('js')


function debounce(func: Function, delay: number,immediate?: boolean): (...args: any[]) => void {
  let timer: number
  return function(...args) {
    if (timer) clearTimeout(timer)
    if (immediate && !timer) {
      func.apply(null, args)
    }
    timer = window.setTimeout(() => {
      func.apply(null, args)
    }, delay)
  }
}

export {
  getImg,
  getJs,
  debounce,
}