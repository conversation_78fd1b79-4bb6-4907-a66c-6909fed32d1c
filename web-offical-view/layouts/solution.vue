<template>
  <div class="solution-layout-container">
    <slot></slot>
    <div class="solution-result-block">
      <ContainerTitle>优化成效</ContainerTitle>
      <SolutionResult></SolutionResult>
    </div>
    <div class="solution-list-container">
      <ContainerTitle>其他行业解决方案</ContainerTitle>
      <SolutionList @click="onClick"></SolutionList>
    </div>
  </div>
</template>

<script
  setup
  lang="ts">
  const openDialog = inject<Function>('openDialog')
  const onClick = () => {
    openDialog?.()
  }
</script>

<style
  scoped
  lang="scss">
@media screen and (min-width: 768px) {
.solution-list-container {
  margin-top: 82px;
  margin-bottom: 167px;
}
}


//res
.solution-result-block {
  margin-top: 80px;
  box-sizing: border-box;
  padding: 81px 0 85px;
  background-image: url("~/assets/img/result-back.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}


@media screen and (max-width: 768px) {
  .solution-list-container {
    margin-top: 3.5rem;
    margin-bottom: 4rem;
  }
  .solution-result-block {
    margin-top: 3.75rem;
    padding: 2.9rem 0 0;
  }
}

</style>