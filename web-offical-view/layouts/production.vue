<template>
  <div class="production-layout-container">
    <slot></slot>
    <!-- 科技主力 -->
    <div
      v-if="isProduction && route.path !== '/production/zfn'"
      class="help-container"
    >
      <ContainerTitle>科技助力效率提升</ContainerTitle>
      <div
        class="help-pc-container ma"
        v-if="!isMobile">
        <LinkPc></LinkPc>
      </div>
      <LinkMobile v-else></LinkMobile>
    </div>
    <div class="production-layout-common">
      <!-- 接入方式 -->
      <div
        class="income-container"
        v-if="isProduction"
      >
        <ContainerTitle>
          {{ route.path === '/production/zfn' ? '接入流程' : '多种接入方式' }}
        </ContainerTitle>
        <template v-if="route.path !== '/production/zfn'">
          <div class="income-switch-container text-center">
            <SwitchContainer
              :width="isMobile ? '17.1rem' : undefined"
              inactive-text="SaaS平台"
              active-text="API接入"
              :inactive-value="0"
              :active-value="1"
            ></SwitchContainer>
          </div>
        </template>
        <div class="income-list-container text-center">
          <div
            class="income-list-tip"
            v-if="route.path !== '/production/zfn'">针对希望实现降本增效的用工企业，推荐使用SaaS平台！无需系统对接即可入驻使用，免去系统维护成本，悦享海量功能升级
          </div>
          <div
            class="income-list flex justify-between items-center"
            v-if="!isMobile">
            <template
              v-for="(item, index) in incomeList"
              :key="index"
            >
              <div class="income-item-container">
                <div class="income-item-img">
                  <ClientOnly>
                    <img
                      v-lazy="getImg(`income-icon${index}.png`)"
                      :alt="item">
                  </ClientOnly>
                </div>
                <div class="income-item-name">{{ item }}</div>
              </div>
              <div
                class="income-arrow"
                v-if="index < incomeList.length - 1"
              >
                <SvgoTypeArrow></SvgoTypeArrow>
              </div>
            </template>
          </div>

          <div
            class="income-mobile-list flex flex-wrap justify-between"
            v-else>
            <div
              class="income-mobile-item"
              v-for="(item, index) in incomeList"
              :key="index">
              <div class="income-mobile-img  relative">
                <ClientOnly>
                  <img
                    v-lazy="getImg(`income-mobile${index}.png`)"
                    alt="icon">
                </ClientOnly>
                <div class="income-mobile-text abs-lr-center">{{ item }}</div>
              </div>
              <div class="income-mobile-index text-center">
                <span class="income-index">{{ index + 1 }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--核心优势-->
      <CoreAdvanced></CoreAdvanced>
    </div>
  </div>
</template>


<script
  setup
  lang="ts">
import { Ref } from "vue";

const route = useRoute()
const isProduction = computed(() => (route.fullPath && route.fullPath.startsWith('/production')))
const isMobile = inject<Ref<boolean>>('isMobile')

interface StepMap {
  [prop: string]: string
}
const step3Map: StepMap = {
  '/production/zfn': '定制OEM系统',
}
const incomeList: string[] = ['沟通合作顾问', '提交合作信息', (step3Map[route.path] || '开通系统账号'), '协助业务运转']
</script>

<style
  lang="scss"
  scoped>
// help
@media screen and (min-width: 768px) {
  .help-container {
    padding: 50px 0;
    background: $back-gray;
  }
}

@media screen and (max-width: 768px) {
  .help-container {
    margin: 5rem 0 0;
  }
}

.help-pc-container {
  margin-top: 70px;
  width: 70%;
}


.production-layout-common {
  margin: 0 auto;
}


// income-container
@media screen and (min-width: 768px) {
  .income-container {
    margin-top: 50px;
    margin-bottom: 113px;

    .income-switch-container {
      margin: 50px 0 30px;
    }
  }

  .income-list-container {
    margin: 0 23vw;

    .income-list {
      margin-top: 55px;
    }

    .income-list-tip {
      color: #666;
      margin: 0 80px;
    }
  }

}

@media screen and (max-width: 768px) {
  .income-container {
    margin-top: 5rem;
    .income-switch-container {
      margin: 2rem 0 1.5rem;
    }
  }

  .income-list-container {
    margin: 0 5.7rem 2rem;
    .income-list {
      margin-top: 2rem;
    }
    .income-list-tip {
      margin: 1.5rem 1.5rem;
      color: #929292;
      font-size: 1rem;
      line-height: 1.6rem;
    }
  }
  .income-mobile-list {
    margin-top: 2rem;
  }

  .income-mobile-item {
    position: relative;
    margin-bottom: 3rem;

    .income-mobile-img {
      width: 11.5rem;
      height: 12.4rem;
    }

    .income-mobile-text {
      bottom: 1rem;
      font-size: 1.2rem;
      font-weight: bold;
      color: #3B3B3B;
      white-space: nowrap;
    }

    .income-mobile-index {
      margin: 2rem 2rem 0;
    }

    .income-index {
      display: inline-block;
      width: 1.75rem;
      height: 1.75rem;
      line-height: 1.75rem;
      font-size: 1.3rem;
      border-radius: 50%;
      background-color: $primary-color;
      color: #fff;
    }


    &:nth-child(2n+1)::after {
      content: '';
      display: block;
      width: 50%;
      border-bottom: 2px dashed #D5D5D5;
      position: absolute;
      bottom: .7rem;
      left: 65%;
      z-index: -10;
    }

    &:nth-child(2n)::after {
      content: '';
      display: block;
      width: 50%;
      border-bottom: 2px dashed #D5D5D5;
      position: absolute;
      bottom: .7rem;
      right: 65%;
      z-index: -10;
    }
  }
}


.income-list {
  .income-item-img {
    width: 92px;
  }

  .income-item-name {
    margin-top: 20px;
    color: $title-color;
    font-size: 14px;
    font-weight: bold;
  }

  .income-arrow {
    color: $primary-color;
  }

}


</style>