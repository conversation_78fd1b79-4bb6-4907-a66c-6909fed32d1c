<template>
  <Transition name="fade">
    <div
      class="toast-container"
      v-show="visible">
      <div class="toast-core">{{ str }}</div>
    </div>
  </Transition>
</template>

<script
  lang="ts"
  setup>
const str = ref<string>()
const visible = ref<boolean>(false)
const timer = ref()
const duration = ref(2500)
defineExpose({
  open
})

function open(val: string) {
  visible.value = true
  str.value = val
  timer.value = setTimeout(() => {
    close()
  }, duration.value)
}

function close() {
  visible.value = false
}
</script>

<style
  scoped
  lang="scss">
.toast-core {
  position: fixed;
  top: 40%;
  left: 50%;
  padding: 10px;
  border-radius: 5px;
  transform: translate3d(-50%, -50%, 0);
  background: rgba(0, 0, 0, .7);
  font-size: 16px;
  color: #fff;
  z-index: 99999;
}
</style>