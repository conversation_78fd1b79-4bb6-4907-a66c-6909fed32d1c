<template>
  <div v-cloak>
    <NavbarContainer
      :is-ready="isReady"
      @click="openDialog">
    </NavbarContainer>

    <NuxtLayout :name="layout">
      <NuxtPage @banner-click="openDialog"></NuxtPage>
    </NuxtLayout>
    <!--mobile form-->
    <div
      v-if="isMobile &&route.path !== '/relation'"
      ref="mobileRef">
      <MobileForm @submit="onSubmit"></MobileForm>
    </div>
    <!--footer-->
    <FooterContainer @click="openDialog"></FooterContainer>


    <!--dialog-->
    <ClientOnly v-if="!isMobile">
      <Dialog v-model:visible="visible">
        <div class="dialog-content relative">
          <img
            v-show="!showRes"
            src="~/assets/img/form-back.png"
            alt="" />
          <img
            v-show="showRes"
            src="~/assets/img/form-result.png"
            alt="提交成功" />
          <div
            class="dialog-form abs-all-center"
            v-if="!showRes">
            <div class="form-container">
              <FormInput
                v-model="form.businessName"
                placeholder="公司名称"></FormInput>
              <FormInput
                v-model="form.industry"
                placeholder="所在行业"></FormInput>
              <FormInput
                v-model="form.username"
                placeholder="您的姓名"></FormInput>
              <FormInput
                v-model="form.phone"
                placeholder="手机号"></FormInput>
              <FormInput
                v-model="form.remark"
                type="textarea"
                placeholder="简要描述您的需求"></FormInput>
            </div>
            <div class="text-center">
              <button
                class="primary-btn form-btn"
                v-width="130"
                @click="onBtnClick">
                提交信息
              </button>
            </div>
          </div>
        </div>
      </Dialog>
    </ClientOnly>

  </div>
</template>

<script
  lang="ts"
  setup>
import { sendMail } from "~/api/common";
import type { Form } from '~/share'

const route = useRoute()

// 移动端标识切换
const mobileWidth = 768
const isMobile = ref(false)
const isReady = ref(false) // 防止移动端菜单闪屏
function handleResize() {
  isMobile.value = (document.documentElement.clientWidth <= mobileWidth)
}

const wrappedResizeHandle = debounce(handleResize, 20)
onMounted(() => {
  handleResize()
  window.addEventListener('resize', wrappedResizeHandle)
  isReady.value = true
})

provide('isMobile', isMobile)

const layout = computed(() => {
  let isHome = route.path === '/'
  let current = route.fullPath.split('/').filter(Boolean)[0]
  if (isHome || current === 'relation') return 'production'
  return current
})

// dialog
const visible = ref<boolean>(false)
const openDialog = () => {
  if (!isMobile.value) {
    form = Object.assign(form, {
      type: '100',
      businessName: '',
      industry: '',
      username: '',
      remark: '',
      phone: '',
    })
    showRes.value = false
    visible.value = true
  } else {
    window.scrollTo({ top: mobileRef.value.offsetTop, behavior: 'smooth' })
  }
}
provide('openDialog', openDialog)

const { $toast } = useNuxtApp()
let form = reactive<Form>({ type: '100' } as Form)
const loading = ref<boolean>(false)
const showRes = ref<boolean>(false)

const onBtnClick = () => {
  onSubmit(form)
}
const onSubmit = async (submitForm: Form) => {
  if (loading.value) return
  if (!submitForm.username || !submitForm.phone) {
    return $toast('请完善信息')
  }
  loading.value = true
  await sendMail(toRaw(submitForm))
  $toast('提交成功')
  loading.value = false
  showRes.value = true
}

const mobileRef = ref()
</script>


<style
  lang="scss"
  scoped>


//form
.dialog-content {
  width: 45vw;
  max-width: 592px;
}

.dialog-form {
  width: 68%;
  top: 60%;

  .form-btn {
    margin-top: 2vw;
  }

  .form-container {
    width: 100%;
  }
}


</style>