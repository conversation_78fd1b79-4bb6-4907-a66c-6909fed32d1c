export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.directive('width', {
    created(el, binding) {
      el.style.width = normalizeWidth(binding.value)
    },
    updated(el, binding) {
      el.style.width = normalizeWidth(binding.value)
    },
    getSSRProps(binding) {
      const width = normalizeWidth(binding.value)
      return {
        style: { width }
      }
    }
  })
})

function normalizeWidth(width: number|string) {
  if (typeof width === 'number') {
    width = width + 'px'
  }
  return width
}