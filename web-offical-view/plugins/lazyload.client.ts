import lozad from 'lozad'

export default  defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.directive('lazy', {
    mounted(el, binding) {
      if (!binding.value) return
      el.setAttribute('data-src', binding.value)
      const observer = lozad(el);
      observer.observe();
    },
    updated(el, binding) {
      el.setAttribute('data-src', binding.value)
      if (el.src !== binding.value) {
        el.dataset.loaded = ''
        const observer = lozad(el);
        observer.observe();
      }
    }
  })
})