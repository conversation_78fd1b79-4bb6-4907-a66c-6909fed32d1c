{"name": "nuxt-app", "private": true, "scripts": {"build": "nuxt build --dotenv .env.dev", "dev": "nuxi dev --dotenv .env.local", "generate:dev": "nuxt generate --dotenv .env.dev", "generate:test": "nuxt generate --dotenv .env.test", "generate:prod": "nuxt generate --dotenv .env.prod", "preview": "nuxt preview --dotenv .env.dev", "postinstall": "nuxt prepare", "update:nuxt": "nuxi upgrade --force"}, "devDependencies": {"@nuxt/image": "1.0.0-rc.1", "@types/node": "^18", "nuxt": "^3.5.1", "nuxt-svgo": "^3.1.0", "sass": "^1.62.1"}, "dependencies": {"@unocss/nuxt": "^0.51.13", "lozad": "^1.16.0", "nuxt-swiper": "^1.1.0"}}