<template>
  <div class="relation-page-container">
    <Banner
      img-url="relation"
      :show-btn="isMobile"
      @click="emits('banner-click')"
      text-width="25rem">
      <div
        v-if="!isMobile"
        class="abs-all-center text-center">
        <div class="relation-banner-text">
          <div class="relation-header">生态合作伙伴</div>
          <div class="relation-text">诚邀供应商、有自由职业者需求的企业以及有商务资源的渠道合伙人进行生态合作，以科技赋能灵活就业！
          </div>
          <button
            class="primary-btn"
            v-width="130">立即咨询
          </button>
        </div>
      </div>
      <template
        v-if="isMobile"
        v-slot:header>
        <span style="color: #fff;">
        生态合作伙伴
        </span>
      </template>
      <template
        v-if="isMobile"
        v-slot:tip>
        <span style="color: #fff;">
          诚邀供应商、有自由职业者需求的企业以及有商务资源的渠道合伙人进行生态合作，以科技赋能灵活就业！
        </span>
      </template>

    </Banner>
    <!--联系-->
    <div class="contact-container">
      <ContainerTitle>立即合作</ContainerTitle>
      <div class="text-center contact-switch-container">
        <SwitchContainer
          v-model="form.type"
          :width="isMobile ? '22rem' : 288"
          inactive-text="我是财务税服务商"
          active-text="我是渠道代理"
          inactive-value="100"
          active-value="101"></SwitchContainer>
      </div>
      <div class="contact-tips text-center">请留下您的联系方式，我们将会尽快为您匹配专属业务经理</div>
      <div
        class="form-container"
        v-if="form.type === '100'">
        <FormInput
          v-model="form.username"
          :mobile="isMobile"
          placeholder="姓名"></FormInput>
        <FormInput
          v-model="form.phone"
          :mobile="isMobile"
          placeholder="手机号"></FormInput>
        <FormInput
          v-model="form.businessName"
          :mobile="isMobile"
          placeholder="公司名称"></FormInput>
        <FormInput
          v-model="form.remark"
          :mobile="isMobile"
          type="textarea"
          placeholder="需求备注"></FormInput>
        <div class="text-center">
          <button
            class="primary-btn form-btn"
            @click="onSubmit">
            {{ isMobile ? '提交信息' : '立即咨询' }}
          </button>
        </div>
      </div>
      <div
        class="form-container"
        v-else-if="form.type === '101'">
        <FormInput
          v-model="form.username"
          :mobile="isMobile"
          placeholder="姓名"></FormInput>
        <FormInput
          v-model="form.phone"
          :mobile="isMobile"
          placeholder="手机号"></FormInput>
        <FormInput
          v-model="form.industry"
          :mobile="isMobile"
          placeholder="所在行业"></FormInput>
        <FormInput
          v-model="form.remark"
          :mobile="isMobile"
          type="textarea"
          placeholder="需求备注"></FormInput>
        <div class="text-center">
          <button
            class="primary-btn form-btn"
            @click="onSubmit">
            {{ isMobile ? '提交信息' : '立即咨询' }}
          </button>
        </div>
      </div>
    </div>
    <!--  地图-->
    <div class="address-container">
      <ContainerTitle>联系我们</ContainerTitle>
      <div
        class="address-info-container flex items-center"
        :class="{'flex-col': isMobile}">
        <div
          class="address-item-container"
          :class="{'flex-grow': !isMobile}">
          <div
            class="address-title"
            v-if="!isMobile">联系方式
          </div>
          <div class="address-item">电话：{{ contactInfo.phone }}</div>
          <div class="address-item">邮箱：{{ contactInfo.email }}</div>
          <div class="address-item">地址：{{ contactInfo.address }}
            <div
              class="abs-tb-center address-arrow"
              v-if="!isMobile">
              <img
                src="~/assets/img/address-arrow.png"
                alt=""></div>
          </div>
        </div>
        <div
          class="address-img">
          <ClientOnly>
            <img
              :src="getImg(`address${isMobile ? '-mobile': ''}.png`)"
              alt="address">
          </ClientOnly>
        </div>
      </div>
    </div>
  </div>
</template>

<script
  setup
  lang="ts">
import { sendMail } from "~/api/common";
import { Ref } from "vue";
import { contactInfo } from "../setting";

interface Form {
  type: string
  businessName?: string
  industry?: string
  username: string
  phone: string
  remark: string
}

const route = useRoute()
let form = reactive<Form>({ type: route.query.type || '100' } as Form)
const loading = ref<boolean>(false)

const { $toast } = useNuxtApp()

const onSubmit = async () => {
  if (loading.value) return
  if (!form.username || !form.phone) {
    return $toast('请完善信息')
  }
  loading.value = true
  await sendMail(form)
  $toast('提交成功')
  loading.value = false
}

const emits = defineEmits<{
  (e: 'banner-click'): void
}>()

const isMobile = inject<Ref<boolean>>('isMobile')
</script>

<style
  scoped
  lang="scss">
.address-container {
  margin: 0 auto 74px;
  padding: 38px 0 51px;
  background: #F2F8FF;

  .address-info-container {
    width: 976px;
    margin: 47px auto 0;
    padding: 37px 38px;
    background: #fff;
  }

  .address-item-container {
    padding-left: 60px;
    padding-right: 115px;
  }

  .address-title {
    font-size: 16px;
    font-weight: 500;
    color: #000;
  }

  .address-item {
    position: relative;
    margin: 20px 0;
    font-size: 12px;
    font-weight: 400;
    color: #000;

    &::before {
      content: '';
      display: block;
      position: absolute;
      top: 2px;
      left: -18px;
      width: 9px;
      height: 9px;
      background: #0D79FA;
      border-radius: 3px;
    }

    .address-arrow {
      width: 18px;
      left: calc(100% + 50px);
    }
  }

  .address-img {
    width: 534px;
  }
}

// contact
.contact-container {
  margin: 60px 0;
}

.contact-switch-container {
  margin: 41px 0 30px;
}

.contact-tips {
  margin-bottom: 34px;
  font-size: 14px;
  color: #929292;
}

//form
.form-container {
  width: 394px;
  margin: 0 auto;
}

.form-btn {
  width: 130px;
  border-radius: 23px
}

//banner

.relation-header {
  font-size: 49px;
  font-weight: bold;
  color: #FFFFFF;
}

.relation-text {
  margin: 20px 0;
  font-size: 15px;
  font-weight: 400;
  line-height: 26px;
  color: #FFFFFF;
}


@media screen and (max-width: 768px) {
  .address-container {
    margin: 4rem 0;
    padding: 0;
    background: transparent;

    .address-info-container {
      margin-top: 2rem;
      padding: 0;
      width: 100%;
    }

    .address-img {
      box-sizing: border-box;
      padding: 0 1.55rem;
      width: 100%;
    }

    .address-item-container {
      padding-left: 2.95rem;
      padding-right: 0;
    }

    .address-item {
      &::before {
        left: -1.3rem;
        width: 0.5rem;
        height: 0.7rem;
        border-radius: 0.25rem;
      }
    }
  }

  .contact-container {
    margin: 0;
  }
  .contact-switch-container {
    margin: 2rem 0;
  }
  .contact-tips {
    font-size: 1rem;
    margin-bottom: 2rem;
    color: #999;
  }
  .form-container {
    width: auto;
    margin: 0 1.5rem;
  }

  .form-btn {
    margin-top: 1rem;
    width: 15.9rem;
    border-radius: 0.5rem;
  }
}
</style>