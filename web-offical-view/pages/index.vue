<template>
  <Banner
    img-url="home"
    @click="emits('banner-click')">
    <template v-slot:header>用科技<br />赋能灵活就业</template>
    <template v-slot:tip>正佳科技出品 劳动力解决方案科技平台</template>
  </Banner>
  <div class="content-container">
    <!-- 看板 -->
    <div class="home-kanban-container">
      <div
        class="kanban-block"
        v-if="!isMobile">
        <div class="kanban-container flex justify-center">
          <div class="kanban-type flex flex-col justify-between">
            <div
              v-for="(item, index) in kanban"
              :key="index"
              class="kanban-type-item relative"
              :class="{ 'kanban-type-item-active': index === activeKanban }"
              @mouseenter="onMouseEnter(index)"
            >
              <SvgoTypeArrow
                class="kanban-type-arrow abs-tb-center"
                v-show="index === activeKanban"
              ></SvgoTypeArrow>
              <div class="kanban-type-prod relative">
                {{ item.prod }}
              </div>
              <div class="kanban-type-desc">{{ item.desc }}</div>
            </div>
          </div>
          <div class="kanban-content grid">
            <div
              class="kanban-content-item"
              v-for="(item, index) in activatedKanbanContent"
              :key="index"
            >
              <div class="kanban-content-icon ma">
                <ClientOnly>
                  <img
                    :src="getImg(`kanban-icon${index}.png`)"
                    :alt="item.title"
                  >
                </ClientOnly>
              </div>
              <div class="kanban-content-title text-center"><span>{{ item.title }}</span></div>
              <div class="kanban-content-text">{{ item.text }}</div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="kanban-swiper-block"
        v-if="isMobile">
        <ContainerTitle>产品介绍</ContainerTitle>
        <Swiper
          :modules="[SwiperPagination]"
          :pagination="{el: '.swiper-pagination', bulletClass: 'swiper-pagination-bullet kanban-swiper-bullet'}"
          class="kanban-swiper">
          <SwiperSlide
            class="kanban-swiper-item"
            v-for="(item, index) in kanban"
            :key="index">
            <div class="kanban-swiper-content">
              <div class="kanban-swiper-title flex items-center">
                <div class="kanban-swiper-index">0{{ index + 1 }}</div>
                <div class="kanban-swiper-title_inner flex-grow">
                  <div class="kanban-swiper-prod">{{ item.prod }}</div>
                  <div class="kanban-swiper-desc">{{ item.desc }}</div>
                </div>
              </div>
              <div class="kanban-prod-content">
                <div
                  class="kanban-content-item flex items-center"
                  v-for="(c, key) in item.content"
                  :key="key">
                  <div class="kanban-swiper-icon">
                    <ClientOnly>
                      <img
                        :src="getImg(`kanban-swiper-icon-${key}.png`)"
                        :alt="c.title"
                      >
                    </ClientOnly>
                  </div>
                  <div class="kanban-content-desc flex-grow">
                    <div class="kanban-content-title func-content">{{ c.title }}</div>
                    <div class="kanban-content-text">{{ c.text }}</div>
                  </div>
                </div>
              </div>
              <div class="kanban-swiper-btn abs-lr-center" v-if="index === 0">
                <a :href="item.url">
                  <button
                    class="primary-btn"
                    v-width="'15.88rem'">了解更多
                  </button>
                </a>
              </div>
            </div>
          </SwiperSlide>
        </Swiper>
        <div class="kanban-pagination swiper-pagination"></div>
      </div>
    </div>
    <!-- 客户 -->
    <div class="home-consumer-container">
      <ContainerTitle>{{ isMobile ? '行业解决方案' :'我们的客户类型' }}</ContainerTitle>
      <SolutionList @click="emits('banner-click')"></SolutionList>
    </div>
    <!-- 合作 -->
    <div class="home-partner-container">
      <ContainerTitle>机构合作伙伴</ContainerTitle>
      <div class="partner-list flex">
        <div
          class="partner-img"
          v-for="(item, index) in 3"
          :key="index">
          <ClientOnly>
            <img
              v-lazy="getImg(`partner-${isMobile ? 'mini'+item : item}.png`)"
              class="lozad"
              alt="partner">
          </ClientOnly>
        </div>
      </div>
    </div>
  </div>
</template>


<script
  lang="ts"
  setup>
import { Ref } from "vue";

interface Kanban {
  prod: string
  desc: string
  content: any[]

  [prop: string]: any
}

const kanban: Kanban[] = [
  {
    prod: '智享汇',
    desc: '企业和批量个人经营者的服务中心',
    content: [
      { title: '自由职业者匹配', text: '为企业进行批量个人经营者匹配对接，项目记录管理等' },
      { title: '经营项目管理', text: '基于用工企业标准选用个人经营者提供指定服务' },
      { title: '批量结算', text: '三方共管账户完成资金出款，不留资金黑洞' },
      { title: '税务合规', text: '协助个人完成税务合规流程' },
    ],
    url: '/production/zxh',
  },
  {
    prod: '创客汇',
    desc: '企业和零散个人经营者的服务中心',
    content: [
      { title: '任务管理', text: '一键发布工作任务，全流程监控任务结果' },
      { title: '签约管理', text: '可按任务管理批量自由职业者，支持多种灵工人员电子签约方式' },
      { title: '灵工结算', text: '按任务成果验收结算个人所得' },
      { title: '税务合规', text: '协助个人完成税务合规流程' },
    ],
    url: '/production/zxh',
  },
  {
    prod: '聚客汇',
    desc: '企业和取得劳务报酬个人的服务中心',
    content: [
      { title: '任务管理', text: '一键发布工作任务，全流程监控任务结果' },
      { title: '签约管理', text: '可按任务管理批量自由职业者，支持多种灵工人员电子签约方式' },
      { title: '灵工结算', text: '按任务成果验收结算个人所得' },
      { title: '税务合规', text: '协助个人完成税务合规流程' },
    ],
    url: '/production/zxh',
  },
]
const activeKanban = ref(0)
const activatedKanbanContent = computed(() => kanban[activeKanban.value].content)

const emits = defineEmits<{
  (e: 'banner-click'): void
}>()

function onMouseEnter(index: number) {
  activeKanban.value = index
}

const isMobile = inject<Ref<boolean>>('isMobile')
</script>


<style
  lang="scss"
  scoped>
// kanban
.kanban-block {
  background: linear-gradient(to bottom, #F0F8FF, #CAE3FE);

  .kanban-container {
    padding: 56px 0 49px;
  }

  .kanban-type {
    margin-top: 8px;
    margin-right: 30px;
    width: 421px;

    &-item {
      box-sizing: border-box;
      width: 100%;
      margin-bottom: 43px;
      padding: 30px 0 30px;
      border-radius: 8px;
      box-shadow: 1px 1px 9px 0px rgba(45, 45, 45, 0.15);
      background: #F3F7FF;
      opacity: 0.55;

      &:last-of-type {
        margin-bottom: 0;
      }

      &-active,
      &:hover {
        opacity: 1;
      }

      &-active .kanban-type-prod::before {
        content: '';
        display: block;
        position: absolute;
        background: $primary-color;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        opacity: .5;
        top: 50%;
        left: 22px;
        transform: translate(0, -50%);
      }
    }

    &-arrow {
      font-size: 20px;
      height: 22px;
      right: 45px;
      color: $primary-color;
    }


    &-prod {
      padding-left: 46px;
      font-weight: bolder;
      font-size: 20px;
    }

    &-desc {
      margin-top: 20px;
      font-size: 16px;
      padding-left: 46px;
      white-space: nowrap;
    }
  }

  .kanban-content {
    grid-template-columns: 1fr 1fr;
    column-gap: 20px;
    row-gap: 20px;

    &-item {
      box-sizing: border-box;
      width: 257px;
      box-shadow: 1px 8px 9px 0px rgba(45, 45, 45, 0.05);
      padding: 18px 20px 15px;
      background: url('~/assets/img/kanban.png') no-repeat;
      background-size: 100% 100%;
    }

    &-icon {
      width: 35px;
      height: 35px;
    }

    &-title {
      margin-top: 10px;
      color: $primary-color;
      font-size: 17px;
      font-weight: bolder;
    }

    &-text {
      margin-top: 13px;
      color: #2D2D2D;
      line-height: 24px;
      font-size: 14px;
    }
  }
}

@media screen and (max-width: 768px) {
  .kanban-block {
    display: none
  }
}


// consumer
.home-consumer-container {
  margin: 50px 0 0;
}

@media screen and (max-width: 768px) {
  .home-consumer-container {
    margin-top: 3rem;
  }
}

// partner
.home-partner-container {
  margin: 80px 0 75px;
}

.partner {
  &-list {
    margin-top: 38px;
    justify-content: center;
  }

  &-img {
    width: 206px;
    margin: 0 8px;
  }
}

@media screen and (max-width: 768px) {
  .partner {
    &-list {
      flex-wrap: wrap;
      margin-top: 2.5rem;
    }

    &-img {
      width: 14.65rem;
      margin: 0 .75rem 1.5rem;
    }
  }
}


// kanban swiper
.kanban-swiper-block {
  margin: 3rem 2rem 0;
  .kanban-swiper {
    margin-top: 2rem;
  }

  .kanban-swiper-item {
    overflow: hidden;
    background: url('~/assets/img/kanban-swiper.png') no-repeat;
    background-size: 100% 100%;
  }

  .kanban-swiper-content {
    margin: 1.5rem 1.5rem 0;
  }

  .kanban-swiper-title {
    color: #FFFFFF;
    font-weight: 800;
  }

  .kanban-swiper-index {
    font-size: 2.8rem;
    margin-right: .5rem;
  }

  .kanban-swiper-prod {
    font-size: 1.5rem;
  }

  .kanban-swiper-desc {
    font-size: .5rem;
  }

  .kanban-prod-content {
    margin: 3.5rem 1.2rem 0;
  }

  .kanban-content-item {
    margin: 2.2rem 0;
  }

  .kanban-swiper-icon {
    width: 2.4rem;
    margin-right: 1rem;
  }

  .kanban-content-title {
    margin-bottom: .8rem;
    font-weight: 800;
  }

  .kanban-swiper-btn {
    bottom: 2rem;
  }

  .kanban-swiper {
    height: 45rem;
  }

  .kanban-pagination {
    position: unset;
  }

  :deep(.kanban-swiper-bullet) {
    width: 0.43rem;
    height: 0.4rem;

  }
}
</style>