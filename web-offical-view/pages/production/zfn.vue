<template>
  <div>
    <Banner
      img-url="production-zfn"
      :text-style="{left: '12.5vw'}"
      @click="emits('banner-click')">
      <template v-slot:header>智赋能<br /><span class="func-content">同业OEM系统</span>解决方案</template>
      <template v-slot:tip>适用于缺少技术能力，难以定制灵工系统的企业客户<br />如：灵活用工渠道服务商、经济园区服务商、灵工平台企业等
      </template>
    </Banner>

    <template v-if="!isMobile">
      <div class="zfn-access-container">
        <div class="zfn-access-title-block">
          <div
            v-for="i in 4"
            :class="['zfn-access-triicon', `zfn-access-triicon-${i}`]"
            :key="i">
            <SvgoTriangle></SvgoTriangle>
          </div>
          <div class="zfn-access-title">
            <ContainerTitle style="padding: 30px 0 58px;">灵活用工服务商发展痛点</ContainerTitle>
          </div>
        </div>
        <div class="zfn-access-detail-block flex">
          <div
            class="zfn-access-detail flex-grow"
            v-for="(item, index) in accessDetail"
            :key="index"
            :class="`zfn-access-detail-${index +1}`">
            <div
              class="zfn-access-detail-box"
              :class="`zfn-access-detail-box-${index+1}`">
              <div class="zfn-access-detail-icon">
                <ClientOnly>
                  <img
                    :src="getImg(`zfn-detail-icon${index}.png`)"
                    alt="icon">
                </ClientOnly>
              </div>
              <div
                class="zfn-access-text-block"
                :class="`zfn-access-text-block-${index}`">
                <div class="zfn-access-detail-title text-center">{{ item.title }}</div>
                <div class="zfn-access-detail-label">{{ item.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="zfn-solution-container">
        <ContainerTitle>智赋能同业OEM系统解决方案</ContainerTitle>
        <div class="zfn-solution-tip text-center">四流合一、强风控、可定制化的佣金结算管理同业OEM系统，为平台企业和服务商提供技术支持，节省开发与运营成本，共享新经济新业态红</div>
        <div class="flex zfn-solution-list justify-center">
          <div
            class="zfn-solution-item"
            v-for="(item, index) in solution"
            :key="index">
            <div class="zfn-solution-icon">
              <ClientOnly>
                <img
                  :src="getImg(`zfn-solution-icon${index}.png`)"
                  alt="icon">
              </ClientOnly>
            </div>
            <div class="zfn-solution-title text-center">{{ item.title }}</div>
            <div class="zfn-solution-label text-center">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="zfn-access-container">
        <ContainerTitle>灵活用工服务商发展痛点</ContainerTitle>
        <div class="zfn-access-list">
          <div
            class="zfn-access-detail"
            v-for="(item, index) in accessDetail"
            :key="index">
            <div class="zfn-access-icon abs-tb-center">
              <ClientOnly>
                <img
                  :src="getImg(`zfn-detail-mini-icon${index}.png`)"
                  alt="icon">
              </ClientOnly>
            </div>
            <div class="zfn-access-title">{{ item.title }}</div>
            <div class="zfn-access-label">{{ item.label }}</div>
          </div>
        </div>
      </div>
      <div class="zfn-solution-container">
        <ContainerTitle>智赋能同业OEM系统解决方案</ContainerTitle>
        <div class="zfn-solution-tip">四流合一、强风控、可定制化的佣金结算管理同业OEM系统，为平台企业和服务商提供技术支持，节省开发与运营成本，共享新经济新业态红利，赢战未来。</div>
        <div
          v-for="(item, key) in solution"
          :key="key"
          class="zfn-solution-item"
          :class="[key === 1 ? 'zfn-solution-back-right' : 'zfn-solution-back-left']">
          <div class="zfn-solution-head clearfix">
            <div class="zfn-solution-icon" :class="[key === 1 ? 'float-right' : 'float-left']">
              <ClientOnly>
                <img
                  :src="getImg(`zfn-solution-mini-icon${key}.png`)"
                  alt="">
              </ClientOnly>
            </div>
            <div class="zfn-solution-title" :class="[key === 1 ? 'float-right' : 'float-left']">
              {{ item.title }}
            </div>
          </div>
          <div class="zfn-solution-label absolute">{{ item.label }}</div>
        </div>
      </div>
    </template>
  </div>
</template>

<script
  setup
  lang="ts">
import { Ref } from "vue";

const emits = defineEmits<{
  (e: 'banner-click'): void
}>()

const isMobile = inject<Ref<boolean>>('isMobile')

const accessDetail = [
  { title: '缺少技术', label: '缺少技术能力无法接入大型客户' },
  { title: '缺少发拥系统', label: '缺少系统承载发拥报税的系统' },
  { title: '难以定制', label: '技术开发成本高，系统难以定制' },
]

const solution = [
  {
    title: '定制化系统 节省成本',
    label: '支持自定义SaaS系统登录域名、登录页面等，个性化设置不同风格的SaaS系统，大大节省开发时间和费用'
  },
  { title: '资源撮合 共创价值', label: '聚合丰富的服务商与园区资源,融入新经济新业态上下游生态，优势互补，实现共赢', },
  {
    title: '提高创收增效能力',
    label: '安全稳定的SaaS系统，集合集群注册、智能报税、商户管理等服务于一体，提升企业运营能力，提高同业服务商管理效能',
  },
]
</script>

<style
  scoped
  lang="scss">
@media screen and (min-width: 768px) {
  .zfn-access {
    &-container {
      width: 870px;
      margin: 40px auto 70px;
    }


    &-title-block {
      position: relative;
      background: linear-gradient(0deg, #2675FF, #FFFFFF);
      padding: 1px;
      border-radius: 50%;
      margin-bottom: -5px;
    }

    &-title {
      position: relative;
      background: linear-gradient(0deg, #DBE8FF 0%, #FFFFFF 89%);
      border-radius: 50%;

      :deep(.container-title) {
        font-size: 15px;
        font-weight: 500;
      }
    }

    &-triicon {
      position: absolute;
      width: 18px;
      height: 18px;
      color: #2675FF;
      z-index: 100;

      &-1 {
        top: 66px;
        left: 37px;
      }

      &-2 {
        top: 93px;
        left: 268px;
        transform: rotate(-10deg);
      }

      &-3 {
        top: 93px;
        right: 268px;
        transform: rotate(-15deg);
      }

      &-4 {
        top: 66px;
        right: 37px;
        transform: rotate(-25deg);

      }
    }

    &-detail {
      background: linear-gradient(0deg, #DBE8FF 0%, #FFFFFF 89%);
      margin-right: 20px;

      &:last-child {
        margin-right: 0;
      }

      &-1 {
        border-bottom-left-radius: 100% 40%;
        border-bottom-right-radius: 10px;
      }

      &-2 {
        border-radius: 0 0 10px 10px;
      }

      &-3 {
        border-bottom-right-radius: 100% 40%;
        border-bottom-left-radius: 10px;
      }
    }

    &-text-block {
      padding: 0 58px;
    }

    &-detail-title {
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: bold;
      color: #333333;
    }

    &-detail-label {
      padding-bottom: 35px;
      font-size: 14px;
      font-weight: 400;
      color: #2D2D2D;
    }

    &-detail-icon {
      width: 54px;
      height: 54px;
      margin: 0 auto 20px;
    }

    &-detail-box-1 {
      transform: translate(-10px, -15px);
    }

    &-detail-box-2 {
      transform: translate(0, -5px);
    }

    &-detail-box-3 {
      transform: translate(10px, -15px);
    }
  }
  .zfn-solution {
    &-container {
      padding: 40px 0 50px;
      background: #F9F9F9;
    }

    &-item {
      box-sizing: border-box;
      width: 249px;
      margin: 0 31px;
      padding: 24px 28px 30px;
      background: #fff;
      box-shadow: 0 4px 5px 0 rgba(0, 0, 0, 0.05);
      border-radius: 14px;
    }

    &-icon {
      width: 56px;
      height: 58px;
      margin: 0 auto;
    }

    &-title {
      margin-top: 30px;
      font-size: 16px;
      font-weight: bold;
      color: #2B2B2B;
    }

    &-label {
      margin-top: 20px;
      font-size: 14px;
      font-weight: 400;
      color: #2D2D2D;
      line-height: 22px;
    }

    &-tip {
      margin: 20px 316px 50px;
      font-size: 14px;
      font-weight: 500;
      color: #929292;
      line-height: 24px;
    }
  }
}


@media screen and (max-width: 768px) {
  .zfn-access {
    &-list {
      margin: 2rem 1rem 0;
    }

    &-detail {
      position: relative;
      margin-bottom: .5rem;
      padding: 2.7rem 0 2.7rem 9rem;
      background-image: url("@/assets/img/zfn-kanban.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    &-icon {
      width: 4.05rem;
      left: 2.7rem;
    }

    &-title {
      font-size: 1.3rem;
      font-weight: bold;
      color: #333333;
    }

    &-label {
      font-size: 1.1rem;
      margin-top: .5rem;
    }
  }
  .zfn-solution {
    &-container {
      margin: 4rem 1.5rem;
    }
    &-tip {
      margin: 1rem 2rem 3rem;
      color: #666;
      font-size: 1rem;
      line-height: 1.8rem;
    }

    &-item {
      position: relative;
      margin: 1.5rem 0;
      padding: 0 1.5rem;
      height: 19.2rem;
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    &-back-right {
      background-image: url("@/assets/img/zfn-back-right.png");
    }

    &-back-left {
      background-image: url("@/assets/img/zfn-back-left.png");
    }
    &-head {
      height: 5rem;
    }
    &-icon {
      margin-top: 1rem;
      width: 2.6rem;
    }
    &-title {
      @include vertical-center(5rem);
      margin: 0 1rem;
      font-size: 1.5rem;
      font-weight: 800;
      color: #FEFEFE;
    }
    &-label {
      left: 50%;
      top: 50%;
      transform: translate3d(-50%, -30%, 0);
      width: 23rem;
      line-height: 1.8rem;
    }
  }
}
</style>