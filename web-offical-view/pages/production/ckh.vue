<template>
  <div class="page-container">
    <div class="flow-container text-center">
      <ContainerTitle>创客汇灵活用工解决方案</ContainerTitle>
      <p class="flow-tip">
        「实现业务流、资金流、票据流三流和一」
      </p>
      <div class="flow-img-container ma">
        <img
          src="~/assets/img/flow-ckh.png"
          alt="">
      </div>
    </div>
    <div class="service-container">
      <div class="service-list-container">
        <ContainerTitle>企业服务流程</ContainerTitle>
        <div class="service-list flex justify-center items-center">
          <div
            class="service-item-container employer-item relative"
            v-for="(item, index) in employerService"
            :key="index">
            <span class="service-index absolute">{{ index + 1 }}</span>
            <div class="servite-item-text">{{ item }}</div>
            <div
              class="service-item-line"
              v-if="index < employerService.length - 1"></div>
          </div>
        </div>
      </div>
      <div class="service-list-container">
        <ContainerTitle>自由职业者服务流程</ContainerTitle>
        <div class="service-list flex justify-center items-center">
          <div
            class="service-item-container employee-item relative"
            v-for="(item, index) in employeeService"
            :key="index">
            <span class="service-index absolute">{{ index + 1 }}</span>
            <div class="servite-item-text">{{ item }}</div>
            <div
              class="service-item-line"
              v-if="index < employeeService.length - 1"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script
  setup
  lang="ts">
const employerService = [
  '沟通合作意向，基于用工场景属性获取个性化解决方案，开通账号',
  '选择落地服务园区，线上签署合作协议，5分钟快速开户',
  '基于真实场景，发布用工任务，导入或通知自由职业者接单',
  '后台上传发佣表格 / api对接，极速完成发放，佣金实时到账',
  '线上申请开票，实时跟踪开票进度，获取发放凭证、税务凭证、完税清单'
]
const employeeService = [
  '在线完成实名认证、电子签约',
  '接受用工企业发布的用工任务',
  '基于真实场景，为企业提供服务并提供完成凭证',
  '获取税后佣金，平台代为开票完税，规避税务风险',
]
</script>


<style
  lang="scss"
  scoped>
// flow
.flow-container {
  margin: 70px 0 40px;

  .flow-tip {
    margin: 20px 0 45px;
    color: $primary-color;
  }

  .flow-img-container {
    width: 63%;
  }
}

//service
.service-container {
  padding: 37px 0 0;
  background: $back-gray;

  .service-list-container {
    margin-top: 10px;
    padding-bottom: 75px;
  }

  .service-list {
    margin-top: 54px;
  }

  .service-item-container {
    width: 170px;
    margin: 0 16px;
    padding: 60px 8px 20px;
    box-sizing: border-box;
    box-shadow: 2px 3px 4px 0px rgba(45, 45, 45, 0.14);
    background-repeat: no-repeat;
    background-size: 100% 100%;

    &:hover .servite-item-text {
      color: $primary-color;
    }
  }

  .servite-item-text {
    font-size: 14px;
    font-weight: 400;
    color: #2D2D2D;
    line-height: 24px;

  }

  .service-index {
    font-size: 22px;
    font-weight: bolder;
    color: #FFFFFF;
  }

  .service-item-line {
    @include top-bottom-center(absolute);
    right: -33px;
    width: 33px;
    height: 15px;
    background: $primary-color;
    opacity: .2;
  }

  .employer-item {
    background-image: url('~/assets/img/service-employer.png');

    .service-index {
      top: 8px;
      left: 10px;

    }
  }

  .employee-item {
    background-image: url('~/assets/img/service-employee.png');

    .service-index {
      top: 8px;
      right: 10px;
    }
  }
}
</style>
