<template>
  <ProductionPage>
    <template #header>
      <Banner
        img-url="production-zxh"
        :show-btn="isMobile"
        :text-style="{left: '12.5vw'}"
        @click="emits('banner-click')">
        <template v-slot:header>智享汇<br /><span class="func-content">灵活用工</span>解决方案</template>
        <template v-slot:tip>适用于企业与批量个体经营者合作项目<br/>如：线上推广、共享生活服务、视频创作服务等</template>
      </Banner>
    </template>
    <div class="flow-container text-center">
      <ContainerTitle>智享汇灵活用工解决方案</ContainerTitle>
      <p class="flow-tip">
        「实现业务流、资金流、票据流三流合一」
      </p>
      <div class="flow-img-container ma">
        <img
          src="~/assets/img/flow-zxh.png"
          alt="">
      </div>
    </div>
  </ProductionPage>
</template>

<script
  lang="ts"
  setup>

import { Ref } from "vue";

const emits = defineEmits<{
  (e: 'banner-click'): void
}>()

const isMobile = inject<Ref<boolean>>('isMobile')
</script>

<style
  lang="scss"
  scoped>
// flow


@media screen and (min-width: 768px) {
  .flow-container {
    margin: 70px 0;

    .flow-tip {
      margin: 20px 0 45px;
      color: $primary-color;
    }
  }
  .flow-img-container {
    padding-left: 3vw;
    width: 66vw;
  }
}

@media screen and (max-width: 768px) {
  .flow-container {
    margin: 4rem 0 0;

    .flow-tip {
      margin: 1rem 0 3rem;
      color: $primary-color;
      font-size: 1rem;
    }
  }
  .flow-img-container {
    width: 34rem;
  }
}
</style>