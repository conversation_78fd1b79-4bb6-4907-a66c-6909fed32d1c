<template>
  <div>
    <Banner
      img-url="solution-life"
      @click="emits('banner-click')"
      text-width="23rem">
      <template v-slot:header><span class="func-content">生活服务</span><br />行业解决方案</template>
      <template v-slot:tip>面向生活服务（保洁、维修）、酒店旅馆、餐饮饭店等场景下自由职业者零散所得发放的问题，助力企业降本增效</template>
    </Banner>
    <div class="point-list-block">
      <ContainerTitle>生活服务行业用工痛点</ContainerTitle>
      <AccessPointerList :list="pointList"></AccessPointerList>
    </div>

    <!--<div class="solution-plan-block">-->
    <!--  <ContainerTitle>灵活用工解决方案</ContainerTitle>-->
    <!--  <SolutionPlan :list="planList"></SolutionPlan>-->
    <!--</div>-->


  </div>
</template>

<script
  setup
  lang="ts">
const pointList = [
  { name: '结算效率低', text: '传统线下方式结算，人数众多效率低' },
  { name: '人员流动大', text: '人员数量多，流动大，管理成本高' },
  { name: '合规成本高', text: '人员庞杂，难以取得合规票据作为成本凭证' },
]

const resList = [
  '<span class="font-medium">优化用工模式：</span>将传统的用工模式企业+劳务转为新型合作模式企业+平台+个人',
  '<span class="font-medium">提供票据凭证：</span>报酬发放+个税申报一体化处理，提供正规发票',
  '<span class="font-medium">薪酬佣金代发：</span>合规发薪，解决教育培训公司公对私、私对私发薪问题，提高发薪效率',
  '<span class="font-medium">降低用工风险：</span>企业和个人为通过第三方平台对接的合作关系，减少劳资纠纷入',
]

const planList = [
  {
    name: '用工成本低', text: '用工成本低&<br/>灵活用工供应商-自由职业者共享服务协议', group: [
      {
        icon: 'group-icon1.png',
        width: 25,
        text: '灵活用工供应商为企业进行<span class="func-content">自由职业者匹配对接</span>；提供共享平台管理自由职业者的<span class="func-content">工作记录</span>等'
      }, {
        icon: 'group-icon2.png',
        width: 27,
        text: '用工纳税成本<span class="func-content">包含</span>在支付给灵活用工供应商的<span class="func-content">服务费</span>中，间接享受部分地区独有的<span class="func-content">财政扶持政策</span>'
      },
    ]
  },
  {
    name: '劳动风险转移', text: '灵活用工供应商&自由职业者（个人）：<br/>合作协议，非劳动合同', group: [
      {
        icon: 'group-icon3.png',
        width: 24,
        text: '灵活用工供应商基于用工企业的 合作协议，选用个人向用工企业<span class="func-content">提供指定的服务</span>'
      }, {
        icon: 'group-icon4.png',
        width: 22,
        text: '个人灵活用工供应商平等合作向 用工企业交付经营成果，<span class="func-content">个人自行承担提供服务的成本与风险</span>， 取得个人经营所得'
      },
    ]
  },
  {
    name: '税务风险转移',
    text: '灵活用工供应商取得税局授予的《委托代征协议书》，<span class="func-content">并根据C端协议的约定为个人完成“经营所得”的纳税申报</span>，相关纳税义务由灵活用工供应商承担'
  },
]

const emits = defineEmits<{
  (e: 'banner-click'): void
}>()
</script>

<style
  scoped
  lang="scss">
.point-list-block {
  margin-top: 55px;
}

//pic
.solution-pic-block {
  margin: 80px 0;
}

.solution-pic-tip {
  margin: 27px 0 56px;
  text-align: center;
  font-size: 14px;
  color: #929292;
}


//plan
.solution-plan-block {
  margin-top: 80px;
}
</style>