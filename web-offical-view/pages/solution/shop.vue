<template>
  <div>
    <Banner
      img-url="solution-shop"
      @click="emits('banner-click')"
      text-width="28rem">
      <template v-slot:header><span class="func-content">电商行业</span>解决方案</template>
      <template v-slot:tip>面向社交电商、直播电商、社区电商、共享电商等场景下推广代理个人日常推广费发放的问题，助力电商企业降本增效</template>
    </Banner>
    <div class="point-list-block">
      <ContainerTitle>电商行业用工痛点</ContainerTitle>
      <AccessPointerList :list="pointList"></AccessPointerList>
    </div>
    <div class="solution-pic-block">
      <ContainerTitle>灵活用工解决方案</ContainerTitle>
      <div class="solution-pic-tip">解决企业日常推广费发放的问题</div>
      <SolutionPic
        :src-list="picSrc"
        :result-list="picRes"></SolutionPic>
    </div>


  </div>
</template>

<script
  setup
  lang="ts">
import { Ref } from "vue";

const pointList = [
  { name: '信息归集难', text: '电商代理网密集，推广费发放碎片化，结算混乱效率低' },
  { name: '纳税申报难', text: '发放推广费的同时，须为个人推广者承担申报纳税的义务' },
  { name: '成本计入难', text: '批量个人推广者难以取得合规的费用凭证' },
  // { name: '风控合规难', text: '大金额费用结算，面临合规与监管压力' },
]

const picSrc = ['电商企业']
const picRes = ['电商推广费结算']

const resList = [
  '享受财政扶持、取得票据凭证，有效降低企业成本',
  '取得票据凭证，有效降低企业成本',
  '流程高效，报酬发放+个税申报一体化处理',
  '无需单独成立个体工商户',
  '操作合法，流程合规',
]

const emits = defineEmits<{
  (e: 'banner-click'): void
}>()

const isMobile = inject<Ref<boolean>>('isMobile')
</script>

<style
  scoped
  lang="scss">
.point-list-block {
  margin-top: 55px;
}

//pic
.solution-pic-block {
  margin-top: 80px;
}

.solution-pic-tip {
  margin: 27px 0 56px;
  text-align: center;
  font-size: 14px;
  color: #929292;
}

@media screen and (max-width: 768px) {
  .solution-pic-block {
    margin-top: 3.25rem;
  }
  .solution-pic-tip {
    margin: 1.5rem 0 2.8rem;
    font-size: 1rem;
    color: #333333;
  }
}
</style>