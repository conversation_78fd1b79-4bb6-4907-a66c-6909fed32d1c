<template>
  <div>
    <Banner
      img-url="solution-media"
      @click="emits('banner-click')"
    text-width="21rem">
      <template v-slot:header><span class="func-content">文娱传媒</span><br />行业解决方案</template>
      <template v-slot:tip>面向直播公会、直播平台、活动营销策划等场景下自由职业者日常所得发放的问题，助力企业降本增效</template>
    </Banner>
    <div class="point-list-block">
      <ContainerTitle>文娱传媒行业用工痛点</ContainerTitle>
      <AccessPointerList :list="pointList"></AccessPointerList>
    </div>
    <!--<div class="solution-pic-block">-->
    <!--  <ContainerTitle>灵活用工解决方案</ContainerTitle>-->
    <!--  <div class="solution-pic-tip">解决企业日常返佣发放的问题</div>-->
    <!--  <SolutionPic-->
    <!--    :src-list="picSrc"-->
    <!--    :result-list="picRes"></SolutionPic>-->
    <!--</div>-->

  </div>
</template>

<script
  setup
  lang="ts">
const pointList = [
  { name: '运营成本高', text: '个人无法提供回票，运营成本提高' },
  { name: '资金清算难', text: '清算困难且流程复杂，人工成本高' },
  { name: '工作量繁琐', text: '重复性事务工作占据大量时间' },
  { name: '竞争压力大', text: '要求提高收入，影响签约与续约' },
]

const picSrc = ['直播电商企业', 'MCN机构']
const picRes = ['平台签约KOL完成报酬结算']

const resList = [
  '享受财政扶持、取得票据凭证，有效降低企业成本',
  '平台直接进行资金管理，避免因发放不及时或发放金额不准确产生纠纷',
  '平台提供票据凭证，减少企业之间资金与票据之间的往来，有效降低企业的连带风险',
  '无需单独成立个体工商户',
  '纳税申报一体化处理，享受优惠政策，提高个人收入',
]

const emits = defineEmits<{
  (e: 'banner-click'): void
}>()
</script>

<style
  scoped
  lang="scss">
.point-list-block {
  margin-top: 55px;
}

//pic
.solution-pic-block {
  margin: 80px 0;
}

.solution-pic-tip {
  margin: 27px 0 56px;
  text-align: center;
  font-size: 14px;
  color: #929292;
}

</style>