<template>
  <div class="consumer-list-wrapper">
    <div class="consumer-list-container">
      <div
        class="consumer-item-container relative"
        v-for="(item, index) in consumers"
        :key="index"
        @click="onItemClick(item)"
      >
        <div class="consumer-item-wrapper">
          <div class="consumer-img relative">
            <ClientOnly>
              <img
                v-lazy="getImg(`consumer-${isMobile ? 'mini' : ''}${index}.png`)"
                :alt="item.text"
              />
            </ClientOnly>
            <div
              class="consumer-background abs-all-zero"
              v-if="!isMobile">
              <div class="block consumer-text-container abs-all-center">
                <div class="consumer-text">{{ item.text }}</div>
                <a :href="config.public.deployUrl + item.url">
                  <div class="consumer-button cursor-pointer">
                    查看更多
                  </div>
                </a>
              </div>
            </div>
          </div>
          <div
            class="consumer-text"
            v-if="isMobile">
            <p class="consumer-text-p">{{ item.text }}行业</p>
            <div class="consumer-text-a func-content flex items-center">
              <a :href="config.public.deployUrl + item.url">了解详情</a>
              <p class="consumer-arrow"><img
                src="~/assets/img/address-arrow.png"
                alt="goto" /></p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="consumer-btn text-center">
    <button
      @click="onBtnClick"
      class="primary-btn"
    >获取专属解决方案
    </button>
  </div>
</template>


<script
  lang="ts"
  setup>// 客户列表
import { Ref } from "vue";

const consumers = [
  { text: '电子商务', url: '/solution/shop' },
  { text: '文娱传媒', url: '/solution/media' },
  { text: '生活服务', url: '/solution/life' },
  { text: '教育培训', url: '/solution/edu' },
]

const config = useRuntimeConfig()

const isMobile = inject<Ref<boolean>>('isMobile')

const onItemClick = (item: any) => {
  if (isMobile?.value) {
    location.href = config.public.deployUrl + item.url
  }
}

const emits =defineEmits<{
  (e:'click'): void
}>()

const onBtnClick = () => {
  emits('click')
}

</script>


<style
  lang="scss"
  scoped>
.consumer {
  &-list-container {
    margin-top: 50px;
  }

  &-item-container {
    transition: all .3s linear;
  }

  &-item-wrapper {
    margin: 0 10px;
    width: 230px;
  }

  &-background {
    opacity: 0;
    z-index: 10;
    transition: all .2s linear;
    background: rgba(0, 0, 0, .5);
    color: #fff;
  }

  &-text {
    font-size: 25px;
    font-weight: bold;
  }

  &-button {
    height: 34px;
    line-height: 34px;
    margin-top: 17px;
    padding: 0 24px;
    border: 1px solid #fff;
    border-radius: 7px;
  }

  &-btn {
    margin-top: 36px;

    button {
      width: 170px;
      font-size: 18px;
      @include vertical-center(48px);
    }
  }
}

@media screen and (min-width: 768px) {
  .consumer {
    &-list-container {
      display: flex;
      justify-content: center;
    }

    &-item-container {
      &:hover {
        transform: translateY(-12px);
      }

      &:hover .consumer-background {
        opacity: 1;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .consumer {
    &-list-wrapper {
      margin-top: 2rem;
      height: 25rem;
      overflow-y: hidden;
    }

    &-list-container {
      box-sizing: border-box;
      width: 100%;
      margin-top: 0;
      overflow-x: auto;
      white-space: nowrap;
      padding-bottom: 2rem;
      scroll-snap-type: x mandatory;
    }

    &-item-container {
      display: inline-block;
      scroll-snap-align: start;
      background: #FFFFFF;
    }

    &-item-wrapper {
      box-sizing: border-box;
      width: 21.95rem;
      height: 24.5rem;
      margin: 0 .5rem 0 1rem;
      padding: 1rem 1rem 0;
      border-radius: 1.5rem;
      box-shadow: 0 0 1rem 0 rgba(45, 45, 45, 0.15);
    }

    &-img {
      width: 20.08rem;
      height: 16.85rem;
    }

    &-text-p {
      margin-top: 1.7rem;
      font-size: 1.3rem;
      font-weight: bold;
      color: $title-color;
    }

    &-text-a {
      margin-top: 1rem;
      font-size: 1.1rem;
      font-weight: bold;
    }

    &-arrow {
      margin-left: 1.4rem;
      width: 1.4rem;
      height: 1.4rem;
    }

    &-btn {
      margin-top: 2rem;

      button {
        width: 15.9rem;
        @include vertical-center(3.9rem);
        border-radius: 0.5rem;
        font-size: 1.3rem;
        font-weight: bold;
      }
    }
  }
}
</style>