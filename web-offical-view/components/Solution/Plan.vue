<template>
  <div class="resolution-plan-container">
    <div class="resolution-plan-list flex justify-center">
      <div
        class="plan-item flex flex-col relative"
        v-for="(item, index) in list"
        :key="index">
        <div class="plan-item-icon">
          <ClientOnly>
            <img
              :src="getImg(`plan-icon${index}.png`)">
          </ClientOnly>
        </div>
        <div class="plan-item-name text-center">{{ item.name }}</div>
        <div
          class="flex-grow flex flex-col"
          :class="{'justify-center': index === list.length - 1}">
          <div
            class="plan-item-text text-center"
            v-html="item.text"></div>
          <div
            class="plan-item-group"
            v-if="item.group?.length">
            <div
              class="plan-group-item relative"
              v-for="(i, key) in item.group"
              :key="key">
              <div
                class="plan-group-icon"
                v-width="i.width">
                <ClientOnly>
                  <img
                    :src="getImg(i.icon)"
                    alt="icon">
                </ClientOnly>
              </div>
              <div
                class="plan-group-text"
                v-html="i.text"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script
  setup
  lang="ts">
interface GroupItem {
  icon: string
  text: string
  width: string | number
}

interface ListItem {
  name: string
  text: string
  group?: GroupItem[]
}

defineProps<{
  list: ListItem[]
}>()
</script>

<style
  scoped
  lang="scss">

.resolution-plan-container {
  margin-top: 74px;
}

.plan-item {
  width: 295px;
  height: 342px;
  margin: 0 20px;
  background: url('~/assets/img/solution-kanban2.png') no-repeat;
  background-size: 100% 100%;

  &:last-of-type {
    .plan-item-name {
      margin-bottom: 0;
    }

    .plan-item-text {
      padding: 19px 34px;
    }
  }

  &:first-of-type {
    .plan-item-name {
      padding-left: 0;
    }
  }

  &-name {
    margin-top: 33px;
    margin-bottom: 33px;
    padding-left: 15px;
    color: #0B78FA;
    font-size: 16px;
    font-weight: bold;
  }

  &-text {
    margin: 0 17px;
    padding: 12px 0;
    border-radius: 14px;
    background: #E3EEFF;
    font-size: 14px;
    font-weight: 400;
    color: #4B4B4B;
    line-height: 20px;
  }

  &-icon {
    position: absolute;
    top: 24px;
    left: 34px;
    width: 32px;
  }
}


//group
.plan-item-group {
  margin-top: 15px;
}

.plan-group-item {
  padding: 0 30px 0 74px;
  margin-bottom: 8px;
}

.plan-item:first-of-type {
  .plan-group-item {
    margin-bottom: 18px;
  }
}

.plan-group-text {
  line-height: 22px;
}

.plan-group-icon {
  @include top-bottom-center(absolute);
  left: 30px;
  width: 25px;
  height: 30px;
}
</style>