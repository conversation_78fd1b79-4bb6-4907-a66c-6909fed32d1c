<template>
  <div
    class="solution-pic-container flex justify-center items-center"
    :class="{'flex-col': isMobile}">
    <div class="solution-pic-source flex items-center justify-center">
      <div class="solution-src-img">
        <img
          src="~/assets/img/solution-plat.png"
          alt="">
      </div>
      <div class="solution-src-text">
        <p
          v-for="(src, index) in srcList"
          :key="index">
          {{ src }}
        </p>
      </div>
    </div>
    <div class="solution-src-line"></div>
    <div class="solution-logo">
      <img
        src="~/assets/img/logo-mini.png"
        alt="logo">
      <p>智享汇</p>
    </div>
    <div class="solution-result-line">
      <img src="~/assets/img/link.png">
    </div>
    <div
      class="solution-result flex"
      :class="{'flex-col': !isMobile}">
      <div class="result-list res-list">
        <div class="res-list-core inline-block">
          <p
            v-for="(item, index) in resultList"
            :key="index">
            {{ item }}
          </p>
        </div>
      </div>
      <div class="result-list">
        <div class="res-list-core">
          税务机构，纳税申报一体化处理
        </div>
      </div>
    </div>
  </div>
</template>

<script
  setup
  lang="ts">
import { Ref } from "vue";

defineProps<{
  srcList: string[]
  resultList: string[]
}>()

const isMobile = inject<Ref<boolean>>('isMobile')
</script>

<style
  scoped
  lang="scss">
.solution-pic-container {
  background: #fff;
}

.solution-pic-source {
  padding: 14px 34px 14px 23px;
  border-radius: 7px;;
  background: $primary-color;
  color: #fff;

  .solution-src-img {
    width: 41px;
    margin-right: 9px;
  }
}

.solution-logo {
  box-sizing: border-box;
  width: 121px;
  padding: 17px 34px 14px;
  border: 2px dashed $primary-color;
  border-radius: 7px;
  text-align: center;

  p {
    margin-top: 5px;
    color: $title-color;
    font-size: 14px;
  }
}

.solution-src-line {
  position: relative;
  border-bottom: 2px dashed #55A3FF;
  margin: 2.5rem 0;
  width: 130px;

  &::after {
    content: '';
    display: block;
    @include top-bottom-center(absolute);
    width: 12px;
    height: 12px;
    background: #55A3FF;
    border-radius: 50%;
    right: -6px;
  }
}

.solution-result-line {
  margin-top: 5px;
  margin-right: -8px;
  width: 94px;
  z-index: 10;
}

.result-list {
  overflow: hidden;
  color: #fff;

  .res-list-core {
    border-radius: 7px;
    padding: 10px 16px 10px 24px;
    background: $primary-color;
  }

  &:first-of-type {
    margin-bottom: 30px;
  }
}

@media screen and (max-width: 768px) {
  .solution-pic-source {
    padding: .6rem 1.5rem;
  }
  .solution-logo {
    width: 10rem;
    height: 10rem;
    padding: 2rem 2.5rem 0;
  }
  .solution-src-line {
    width: 5rem;
    transform: rotate(90deg);
  }
  .solution-result-line {
    margin: 0;
    transform: rotate(90deg);
  }
  .result-list {
    width: 15rem;
    margin-bottom: 0 !important;
  }
}
</style>