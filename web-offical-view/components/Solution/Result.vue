<template>
  <div class="solution-result-container flex items-center justify-center">
    <div class="solution-result-list flex justify-center" :class="{'flex-wrap' : isMobile}">
      <div
        class="result-list-item relative text-center"
        v-for="(item, index) in list"
        :key="index">
        <div class="result-item-img">
          <ClientNuxtImage :src="`result-img${index}.png`"></ClientNuxtImage>
          <!--<ClientOnly>-->
          <!--<img-->
          <!--  :src="getImg(`result-img${index}.png`)"-->
          <!--  alt="">-->
          <!--</ClientOnly>-->
        </div>
        <div class="result-item-title">{{ item.title }}</div>
        <div class="result-item-text">{{ item.text }}</div>
      </div>
    </div>
  </div>
</template>

<script
  setup
  lang="ts">
const isMobile = inject('isMobile')

const list = [
  {
    title: '降低成本',
    text: '直击痛点，解决企业无票支出难题，有效降低企业成本，提高企业利润',
  },{
    title: '降低风险',
    text: '真实业务与票据相符，降低企业运营风险，促进企业良性发展',
  },{
    title: '提高效率',
    text: '减少内部员工的工作量与压力，提高企业运行效率',
  }, {
    title: '提高收入',
    text: '享受优惠政策，提高个人收入，便于企业进行商务谈判，提供更多议价空间',
  },
]
</script>

<style
  scoped
  lang="scss">
//list
.solution-result-container {
  margin-top: 83px;
}
.result-list-item {
  box-sizing: border-box;
  width: 169px;
  height: 291px;
  margin: 0 17px;
  padding: 60px 18px 0;
  background: #FFFFFF;
  box-shadow: 0 0 15px 0 #DCDFE2;
  border-radius: 8px;
}
.result-item-img {
  width: 88px;
  margin: 0 auto;
}
.result-item-title {
  margin-top: 30px;
  font-size: 16px;
  font-weight: 500;
  color: #30333A;
}
.result-item-text {
  margin-top: 16px;
  font-size: 10px;
  font-weight: 400;
  color: #898D90;
}

@media screen and (max-width: 768px) {
  .solution-result-container {
    margin-top: 2.95rem;
    padding-bottom: 1.2rem;
  }
  .result-list-item {
    width: 12.5rem;
    height: 25rem;
    margin: 0 1.25rem 1.85rem;
    padding: 4.35rem 1.25rem 0;
    border-radius: 0.5rem;
  }
  .result-item-img{
    width: 6.4rem;
  }
  .result-item-title {
    margin-top: 2.3rem;
    font-size: 1.2rem;
  }
  .result-item-text {
    margin-top: 1.15rem;
  }
}
</style>