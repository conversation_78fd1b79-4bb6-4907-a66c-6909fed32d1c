<template>
  <div class="container-title-block text-center">
    <div class="container-title">
      <div class="title-left"></div>
      <div class="title-right"></div>
      <slot></slot>
    </div>
  </div>
</template>

<style
  lang="scss"
  scoped>
// 内容块标题
.container-title {
  display: inline-block;
  position: relative;
  color: $title-color;
  font-size: 20px;

  .title-left {
    display: block;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translate(-130%, -50%);
    width: 75px;
    height: 8px;
    background: url('~/assets/img/text-back.png') no-repeat;
    background-size: 100% 100%;
  }

  .title-right {
    display: block;
    content: '';
    position: absolute;
    top: 50%;
    right: 0;
    transform: translate(130%, -50%) rotateZ(180deg);
    transform-origin: center center;
    width: 75px;
    height: 8px;
    background: url('~/assets/img/text-back.png') no-repeat;
    background-size: 100% 100%;
  }
}

@media screen and (max-width: 768px) {
  .container-title {
    font-size: 1.5rem;
    font-weight: bold;
    color: #000000;
  }
  .title-left, .title-right {
    width: 4.6rem !important;
    height: .55rem !important;
  }
}
</style>