<template>
  <div class="link-pc-container">
    <div class="link-title-container flex items-center text-center">
      <div class="link-title">智享汇流程 </div>
      <div class="link-middle-title flex-grow">vs</div>
      <div class="link-title">传统流程</div>
    </div>
    <div
      class="link-panel-item flex items-center"
      v-for="(item, index) in left"
      :key="index"
    >
      <div class="link-left-item link-item text-center">
        <div class="link-label">
          {{ item.label }}
        </div>
        <div class="link-text">
          {{ item.text }}</div>
      </div>
      <div class="link-line flex-grow"></div>
      <div class="link-right-item link-item text-center">
        <div class="link-label">
          {{ right[index].label }}
        </div>
        <div class="link-text">
          {{ right[index].text }}</div>
      </div>
    </div>
    <div class="link-icon-container flex justify-between">
      <div class="icon-item">
        <div class="ok-icon icon ma">
          <img
            src="~/assets/img/ok-icon.png"
            alt=""
          >
        </div>
      </div>
      <div class="icon-item">
        <div class="err-icon icon ma">
          <img
            src="~/assets/img/err-icon.png"
            alt=""
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const left = [
  { label: '3-5分钟', text: '商户在线开户，合同在线签署' },
  { label: '5-10分钟', text: '选择服务商，开通资金托管户' },
  { label: '实时', text: '充值并上传发放表格，资金实时到账' },
  { label: '自动跟进', text: '线上申请开票和查询开展进度' },
]
const right = [
  { label: '1-3天', text: '线下纸质合同流程繁琐，签署周期长' },
  { label: '1-2天', text: '多方对接，费时费力' },
  { label: '1-2小时', text: '线下对公账户打款，手工认款' },
  { label: '人工跟进', text: '人工沟通申请开票，信息反馈不及时' },
]
</script>

<style lang="scss" scoped>
.link-pc-container {
  --link-item-width: 405px
}

// title
.link-title {
  width: var(--link-item-width);
  color: #000;
  font-size: 16px;
  font-weight: bold;
}

.link-middle-title {
  font-size: 61px;
  font-weight: bold;
  color: $primary-color;
  transform: translateY(-10%);
}

// link
.link-panel-item {
  margin-bottom: 10px;
}
.link-item {
  width: var(--link-item-width);
  border-radius: 7px;
  font-size: 14px;
  box-shadow: 2px 3px 4px 0px rgba(45, 45, 45, 0.14);

  &.link-left-item {
    background: $primary-color;
    color: #fff;
  }

  &.link-right-item {
    background: #fff;
    color: #2D2D2D;
  }

  .link-label {
    font-weight: bold;
    margin-top: 10px;
  }

  .link-text {
    margin: 8px 0 15px;
  }
}

.link-line {
  position: relative;
  border-bottom: 3px dashed #38D28B;

  &::after {
    content: '';
    display: block;
    @include top-bottom-center(absolute);
    transform: translateY(calc(-50% + 1px));
    right: -20px;
    width: 16px;
    height: 16px;
    background: #38D28B;
    border-radius: 50%;
  }
}

// icon
.link-icon-container {
  margin-top: 18px;
}
.icon-item {
  width: var(--link-item-width);
}

.icon {
  width: 58px;
  height: 58px;
}</style>