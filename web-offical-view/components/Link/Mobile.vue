<template>
  <div class="link-mobile-container">
    <div class="link-mobile-list">
      <div class="link-mobile-item relative flex items-center" v-for="(item, index) in top" :key="index">
        <div class="link-mobile-ring"></div>
        <div class="link-mobile-img">
          <img
            :src="getImg(`link-mobile${index}.png`)"
            alt="">
        </div>
        <div class="link-mobile-text">
          <div class="link-mobile-top">
            <div class="link-top-title">现在 <span class="func-content">{{ item.label }}</span></div>
            <div class="link-top-text">{{ item.text }}</div>
          </div>
          <div class="link-mobile-bottom">
            <div class="link-bottom-title">{{ bottom[index].label }}</div>
            <div class="link-bottom-text">{{ bottom[index].text }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script
  setup
  lang="ts">
const top = [
  { label: '3-5分钟', text: '商户在线开户，合同在线签署' },
  { label: '5-10分钟', text: '选择服务商，开通资金托管户' },
  { label: '实时', text: '充值并上传发放表格，资金实时到账' },
  { label: '自动跟进', text: '线上申请开票和查询开展进度' },
]
const bottom = [
  { label: '1-3天', text: '线下纸质合同流程繁琐，签署周期长' },
  { label: '1-2天', text: '多方对接，费时费力' },
  { label: '1-2小时', text: '线下对公账户打款，手工认款' },
  { label: '人工跟进', text: '人工沟通申请开票，信息反馈不及时' },
]
</script>

<style scoped lang="scss">
  .link-mobile-item {
    margin-left: 1.5rem;
    padding: 1rem 0;

    &::after {
      content: '';
      display: block;
      position: absolute;
      left: .85rem;
      top: 50%;
      height: 100%;
      border-left: .15rem solid #D5D5D5;
      z-index: -10;
    }
    &:last-child::after {
      height: 0
    }

  }
  .link-mobile-img {
    width: 8.9rem;
    margin-left: 1.5rem;
    margin-right: 1.5rem;
  }
  .link-top-title {
    font-size: 1.3rem;
    font-weight: 800;
    color: #3B3B3B;
  }
  .link-top-text {
    margin-top: .5rem;
    font-size: 1.1rem;
    font-weight: 500;
    color: #3B3B3B;
  }
  .link-mobile-bottom {
    margin-top: 1rem;
  }
  .link-bottom-title {
    font-size: 1.3rem;
    font-weight: 800;
    color: #A8A8A8;
  }
  .link-bottom-text {
    margin-top: .5rem;
    font-size: 1.1rem;
    font-weight: 500;
    color: #A8A8A8;
  }
  .link-mobile-ring {
    box-sizing: border-box;
    width: 1.75rem;
    height: 1.75rem;
    border-radius: 50%;
    border: .45rem solid $primary-color;
    background-color: #fff;
  }
</style>