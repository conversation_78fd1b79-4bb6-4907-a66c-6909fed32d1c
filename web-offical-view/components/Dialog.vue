<template>
  <Teleport to="body">
    <div class="dialog-container" v-show="visible">
      <div class="dialog-background"></div>
      <div class="dialog-wrap">
        <div class="dialog-close" @click="close"></div>
        <slot></slot>
      </div>
    </div>
  </Teleport>
</template>

<script
  setup
  lang="ts">
  defineProps<{
    visible: boolean
  }>()

  const emits = defineEmits<{
    (e: 'update:visible', val: boolean): void
  }>()

  const close = () => {
    emits('update:visible', false)
  }

</script>

<style
  scoped
  lang="scss">

.dialog-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, .7);
  z-index: 100;
}
.dialog-wrap {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  z-index: 500;
}
.dialog-close {
  position: absolute;
  top: 0;
  right: 0;
  width: 70px;
  height: 70px;
  z-index: 100;
}
</style>