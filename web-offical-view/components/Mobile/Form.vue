<template>
  <div class="mobile-form-container">
    <ContainerTitle>定制您的专属方案</ContainerTitle>
    <div class="mobile-form-tip text-center">留下您的联系方式和需求，将有专人提供1对1服务</div>
    <div class="mobile-form-wrap">
      <FormInput
        mobile
        placeholder="您公司的名字"
        v-model="form.businessName"></FormInput>
      <FormInput
        mobile
        placeholder="您所处的行业"
        v-model="form.industry"></FormInput>
      <FormInput
        mobile
        placeholder="您的姓名"
        v-model="form.username"></FormInput>
      <FormInput
        mobile
        placeholder="您的手机号"
        v-model="form.phone"></FormInput>
      <FormInput
        mobile
        placeholder="简单描述您的需求"
        v-model="form.remark"
        type="textarea"></FormInput>

      <div class="mobile-form-btn text-center">
        <button class="primary-btn" v-width="'15.9rem'" @click="onBtnClick">提交信息</button>
      </div>
    </div>
  </div>
</template>

<script
  setup
  lang="ts">
  import type { Form } from "~/share";

  const form = reactive<Form>({type: '100'} as Form)
  const emits = defineEmits<{
    (e: 'submit', value: Form): void
  }>()
  const onBtnClick = () => {
    emits('submit', toRaw(form))
  }
</script>

<style
  scoped
  lang="scss">
.mobile-form-tip {
  margin-top: 1.5rem;
  font-size: 1rem;
  color: #666666;
}

.mobile-form-wrap {
  margin: 2.5rem 1.5rem 0;
}
.mobile-form-btn {
  margin-top: 2rem;
}
</style>