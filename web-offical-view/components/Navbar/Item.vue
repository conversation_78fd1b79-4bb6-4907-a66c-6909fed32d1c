<template>
  <div
    class="navbar-menu-item cursor-pointer relative"
    :class="[hasChild ? 'has-child' : 'no-child', isMobile ? 'mobile-item' :'pc-item' , {'active': isActive,}]"
    @click="onItemClick"
    @mouseenter="onMouseEnter"
    @mouseleave="onMouseLeave"
  >
    <div class="navbar-item-label flex items-center">
      <span>
        {{ itemInfo[labelField] }}
      </span>
      <div
        class="navbar-item-arrow"
        :class="{ 'arrow-active': showSub }"
        v-show="hasChild">
        <SvgoArrow></SvgoArrow>
      </div>
    </div>
    <!-- children -->
    <div
      v-if="hasChild"
      class="navbar-children-container"
      :class="[isMobile ? 'mobile-children-container' : 'pc-children-container', { 'open': hasChild && showSub, 'active': isActive }]"
    >
      <template
        v-for="child in children"
        :key="child[urlField]"
      >
        <NavbarItem
          :item="child"
          :parent="itemInfo"
          @item-click="(e) => emit('item-click', e)"></NavbarItem>
      </template>
    </div>
  </div>
</template>

<script
  setup
  lang="ts">
interface NavbarItemProp<T> {
  item: T;
  labelField?: string;
  childrenField?: string;
  urlField?: string;
  parent?: T;
}

const props = withDefaults(defineProps<NavbarItemProp<any>>(), {
  item: {} as any,
  labelField: 'label',
  childrenField: 'children',
  urlField: 'url',
})

const emit = defineEmits<{
  (event: 'item-click', item: any): void
}>()

const componentName = 'NavbarItem'
defineExpose({ componentName })

const children: any[] = props.item[props.childrenField]
const hasChild = computed(() => children?.length > 0)
const showSub = ref(false)
const itemInfo = props.item

const activeKeyList = inject('activeKeyList') as Ref<string[]>
const isMobile = inject('isMobile') as Ref<boolean>
// 菜单逻辑
const openDelay = 250
const timer = ref()


// 设置keylist
if (!props.parent) {
  itemInfo.keyList = [itemInfo[props.urlField]]
} else {
  itemInfo.keyList = [...props.parent.keyList, itemInfo[props.urlField]]
}

// active标识
const isActive = computed(() => {
  return activeKeyList.value.indexOf(itemInfo[props.urlField]) > -1
})

const route = useRoute()

onMounted(() => {
  // 往上返回activeKeyList
  const currentPath = route.path.replace(/(?<=\w+)\/$/, '')
  if (currentPath === itemInfo[props.urlField]) {
    dispatchClick()
  }
})


const runtimeConfig = useRuntimeConfig()

function onItemClick(e: Event) {
  e.stopPropagation()
  if (hasChild.value) {
    if (showSub.value) {
      closeMenu()
    } else {
      openMenu()
    }
  } else {
    location.href = runtimeConfig.public.deployUrl + itemInfo[props.urlField]
    dispatchClick()
  }
}

function onMouseEnter() {
  if (timer.value || isMobile.value) return
  timer.value = setTimeout(() => {
    openMenu()
  }, openDelay);
}

function onMouseLeave() {
  if (isMobile.value) return
  clearTimeout(timer.value)
  closeMenu()
}

function openMenu() {
  if (hasChild.value) {
    showSub.value = true
  }
  clearTimeout(timer.value)
  timer.value = null
}

function closeMenu() {
  if (hasChild.value) {
    showSub.value = false
    timer.value = null
  }
}

// 触发点击通知
function dispatchClick() {
  emit('item-click', itemInfo)
}
</script>

<style
  lang="scss"
  scoped>
// 一级菜单项
.navbar-container-item.navbar-menu-item.pc-item {
  padding: 20px 0;

  &.active > .navbar-item-label::after {
    content: '';
    display: block;
    position: absolute;
    left: 50%;
    top: 100%;
    transform: translate(-50%, 100%);
    width: 6px;
    height: 6px;
    background: $primary-color;
    border-radius: 3px;
  }

  &.active.has-child:hover .navbar-item-label::after {
    background: transparent;
  }

  .navbar-item-label {
    position: relative;
    justify-content: center;
  }
}

.navbar-item-arrow {
  color: currentColor;
  margin-left: 0px;
  transition: all .2s linear;
}


.pc-item {
  .navbar-item-arrow {
    transform: rotate(180deg);
    opacity: 0;
    max-width: 0;
  }

  .arrow-active {
    margin-left: 6px;
    max-width: 100px;
    opacity: 1;
    transform: rotate(0);
  }
}

.pc-item:hover {
  & > .navbar-item-label {
    color: $primary-color;
  }

  .navbar-item-arrow {
    margin-left: 6px;
    max-width: 16px;
    opacity: 1;
    transform: rotate(0);
  }
}

.pc-item.active {
  & > .navbar-item-label {
    color: $primary-color;
  }
}


.mobile-item {
  user-select: none;
  margin-top: 2.7rem;

  .navbar-item-arrow {
    margin-left: 11.15rem;
    transform: rotate(90deg);
  }

  .arrow-active {
    transform: rotate(180deg);
  }
}

// children
.navbar-children-container {
  box-sizing: border-box;
  font-size: 12px;
  background: #fff;
  color: $text;
  transition: all .2s linear;
  overflow: hidden;
  max-height: 0;
  will-change: max-height;

  &.open {
    max-height: 300px;
  }

  &.active .navbar-item-label {
    padding-left: 9px;
  }

  .navbar-menu-item {

    &.pc-item {
      margin: 16px 0 16px;
    }

    &.mobile-item {
      margin: 2.7rem 0 0 1.3rem;
    }

    &.pc-item.active {
      .navbar-item-label::before {
        content: '';
        display: block;
        position: absolute;
        top: 50%;
        left: 9px;
        transform: translateY(-50%);
        width: 6px;
        height: 6px;
        border-radius: 3px;
        background: $primary-color;
      }
    }
  }

}

.navbar-children-container .navbar-menu-item.mobile-item.active, .mobile-item.no-child.active {
  &::after {
    content: '';
    display: block;
    position: absolute;
    left: 6.5rem;
    top: 50%;
    transform: translate(0, -50%);
    width: 6px;
    height: 6px;
    background: $primary-color;
    border-radius: 3px;
  }
}

.pc-children-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 100;
  width: 76px;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.14);
  border-radius: 7px;
  z-index: 100;
}
</style>