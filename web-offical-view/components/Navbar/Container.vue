<template>
  <div class="navbar navbar-container">
    <div
      class="navbar-box ma"
      :class="{'flex items-center': !isMobile}"
      v-show="isReady"
    >
      <div class="flex items-center" :class="{'justify-between': isMobile}">
        <SvgoMenu
          v-if="isMobile"
          class="w-1.6rem"
          @click="toggleMenu"
        ></SvgoMenu>
        <div class="navbar-logo-container">
          <div class="navbar-logo">
            <img
              src="~/assets/img/logo.png"
              alt="logo"
            >
        </div>
      </div>
      </div>
      <div
        class="navbar-menu"
        :class="{ 'hidden': isMobile && menuHidden }"
      >
        <div
          class="menu-close-btn"
          v-if="isMobile"
        >
          <SvgoClose @click="toggleMenu"></SvgoClose>
        </div>
        <template
          v-for="(item, index) in menuList"
          :key="item.url"
        >
          <NavbarItem
            :item="item"
            class="navbar-container-item"
            @item-click="handleItemClick"
          ></NavbarItem>
        </template>
      </div>
      <div class="navbar-btn-container flex">
        <a class="navbar-btn navbar-btn-query text-center cursor-pointer box-border" @click="onClick">立即咨询</a>
        <a
          class="navbar-btn navbar-btn-login text-center cursor-pointer box-border"
          href="https://b.hjzxh.com/"
        >登录/注册</a>
      </div>
    </div>
    <div
      class="navbar-background"
      v-if="isMobile && !menuHidden"
      @click="toggleMenu"
    ></div>
  </div>
</template>

<script lang="ts" setup>
import { productionList, solutionList } from "~/setting";
import { Ref } from "vue";

interface MenuItem {
  label: string
  url: string
  children?: MenuItem[]
  [prop: string]: any
}

const componentName = 'NavbarContainer'
defineExpose({ componentName })
defineProps<{ isReady : boolean}>()
const emits = defineEmits<{
  (e: 'click'): void
}>()

const menuList: MenuItem[] = [
  { label: '首页', url: '/' },
  {
    label: '产品介绍',
    url: '/production',
    children: productionList
  },
  {
    label: '解决方案', url: '/solution', children: solutionList
  },
  { label: '生态合作', url: '/relation' },
]
const menuHidden = ref(true)
const isMobile = inject<Ref<boolean>>('isMobile')



const route = useRoute()
const activeKey = computed(() => route.fullPath)
const activeKeyList = ref([])

provide('activeKey', activeKey)
provide('activeKeyList', activeKeyList)


function toggleMenu() {
  menuHidden.value = !menuHidden.value
}

function handleItemClick(item: MenuItem) {
  activeKeyList.value = item.keyList || []
}

const onClick = () => {
  emits('click')
}
</script>

<style lang="scss" scoped>
@media screen and (min-width: 768px) {
  .navbar-container {
    height: 70px;
  }
}
.navbar {
  background: #fff;

  &-box {
    width: 74%;
    height: 100%;
    padding-left: .8rem;
  }
  &-logo-container {
    margin-right: 10vw;
  }
  &-logo {
    width: 126px;

    img {
      object-fit: contain;
    }
  }

  &-menu {
    display: flex;
    align-items: center;
    flex: 1.5 0 auto;
    justify-content: space-evenly;
    height: 100%;
    transition: transform linear .1s;
    margin-right: 5vw;
  }
  &-btn-container {
    flex: .3 0 auto;;
  }
  &-btn {
    width: 90px;
    height: 30px;
    line-height: 30px;
    border-radius: 7px;

    &-query {
      border: 1px solid #2D2D2D;
      margin-right: 12px;
    }

    &-login {
      margin-left: 12px;
      background: $primary-color;
      color: #fff;
    }
  }
}

// mobile style
@media screen and (max-width: 768px) {
  .navbar {
    position: sticky;
    top: 0;
    z-index: 999;
    &-box {
      margin: 0 1.6rem;
      padding: 0.25rem 0 0.25rem;
      overflow: hidden;
      width: auto;
    }
    &-logo-container {
      margin: 0;
    }
    &-logo {
      width: 102px;
    }

    &-menu {
      display: block;
      position: fixed;
      box-sizing: border-box;
      left: 0;
      top: 0;
      bottom: 0;
      width: 24rem;
      padding: 2.9rem 0 0 1.9rem;
      background: #fff;
      z-index: 100;

      &.hidden {
        transform: translateX(-100%);
      }
    }

    &-btn-container {
      display: none;
    }
  }
}

.navbar-background {
  position: fixed;
  height: 100vh;
  width: 100vw;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .3);
  z-index: 10;
}

.menu-close-btn {
  font-size: 1.6rem;
  margin-bottom: 4.15rem;
}</style>