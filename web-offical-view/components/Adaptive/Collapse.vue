<template>
  <div class="adaptive-collapse-container">
    <div
      class="adaptive-collapse-title flex"
      @click="collapsePanel"
    >
      <div class="flex-grow">
        <slot name="title"></slot>
      </div>
      <div v-if="collapse">
        <SvgoArrow :class="[collapsed ? 'collapse-arraw' : 'open-arrow']"></SvgoArrow>
      </div>
    </div>
    <component
      :is="collapse ? Transition : 'div'"
      class="adaptive-panel-container"
      :class="{'collapse-on': collapse}"
      @before-enter="onBeforeEnter"
      @enter="onEnter"
      @leave="onLeave"
      appear
    >
      <div v-show="!collapse || !collapsed">
        <slot name="panel"></slot>
      </div>
    </component>
  </div>
</template>

<script lang="ts" setup>
import { Transition } from 'vue'

/**
 * collapse: 是否启用折叠
 */
const props = withDefaults(defineProps<{
  collapse?: boolean
}>(), { collapse: false })


const collapsed = ref(true) // 内部折叠标识

// 动画hook
const onBeforeEnter = (el: Element) => {
  const elm = el as unknown as HTMLElement
  elm.style.height = '0px'
}
const onEnter = (el: Element, done: any) => {
  const elm = el as unknown as HTMLElement
  elm.style.height = el.scrollHeight + 'px'
  done()
}
const onLeave = (el: Element, done: any) => {
  const elm = el as unknown as HTMLElement
  elm.style.height = '0px'
}


function collapsePanel() {
  if (props.collapse) {
    collapsed.value = !collapsed.value
  }
}
</script>

<style lang="scss" scoped>
.collapse-arraw {
  transform: rotate(180deg);
}
.adaptive-panel-container {
  overflow: hidden;
  &.collapse-on {
    transition: all linear .2s;
  }
}
</style>