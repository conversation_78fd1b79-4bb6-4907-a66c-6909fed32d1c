<template>
  <div>
    <slot name="header"></slot>
    <div class="task-container">
      <ContainerTitle>您的企业是否遇到以下用工问题</ContainerTitle>
      <div class="task-list-container flex justify-center">
        <div
          class="task-item-container text-center"
          v-for="(item, index) in task"
          :key="index"
        >
          <p
            class="task-num"
            v-if="!isMobile">{{ index + 1 }}</p>
          <div class="task-icon ma">
            <ClientOnly>
              <img
                :src="getImg(`task-icon${isMobile ? '-mobile'+index : index}.png`)"
                alt=""
              ></ClientOnly>
          </div>
          <div class="task-label">{{ item.label }}</div>
          <div class="task-desc">{{ item.desc }}</div>
        </div>
      </div>
    </div>
    <slot></slot>
  </div>
</template>

<script
  setup
  lang="ts">

import { Ref } from "vue";

const task = [
  { label: '公对私转账难', desc: '无法从个人处获取合规凭证' },
  { label: '项目管理难', desc: '自由职业者数量庞大，任务零散' },
  { label: '费用控制难', desc: '财务人员结算所得效率低，易出错' },
  { label: '风险合规难', desc: '费用合规凭证归集难，涉税风险大' },
]

const isMobile = inject<Ref<boolean>>('isMobile')
</script>

<style
  scoped
  lang="scss">
// task
.task-container {
  margin-top: 55px;
}
.task-list-container {
  margin-top: 48px;
}

.task-item-container {
  width: 218px;
  margin: 0 19px;
  background-image: url('~/assets/img/kanban.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  box-shadow: 1px 8px 10px -8px rgba(45, 45, 45, 0.15);

  .task-num {
    margin-top: 22px;
    font-size: 24px;
    font-weight: bold;
    color: #fff;
  }

  .task-icon {
    width: 35px;
    height: 35px;
    margin-top: 23px;
  }

  .task-label {
    margin-top: 16px;
    font-size: 16px;
    color: #2B2B2B;
    font-weight: bold;
  }

  .task-desc {
    margin: 14px 0 26px;
    font-size: 14px;
    color: #2D2D2D;
  }
}

@media screen and (max-width: 768px) {
  .task-list-container {
    flex-wrap: wrap;
    margin-top: 1rem;
  }
  .task-item-container {
    box-sizing: border-box;
    width: 16.4rem;
    height: 19.45rem;
    margin: 1rem .5rem 0;
    padding: 0 3rem;
    background-image: url('~/assets/img/kanban-mobile.png');
    box-shadow: none;

    .task-label {
      margin-top: 1rem;
      font-size: 1.3rem;
      font-weight: bold;
    }

    .task-desc {
      font-size: 1.1rem;
    }
  }
}
</style>