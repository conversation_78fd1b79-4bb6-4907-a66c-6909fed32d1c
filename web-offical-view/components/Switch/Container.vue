<template>
  <div class="switch-container">
    <div class="switch-core">
      <div
        class="switch-text-container cursor-pointer"
        :class="{ 'checked': checked }"
        :style="switchStyle"
        @click="switchCheck"
      >
        <div
          class="switch-text switch-inactive-text text-center"
          :class="{ 'checked': !checked }"
        >{{ inactiveText }}
        </div>
        <div
          class="switch-text switch-active-text text-center"
          :class="{ 'checked': checked }"
        >{{ activeText }}
        </div>
        <div
          class="switch-slide"
          :class="{'checked': checked}"></div>
      </div>
    </div>
  </div>
</template>

<script
  lang="ts"
  setup>
type ValueType = boolean | number | string

const props = withDefaults(defineProps<{
  modelValue?: ValueType
  width?: number | string
  activeText?: string
  inactiveText?: string
  activeValue?: ValueType
  inactiveValue?: ValueType
}>(), {
  width: 236,
  activeText: '',
  inactiveText: '',
  activeValue: true,
  inactiveValue: false,
  modelValue: false,
})

const emits = defineEmits<{
  (e: 'change'): void
  (e: 'update:modelValue', val: ValueType): void
}>()

const checked = ref(false)

const switchStyle = computed(() => ({
  width: isNaN(props.width as number) ? props.width : (props.width + 'px')
}))

function switchCheck(e: Event) {
  const elm = e.target as HTMLElement
  if (elm.className.indexOf('checked') === -1) {
    checked.value = !checked.value
    emits('change')
    emits('update:modelValue', checked.value ? props.activeValue : props.inactiveValue)
  }
}

onMounted(() => {
  emits('update:modelValue', props.modelValue)
  checked.value = props.modelValue === props.activeValue
})

</script>

<style
  lang="scss"
  scoped>
.switch {
  &-container {
    display: inline-flex;
  }

  &-core {
    border: 3px solid $primary-color;
    padding: 7px 9px;
    background-color: rgba(135, 188, 253, .3);
    border-radius: 30px;
  }

  &-text-container {
    position: relative;
    display: flex;
    width: 100%;

    .switch-slide {
      position: absolute;
      display: block;
      top: 0;
      left: 0;
      bottom: 0;
      width: 50%;
      border-radius: 22px;
      background: #fff;
      transition: all linear .2s;

      &.checked {
        left: 50%;
      }
    }
  }

  &-text {
    box-sizing: border-box;
    width: 50%;
    padding: 14px 0;
    color: #929292;
    z-index: 20;
    white-space: nowrap;

    &.checked {
      color: $primary-color;
    }
  }
}

@media screen and (max-width: 768px) {
  .switch {

    &-core {
      padding: .5rem .6rem;
    }

    &-text {
      padding: 1rem 0;
      font-size: 1rem;
    }
  }
}

</style>