<template>
  <div class="production-advaned-container">
    <ContainerTitle>我们的核心优势</ContainerTitle>
    <div class="advanced-container flex flex-wrap">
      <div
        class="advanced-item-container text-center"
        v-for="(item, index) in items"
        :key="index"
      >
        <div class="advanced-item-img">
          <ClientOnly>
            <img
              v-lazy="getImg(`footer/foot-img-${index}.png`)"
              :alt="item.title">
          </ClientOnly>
        </div>
        <div class="advanced-item-title">
          {{ item.title }}
        </div>
        <div class="advanced-item-desc">
          {{ item.desc }}
        </div>
      </div>
    </div>
  </div>
</template>

<script
  setup
  lang="ts">
import img0 from '~/assets/img/footer/foot-img-0.png';
import img1 from '~/assets/img/footer/foot-img-1.png';
import img2 from '~/assets/img/footer/foot-img-2.png';
import img3 from '~/assets/img/footer/foot-img-3.png';
import img4 from '~/assets/img/footer/foot-img-4.png';
import img5 from '~/assets/img/footer/foot-img-5.png';

const items = [
  { title: '质量监控', desc: '专业运营后台应急咨询及问题处理', img: img0 },
  { title: '贴心客服', desc: '实时为您解决问题VIP服务全年无休', img: img1 },
  { title: '技术支持', desc: '专业的综合服务团队实时维护解决问题', img: img2 },
  { title: '快速接入方式', desc: '接口简单、安全专人协助快速接入', img: img3 },
  { title: '强大的商户后台', desc: '实时交易明细查询实时账务变动查询', img: img4 },
  { title: '全方位安全保护', desc: '交易数据全加密专业的风控团队', img: img5 },
]
</script>

<style
  scoped
  lang="scss">
// advanced
@media screen and (min-width: 768px) {
  .production-advaned-container {
    margin-bottom: 145px;
  }
  .advanced-container {
    width: 71%;
    margin: 0 auto;
  }
}

.advanced-item {
  &-container {
    flex-grow: 1;
    padding: 60px 5.9vw 0;
    width: 10vw;
  }

  &-img {
    margin: 0 auto;
  }

  &-title {
    font-size: 14px;
    font-weight: bold;
    color: $title-color;
  }

  &-desc {
    margin: 12px 9px 0;
    font-size: 12px;
    color: #666;
  }
}

@media screen and (max-width: 768px) {
  .production-advaned-container {
    margin-bottom: 3rem;
  }
  .advanced-container {
    margin: 0 0 6rem;
  }

  .advanced-item {
    &-container {
      width: 10rem;
      padding: 3rem 4.2rem 0;
    }

    &-title {
      font-size: 1rem;
    }

    &-desc {
      margin: 12px 0 0;
      font-size: .9rem;
    }
  }
}</style>