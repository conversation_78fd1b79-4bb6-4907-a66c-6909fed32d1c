<template>
  <div class="pointer-list-container">
    <div class="pointer-list flex">
      <div
        v-for="(item,index) in list"
        :key="index"
        class="pointer-list-item">
        <p class="list-name">{{ item.name }}</p>
        <p class="list-text">{{ item.text }}</p>
      </div>
    </div>
  </div>
</template>

<script
  setup
  lang="ts">
interface ListItem {
  name: string
  text: string
}

const props = defineProps<{
  list: ListItem[]
}>()
</script>

<style
  scoped
  lang="scss">
.pointer-list-container {
  margin-top: 42px;
}

.pointer-list-item {
  width: 230px;
  height: 183px;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &:hover {
    .list-name, .list-text {
      color: $primary-color;
    }
  }

  .list-name {
    color: #2B2B2B;
    font-size: 16px;
    font-weight: bold;
  }

  .list-text {
    color: #2D2D2D;
    font-size: 14px;
    line-height: 22px;
  }
}

@media screen and (min-width: 768px) {
  .pointer-list {
    justify-content: center;
  }
  .pointer-list-item {
    margin: 0 18px;
    padding: 0 32px;
    background-image: url('~/assets/img/solution-kanban.png');
  }
  .list-name {
    margin-top: 30px;
    text-align: center;
  }
  .list-text {
    margin-top: 15px;
  }
}

@media screen and (max-width: 768px) {
  .pointer-list {
    flex-direction: column;
  }
  .pointer-list-item {
    width: 35rem;
    height: 9.55rem;
    margin:0 auto 1rem;
    padding: 1.8rem 0 0 2.55rem;
    background-image: url('~/assets/img/solution-kanban-mobile.png');
    .list-name {
      font-size: 1.3rem;
      font-weight: bold;
    }
    .list-text {
      margin-top: .5rem;
      font-size: 1.1rem;
    }
  }
}

</style>