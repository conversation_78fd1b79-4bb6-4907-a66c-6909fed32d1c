<template>
  <div class="footer-container">
    <!-- img -->
    <div
      class="foot-img-container flex items-end"
      v-if="!isMobile"
    >
      <!-- pc -->
      <div class="foot-pc-img">
        <img
          src="~/assets/img/footbar.png"
          alt="foot-img"
        >
      </div>
      <div class="foot-pc-text">
        <p>已为3000+商户</p>
        <p>提供灵活用工解决方案</p>
        <div class="foot-btn-container">
          <button
            @click="emits('click')"
            class="primary-btn"
            v-width="107"
          >立即咨询</button>
        </div>
      </div>
    </div>
    <!-- menu -->
    <div
      class="foot-info-menu-block"
      :class="{ 'foot-mobile-menu': isMobile }"
    >
      <div
        class="foot-info-container"
        :class="{ 'flex': !isMobile, 'foot-info-mobile-container': isMobile }"
      >
        <div
          class="foot-logo-container"
          :class="[isMobile ? 'mobile-logo' : 'pc-logo']"
        >
          <img
            src="~/assets/img/logo-white.png"
            alt="logo"
          >
        </div>
        <div
          class="foot-url-container flex justify-between flex-grow"
          :class="{ 'mobile-foor-url-container': isMobile }"
        >
          <template
            v-for="(menu) in menuList"
            :key="menu.url"
          >
            <div class="menu-item-container">
              <AdaptiveCollapse :collapse="isMobile">
                <template #title>
                  <div class="menu-item-title">{{ menu.label }}</div>
                </template>
                <template #panel>
                  <a
                    v-for="(child) in menu.children"
                    :key="child.url"
                    class="menu-item-panel cursor-pointer"
                    @click="onMenuItemClick(child)"
                  >
                    {{ child.label }}</a>
                </template>
              </AdaptiveCollapse>
            </div>
          </template>
          <div
            class="menu-item-container"
            :class="{ 'mobile-address': isMobile }"
            ref="addressRef"
            v-width="195"
          >
            <div class="menu-item-title">联系方式</div>
            <div class="menu-item-panel">
              电话：{{ contactInfo.phone }}
            </div>
            <div class="menu-item-panel">邮箱：{{ contactInfo.email }}</div>
            <div class="menu-item-panel">
              地址：{{ contactInfo.address }}
            </div>
          </div>
        </div>
      </div>
      <div class="text-center foot-isp">
        Copyright&copy;2020广州市汇聚智享电子科技有限公司 <a href="http://beian.miit.gov.cn/">粤ICP备2020096931号</a></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { contactInfo, linkList, productionList, solutionList } from "~/setting";
import { Ref } from "vue";

interface Menu {
  label: string;
  url: string;
  children?: Menu[];
}

const emits = defineEmits<{
  (e: 'click'): void
}>()

const isMobile = inject<Ref<boolean>>('isMobile')

const footSolution = JSON.parse(JSON.stringify(solutionList))
footSolution.forEach((item: Menu) => item.label += '行业')
const constantMenu = [
  {
    label: '产品',
    url: '/production',
    children: productionList
  },
  {
    label: '解决方案', url: '/solution', children: footSolution
  },
  {
    label: '生态合作', url: '/relation', children: [
      { label: '我是财税服务商', url: '/relation?type=100' },
      { label: '我是渠道代理', url: '/relation?type=101' },
    ]
  },
]
const pcMenu = {
  label: '友情链接',
  url: '/link',
  children: linkList
}
const menuList = computed<Menu[]>(() => isMobile!.value ? constantMenu : [...constantMenu, pcMenu])

// address ref
const addressRef = ref<HTMLElement>()
watch(isMobile!, (val, old) => {
  // 联系方式位置改变
  let parent = addressRef.value?.parentElement
  if (!parent) return
  if (val) {
    parent?.insertBefore(addressRef.value!, parent.childNodes[0])
  } else {
    parent?.appendChild(addressRef.value!)
  }
}, {immediate: true})

const runtimeConfig = useRuntimeConfig()
function onMenuItemClick(item: Menu) {
  let url = item.url
  if (!/https/.test(url)) {
    url = runtimeConfig.public.deployUrl + url
  }
  location.href = url
}

</script>

<style lang="scss" scoped>
.footer-container {
  margin-top: 11vw;
}
// img
.foot-img-container {
  padding-left: 6vw;
  height: 22vw;
  background: $back-gray;

  .foot-pc-img {
    margin-right: 6.5vw;
    width: 46vw;
  }

  .foot-pc-text {
    padding: 5.6vw 0;
    font-weight: bold;
    font-size: 30px;
    color: #2A3E55;
  }

  .foot-btn-container {
    margin-top: 2vw;
  }
}

// foot-info-container 
.foot-info-menu-block {
  box-sizing: border-box;
  padding: 40px 0 33px;
  background: #202430;
  color: #fff;

  .foot-info-container {
    width: 73%;
    margin: 0 auto 30px;
    padding-bottom: 33px;
    border-bottom: 2px solid #31363C;
  }

  .foot-info-mobile-container {
    width: 90%;
    margin-bottom: 1rem;
    padding-bottom: 8rem;
  }

  .menu-item-title {
    font-size: 16px;
  }

  .menu-item-panel {
    display: block;
    margin-top: 20px;
    font-size: 12px;
    &:first-of-type {
      margin-top: 23px;
    }
  }

  .foot-logo-container {
    margin-right: 4vw;
    width: 115px;
  }

  .foo-isp {
    font-size: 14px;
  }
}

// 移动端
.foot-mobile-menu {
  .foo-isp {
    font-size: 10px;
  }

  .mobile-foor-url-container {
    flex-direction: column;
    flex-wrap: wrap;

    .menu-item-container {
      width: 100% !important;
      margin-top: 3rem;

      &:not(:first-of-type) {
        .menu-item-panel {
          margin-left: 1.4rem;
        }
      }
    }
  }
}
</style>