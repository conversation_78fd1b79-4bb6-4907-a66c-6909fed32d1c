<template>
  <div
    class="form-input-container"
    :class="{'mobile-input-container': mobile}">
    <div
      class="form-input-inner"
      :class="{'mobile-input-inner': mobile}">
      <input
        v-if="type === 'text'"
        class="form-input__core"
        :class="{'mobile-input__core': mobile}"
        :value="modelValue"
        :placeholder="placeholder"
        @change="onChange" />

      <textarea
        v-else
        class="form-input__core"
        :class="{'mobile-input__core': mobile}"
        :value="modelValue"
        :placeholder="placeholder"
        :rows="rows"
        @change="onChange"></textarea>
    </div>
  </div>
</template>

<script
  setup
  lang="ts">
const props = withDefaults(defineProps<{
  modelValue: string
  placeholder: string
  type?: 'text' | 'textarea'
  mobile?: boolean
  rows?: number|string
}>(), { modelValue: '', type: "text", placeholder: '请输入内容', mobile: false, rows: 4 })

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

const onChange = (e: InputEvent) => {
  emit('update:modelValue', (e.target as HTMLInputElement).value)
}
</script>

<style
  scoped
  lang="scss">
.form-input-container {
  margin-bottom: 15px;
}

.form-input__core {
  box-sizing: border-box;
  width: 100%;
  padding: 13px 0 13px 15px;
  border: 1px solid #B2B2B2;
  font-size: 14px;
  border-radius: 3px;;
}


//mobile
.mobile-input-container {
  margin-bottom: 1.1rem;
}

.mobile-input__core {
  padding: 1.6rem 0 1.6rem 1rem;
  border: none;
  background: #F8F8F8;
  border-radius: .5rem;
  resize: none;
}
</style>