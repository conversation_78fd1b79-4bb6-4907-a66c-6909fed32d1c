<template>
  <div
    class="banner-container cursor-pointer"
    @click="onClick">
    <ClientOnly>
      <img
        v-show="bannerUrl"
        class="banner-img"
        :src="bannerUrl"
      >
    </ClientOnly>
    <slot></slot>
    <div
      class="banner-text"
      :style="isMobile ? mobileTextStyle : textStyle">
      <template v-if="showText">
        <div class="banner-head-text white-nowrap">
          <slot name="header"></slot>
        </div>
        <div class="banner-tip-text">
          <slot name="tip"></slot>
        </div>
      </template>
      <button
        v-if="showBtn"
        class="primary-btn banner-btn">
        立即咨询
      </button>
    </div>
  </div>
</template>

<script
  setup
  lang="ts">
import { getImg } from "@/utils";
import { Ref } from "vue";

const props = withDefaults(defineProps<{
  imgUrl: string
  showText?: boolean
  showBtn?: boolean
  textStyle?: object
  mobileTextStyle?: object
  textWidth?: string
}>(), {
  imgUrl: '',
  showText: true,
  showBtn: true,
  textStyle: undefined,
  textWidth: '33rem',
})

const emit = defineEmits<{
  (e: 'click'): void
}>()

const isMobile = inject<Ref<boolean>>('isMobile')

const bannerUrl = computed(() => {
  const actualUrl = isMobile?.value ? props.imgUrl + '-mini' : props.imgUrl
  return getImg('banner/' + actualUrl + '.png')
})


function onClick() {
  emit('click')
}
</script>


<style
  lang="scss"
  scoped>
.banner {
  &-container {
    position: relative;
    height: 37.5vw;
    overflow: hidden;
  }

  &-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &-text {
    position: absolute;
    top: 9.6vw;
    left: 14vw;
    z-index: 10;
    letter-spacing: 2px;
    width: 44vw;
    max-width: 38vw;
  }

  &-head-text {
    font-size: 3.6vw;
    font-weight: bold;
    color: #2B3137;
    line-height: 4.9vw;
  }

  &-tip-text {
    margin: 8px 0 50px;
    font-size: 1.2vw;
    font-weight: 500;
    color: #2B3137;
    opacity: 0.9;
  }

  &-btn {
    width: 150px;
    height: 53px;
    font-size: 19px;
    font-weight: 400;
    color: #FEFEFE;
    letter-spacing: 1px;
  }
}

@media screen and (max-width: 768px) {
  .banner {
    &-container {
      height: 35.9rem;
      margin-bottom: 3rem;
    }
    &-text {
      width: v-bind(textWidth);
      max-width: unset;
      left: 1.5rem;
    }

    &-head-text{
      font-size: 3rem;
      font-weight: 800;
      color: #2B3137;
      line-height: 3.9rem;
    }
    &-tip-text {
      margin: 1rem 0 2.8rem;
      font-size: 1.3rem;
      font-weight: normal;
      color: #666;
      opacity: 0.9;
    }


    &-btn {
      width: 9.6rem;
      height: 3.4rem;
      opacity: 0.95;
      border-radius: 0.5rem;
      font-size: 1.2rem;
      font-weight: 500;
      line-height: 1.5rem;
    }
  }
}
</style>