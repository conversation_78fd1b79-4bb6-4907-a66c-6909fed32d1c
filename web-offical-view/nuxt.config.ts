// https://nuxt.com/docs/api/configuration/nuxt-config
const appBaseUrl = process.env.NUXT_APP_BASE_URL
export default defineNuxtConfig({
  app: {
    baseURL: appBaseUrl,
    head: {
      script: [
        { src: `${appBaseUrl}static/js/hotcss.js`, defer: true }
      ],
      link: [
        {rel: 'icon', href: `${appBaseUrl}favicon.ico`}
      ],
      title: '汇聚智享',
    }
  },
  css: [
    '@unocss/reset/normalize.css',
    '@/assets/scss/style.scss',
  ],
  modules: [
    '@nuxt/image',
    '@unocss/nuxt',
    'nuxt-svgo',
    'nuxt-swiper'
  ],
  image: {
    quality: 100,
    dir: 'assets/img',
  },
  swiper: {
  },
  runtimeConfig: {
    public: {
      deployUrl: '',
      baseApi: '',
    }
  },
  nitro: {
    devProxy: {
      '/api': {
        target: 'http://************:20900',
        changeOrigin: true
      }
    }
  },
  vite: {
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/assets/scss/_color.scss" as *;
          @use "@/assets/scss/_mixin.scss" as *;
          @use "@/assets/scss/extend.scss" as *;`
        }
      }
    },
    build: {
      // FIXME: 部份背景图片路径打包后出错
      // rollupOptions: {
      //   output: {
      //     assetFileNames: (assetInfo) => {
      //       let extType = assetInfo.name?.split('.')[1] ?? ''
      //       if (/(png|jpe?g|svg)$/i.test(extType)) {
      //         extType ='img'
      //       } else if (/s?css$/i.test(extType)) {
      //         extType = 'css'
      //       }
      //       return `_nuxt/${extType}/[name].[hash].[ext]`
      //     }
      //   }
      // }
    }
  },
})
