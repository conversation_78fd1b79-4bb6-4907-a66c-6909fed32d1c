<template>
  <el-dialog
    width="800px"
    title="回复"
    :before-close="close"
    :visible="visible"
    append-to-body
  >
    <!--tinymce 保持v-if 命令，解决二次打开无法输入-->
    <tinymce
      v-if="visible"
      v-model="content"
      id="tinymce"
      :show-img-upload="false"
      :menubar="false"
      :height="250"
      toolbar="simple"
      ref="editor"
    ></tinymce>
    <template v-slot:footer>
      <el-button type="primary" @click="confirm">回复</el-button>
      <el-button @click="close">取消</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import Tinymce from '@/components/Tinymce'
  export default {
    name: 'ReplyDialog',
    components: {
      Tinymce
    },
    props: {
      value: {
        type: String,
        default: ''
      },
      visible: {
        type: Boolean,
        default: false,
      }
    },
    data() {
      return {
        content: ''
      }
    },
    watch: {
      content(val) {
        this.$emit('input', val);
      }
    },
    methods: {
      close() {
        this.$emit('update:visible', false)
      },
      confirm() {
        this.$emit('confirm');
        this.$nextTick(() => {
          this.$refs.editor.setContent('')
        });
        this.close();
      }
    },
  }
</script>

<style scoped lang="scss">

</style>
