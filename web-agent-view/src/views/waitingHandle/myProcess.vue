<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">流程状态：</span>
            <el-select clearable v-model="form.status">
              <el-option
                v-for="item in $dict('FlowStatus')"
                :key="item.code"
                :value="item.code"
                :label="item.desc"
              ></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">发起人：</span>
            <el-input v-model="form.initiatorNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">流程主题：</span>
            <el-input v-model="form.flowTopicNameLike"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">开始时间：</span>
            <date-picker
              ref="datePicker"
              type="datetimerange"
              v-model="form.timeRange"
              @change="getTimeRange"
            ></date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="search"
            >搜索</el-button>
          </div>
        </div>
      </div>
    </div>

    <!--<el-radio-group-->
      <!--v-model="activeName"-->
      <!--@change="changTab"-->
    <!--&gt;-->
      <!--<el-radio-button label="third">待我审批的</el-radio-button>-->
      <!--<el-radio-button label="first">我收到的审批</el-radio-button>-->
      <!--<el-radio-button label="second">我发出的审批</el-radio-button>-->
    <!--</el-radio-group>-->

    <div class="content-container">
      <el-table
        :data="receivedObj.data"
      >
        <el-table-column
          label="流水号"
          prop="id"
        />

        <el-table-column
          label="流程主题"
          prop="flowTopicType"
          min-width="100px"
        >
          <template v-slot="{row}">
            {{ row.flowTopicName }}
          </template>
        </el-table-column>
        <el-table-column
          label="发起人"
          prop="initiatorName"
          min-width="80px"
        />

        <el-table-column
          label="流程状态"
          prop="status"
          min-width="60px"
        >
          <template v-slot="{row}">
            <el-tag v-if="row.status == 101">
              {{ $dictCode('FlowStatus', row.status).desc }}
            </el-tag>
            <el-tag v-if="row.status == 100" type="success">
              {{ $dictCode('FlowStatus', row.status).desc }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="流程开始时间"
          prop="createTime"
          min-width="140px"
        />
        <el-table-column
          label="最后更新时间"
          prop="updateTime"
          min-width="140px"
        >
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="70px"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="$router.push('/waitingHandle/detailProcess?processId=' + scope.row.id)"
            >详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container">
      <el-pagination
        v-if="receivedObj"
        ref="pagination"
        :total="receivedObj.totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10,50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
  import { getSend } from '@/api/waitingHandle'

  export default {
    name: 'myProcess',
    data() {
      return {
        activeName: 'third',
        form: {
          status: '',
          timeRange: [],
          initiatorNameLike: '',
          flowTopicNameLike: '',
        },
        pageCurrent: 1,
        pageSize: 10,
        receivedObj: {},
      }
    },
    mounted() {
      this.getSend();
    },
    methods: {
      getTimeRange(data) {
        this.form.timeRange = data;
      },
      changTab() {
        // 重置基础信息
        this.form = {
          status: '',
          timeRange: [],
          initiatorNameLike: '',
          flowTopicNameLike: '',
        }
        this.$refs.datePicker.clearTime();
        this.pageSize = 10;
        this.pageCurrent = 1;
        switch (this.activeName) {
          case 'first':
            this.getReceived();
            break;
          case 'second':
            this.getSend();
            break;
          case 'third':
            this.getPending();
            break;
          default:
            break;
        }
      },
      getReceived(initFlag) {
        if (initFlag === true) {
          this.pageCurrent = 1;
        }
        getReceived({
          status: this.form.status,
          beginDate: this.form.timeRange[0],
          endDate: this.form.timeRange[1],
          pageCurrent: this.pageCurrent,
          initiatorNameLike: this.form.initiatorNameLike,
          flowTopicNameLike: this.form.flowTopicNameLike,
          pageSize: this.pageSize
        }).then(response => {
          this.receivedObj = response.data;
        })
      },
      getSend(initFlag) {
        if (initFlag === true) {
          this.pageCurrent = 1;
        }
        getSend({
          status: this.form.status,
          beginDate: this.form.timeRange[0],
          endDate: this.form.timeRange[1],
          pageCurrent: this.pageCurrent,
          initiatorNameLike: this.form.initiatorNameLike,
          flowTopicNameLike: this.form.flowTopicNameLike,
          pageSize: this.pageSize
        }).then(response => {
          this.receivedObj = response.data;
        })
      },
      getPending(initFlag) {
        if (initFlag === true) {
          this.pageCurrent = 1;
        }
        getPending({
          status: this.form.status,
          beginDate: this.form.timeRange[0],
          endDate: this.form.timeRange[1],
          pageCurrent: this.pageCurrent,
          initiatorNameLike: this.form.initiatorNameLike,
          flowTopicNameLike: this.form.flowTopicNameLike,
          pageSize: this.pageSize
        }).then(response => {
          this.receivedObj = response.data;
        })
      },
      search(initFlag) {
        // switch (this.activeName) {
        //   case 'first':
        //     this.getReceived(initFlag);
        //     break;
        //   case 'second':
        //     this.getSend(initFlag);
        //     break;
        //   case 'third':
        //     this.getPending(initFlag);
        //     break;
        //   default:
        //     break;
        // }
        this.getSend(initFlag);
      },
      handleCurrentChange(val) {
        this.pageCurrent = val;
        this.search();
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.search(true);
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
    }
  }
</script>

<style lang="scss" scoped>
  .box-container {
    .el-radio-group {
      margin-top: 16px;
    }
  }
</style>
