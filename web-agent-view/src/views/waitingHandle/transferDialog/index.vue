<template>
  <!--更改审批人-->
  <el-dialog
    :visible.sync="visible"
    title="更改审批人"
    :close-on-click-modal="false"
    :before-close="close"
    width="400px"
  >
    <p>请选择审批人</p>
    <el-select
      v-model="nextUserId"
      clearable
    >
      <el-option
        v-for="item in staff"
        :key="item.id"
        :value="item.id"
        :label="item.name"
      ></el-option>
    </el-select>

    <template v-slot:footer>
      <el-button @click="confirm" type="primary">确定</el-button>
      <el-button @click="close">取消</el-button>
    </template>
  </el-dialog>

</template>

<script>
  export default {
    name: "TransferTask",
    props: {
      taskId: {
        type: [String, Number],
        default: '',
      },
      commonFlowId: {
        type: [String, Number],
        default: '',
      },
      staff: {
        type: Array,
        default: () => [],
      },
      visible: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        nextUserId: null,
      }
    },
    computed: {
      form({ taskId, commonFlowId, nextUserId }) {
        return {
          commonFlowId,
          taskId,
          nextUserId,
        }
      },
    },
    methods: {
      close() {
        this.$emit('update:visible', false)
      },
      confirm() {
        this.$emit('confirm', { ...this.form });
        this.nextUserId = null;
        this.close();
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
