<template>
  <div>
    <div class="warning-container">
      <i class="el-icon-warning"></i>
      <div class="warning-content">
        更换负责人后，原负责人帐号将无法管理商户
      </div>
    </div>
    <div class="leader-change-warpper">
      <el-form
        class="leader-change-form"
        ref="leaderChangeForm"
        :model="leaderChangeForm"
        :rules="leaderChangeRules"
        label-position="right"
        label-width="150px"
      >
        <el-form-item label="原负责人：">
          {{ originLeader.name }} （+86-{{ originLeader.phone }}）
        </el-form-item>

        <el-form-item
          label="新负责人手机号："
          prop="newLeaderPhone"
        >
          <el-input v-model="leaderChangeForm.newLeaderPhone">
            <template slot="prepend">+86</template>
          </el-input>
          <span class="remark">新负责人使用该手机号作为帐号即可登录后台</span>
        </el-form-item>

        <el-form-item
          label="新负责人姓名："
          prop="newLeaderName"
        >
          <el-input v-model="leaderChangeForm.newLeaderName">

          </el-input>
        </el-form-item>

        <el-form-item
          label="短信验证码："
          prop="smsCode"
        >
          <el-input v-model="leaderChangeForm.smsCode">
            <template slot="append">
              <SmsCode :phone="leaderChangeForm.newLeaderPhone"></SmsCode>
            </template>
          </el-input>
          <span class="remark">验证短信将发送到新负责人手机号</span>.
        </el-form-item>

        <el-form-item
          label="支付密码："
          prop="tradePwd"
        >
          <el-input
            v-model="leaderChangeForm.tradePwd"
            type="password"
            autocomplete="new-password"
            maxlength="6"
          >
          </el-input>
        </el-form-item>

        <el-button
          class="submit-btn"
          type="primary"
          @click="onSubmit"
        >保存</el-button>

        <el-button
          class="cancel-btn"
          @click="onCancel"
        >取消</el-button>
      </el-form>
    </div>
  </div>
</template>

<script>
  import { getLeader, changeLeaderSms, changeLeader } from "@/api/safeSetting";
  import { sendSmsCode } from "@/api/common";
  import { encryptParam } from "@/utils/jsencrypt";
  import { validateParams } from "../../utils/validate";
  import SmsCode from "../../components/SmsCode/index";
  import { getKey } from '@/utils/publicKey'

  export default {
    name: "leaderChange",
    components: {
      SmsCode
    },
    data() {
      return {
        originLeader: {},  // 原负责人
        leaderChangeForm: {
          newLeaderPhone: null,
          newLeaderName: null,
          smsCode: null,
          tradePwd: null
        },
        leaderChangeRules: {
          newLeaderPhone: [
            { required: true, message: '请输入新负责人手机号', trigger: 'blur' },
            { validator: validateParams({ type: 'Phone', msg: '请输入正确的手机号' }), trigger: 'blur' }
          ],
          newLeaderName: [
            { required: true, message: '请输入新负责人姓名', trigger: 'blur' },
            { validator: validateParams({ type: 'Chinese', msg: '请输入中文' }), trigger: 'blur' },
            { validator: validateParams({ type: 'Length', max: '15', msg: '最多输入15个中文' }), trigger: 'blur' }
          ],
          smsCode: [
            { required: true, message: '请输入短信验证码', trigger: 'blur' },
          ],
          tradePwd: [
            { required: true, message: '请输入支付密码', trigger: 'blur' },
          ],
        }
      };
    },
    mounted() {
      getLeader().then(({ data }) => {
        this.originLeader = data;
      })
    },
    methods: {
      onSubmit() {
        this.$refs.leaderChangeForm.validate(valid => {
          if (valid) {
            let form = { ...this.leaderChangeForm }
            form.tradePwd = encryptParam({
              pwd: form.tradePwd,
              publicKey: getKey(),
            })
            changeLeader(form).then((data) => {
              if (data.code === 20000) {
                this.$message.success(data.data);
                this.$router.push("/system/safe");
              }
            })
          }
        })
      },
      onCancel() {
        this.$router.push("/system/safe");
      }
    }
  };
</script>

<style scoped lang="scss">
  .leader-change-warpper {
    text-align: center;
    background: #ffffff;

    .leader-change-form {
      box-sizing: content-box;
      width: 500px;
      padding: 50px;
      margin-top: 16px;
      ::v-deep .el-form-item__content {
        text-align: left;
      }
      .el-form-item {
        margin-bottom: 24px;
      }
      .remark {
        font-size: 14px;
        color: #00000072;
      }

      .captcha-btn {
        color: #fff;
        background-color: #409eff;
        border-color: #409eff;
        border-radius: 0px;
      }
      .submit-btn {
        width: 120px;
      }

      .cancel-btn {
        width: 120px;
      }
    }
  }
</style>
