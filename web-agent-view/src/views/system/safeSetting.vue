<template>
    <div class="safe-setting-warpper page-container">
      <div class="setting-container">
				<div class="setting-title"><span>负责人 +86 {{ admin.phone }} ({{ admin.name }})</span></div>
				<el-button v-permission="'safe:leader:change'" type="info" plain @click="toChangeLeader">更换</el-button>
			</div>
      <el-divider></el-divider>

			<div class="setting-container">
				<div class="setting-title"><span>常用邮箱 {{ email }}</span></div>
				<el-button v-permission="'safe:email:change'" type="info" plain @click="toChangeEmail">更换</el-button>
			</div>
      <el-divider></el-divider>
			
			<div class="setting-container" v-if="isSettingTradePwd">
				<div class="setting-title"><span>支付密码 已设置</span></div>
				<div class="setting-remark"><span>用于付款或其他操作验证</span></div>
				<el-button v-permission="'safe:tradePwd:change'" type="info" plain @click="toChangeTradePwd">更换</el-button>
			</div>
			<div class="setting-container" v-else>
				<div class="setting-title no-setting-pwd"><span>支付密码 未设置</span></div>
				<div class="setting-remark"><span>用于付款或其他操作验证</span></div>
				<el-button v-permission="'safe:tradePwd:change'" type="info" plain @click="toChangeTradePwd">设置</el-button>
			</div>
    </div>
</template>

<script>
import { getSafeSetting } from "@/api/safeSetting"

export default {
		name: "safeSetting",
		data() {
			return {
				admin: {	},
				email: null,
				isSettingTradePwd: false
			};
		},
		mounted() {
			getSafeSetting().then(({ data }) => {
				this.admin = data.admin || { };
				this.email = data.email;
				this.isSettingTradePwd = data.settingTradePwd;
			});
		},
		methods: {
			toChangeLeader() {
				this.$router.push("/safe/leaderChange");
			},
			toChangeEmail() {
				this.$router.push("/safe/emailChange");
			},
			toChangeTradePwd() {
				this.$router.push("/safe/tradePwdChange");
			}
		}
}
</script>

<style scoped lang="scss">
.safe-setting-warpper {

	.setting-container {
		display: flex;
		flex-direction: column;
		padding: 24px 8px 16px 24px;

		.setting-title {
			margin-bottom: 16px;
			font-size: 20px;
			font-weight: bold;
			color:#666666;
			line-height: 28px;
		}
		
		.no-setting-pwd {
			color:#F59A23
		}

		.setting-remark {
			margin-bottom: 16px;
			font-size: 16px;
			color:#999999;
			line-height: 24px;
		}

		.el-button {
			width: 150px;
			height: 45px;
		}
	}

	.el-divider {
		margin: 0px 0px 0px 0px;
	}
}
</style>