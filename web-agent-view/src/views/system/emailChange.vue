<template>
  <div class="email-change-warpper">
    <el-form
      class="email-change-form" ref="emailChangeForm" :model="emailChangeForm"
      :rules="emailChangeRules" label-position="right" label-width="150px">
      <el-form-item label="原常用邮箱：">
        {{ originEmail }}
      </el-form-item>

      <el-form-item label="新常用邮箱：" prop="newEmail">
        <el-input v-model="emailChangeForm.newEmail">

        </el-input>
        <span class="remark">请输入新的邮箱地址</span>
      </el-form-item>

      <el-form-item label="邮箱验证码：" prop="verifyCode">
        <el-input v-model="emailChangeForm.verifyCode">
          <template slot="append">
            <el-button type="primary" class="captcha-btn" @click="onSendCode" :disabled="!emailChangeForm.newEmail || !sendCodeAccesss">
              {{ sendCodeBtnWord }}
            </el-button>
          </template>
        </el-input>
        <span class="remark">已向新的邮箱发送验证码</span>
      </el-form-item>

      <el-form-item label="支付密码：" prop="tradePwd">
        <el-input
          v-model="emailChangeForm.tradePwd"
          type="password"
          autocomplete="new-password"
          maxlength="6">
        </el-input>
      </el-form-item>

      <el-button class="submit-btn" type="primary" @click="onSubmit">保存</el-button>

      <el-button class="cancel-btn" @click="onCancel">取消</el-button>
    </el-form>
  </div>
</template>

<script>
  import { getEmail, changeEmailVerifyCode, changeEmail } from "@/api/safeSetting";
  import { encryptParam } from "@/utils/jsencrypt";
  import { validateParams } from "../../utils/validate";
  import { getKey } from '@/utils/publicKey'

  export default {
    name: "emailChange",
    data() {
      return {
        originEmail: "",  // 原常用邮箱
        emailChangeForm: {
          newEmail: null,
          verifyCode: null,
          tradePwd: null
        },
        emailChangeRules: {
          newEmail: [
            {required: true, message: '请输入新常用邮箱', trigger: 'blur'},
            {validator: validateParams({type: 'Email', msg: '请输入正确的邮箱'}), trigger: 'blur'}
          ],
          verifyCode: [
            {required: true, message: '请输入邮箱验证码', trigger: 'blur'},
          ],
          tradePwd: [
            {required: true, message: '请输入支付密码', trigger: 'blur'},
          ],
        },
        sendCodeBtnWord: '发送邮箱验证码', // 发送验证码按钮的文案
        sendCodeAccesss: true, // 邮箱验证码按钮是否可点击，与 emailChangeForm.newEmail 共同判断
      };
    },
    mounted() {
      getEmail().then(({data}) => {
        this.originEmail = data;
      })
    },
    methods: {
      onSendCode() {
        changeEmailVerifyCode({newEmail: this.emailChangeForm.newEmail})
        .then(({data}) => {
          this.sendCodeAccesss = false;
          this.timer = setTimeout(() => {
            this.countDown(59)
          }, 1000)
        });
      },
      onSubmit() {
        this.$refs.emailChangeForm.validate(valid => {
          if (valid) {
            let form = {...this.emailChangeForm}
            form.tradePwd = encryptParam({
              pwd: form.tradePwd,
              publicKey: getKey(),
            })
            changeEmail(form).then(({code, data}) => {
              if (code === 20000) {
                this.$message.success(data);
                this.$router.push("/system/safe");
              }
            })
          }
        })
      },
      onCancel() {
        this.$router.push("/system/safe");
      },
      countDown(second) {
        clearTimeout(this.timer);
        if (second === 0) {
          this.sendCodeAccesss = true;
          this.sendCodeBtnWord = '发送邮箱验证码';
        } else {
          this.sendCodeBtnWord = second + '秒后重新发送';
          this.timer = setTimeout(() => {
            this.countDown(--second)
          }, 1000)
        }
      }
    }
  };
</script>

<style scoped lang="scss">
  .email-change-warpper {
    text-align: center;
    background: #ffffff;

    .email-change-form {
      width: 500px;
      box-sizing: content-box;
      padding: 50px;
      ::v-deep .el-form-item__content {
        text-align: left;
      }
      .el-form-item {
        margin-bottom: 24px;
      }

      .remark {
        font-size: 14px;
        color: #00000072
      }

      .submit-btn {
        width: 120px;
      }

      .cancel-btn {
        width: 120px;
      }
    }
  }
</style>
