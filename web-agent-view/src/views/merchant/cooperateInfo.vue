<template>
  <el-card>
    <div slot="header">合作信息</div>
      <div class="box-wrapper">
        <div class="flex-container">
          <div class="flex-wrapper search-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">商户编号</span>
              {{ cooperationInfo.mchNo }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">商户名称</span>
              {{ cooperationInfo.mchName }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">企业行业类别</span>
              {{ cooperationInfo.industryTypeName }}
            </div>
          </div>
          <div class="flex-wrapper search-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">预计用工人数</span>
              {{ cooperationInfo.workerNum }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">预期可实现C端签约率区间</span>
              {{ $dictCode('SignRateLevelEnum', cooperationInfo.signRateLevel).desc }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者单人月经营所得9.7万以下占比</span>
              {{ cooperationInfo.workerMonthIncomeRate }}
            </div>
          </div>
          <div class="flex-wrapper search-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">月流水预估</span>
              {{ cooperationInfo.monthMoneySlip }}万元
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">是否可提供服务记录及经营所得计算明细</span>
              {{ $dictCode('ProvideIncomeDetailTypeEnum', cooperationInfo.signRateLevel).desc }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">公司自有业务平台名称</span>
              {{ cooperationInfo.bizPlatformName }}
            </div>
          </div>
          <div class="flex-wrapper search-wrapper">
            <div class="flex-vertical-item flex-item-half"><span class="flex-vertical-label">公司网站</span>
              {{ cooperationInfo.companyWebsite }}
            </div>
          </div>
          <div class="flex-wrapper search-wrapper" v-for="(item, index) in cooperationInfo.positionVoList" :key="index">
            <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者的工作场所</span>
              {{ $dictCode('WorkPlaceEnum', item.workplaceCode).desc }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者服务类型</span>
              {{ item.workCategoryName }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">建议发票类目</span>
              <div class="invoice-category-list">
                <el-tooltip
                  v-for="(cate, key) in item.jsonEntity.invoiceCategoryList"
                  :key="key"
                  :content="cate.invoiceCategoryName"
                  :disabled="calculateDisAble(cate.invoiceCategoryName)">
                  <el-tag type="info" class="invoice-cate-item">
                    {{ cate.invoiceCategoryName }}
                  </el-tag>
                </el-tooltip>
              </div>
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者服务描述</span>
              {{ item.serviceDesc }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者服务所得计算规则</span>
              {{ item.chargeRuleDesc }}
            </div>
          </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item flex-vertical-main">
                <span class="flex-vertical-label">补充信息：</span>
                <div class="clearfix">
                  <span
                    class="flex-vertical-content flex-func-link"
                    v-for="(item, index) in cooperationInfo.supplementFileUrls"
                    :key="index"
                    v-preview="fileUrl + item"
                  >点击查看
                  </span>
                </div>
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item flex-vertical-main">
                <span class="flex-vertical-label">公司宣传文件：</span>
                <div>
                  <el-link
                    class="flex-vertical-content"
                    v-for="(item, index) in cooperationInfo.cooperateFileList"
                    :key="index"
                    :href="fileUrl + item"
                    type="primary"
                    target="_blank">点击查看</el-link>
                </div>
              </div>
            </div>
          <el-table class="content-container" :data="cooperationInfo.quoteVoList">
            <el-table-column label="id" prop="mainstayMchNo">

            </el-table-column>
            <el-table-column label="代征主体" prop="mainstayMchName">

            </el-table-column>
            <el-table-column label="商户费率" prop="rate">

            </el-table-column>
          </el-table>
        </div>
      </div>
  </el-card>
</template>

<script>
  import { getEmployerCooperate } from '@/api/merchant'
  export default {
    data() {
      return {
        cooperationInfo: {},
      }
    },
    mounted() {
      this.getEmployerCooperate();
    },
    methods: {
      async getEmployerCooperate() {
        const { data } = await getEmployerCooperate();
        this.cooperationInfo = data;
      },
      calculateDisAble(name) {
        return name.length <= 20
      }
    }

  }
</script>

<style lang="scss" scoped>
  .el-card {
    .invoice-category-list .el-tag{
      max-width: 90%;
      margin-right: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

</style>