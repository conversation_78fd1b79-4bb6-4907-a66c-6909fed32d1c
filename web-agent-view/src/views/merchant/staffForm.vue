<template>
  <div class="page-container">
    <el-form label-width="120px" :rules="rules" :model="form" ref="form" v-loading="load">
      <el-form-item label="员工所属商户：">
        <span>{{ agentName }}（{{ agentNo }}）</span>
      </el-form-item>
      <el-form-item label="账号：" prop="phone">
        <el-input :value="form.phone" @input="handleNumberInput(form, 'phone', $event)" :disabled="type == 'edit'">
          <span slot="prepend">+86</span>
        </el-input>
        <span class="form-tip color-gray">员工使用该手机号作为登录智享汇综合服务平台的账号</span>
      </el-form-item>
      <el-form-item label="员工姓名" prop="name">
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <el-form-item label="所属角色：" prop="roleIds">
        <el-select clearable v-model="form.roleIds" multiple>
          <el-option
            v-for="item in roleList" :key="item.roleName" :value="item.id"
            :label="item.roleName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="支付密码：" prop="tradePwd">
        <el-input v-model="form.tradePwd" type="password"></el-input>
      </el-form-item>

      <div class="footer-container">
        <el-button type="primary" @click="submit">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
  import { getStaffById, addStaff, editStaff } from "@/api/staff";
  import { getAllRoles } from "@/api/role";
  import { encryptParam } from "@/utils/jsencrypt";
  import store from '@/store';
  import { getKey } from '@/utils/publicKey'
  import { validateParams } from '@/utils/validate'

  export default {
    name: "staffForm",
    data() {
      return {
        agentNo: "",
        agentName: "",
        type: "create",
        load: false,
        rules: {
          phone: [
            {required: true, message: '请输入格式正确的手机号', trigger: 'blur'},
            {validator: validateParams({type: 'Phone', msg: '请输入格式正确的手机号'}), trigger: 'blur'}
          ],
          roleIds: [{required: true, message: '请选择所属角色', trigger: 'change'}],
          tradePwd: [{required: true, message: '请输入支付密码', trigger: 'blur'}],
          name: [{required: true, message: '请输入员工姓名', trigger: 'blur'}]
        },
        form: {
          id: null,
          phone: "",
          name: "",
          roleIds: [],
          tradePwd: ""
        },
        roleList: [],
      };
    },
    mounted() {
      this.agentNo = store.getters.userData.staff.agentNo;
      this.agentName = store.getters.userData.staff.agentName;

      this.type = this.$route.query.type;
      if (this.type == "edit") {
        this.load = true;

        getStaffById({id: this.$route.query.id}).then(({data}) => {
          this.form.id = data.staff.id;
          this.form.phone = data.staff.phone;
          this.form.name = data.staff.name;
          this.form.roleIds = data.roleIds;
          this.load = false;
        });
      }
      getAllRoles().then(({data}) => {
        this.roleList = data;
      });
    },
    methods: {
      submit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            let data = {...this.form};
            data.tradePwd = encryptParam({
              publicKey: getKey(),
              pwd: data.tradePwd
            });

            if (this.type == "create") {
              addStaff(data).then((response) => {
                this.$message.success("添加成功");
                this.$router.push(this.permissionToPath('pms:staff:view'));
              });
            } else {
              editStaff(data).then((response) => {
                this.$message.success("编辑成功");
                this.$router.push(this.permissionToPath('pms:staff:view'));
              });
            }
          }
        });
      },
      cancel() {
        this.$router.push(this.permissionToPath('pms:staff:view'));
      },
    },
  };
</script>

<style lang="scss" scoped>
  .page-container {

    .el-form {
      width: 500px;
      margin-left: 30px;

      ::v-deep .el-form-item__label {
        font-weight: normal;
      }
    }

    .footer-container {
      padding-left: 120px;
    }
  }
</style>
