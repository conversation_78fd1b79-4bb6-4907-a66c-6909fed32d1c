<template>
  <el-card v-loading="loading">
    <div slot="header">基本信息</div>
    <el-main>
      <div class="flex-container">
        <div class="flex-wrapper">
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">合伙人编号</div>
            <div>{{ merchantInfo.agentNo }}</div>
          </div>
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">合伙人名称</div>
            <div>{{ merchantInfo.agentName }}</div>
          </div>
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">合伙人类型</div>
            <div>{{ $dictCode('AgentTypeEnum', merchantInfo.agentType).desc }}</div>
          </div>
        </div>
        <div class="flex-wrapper">
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">用户状态</div>
            <div>{{ $dictCode('AgentStatusEnum', merchantInfo.agentStatus).desc }}</div>
          </div>
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">资质状态</div>
            <div>
              {{ $dictCode('AuthStatusEnum', merchantInfo.authStatus).desc }}
            </div>
          </div>
        </div>
        <div class="flex-wrapper">
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">创建时间</div>
            <div>{{ merchantInfo.createTime }}</div>
          </div>
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">激活时间</div>
            <div>{{ merchantInfo.activeTime }}</div>
          </div>
        </div>
        <div class="flex-wrapper">
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">负责人</div>
            <div>
              {{ merchantInfo.contactName}}({{ merchantInfo.contactPhone }})
              <router-link to="/safe/leaderChange" replace class="flex-func-link">更换</router-link>
            </div>
          </div>
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">常用邮箱</div>
            <div>{{ merchantInfo.contactEmail }}</div>
          </div>
        </div>
      </div>
    </el-main>
  </el-card>
</template>

<script>
import { getAgent } from '@/api/agent.js'
export default {
  name: 'merchantInfo',
  data() {
    return {
      loading: false,
      merchantInfo: {},
    }
  },
  async mounted() {
    await this.getMerchant()
  },
  methods: {
    async getMerchant() {
      this.loading = true;
      const { data } = await getAgent()
      this.merchantInfo = data;
      this.loading = false;
    },
    goAuth() {
      sessionStorage.setItem('mainInfo', JSON.stringify(this.merchantInfo));
      if (this.$dictCode('AuthStatusEnum', this.merchantInfo.authStatus).desc == '未认证') {
        this.$router.push(`/merchant/auth?mchNo=${this.merchantInfo.agentNo}`);
      }
      if (this.$dictCode('AuthStatusEnum', this.merchantInfo.authStatus).desc == '认证失败') {
        this.$router.push({
          path: '/merchant/auth',
          query: {
            mchNo: this.merchantInfo.agentNo,
            result: 'fail'
          }
        })
      }

    },
  }
}
</script>

<style>

</style>
