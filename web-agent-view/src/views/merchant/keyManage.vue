<template>
  <div class="box-container">
    <div
      class="flex-container"
      v-if="step == 1"
    >
      <div class="flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">商户编号：</span>
          <div class="flex-item__content">{{ userData.mchNo }}</div>
        </div>
      </div>
      <div class="flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">签名方式：</span>
          <div class="flex-item__content">RSA</div>
        </div>
      </div>
      <div class="flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">负责人手机号：</span>
          <div class="flex-item__content">{{ leaderData.phone | phoneEncrypt }} ({{ leaderData.name | nameEncrypt }})</div>
        </div>
      </div>
      <div class="flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">短信验证码：</span>
          <div class="flex-item__content">
            <el-input
              v-model="smsCode"
              @input="handleChange"
              ref="smsInput"
            >
              <template slot="append">
                <el-button
                  class="sms-btn"
                  :disabled="!leaderData.phone || counter !== null"
                  @click="sendCode"
                >
                  {{ btnContent }}
                </el-button>
              </template>
            </el-input>
          </div>
        </div>
      </div>
      <p class="flex-item__tip">验证短信将发送到负责人手机号</p>
    </div>
    <div class="form-wrapper" v-else>
      <el-form label-width="100px">
        <el-form-item label="签名方式：">{{ dialogData.signType || '' }}</el-form-item>
        <el-form-item
          label="商户签名公钥"
          class="form-item-inline"
        >
          <el-input
            type="textarea"
            rows="7"
            spellcheck="false"
            v-model="publicKey"
            :disabled="shouldEdit"
          >
          </el-input>
          <p
            class="func-link"
            @click="showDialog"
          >查看平台公钥</p>
        </el-form-item>
        <div class="form-btn-group">
          <el-button type="primary" @click="confirm">{{ confirmBtn }}</el-button>
          <el-button @click="cancel">取消</el-button>
        </div>
      </el-form>
    </div>

    <el-dialog
      width="500px"
      :title="dialogData.signType + '平台公钥'"
      :visible="visible"
      center
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <div class="dialog-body">
        <div class="tag-info">
          <el-tag>请谨记平台公钥，可以使用该公钥对下面的明文及密文进行验签校验</el-tag>
        </div>
        <div class="dialog-content">
          <div class="dialog-item">
            <label class="dialog-item-label">平台公钥：</label>
            <div class="dialog-item-content">{{ dialogData.platformPublicKey}}</div>
          </div>
          <div class="dialog-item">
            <label class="dialog-item-label">平台签名明文：</label>
            <div class="dialog-item-content">{{ dialogData.randomStr }}</div>
          </div>
          <div class="dialog-item">
            <label class="dialog-item-label">商户RSA签名：</label>
            <div class="dialog-item-content">{{ dialogData.randomStrSign }}</div>
          </div>
        </div>
      </div>
      <span slot="footer">
        <el-button type="primary" @click="() => {this.visible = false}">
          返回
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
  import { getLeader } from '@/api/safeSetting'
  import { sendSmsCode, getSecret, updateSecret } from '@/api/merchant'
import settingVue from '../common/setting.vue'
  export default {
    data() {
      return {
        leaderData: {},
        smsCode: '',
        counter: null,
        btnContent: '获取验证码',

        publicKey: '',

        step: 1,

        visible: false,
        dialogData: {
          merchantPublicKey: '123',
          platformPublicKey: '',
          randomStr: '',
          randomStrSign: '',
          signType: '',
        },

        shouldEdit: false,
      }
    },
    computed: {
      confirmBtn() {
        if (this.shouldEdit) {
          return '编辑'
        } else {
          return '提交商户公钥'
        }
      },
    },
    async mounted() {
      const { data: leaderData } = await getLeader();
      this.leaderData = leaderData;
    },
    methods: {
      async handleChange(val) {
        if (val.length >= 6) {
          await this.getSecret();
          this.step = 2;
        }
      },
      async sendCode() {
        const { data } = await sendSmsCode({
          phone: this.leaderData.phone,
          mchNo: this.userData.mchNo
        })
        this.$message.success(data);
        this.$refs.smsInput.focus();
        this.changeCount(59);
      },
      changeCount(second) {
        this.counter && clearTimeout(this.counter);
        if (second == 0) {
          this.btnContent = '获取验证码';
          this.counter = null;
        } else {
          this.btnContent = second + '秒后重新获取';
          this.counter = setTimeout(() => {
            this.changeCount(--second);
          }, 1000);
        }
      },
      async getSecret() {
        const { data } = await getSecret({
          phone: this.leaderData.phone,
          mchNo: this.userData.mchNo,
          code: this.smsCode
        });
        this.dialogData = data;
        this.publicKey = this.dialogData.merchantPublicKey || '';
        this.shouldEdit = !!this.dialogData.merchantPublicKey;
      },
      async confirm() {
        if (this.shouldEdit) {
          return this.shouldEdit = false;
        }
        if (!this.publicKey) {
          return this.$message.error('请输入商户签名公钥');
        }
        if (this.publicKey.includes('-----BEGIN PUBLIC KEY-----')
          || this.publicKey.includes('-----END PUBLIC KEY-----')
          // || /\s/g.test(this.employerKey) || /\r\n/g.test(this.employerKey)
        ) {
          return this.$message.error('请删除公钥中的 -----BEGIN PUBLIC KEY----- 和 -----END PUBLIC KEY-----')
        }
        const {code, data} = await updateSecret({
          publicKey: this.publicKey,
          mchNo: this.userData.mchNo,
        })
        if (code == 20000) {
          this.$message.success(data);
          this.shouldEdit = true;
          // this.$router.push('/merchant/info');
        }

      },
      cancel() {
        // this.$router.push('/merchant/info')
        this.shouldEdit = false;
        this.step = 1;
        this.publicKey = '';
      },
      showDialog() {
        this.visible = true;
      },
      handleClose() {
        this.visible = false;
      },
    },

  }
</script>

<style lang="scss" scoped>
  .box-container {
    padding-bottom: 32px;
    .flex-wrapper {
      margin-top: 32px;
    }
    .flex-item__label {
      width: 150px;
    }
    .flex-item__content {
      display: inline-block;
      position: relative;
      overflow-y: visible;
    }
    .flex-item__tip {
      padding-left: 150px;
      color: $fontGray;
    }
    .sms-btn {
      background: $mainColor;
      color: #fff;
    }

    .form-wrapper {
      padding: 16px;
    }
    .form-item-inline {
      width: 500px;
    }

    .form-btn-group .el-button {
      width: 130px;
    }

    .tag-info {
      text-align: center;
    }
    .dialog-item {
      display: flex;
      align-items: flex-start;
      margin-top: 32px;
    }
    .dialog-item-label {
      width: 120px;
    }
    .dialog-item-content {
      flex: 1;
    }
  }
</style>