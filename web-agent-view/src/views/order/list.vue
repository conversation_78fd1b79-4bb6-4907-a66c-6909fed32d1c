<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">平台流水号	：</span>
          <el-input v-model="searchForm.platTrxNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">商户编号：</span>
          <el-input v-model="searchForm.mchNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">商户名称：</span>
          <el-input v-model="searchForm.mchNameLike" placeholder="模糊查询"></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">产品：</span>
          <el-select
            v-model="searchForm.productNo"
            clearable
          >
            <el-option
              v-for="item in productList"
              :key="item.id"
              :label="item.productName"
              :value="item.productNo"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">奖励类型：</span>
          <el-select
            v-model="searchForm.rewardType"
            clearable
          >
            <el-option
              v-for="item in $dict('RewardTypeEnum')"
              :key="item.code"
              :label="item.desc"
              :value="item.code"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">合伙人编号：</span>
          <el-input v-model="searchForm.agentNo"></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">交易时间：</span>
          <date-picker
            ref="datepicker"
            type="datetimerange"
            :start-time.sync="searchForm.tradeTimeBegin"
            :end-time.sync="searchForm.tradeTimeEnd"
            fast-time="today"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询
        </el-button>
        <el-button @click="exportList">
          导出
        </el-button>
        <el-button type="text" @click="getExportList">
          查看已导出列表
        </el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件
        </el-button>
      </div>
    </div>
    <div class="func-container">
      <el-tag type="info">
        总实发金额 {{ countData.totalNetAmount || 0 }} 元, 合伙人总分润 {{ countData.totalProfit || 0 }} 元, 合伙人总成本 {{ countData.totalAgentCost || 0 }} 元，总笔数 {{ countData.totalCount }} 笔
      </el-tag>
    </div>
    <div class="content-container">
      <el-table :data="list">
        <el-table-column type="index" :index="getIndex" label="序号"></el-table-column>
        <el-table-column label="交易时间" prop="tradeTime" width="150"></el-table-column>
        <el-table-column label="交易流水号" prop="platTrxNo" width="180"></el-table-column>

        <el-table-column label="商户编号/名称" prop="mchNo" width="150">
          <template v-slot="{row}">
            {{ row.mchNo }}<br>
            {{ row.mchName }}
          </template>
        </el-table-column>
        <el-table-column label="合伙人编号/名称" prop="agentNo" width="150">
          <template v-slot="{row}">
            {{ row.agentNo }}<br>
            {{ row.agentName }}
          </template>
        </el-table-column>
        <el-table-column label="所属产品" prop="productNo" width="150">
          <template v-slot="{row}">
            {{ row.productNo }}<br>
            {{ row.productName }}
          </template>
        </el-table-column>
        <el-table-column label="产品供应商" prop="vendorName" width="150">
          <template v-slot="{row}">
            {{ row.vendorNo }}<br>
            {{ row.vendorName }}
          </template>
        </el-table-column>

        <el-table-column label="交易金额" prop="orderAmount">
          <template v-slot="{row}">
            {{ row.orderAmount | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column label="奖励类型" prop="rewardType">
          <template v-slot="{row}">
            {{ $dictCode('RewardTypeEnum',row.rewardType).desc }}
          </template>
        </el-table-column>

        <el-table-column label="合伙人收益" prop="agentProfit" width="150">
          <template v-slot="{row}">
            {{ row.agentProfit | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column label="费率基数" width="240" prop="calculateFormula">
        </el-table-column>

        <el-table-column label="订单类型">
          <template v-slot="{row}">
            <el-tag :type="row.orderType != 1 ? 'danger' : 'info'">
              {{ $dictCode('OrderTypeEnum', row.orderType).desc }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </el-footer>
    <ExportRecord ref="exportRecord"></ExportRecord>
  </div>
</template>

<script>
  import { getOrderList, exportList, getAgentProduct, countOrder } from '@/api/agent';
  import ExportRecord from '@/components/ExportRecord'

  import dayjs from 'dayjs'

  export default {
    components: {
      ExportRecord
    },
    data() {
      return {
        searchForm: {
          platTrxNo: '',
          mchNo: '',
          mchNameLike: '',
          productNo: '',
          rewardType: '',
          tradeTimeBegin: '',
          tradeTimeEnd: '',
          agentNo: ''
        },
        pageSize: 10,
        pageCurrent: 1,
        totalRecord: 0,

        list: [],
        countData: {},

        productList: []
      }
    },
    mounted() {
      getAgentProduct().then(res => {
        this.productList = res.data
      });
      if (this.$route.query.date) {
        let start = dayjs(this.$route.query.date).startOf('month').hour(0).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss');
        let end = dayjs(this.$route.query.date).endOf('month').hour(23).minute(59).second(59).format('YYYY-MM-DD HH:mm:ss');
        this.$refs.datepicker.changeTime([start, end])
      }
      this.search();
    },
    methods: {
      async search(init) {
        if (init) {
          this.pageCurrent = 1;
        }
        const [{data}, { data: countData }] = await Promise.all([
          getOrderList({
            ...this.searchForm,
            pageSize: this.pageSize,
            pageCurrent: this.pageCurrent,
          }),
          countOrder(this.searchForm),
        ])
        this.list = data.data;
        this.totalRecord = data.totalRecord;
        this.countData = countData;
      },
      resetForm() {
        this.searchForm = {
          platTrxNo: '',
          mchNo: '',
          mchNameLike: '',
          productNo: '',
          rewardType: '',
          tradeTimeBegin: '',
          tradeTimeEnd: '',
        };
        this.$refs.datepicker.resetTime();
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
      async exportList() {
        const { data } = await exportList(this.searchForm);
        this.$message.success(data);
      },
      getExportList() {
        this.$refs.exportRecord.isShow = true;
        this.$refs.exportRecord.getExportRecord('16');
      },
    },
  }
</script>

<style scoped lang="scss">

</style>
