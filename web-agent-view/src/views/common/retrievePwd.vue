<template>
  <el-container class="layout">
    <el-header class="layout-header">
      <div class="logo-container">
        <img src="@/assets/joinpay_logo.png" alt class="logo"/>
      </div>
    </el-header>

    <el-main class="layout-main">
      <el-form class="retrieve-pwd-form" ref="retrievePwdForm" :rules="rules" :model="retrievePwdForm">
        <div class="retrieve-pwd-title">{{ setPwd ? '设置密码' : '找回密码'}}</div>
        <el-form-item prop="phone">
          <el-input v-model="retrievePwdForm.phone" placeholder="注册时填写的手机号">
            <template slot="prepend">中国+86</template>
          </el-input>
        </el-form-item>
        <el-form-item prop="smsCode">
          <el-input v-model="retrievePwdForm.smsCode" placeholder="输入短信验证码">
            <template slot="append">
              <SmsCode :phone="retrievePwdForm.phone"></SmsCode>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="pwd">
          <el-input v-model="retrievePwdForm.pwd" placeholder="设置密码（8 - 16位密码，包含字母和数字，区分大小写）" type="password"></el-input>
        </el-form-item>
        <el-form-item v-if="setPwd" prop="newPwd">
          <el-input v-model="retrievePwdForm.newPwd" placeholder="请再次输入密码" type="password"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button class="submit-btn" type="primary" @click="onSubmit">确认修改</el-button>
        </el-form-item>
      </el-form>
    </el-main>
  </el-container>
</template>

<script>
  import { sendSmsCode, resetPwd, getUserPublickey } from "@/api/common";
  import { encryptParam } from "@/utils/jsencrypt";
  import SmsCode from "../../components/SmsCode/index";

  export default {
    name: "retrievePwd",
    components: {
      SmsCode
    },
    data() {
      const pwdValidate = (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入密码'))
        } else {
          if (value.length < 8 || value.length > 16 || !/\d/g.test(value) || !/[a-zA-Z]/g.test(value)) {
            callback(new Error('请输入符合规范的密码（8 - 16位长度，包含字母和数字，区分大小写）'))
          } else {
            callback()
          }
        }
      };
      const newPwdConfirm = (rule, value, callback) => {
        this.retrievePwdForm.newPwd == this.retrievePwdForm.pwd ? callback() : callback(new Error('两次输入密码不一致，请重新输入'));
      };
      return {

        retrievePwdForm: {
          phone: "",
          pwd: "",
          smsCode: "",
          newPwd: '',
        },
        rules: {
          phone: [
            {required: true, message: '请输入手机号', trigger: 'blur'},
          ],
          smsCode: [
            {required: true, message: '请输入验证码', trigger: 'blur'},
          ],
          pwd: [
            {validator: pwdValidate, trigger: 'blur', message: '请输入密码'}
          ],
          newPwd: [
            {required: true, message: '请确认新登录密码', trigger: 'blur'},
            {validator: newPwdConfirm, trigger: 'blur'}
          ]
        },
        setPwd: false,
      };
    },
    mounted() {
      if (sessionStorage.getItem('phone')) {
        this.retrievePwdForm.phone = sessionStorage.getItem('phone');
        this.setPwd = true;
      }
    },
    beforeDestroy() {
      sessionStorage.removeItem('phone');
    },
    methods: {
      onSendSms() {
        sendSmsCode({phone: this.retrievePwdForm.phone}).then((data) => {

        });
      },
      onSubmit() {
        this.$refs['retrievePwdForm'].validate(async (valid) => {
          if (valid) {
            let form = {...this.retrievePwdForm};
            const {data: {publicKey}} = await getUserPublickey({
              phone: form.phone
            })
            delete form.newPwd;
            form.pwd = encryptParam({
              pwd: form.pwd,
              publicKey
            });  // 密码RSA加密
            const {data} = await resetPwd(form)
            this.$message.success(data);
            this.$router.push("/login");
          }
        })
      }
    },
  };
</script>

<style scoped lang="scss">
  .layout {
    width: 100%;
    text-align: center;

    .layout-header {
      display: flex;
      align-items: center;

      .logo-container .logo {
        width: 150px;
        padding: 15px 15px 15px 15px;
        box-sizing: border-box;
      }
    }

    .layout-main {
      align-self: center;
    }
  }

  .retrieve-pwd-title {
    font-weight: 700;
    font-style: normal;
    font-size: 24px;
    color: #000000;
    text-align: left;
    line-height: 24px;

    padding: 0px 0px 20px 0px;
  }

  .retrieve-pwd-form {
    width: 400px;

    .submit-btn {
      width: 100%;
      height: 40px;
    }
  }

</style>
