<template>
  <el-container class="layout">
    <div class="layout-container">
      <el-header class="layout-header">
        <div class="layout-header-left">
          <div class="logo-container">
            <img src="@/assets/logo.png" class="logo"/>
          </div>
          <div>
            <span>选择合伙人</span>
          </div>
        </div>
        <div class="layout-header-right">
          <span>{{ userData && userData.operator.name }} </span>
          <el-button type="text" @click="onLogout()">退出</el-button>
        </div>
      </el-header>

      <div class="layout-main">
        <div class="user-info-wrapper">
          <div class="user-info-container">
            <svg-icon icon-class="avatar"></svg-icon>
            <div class="user-info-block">
              <div class="user-info-name">{{ userData.operator.name }}</div>
              <div class="user-info-phone">
                {{ userData.operator.phone }}
                <span class="setting-btn" type="text" @click="goAccountManagemnet">设置</span>
              </div>
            </div>
          </div>
        </div>
        <div class="select-mch-wrapper clearfix">
          <div class="clearfix">
            <el-input class="mch-input" v-model="mchInput" clearable placeholder="搜索合伙人" @change="searchMch"></el-input>
          </div>
          <div
            v-for="mch in mchList" :key="mch.agentNo" class="mch-info"
            :class="mchAuthStatusDict[mch.authStatus] == '已认证' ? 'active' : ''" @click="selectMch(mch)"
            :style="mchInfoStyle(mch.agentStatus)">
            <div style="text-overflow: ellipsis; white-space: nowrap; overflow: hidden;">
              <el-tooltip :content="mch.agentName" :disabled="mch.agentName.length < 15">
                <strong><span>{{mch.agentName}}</span></strong>
              </el-tooltip>
            </div>
            <div><span>合伙人编号：{{mch.agentNo}}</span></div>
            <div><span>合伙人状态：{{$dictCode('AgentStatusEnum', [mch.agentStatus]).desc }}</span></div>
            <div><span>资质状态：{{mchAuthStatusDict[mch.authStatus]}}</span></div>
          </div>
        </div>

        <div class="pagination-container">
          <el-pagination
            :current-page.sync="pageCurrent"
            :total="pageTotal"
            :page-size="pageSize"
            layout="total,prev,pager,next,jumper"
            @current-change="handleCurrChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </el-container>
</template>

<script>
  import {mapGetters} from "vuex"
  import {removeToken} from "@/utils/loginToken"
  import {getMchList, selectMch} from "@/api/common"
  import store from "@/store"
  import {addRoutersAndBuildMenu} from "@/permission"
  import { removeKey } from "@/utils/publicKey"

  export default {
    name: "SelectMch",
    computed: {
      ...mapGetters(["userData"]),
    },
    data() {
      return {
        mchStatusDict: {
          100: "已激活",
          101: "已冻结",
          102: "已创建",
        },
        mchAuthStatusDict: {
          100: "已认证",
          101: "认证失败",
          102: "未认证",
          103: "认证审核中"
        },
        mchList: [],

        mchOriginList: [], // 原份保留

        mchInput: '',

        pageCurrent: 1,

        pageSize: 6,
        pageTotal: 0,
      }
    },
    mounted() {
      getMchList().then(({data}) => {
        this.mchOriginList = [...data];
        this.mchList = data.slice(0, this.pageSize * this.pageCurrent);
        this.pageTotal = this.mchOriginList.length;
      })
    },
    methods: {
      mchInfoStyle(mchStatus) {
        return "cursor: " + (mchStatus === 101 ? "not-allowed" : "pointer") + ";";
      },
      onLogout() {
        removeToken();
        removeKey();
        this.$router.push({path: "/login"});
      },
      goAccountManagemnet() {
        this.$router.push('/accountManagement');
      },
      selectMch(mch) {
        // 非冻结状态才可以选择，冻结状态：101
        if (mch.agentStatus !== 101) {
          selectMch({agentNo: mch.agentNo, agentType: mch.agentType}).then((data) => {
            if (data.code === 20000) {
              // 请求成功，返回员工信息和权限
              let userData = JSON.parse(JSON.stringify(store.getters.userData));
              userData.agentNo = mch.agentNo;
              userData.agentName = mch.agentName;
              userData.agentType = mch.agentType;
              userData.contactPhone = mch.contactPhone;
              userData.contactName = mch.contactName;
              userData.staff = data.data.staff;
              userData.functions = data.data.functions;
              store.dispatch("setUserData", userData);

              addRoutersAndBuildMenu(userData.functions);
              this.$router.push({path: "/"});
            }
          }).catch((err) => {
          });
        }
      },
      searchMch() {
        this.mchList = this.mchOriginList.filter(mch => {
          return mch.agentName.includes(this.mchInput);
        })
        this.pageTotal = this.mchList.length;
        this.mchList = this.mchList.slice(0, 6);
      },
      handleCurrChange(val) {
        this.mchList = this.mchOriginList.slice((val - 1) * this.pageSize, val * this.pageSize)
      }
    }
  }
</script>

<style scoped lang="scss">
  .layout {
    position: relative;
    width: 100%;
    height: 100vh;
    min-width: 1200px;
    text-align: center;

    .layout-container {
      width: 900px;
      margin: 40px auto 0;
    }

    .el-header {
      padding: 0;
    }
    .layout-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .layout-header-left {
        display: flex;
        justify-self: start;
        align-items: center;

        .logo {
          width: 30px;
          height: 30px;
          margin-right: 32px;
        }
      }

      .layout-header-right {
        display: flex;
        justify-self: start;
        align-items: center;
      }
    }

    .layout-main {
      padding: 8px;
      background: #eee;
    }


    .user-info-wrapper {
      display: flex;
    }
    .user-info-container {
      display: flex;
      justify-self: start;
      align-items: center;
      text-align: left;
    }
    .user-info-block {
      margin-left: 12px;

      .user-info-name {
        font-weight: bold;
      }
    }
    .svg-icon {
      width: 50px;
      height: 50px;
    }
    .setting-btn {
      margin-left: 12px;
      cursor: pointer;
    }

    .select-mch-wrapper {
      margin-top: 12px;
      padding-bottom: 32px;
      background: #fff;
      text-align: center;

      .mch-info {
        float: left;
        width: 30.5%;
        border: 1px solid #ccc;
        border-top: 4px solid #ccc;
        margin: 24px 12px 0;
        padding: 16px;
        line-height: 1.5;
        text-align: left;
        transition: all linear .1s;

        &.active {
          border-top: 4px solid #20a0ff;
        }
        &:hover {
          box-shadow: 2px 2px 4px #eee;
          transform: translateY(-5px);
        }
      }
    }

    .mch-input {
      float: right;
      width: 240px;
      margin: 12px 12px 0 0;
    }
  }

</style>
