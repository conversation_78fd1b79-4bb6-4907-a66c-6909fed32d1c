<template>
  <el-container class="layout">
    <el-header class="layout-header">
      <div class="layout-header-left">
        <div class="logo-container">
          <img src="@/assets/joinpay_logo.png" alt class="logo"/>
        </div>
      </div>
      <div class="layout-header-right">
        <span>{{ userData && userData.operator.name }} </span>
        <el-button type="text" @click="onLogout()">退出</el-button>
      </div>
    </el-header>

    <el-main class="layout-main">
      <div class="item">
        <span class="text">登录账号</span>
        <span>{{ userData && userData.operator.phone }} </span>
        <el-button type="text" @click="$router.push('/changePwd')">修改密码</el-button>
      </div>
      <div class="item">
        <span class="text">姓名</span>
        <el-input v-model="name" maxlength="20"></el-input>
      </div>
      <div class="item">
        <span class="text">头像</span>
        <svg-icon icon-class="avatar"></svg-icon>
      </div>
      <div class="item">
        <span class="text">账号状态</span>
        <span>{{ userData && statusEnum[userData.operator.status] }} </span>
      </div>
      <div class="item">
        <span class="text">创建时间</span>
        <span>{{ userData && userData.operator.createTime }} </span>
      </div>
      <div class="btn-group">
        <el-button type="primary" @click="updateInfo">确定</el-button>
        <el-button @click="goBack">返回商户</el-button>
      </div>
    </el-main>
  </el-container>
</template>

<script>
  import { mapGetters } from "vuex"
  import { updateUserInfo, getUserInfo } from "../../api/common";
  import { removeToken, getToken } from "../../utils/loginToken";
  import { removeKey } from "@/utils/publicKey"
  export default {
    name: "accountManagement",
    computed: {
      ...mapGetters(["userData"]),
      baseUrl() {
        return process.env.VUE_APP_BASE_API;
      },
      uploadHeader() {
        return {
          'X-Token': getToken()
        }
      },
    },
    data() {
      return {
        statusEnum: {
          '1': '激活',
          '-1': '冻结'
        },
        name: '',
        fileList: []
      }
    },
    mounted() {
      this.name = this.userData.operator.name;
    },
    methods: {
      onLogout() {
        removeToken();
        removeKey();
        this.$router.push({path: "/login"});
      },
      updateInfo() {
        let form = {
          name: this.name
        }
        if (this.fileList.length > 0) {
          form.headPortraitFileUrl = this.fileList[0].response.data
        }
        updateUserInfo(form).then(res => {
          this.$message.success(res.data);
          this.$store.commit('SET_USER_NAME', this.name);
          this.$store.commit('SET_USER_AVATAR', form.headPortraitFileUrl);
          this.fileList = [];
        })
      },
      beforeAvatarUpload(file) {
        const isTrueType = ['image/jpeg','image/png','image/bmp','image/gif'].includes(file.type);
        const isLt6M = file.size / 1024 / 1024 < 6;

        if (!isTrueType) {
          this.$message.error('上传图片只能是 bmp、png、jpeg、jpg或gif 格式!');
        }
        if (!isLt6M) {
          this.$message.error('上传图片大小不能超过 6MB!');
        }
        return isTrueType && isLt6M;
      },
      handleRemove(file, fileList, param) {
        this[param] = fileList;
      },
      handleSuccess(response, file, fileList, param) {
        this[param] = fileList;
        this.$store.commit('SET_USER_AVATAR', fileList[0].response.data);
      },
      goBack() {
        this.$router.back();
      }
    }
  }
</script>

<style lang="scss" scoped>
  .layout {
    width: 100%;
    background: $lightGray;

    .layout-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .layout-header-left {
        display: flex;
        justify-self: start;
        align-items: center;

        .logo-container .logo {
          width: 150px;
          padding: 15px 15px 15px 15px;
          box-sizing: border-box;
        }
      }

      .layout-header-right {
        display: flex;
        justify-self: start;
        align-items: center;
      }
    }

    .layout-main {
      width: 500px;
      margin: 0 auto;
      .item {
        margin-bottom: 10px;
        line-height: 1.5;
        .el-input {
          width: 200px;
        }
      }
      .text {
        display: inline-block;
        width: 120px;
        margin-right: 50px;
        text-align: right;
      }
      .btn-group {
        margin-top: 30px;
        text-align: center;
      }
      .svg-icon {
        width: 50px;
        height: 50px;
        vertical-align: middle;
      }
    }
    .user-avatar {
      width: 150px;
      height: 150px;
    }
    .upload-demo {
      display: inline-block;
      margin-left: 10px;
      text-align: center;
    }
  }
</style>
