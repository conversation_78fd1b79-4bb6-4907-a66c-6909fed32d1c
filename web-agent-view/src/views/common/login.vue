<template>
  <el-container class="layout">
    <el-header class="layout-header">
      <div class="logo-container">
        <img
          src="@/assets/joinpay_logo.png"
          alt
          class="logo"
        />
      </div>
    </el-header>

    <el-main class="layout-container">
      <div class="layout-main">
        <div class="login-container">
          <div class="logn-type-select">
            <span
              class="login-type"
              :class="{'active': loginType == '0'}"
              @click="toggleType"
            >密码登录</span>
            <span
              class="login-type"
              :class="{'active': loginType == '1'}"
              @click="toggleType"
            >验证码登录</span>
          </div>

          <el-form
            v-show="loginType == '0'"
            class="login-form"
            ref="pwdLoginForm"
            :model="pwdLoginForm"
            :rules="rules">
            <el-form-item prop="phone">
              <el-input
                class="login-input-phone"
                v-model.trim="pwdLoginForm.phone"
                placeholder="注册时填写的手机号"
                @blur="checkAccount(pwdLoginForm.phone)"
              >
                <template slot="prepend">中国 +86</template>
              </el-input>
              <div class="errText" @click="clickError" :class="{'no-pwd': noPwd && !shouldRegister}" v-html="errText"></div>
            </el-form-item>
            <el-form-item prop="pwd">
              <el-input
                class="login-input-pwd"
                v-model.trim="pwdLoginForm.pwd"
                placeholder="输入登录密码"
                type="password"
                show-password
                maxlength="16"
              ></el-input>
            </el-form-item>
            <el-form-item class="captcha-item" prop="captcha">
              <el-input
                class="captcha-input"
                v-model="pwdLoginForm.captcha"
                placeholder="输入图片验证码"
                @keyup.enter.native="onLogin"
              >
                <template slot="append">
                  <div
                    class="captcha-container"
                    @click="refreshCaptcha"
                  >
                    <img
                      :src="captchaSrc"
                      class="captcha"
                    />
                  </div>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                class="submit-btn"
                type="primary"
                @click="onLogin"
              >登录</el-button>
            </el-form-item>
          </el-form>

          <el-form
            v-show="loginType == '1'"
            class="login-form"
            ref="smsLoginForm"
            :model="smsLoginForm"
            :rules="rules">
            <el-form-item prop="phone">
              <el-input
                class="login-input-phone"
                v-model.trim="smsLoginForm.phone"
                placeholder="注册时填写的手机号"
                @blur="checkAccount(smsLoginForm.phone)"
              >
                <template slot="prepend">中国 +86</template>
              </el-input>
              <div class="errText" @click="clickError" :class="{'no-pwd': noPwd && !shouldRegister}" v-html="errText"></div>
            </el-form-item>
            <el-form-item prop="smsCode">
              <el-input
                class="login-input-pwd"
                v-model="smsLoginForm.smsCode"
                placeholder="输入短信验证码"
                maxlength="16"
              >
                <template slot="append">
                  <SmsCode :phone="smsLoginForm.phone" :disable="noPwd"></SmsCode>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item class="captcha-item" prop="captcha">
              <el-input
                class="captcha-input"
                v-model="smsLoginForm.captcha"
                placeholder="输入图片验证码"
                @keyup.enter.native="onLogin"
              >
                <template slot="append">
                  <div
                    class="captcha-container"
                    @click="refreshCaptcha"
                  >
                    <img
                      :src="captchaSrc"
                      class="captcha"
                    />
                  </div>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                class="submit-btn"
                type="primary"
                @click="onLogin"
              >登录</el-button>
            </el-form-item>
          </el-form>

          <div class="login-footer clearfix">
            <router-link to="/retrievePwd" class="func-link fl">忘记密码</router-link>
            <span class="fr">还没有账号？请联系客服</span>
          </div>
        </div>
        <div class="login-pic">
          <img src="@/assets/login_pic.jpg" alt="login">
        </div>
      </div>
    </el-main>
    <p class="login-web-isp">&copy; 2020 - 2021 Hjzxh.com 粤ICP备**********号</p>
  </el-container>
</template>

<script>
  import { getCaptcha, sendSmsCode, login, getUserPublickey } from "@/api/common";
  import { encryptParam } from "@/utils/jsencrypt";
  import { setToken } from "@/utils/loginToken"
  import { setKey } from "@/utils/publicKey"
  import SmsCode from "../../components/SmsCode/index";
  import { getAccountStatus } from "../../api/common";
  import { validateParams } from "../../utils/validate";

  export default {
    name: "login",
    components: {
      SmsCode
    },
    data() {
      return {
        loginType: "0",
        captchaSrc: "",
        errText: '',
        pwdLoginForm: {
          phone: "",
          pwd: "",
          captchaId: "",
          captcha: "",
        },
        smsLoginForm: {
          phone: "",
          smsCode: "",
          captchaId: "",
          captcha: "",
        },
        rules: {
          phone: [
            // { required: true, trigger: 'blur', message: '请输入帐号'},
          ],
          pwd: [
            { required: true, trigger: 'blur', message: '请输入密码'},
          ],
          smsCode: [
            { required: true, trigger: 'blur', message: '请输入验证码'},
          ],
          captcha: [
            { required: true, trigger: 'blur', message: '请输入图片验证码'},
          ]
        },
        noPwd: false,
        shouldRegister: false,
      };
    },
    mounted() {
      this.refreshCaptcha();
    },
    methods: {
      refreshCaptcha() {  // 刷新图片验证码
        getCaptcha().then(({ data }) => {
          this.captchaSrc = "data:image/png;base64, " + data.img;

          if (this.loginType === "0") {
            this.pwdLoginForm.captchaId = data.id;
            this.pwdLoginForm.captcha = '';
          } else if (this.loginType === "1") {
            this.smsLoginForm.captchaId = data.id;
            this.smsLoginForm.captcha = '';
          }
        });
      },
      toggleType() {
        this.$refs.pwdLoginForm.resetFields();
        this.$refs.smsLoginForm.resetFields();
        this.errText = '';
        this.loginType = this.loginType === '0' ? '1' : '0';
        this.refreshCaptcha();
      },
      async checkAccount(phone) {
        this.errText = '';
        if (!/^[1][0-9]{10}$/.test(phone)) {
          this.errText = '请填写正确的手机号';
          return;
        }
        this.noPwd = false;
        const res = await getAccountStatus({
          phone
        })
        let data = res.data;
        if (!data.register) {
          this.errText = '该手机号未注册，请先注册';
          this.noPwd = true;
          this.shouldRegister = true;
        } else if (!data.initPwd) {
          this.errText = '账号未设置密码，<span class="func-link">请点击设置登录密码</span>';
          this.noPwd = true;
        } else if (data.status == '-1') {
          this.errText = '账号被冻结，请联系管理员';
        }
      },
      async onLogin() {
        let loginData;
        let publicKey;
        if (this.loginType === "0") {
          await this.checkAccount(this.pwdLoginForm.phone);
          this.$refs.pwdLoginForm.validate(async (valid) => {
            if (valid) {
              loginData = { ...this.pwdLoginForm };
              const { data } = await getUserPublickey({
                phone: loginData.phone
              });
              publicKey = data.publicKey;
              loginData.pwd = encryptParam({
                publicKey,
                pwd: loginData.pwd
              });  // 密码RSA加密
              if (loginData) {
                loginData.loginType = this.loginType;
                this.login(loginData, publicKey);
              }
            }
          })
        } else {
          await this.checkAccount(this.smsLoginForm.phone);
          this.$refs.smsLoginForm.validate(async (valid) => {
            if (valid) {
              loginData = { ...this.smsLoginForm };
              const { data } = await getUserPublickey({
                phone: loginData.phone
              });
              publicKey = data.publicKey;
              loginData.smsCode = encryptParam({
                publicKey,
                pwd: loginData.smsCode
              });
              if (loginData) {
                loginData.loginType = this.loginType;
                this.login(loginData, publicKey);
              }
            }
          })
        }
      },
      async login(loginData, publicKey) {
        login(loginData)
        .then(({data}) => {
          setToken(data.token);
          setKey(publicKey);
          this.$router.push({ path: "/selectMch" });
        })
        .catch(() => {
          this.refreshCaptcha();
        })
      },
      clickError() {
        if (this.noPwd && !this.shouldRegister) {
          const phone = this.pwdLoginForm.phone || this.smsLoginForm.phone;
          sessionStorage.setItem('phone', phone);
          this.$router.push('/retrievePwd');
        }
      },
    },
  };
</script>

<style scoped lang="scss">
  .layout {
    position: relative;
    width: 100%;
    min-height: 100vh;
    text-align: center;
    background: #f2f3f5;

    .layout-header {
      display: flex;
      align-items: center;
      padding-top: 16px;

      .logo-container .logo {
        height: 50px;
        box-sizing: border-box;
      }
    }

    .layout-main {
      text-align: center;
      white-space: nowrap;
    }
    .login-pic {
      display: inline-block;
      width: 325px;
      margin-left: 16px;
      vertical-align: middle;

      img {
        vertical-align: bottom;
      }
    }

    .login-web-isp {
      position: fixed;
      bottom: 8px;
      left: 50%;
      transform: translateX(-50%);
      color: #999;
      white-space: nowrap;
    }

    .login-container {
      display: inline-block;
      width: 520px;
      padding: 50px 56px 60px;
      background: #fff;
      vertical-align: middle;
    }
    .login-form {
      margin-top: 24px;
      .el-input-group__append {
        padding: 0px 0px;
      }
      .captcha-container {
        width: 95px;
        height: 38px;
        cursor: pointer;

        .captcha {
          height: 38px;
        }
      }
      .captcha-item >>> .el-input-group__append {
        padding: 0;
      }
      .errText {
        position: absolute;
        top: 45px;
        right: 0;
        font-size: 12px;
        line-height: 1;
        color: #ff0000;
        &.no-pwd {
          cursor: pointer;
        }
      }
      .submit-btn {
        width: 100%;
        height: 40px;
      }
      .el-button--primary.is-disabled,
      .el-button--primary.is-disabled:active,
      .el-button--primary.is-disabled:focus,
      .el-button--primary.is-disabled:hover {
        background: transparent;
        color: #C0C4CC;
        border-color: transparent transparent #EBEEF5;
      }
    }

    ::v-deep .el-input {
      .el-input-group__prepend {
        background: transparent;
        margin: 0 10px;
        padding: 0 10px;
      }
      .el-input-group__prepend {
        border: transparent;
      }
      .el-input__inner {
        padding: 0;
        border-color: transparent transparent #dcdee0;

        &:focus {
          border-bottom-color: $mainColor;
        }
      }
      .el-input-group__append {
        background: transparent;
        border-top-color: transparent;
        border-right-color: transparent;
      }
    }

    .login-input-phone ::v-deep.el-input__inner {
      width: 313px;
      margin-left: 20px;
    }
    .captcha-input ::v-deep.el-input__inner {
      width: 280px;
      margin-right: 30px;
    }

    .login-form-footer {
      display: flex;
      justify-content: space-between;
    }

    .logn-type-select {
      text-align: left;

      .login-type {
        margin-right: 24px;
        font-size: 20px;
        cursor: pointer;
        color: $fontGray;

        &.active {
          font-weight: 500;
          font-size: 26px;
          color: #323233;
        }
      }
    }
  }
</style>
