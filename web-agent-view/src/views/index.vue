<template>
  <el-main>
    <!-- <div class="total-amount-block">
      <p>总可用金额（元）</p>
      <div class="total-amount-num">{{ totalAmount }}</div>
    </div>
    <div
      class="box-container"
      v-for="(item, index) in mainstayList"
      :key="index">
      <div>
        <p class="mainstay-name">
          {{ item.mainstayName }}
        </p>
        <div class="flex-container">
          <div class="flex-wrapper">
            <div class="flex-vertical-item"
              v-for="(amount, key) in item.amount"
              :key="key">
              <p class="flex-amount-title" :class="'flex-title-' + key">
                {{ $dictFlag('ChannelTypeEnum', key).desc }}通道 | 可用金额（元）
              </p>
              <p class="flex-amount-num">
                {{ amount | numberFilter }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div> -->
  </el-main>
</template>

<script>
  export default {
    data() {
      return {
        mainstayList: [],
      }
    },
    computed: {
      totalAmount() {
        let total = 0;
        for (let i = 0; i < this.mainstayList.length; i++) {
          if (this.mainstayList[i].amount) {
            for (let p in this.mainstayList[i].amount) {
              let num = parseFloat(this.mainstayList[i]['amount'][p]) * 100;
              if (isNaN(num)) {
                num = 0
              }
              total += num
            }
          }
        }
        total /= 100;
        return total;
      }
    },
    filters: {
      numberFilter(val) {
        if (isNaN(val)) {
          return '-'
        } else {
          return val;
        }
      }
    },
    async mounted() {
    },
    methods: {
      async getMainstayList() {
        const { data } = await getMainstayList();
        this.mainstayList = data;
      },
      async getAmount() {
        let promise = [];
        for (let i = 0; i < this.mainstayList.length; i++) {
          promise.push(getAmount({
            mainstayNo: this.mainstayList[i].mainstayNo
          }))
        };
        const response = await Promise.all(promise);
        response.forEach((item, index) => {
          this.$set(this.mainstayList[index], 'amount', item.data);
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .el-main {
    .box-container {
      box-shadow: none;
      border: none;
      padding: 8px 0;
      .flex-wrapper {
        justify-content: space-between;
        padding-left: 32px;
        padding-right: 32px;
      }
      .flex-vertical-item {
        margin-top: 16px;
      }
      .mainstay-name {
        margin-bottom: 0px;
        padding-left: 32px;
        padding-bottom: 8px;
        border-bottom: 1px solid #EBEDF0;
        font-size: 20px;
      }
      .flex-amount-title {
        margin-bottom: 4px;
      }
      .flex-title-BANK {
        color: orange;
      }
      .flex-title-ALIPAY {
        color: $mainColor;
      }
      .flex-title-WENXIN {
        color: $successColor;
      }
      .flex-amount-num {
        margin-top: 0px;
        font-size: 35px;
      }
    }

    .total-amount-block {
      margin-left: 16px;
      margin-bottom: 8px;

      .total-amount-num {
        font-size: 25px;
      }
    }
  }
</style>