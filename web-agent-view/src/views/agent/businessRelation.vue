<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">合伙人编号：</span>
          <el-input
            v-model="searchForm.agentNo"
            disabled
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">合伙人名称：</span>
          <el-input
            v-model="searchForm.agentName"
            disabled
          ></el-input>
        </div>
      </div>
    </div>

    <div class="func-container" v-if="type == 0">
      <el-radio-group
        v-model="searchForm.relationType"
        @change="search(true)">
        <el-radio-button
          v-for="item in $dict('RelationTypeEnum')"
          :key="item.code"
          :label="item.code"
        >
          {{ item.desc }}
        </el-radio-button>
      </el-radio-group>
    </div>

    <div class="content-container">
      <el-table :data="list">
        <el-table-column
          label="序号"
          type="index"
          :index="getIndex"
        ></el-table-column>
        <el-table-column
          label="商户编号"
          prop="mchNo"
        ></el-table-column>
        <el-table-column
          label="商户名称"
          prop="mchName"
        ></el-table-column>
        <el-table-column
          label="所属合伙人"
          prop="mchNo"
        >
          <template v-slot="{row}">
            {{ row.agentName }}<br>
            {{ row.agentNo }}
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          prop="createTime"
        ></el-table-column>
        <el-table-column
          label="操作" fixed="right"
        >
          <template v-slot="{row}">
            <el-button type="text" @click="goDetail(row)">查看报价明细</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        background
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </el-footer>

    <el-dialog
      :title="diaLogTitle"
      :visible="showDetail"
      :before-close="() => showDetail = false"
      :close-on-click-modal="false"
      width="1200px"
    >
      <MchQuoteInfo :info="quoteInfo"></MchQuoteInfo>
    </el-dialog>
  </div>
</template>

<script>
  import { getBusinessRelation, getSelfBusinessRelation, getMerchantQuoteInfo } from '@/api/agent'
  import MchQuoteInfo from "@/views/agent/MchQuoteInfo.vue";

  export default {
    components: {
      MchQuoteInfo
    },
    computed: {
      type() {
        return this.$route.query.type || 0;
      }
    },
    data() {
      return {
        searchForm: {
          relationType: '100',
          agentNo: '',
          agentName: '',
        },
        pageSize: 10,
        pageCurrent: 1,

        list: [],
        totalRecord: 0,

        showDetail: false,
        diaLogTitle: '',
        quoteInfo: null
      }
    },
    mounted() {
      if (this.type == 1) {
        this.searchForm.agentNo = this.$route.query.agentNo;
        this.searchForm.agentName = this.$route.query.agentName;
      } else {
        this.searchForm.agentNo = this.$store.getters.userData.agentNo;
        this.searchForm.agentName = this.$store.getters.userData.agentName;
      }
      this.search();
    },
    methods: {
      async search(init) {
        if (init) {
          this.pageCurrent = 1
        }
        const api = this.type == 0 ? getSelfBusinessRelation : getBusinessRelation
        const {data} = await api({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent,
        })
        this.list = data.data;
        this.totalRecord = data.totalRecord;
      },
      goToMerchant() {
        this.$router.push("/merchant/merchantManagement/merchant")
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1
      },
      async goDetail(param) {
        const { data } = await getMerchantQuoteInfo(param.mchNo);

        this.showDetail = true;
        this.diaLogTitle = param.mchName;
        this.quoteInfo = data;
      }
    }
  }
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  max-height: 550px;
  overflow: auto;
}
</style>
