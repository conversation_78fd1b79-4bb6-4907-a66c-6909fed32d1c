<template>
  <div class="merchant-page-container">
    <div class="header-container" v-sticky="{ zIndex: 100, stickyTop: 0 }">
      <el-steps v-if="!isEdit" :active="activeStep" align-center class="el-steps-container">
        <el-step title="填写合作信息"></el-step>
        <el-step title="审核与公示"></el-step>
      </el-steps>
      <el-steps v-else :active="activeStep" align-center>
        <el-step title="填写合作信息"></el-step>
      </el-steps>
    </div>
    <div class="page-container">
      <div class="content-container">
        <!--step_1-->
        <el-form ref="form_1" :model="form_1" :rules="rules" label-width="400px" class="step_1" v-show="activeStep == 1">
          <!--企业信息-->
          <div class="company_info">
            <p class="subTitle">企业信息</p>
            <div>
              <el-form-item label="企业名称：" prop="mchName">
                <el-input v-model="form_1.mchName" maxlength="100"></el-input>
              </el-form-item>
              <el-form-item label="商户负责人姓名：" prop="contactName">
                <el-input v-model="form_1.contactName" :disabled="!editAccess" maxlength="10"></el-input>
                <p class="color-gray form-tip">请填写公司运营负责人</p>
              </el-form-item>
              <el-form-item label="商户负责人邮箱：" prop="contactEmail">
                <el-input clearable v-model="form_1.contactEmail" :disabled="!editAccess"></el-input>
              </el-form-item>
              <el-form-item label="商户负责人手机号：" prop="contactPhone">
                <el-input v-model="form_1.contactPhone" :disabled="!editAccess">
                  <template slot="prepend">中国+86</template>
                </el-input>
                <p class="color-gray form-tip">用于商户的后台登录、接收日常经营提醒及相关操作验证</p>
                <div v-if="actionType !== 'ADD'">
                  <el-button
                    type="text"
                    @click="() => {editAccess = true}">
                    更改
                  </el-button>
                </div>
              </el-form-item>
              <el-form-item label="品牌名称：" prop="branchName">
                <el-input v-model="form_1.branchName" maxlength="100"></el-input>
              </el-form-item>
            </div>
          </div>
          <!--岗位信息-->
          <div class="job_info">
            <p class="subTitle">岗位信息
              <el-button type="text" @click="addJob">增加岗位</el-button>
            </p>
            <div class="job-box" v-for="(item, index) in form_1.positionVoList" :key="index">
              <el-divider v-if="index == 1"></el-divider>
              <el-form-item label="自由职业者的工作场所：" :prop="'positionVoList.' + index + '.workplaceCode'" :rules="rules.positionVoList['workplaceCode']">
                <el-select clearable v-model="item.workplaceCode">
                  <el-option v-for="item in $dict('WorkPlaceEnum')" :key="item.code" :value="item.code" :label="item.desc"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="自由职业者服务类型：" :prop="'positionVoList.' + index + '.workCategoryCode'" :rules="rules.positionVoList['workCategoryCode']">
                <el-cascader
                  data-index="index"
                  v-model="item.workCategoryCode"
                  :options="workCategoryOptions"
                  :props="{ expandTrigger: 'hover', 'emitPath': false, 'value': 'workCategoryCode', 'label': 'workCategoryName', 'leaf': 'workCategoryCode' }"
                  @change="changeWorkCategory($event, index)">
                  <div slot-scope="{data}">
                    <el-tooltip :content="data.workCategoryName">
                      <span class="cascader-content-item">
                        {{ data.workCategoryName }}
                      </span>
                    </el-tooltip>
                  </div>
                </el-cascader>
              </el-form-item>
              <el-form-item label="发票类目：" :prop="'positionVoList.' + index + '.invoiceCategoryList'" :rules="rules.positionVoList['invoiceCategoryList']">
                <el-select
                  style="overflow: hidden;"
                  v-model="item.invoiceCategoryList"
                  multiple
                  clearable
                  value-key="invoiceCategoryCode"
                  filterable
                >
                  <el-option
                    v-for="(cate, key) in item._invoiceCateList"
                    :key="key"
                    :label="cate.invoiceCategoryName"
                    :value="cate"
                  ></el-option>
                </el-select>
                <span class="func-link" @click="changeCustomInvoice(item)">自定义选择</span>
                <p class="color-gray form-tip">平台根据贵司业务推荐开票类目，如您有其他选项需求请联系客户经理</p>
              </el-form-item>
              <el-form-item label="自由职业者服务描述：" :prop="'positionVoList.' + index + '.serviceDesc'" :rules="rules.positionVoList['serviceDesc']">
                <el-input
                  type="textarea"
                  v-model="item.serviceDesc"
                  :autosize="{ minRows: 5, maxRows: 5}"
                  placeholder="为使落地供应商匹配到合适的自由职业者，请按实际描述个人具体需要做什么"></el-input>
              </el-form-item>
              <el-form-item label="自由职业者服务所得计算规则：" :prop="'positionVoList.' + index + '.chargeRuleDesc'" :rules="rules.positionVoList['chargeRuleDesc']">
                <el-input
                  type="textarea"
                  v-model="item.chargeRuleDesc"
                  :autosize="{ minRows: 5, maxRows: 5}"
                  placeholder="为吸引合适的自由职业者进行匹配，请按实际填写个人完成服务后钱怎么算；如规则的数据范围有差异，请列明确定不同规则的依据"></el-input>
              </el-form-item>
              <div class="job-divider">
                <el-divider v-if="index > 0"></el-divider>
                <el-button v-if="index > 0" type="text" @click="deleteJob(index)">删除</el-button>
              </div>
            </div>
          </div>
          <!--合作信息-->
          <div class="cooperation_info">
            <p class="subTitle">合作信息</p>
            <div>
              <el-form-item label="企业行业类别：" prop="industryTypeCode">
                <el-cascader
                  v-model="form_1.industryTypeCode"
                  :options="industryListOptions"
                  :props="{ expandTrigger: 'hover', 'emitPath': false, 'value': 'industryTypeCode', 'label': 'industryTypeName'}"
                  @change="selectIndustry"
                ></el-cascader>
                <div>
                  <p class="color-gray form-tip">请正确选择企业行业类别，若类目选择错误可能导致审核驳回</p>
                </div>
              </el-form-item>
              <el-form-item label="预计用工人数：" prop="workerNum">
                <el-input
                  clearable
                  maxlength="8"
                  :value="form_1.workerNum"
                  @input="handleNumberInput(form_1, 'workerNum', $event)"
                ></el-input>
              </el-form-item>
              <el-form-item label="预期可实现C端签署率区间：" prop="signRateLevel">
                <el-select clearable v-model="form_1.signRateLevel">
                  <el-option v-for="item in $dict('SignRateLevelEnum')" :key="item.code" :value="Number(item.code)" :label="item.desc"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="自由职业者单人月经营所得9.7万以下百分比：" prop="workerMonthIncomeRate">
                <el-input clearable @input="handleRateInput(form_1, 'workerMonthIncomeRate', $event, 0, 100)" :value="form_1.workerMonthIncomeRate"></el-input>
              </el-form-item>
              <el-form-item label="月流水预估：" prop="monthMoneySlip">
                <el-input clearable @input="handleRateInput(form_1, 'monthMoneySlip', $event)" :value="form_1.monthMoneySlip" maxlength="8">
                  <template slot="append">万元</template>
                </el-input>
                <p class="color-gray form-tip">全月自由职业者经营所得预估</p>
              </el-form-item>
              <el-form-item label="是否可提供服务记录及经营所得计算明细：" prop="provideIncomeDetailType">
                <el-radio-group v-model="form_1.provideIncomeDetailType">
                  <el-radio v-for="item in $dict('ProvideIncomeDetailTypeEnum')" :key="item.code" :label="Number(item.code)">{{ item.desc }}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="公司网站：" prop="companyWebsite">
                <el-input clearable v-model="form_1.companyWebsite" maxlength="200"></el-input>
              </el-form-item>
              <el-form-item label="公司自有业务平台名称：" prop="bizPlatformName">
                <el-input clearable v-model="form_1.bizPlatformName" maxlength="200"></el-input>
                <p class="color-gray form-tip">如微信公众号名、小程序名、APP名等</p>
              </el-form-item>
              <el-form-item label="公司对外宣传资料：">
                <el-upload
                  class="upload-demo"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  accept=".doc,.docx,.pdf,.ppt,.pptx,.txt,.wps,.bpm,.png,.jpg,.jpeg,.gif"
                  :file-list="companyLeafletFileUrls"
                  :before-upload="beforeFileUpload"
                  :on-remove="handleRemove('companyLeafletFileUrls')"
                  :on-success="handleSuccess('companyLeafletFileUrls')"
                >
                  <el-button type="primary" size="small">点击上传文件</el-button>
                  <div slot="tip" class="el-upload__tip">
                    如产品服务宣传册，业务介绍PPT等<br>
                    文件大小需小于10M，格式为word、pdf、ppt、txt、wps、bmp、png、jpeg、jpg或gif
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item label="补充信息：">
                <el-upload
                  v-toggle:8="form_1.supplementFileUrls"
                  class="upload-demo"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  list-type="picture-card"
                  accept=".doc,.docx,.pdf,.ppt,.pptx,.txt,.wps,.bpm,.png,.jpg,.jpeg,.gif"
                  :limit="8"
                  :file-list="supplementFileUrls"
                  :before-upload="beforeFileUpload"
                  :on-remove="handleRemove('supplementFileUrls')"
                  :on-success="handleSuccess('supplementFileUrls')"
                  :on-exceed="handleExceed"
                  :on-preview="handlePreview"
                >
                  <template v-slot:file="{file}">
                    <div class="fileBg" v-if="!isImg(file.name)">
                      <span class="file-name">{{ file.name || '补充信息文件' }}</span>
                      <span class="el-upload-list__item-actions">
                        <span
                          class="el-upload-list__item-delete"
                          @click="() => { handleRemove('supplementFileUrls')(file)}"
                        >
                          <i class="el-icon-delete"></i>
                        </span>
                         <span
                           class="el-upload-list__item-preview"
                           @click="handlePreview(file)"
                         >
                          <i class="el-icon-zoom-in"></i>
                        </span>
                      </span>
                    </div>
                  </template>
                  <div slot="tip" class="el-upload__tip">
                    其他可佐证业务场景真实性的资料、照片或截图，包括但不限于
                    <span class="upload-tip__weight">公司自有业务平台（微信公众号关注数/小程序用户数/app下载量等）的截图、自由职业者用工场景照片等。</span>
                    <br>
                    必须为彩色图片（文档请截图后上传），最多8张，单张照片不超过6M。格式为word、pdf、ppt、txt、wps、bmp、png、jpeg、jpg或gif。
                  </div>
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </div>

          </div>
          <!--其他-->
          <div class="other">
            <div class="subTitle">其他</div>
            <el-form-item label="备注：">
              <el-input type="textarea" v-model="form_1.remark" maxlength="200" :rows="6"></el-input>
            </el-form-item>
          </div>
        </el-form>

        <el-footer class="form-btn-group" v-show="activeStep == 1">
          <el-button type="primary" @click="nextStep">提交</el-button>
          <el-button @click="backStep">取消</el-button>
        </el-footer>

        <!--step_3-->
        <div class="step_3" v-show="activeStep == 2">
          <img src="@/assets/success.png">
          <p class="result-title">提交成功</p>
          <p class="result-tip">商户新增成功</p>
        </div>
      </div>

      <!--审批意见-->
      <flow-opinion
        :visible.sync="flowVisible"
        @change="flowConfirm"
      ></flow-opinion>

    </div>
  </div>
</template>

<script>
  import { convert, clearVoid } from '@/utils/index';
  import { submitAudit, getActiveMainstay } from '@/api/merchant'
  import { listAllIndustryType, listAllWorkCategory } from '@/api/business'
  import { validateParams } from '@/utils/validate'
  import { updateExtInfo } from '@/api/waitingHandle';
  import { getProductList } from '@/api/product';
  import { editBusinessData } from '@/api/flow';
  import { getAllInvoiceCategory } from '@/api/invoice'

  import sticky from 'vue-sticky'
  import '@/styles/merchant-form.scss'
  import FlowOpinion from '@/components/Flow/FlowOpinion'

  export default {
    name: 'submitAudit',
    components: {
      FlowOpinion,
    },
    directives: {
      sticky
    },
    data() {
      const noNumValid = (rule, value, callback) => {
        if (/\d/.test(value)) {
          callback(new Error('公司名称不能包含数字'));
        } else {
          callback();
        }
      }
      return {
        isEdit: false,
        activeStep: 1,
        industryList: [], // 行业类别
        industryListOptions: [],
        workCategoryList: [], // 工作类目
        workCategoryOptions: [], // 工作类目树
        staffList: [],
        companyLeafletFileUrls: [], // 公司宣传文件
        supplementFileUrls: [], // 补充资料

        form_1: {
          mchName: '',
          mchNo: '',
          salerId: '',
          contactName: '',
          contactPhone: '',
          contactEmail: '',
          branchName:'',
          positionVoList: [{
            workplaceCode: '',
            workCategoryName: '',
            workCategoryCode: [],
            invoiceCategoryList: [],
            invoiceCategoryCode: '',
            serviceDesc: '',
            chargeRuleDesc: '',

            _invoiceCateList: [], // 下拉框选项列表
          }],
          agentNo: '',
          agentName: '',

          industryTypeCode: [],
          industryTypeName: '',
          workerNum: '',
          signRateLevel: '',
          workerMonthIncomeRate: '',
          monthMoneySlip: '',
          provideIncomeDetailType: '',
          companyWebsite: '',
          bizPlatformName: '',

          companyLeafletFileUrls: [],
          supplementFileUrls: [],

          remark: '',

        },
        form: {},
        rules: {
          // step 1
          mchName: [
            {required: true, message: '请输入企业名称', trigger: 'blur',},
            {validator: noNumValid, trigger: 'blur'},
          ],
          branchName:[
            {validator: validateParams({type: 'Length', max: '6', msg: '最多输入6个字符'}), trigger: 'blur'}
          ],
          industryTypeCode: [
            {required: true, message: '请选择企业行业类别', trigger: 'change',}
          ],
          salerId: [{
            required: true, message: '请选择销售', trigger: 'change',
          }],
          contactName: [
            {required: true, message: '请输入商户联系人姓名', trigger: 'blur'},
            {validator: validateParams({type: 'Chinese', msg: '请输入中文'}), trigger: 'blur'},
            {validator: validateParams({type: 'Length', max: '10', msg: '最多输入10个中文'}), trigger: 'blur'}
          ],
          contactPhone: [
            {required: true, message: '请输入商户联系人手机号', trigger: 'blur'},
            {validator: validateParams({type: 'Phone', msg: '请输入正确的手机号'}), trigger: 'blur'}
          ],
          contactEmail: [
            {required: true, message: '请输入商户联系人邮箱', trigger: 'blur'},
          ],
          positionVoList: {
            workplaceCode: [
              {required: true, message: '请选择自由职业者的工作场所', trigger: 'change',}
            ],
            workCategoryCode: [
              {required: true, message: '请选择自由职业者服务类型', trigger: 'change',}
            ],
            serviceDesc: [
              {required: true, message: '请输入自由职业者服务描述', trigger: 'blur',},
              {validator: validateParams({type: 'Length', max: '500', msg: '最多输入500个字符'}), trigger: 'blur'}
            ],
            chargeRuleDesc: [
              {required: true, message: '请输入自由职业者服务所得计算规则', trigger: 'blur',},
              {validator: validateParams({type: 'Length', max: '500', msg: '最多输入500个字符'}), trigger: 'blur'}
            ],
            invoiceCategoryList: [
              {required: true, message: '请选择发票类目', trigger: 'change'}
            ]
          },

          workerNum: [
            {required: true, message: '请输入预计用工人数', trigger: 'blur',},
            {validator: validateParams({type: 'Reg', pattern: /^\d{1,10}$/, msg: '预计用工人数最多可输入10个字'}), trigger: 'blur'}
          ],
          signRateLevel: [
            {required: true, message: '请选择预期可实现C端签署率区间', trigger: 'change',},
          ],
          workerMonthIncomeRate: [
            {required: true, message: '请输入自由职业者单人月经营所得9.7万以下百分比', trigger: 'blur',},
          ],
          monthMoneySlip: [
            {required: true, message: '请输入月流水预估', trigger: 'blur',},
            {validator: validateParams({type: 'Reg', pattern: /^\d{1,8}\.?\d*$/, msg: '月流水预估最多可输入8个字'}), trigger: 'blur'}
          ],
          provideIncomeDetailType: [
            {required: true, message: '请选择是否可提供服务记录及经营所得计算明细', trigger: 'change',},
          ],
          companyWebsite: [
            {validator: validateParams({type: 'Null', msg: '请输入正确的网址'}), trigger: 'blur'},
          ],
          bizPlatformName: [
            {validator: validateParams({type: 'Null', msg: '请输入中文'}), trigger: 'blur'},
          ],

          companyLeafletFileUrls: [],

          // step 2
          quoteVoList: [
            {
              validator(rule, value, cb) {
                if (value.length <= 0) {
                  cb(new Error('请填写报价单'))
                } else {
                  cb()
                }
              }
            }
          ],
          remark: '',
        },
        // step 3
        result: '',

        editAccess: false, // 是否可以修改负责人

        firstSet: false,

        agentList: [],

        flowVisible: false, // 审批意见显示控制
        flowRemark: '',
      }
    },
    computed: {
      actionType() {
        return this.$route.query.actionType || 'ADD';
      },
      processId() {
        return this.$route.query.processId || '';
      },
      taskId() {
        return this.$route.query.taskId || '';
      },
    },
    async mounted() {
      await this.init();

      if (sessionStorage.getItem('cooperationInfo')) {
        this.firstSet = true;
        let data = JSON.parse(sessionStorage.getItem('cooperationInfo'));
        this.$set(this, 'form', data);
        this.isEdit = true;
        if (this.form.companyLeafletFileUrls) {
          for (let item of this.form.companyLeafletFileUrls) {
            this.companyLeafletFileUrls.push({
              name: '文件',
              url: item
            })
          }
          this.form.companyLeafletFileUrls = this.companyLeafletFileUrls
        }
        if (this.form.supplementFileUrls) {
          for (let item of this.form.supplementFileUrls) {
            let suffix = item.split('.')[1];
            this.supplementFileUrls.push({
              url: this.fileUrl + item,
              name: `补充信息文件.${suffix}`
            })
          }
          this.form.supplementFileUrls = this.supplementFileUrls
        }

        for (let p in this.form_1) {
          this.form_1[p] = this.form[p];
        }
        this.preHandleCategory()
      }

      if (this.actionType == 'ADD') {
        this.editAccess = true;
      }
    },
    destroyed() {
      sessionStorage.removeItem('cooperationInfo');
    },
    methods: {
      async init() {
        await Promise.all([
          this.getListAllIndustryType(),
          this.getListAllWorkCategory(),
          this.getAllInvoiceCate()
        ])
      },
      // 预处理数据
      preHandleCategory() {
        this.form_1.positionVoList.forEach((item, index) => {
          this.changeWorkCategory(item.workCategoryCode, index, true);
        })
      },
      // 获取企业行业类别
      getListAllIndustryType() {
        listAllIndustryType().then(response => {
          this.industryList = response.data;
          this.industryListOptions = convert(response.data, 0);
        })
      },
      // 获取工作类目
      async getListAllWorkCategory() {
        const response = await listAllWorkCategory();
        this.workCategoryList = response.data;
        this.workCategoryOptions = convert(response.data, 0);
      },
      changeWorkCategory(value, index, prehandle) {
        for (let item of this.workCategoryList) {
          if (item.workCategoryCode == value) {
            if (!prehandle) {
              this.form_1.positionVoList[index].workCategoryName = item.workCategoryName;
              this.form_1.positionVoList[index].invoiceCategoryCode = item.invoiceCategoryCode;
              this.form_1.positionVoList[index].invoiceCategoryName = item.invoiceCategoryName;
              this.form_1.positionVoList[index].serviceDesc = item.workDesc;
              this.form_1.positionVoList[index].chargeRuleDesc = item.chargeRuleDesc;
            }
            const list = item.jsonEntity.invoiceCategoryList.map(cate => {
              return {
                invoiceCategoryCode: cate.invoiceCategoryCode,
                invoiceCategoryName: cate.invoiceCategoryName,
              }
            })
            if (!prehandle) {
              this.form_1.positionVoList[index].invoiceCategoryList = [];
            }
            if (prehandle) {
              this.$set(this.form_1.positionVoList[index], '_invoiceCateList', this.allInvoiceCate);
            } else {
              this.$set(this.form_1.positionVoList[index], '_invoiceCateList', list);
            }
            break;
          }
        }
      },
      selectIndustry(value, index) {
        for (let item of this.industryList) {
          if (item.industryTypeCode == value) {
            this.form_1.industryTypeName = item.industryTypeName;
          }
        }
      },
      addJob() {
        this.form_1.positionVoList.push({
          workplaceCode: '',
          workCategoryCode: '',
          invoiceCategoryCode: '',
          serviceDesc: '',
          chargeRuleDesc: '',
        })
      },
      deleteJob(index) {
        if (this.form_1.positionVoList.length == 1) {
          return this.$message.error("必须含有一个岗位信息")
        } else {
          this.form_1.positionVoList.splice(index, 1);
        }
      },
      beforeFileUpload(file) {
        return this.validateUploadFile({
          img: true,
          pdf: true,
          word: true,
          addition: ['ppt', 'txt', 'pptx'],
          size: 10
        })(file)
      },
      handleRemove(param) {
        return (file) => {
          let form = this['form_' + this.activeStep];
          this[param] = this[param].filter(item => {
            return item.uid !== file.uid
          });
          form[param] = this[param];
        }
      },
      handleExceed() {
        this.$message.error('最多上传8张!')
      },
      handleSuccess(param) {
        return (response, file, fileList) => {
          let form = this['form_' + this.activeStep];
          form[param] = this[param] = fileList;
        }
      },
      async nextStep() {
        for (let p in this[`form_${this.activeStep}`]) {
          if (typeof this[`form_${this.activeStep}`][p] == 'string' && !(/Term/.test(p))) {
            this[`form_${this.activeStep}`][p] = this[`form_${this.activeStep}`][p].replace(/\s+/, '')
          }
        }
        let form = this.$refs['form_' + this.activeStep];
        const valid = await form.validate().catch(_ => false);
        if (!valid) {
          return this.$message.error('请填写必填信息');
        }
        if (this.activeStep == 1) {
          if (!this.flowVisible) {
            this.flowVisible = true;
            return;
          }
          this.flowVisible = false;

          let data = {...this.form, ...this.form_1};
          // 遍历上传图片的fileUrl，拼装数据
          data.companyLeafletFileUrls = data.companyLeafletFileUrls.map(item => {
            if (item.response) {
              return item.response.data
            } else {
              return item.url
            }
          });
          if (data.supplementFileUrls) {
            data.supplementFileUrls = data.supplementFileUrls.map(item => {
              if (item.response) {
                return item.response.data
              } else {
                return item.url.replace(this.fileUrl, '')
              }
            });
          }
          data.positionVoList.forEach(item => {
            delete item._invoiceCateList
          });

          clearVoid(data);

          if (this.isEdit) {
            if (this.processId) {
              editBusinessData({
                commonFlowId: this.processId,
                taskId: this.taskId,
                remark: this.flowRemark,
                extInfo: JSON.stringify(data)
              }).then(response => {
                this.$message.success(response.data);
                this.$router.push({
                  path: '/waitingHandle/detailProcess',
                  query: {
                    processId: this.processId,
                    taskId: this.taskId
                  }
                });
              })
            }
          } else {
            const res = await submitAudit({
              extObj: data,
              condition: {},
              participant: {},
              remark: this.flowRemark,
            });
            this.activeStep++;
            this.result = res.data;
          }
        }
      },
      goDetailProcess() {
        this.$router.push('/waitingHandle/detailProcess?processId=' + this.result.commonFlowId);
      },
      handleFileRemove(file) {
        this.companyLeafletFileUrls = this.companyLeafletFileUrls.filter(item => {
          return item.name !== file.name;
        })
      },
      removePubFile(file) {
        this.supplementFileUrls = this.form_1.supplementFileUrls.filter(item => {
          return item.url !== file.url;
        })
      },
      handleAgentChange(val) {
        if (!val) {
          this.form_1.agentNo = this.form_1.agentName = ''
          return;
        }
        for (let i = 0; i < this.agentList.length; i++) {
          if (this.agentList[i].agentNo == val) {
            this.form_1.agentName = this.agentList[i].agentName;
            break;
          }
        }
      },
      backStep() {
        this.$router.back();
      },
      editSpecialFee(row, index) {
        const temp = {
          ruleParam: [{
            specialRuleType: 1,
            compareType: 1,
            value: row.mainstayMchNo
          }],
        };
        this.editRowIndex = index;
        if (row.ruleParam.length == 0) {
          this.editRow = JSON.parse(JSON.stringify({...row, ...temp}));
        } else {
          this.editRow = JSON.parse(JSON.stringify(row))
        }
        this.editFeeVisible = true;
        this.$nextTick(() => {
          this.$refs.editForm.clearValidate();
        })
      },
      closeEditDialog() {
        this.$refs.editForm.clearValidate();
        this.$refs.editForm.resetFields();
      },
      addRuleList() {
        this.editRow.ruleParam.push({
          specialRuleType: '',
          compareType: '',
          value: ''
        })
      },
      selectRuleType(index) {
        this.editRow.ruleParam[index].compareType = 1;
      },
      deleteRule(index) {
        this.editRow.ruleParam.splice(index, 1)
      },
      async confirmEdit() {
        const valid = await this.$refs.editForm.validate().catch(_ => false);
        if (!valid) return;
        this.form_2.quoteVoList.splice(this.editRowIndex, 1, {...this.editRow});
        this.editFeeVisible = false;
      },
      vendorChange(val, row) {
        for (let i = 0; i < this.mainstayList.length; i++) {
          if (val == this.mainstayList[i].mchNo) {
            row['objName'] = this.mainstayList[i].mchName;
            break;
          }
        }
      },
      async getProductList() {
        const {data} = await getProductList();
        this.productList = data;
      },
      productChange(val, row) {
        for (let i = 0; i < this.productList.length; i++) {
          if (val == this.productList[i].productNo) {
            row['productName'] = this.productList[i].productName;
            break;
          }
        }
      },
      flowConfirm(remark) {
        this.flowRemark = remark;
        this.nextStep();
      },
      handlePreview(file) {
        let url = file.response ? this.fileUrl + file.response.data : file.url;
        if (this.isImg(url)) {
          this.$preview(url)
        } else {
          let suffix = url.split('.')[1];
          this.downloadFile(url, `补充信息文件.${suffix}`);
        }
      },
      async getAllInvoiceCate() {
        const { data } = await getAllInvoiceCategory();
        this.allInvoiceCate = data
      },
      changeCustomInvoice(item) {
        item._invoiceCateList = this.allInvoiceCate
      },
    }
  }
</script>

<style lang="scss" scoped>
  .page-container {
    .footer-container {
      text-align: center;
    }
    .step_1 {
      .job-divider {
        position: relative;
        .el-button {
          position: absolute;
          bottom: 0;
          right: 0;
        }
      }
    }
    .step_3 {
      padding: 50px 0;
      text-align: center;
      p {
        margin-bottom: 20px;
      }
    }
  }
</style>
