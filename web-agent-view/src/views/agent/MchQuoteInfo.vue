<template>
  <div class="info-container">
    <template v-if="zxhForm.length">
      <p class="subTitle">智享汇产品报价单</p>
      <el-table
        :data="zxhForm"
      >
        <el-table-column label="产品">
          <el-table-column label="产品名称">
            <template v-slot="{row}">
              {{ row.productNo }} <br>
              {{ row.productName }}
            </template>
          </el-table-column>
          <el-table-column label="供应商">
            <template v-slot="{row}">
              {{ row.mainstayMchNo }} <br>
              {{ row.mainstayMchName }}
            </template>
          </el-table-column>
          <el-table-column label="岗位类别">
            <template v-slot="{row}">
              <el-tooltip
                v-for="(item, index) in row.positionNameList"
                :key="index"
                :content="item">
                <el-tag type="info">
                  {{ item }}
                </el-tag>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            label="发票类目"
            prop="invoiceCategoryList">
            <template v-slot="{row}">
              <el-tooltip
                v-for="(item, index) in row.invoiceCategoryList"
                :key="index"
                :content="item.invoiceCategoryName"
              >
                <el-tag type="info">
                  {{ item.invoiceCategoryName }}
                </el-tag>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column>
          <template v-slot:header>
            报价
            <el-tooltip content="如有阶梯计费、单笔金额等特殊计费需求，可使用「编辑计费」功能">
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </template>
          <el-table-column label="公式类型">
            <template v-slot="{row}">
              {{ $dictCode('FormulaEnum', row.quoteData.formulaType).desc }}
            </template>
          </el-table-column>
          <el-table-column label="固定金额">
            <template v-slot="{row}">
              <div v-if="row.quoteData.formulaType == 1 || row.quoteData.formulaType == 2">
                {{ row.quoteData.fixedFee }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="费率">
            <template v-slot="{row}">
              <div v-if="row.quoteData.formulaType == 0 || row.quoteData.formulaType == 2">
                {{ row.quoteData.rate }} %
              </div>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="特殊计费规则">
          <template v-slot="{row}">
            <div
              v-for="(item, index) in row.ruleParam"
              :key="index"
            >
              {{ $dictCode('ProductFeeSpecialRuleTypeEnum', item.specialRuleType).desc }}
              {{ $dictCode('CompareTypeEnum', item.compareType).desc }}
              {{ item.value }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template v-slot="{row}">
            <el-tag :type="row.status == 100 ? '' : 'info'">
              {{ $dictCode('QuoteStatusEnum', row.status).desc }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template v-if="ckhForm.length">
      <p class="subTitle">创客汇产品报价单</p>
      <el-table :data="ckhForm">
        <el-table-column label="产品">
          <el-table-column label="产品名称">
            <template v-slot="{row}">
              {{ row.productNo }} <br>
              {{ row.productName }}
            </template>
          </el-table-column>
          <el-table-column label="供应商">
            <template v-slot="{row}">
              {{ row.mainstayMchNo }} <br>
              {{ row.mainstayMchName }}
            </template>
          </el-table-column>
          <el-table-column label="岗位类别">
            <template v-slot="{row}">
              <el-tooltip
                v-for="(item, index) in row.positionNameList"
                :key="index"
                :content="item"
              >
                <el-tag type="info">
                  {{ item }}
                </el-tag>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            label="发票类目"
            prop="invoiceCategoryList">
            <template v-slot="{row}">
              <el-tooltip
                v-for="(item, index) in row.invoiceCategoryList"
                :key="index"
                :content="item.invoiceCategoryName"
              >
                <el-tag type="info">
                  {{ item.invoiceCategoryName }}
                </el-tag>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="个税">
          <el-table-column
            label="个税类型"
            prop="taxTypeDesc"
          >
            <template v-slot="{row, column}">
              {{ row.merchantCkhQuote && row.merchantCkhQuote[column.property] }}
            </template>
          </el-table-column>
          <el-table-column label="个税承担方">
            <template v-slot="{row}">
              {{ row.merchantCkhQuote && $dictCode('TaxPayerEnum', row.merchantCkhQuote.taxPayer).desc }}
            </template>
          </el-table-column>
          <el-table-column label="增值税税率 %">
            <template v-slot="{row}">
              {{ row.merchantCkhQuote && row.merchantCkhQuote.addedTaxRatePct }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="服务费费率 %"
          prop="serviceFeeRatePct"
        >
          <template v-slot="{row, column}">
            {{ row.merchantCkhQuote && row.merchantCkhQuote[column.property] }}
          </template>
        </el-table-column>
        <el-table-column
          label="服务费结算模式"
          prop="balancedMode"
        >
          <template v-slot="{row, column}">
            {{ row.merchantCkhQuote && $dictCode('BalancedEnum', row.merchantCkhQuote[column.property]).desc }}
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template v-slot="{row}">
            <el-tag :type="row.status == 100 ? '' : 'info'">
              {{ $dictCode('QuoteStatusEnum', row.status).desc }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template v-if="zftForm.length">
      <p class="subTitle">直付通产品报价单</p>
      <el-table
        :data="zftForm"
      >
        <el-table-column label="产品">
          <el-table-column label="产品名称">
            <template v-slot="{row}">
              {{ row.productNo }} <br>
              {{ row.productName }}
            </template>
          </el-table-column>

        </el-table-column>
        <el-table-column>
          <template v-slot:header>
            报价
          </template>
          <el-table-column label="公式类型">
            <template v-slot="{row}">
              {{ $dictCode('FormulaEnum', row.quoteData.formulaType).desc }}
            </template>
          </el-table-column>
          <el-table-column label="固定金额">
            <template v-slot="{row}">
              <div v-if="row.quoteData.formulaType == 1 || row.quoteData.formulaType == 2">
                {{ row.quoteData.fixedFee }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="费率">
            <template v-slot="{row}">
              <div v-if="row.quoteData.formulaType == 0 || row.quoteData.formulaType == 2">
                {{ row.quoteData.rate }} %
              </div>
            </template>
          </el-table-column>
          <el-table-column label="退款是否退服务费">
            <template v-slot="{row}">
              {{ row.quoteData.canRefund == 1 ? '是' : '否' }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="状态">
          <template v-slot="{row}">
            <el-tag :type="row.status == 100 ? '' : 'info'">
              {{ $dictCode('QuoteStatusEnum', row.status).desc }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </template>
  </div>
</template>

<script>
export default {
  name: "MchQuoteInfo",
  props: {
    info: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      quoteList: [],
      zxhForm: [],
      ckhForm: [],
      zftForm: []
    }
  },
  watch: {
    info: {
      immediate: true,
      handler() {
        this.preHandle();
      }
    }
  },
  methods: {
    preHandle() {
      this.zxhForm = [];
      this.ckhForm = [];
      this.info.forEach(item => {
        if (item.productNo === 'ZXH') {
          const length = item.quoteRateList ? item.quoteRateList.length : 0;
          if (item.quoteRateList) {
            item.quoteRateList.forEach((quote, index) => {
              quote.ruleParam = JSON.parse(quote.ruleParam);
              this.zxhForm.push({
                ...item,
                quoteData: quote,
                rowspan: index === 0 ? length : null
              })
            })
          }
        } else {
          if (item.productNo === 'CKH') {
            this.ckhForm.push(item)
          } else if (item.productNo === 'ZFT') {
            const length = item.merchantZftQuote ? item.merchantZftQuote.length : 0;
            if (item.merchantZftQuote) {
              item.merchantZftQuote.forEach((quote, index) => {
                quote.ruleParam = JSON.parse(quote.ruleParam);
                this.zftForm.push({
                  ...item,
                  quoteData: quote,
                  rowspan: index === 0 ? length : null
                })
              })
            }
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
</style>
