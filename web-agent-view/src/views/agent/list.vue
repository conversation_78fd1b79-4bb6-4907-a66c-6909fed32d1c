<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">合伙人编号：</span>
          <el-input v-model="searchForm.agentNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">合伙人名称：</span>
          <el-input v-model="searchForm.agentNameLike" placeholder="模糊查询"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">类型：</span>
          <el-select
            clearable
            v-model="searchForm.agentType"
          >
            <el-option
              v-for="item in $dict('AgentTypeEnum')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">状态：</span>
          <el-select
            v-model="searchForm.agentStatus"
            clearable
          >
            <el-option
              v-for="item in $dict('AgentStatusEnum')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            type="datetimerange"
            :start-time.sync="searchForm.createBeginTime"
            :end-time.sync="searchForm.createEndTime"
            ref="datepicker"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询</el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件</el-button>
      </div>
    </div>

    <div class="content-container">
      <el-table
        ref="table"
        :data="list"
      >
        <el-table-column
          label="合伙人编号"
          prop="agentNo"
          width="120"
        ></el-table-column>
        <el-table-column
          label="合伙人名称"
          prop="agentName"
          width="120"
        ></el-table-column>
        <el-table-column
          label="类型"
          prop="agentType"
        >
          <template v-slot="{row}">
            {{ $dictCode('AgentTypeEnum', row.agentType).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          prop="agentStatus"
        >
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.agentStatus)">
              {{ $dictCode('AgentStatusEnum', row.agentStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="商户关系"
          prop="merNum"
        >
          <template v-slot="{row}">
            <span
              class="func-content"
              @click="goBusiness(row)"
            >
              {{ row.merNum || 0 }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="创建时间"
          prop="createTime"
          width="150"
        ></el-table-column>

      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        key="agent"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>
  </div>
</template>

<script>
  import { getAgentList } from '@/api/agent'
  export default {
    data() {
      return {
        searchForm: {
          agentNo: '',
          agentNameLike: '',
          agentType: '',
          agentStatus: '',
          createBeginTime: '',
          createEndTime: '',
        },

        pageSize: 10,
        pageCurrent: 1,

        list: [],
        totalRecord: 0,

      }
    },
    mounted() {
      this.search()
    },
    methods: {
      create() {
        this.$router.push('/agent/createGuide')
      },
      resetForm() {
        this.searchForm = {
          agentNo: '',
          agentNameLike: '',
          agentType: '',
          agentStatus: '',
          createBeginTime: '',
          createEndTime: '',
        };
        this.$refs.datepicker.clearTime();
      },
      async search(init) {
        if (init) {
          this.pageCurrent = 1;
        }
        const { data, totalRecord } = await getAgentList({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent,
        })
        this.list = data.data;
        this.totalRecord = data.totalRecord;
      },
      getTagType(status) {
        switch (Number(status)) {
          case 100:
            return 'success';
          case 101:
            return 'warning';
          case 103:
            return 'danger';
          default:
            return ''
        }
      },
      goDetail(row) {
        this.$router.push({
          path: '/agent/agentInfo',
          query: {
            agentNo: row.agentNo
          }
        })
      },
      changeStatus(agent, status) {
        this.changingStatus = status;
        this.changingAgent = agent;
        this.$refs.statusDialog.visible = true;
      },
      goBusiness(row) {
        this.$router.push({
          path: '/agent/businessRelation',
          query: {
            agentNo: row.agentNo,
            agentName: row.agentName,
            type: 1,
          }
        })
      }
    }
  }
</script>

<style>
</style>
