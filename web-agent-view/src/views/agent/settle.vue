<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">账单编号：</span>
          <el-input v-model="searchForm.billNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">结算状态：</span>
          <el-select v-model="searchForm.settlementStatus" clearable>
            <el-option
              v-for="item in $dict('AgentMonthBillTypeEnum')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">账单月份：</span>
          <el-date-picker
            ref="datepicker"
            type="month"
            v-model="searchForm.billDate"
            value-format="yyyy-MM"
            :picker-options="pickerOption"
          ></el-date-picker>
        </div>
      </div>

      <div class="search-wrapper search-btn-group">
        <el-button type="primary" @click="search(true)">查询</el-button>
        <el-button @click="exportList">导出</el-button>
        <el-button type="text" @click="getExportList">查看已导出列表</el-button>
        <el-button type="text" @click="resetForm">清空筛选条件</el-button>
      </div>
    </div>

    <div class="content-container">
      <el-table
        ref="table"
        :data="list"
        :key="'table-' + searchForm.agentType"
      >
        <el-table-column type="index" label="序号" :index="getIndex"></el-table-column>
        <el-table-column label="账单月份" prop="billDate"></el-table-column>
        <el-table-column label="账单编号" prop="billNo" width="180"></el-table-column>

        <el-table-column width="120">
          <template v-slot:header>
            收益（元）<el-tooltip content="收益包含个税" placement="top"><i class="el-icon-warning"></i></el-tooltip>
          </template>
          <template v-slot="{row}">
            <p class="text-right">
              {{ row.agentMonthProfit | moneyFormat }}
            </p>
          </template>
        </el-table-column>

        <el-table-column width="220" label="个税（元）/代扣税比例（%）" v-if="userData.agentType == 100">
          <template v-slot="{row}">
            <span class="text-right" style="display: block">
              {{ row.tax | moneyFormat }}
            </span>
            <span class="text-right color-gray" style="display: block">
              {{ row.taxPercent }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="结算金额（元）" width="180">
          <template v-slot="{row}">
            <p class="text-right">
              {{ row.settlementAmount | moneyFormat }}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="结算状态" width="150">
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.settlementStatus)">
              {{ $dictCode('AgentMonthBillTypeEnum', row.settlementStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right">
          <template v-slot="{row}">
            <el-button type="text" @click="goDetail(row)">查看明细</el-button>
          </template>
        </el-table-column>

      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        v-if="list"
        ref="pagination"
        :total="totalRecord"
        :page-sizes="[10,50]"
        :current-page.sync="pageCurrent"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      />
    </el-footer>

    <ExportRecord ref="exportRecord"></ExportRecord>
  </div>
</template>

<script>
  import dayjs from 'dayjs'

  import ExportRecord from '@/components/ExportRecord'
  import { getMonthBill, exportBillList } from '@/api/agent'

  const nowMonth = dayjs().month()
  const nowYear = dayjs().year()

  export default {
    name: 'agentSettle',
    components: {
      ExportRecord,
    },
    data() {
      return {
        defaultActive: '100',
        searchForm: {
          billNo: '',
          settlementStatus: '',
          billDate: '',
        },

        pageCurrent: 1,
        pageSize: 10,
        totalRecord: 0,

        list: [],

        pickerOption: {
          disabledDate(time) {
            let date = new Date(time);
            if (date.getFullYear() > nowYear) {
              return true
            } else if (date.getFullYear() < nowYear) {
              return false
            } else {
              return date.getMonth() >= nowMonth
            }
          }
        },

      }
    },
    mounted() {
      this.search();
    },
    methods: {
      async search(init) {
        if (init) {
          this.pageCurrent = 1
        }
        const { data } = await getMonthBill({
          ...this.searchForm,
          pageCurrent: this.pageCurrent,
          pageSize: this.pageSize,
        })

        this.list = data.data;
        this.totalRecord = data.totalRecord;
      },
      async exportList() {
        await exportBillList(this.searchForm)
        this.$message.success('导出成功')
      },
      getExportList() {
        this.$refs.exportRecord.isShow = true;
        this.$refs.exportRecord.getExportRecord('18');
      },
      resetForm() {
        this.searchForm = {
          billNo: '',
          settlementStatus: '',
          billDate: '',
        }
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
      getTagType(status) {
        switch (Number(status)) {
          case 100:
          case 104:
            return 'success';
          case 103:
            return 'danger';
          case 102:
            return 'warning';
          case 105:
            return 'info';
          default:
            return 'info'
        }
      },
      goDetail(row) {
        this.$router.push({
          path: '/order/list',
          query: {
            date: row.billDate,
          }
        })
      },
    },
  }
</script>

<style scoped lang="scss">

</style>
