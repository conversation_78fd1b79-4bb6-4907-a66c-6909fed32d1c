<template>
  <el-dialog :visible.sync="isShow"
             title="导出列表"
             width="800px">
    <el-table :data="recordList">
      <el-table-column label="创建时间" prop="createTime" width="100"></el-table-column>
      <el-table-column label="标题" prop="fileName"></el-table-column>
      <el-table-column label="操作人" prop="operatorLoginName"></el-table-column>
      <el-table-column label="状态" prop="exportStatus">
        <template slot-scope="scope">
          {{ $dictCode('ExportStatusEnum', scope.row.exportStatus).desc }}
        </template>
      </el-table-column>
      <el-table-column label="描述" prop="errDesc"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" v-if="$dictCode('ExportStatusEnum', scope.row.exportStatus).desc === '成功'"
           @click="exportFile(scope.row)">导出</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
  import { getExportRecord } from '@/api/common'

  export default {
    name: 'index',
    data() {
      return {
        isShow: false,
        recordList: []
      }
    },
    methods: {
      getExportRecord(reportType) {
        getExportRecord({
          reportType
        }).then(response => {
          this.recordList = response.data;
        })
      },
      async exportFile(data) {
        const fileMsg = await this.formatFileMsg(data.fileUrl);
        const suffix = data.fileUrl.split('.').pop();
        this.downloadFile(fileMsg.fileUrl, data.fileName, suffix);
      }
    }
  }
</script>

<style scoped>

</style>
