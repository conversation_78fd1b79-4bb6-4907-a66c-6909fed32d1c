import { JSEncrypt } from 'jsencrypt';

export const encryptParam = param => {
  const jsencrypt = new JSEncrypt();
  if (!param.publicKey) {
    return
  }
  jsencrypt.setPublicKey(param.publicKey);
  const RSA_encrypt = jsencrypt.encrypt(param.pwd);
  return RSA_encrypt;
};

let LOING_RSA_PUBLIC_KEY = '';
if (process.env.VUE_APP_FLAG === 'production') {
  LOING_RSA_PUBLIC_KEY = 
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCJETWYiap918352mFxMscXCRifxCArqt5acUyTsdX1w63FkGXkbwtZdA0NW/I0LlRrNqDPW2aJS3JTxVMsM79tOPY1LrzSzkos5ik6vvs+iPeIAX3SyLZulEQCbBVWzKTZjYfNKozmTSrPJG7Ls2iuKZiNygyR+Ybw676fTMo2kwIDAQAB'
} else {
  LOING_RSA_PUBLIC_KEY = 
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDSflrDvH/OXG1dzLILG0JJbJSOW8zv7nUAwyJjZJcJK/l5NG0Dr+ictwQysZkpoC5YaRVNNZUj5anG2aD0GGcsvZCS1LEwCoSNu9yKLSVbhQ/sW76LBASOauh4HEpTfPub+/DvrQuV3ag80eA7HH10p4VusGLO3V8SeggtM2n7cQIDAQAB'
}


export const encryptFileParam = param => {
  const jsencrypt = new JSEncrypt();
  jsencrypt.setPublicKey(LOING_RSA_PUBLIC_KEY);
  const RSA_encrypt = jsencrypt.encrypt(param);
  return RSA_encrypt;
};
