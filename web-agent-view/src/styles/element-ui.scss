// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}


.el-steps-container {
  .el-step__title.is-finish {
    font-weight: 700;
    color: #303133;
  }
  .el-step__head.is-finish {
    color: #fff;

    .el-step__icon {
      background: #409EFF
    }
  }

}

.el-scrollbar__bar {
  opacity: 0 !important;
}

.el-link + .el-link{
  margin-left: 4px;
}

// upload组件提示信息样式
.el-upload__tip {
  line-height: 1.8;
  @extend .color-gray;
}
// upload-tip
.upload-tip__weight {
  font-weight: bolder;
}
