// 表单页面样式
.page-container {
  padding: 32px 0 50px;
  background: #fff;
  @include boxShadow;
  .warning-container {
    margin-left: 16px;
    margin-right: 16px;
  }
  .header-container {
    padding: 32px 0;
  }
  .content-container {
    position: relative;
    margin-top: 32px;
    padding: 0 16px 64px;
  }
  .hgroup {
    text-align: center;
  }
  .el-input, .el-textarea {
    width: 300px;
  }
  .subTitle {
    margin-top: 60px;
    margin-left: 30px;
    margin-bottom: 20px;
  }
  .el-form-item {
    margin-bottom: 24px;
  }
  .el-form-item__label {
    text-align: right;
  }
  .form-tip {
    margin-top: 4px;
    margin-bottom: 0;
    font-size: 12px;
  }
  .form-btn-group {
    position: fixed;
    left: 90px;
    right: 0;
    bottom: 0;
    padding: 8px;
    background: #fff;
    text-align: center;
    box-shadow: 0px -3px 3px 1px #ddd;
    z-index: 10;
  }
  .el-upload-list {
    height: 100%;
  }

  .flex-container {
    padding: 32px;
  }
}

.result-detail {
  margin: 0 0 20px;
  padding: 20px;
  padding-left: 270px;
  text-align: left;
}
.result-title {
  font-size: 30px;
  font-weight: 600;
}
.result-detail-title {
  font-weight: 600;
}
.result-tip {
  color: #ccc;
}
.result-wrapper {
  display: flex;
  justify-content: space-between;
  .result-item {
    flex: 1;
  }
}

