import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/reset.css';
import 'element-ui/lib/theme-chalk/index.css'

import '@/styles/index.scss' // global css
import App from './App'
import store from './store'
import router from './router'
import mixin from '@/mixin'
import VToggle from '@/directive/toggle'
import VPreview from '@/directive/preview'
import VClipboard from '@/directive/clipboard'


// 过滤器
import filters from '@/filter/filter.js';
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})
import '@/icons' // icon
import '@/permission' // permission control
import '@/utils/validate.js'
import VPermission from '@/directive/permission'

import datePicker from '@/components/DatePicker/datePicker.vue';
import addressSelect from '@/components/AddressSelect/index.vue';
import scrollPanel from '@/components/ScrollPanel/index.vue';

Vue.component('date-picker', datePicker);
Vue.component('address-select', addressSelect);
Vue.component('scroll-panel', scrollPanel);

//install v-permission
VPermission.install(Vue)

// set ElementUI lang to EN
Vue.use(ElementUI)

Vue.use(VToggle)
Vue.use(VPreview)
Vue.use(VClipboard)

Vue.mixin(mixin)

Vue.config.productionTip = false

export default new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})

Vue.prototype.$dict = (dataName) => {
  let dictionary = store.getters.userData && store.getters.userData.dictionary;
  if (dictionary && dictionary[dataName]) {
    return dictionary[dataName]
  } else {
    return []
  }
};


Vue.prototype.$dictFlag = (dataName, dataFlag) => {
  let dictionary = store.getters.userData && store.getters.userData.dictionary;
  if (dictionary && dictionary[dataName]) {
    for (let item of dictionary[dataName]) {
      if (item.flag === String(dataFlag)) {
        return item;
      }
    }
  }
  return {}
};

Vue.prototype.$dictCode = (dataName, dataCode) => {
  let dictionary = store.getters.userData && store.getters.userData.dictionary;
  if (dictionary && dictionary[dataName]) {
    for (let item of dictionary[dataName]) {
      if (item.code === String(dataCode)) {
        return item;
      }
    }
  }
  return {}
}

Vue.prototype.$dictDesc = (dataName, dataDesc) => {
  let dictionary = store.getters.userData && store.getters.userData.dictionary;
  if (dictionary && dictionary[dataName]) {
    for (let item of dictionary[dataName]) {
      if (item.desc === String(dataDesc)) {
        return item;
      }
    }
  }
  return {}
}

