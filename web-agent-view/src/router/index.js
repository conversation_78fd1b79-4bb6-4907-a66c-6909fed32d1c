import Vue from 'vue'
import Router from 'vue-router'
/* Layout */
import Layout from '@/layout'

Vue.use(Router)

export const constantRoutes = [
  {
    path: '/login',
    component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/login'),
    hidden: true
  },
  {
    path: '/retrievePwd',
    component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/retrievePwd'),
    hidden: true
  },
  {
    path: '/changePwd',
    component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/changePwd'),
    hidden: true
  },
  {
    path: '/selectMch',
    component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/selectMch'),
    hidden: true
  },
  {
    path: '/accountManagement',
    component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/accountManagement'),
    hidden: true
  },
  {
    path: '/setting',
    component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/setting'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/404'),
    hidden: true
  },
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: () => import(/* webpackChunkName: "constant-group" */ '@/views/common/redirect')
      },
    ]
  },
  {
    path: '/',
    component: Layout,
    hidden: true,
    redirect: '/index',
    children: [
      {
        path: '/index',
        component: () => import(/* webpackChunkName: "constant-group" */ '@/views/index'),
        hidden: true,
      },
      {
        path: '/safe/leaderChange',
        component: () => import(/* webpackChunkName: "constant-group" */ '@/views/system/leaderChange')
      },
      {
        path: '/safe/emailChange',
        component: () => import(/* webpackChunkName: "constant-group" */ '@/views/system/emailChange')
      },
      {
        path: '/safe/tradePwdChange',
        component: () => import(/* webpackChunkName: "constant-group" */ '@/views/system/tradePwdChange')
      },
      {
        path: '/merchant/staffForm',
        component: () => import(/* webpackChunkName: "constant-group" */ '@/views/merchant/staffForm')
      },
      {
        path: '/agent/businessRelation',
        name: 'agentBusinessRelation',
        component: () => import(/* webpackChunkName: "agent-group" */ '@/views/agent/businessRelation')
      },
      {
        path: '/waitingHandle/detailProcess',
        component: () => import(/* webpackChunkName: "process-group" */ '@/views/waitingHandle/flowDetail'),
      },
      {
        path: '/notify/list',
        component: () => import(/* webpackChunkName: "notify-group" */ '@/views/notify/list'),
      }
    ]
  },
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({
    y: 0
  }),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router

let routesInfo = {
  /* 系统 */
  "system:safe": () => import(/* webpackChunkName: "system-group" */ "@/views/system/safeSetting"),

  /* 商户 */
  "pms:staff:view": () => import(/* webpackChunkName: "merchant-group" */ "@/views/merchant/staff"),
  "pms:role:view": () => import(/* webpackChunkName: "merchant-group" */ "@/views/merchant/role"),
  "pms:info:view": () => import(/* webpackChunkName: "merchant-group" */ "@/views/merchant/info"),

  "pms:key:view": () => import(/* webpackChunkName: "merchant-group" */ '@/views/merchant/keyManage'),
  "merchantEmployer:plat:view": () => import(/* webpackChunkName: "merchant-group" */ '@/views/merchant/cooperateInfo'),

  /* 合伙人 */
  "agent:list:view": () => import(/* webpackChunkName: "agent-group" */ '@/views/agent/list'),
  'fee:agentFeeOrder:list': () => import(/* webpackChunkName: "order-group" */ '@/views/order/list'),
  'fee:agentFeeSum:list': () => import(/* webpackChunkName: "agent-group" */ '@/views/agent/statistic'),
  'agent:merchant:report': () => import(/* webpackChunkName :"agent-group" */ '@/views/agent/merchantReport'),
  /* 结算 */
  'agent:settle:view': () => import(/* webpackChunkName: "finance-group" */ '@/views/agent/settle'),


  /* 待办 */
  'waitingHandle:myProcess:view': () => import(/* webpackChunkName: "process-group" */ '@/views/waitingHandle/flow'),
};

export const addRoutes = function (functions) {
  let menuRoute = {
    path: '/',
    component: Layout,
    children: []
  };
  functions.filter(f => f.type === 1).forEach(f => {
    menuRoute.children.push({
      component: routesInfo[f.permissionFlag],
      path: f.url,
      meta: {
        id: f.id,
        title: f.name,
        permissionFlag: f.permissionFlag
      }
    });
  });
  router.addRoutes([menuRoute]) // 添加路由
}
