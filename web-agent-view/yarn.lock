# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 5
  cacheKey: 8

"@ampproject/remapping@npm:^2.0.0":
  version: 2.1.0
  resolution: "@ampproject/remapping@npm:2.1.0"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.0
  checksum: 10ff0d4a559f930082f1a4c1b68dc521d5b1a75e0b8ab4829e9eedf6621386893e4a008f0db6c716f64db5d8eed49c0abcfbf3bd6ff11d5a00312454a9351ed4
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.0.0-beta.35, @babel/code-frame@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/code-frame@npm:7.16.7"
  dependencies:
    "@babel/highlight": ^7.16.7
  checksum: db2f7faa31bc2c9cf63197b481b30ea57147a5fc1a6fab60e5d6c02cdfbf6de8e17b5121f99917b3dabb5eeb572da078312e70697415940383efc140d4e0808b
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.13.11, @babel/compat-data@npm:^7.16.4":
  version: 7.17.0
  resolution: "@babel/compat-data@npm:7.17.0"
  checksum: fe5afaf529d107a223cd5937dace248464b6df1e9f4ea4031a5723e9571b46a4db1c4ff226bac6351148b1bc02ba1b39cb142662cd235aa99c1dda77882f8c9d
  languageName: node
  linkType: hard

"@babel/core@npm:7.0.0":
  version: 7.0.0
  resolution: "@babel/core@npm:7.0.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    "@babel/generator": ^7.0.0
    "@babel/helpers": ^7.0.0
    "@babel/parser": ^7.0.0
    "@babel/template": ^7.0.0
    "@babel/traverse": ^7.0.0
    "@babel/types": ^7.0.0
    convert-source-map: ^1.1.0
    debug: ^3.1.0
    json5: ^0.5.0
    lodash: ^4.17.10
    resolve: ^1.3.2
    semver: ^5.4.1
    source-map: ^0.5.0
  checksum: 43172aa88643453ac71810d85b7774a6a56541e0b16844dd2f4b2c2d941498235fee49e7effc4a213d1536f3518c7a0cba87f61dccdd4411d176b3becf157163
  languageName: node
  linkType: hard

"@babel/core@npm:^7.0.0":
  version: 7.17.2
  resolution: "@babel/core@npm:7.17.2"
  dependencies:
    "@ampproject/remapping": ^2.0.0
    "@babel/code-frame": ^7.16.7
    "@babel/generator": ^7.17.0
    "@babel/helper-compilation-targets": ^7.16.7
    "@babel/helper-module-transforms": ^7.16.7
    "@babel/helpers": ^7.17.2
    "@babel/parser": ^7.17.0
    "@babel/template": ^7.16.7
    "@babel/traverse": ^7.17.0
    "@babel/types": ^7.17.0
    convert-source-map: ^1.7.0
    debug: ^4.1.0
    gensync: ^1.0.0-beta.2
    json5: ^2.1.2
    semver: ^6.3.0
  checksum: 68ab3459f41b41feb5cb263937f15e418e1c46998d482d1b6dfe34f78064765466cfd5b10205c22fb16b69dbd1d46e7a3c26c067884ca4eb514b3dac1e09a57f
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.0.0, @babel/generator@npm:^7.17.0":
  version: 7.17.0
  resolution: "@babel/generator@npm:7.17.0"
  dependencies:
    "@babel/types": ^7.17.0
    jsesc: ^2.5.1
    source-map: ^0.5.0
  checksum: 2987dbebb484727a227f1ce3db90810320986cfb3ffd23e6d1d87f75bbd8e7871b5bc44252822d4d5f048a2d872a5702b2a9bf7bab7e07f087d7f306f0ea6c0a
  languageName: node
  linkType: hard

"@babel/helper-annotate-as-pure@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-annotate-as-pure@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: d235be963fed5d48a8a4cfabc41c3f03fad6a947810dbcab9cebed7f819811457e10d99b4b2e942ad71baa7ee8e3cd3f5f38a4e4685639ddfddb7528d9a07179
  languageName: node
  linkType: hard

"@babel/helper-builder-binary-assignment-operator-visitor@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-builder-binary-assignment-operator-visitor@npm:7.16.7"
  dependencies:
    "@babel/helper-explode-assignable-expression": ^7.16.7
    "@babel/types": ^7.16.7
  checksum: 1784f19a57ecfafca8e5c2e0f3eac53451cb13a857cbe0ca0cd9670922228d099ef8c3dd8cd318e2d7bce316fdb2ece3e527c30f3ecd83706e37ab6beb0c60eb
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.13.0, @babel/helper-compilation-targets@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-compilation-targets@npm:7.16.7"
  dependencies:
    "@babel/compat-data": ^7.16.4
    "@babel/helper-validator-option": ^7.16.7
    browserslist: ^4.17.5
    semver: ^6.3.0
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 7238aaee78c011a42fb5ca92e5eff098752f7b314c2111d7bb9cdd58792fcab1b9c819b59f6a0851dc210dc09dc06b30d130a23982753e70eb3111bc65204842
  languageName: node
  linkType: hard

"@babel/helper-create-class-features-plugin@npm:^7.16.7, @babel/helper-create-class-features-plugin@npm:^7.17.1":
  version: 7.17.1
  resolution: "@babel/helper-create-class-features-plugin@npm:7.17.1"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.16.7
    "@babel/helper-environment-visitor": ^7.16.7
    "@babel/helper-function-name": ^7.16.7
    "@babel/helper-member-expression-to-functions": ^7.16.7
    "@babel/helper-optimise-call-expression": ^7.16.7
    "@babel/helper-replace-supers": ^7.16.7
    "@babel/helper-split-export-declaration": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: fb791071dcaa664640d7f1d041772c6b57a8a456720bf7cb21aa055845fad98c644cc7707f03aa94abe8720d19a7c69fd5984fe02fe57b7e99a69f77aa501fc8
  languageName: node
  linkType: hard

"@babel/helper-create-regexp-features-plugin@npm:^7.16.7":
  version: 7.17.0
  resolution: "@babel/helper-create-regexp-features-plugin@npm:7.17.0"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.16.7
    regexpu-core: ^5.0.1
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: eb66d9241544c705e9ce96d2d122b595ef52d926e6e031653e09af8a01050bd9d7e7fee168bf33a863342774d7d6a8cc7e8e9e5a45b955e9c01121c7a2d51708
  languageName: node
  linkType: hard

"@babel/helper-define-polyfill-provider@npm:^0.3.1":
  version: 0.3.1
  resolution: "@babel/helper-define-polyfill-provider@npm:0.3.1"
  dependencies:
    "@babel/helper-compilation-targets": ^7.13.0
    "@babel/helper-module-imports": ^7.12.13
    "@babel/helper-plugin-utils": ^7.13.0
    "@babel/traverse": ^7.13.0
    debug: ^4.1.1
    lodash.debounce: ^4.0.8
    resolve: ^1.14.2
    semver: ^6.1.2
  peerDependencies:
    "@babel/core": ^7.4.0-0
  checksum: e3e93cb22febfc0449a210cdafb278e5e1a038af2ca2b02f5dee71c7a49e8ba26e469d631ee11a4243885961a62bb2e5b0a4deb3ec1d7918a33c953d05c3e584
  languageName: node
  linkType: hard

"@babel/helper-environment-visitor@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-environment-visitor@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: c03a10105d9ebd1fe632a77356b2e6e2f3c44edba9a93b0dc3591b6a66bd7a2e323dd9502f9ce96fc6401234abff1907aa877b6674f7826b61c953f7c8204bbe
  languageName: node
  linkType: hard

"@babel/helper-explode-assignable-expression@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-explode-assignable-expression@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: ea2135ba36da6a2be059ebc8f10fbbb291eb0e312da54c55c6f50f9cbd8601e2406ec497c5e985f7c07a97f31b3bef9b2be8df53f1d53b974043eaf74fe54bbc
  languageName: node
  linkType: hard

"@babel/helper-function-name@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-function-name@npm:7.16.7"
  dependencies:
    "@babel/helper-get-function-arity": ^7.16.7
    "@babel/template": ^7.16.7
    "@babel/types": ^7.16.7
  checksum: fc77cbe7b10cfa2a262d7a37dca575c037f20419dfe0c5d9317f589599ca24beb5f5c1057748011159149eaec47fe32338c6c6412376fcded68200df470161e1
  languageName: node
  linkType: hard

"@babel/helper-get-function-arity@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-get-function-arity@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: 25d969fb207ff2ad5f57a90d118f6c42d56a0171022e200aaa919ba7dc95ae7f92ec71cdea6c63ef3629a0dc962ab4c78e09ca2b437185ab44539193f796e0c3
  languageName: node
  linkType: hard

"@babel/helper-hoist-variables@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-hoist-variables@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: 6ae1641f4a751cd9045346e3f61c3d9ec1312fd779ab6d6fecfe2a96e59a481ad5d7e40d2a840894c13b3fd6114345b157f9e3062fc5f1580f284636e722de60
  languageName: node
  linkType: hard

"@babel/helper-member-expression-to-functions@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-member-expression-to-functions@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: e275378022278a7e7974a3f65566690f1804ac88c5f4e848725cf936f61cd1e2557e88cfb6cb4fea92ae5a95ad89d78dbccc9a53715d4363f84c9fd109272c18
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.0.0, @babel/helper-module-imports@npm:^7.12.13, @babel/helper-module-imports@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-module-imports@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: ddd2c4a600a2e9a4fee192ab92bf35a627c5461dbab4af31b903d9ba4d6b6e59e0ff3499fde4e2e9a0eebe24906f00b636f8b4d9bd72ff24d50e6618215c3212
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-module-transforms@npm:7.16.7"
  dependencies:
    "@babel/helper-environment-visitor": ^7.16.7
    "@babel/helper-module-imports": ^7.16.7
    "@babel/helper-simple-access": ^7.16.7
    "@babel/helper-split-export-declaration": ^7.16.7
    "@babel/helper-validator-identifier": ^7.16.7
    "@babel/template": ^7.16.7
    "@babel/traverse": ^7.16.7
    "@babel/types": ^7.16.7
  checksum: 6e930ce776c979f299cdbeaf80187f4ab086d75287b96ecc1c6896d392fcb561065f0d6219fc06fa79b4ceb4bbdc1a9847da8099aba9b077d0a9e583500fb673
  languageName: node
  linkType: hard

"@babel/helper-optimise-call-expression@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-optimise-call-expression@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: 925feb877d5a30a71db56e2be498b3abbd513831311c0188850896c4c1ada865eea795dce5251a1539b0f883ef82493f057f84286dd01abccc4736acfafe15ea
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.13.0, @babel/helper-plugin-utils@npm:^7.16.7, @babel/helper-plugin-utils@npm:^7.8.0":
  version: 7.16.7
  resolution: "@babel/helper-plugin-utils@npm:7.16.7"
  checksum: d08dd86554a186c2538547cd537552e4029f704994a9201d41d82015c10ed7f58f9036e8d1527c3760f042409163269d308b0b3706589039c5f1884619c6d4ce
  languageName: node
  linkType: hard

"@babel/helper-remap-async-to-generator@npm:^7.16.8":
  version: 7.16.8
  resolution: "@babel/helper-remap-async-to-generator@npm:7.16.8"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.16.7
    "@babel/helper-wrap-function": ^7.16.8
    "@babel/types": ^7.16.8
  checksum: 29282ee36872130085ca111539725abbf20210c2a1d674bee77f338a57c093c3154108d03a275f602e471f583bd2c7ae10d05534f87cbc22b95524fe2b569488
  languageName: node
  linkType: hard

"@babel/helper-replace-supers@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-replace-supers@npm:7.16.7"
  dependencies:
    "@babel/helper-environment-visitor": ^7.16.7
    "@babel/helper-member-expression-to-functions": ^7.16.7
    "@babel/helper-optimise-call-expression": ^7.16.7
    "@babel/traverse": ^7.16.7
    "@babel/types": ^7.16.7
  checksum: e5c0b6eb3dad8410a6255f93b580dde9b3c1564646c6ef751de59d5b2a65b5caa80cc9e568155f04bbae895ad0f54305c2e833dbd971a4f641f970c90b3d892b
  languageName: node
  linkType: hard

"@babel/helper-simple-access@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-simple-access@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: 8d22c46c5ec2ead0686c4d5a3d1d12b5190c59be676bfe0d9d89df62b437b51d1a3df2ccfb8a77dded2e585176ebf12986accb6d45a18cff229eef3b10344f4b
  languageName: node
  linkType: hard

"@babel/helper-skip-transparent-expression-wrappers@npm:^7.16.0":
  version: 7.16.0
  resolution: "@babel/helper-skip-transparent-expression-wrappers@npm:7.16.0"
  dependencies:
    "@babel/types": ^7.16.0
  checksum: b9ed2896eb253e6a85f472b0d4098ed80403758ad1a4e34b02b11e8276e3083297526758b1a3e6886e292987266f10622d7dbced3508cc22b296a74903b41cfb
  languageName: node
  linkType: hard

"@babel/helper-split-export-declaration@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-split-export-declaration@npm:7.16.7"
  dependencies:
    "@babel/types": ^7.16.7
  checksum: e10aaf135465c55114627951b79115f24bc7af72ecbb58d541d66daf1edaee5dde7cae3ec8c3639afaf74526c03ae3ce723444e3b5b3dc77140c456cd84bcaa1
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-validator-identifier@npm:7.16.7"
  checksum: dbb3db9d184343152520a209b5684f5e0ed416109cde82b428ca9c759c29b10c7450657785a8b5c5256aa74acc6da491c1f0cf6b784939f7931ef82982051b69
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/helper-validator-option@npm:7.16.7"
  checksum: c5ccc451911883cc9f12125d47be69434f28094475c1b9d2ada7c3452e6ac98a1ee8ddd364ca9e3f9855fcdee96cdeafa32543ebd9d17fee7a1062c202e80570
  languageName: node
  linkType: hard

"@babel/helper-wrap-function@npm:^7.16.8":
  version: 7.16.8
  resolution: "@babel/helper-wrap-function@npm:7.16.8"
  dependencies:
    "@babel/helper-function-name": ^7.16.7
    "@babel/template": ^7.16.7
    "@babel/traverse": ^7.16.8
    "@babel/types": ^7.16.8
  checksum: d8aae4bacaf138d47dca1421ba82b41eac954cbb0ad17ab1c782825c6f2afe20076fbed926ab265967758336de5112d193a363128cd1c6967c66e0151174f797
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.0.0, @babel/helpers@npm:^7.17.2":
  version: 7.17.2
  resolution: "@babel/helpers@npm:7.17.2"
  dependencies:
    "@babel/template": ^7.16.7
    "@babel/traverse": ^7.17.0
    "@babel/types": ^7.17.0
  checksum: 5fa06bbf59636314fb4098bb2e70cf488e0fb6989553438abab90356357b79976102ac129fb16fc8186893c79e0809de1d90e3304426d6fcdb1750da2b6dff9d
  languageName: node
  linkType: hard

"@babel/highlight@npm:^7.16.7":
  version: 7.16.10
  resolution: "@babel/highlight@npm:7.16.10"
  dependencies:
    "@babel/helper-validator-identifier": ^7.16.7
    chalk: ^2.0.0
    js-tokens: ^4.0.0
  checksum: 1f1bdd752a90844f4efc22166a46303fb651ba0fd75a06daba3ebae2575ab3edc1da9827c279872a3aaf305f50a18473c5fa1966752726a2b253065fd4c0745e
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.0.0, @babel/parser@npm:^7.16.7, @babel/parser@npm:^7.17.0, @babel/parser@npm:^7.7.0":
  version: 7.17.0
  resolution: "@babel/parser@npm:7.17.0"
  bin:
    parser: ./bin/babel-parser.js
  checksum: d0ac5ffba0b234dde516f867edf5da5d92d6f841592b370ae3244cd7c8f27a7f5e3e3d4e90ca9c15ea58bc46823f1643f3f75b6eb9a9f676ae16e8b2365e922a
  languageName: node
  linkType: hard

"@babel/plugin-proposal-async-generator-functions@npm:^7.2.0":
  version: 7.16.8
  resolution: "@babel/plugin-proposal-async-generator-functions@npm:7.16.8"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-remap-async-to-generator": ^7.16.8
    "@babel/plugin-syntax-async-generators": ^7.8.4
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: abd2c2c67de262720d37c5509dafe2ce64d6cee2dc9a8e863bbba1796b77387214442f37618373c6a4521ca624bfc7dcdbeb1376300d16f2a474405ee0ca2e69
  languageName: node
  linkType: hard

"@babel/plugin-proposal-class-properties@npm:^7.0.0":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-class-properties@npm:7.16.7"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3977e841e17b45b47be749b9a5b67b9e8b25ff0840f9fdad3f00cbcb35db4f5ff15f074939fe19b01207a29688c432cc2c682351959350834d62920b7881f803
  languageName: node
  linkType: hard

"@babel/plugin-proposal-decorators@npm:^7.1.0":
  version: 7.17.2
  resolution: "@babel/plugin-proposal-decorators@npm:7.17.2"
  dependencies:
    "@babel/helper-create-class-features-plugin": ^7.17.1
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-replace-supers": ^7.16.7
    "@babel/plugin-syntax-decorators": ^7.17.0
    charcodes: ^0.2.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: da5424d51e49912a1784a7074e8fb7b2d55b4a41c32bf05a829a81987274068e170f469de81d95d177def3480f7de3402a1808d599ad91f98fdaa44023a416da
  languageName: node
  linkType: hard

"@babel/plugin-proposal-json-strings@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-json-strings@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-json-strings": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ea6487918f8d88322ac2a4e5273be6163b0d84a34330c31cee346e23525299de3b4f753bc987951300a79f55b8f4b1971b24d04c0cdfcb7ceb4d636975c215e8
  languageName: node
  linkType: hard

"@babel/plugin-proposal-object-rest-spread@npm:^7.3.4":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-object-rest-spread@npm:7.16.7"
  dependencies:
    "@babel/compat-data": ^7.16.4
    "@babel/helper-compilation-targets": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-object-rest-spread": ^7.8.3
    "@babel/plugin-transform-parameters": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2d3740e4df6d3f51d57862100c45c000104571aa98b7f798fdfc05ae0c12b9e7cc9b55f4a28612d626e29f3369a1481a0ee8a0241b23508b9d3da00c55f99d41
  languageName: node
  linkType: hard

"@babel/plugin-proposal-optional-catch-binding@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-optional-catch-binding@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/plugin-syntax-optional-catch-binding": ^7.8.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4a422bb19a23cf80a245c60bea7adbe5dac8ff3bc1a62f05d7155e1eb68d401b13339c94dfd1f3d272972feeb45746f30d52ca0f8d5c63edf6891340878403df
  languageName: node
  linkType: hard

"@babel/plugin-proposal-unicode-property-regex@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-proposal-unicode-property-regex@npm:7.16.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2b8a33713d456183f0b7d011011e7bd932c08cc06216399a7b2015ab39284b511993dc10a89bbb15d1d728e6a2ef42ca08c3202619aa148cbd48052422ea3995
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.2.0, @babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7ed1c1d9b9e5b64ef028ea5e755c0be2d4e5e4e3d6cf7df757b9a8c4cfa4193d268176d0f1f7fbecdda6fe722885c7fda681f480f3741d8a2d26854736f05367
  languageName: node
  linkType: hard

"@babel/plugin-syntax-decorators@npm:^7.17.0":
  version: 7.17.0
  resolution: "@babel/plugin-syntax-decorators@npm:7.17.0"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 745a3553c8ad4d2ea4805eaf50634cf0cb3036f1259fbfa1cd3cb04d685cec68b6f2f0b3ca1856091730e5aca630975283f9f910d87694141e81754fbc074a7a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-dynamic-import@npm:^7.0.0":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-dynamic-import@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ce307af83cf433d4ec42932329fad25fa73138ab39c7436882ea28742e1c0066626d224e0ad2988724c82644e41601cef607b36194f695cb78a1fcdc959637bd
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.2.0, @babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: bf5aea1f3188c9a507e16efe030efb996853ca3cadd6512c51db7233cc58f3ac89ff8c6bdfb01d30843b161cfe7d321e1bf28da82f7ab8d7e6bc5464666f354a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.0.0, @babel/plugin-syntax-jsx@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-syntax-jsx@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: cd9b0e53c50e8ddb0afaf0f42e0b221a94e4f59aee32a591364266a31195c48cac5fef288d02c1c935686bda982d2e0f1ed61cceb995fc9f6fb09ef5ebecdd2b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.2.0, @babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: fddcf581a57f77e80eb6b981b10658421bc321ba5f0a5b754118c6a92a5448f12a0c336f77b8abf734841e102e5126d69110a306eadb03ca3e1547cab31f5cbf
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.2.0, @babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": ^7.8.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 910d90e72bc90ea1ce698e89c1027fed8845212d5ab588e35ef91f13b93143845f94e2539d831dc8d8ededc14ec02f04f7bd6a8179edd43a326c784e7ed7f0b9
  languageName: node
  linkType: hard

"@babel/plugin-transform-arrow-functions@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-arrow-functions@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2a6aa982c6fc80f4de7ccd973507ce5464fab129987cb6661136a7b9b6a020c2b329b912cbc46a68d39b5a18451ba833dcc8d1ca8d615597fec98624ac2add54
  languageName: node
  linkType: hard

"@babel/plugin-transform-async-to-generator@npm:^7.3.4":
  version: 7.16.8
  resolution: "@babel/plugin-transform-async-to-generator@npm:7.16.8"
  dependencies:
    "@babel/helper-module-imports": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-remap-async-to-generator": ^7.16.8
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3a2e781800e3dea1f526324ed259d1f9064c5ea3c9909c0c22b445d4c648ad489c579f358ae20ada11f7725ba67e0ddeb1e0241efadc734771e87dabd4c6820a
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoped-functions@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-block-scoped-functions@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 591e9f75437bb32ebf9506d28d5c9659c66c0e8e0c19b12924d808d898e68309050aadb783ccd70bb4956555067326ecfa17a402bc77eb3ece3c6863d40b9016
  languageName: node
  linkType: hard

"@babel/plugin-transform-block-scoping@npm:^7.3.4":
  version: 7.16.7
  resolution: "@babel/plugin-transform-block-scoping@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f93b5441af573fc274655f1707aeb4f67a43e926b58f56d89cc35a27877ae0bf198648603cbc19f442579489138f93c3838905895f109aa356996dbc3ed97a68
  languageName: node
  linkType: hard

"@babel/plugin-transform-classes@npm:^7.3.4":
  version: 7.16.7
  resolution: "@babel/plugin-transform-classes@npm:7.16.7"
  dependencies:
    "@babel/helper-annotate-as-pure": ^7.16.7
    "@babel/helper-environment-visitor": ^7.16.7
    "@babel/helper-function-name": ^7.16.7
    "@babel/helper-optimise-call-expression": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-replace-supers": ^7.16.7
    "@babel/helper-split-export-declaration": ^7.16.7
    globals: ^11.1.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 791526a1bf3c4659b94d619536e3181d3ad54887d50539066628c6e695789a3bb264dc1fbc8540169d62a222f623df54defb490c1811ae63bad1e3557d6b3bb0
  languageName: node
  linkType: hard

"@babel/plugin-transform-computed-properties@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-computed-properties@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 28b17f7cfe643f45920b76dc040cab40d4e54eccf5074fba2658c484feacda9b4885b3854ffaf26292189783fdecc97211519c61831b6708fcbf739cfbcbf31c
  languageName: node
  linkType: hard

"@babel/plugin-transform-destructuring@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-destructuring@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d1c2e15e7be2a7c57ac8ec4df06fbb706c7ecc872ab7bc2193606e6d6a01929b6d5a1bb41540e41180e42a5ce0e70dce22e7896cb6578dd581d554f77780971b
  languageName: node
  linkType: hard

"@babel/plugin-transform-dotall-regex@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-dotall-regex@npm:7.16.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 554570dddfd5bfd87ab307be520f69a3d4ed2d2db677c165971b400d4c96656d0c165b318e69f1735612dcd12e04c0ee257697dc26800e8a572ca73bc05fa0f4
  languageName: node
  linkType: hard

"@babel/plugin-transform-duplicate-keys@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-duplicate-keys@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b96f6e9f7b33a91ad0eb6b793e4da58b7a0108b58269109f391d57078d26e043b3872c95429b491894ae6400e72e44d9b744c9b112b8433c99e6969b767e30ed
  languageName: node
  linkType: hard

"@babel/plugin-transform-exponentiation-operator@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-exponentiation-operator@npm:7.16.7"
  dependencies:
    "@babel/helper-builder-binary-assignment-operator-visitor": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 8082c79268f5b1552292bd3abbfed838a1131747e62000146e70670707b518602e907bbe3aef0fda824a2eebe995a9d897bd2336a039c5391743df01608673b0
  languageName: node
  linkType: hard

"@babel/plugin-transform-for-of@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-for-of@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 35c9264ee4bef814818123d70afe8b2f0a85753a0a9dc7b73f93a71cadc5d7de852f1a3e300a7c69a491705805704611de1e2ccceb5686f7828d6bca2e5a7306
  languageName: node
  linkType: hard

"@babel/plugin-transform-function-name@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-function-name@npm:7.16.7"
  dependencies:
    "@babel/helper-compilation-targets": ^7.16.7
    "@babel/helper-function-name": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4d97d0b84461cdd5d5aa2d010cdaf30f1f83a92a0dedd3686cbc7e90dc1249a70246f5bac0c1f3cd3f1dbfb03f7aac437776525a0c90cafd459776ea4fcc6bde
  languageName: node
  linkType: hard

"@babel/plugin-transform-literals@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-literals@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: a9565d999fc7a72a391ef843cf66028c38ca858537c7014d9ea8ea587a59e5f952d9754bdcca6ca0446e84653e297d417d4faedccb9e4221af1aa30f25d918e0
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-amd@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-modules-amd@npm:7.16.7"
  dependencies:
    "@babel/helper-module-transforms": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    babel-plugin-dynamic-import-node: ^2.3.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9ac251ee96183b10cf9b4ec8f9e8d52e14ec186a56103f6c07d0c69e99faa60391f6bac67da733412975e487bd36adb403e2fc99bae6b785bf1413e9d928bc71
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-commonjs@npm:^7.2.0":
  version: 7.16.8
  resolution: "@babel/plugin-transform-modules-commonjs@npm:7.16.8"
  dependencies:
    "@babel/helper-module-transforms": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-simple-access": ^7.16.7
    babel-plugin-dynamic-import-node: ^2.3.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: c0ac00f5457e12cac7825b14725b6fc787bef78945181469ff79f07ef0fd7df021cb00fe1d3a9f35fc9bc92ae59e6e3fc9075a70b627dfe10e00d0907892aace
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-systemjs@npm:^7.3.4":
  version: 7.16.7
  resolution: "@babel/plugin-transform-modules-systemjs@npm:7.16.7"
  dependencies:
    "@babel/helper-hoist-variables": ^7.16.7
    "@babel/helper-module-transforms": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-validator-identifier": ^7.16.7
    babel-plugin-dynamic-import-node: ^2.3.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2e50ae45a725eeafac5a9d30e07a5e17ab8dcf62c3528cf4efe444fc6f12cd3c4e42e911a9aa37abab169687a98b29a4418eeafcf2031f9917162ac36105cb1b
  languageName: node
  linkType: hard

"@babel/plugin-transform-modules-umd@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-modules-umd@npm:7.16.7"
  dependencies:
    "@babel/helper-module-transforms": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d1433f8b0e0b3c9f892aa530f08fe3ba653a5e51fe1ed6034ac7d45d4d6f22c3ba99186b72e41ad9ce5d8dcf964104c3da2419f15fcdcf5ba05c5fda3ea2cefc
  languageName: node
  linkType: hard

"@babel/plugin-transform-named-capturing-groups-regex@npm:^7.3.0":
  version: 7.16.8
  resolution: "@babel/plugin-transform-named-capturing-groups-regex@npm:7.16.8"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 73e149f5ff690f5b8e3764a881e8e5240f12f394256e7d5217705d0cbeae074c3faff394783190fe1a41f9fc5a53b960b6021158b7e5174391b5fc38f4ba047a
  languageName: node
  linkType: hard

"@babel/plugin-transform-new-target@npm:^7.0.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-new-target@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 7410c3e68abc835f87a98d40269e65fb1a05c131decbb6721a80ed49a01bd0c53abb6b8f7f52d5055815509022790e1accca32e975c02f2231ac3cf13d8af768
  languageName: node
  linkType: hard

"@babel/plugin-transform-object-super@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-object-super@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-replace-supers": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 46e3c879f4a93e904f2ecf83233d40c48c832bdbd82a67cab1f432db9aa51702e40d9e51e5800613e12299974f90f4ed3869e1273dbca8642984266320c5f341
  languageName: node
  linkType: hard

"@babel/plugin-transform-parameters@npm:^7.16.7, @babel/plugin-transform-parameters@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-parameters@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 4d6904376db82d0b35f0a6cce08f630daf8608d94e903d6c7aff5bd742b251651bd1f88cdf9f16cad98aba5fc7c61da8635199364865fad6367d5ae37cf56cc1
  languageName: node
  linkType: hard

"@babel/plugin-transform-regenerator@npm:^7.3.4":
  version: 7.16.7
  resolution: "@babel/plugin-transform-regenerator@npm:7.16.7"
  dependencies:
    regenerator-transform: ^0.14.2
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 12b1f9a4f324027af69f49522fbe7feea2ac53285ca5c7e27a70de09f56c74938bfda8b09ac06e57fa1207e441f00efb7adbc462afc9be5e8abd0c2a07715e01
  languageName: node
  linkType: hard

"@babel/plugin-transform-runtime@npm:^7.4.0":
  version: 7.17.0
  resolution: "@babel/plugin-transform-runtime@npm:7.17.0"
  dependencies:
    "@babel/helper-module-imports": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
    babel-plugin-polyfill-corejs2: ^0.3.0
    babel-plugin-polyfill-corejs3: ^0.5.0
    babel-plugin-polyfill-regenerator: ^0.3.0
    semver: ^6.3.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 9a469d4389cb265d50f1e83e6b524ceda7abd24a0bd7cda57e54a1e6103ca7c36efc99eebd485cf0a468f048739e21d940126df40b11db34f4692bdd2d5beacd
  languageName: node
  linkType: hard

"@babel/plugin-transform-shorthand-properties@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-shorthand-properties@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ca381ecf8f48696512172deca40af46b1f64e3497186fdc2c9009286d8f06b468c4d61cdc392dc8b0c165298117dda67be9e2ff0e99d7691b0503f1240d4c62b
  languageName: node
  linkType: hard

"@babel/plugin-transform-spread@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-spread@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
    "@babel/helper-skip-transparent-expression-wrappers": ^7.16.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6e961af1a70586bb72dd85e8296cee857c5dadd73225fccd0fe261c0d98652a82d69c65f3e9dc31ce019a12e9677262678479b96bd2d9140ddf6514618362828
  languageName: node
  linkType: hard

"@babel/plugin-transform-sticky-regex@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-sticky-regex@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d59e20121ff0a483e29364eff8bb42cd8a0b7a3158141eea5b6f219227e5b873ea70f317f65037c0f557887a692ac993b72f99641a37ea6ec0ae8000bfab1343
  languageName: node
  linkType: hard

"@babel/plugin-transform-template-literals@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-template-literals@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: b55a519dd8b957247ebad3cab21918af5adca4f6e6c87819501cfe3d4d4bccda25bc296c7dfc8a30909b4ad905902aeb9d55ad955cb9f5cbc74b42dab32baa18
  languageName: node
  linkType: hard

"@babel/plugin-transform-typeof-symbol@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-typeof-symbol@npm:7.16.7"
  dependencies:
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 739a8c439dacbd9af62cfbfa0a7cbc3f220849e5fc774e5ef708a09186689a724c41a1d11323e7d36588d24f5481c8b702c86ff7be8da2e2fed69bed0175f625
  languageName: node
  linkType: hard

"@babel/plugin-transform-unicode-regex@npm:^7.2.0":
  version: 7.16.7
  resolution: "@babel/plugin-transform-unicode-regex@npm:7.16.7"
  dependencies:
    "@babel/helper-create-regexp-features-plugin": ^7.16.7
    "@babel/helper-plugin-utils": ^7.16.7
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ef7721cfb11b269809555b1c392732566c49f6ced58e0e990c0e81e58a934bbab3072dcbe92d3a20d60e3e41036ecf987bcc63a7cde90711a350ad774667e5e6
  languageName: node
  linkType: hard

"@babel/preset-env@npm:^7.0.0 < 7.4.0":
  version: 7.3.4
  resolution: "@babel/preset-env@npm:7.3.4"
  dependencies:
    "@babel/helper-module-imports": ^7.0.0
    "@babel/helper-plugin-utils": ^7.0.0
    "@babel/plugin-proposal-async-generator-functions": ^7.2.0
    "@babel/plugin-proposal-json-strings": ^7.2.0
    "@babel/plugin-proposal-object-rest-spread": ^7.3.4
    "@babel/plugin-proposal-optional-catch-binding": ^7.2.0
    "@babel/plugin-proposal-unicode-property-regex": ^7.2.0
    "@babel/plugin-syntax-async-generators": ^7.2.0
    "@babel/plugin-syntax-json-strings": ^7.2.0
    "@babel/plugin-syntax-object-rest-spread": ^7.2.0
    "@babel/plugin-syntax-optional-catch-binding": ^7.2.0
    "@babel/plugin-transform-arrow-functions": ^7.2.0
    "@babel/plugin-transform-async-to-generator": ^7.3.4
    "@babel/plugin-transform-block-scoped-functions": ^7.2.0
    "@babel/plugin-transform-block-scoping": ^7.3.4
    "@babel/plugin-transform-classes": ^7.3.4
    "@babel/plugin-transform-computed-properties": ^7.2.0
    "@babel/plugin-transform-destructuring": ^7.2.0
    "@babel/plugin-transform-dotall-regex": ^7.2.0
    "@babel/plugin-transform-duplicate-keys": ^7.2.0
    "@babel/plugin-transform-exponentiation-operator": ^7.2.0
    "@babel/plugin-transform-for-of": ^7.2.0
    "@babel/plugin-transform-function-name": ^7.2.0
    "@babel/plugin-transform-literals": ^7.2.0
    "@babel/plugin-transform-modules-amd": ^7.2.0
    "@babel/plugin-transform-modules-commonjs": ^7.2.0
    "@babel/plugin-transform-modules-systemjs": ^7.3.4
    "@babel/plugin-transform-modules-umd": ^7.2.0
    "@babel/plugin-transform-named-capturing-groups-regex": ^7.3.0
    "@babel/plugin-transform-new-target": ^7.0.0
    "@babel/plugin-transform-object-super": ^7.2.0
    "@babel/plugin-transform-parameters": ^7.2.0
    "@babel/plugin-transform-regenerator": ^7.3.4
    "@babel/plugin-transform-shorthand-properties": ^7.2.0
    "@babel/plugin-transform-spread": ^7.2.0
    "@babel/plugin-transform-sticky-regex": ^7.2.0
    "@babel/plugin-transform-template-literals": ^7.2.0
    "@babel/plugin-transform-typeof-symbol": ^7.2.0
    "@babel/plugin-transform-unicode-regex": ^7.2.0
    browserslist: ^4.3.4
    invariant: ^2.2.2
    js-levenshtein: ^1.1.3
    semver: ^5.3.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 83141f76123f1ca9ec6d3f7667e717c8af58216be8b06c5cefaccb65bc5dc60a896bb90ffd658888b8abee2fee792546d4573d26e6e5c621b24dbfcb2de77948
  languageName: node
  linkType: hard

"@babel/register@npm:7.0.0":
  version: 7.0.0
  resolution: "@babel/register@npm:7.0.0"
  dependencies:
    core-js: ^2.5.7
    find-cache-dir: ^1.0.0
    home-or-tmp: ^3.0.0
    lodash: ^4.17.10
    mkdirp: ^0.5.1
    pirates: ^4.0.0
    source-map-support: ^0.5.9
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 5f8dffc163bd93734af9926835c4aef8886925f6c238b0c11f1dbd0ea46511636af61ec5eabac1f9b62d0c627af240342088879f813164012406c9d287d140af
  languageName: node
  linkType: hard

"@babel/runtime-corejs2@npm:^7.2.0":
  version: 7.17.2
  resolution: "@babel/runtime-corejs2@npm:7.17.2"
  dependencies:
    core-js: ^2.6.5
    regenerator-runtime: ^0.13.4
  checksum: 4012fa17146a1093a3e742e869ea7ada6e70a4b788e63c7d280db8c049132a1f99d6f0085848c75477adc571ca9bbd12be38cd1d326484877df0c48996482a40
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.0.0, @babel/runtime@npm:^7.8.4":
  version: 7.17.2
  resolution: "@babel/runtime@npm:7.17.2"
  dependencies:
    regenerator-runtime: ^0.13.4
  checksum: a48702d271ecc59c09c397856407afa29ff980ab537b3da58eeee1aeaa0f545402d340a1680c9af58aec94dfdcbccfb6abb211991b74686a86d03d3f6956cacd
  languageName: node
  linkType: hard

"@babel/template@npm:^7.0.0, @babel/template@npm:^7.16.7":
  version: 7.16.7
  resolution: "@babel/template@npm:7.16.7"
  dependencies:
    "@babel/code-frame": ^7.16.7
    "@babel/parser": ^7.16.7
    "@babel/types": ^7.16.7
  checksum: 10cd112e89276e00f8b11b55a51c8b2f1262c318283a980f4d6cdb0286dc05734b9aaeeb9f3ad3311900b09bc913e02343fcaa9d4a4f413964aaab04eb84ac4a
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.0.0, @babel/traverse@npm:^7.13.0, @babel/traverse@npm:^7.16.7, @babel/traverse@npm:^7.16.8, @babel/traverse@npm:^7.17.0, @babel/traverse@npm:^7.7.0":
  version: 7.17.0
  resolution: "@babel/traverse@npm:7.17.0"
  dependencies:
    "@babel/code-frame": ^7.16.7
    "@babel/generator": ^7.17.0
    "@babel/helper-environment-visitor": ^7.16.7
    "@babel/helper-function-name": ^7.16.7
    "@babel/helper-hoist-variables": ^7.16.7
    "@babel/helper-split-export-declaration": ^7.16.7
    "@babel/parser": ^7.17.0
    "@babel/types": ^7.17.0
    debug: ^4.1.0
    globals: ^11.1.0
  checksum: 9b7de053d8a29453fd7b9614a028d8dc811817f02948eaee02093274b67927a1cfb0513b521bc4a9328c9b6e5b021fd343b358c3526bbb6ee61ec078d4110c0c
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.16.0, @babel/types@npm:^7.16.7, @babel/types@npm:^7.16.8, @babel/types@npm:^7.17.0, @babel/types@npm:^7.7.0, @babel/types@npm:^7.8.3":
  version: 7.17.0
  resolution: "@babel/types@npm:7.17.0"
  dependencies:
    "@babel/helper-validator-identifier": ^7.16.7
    to-fast-properties: ^2.0.0
  checksum: 12e5a287986fe557188e87b2c5202223f1dc83d9239a196ab936fdb9f8c1eb0be717ff19f934b5fad4e29a75586d5798f74bed209bccea1c20376b9952056f0e
  languageName: node
  linkType: hard

"@gar/promisify@npm:^1.0.1":
  version: 1.1.2
  resolution: "@gar/promisify@npm:1.1.2"
  checksum: d05081e0887a49c178b75ee3067bd6ee086f73c154d121b854fb2e044e8a89cb1cbb6de3a0dd93a519b80f0531fda68b099dd7256205f7fbb3490324342f2217
  languageName: node
  linkType: hard

"@hapi/address@npm:2.x.x":
  version: 2.1.4
  resolution: "@hapi/address@npm:2.1.4"
  checksum: 10341c3b650746c79ac2c57118efb05d45d850b33aef82a6f2ba89419fdbf1b6d337c8b085cc9bc1af7a5fb18379c07edaf3be7584426f40bd370ed6de29e965
  languageName: node
  linkType: hard

"@hapi/bourne@npm:1.x.x":
  version: 1.3.2
  resolution: "@hapi/bourne@npm:1.3.2"
  checksum: 8403a2e8297fbb49a0e4fca30e874590d96ecaf7165740804037ff30625f3fdea6353d9f7f4422607eb069a3f471900a3037df34285a95135d15c6a8b10094b0
  languageName: node
  linkType: hard

"@hapi/hoek@npm:8.x.x, @hapi/hoek@npm:^8.3.0":
  version: 8.5.1
  resolution: "@hapi/hoek@npm:8.5.1"
  checksum: 8f8ce36be4f5e5d7a712072d4a028a4a95beec7a7da3a7a6e9a0c07d697f04c19b37d93751db352c314ea831f7e2120569a035dc6a351ed8c0444f1d3b275621
  languageName: node
  linkType: hard

"@hapi/joi@npm:^15.0.1":
  version: 15.1.1
  resolution: "@hapi/joi@npm:15.1.1"
  dependencies:
    "@hapi/address": 2.x.x
    "@hapi/bourne": 1.x.x
    "@hapi/hoek": 8.x.x
    "@hapi/topo": 3.x.x
  checksum: 5bc3df9d43d4a53c41fd172d2958a4a846dbacbe2a2abe16830059109c436776d7be98144f14af9d328ebd107dbebafe55e00a9032a905aef45aadff988b54bf
  languageName: node
  linkType: hard

"@hapi/topo@npm:3.x.x":
  version: 3.1.6
  resolution: "@hapi/topo@npm:3.1.6"
  dependencies:
    "@hapi/hoek": ^8.3.0
  checksum: 34278bc13b4023d6d0d7c913605a254b2d728dc6489cd465269eebaa7d8d2d81cda3f2157398dca87d3cb9e1a8aa8a1158ce2564c71a8e289b807c144e3b4c1e
  languageName: node
  linkType: hard

"@intervolga/optimize-cssnano-plugin@npm:^1.0.5":
  version: 1.0.6
  resolution: "@intervolga/optimize-cssnano-plugin@npm:1.0.6"
  dependencies:
    cssnano: ^4.0.0
    cssnano-preset-default: ^4.0.0
    postcss: ^7.0.0
  peerDependencies:
    webpack: ^4.0.0
  checksum: 85d109afb5f069cf53f2af2048828afde197e9fc3e3c4172ae7dbbb99659cd74d0c30871b874e9159f3ce7e8998e2679abfb895185bc7b3a3463ed2c3d75e543
  languageName: node
  linkType: hard

"@jest/console@npm:^24.9.0":
  version: 24.9.0
  resolution: "@jest/console@npm:24.9.0"
  dependencies:
    "@jest/source-map": ^24.9.0
    chalk: ^2.0.1
    slash: ^2.0.0
  checksum: ee6468c4aeeb8752126e92e20b0ffbf32abda731e9b7865b63b60bd569c3536e9c901efcec4d81c506a7c6fea2a970ace8262190961aba31dedbfeaa3459d78b
  languageName: node
  linkType: hard

"@jest/fake-timers@npm:^24.9.0":
  version: 24.9.0
  resolution: "@jest/fake-timers@npm:24.9.0"
  dependencies:
    "@jest/types": ^24.9.0
    jest-message-util: ^24.9.0
    jest-mock: ^24.9.0
  checksum: d49ab33e28b070d5be75659ed89d4b79e74012c8c28ecf51cf9b89732ba5b2a57129787dd144949c048a0460ed62f1e32079a4b10d896c75bde024699d7a2c5c
  languageName: node
  linkType: hard

"@jest/source-map@npm:^24.9.0":
  version: 24.9.0
  resolution: "@jest/source-map@npm:24.9.0"
  dependencies:
    callsites: ^3.0.0
    graceful-fs: ^4.1.15
    source-map: ^0.6.0
  checksum: 00479faf6854d5d183b94465db1a0876980ced72bf26cb6a2fe8c04977dc2692e6529faa6b64269492d1d9cab51feebaac9d453d1e6bb1306fc15777143b72af
  languageName: node
  linkType: hard

"@jest/test-result@npm:^24.9.0":
  version: 24.9.0
  resolution: "@jest/test-result@npm:24.9.0"
  dependencies:
    "@jest/console": ^24.9.0
    "@jest/types": ^24.9.0
    "@types/istanbul-lib-coverage": ^2.0.0
  checksum: 7145c7baa289798881160b3cfa5b2466b2636238a52b77cf46e5468ffe2881fb8fb8d4966155a8d508b26a8d29a302a9eb9037de1a371e5dc9bb6e94837c0ae7
  languageName: node
  linkType: hard

"@jest/types@npm:^24.9.0":
  version: 24.9.0
  resolution: "@jest/types@npm:24.9.0"
  dependencies:
    "@types/istanbul-lib-coverage": ^2.0.0
    "@types/istanbul-reports": ^1.1.1
    "@types/yargs": ^13.0.0
  checksum: 603698f774cf22f9d16a0e0fac9e10e7db21052aebfa33db154c8a5940e0eb1fa9c079a8c91681041ad3aeee2adfa950608dd0c663130316ba034b8bca7b301c
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.0.3":
  version: 3.0.4
  resolution: "@jridgewell/resolve-uri@npm:3.0.4"
  checksum: 799bcba2730280a42f11b4d41a5d34d68ce72cb1bd23186bd3356607c93b62765b2b050e5dfb67f04ce4e817f882bfc10a4d1c43fe2d8eeb38371c98d71217b4
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10":
  version: 1.4.10
  resolution: "@jridgewell/sourcemap-codec@npm:1.4.10"
  checksum: 247229218edbe165dcf0a5ae0c4b81bff1b5438818bb09221f756681fe158597fdf25c2a803f9260453b299c98c7e01ddebeb1555cda3157d987cd22c08605ef
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.0":
  version: 0.3.2
  resolution: "@jridgewell/trace-mapping@npm:0.3.2"
  dependencies:
    "@jridgewell/resolve-uri": ^3.0.3
    "@jridgewell/sourcemap-codec": ^1.4.10
  checksum: b58be6b4133cbcb20bfd28c9ca843b8db9efa0bf1d7e0e9e26b2228dace94ad53161c996ab1d762d7c3955dfc398a7734e7b84a2493ae36b451f232234fbb257
  languageName: node
  linkType: hard

"@mrmlnc/readdir-enhanced@npm:^2.2.1":
  version: 2.2.1
  resolution: "@mrmlnc/readdir-enhanced@npm:2.2.1"
  dependencies:
    call-me-maybe: ^1.0.1
    glob-to-regexp: ^0.3.0
  checksum: d3b82b29368821154ce8e10bef5ccdbfd070d3e9601643c99ea4607e56f3daeaa4e755dd6d2355da20762c695c1b0570543d9f84b48f70c211ec09c4aaada2e1
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:^1.1.2":
  version: 1.1.3
  resolution: "@nodelib/fs.stat@npm:1.1.3"
  checksum: 318deab369b518a34778cdaa0054dd28a4381c0c78e40bbd20252f67d084b1d7bf9295fea4423de2c19ac8e1a34f120add9125f481b2a710f7068bcac7e3e305
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^1.0.0":
  version: 1.1.0
  resolution: "@npmcli/fs@npm:1.1.0"
  dependencies:
    "@gar/promisify": ^1.0.1
    semver: ^7.3.5
  checksum: e435b883b4f8da8c95a820f458cabb7d86582406eed5ad79fc689000d3e2df17e1f475c4903627272c001357cabc70d8b4c62520cbdae8cfab1dfdd51949f408
  languageName: node
  linkType: hard

"@npmcli/move-file@npm:^1.0.1":
  version: 1.1.2
  resolution: "@npmcli/move-file@npm:1.1.2"
  dependencies:
    mkdirp: ^1.0.4
    rimraf: ^3.0.2
  checksum: c96381d4a37448ea280951e46233f7e541058cf57a57d4094dd4bdcaae43fa5872b5f2eb6bfb004591a68e29c5877abe3cdc210cb3588cbf20ab2877f31a7de7
  languageName: node
  linkType: hard

"@soda/friendly-errors-webpack-plugin@npm:^1.7.1":
  version: 1.8.1
  resolution: "@soda/friendly-errors-webpack-plugin@npm:1.8.1"
  dependencies:
    chalk: ^3.0.0
    error-stack-parser: ^2.0.6
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: 397f31580f088460472abbb61a49551d4ff60c0bf949f3cb4667e013a3b7f4575aa2b5a7dcf8a3d5a3862f043eef93a19ad60fd3f4b7cfd0cc8128d95a202479
  languageName: node
  linkType: hard

"@tootallnate/once@npm:1":
  version: 1.1.2
  resolution: "@tootallnate/once@npm:1.1.2"
  checksum: e1fb1bbbc12089a0cb9433dc290f97bddd062deadb6178ce9bcb93bb7c1aecde5e60184bc7065aec42fe1663622a213493c48bbd4972d931aae48315f18e1be9
  languageName: node
  linkType: hard

"@types/glob@npm:^7.1.1":
  version: 7.2.0
  resolution: "@types/glob@npm:7.2.0"
  dependencies:
    "@types/minimatch": "*"
    "@types/node": "*"
  checksum: 6ae717fedfdfdad25f3d5a568323926c64f52ef35897bcac8aca8e19bc50c0bd84630bbd063e5d52078b2137d8e7d3c26eabebd1a2f03ff350fff8a91e79fc19
  languageName: node
  linkType: hard

"@types/istanbul-lib-coverage@npm:*, @types/istanbul-lib-coverage@npm:^2.0.0":
  version: 2.0.4
  resolution: "@types/istanbul-lib-coverage@npm:2.0.4"
  checksum: a25d7589ee65c94d31464c16b72a9dc81dfa0bea9d3e105ae03882d616e2a0712a9c101a599ec482d297c3591e16336962878cb3eb1a0a62d5b76d277a890ce7
  languageName: node
  linkType: hard

"@types/istanbul-lib-report@npm:*":
  version: 3.0.0
  resolution: "@types/istanbul-lib-report@npm:3.0.0"
  dependencies:
    "@types/istanbul-lib-coverage": "*"
  checksum: 656398b62dc288e1b5226f8880af98087233cdb90100655c989a09f3052b5775bf98ba58a16c5ae642fb66c61aba402e07a9f2bff1d1569e3b306026c59f3f36
  languageName: node
  linkType: hard

"@types/istanbul-reports@npm:^1.1.1":
  version: 1.1.2
  resolution: "@types/istanbul-reports@npm:1.1.2"
  dependencies:
    "@types/istanbul-lib-coverage": "*"
    "@types/istanbul-lib-report": "*"
  checksum: 00866e815d1e68d0a590d691506937b79d8d65ad8eab5ed34dbfee66136c7c0f4ea65327d32046d5fe469f22abea2b294987591dc66365ebc3991f7e413b2d78
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.5":
  version: 7.0.9
  resolution: "@types/json-schema@npm:7.0.9"
  checksum: 259d0e25f11a21ba5c708f7ea47196bd396e379fddb79c76f9f4f62c945879dc21657904914313ec2754e443c5018ea8372362f323f30e0792897fdb2098a705
  languageName: node
  linkType: hard

"@types/minimatch@npm:*":
  version: 3.0.5
  resolution: "@types/minimatch@npm:3.0.5"
  checksum: c41d136f67231c3131cf1d4ca0b06687f4a322918a3a5adddc87ce90ed9dbd175a3610adee36b106ae68c0b92c637c35e02b58c8a56c424f71d30993ea220b92
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 17.0.16
  resolution: "@types/node@npm:17.0.16"
  checksum: 2d452a9e0ee7ec83d447d66dd2baf4dd05ef2fbcda39b06f73d86b36d5150d86c43a733d534d77467e7782104756944d42a35ed4b64113bda341b8334ee4d01d
  languageName: node
  linkType: hard

"@types/normalize-package-data@npm:^2.4.0":
  version: 2.4.1
  resolution: "@types/normalize-package-data@npm:2.4.1"
  checksum: e87bccbf11f95035c89a132b52b79ce69a1e3652fe55962363063c9c0dae0fe2477ebc585e03a9652adc6f381d24ba5589cc5e51849df4ced3d3e004a7d40ed5
  languageName: node
  linkType: hard

"@types/q@npm:^1.5.1":
  version: 1.5.5
  resolution: "@types/q@npm:1.5.5"
  checksum: 3bd386fb97a0e5f1ce1ed7a14e39b60e469b5ca9d920a7f69e0cdb58d22c0f5bdd16637d8c3a5bfeda76663c023564dd47a65389ee9aaabd65aee54803d5ba45
  languageName: node
  linkType: hard

"@types/stack-utils@npm:^1.0.1":
  version: 1.0.1
  resolution: "@types/stack-utils@npm:1.0.1"
  checksum: 9dc052b575acfeca3f165fb19d87b7b2989d54ed7d64a7eeb0b7587bc5795ef1f2c2b1511a44dcf0831ef35b8ce3486f97fcbfdd50c01f68aa297de31502c9d9
  languageName: node
  linkType: hard

"@types/strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "@types/strip-bom@npm:3.0.0"
  checksum: cb165d0c2ce6abbef95506ebee25be02bd453600ef1792dc1754236e5d6f9c830d52bdb85978d0b08ea1f36b96a61235ac5ad99e0f4c2767fb4ea004e141d2df
  languageName: node
  linkType: hard

"@types/strip-json-comments@npm:0.0.30":
  version: 0.0.30
  resolution: "@types/strip-json-comments@npm:0.0.30"
  checksum: 829ddd389645073f347c5b1924a8c34b8813af29756576e511c46f40e218193cf93ccbade62661d47fc70f707e98f410331729b8c20edfcb2e807d51df1ad4b7
  languageName: node
  linkType: hard

"@types/yargs-parser@npm:*":
  version: 20.2.1
  resolution: "@types/yargs-parser@npm:20.2.1"
  checksum: 1d039e64494a7a61ddd278349a3dc60b19f99ff0517425696e796f794e4252452b9d62178e69755ad03f439f9dc0c8c3d7b3a1201b3a24e134bac1a09fa11eaa
  languageName: node
  linkType: hard

"@types/yargs@npm:^13.0.0":
  version: 13.0.12
  resolution: "@types/yargs@npm:13.0.12"
  dependencies:
    "@types/yargs-parser": "*"
  checksum: 4eb34d8c071892299646e5a3fb02a643f5a5ea8da8f4d1817001882ebbcfa4fbda235b8978732f8eb55fa16433296e2087907fe69678a69125f0dca627a91426
  languageName: node
  linkType: hard

"@vue/babel-helper-vue-jsx-merge-props@npm:^1.2.1":
  version: 1.2.1
  resolution: "@vue/babel-helper-vue-jsx-merge-props@npm:1.2.1"
  checksum: 1fad42dfb93d6d008705f6d302df202596ebbf6a910f50627e42ea66efa624b828cbdc4f626937500e9c92dc31c59e3f1e19206e4b388e8464e64afe863354f4
  languageName: node
  linkType: hard

"@vue/babel-plugin-transform-vue-jsx@npm:^1.2.1":
  version: 1.2.1
  resolution: "@vue/babel-plugin-transform-vue-jsx@npm:1.2.1"
  dependencies:
    "@babel/helper-module-imports": ^7.0.0
    "@babel/plugin-syntax-jsx": ^7.2.0
    "@vue/babel-helper-vue-jsx-merge-props": ^1.2.1
    html-tags: ^2.0.0
    lodash.kebabcase: ^4.1.1
    svg-tags: ^1.0.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d00b7b02ce73b434c44e7068dc1f3d81037511fa3b99f9ae4105224ce78b99619b9c8e4c47aba55a41e2844478e2c27a96fc8b667aa94d11c7d50e77ca82aa87
  languageName: node
  linkType: hard

"@vue/babel-preset-app@npm:^3.6.0":
  version: 3.12.1
  resolution: "@vue/babel-preset-app@npm:3.12.1"
  dependencies:
    "@babel/helper-module-imports": ^7.0.0
    "@babel/plugin-proposal-class-properties": ^7.0.0
    "@babel/plugin-proposal-decorators": ^7.1.0
    "@babel/plugin-syntax-dynamic-import": ^7.0.0
    "@babel/plugin-syntax-jsx": ^7.0.0
    "@babel/plugin-transform-runtime": ^7.4.0
    "@babel/preset-env": ^7.0.0 < 7.4.0
    "@babel/runtime": ^7.0.0
    "@babel/runtime-corejs2": ^7.2.0
    "@vue/babel-preset-jsx": ^1.0.0
    babel-plugin-dynamic-import-node: ^2.2.0
    babel-plugin-module-resolver: 3.2.0
    core-js: ^2.6.5
  checksum: 0d6817f51a63f8e87b66e9801db3a16bab07c67d70fa658f0dc3a9a6fb3c3f8da2404a802151372f9f9f2294dacc932ee43d1e4869f01df7f40459187112fa83
  languageName: node
  linkType: hard

"@vue/babel-preset-jsx@npm:^1.0.0":
  version: 1.2.4
  resolution: "@vue/babel-preset-jsx@npm:1.2.4"
  dependencies:
    "@vue/babel-helper-vue-jsx-merge-props": ^1.2.1
    "@vue/babel-plugin-transform-vue-jsx": ^1.2.1
    "@vue/babel-sugar-composition-api-inject-h": ^1.2.1
    "@vue/babel-sugar-composition-api-render-instance": ^1.2.4
    "@vue/babel-sugar-functional-vue": ^1.2.2
    "@vue/babel-sugar-inject-h": ^1.2.2
    "@vue/babel-sugar-v-model": ^1.2.3
    "@vue/babel-sugar-v-on": ^1.2.3
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 304b521ba9ac143c797f28675d2f1542e686a78dd64417ef184b34f2fecd960bdd9d2fcdcfbb3a558cb95e773dd3853b96fd7a1daa7c8c73d554a54616f154b7
  languageName: node
  linkType: hard

"@vue/babel-sugar-composition-api-inject-h@npm:^1.2.1":
  version: 1.2.1
  resolution: "@vue/babel-sugar-composition-api-inject-h@npm:1.2.1"
  dependencies:
    "@babel/plugin-syntax-jsx": ^7.2.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f3a6fa8b9626acf11c2a28ac13056904cb09a1bc58a98d06483b549d4a0aa690e48b6ebe7305fa81fa9f4ccd1519f880b8b14e876fc28c91c202c7a7fc7b1bd8
  languageName: node
  linkType: hard

"@vue/babel-sugar-composition-api-render-instance@npm:^1.2.4":
  version: 1.2.4
  resolution: "@vue/babel-sugar-composition-api-render-instance@npm:1.2.4"
  dependencies:
    "@babel/plugin-syntax-jsx": ^7.2.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 73e86378222a6e2aaf91e6bcfacf40208195c7c8cad616f083fb61fd07123a3a159f47bd4439ff3d679d340f228e4b915eaebfcf026d0f6e9a5deb0a87a99751
  languageName: node
  linkType: hard

"@vue/babel-sugar-functional-vue@npm:^1.2.2":
  version: 1.2.2
  resolution: "@vue/babel-sugar-functional-vue@npm:1.2.2"
  dependencies:
    "@babel/plugin-syntax-jsx": ^7.2.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: d188419e891e712de4865104ae205cee18a6b98e090628fe125e0744b1bb0982db1516d63c6b67bf4d7acb11960407cd3838b4285f4a23ea559a47b249d67b46
  languageName: node
  linkType: hard

"@vue/babel-sugar-inject-h@npm:^1.2.2":
  version: 1.2.2
  resolution: "@vue/babel-sugar-inject-h@npm:1.2.2"
  dependencies:
    "@babel/plugin-syntax-jsx": ^7.2.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 0fe63c4c06c7f4709b3ed0d9756d6830257f53276a3d417ce827dba376d4ae8dd5d647134550624a0f8c58d0fdd3fd662bfc8347d192081640f4c7d584a90bd2
  languageName: node
  linkType: hard

"@vue/babel-sugar-v-model@npm:^1.2.3":
  version: 1.2.3
  resolution: "@vue/babel-sugar-v-model@npm:1.2.3"
  dependencies:
    "@babel/plugin-syntax-jsx": ^7.2.0
    "@vue/babel-helper-vue-jsx-merge-props": ^1.2.1
    "@vue/babel-plugin-transform-vue-jsx": ^1.2.1
    camelcase: ^5.0.0
    html-tags: ^2.0.0
    svg-tags: ^1.0.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 6af4d076cedecf1f23577088f8f62e987fb936f9cfadb3ab3465b8605ca313bd4549169536efb2aed7ebd972b1b04d38297a96bc65b40021356e184dfc9043a5
  languageName: node
  linkType: hard

"@vue/babel-sugar-v-on@npm:^1.2.3":
  version: 1.2.3
  resolution: "@vue/babel-sugar-v-on@npm:1.2.3"
  dependencies:
    "@babel/plugin-syntax-jsx": ^7.2.0
    "@vue/babel-plugin-transform-vue-jsx": ^1.2.1
    camelcase: ^5.0.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 3c7330502f02b318b37ed6a2827f3c4caa6af02f73cea3e80b3c640be38d610d8813cb079e6f8d4a73728e269eb50518d58b26900223a408aee90590205b1de3
  languageName: node
  linkType: hard

"@vue/cli-overlay@npm:^3.6.0":
  version: 3.12.1
  resolution: "@vue/cli-overlay@npm:3.12.1"
  checksum: 7f4ff072e653b477d68847136d116ac9d54b64246ed3bc1092d9cb21fdfcdc2052eb7a4f80f6e82dfe9e5fca7b069dd4850bfc2ad5c73a8bc6cacc5d6f441f2f
  languageName: node
  linkType: hard

"@vue/cli-plugin-babel@npm:3.6.0":
  version: 3.6.0
  resolution: "@vue/cli-plugin-babel@npm:3.6.0"
  dependencies:
    "@babel/core": ^7.0.0
    "@vue/babel-preset-app": ^3.6.0
    "@vue/cli-shared-utils": ^3.6.0
    babel-loader: ^8.0.5
    webpack: ">=4 < 4.29"
  checksum: ca1d09262a13ac8b58f168af584d1c06bbec6ccd8ac76e4e79a64fd8ec9e76b901f525433ca30654c499ef470c375772085747716d65e21fbe93dbe8e2f2db34
  languageName: node
  linkType: hard

"@vue/cli-plugin-eslint@npm:^3.9.1":
  version: 3.12.1
  resolution: "@vue/cli-plugin-eslint@npm:3.12.1"
  dependencies:
    "@vue/cli-shared-utils": ^3.12.1
    babel-eslint: ^10.0.1
    eslint: ^4.19.1
    eslint-loader: ^2.1.2
    eslint-plugin-vue: ^4.7.1
    globby: ^9.2.0
    webpack: ^4.0.0
    yorkie: ^2.0.0
  dependenciesMeta:
    eslint:
      optional: true
    eslint-plugin-vue:
      optional: true
  checksum: f19bd45ad5358a3c6196c41ea04d058227e87207042523e7491a186e4bf8fae17367315578b214941645e91d6fca11d087e652c3926b7c8166768c98833c8c32
  languageName: node
  linkType: hard

"@vue/cli-plugin-unit-jest@npm:3.6.3":
  version: 3.6.3
  resolution: "@vue/cli-plugin-unit-jest@npm:3.6.3"
  dependencies:
    "@vue/cli-shared-utils": ^3.6.0
    babel-jest: ^23.6.0
    babel-plugin-transform-es2015-modules-commonjs: ^6.26.2
    jest: ^23.6.0
    jest-serializer-vue: ^2.0.2
    jest-transform-stub: ^2.0.0
    jest-watch-typeahead: ^0.3.0
    vue-jest: ^3.0.4
  checksum: 2cba5e69e0e4d79aef62abb9bdd9eba8f423d271861cb4659bd215b83fc6581736e37d7374eedd651c0c6014d0fbd3c5ecea532ae271e2582f67b7924f9b4bda
  languageName: node
  linkType: hard

"@vue/cli-service@npm:3.6.0":
  version: 3.6.0
  resolution: "@vue/cli-service@npm:3.6.0"
  dependencies:
    "@intervolga/optimize-cssnano-plugin": ^1.0.5
    "@soda/friendly-errors-webpack-plugin": ^1.7.1
    "@vue/cli-overlay": ^3.6.0
    "@vue/cli-shared-utils": ^3.6.0
    "@vue/component-compiler-utils": ^2.6.0
    "@vue/preload-webpack-plugin": ^1.1.0
    "@vue/web-component-wrapper": ^1.2.0
    acorn: ^6.1.1
    acorn-walk: ^6.1.1
    address: ^1.0.3
    autoprefixer: ^9.5.1
    browserslist: ^4.5.4
    cache-loader: ^2.0.1
    case-sensitive-paths-webpack-plugin: ^2.2.0
    chalk: ^2.4.2
    clipboardy: ^2.0.0
    cliui: ^5.0.0
    copy-webpack-plugin: ^4.6.0
    css-loader: ^1.0.1
    cssnano: ^4.1.10
    current-script-polyfill: ^1.0.0
    debug: ^4.1.1
    dotenv: ^7.0.0
    dotenv-expand: ^5.1.0
    escape-string-regexp: ^1.0.5
    file-loader: ^3.0.1
    fs-extra: ^7.0.1
    globby: ^9.2.0
    hash-sum: ^1.0.2
    html-webpack-plugin: ^3.2.0
    launch-editor-middleware: ^2.2.1
    lodash.defaultsdeep: ^4.6.0
    lodash.mapvalues: ^4.6.0
    lodash.transform: ^4.6.0
    mini-css-extract-plugin: ^0.6.0
    minimist: ^1.2.0
    ora: ^3.4.0
    portfinder: ^1.0.20
    postcss-loader: ^3.0.0
    read-pkg: ^5.0.0
    semver: ^6.0.0
    slash: ^2.0.0
    source-map-url: ^0.4.0
    ssri: ^6.0.1
    string.prototype.padend: ^3.0.0
    terser-webpack-plugin: ^1.2.3
    thread-loader: ^2.1.2
    url-loader: ^1.1.2
    vue-loader: ^15.7.0
    webpack: ">=4 < 4.29"
    webpack-bundle-analyzer: ^3.3.0
    webpack-chain: ^4.11.0
    webpack-dev-server: ^3.3.1
    webpack-merge: ^4.2.1
    yorkie: ^2.0.0
  peerDependencies:
    vue-template-compiler: ^2.0.0
  bin:
    vue-cli-service: bin/vue-cli-service.js
  checksum: 99a980f88f382e09c872f6c8189c4b83203696ca4e627193d151a6fccd1a1c8e88f098bf078645a8cca2c9f0c15bd2c91d3deb5f861527d4f27995efa298c051
  languageName: node
  linkType: hard

"@vue/cli-shared-utils@npm:^3.12.1, @vue/cli-shared-utils@npm:^3.6.0":
  version: 3.12.1
  resolution: "@vue/cli-shared-utils@npm:3.12.1"
  dependencies:
    "@hapi/joi": ^15.0.1
    chalk: ^2.4.1
    execa: ^1.0.0
    launch-editor: ^2.2.1
    lru-cache: ^5.1.1
    node-ipc: ^9.1.1
    open: ^6.3.0
    ora: ^3.4.0
    request: ^2.87.0
    request-promise-native: ^1.0.7
    semver: ^6.0.0
    string.prototype.padstart: ^3.0.0
  checksum: c724f8fda422391cbe291634ac7a6beeefef942ef49b5b395996c46bfd0a139dda7bf1b45b27364d1bd3bcce9ad8036d9a6459405434cad149a38c8a0c25da53
  languageName: node
  linkType: hard

"@vue/component-compiler-utils@npm:^2.6.0":
  version: 2.6.0
  resolution: "@vue/component-compiler-utils@npm:2.6.0"
  dependencies:
    consolidate: ^0.15.1
    hash-sum: ^1.0.2
    lru-cache: ^4.1.2
    merge-source-map: ^1.1.0
    postcss: ^7.0.14
    postcss-selector-parser: ^5.0.0
    prettier: 1.16.3
    source-map: ~0.6.1
    vue-template-es2015-compiler: ^1.9.0
  checksum: 463156b56611f3a82de87255e5c52527230925d870b06b34ad6d49a2d697c973f525772821d938f586493587d69f29a3aa70f48c0f24a32d4d00ae15bfd55397
  languageName: node
  linkType: hard

"@vue/component-compiler-utils@npm:^3.1.0":
  version: 3.3.0
  resolution: "@vue/component-compiler-utils@npm:3.3.0"
  dependencies:
    consolidate: ^0.15.1
    hash-sum: ^1.0.2
    lru-cache: ^4.1.2
    merge-source-map: ^1.1.0
    postcss: ^7.0.36
    postcss-selector-parser: ^6.0.2
    prettier: ^1.18.2 || ^2.0.0
    source-map: ~0.6.1
    vue-template-es2015-compiler: ^1.9.0
  dependenciesMeta:
    prettier:
      optional: true
  checksum: 70fee2289a4f54ec1be4d46136ee9b9893e31bf5622cead5be06c3dfb83449c3dbe6f8c03404625ccf302d0628ff9e2ea1debfae609d1bfe1d065d8f57c5dba8
  languageName: node
  linkType: hard

"@vue/preload-webpack-plugin@npm:^1.1.0":
  version: 1.1.2
  resolution: "@vue/preload-webpack-plugin@npm:1.1.2"
  peerDependencies:
    html-webpack-plugin: ">=2.26.0"
    webpack: ">=4.0.0"
  checksum: 91afc6f0f018ac52d1e88d3414cb577264148e236b7e27b8e9269c571a2da7efd06d14fb940e146ea1760111284fee139ead7bc523c944f52f667d9125035f25
  languageName: node
  linkType: hard

"@vue/test-utils@npm:1.0.0-beta.29":
  version: 1.0.0-beta.29
  resolution: "@vue/test-utils@npm:1.0.0-beta.29"
  dependencies:
    dom-event-types: ^1.0.0
    lodash: ^4.17.4
  peerDependencies:
    vue: 2.x
    vue-template-compiler: ^2.x
  checksum: 1205d80a34c54281d87d8f1f007aa49cc8ab11981e89136227c16f0c86d246ed478a660929ff3ff797ebe907e1f7288955f2da5a964bb7eb1cf0e3a18b21f402
  languageName: node
  linkType: hard

"@vue/web-component-wrapper@npm:^1.2.0":
  version: 1.3.0
  resolution: "@vue/web-component-wrapper@npm:1.3.0"
  checksum: 8cc4d1135990e61ab9d38a7b6460b018703b38b4dd3477390083018bffb93b283fabb7d57d83b3cfb78dd44da4f863167b964fe88dfa9886a54996f308036a94
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/ast@npm:1.7.11"
  dependencies:
    "@webassemblyjs/helper-module-context": 1.7.11
    "@webassemblyjs/helper-wasm-bytecode": 1.7.11
    "@webassemblyjs/wast-parser": 1.7.11
  checksum: 4c39b3bc793edb7933459d057f035764b80133debcc714ac078d5b62cea976c6cd51ef57f2e7c93f682088e13bca406ab85d64aa3a01b5c17f66501b327671b0
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/ast@npm:1.9.0"
  dependencies:
    "@webassemblyjs/helper-module-context": 1.9.0
    "@webassemblyjs/helper-wasm-bytecode": 1.9.0
    "@webassemblyjs/wast-parser": 1.9.0
  checksum: 8a9838dc7fdac358aee8daa75eefa35934ab18dafb594092ff7be79c467ebe9dabb2543e58313c905fd802bdcc3cb8320e4e19af7444e49853a7a24e25138f75
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.7.11"
  checksum: 624e05d5b85276beaf9e606d4d6ff2cf3c81927bd76bf46a9b69fda122aeb7df007d45811a93e8fce233404d2385d6c57cbf88a605a6908e11e8901b36a22709
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.9.0"
  checksum: d3aeb19bc30da26f639698daa28e44e0c18d5aa135359ef3c54148e194eec46451a912d0506099d479a71a94bc3eef6ef52d6ec234799528a25a9744789852de
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/helper-api-error@npm:1.7.11"
  checksum: b3c7b57b59a87b2e036109329818ef324df23c509b0468e7aad580d3cd19b6edc0cf2794753f64c0b8b4e94829742f08a1f33da18725a56c7bd2a8c542e765eb
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/helper-api-error@npm:1.9.0"
  checksum: 9179d3148639cc202e89a118145b485cf834613260679a99af6ec487bbc15f238566ca713207394b336160a41bf8c1b75cf2e853b3e96f0cc73c1e5c735b3f64
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/helper-buffer@npm:1.7.11"
  checksum: ac24b13f2ba6a5848815df3ebbc2dffc511b5729c5ddb4036ef6d6ae6510ca1579179ed306e359e4896579ee3467c2d79f2042044dfabf5c41b80cbbe2fdab0f
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/helper-buffer@npm:1.9.0"
  checksum: dcb85f630f8a2e22b7346ad4dd58c3237a2cad1457699423e8fd19592a0bd3eacbc2639178a1b9a873c3ac217bfc7a23a134ff440a099496b590e82c7a4968d5
  languageName: node
  linkType: hard

"@webassemblyjs/helper-code-frame@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/helper-code-frame@npm:1.7.11"
  dependencies:
    "@webassemblyjs/wast-printer": 1.7.11
  checksum: fab9af696af64746f0be578ea73517d00c0062faa2d83dd1033f10c66cf97e9aff1bcb0d6d25084601407e90a3386cca424abcc1b49fb307b48f3a7548797a9d
  languageName: node
  linkType: hard

"@webassemblyjs/helper-code-frame@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/helper-code-frame@npm:1.9.0"
  dependencies:
    "@webassemblyjs/wast-printer": 1.9.0
  checksum: a28fa057f7beff0fd14bff716561520f8edb8c9c56c7a5559451e6765acfb70aaeb8af718ea2bd2262e7baeba597545af407e28eb2eff8329235afe8605f20d1
  languageName: node
  linkType: hard

"@webassemblyjs/helper-fsm@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/helper-fsm@npm:1.7.11"
  checksum: 42755b60130790818f497a463dadb1a55ab635a82455eb1c9c2a50e8d874303d98c655f314b575585851738b21b05c367278be00329e9673c58d89a453279438
  languageName: node
  linkType: hard

"@webassemblyjs/helper-fsm@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/helper-fsm@npm:1.9.0"
  checksum: 374cc510c8f5a7a07d4fe9eb7036cc475a96a670b5d25c31f16757ac8295be8d03a2f29657ff53eaefa9e8315670a48824d430ed910e7c1835788ac79f93124e
  languageName: node
  linkType: hard

"@webassemblyjs/helper-module-context@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/helper-module-context@npm:1.7.11"
  checksum: 00b8340376d460d1faa5d18415136dc49c591229f5309c0af7bfc020c56d8da00c49076519114d150012c162d1236950d662b1484ec7fe751c9fcf2bcffe5b34
  languageName: node
  linkType: hard

"@webassemblyjs/helper-module-context@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/helper-module-context@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": 1.9.0
  checksum: 55e8f89c7ea1beaa78fad88403f3753b8413b0f3b6bb32d898ce95078b3e1d1b48ade0919c00b82fc2e3813c0ab6901e415f7a4d4fa9be50944e2431adde75a5
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.7.11"
  checksum: a496721d4a5cbfa81aa9a80cc03083447531210d46ca995963949e5e4acb1d1abb0f9edd17484b777923241523ac304c69563e3a36beb8aaa42b19e3ca8f69a9
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.9.0"
  checksum: 280da4df3c556f73a1a02053277f8a4be481de32df4aa21050b015c8f4d27c46af89f0417eb88e486df117e5df4bccffae593f78cb1e79f212d3b3d4f3ed0f04
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.7.11"
  dependencies:
    "@webassemblyjs/ast": 1.7.11
    "@webassemblyjs/helper-buffer": 1.7.11
    "@webassemblyjs/helper-wasm-bytecode": 1.7.11
    "@webassemblyjs/wasm-gen": 1.7.11
  checksum: 092edc4c46a3d3a20e846f5eeacd6a0bb8a43f1f098b329c0986cfc37bc8bee763e025ca8fa89ef8a4334bfe1050adc6c181c04932f3380d98049b481b89a5a1
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": 1.9.0
    "@webassemblyjs/helper-buffer": 1.9.0
    "@webassemblyjs/helper-wasm-bytecode": 1.9.0
    "@webassemblyjs/wasm-gen": 1.9.0
  checksum: b8f7bb45d4194074c82210211a5d3e402a5b5fa63ecae26d2c356ae3978af5a530e91192fb260f32f9d561b18e2828b3da2e2f41c59efadb5f3c6d72446807f0
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/ieee754@npm:1.7.11"
  dependencies:
    "@xtuc/ieee754": ^1.2.0
  checksum: 3ee55e1006f852b56190659f3bde070b7a800d666776654d65ba29f2196c0b176612f615bd62a0ebe1298cfacc46db36183295b2a166ee9fa9c2845b52eb16ea
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/ieee754@npm:1.9.0"
  dependencies:
    "@xtuc/ieee754": ^1.2.0
  checksum: 7fe4a217ba0f7051e2cfef92919d4a64fac1a63c65411763779bd50907820f33f440255231a474fe3ba03bd1d9ee0328662d1eae3fce4c59b91549d6b62b839b
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/leb128@npm:1.7.11"
  dependencies:
    "@xtuc/long": 4.2.1
  checksum: e4481ef4bcbb0df768c8336c7be9d52346ce5449634a72f1727e5a59752dd28e9f589524718f84452ab5d92918d484208918b321890159de8a6855a0fb9c6578
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/leb128@npm:1.9.0"
  dependencies:
    "@xtuc/long": 4.2.2
  checksum: 4ca7cbb869530d78d42a414f34ae53249364cb1ecebbfb6ed5d562c2f209fce857502f088822ee82a23876f653a262ddc34ab64e45a7962510a263d39bb3f51a
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/utf8@npm:1.7.11"
  checksum: 81f6bb58086fe53f2871136c4cf07c289e659d6c0d7d0b8da3f261cd6fdceec0e566410033321fcd94b0138485e02411221ef4416e1bb21152c9b3e725c3af48
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/utf8@npm:1.9.0"
  checksum: e328a30ac8a503bbd015d32e75176e0dedcb45a21d4be051c25dfe89a00035ca7a6dbd8937b442dd5b4b334de3959d4f5fe0b330037bd226a28b9814cd49e84f
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/wasm-edit@npm:1.7.11"
  dependencies:
    "@webassemblyjs/ast": 1.7.11
    "@webassemblyjs/helper-buffer": 1.7.11
    "@webassemblyjs/helper-wasm-bytecode": 1.7.11
    "@webassemblyjs/helper-wasm-section": 1.7.11
    "@webassemblyjs/wasm-gen": 1.7.11
    "@webassemblyjs/wasm-opt": 1.7.11
    "@webassemblyjs/wasm-parser": 1.7.11
    "@webassemblyjs/wast-printer": 1.7.11
  checksum: 1727786c47bbd9ad77a9497868a80aa0a5af1b37d6615957549bca0eb214a926ee781c42ff48656bb4d1e319df817320c16bf5ec3a9e036bbff0d762b6aefcc9
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/wasm-edit@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": 1.9.0
    "@webassemblyjs/helper-buffer": 1.9.0
    "@webassemblyjs/helper-wasm-bytecode": 1.9.0
    "@webassemblyjs/helper-wasm-section": 1.9.0
    "@webassemblyjs/wasm-gen": 1.9.0
    "@webassemblyjs/wasm-opt": 1.9.0
    "@webassemblyjs/wasm-parser": 1.9.0
    "@webassemblyjs/wast-printer": 1.9.0
  checksum: 1997e0c2f4051c33239587fb143242919320bc861a0af03a873c7150a27d6404bd2e063c658193288b0aa88c35aadbe0c4fde601fe642bae0743a8c8eda52717
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/wasm-gen@npm:1.7.11"
  dependencies:
    "@webassemblyjs/ast": 1.7.11
    "@webassemblyjs/helper-wasm-bytecode": 1.7.11
    "@webassemblyjs/ieee754": 1.7.11
    "@webassemblyjs/leb128": 1.7.11
    "@webassemblyjs/utf8": 1.7.11
  checksum: 18019bca26bfdda88f7e7a348b91caaf59f1afe301f8e1c8e66c1268c976275d916ca7b5478ddb4791c9e0fea203dbed9b08b95101dcd577f7ac9da9586ba13e
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/wasm-gen@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": 1.9.0
    "@webassemblyjs/helper-wasm-bytecode": 1.9.0
    "@webassemblyjs/ieee754": 1.9.0
    "@webassemblyjs/leb128": 1.9.0
    "@webassemblyjs/utf8": 1.9.0
  checksum: 2456e84e8e6bedb7ab47f6333a0ee170f7ef62842c90862ca787c08528ca8041061f3f8bc257fc2a01bf6e8d1a76fddaddd43418c738f681066e5b50f88fe7df
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/wasm-opt@npm:1.7.11"
  dependencies:
    "@webassemblyjs/ast": 1.7.11
    "@webassemblyjs/helper-buffer": 1.7.11
    "@webassemblyjs/wasm-gen": 1.7.11
    "@webassemblyjs/wasm-parser": 1.7.11
  checksum: 66fcc0f6c33d94c2e1909d869921733f6512c887ec9f0133d60042615c2600bcc2b6007b5cd048329914521dc56857f07c8330dd897c258a3f0b3d079b480814
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/wasm-opt@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": 1.9.0
    "@webassemblyjs/helper-buffer": 1.9.0
    "@webassemblyjs/wasm-gen": 1.9.0
    "@webassemblyjs/wasm-parser": 1.9.0
  checksum: 91242205bdbd1aa8045364a5338bfb34880cb2c65f56db8dd19382894209673699fb31a0e5279f25c7e5bcd8f3097d6c9ca84d8969d9613ef2cf166450cc3515
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/wasm-parser@npm:1.7.11"
  dependencies:
    "@webassemblyjs/ast": 1.7.11
    "@webassemblyjs/helper-api-error": 1.7.11
    "@webassemblyjs/helper-wasm-bytecode": 1.7.11
    "@webassemblyjs/ieee754": 1.7.11
    "@webassemblyjs/leb128": 1.7.11
    "@webassemblyjs/utf8": 1.7.11
  checksum: d1f4afae9e053a017fd9b1469da9856f83bcbb9dc8beeb0a7bbd96ae08cb370cf355a7022cf6b510e4230339aa0860ec1297cda2186cf857524e8ec3a405b300
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/wasm-parser@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": 1.9.0
    "@webassemblyjs/helper-api-error": 1.9.0
    "@webassemblyjs/helper-wasm-bytecode": 1.9.0
    "@webassemblyjs/ieee754": 1.9.0
    "@webassemblyjs/leb128": 1.9.0
    "@webassemblyjs/utf8": 1.9.0
  checksum: 493f6cfc63a5e16073056c81ff0526a9936f461327379ef3c83cc841000e03623b6352704f6bf9f7cb5b3610f0032020a61f9cca78c91b15b8e995854b29c098
  languageName: node
  linkType: hard

"@webassemblyjs/wast-parser@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/wast-parser@npm:1.7.11"
  dependencies:
    "@webassemblyjs/ast": 1.7.11
    "@webassemblyjs/floating-point-hex-parser": 1.7.11
    "@webassemblyjs/helper-api-error": 1.7.11
    "@webassemblyjs/helper-code-frame": 1.7.11
    "@webassemblyjs/helper-fsm": 1.7.11
    "@xtuc/long": 4.2.1
  checksum: c719deaa5a4db86b0eb721193d087ecbd34c7c88e02dfec20346bdc0859be8aab0a1e363ccd6346c79cd5a4faf8bee0687b82261983aa21f179cb0f832f52882
  languageName: node
  linkType: hard

"@webassemblyjs/wast-parser@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/wast-parser@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": 1.9.0
    "@webassemblyjs/floating-point-hex-parser": 1.9.0
    "@webassemblyjs/helper-api-error": 1.9.0
    "@webassemblyjs/helper-code-frame": 1.9.0
    "@webassemblyjs/helper-fsm": 1.9.0
    "@xtuc/long": 4.2.2
  checksum: 705dd48fbbceec7f6bed299b8813631b242fd9312f9594dbb2985dda86c9688048692357d684f6080fc2c5666287cefaa26b263d01abadb6a9049d4c8978b9db
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.7.11":
  version: 1.7.11
  resolution: "@webassemblyjs/wast-printer@npm:1.7.11"
  dependencies:
    "@webassemblyjs/ast": 1.7.11
    "@webassemblyjs/wast-parser": 1.7.11
    "@xtuc/long": 4.2.1
  checksum: 9ad09b892476d0278e08858e2f80764d0c64b3eb3f26a02931151329000831a86d57592fd19a819398aede0088ae2e7e354dd16ccee56f0641eca6fc8ffb3396
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.9.0":
  version: 1.9.0
  resolution: "@webassemblyjs/wast-printer@npm:1.9.0"
  dependencies:
    "@webassemblyjs/ast": 1.9.0
    "@webassemblyjs/wast-parser": 1.9.0
    "@xtuc/long": 4.2.2
  checksum: 3d1e1b2e84745a963f69acd1c02425b321dd2e608e11dabc467cae0c9a808962bc769ec9afc46fbcea7188cc1e47d72370da762d258f716fb367cb1a7865c54b
  languageName: node
  linkType: hard

"@xtuc/ieee754@npm:^1.2.0":
  version: 1.2.0
  resolution: "@xtuc/ieee754@npm:1.2.0"
  checksum: ac56d4ca6e17790f1b1677f978c0c6808b1900a5b138885d3da21732f62e30e8f0d9120fcf8f6edfff5100ca902b46f8dd7c1e3f903728634523981e80e2885a
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.1":
  version: 4.2.1
  resolution: "@xtuc/long@npm:4.2.1"
  checksum: 353fa0d53b3619771be06b1bde5cb69e2b0c5dadb9e27cfe26dad4efb1cbce08464c9636073945c06288034af4d059b9e000572f1918f40e25a3b7265aa690f6
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.2":
  version: 4.2.2
  resolution: "@xtuc/long@npm:4.2.2"
  checksum: 8ed0d477ce3bc9c6fe2bf6a6a2cc316bb9c4127c5a7827bae947fa8ec34c7092395c5a283cc300c05b5fa01cbbfa1f938f410a7bf75db7c7846fea41949989ec
  languageName: node
  linkType: hard

"abab@npm:^2.0.0":
  version: 2.0.5
  resolution: "abab@npm:2.0.5"
  checksum: 0ec951b46d5418c2c2f923021ec193eaebdb4e802ffd5506286781b454be722a13a8430f98085cd3e204918401d9130ec6cc8f5ae19be315b3a0e857d83196e1
  languageName: node
  linkType: hard

"abbrev@npm:1":
  version: 1.1.1
  resolution: "abbrev@npm:1.1.1"
  checksum: a4a97ec07d7ea112c517036882b2ac22f3109b7b19077dc656316d07d308438aac28e4d9746dc4d84bf6b1e75b4a7b0a5f3cb30592419f128ca9a8cee3bcfa17
  languageName: node
  linkType: hard

"accepts@npm:~1.3.4, accepts@npm:~1.3.5, accepts@npm:~1.3.7":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: ~2.1.34
    negotiator: 0.6.3
  checksum: 50c43d32e7b50285ebe84b613ee4a3aa426715a7d131b65b786e2ead0fd76b6b60091b9916d3478a75f11f162628a2139991b6c03ab3f1d9ab7c86075dc8eab4
  languageName: node
  linkType: hard

"acorn-dynamic-import@npm:^3.0.0":
  version: 3.0.0
  resolution: "acorn-dynamic-import@npm:3.0.0"
  dependencies:
    acorn: ^5.0.0
  checksum: 60ba19103fdaa87e048a9480238faefd451dc39e21cf079812acd5e59ca064619a8c905b274f095b7c686736605547b089c6a5b75e926202afb8a4392d012659
  languageName: node
  linkType: hard

"acorn-globals@npm:^4.1.0":
  version: 4.3.4
  resolution: "acorn-globals@npm:4.3.4"
  dependencies:
    acorn: ^6.0.1
    acorn-walk: ^6.0.1
  checksum: c31bfde102d8a104835e9591c31dd037ec771449f9c86a6b1d2ac3c7c336694f828cfabba7687525b094f896a854affbf1afe6e1b12c0d998be6bab5d49c9663
  languageName: node
  linkType: hard

"acorn-jsx@npm:^3.0.0":
  version: 3.0.1
  resolution: "acorn-jsx@npm:3.0.1"
  dependencies:
    acorn: ^3.0.4
  checksum: 43f1302dabfd263674e029537558be832b4bb8baa095a17823bf0ca0509e779deddcb39ab7c398a3f0de406dbf5e6d32dce87da24188ad52397701aa6ebcb6b2
  languageName: node
  linkType: hard

"acorn-jsx@npm:^5.0.0":
  version: 5.3.2
  resolution: "acorn-jsx@npm:5.3.2"
  peerDependencies:
    acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
  checksum: c3d3b2a89c9a056b205b69530a37b972b404ee46ec8e5b341666f9513d3163e2a4f214a71f4dfc7370f5a9c07472d2fd1c11c91c3f03d093e37637d95da98950
  languageName: node
  linkType: hard

"acorn-walk@npm:^6.0.1, acorn-walk@npm:^6.1.1":
  version: 6.2.0
  resolution: "acorn-walk@npm:6.2.0"
  checksum: ea241a5d96338f1e8030aafae72a91ff0ec4360e2775e44a2fdb2eb618b07fc309e000a5126056631ac7f00fe8bd9bbd23fcb6d018eee4ba11086eb36c1b2e61
  languageName: node
  linkType: hard

"acorn-walk@npm:^7.1.1":
  version: 7.2.0
  resolution: "acorn-walk@npm:7.2.0"
  checksum: 9252158a79b9d92f1bc0dd6acc0fcfb87a67339e84bcc301bb33d6078936d27e35d606b4d35626d2962cd43c256d6f27717e70cbe15c04fff999ab0b2260b21f
  languageName: node
  linkType: hard

"acorn@npm:^3.0.4":
  version: 3.3.0
  resolution: "acorn@npm:3.3.0"
  bin:
    acorn: ./bin/acorn
  checksum: d24aee1c838a3467c5ab493601ed4b646e4fd0599ca19b16f47f764e7929d0ffcb4d536935283a8d823d64ca3817d27dd5f93830bb86f592249d9feb0fcf78f9
  languageName: node
  linkType: hard

"acorn@npm:^5.0.0, acorn@npm:^5.5.0, acorn@npm:^5.5.3, acorn@npm:^5.6.2":
  version: 5.7.4
  resolution: "acorn@npm:5.7.4"
  bin:
    acorn: bin/acorn
  checksum: f51392a4d25c7705fadb890f784c59cde4ac1c5452ccd569fa59bd2191b7951b4a6398348ab7ea08a54f0bc0a56c13776710f4e1bae9de441e4d33e2015ad1e0
  languageName: node
  linkType: hard

"acorn@npm:^6.0.1, acorn@npm:^6.0.2, acorn@npm:^6.0.7, acorn@npm:^6.1.1, acorn@npm:^6.4.1":
  version: 6.4.2
  resolution: "acorn@npm:6.4.2"
  bin:
    acorn: bin/acorn
  checksum: 44b07053729db7f44d28343eed32247ed56dc4a6ec6dff2b743141ecd6b861406bbc1c20bf9d4f143ea7dd08add5dc8c290582756539bc03a8db605050ce2fb4
  languageName: node
  linkType: hard

"acorn@npm:^7.1.1":
  version: 7.4.1
  resolution: "acorn@npm:7.4.1"
  bin:
    acorn: bin/acorn
  checksum: 1860f23c2107c910c6177b7b7be71be350db9e1080d814493fae143ae37605189504152d1ba8743ba3178d0b37269ce1ffc42b101547fdc1827078f82671e407
  languageName: node
  linkType: hard

"address@npm:^1.0.3":
  version: 1.1.2
  resolution: "address@npm:1.1.2"
  checksum: d966deee6ab9a0f96ed1d25dc73e91a248f64479c91f9daeb15237b8e3c39a02faac4e6afe8987ef9e5aea60a1593cef5882b7456ab2e6196fc0229a93ec39c2
  languageName: node
  linkType: hard

"agent-base@npm:6, agent-base@npm:^6.0.2":
  version: 6.0.2
  resolution: "agent-base@npm:6.0.2"
  dependencies:
    debug: 4
  checksum: f52b6872cc96fd5f622071b71ef200e01c7c4c454ee68bc9accca90c98cfb39f2810e3e9aa330435835eedc8c23f4f8a15267f67c6e245d2b33757575bdac49d
  languageName: node
  linkType: hard

"agentkeepalive@npm:^4.1.3":
  version: 4.2.0
  resolution: "agentkeepalive@npm:4.2.0"
  dependencies:
    debug: ^4.1.0
    depd: ^1.1.2
    humanize-ms: ^1.2.1
  checksum: 89806f83ceebbcaabf6bd581a8dce4870910fd2a11f66df8f505b4cd4ce4ca5ab9e6eec8d11ce8531a6b60f6748b75b0775e0e2fa33871503ef00d535418a19a
  languageName: node
  linkType: hard

"aggregate-error@npm:^3.0.0":
  version: 3.1.0
  resolution: "aggregate-error@npm:3.1.0"
  dependencies:
    clean-stack: ^2.0.0
    indent-string: ^4.0.0
  checksum: 1101a33f21baa27a2fa8e04b698271e64616b886795fd43c31068c07533c7b3facfcaf4e9e0cab3624bd88f729a592f1c901a1a229c9e490eafce411a8644b79
  languageName: node
  linkType: hard

"ajv-errors@npm:^1.0.0":
  version: 1.0.1
  resolution: "ajv-errors@npm:1.0.1"
  peerDependencies:
    ajv: ">=5.0.0"
  checksum: 2c9fc02cf58f9aae5bace61ebd1b162e1ea372ae9db5999243ba5e32a9a78c0d635d29ae085f652c61c941a43af0b2b1acdb255e29d44dc43a6e021085716d8c
  languageName: node
  linkType: hard

"ajv-keywords@npm:^2.1.0":
  version: 2.1.1
  resolution: "ajv-keywords@npm:2.1.1"
  peerDependencies:
    ajv: ^5.0.0
  checksum: 82b3d691e321b2fafd6a0887c0ee09859f9cac54ea5b28881ab867017e984d1bedca0b5ac0ccbedf0b9abda7ebf10eddce6bb1cd67600f9d2a46fe5a47bf015a
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.1.0, ajv-keywords@npm:^3.4.1, ajv-keywords@npm:^3.5.2":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 7dc5e5931677a680589050f79dcbe1fefbb8fea38a955af03724229139175b433c63c68f7ae5f86cf8f65d55eb7c25f75a046723e2e58296707617ca690feae9
  languageName: node
  linkType: hard

"ajv@npm:^5.2.3, ajv@npm:^5.3.0":
  version: 5.5.2
  resolution: "ajv@npm:5.5.2"
  dependencies:
    co: ^4.6.0
    fast-deep-equal: ^1.0.0
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.3.0
  checksum: a69645c843e1676b0ae1c5192786e546427f808f386d26127c6585479378066c64341ceec0b127b6789d79628e71d2a732d402f575b98f9262db230d7b715a94
  languageName: node
  linkType: hard

"ajv@npm:^6.1.0, ajv@npm:^6.10.2, ajv@npm:^6.12.3, ajv@npm:^6.12.4, ajv@npm:^6.9.1":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"alphanum-sort@npm:^1.0.0":
  version: 1.0.2
  resolution: "alphanum-sort@npm:1.0.2"
  checksum: 5a32d0b3c0944e65d22ff3ae2f88d7a4f8d88a78a703033caeae33f2944915e053d283d02f630dc94823edc7757148ecdcf39fd687a5117bda5c10133a03a7d8
  languageName: node
  linkType: hard

"ansi-colors@npm:^3.0.0":
  version: 3.2.4
  resolution: "ansi-colors@npm:3.2.4"
  checksum: 026c51880e9f8eb59b112669a87dbea4469939ff94b131606303bbd697438a6691b16b9db3027aa9bf132a244214e83ab1508b998496a34d2aea5b437ac9e62d
  languageName: node
  linkType: hard

"ansi-escapes@npm:^3.0.0, ansi-escapes@npm:^3.2.0":
  version: 3.2.0
  resolution: "ansi-escapes@npm:3.2.0"
  checksum: 0f94695b677ea742f7f1eed961f7fd8d05670f744c6ad1f8f635362f6681dcfbc1575cb05b43abc7bb6d67e25a75fb8c7ea8f2a57330eb2c76b33f18cb2cef0a
  languageName: node
  linkType: hard

"ansi-html-community@npm:0.0.8":
  version: 0.0.8
  resolution: "ansi-html-community@npm:0.0.8"
  bin:
    ansi-html: bin/ansi-html
  checksum: 04c568e8348a636963f915e48eaa3e01218322e1169acafdd79c384f22e5558c003f79bbc480c1563865497482817c7eed025f0653ebc17642fededa5cb42089
  languageName: node
  linkType: hard

"ansi-regex@npm:^2.0.0":
  version: 2.1.1
  resolution: "ansi-regex@npm:2.1.1"
  checksum: 190abd03e4ff86794f338a31795d262c1dfe8c91f7e01d04f13f646f1dcb16c5800818f886047876f1272f065570ab86b24b99089f8b68a0e11ff19aed4ca8f1
  languageName: node
  linkType: hard

"ansi-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "ansi-regex@npm:3.0.0"
  checksum: 2ad11c416f81c39f5c65eafc88cf1d71aa91d76a2f766e75e457c2a3c43e8a003aadbf2966b61c497aa6a6940a36412486c975b3270cdfc3f413b69826189ec3
  languageName: node
  linkType: hard

"ansi-regex@npm:^4.1.0":
  version: 4.1.0
  resolution: "ansi-regex@npm:4.1.0"
  checksum: 97aa4659538d53e5e441f5ef2949a3cffcb838e57aeaad42c4194e9d7ddb37246a6526c4ca85d3940a9d1e19b11cc2e114530b54c9d700c8baf163c31779baf8
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 2aa4bb54caf2d622f1afdad09441695af2a83aa3fe8b8afa581d205e57ed4261c183c4d3877cee25794443fde5876417d859c108078ab788d6af7e4fe52eb66b
  languageName: node
  linkType: hard

"ansi-styles@npm:^2.2.1":
  version: 2.2.1
  resolution: "ansi-styles@npm:2.2.1"
  checksum: ebc0e00381f2a29000d1dac8466a640ce11943cef3bda3cd0020dc042e31e1058ab59bf6169cd794a54c3a7338a61ebc404b7c91e004092dd20e028c432c9c2c
  languageName: node
  linkType: hard

"ansi-styles@npm:^3.1.0, ansi-styles@npm:^3.2.0, ansi-styles@npm:^3.2.1":
  version: 3.2.1
  resolution: "ansi-styles@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.0
  checksum: d85ade01c10e5dd77b6c89f34ed7531da5830d2cb5882c645f330079975b716438cd7ebb81d0d6e6b4f9c577f19ae41ab55f07f19786b02f9dfd9e0377395665
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"anymatch@npm:^2.0.0":
  version: 2.0.0
  resolution: "anymatch@npm:2.0.0"
  dependencies:
    micromatch: ^3.1.4
    normalize-path: ^2.1.1
  checksum: f7bb1929842b4585cdc28edbb385767d499ce7d673f96a8f11348d2b2904592ffffc594fe9229b9a1e9e4dccb9329b7692f9f45e6a11dcefbb76ecdc9ab740f6
  languageName: node
  linkType: hard

"anymatch@npm:~3.1.2":
  version: 3.1.2
  resolution: "anymatch@npm:3.1.2"
  dependencies:
    normalize-path: ^3.0.0
    picomatch: ^2.0.4
  checksum: 985163db2292fac9e5a1e072bf99f1b5baccf196e4de25a0b0b81865ebddeb3b3eb4480734ef0a2ac8c002845396b91aa89121f5b84f93981a4658164a9ec6e9
  languageName: node
  linkType: hard

"append-transform@npm:^0.4.0":
  version: 0.4.0
  resolution: "append-transform@npm:0.4.0"
  dependencies:
    default-require-extensions: ^1.0.0
  checksum: f5edcf48e3327e8c9594d3ff57ea250401c1cda8dd2460704025fca5ef304b31cdba6e4ad522101ca69bd2245835add4831427bb18a7eb454ec275af08be11d0
  languageName: node
  linkType: hard

"aproba@npm:^1.0.3 || ^2.0.0":
  version: 2.0.0
  resolution: "aproba@npm:2.0.0"
  checksum: 5615cadcfb45289eea63f8afd064ab656006361020e1735112e346593856f87435e02d8dcc7ff0d11928bc7d425f27bc7c2a84f6c0b35ab0ff659c814c138a24
  languageName: node
  linkType: hard

"aproba@npm:^1.1.1":
  version: 1.2.0
  resolution: "aproba@npm:1.2.0"
  checksum: 0fca141966559d195072ed047658b6e6c4fe92428c385dd38e288eacfc55807e7b4989322f030faff32c0f46bb0bc10f1e0ac32ec22d25315a1e5bbc0ebb76dc
  languageName: node
  linkType: hard

"arch@npm:^2.1.1":
  version: 2.2.0
  resolution: "arch@npm:2.2.0"
  checksum: e21b7635029fe8e9cdd5a026f9a6c659103e63fff423834323cdf836a1bb240a72d0c39ca8c470f84643385cf581bd8eda2cad8bf493e27e54bd9783abe9101f
  languageName: node
  linkType: hard

"are-we-there-yet@npm:^2.0.0":
  version: 2.0.0
  resolution: "are-we-there-yet@npm:2.0.0"
  dependencies:
    delegates: ^1.0.0
    readable-stream: ^3.6.0
  checksum: 6c80b4fd04ecee6ba6e737e0b72a4b41bdc64b7d279edfc998678567ff583c8df27e27523bc789f2c99be603ffa9eaa612803da1d886962d2086e7ff6fa90c7c
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: ~1.0.2
  checksum: 7ca6e45583a28de7258e39e13d81e925cfa25d7d4aacbf806a382d3c02fcb13403a07fb8aeef949f10a7cfe4a62da0e2e807b348a5980554cc28ee573ef95945
  languageName: node
  linkType: hard

"arr-diff@npm:^2.0.0":
  version: 2.0.0
  resolution: "arr-diff@npm:2.0.0"
  dependencies:
    arr-flatten: ^1.0.1
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"arr-diff@npm:^4.0.0":
  version: 4.0.0
  resolution: "arr-diff@npm:4.0.0"
  checksum: ea7c8834842ad3869297f7915689bef3494fd5b102ac678c13ffccab672d3d1f35802b79e90c4cfec2f424af3392e44112d1ccf65da34562ed75e049597276a0
  languageName: node
  linkType: hard

"arr-flatten@npm:^1.0.1, arr-flatten@npm:^1.1.0":
  version: 1.1.0
  resolution: "arr-flatten@npm:1.1.0"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"arr-union@npm:^3.1.0":
  version: 3.1.0
  resolution: "arr-union@npm:3.1.0"
  checksum: b5b0408c6eb7591143c394f3be082fee690ddd21f0fdde0a0a01106799e847f67fcae1b7e56b0a0c173290e29c6aca9562e82b300708a268bc8f88f3d6613cb9
  languageName: node
  linkType: hard

"array-equal@npm:^1.0.0":
  version: 1.0.0
  resolution: "array-equal@npm:1.0.0"
  checksum: 3f68045806357db9b2fa1ad583e42a659de030633118a0cd35ee4975cb20db3b9a3d36bbec9b5afe70011cf989eefd215c12fe0ce08c498f770859ca6e70688a
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-flatten@npm:^2.1.0":
  version: 2.1.2
  resolution: "array-flatten@npm:2.1.2"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"array-union@npm:^1.0.1, array-union@npm:^1.0.2":
  version: 1.0.2
  resolution: "array-union@npm:1.0.2"
  dependencies:
    array-uniq: ^1.0.1
  checksum: 82cec6421b6e6766556c484835a6d476a873f1b71cace5ab2b4f1b15b1e3162dc4da0d16f7a2b04d4aec18146c6638fe8f661340b31ba8e469fd811a1b45dc8d
  languageName: node
  linkType: hard

"array-uniq@npm:^1.0.1":
  version: 1.0.3
  resolution: "array-uniq@npm:1.0.3"
  checksum: 1625f06b093d8bf279b81adfec6e72951c0857d65b5e3f65f053fffe9f9dd61c2fc52cff57e38a4700817e7e3f01a4faa433d505ea9e33cdae4514c334e0bf9e
  languageName: node
  linkType: hard

"array-unique@npm:^0.2.1":
  version: 0.2.1
  resolution: "array-unique@npm:0.2.1"
  checksum: 899deaf07abedf17ee89a757c7bcc9253fb248a7f6c394a1fec9ec3f3ac244314feb3048efee80ed7fdcb047960e32d7c234291bfd26b78ced668c346d9f4e3c
  languageName: node
  linkType: hard

"array-unique@npm:^0.3.2":
  version: 0.3.2
  resolution: "array-unique@npm:0.3.2"
  checksum: da344b89cfa6b0a5c221f965c21638bfb76b57b45184a01135382186924f55973cd9b171d4dad6bf606c6d9d36b0d721d091afdc9791535ead97ccbe78f8a888
  languageName: node
  linkType: hard

"arrify@npm:^1.0.1":
  version: 1.0.1
  resolution: "arrify@npm:1.0.1"
  checksum: 745075dd4a4624ff0225c331dacb99be501a515d39bcb7c84d24660314a6ec28e68131b137e6f7e16318170842ce97538cd298fc4cd6b2cc798e0b957f2747e7
  languageName: node
  linkType: hard

"asn1.js@npm:^5.2.0":
  version: 5.4.1
  resolution: "asn1.js@npm:5.4.1"
  dependencies:
    bn.js: ^4.0.0
    inherits: ^2.0.1
    minimalistic-assert: ^1.0.0
    safer-buffer: ^2.1.0
  checksum: 3786a101ac6f304bd4e9a7df79549a7561950a13d4bcaec0c7790d44c80d147c1a94ba3d4e663673406064642a40b23fcd6c82a9952468e386c1a1376d747f9a
  languageName: node
  linkType: hard

"asn1@npm:~0.2.3":
  version: 0.2.6
  resolution: "asn1@npm:0.2.6"
  dependencies:
    safer-buffer: ~2.1.0
  checksum: 39f2ae343b03c15ad4f238ba561e626602a3de8d94ae536c46a4a93e69578826305366dc09fbb9b56aec39b4982a463682f259c38e59f6fa380cd72cd61e493d
  languageName: node
  linkType: hard

"assert-plus@npm:1.0.0, assert-plus@npm:^1.0.0":
  version: 1.0.0
  resolution: "assert-plus@npm:1.0.0"
  checksum: 19b4340cb8f0e6a981c07225eacac0e9d52c2644c080198765d63398f0075f83bbc0c8e95474d54224e297555ad0d631c1dcd058adb1ddc2437b41a6b424ac64
  languageName: node
  linkType: hard

"assert@npm:^1.1.1":
  version: 1.5.0
  resolution: "assert@npm:1.5.0"
  dependencies:
    object-assign: ^4.1.1
    util: 0.10.3
  checksum: 9be48435f726029ae7020c5888a3566bf4d617687aab280827f2e4029644b6515a9519ea10d018b342147c02faf73d9e9419e780e8937b3786ee4945a0ca71e5
  languageName: node
  linkType: hard

"assign-symbols@npm:^1.0.0":
  version: 1.0.0
  resolution: "assign-symbols@npm:1.0.0"
  checksum: c0eb895911d05b6b2d245154f70461c5e42c107457972e5ebba38d48967870dee53bcdf6c7047990586daa80fab8dab3cc6300800fbd47b454247fdedd859a2c
  languageName: node
  linkType: hard

"astral-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "astral-regex@npm:1.0.0"
  checksum: 93417fc0879531cd95ace2560a54df865c9461a3ac0714c60cbbaa5f1f85d2bee85489e78d82f70b911b71ac25c5f05fc5a36017f44c9bb33c701bee229ff848
  languageName: node
  linkType: hard

"async-each@npm:^1.0.1":
  version: 1.0.3
  resolution: "async-each@npm:1.0.3"
  checksum: 868651cfeb209970b367fbb96df1e1c8dc0b22c681cda7238417005ab2a5fbd944ee524b43f2692977259a57b7cc2547e03ff68f2b5113dbdf953d48cc078dc3
  languageName: node
  linkType: hard

"async-limiter@npm:~1.0.0":
  version: 1.0.1
  resolution: "async-limiter@npm:1.0.1"
  checksum: 2b849695b465d93ad44c116220dee29a5aeb63adac16c1088983c339b0de57d76e82533e8e364a93a9f997f28bbfc6a92948cefc120652bd07f3b59f8d75cf2b
  languageName: node
  linkType: hard

"async-validator@npm:~1.8.1":
  version: 1.8.5
  resolution: "async-validator@npm:1.8.5"
  dependencies:
    babel-runtime: 6.x
  checksum: 3c526e3578119cf1c54bb033a931d63772bcf022ca1edb81f2a4799a90666e90b28d8c48fd82c9e67b1756f130dffdaad051bee1b7c3da7243d232356a388b93
  languageName: node
  linkType: hard

"async@npm:^2.1.4, async@npm:^2.6.2":
  version: 2.6.3
  resolution: "async@npm:2.6.3"
  dependencies:
    lodash: ^4.17.14
  checksum: 5e5561ff8fca807e88738533d620488ac03a5c43fce6c937451f7e35f943d33ad06c24af3f681a48cca3d2b0002b3118faff0a128dc89438a9bf0226f712c499
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 7b78c451df768adba04e2d02e63e2d0bf3b07adcd6e42b4cf665cb7ce899bedd344c69a1dcbce355b5f972d597b25aaa1c1742b52cffd9caccb22f348114f6be
  languageName: node
  linkType: hard

"atob@npm:^2.1.2":
  version: 2.1.2
  resolution: "atob@npm:2.1.2"
  bin:
    atob: bin/atob.js
  checksum: dfeeeb70090c5ebea7be4b9f787f866686c645d9f39a0d184c817252d0cf08455ed25267d79c03254d3be1f03ac399992a792edcd5ffb9c91e097ab5ef42833a
  languageName: node
  linkType: hard

"autoprefixer@npm:^9.5.1":
  version: 9.8.8
  resolution: "autoprefixer@npm:9.8.8"
  dependencies:
    browserslist: ^4.12.0
    caniuse-lite: ^1.0.30001109
    normalize-range: ^0.1.2
    num2fraction: ^1.2.2
    picocolors: ^0.2.1
    postcss: ^7.0.32
    postcss-value-parser: ^4.1.0
  bin:
    autoprefixer: bin/autoprefixer
  checksum: 8f017672fbac248db0cf4e86aa707d8b148d9abadb842b5cf4c6be306d80fa6a654fadefd17e46213234c1f0947612acce2864f93e903f3e736b183fc1aedc45
  languageName: node
  linkType: hard

"aws-sign2@npm:~0.7.0":
  version: 0.7.0
  resolution: "aws-sign2@npm:0.7.0"
  checksum: b148b0bb0778098ad8cf7e5fc619768bcb51236707ca1d3e5b49e41b171166d8be9fdc2ea2ae43d7decf02989d0aaa3a9c4caa6f320af95d684de9b548a71525
  languageName: node
  linkType: hard

"aws4@npm:^1.8.0":
  version: 1.11.0
  resolution: "aws4@npm:1.11.0"
  checksum: 5a00d045fd0385926d20ebebcfba5ec79d4482fe706f63c27b324d489a04c68edb0db99ed991e19eda09cb8c97dc2452059a34d97545cebf591d7a2b5a10999f
  languageName: node
  linkType: hard

"axios@npm:0.18.1":
  version: 0.18.1
  resolution: "axios@npm:0.18.1"
  dependencies:
    follow-redirects: 1.5.10
    is-buffer: ^2.0.2
  checksum: 4a27cea1e3c674d89d9097fe13b62081b692280401b9c4216f37c42dfa8b433091baa15832336523ddad1df2f3f21e1e38a4cff207b4926ead3076c91a8ec5fe
  languageName: node
  linkType: hard

"babel-code-frame@npm:^6.22.0, babel-code-frame@npm:^6.26.0":
  version: 6.26.0
  resolution: "babel-code-frame@npm:6.26.0"
  dependencies:
    chalk: ^1.1.3
    esutils: ^2.0.2
    js-tokens: ^3.0.2
  checksum: 9410c3d5a921eb02fa409675d1a758e493323a49e7b9dddb7a2a24d47e61d39ab1129dd29f9175836eac9ce8b1d4c0a0718fcdc57ce0b865b529fd250dbab313
  languageName: node
  linkType: hard

"babel-core@npm:7.0.0-bridge.0":
  version: 7.0.0-bridge.0
  resolution: "babel-core@npm:7.0.0-bridge.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2a1cb879019dffb08d17bec36e13c3a6d74c94773f41c1fd8b14de13f149cc34b705b0a1e07b42fcf35917b49d78db6ff0c5c3b00b202a5235013d517b5c6bbb
  languageName: node
  linkType: hard

"babel-core@npm:^6.0.0, babel-core@npm:^6.26.0":
  version: 6.26.3
  resolution: "babel-core@npm:6.26.3"
  dependencies:
    babel-code-frame: ^6.26.0
    babel-generator: ^6.26.0
    babel-helpers: ^6.24.1
    babel-messages: ^6.23.0
    babel-register: ^6.26.0
    babel-runtime: ^6.26.0
    babel-template: ^6.26.0
    babel-traverse: ^6.26.0
    babel-types: ^6.26.0
    babylon: ^6.18.0
    convert-source-map: ^1.5.1
    debug: ^2.6.9
    json5: ^0.5.1
    lodash: ^4.17.4
    minimatch: ^3.0.4
    path-is-absolute: ^1.0.1
    private: ^0.1.8
    slash: ^1.0.0
    source-map: ^0.5.7
  checksum: 3d6a37e5c69ea7f7d66c2a261cbd7219197f2f938700e6ebbabb6d84a03f2bf86691ffa066866dcb49ba6c4bd702d347c9e0e147660847d709705cf43c964752
  languageName: node
  linkType: hard

"babel-eslint@npm:10.0.1":
  version: 10.0.1
  resolution: "babel-eslint@npm:10.0.1"
  dependencies:
    "@babel/code-frame": ^7.0.0
    "@babel/parser": ^7.0.0
    "@babel/traverse": ^7.0.0
    "@babel/types": ^7.0.0
    eslint-scope: 3.7.1
    eslint-visitor-keys: ^1.0.0
  peerDependencies:
    eslint: ">= 4.12.1"
  checksum: 4429f99aedbb1db5d134ba4995c160247d62038b3d0133084ce43f88cb1cb63c0298bb49dd747bfed1d70b35809846023063bb67cd44c65a5935efe31c104d55
  languageName: node
  linkType: hard

"babel-eslint@npm:^10.0.1":
  version: 10.1.0
  resolution: "babel-eslint@npm:10.1.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    "@babel/parser": ^7.7.0
    "@babel/traverse": ^7.7.0
    "@babel/types": ^7.7.0
    eslint-visitor-keys: ^1.0.0
    resolve: ^1.12.0
  peerDependencies:
    eslint: ">= 4.12.1"
  checksum: bdc1f62b6b0f9c4d5108c96d835dad0c0066bc45b7c020fcb2d6a08107cf69c9217a99d3438dbd701b2816896190c4283ba04270ed9a8349ee07bd8dafcdc050
  languageName: node
  linkType: hard

"babel-generator@npm:^6.18.0, babel-generator@npm:^6.26.0":
  version: 6.26.1
  resolution: "babel-generator@npm:6.26.1"
  dependencies:
    babel-messages: ^6.23.0
    babel-runtime: ^6.26.0
    babel-types: ^6.26.0
    detect-indent: ^4.0.0
    jsesc: ^1.3.0
    lodash: ^4.17.4
    source-map: ^0.5.7
    trim-right: ^1.0.1
  checksum: 5397f4d4d1243e7157e3336be96c10fcb1f29f73bf2d9842229c71764d9a6431397d249483a38c4d8b1581459e67be4df6f32d26b1666f02d0f5bfc2c2f25193
  languageName: node
  linkType: hard

"babel-helper-vue-jsx-merge-props@npm:^2.0.0":
  version: 2.0.3
  resolution: "babel-helper-vue-jsx-merge-props@npm:2.0.3"
  checksum: 94d354b401fae665137433632d6ecb4f212217c115feb06b2544987939fdb2735b35bc856eb99e306e8ead5f840b7891d8e050916a76c025b2380ee052a3b8df
  languageName: node
  linkType: hard

"babel-helpers@npm:^6.24.1":
  version: 6.24.1
  resolution: "babel-helpers@npm:6.24.1"
  dependencies:
    babel-runtime: ^6.22.0
    babel-template: ^6.24.1
  checksum: 751c6010e18648eebae422adfea5f3b5eff70d592d693bfe0f53346227d74b38e6cd2553c4c18de1e64faac585de490eccbd3ab86ba0885bdac42ed4478bc6b0
  languageName: node
  linkType: hard

"babel-jest@npm:23.6.0, babel-jest@npm:^23.6.0":
  version: 23.6.0
  resolution: "babel-jest@npm:23.6.0"
  dependencies:
    babel-plugin-istanbul: ^4.1.6
    babel-preset-jest: ^23.2.0
  peerDependencies:
    babel-core: ^6.0.0 || ^7.0.0-0
  checksum: c1b915c62919acc894a1fb991a932312b1a29bda3136766dbd988666f3e877fbe5e9924e8e4e800faf23ebd2bc7185a8f5aaf8bd0cd2b16dfa34ac9a19ab2693
  languageName: node
  linkType: hard

"babel-loader@npm:^8.0.5":
  version: 8.2.3
  resolution: "babel-loader@npm:8.2.3"
  dependencies:
    find-cache-dir: ^3.3.1
    loader-utils: ^1.4.0
    make-dir: ^3.1.0
    schema-utils: ^2.6.5
  peerDependencies:
    "@babel/core": ^7.0.0
    webpack: ">=2"
  checksum: 78e1e1a91954d644b6ce66366834d4d245febbc0fde33e4e2831725e83d6e760d12b3a78e9534ce92af69067bef1d9d9674df36d8c1f20ee127bc2354b2203ba
  languageName: node
  linkType: hard

"babel-messages@npm:^6.23.0":
  version: 6.23.0
  resolution: "babel-messages@npm:6.23.0"
  dependencies:
    babel-runtime: ^6.22.0
  checksum: c8075c17587a33869e1a5bd0a5b73bbe395b68188362dacd5418debbc7c8fd784bcd3295e81ee7e410dc2c2655755add6af03698c522209f6a68334c15e6d6ca
  languageName: node
  linkType: hard

"babel-plugin-dynamic-import-node@npm:^2.2.0, babel-plugin-dynamic-import-node@npm:^2.3.3":
  version: 2.3.3
  resolution: "babel-plugin-dynamic-import-node@npm:2.3.3"
  dependencies:
    object.assign: ^4.1.0
  checksum: c9d24415bcc608d0db7d4c8540d8002ac2f94e2573d2eadced137a29d9eab7e25d2cbb4bc6b9db65cf6ee7430f7dd011d19c911a9a778f0533b4a05ce8292c9b
  languageName: node
  linkType: hard

"babel-plugin-istanbul@npm:^4.1.6":
  version: 4.1.6
  resolution: "babel-plugin-istanbul@npm:4.1.6"
  dependencies:
    babel-plugin-syntax-object-rest-spread: ^6.13.0
    find-up: ^2.1.0
    istanbul-lib-instrument: ^1.10.1
    test-exclude: ^4.2.1
  checksum: 38456e3e426ffba8e708e1995ad527c62a666cf59fe809687a8c521d0acad52b92dac8fe17d4f2b93c593f19b6fd808b69a5d66729449af7662413f4a84e8a1c
  languageName: node
  linkType: hard

"babel-plugin-jest-hoist@npm:^23.2.0":
  version: 23.2.0
  resolution: "babel-plugin-jest-hoist@npm:23.2.0"
  checksum: 73d84b341a745e0e8cfa4af2eedc57ac960370261beb4eb9e383bd1e131a838ca747eb31eecb52d2ef80e2b496c2f5af760d7daf7a0bdb9311d4cd93e3c2cbb1
  languageName: node
  linkType: hard

"babel-plugin-module-resolver@npm:3.2.0":
  version: 3.2.0
  resolution: "babel-plugin-module-resolver@npm:3.2.0"
  dependencies:
    find-babel-config: ^1.1.0
    glob: ^7.1.2
    pkg-up: ^2.0.0
    reselect: ^3.0.1
    resolve: ^1.4.0
  checksum: ef4f7fdb6db97b8c5d7f70b85f54876a1bead35ee360b85a4153d02ce32eba4571afcc71ae11d43b2b6d4c5eeae865fc2127012f9534cfab07f998e2692671e5
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs2@npm:^0.3.0":
  version: 0.3.1
  resolution: "babel-plugin-polyfill-corejs2@npm:0.3.1"
  dependencies:
    "@babel/compat-data": ^7.13.11
    "@babel/helper-define-polyfill-provider": ^0.3.1
    semver: ^6.1.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: ca873f14ccd6d2942013345a956de8165d0913556ec29756a748157140f5312f79eed487674e0ca562285880f05829b3712d72e1e4b412c2ce46bd6a50b4b975
  languageName: node
  linkType: hard

"babel-plugin-polyfill-corejs3@npm:^0.5.0":
  version: 0.5.2
  resolution: "babel-plugin-polyfill-corejs3@npm:0.5.2"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.3.1
    core-js-compat: ^3.21.0
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 2f3184c73f80f00ac876a5ebcad945fd8d2ae70e5f85b7ab6cc3bc69bc74025f4f7070de7abbb2a7274c78e130bd34fc13f4c85342da28205930364a1ef0aa21
  languageName: node
  linkType: hard

"babel-plugin-polyfill-regenerator@npm:^0.3.0":
  version: 0.3.1
  resolution: "babel-plugin-polyfill-regenerator@npm:0.3.1"
  dependencies:
    "@babel/helper-define-polyfill-provider": ^0.3.1
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: f1473df7b700d6795ca41301b1e65a0aff15ce6c1463fc0ce2cf0c821114b0330920f59d4cebf52976363ee817ba29ad2758544a4661a724b08191080b9fe1da
  languageName: node
  linkType: hard

"babel-plugin-syntax-object-rest-spread@npm:^6.13.0":
  version: 6.13.0
  resolution: "babel-plugin-syntax-object-rest-spread@npm:6.13.0"
  checksum: 14083f2783c760f5f199160f48e42ad4505fd35fc7cf9c4530812b176705259562b77db6d3ddc5e3cbce9e9b2b61ec9db3065941f0949b68e77cae3e395a6eef
  languageName: node
  linkType: hard

"babel-plugin-transform-es2015-modules-commonjs@npm:^6.26.0, babel-plugin-transform-es2015-modules-commonjs@npm:^6.26.2":
  version: 6.26.2
  resolution: "babel-plugin-transform-es2015-modules-commonjs@npm:6.26.2"
  dependencies:
    babel-plugin-transform-strict-mode: ^6.24.1
    babel-runtime: ^6.26.0
    babel-template: ^6.26.0
    babel-types: ^6.26.0
  checksum: 9cd93a84037855c1879bcc100229bee25b44c4805a9a9f040e8927f772c4732fa17a0706c81ea0db77b357dd9baf84388eec03ceb36597932c48fe32fb3d4171
  languageName: node
  linkType: hard

"babel-plugin-transform-strict-mode@npm:^6.24.1":
  version: 6.24.1
  resolution: "babel-plugin-transform-strict-mode@npm:6.24.1"
  dependencies:
    babel-runtime: ^6.22.0
    babel-types: ^6.24.1
  checksum: 32d70ce9d8c8918a6a840e46df03dfe1e265eb9b25df5a800fedb5065ef1b4b5f24d7c62d92fca0e374db8b0b9b6f84e68edd02ad21883d48f608583ec29f638
  languageName: node
  linkType: hard

"babel-preset-jest@npm:^23.2.0":
  version: 23.2.0
  resolution: "babel-preset-jest@npm:23.2.0"
  dependencies:
    babel-plugin-jest-hoist: ^23.2.0
    babel-plugin-syntax-object-rest-spread: ^6.13.0
  checksum: aa17dc7607612554fc8f42ba2200f446e1c3e78ba9632286093d109aa70bd273e76567484033dc5acbd608a0081a6b92afd540311d28da395606c7fd609713db
  languageName: node
  linkType: hard

"babel-register@npm:^6.26.0":
  version: 6.26.0
  resolution: "babel-register@npm:6.26.0"
  dependencies:
    babel-core: ^6.26.0
    babel-runtime: ^6.26.0
    core-js: ^2.5.0
    home-or-tmp: ^2.0.0
    lodash: ^4.17.4
    mkdirp: ^0.5.1
    source-map-support: ^0.4.15
  checksum: 75d5fe060e4850dbdbd5f56db2928cd0b6b6c93a65ba5f2a991465af4dc3f4adf46d575138f228b2169b1e25e3b4a7cdd16515a355fea41b873321bf56467583
  languageName: node
  linkType: hard

"babel-runtime@npm:6.x, babel-runtime@npm:^6.22.0, babel-runtime@npm:^6.26.0":
  version: 6.26.0
  resolution: "babel-runtime@npm:6.26.0"
  dependencies:
    core-js: ^2.4.0
    regenerator-runtime: ^0.11.0
  checksum: 8aeade94665e67a73c1ccc10f6fd42ba0c689b980032b70929de7a6d9a12eb87ef51902733f8fefede35afea7a5c3ef7e916a64d503446c1eedc9e3284bd3d50
  languageName: node
  linkType: hard

"babel-template@npm:^6.16.0, babel-template@npm:^6.24.1, babel-template@npm:^6.26.0":
  version: 6.26.0
  resolution: "babel-template@npm:6.26.0"
  dependencies:
    babel-runtime: ^6.26.0
    babel-traverse: ^6.26.0
    babel-types: ^6.26.0
    babylon: ^6.18.0
    lodash: ^4.17.4
  checksum: 028dd57380f09b5641b74874a19073c53c4fb3f1696e849575aae18f8c80eaf21db75209057db862f3b893ce2cd9b795d539efa591b58f4a0fb011df0a56fbed
  languageName: node
  linkType: hard

"babel-traverse@npm:^6.0.0, babel-traverse@npm:^6.18.0, babel-traverse@npm:^6.26.0":
  version: 6.26.0
  resolution: "babel-traverse@npm:6.26.0"
  dependencies:
    babel-code-frame: ^6.26.0
    babel-messages: ^6.23.0
    babel-runtime: ^6.26.0
    babel-types: ^6.26.0
    babylon: ^6.18.0
    debug: ^2.6.8
    globals: ^9.18.0
    invariant: ^2.2.2
    lodash: ^4.17.4
  checksum: fca037588d2791ae0409f1b7aa56075b798699cccc53ea04d82dd1c0f97b9e7ab17065f7dd3ecd69101d7874c9c8fd5e0f88fa53abbae1fe94e37e6b81ebcb8d
  languageName: node
  linkType: hard

"babel-types@npm:^6.0.0, babel-types@npm:^6.18.0, babel-types@npm:^6.24.1, babel-types@npm:^6.26.0":
  version: 6.26.0
  resolution: "babel-types@npm:6.26.0"
  dependencies:
    babel-runtime: ^6.26.0
    esutils: ^2.0.2
    lodash: ^4.17.4
    to-fast-properties: ^1.0.3
  checksum: d16b0fa86e9b0e4c2623be81d0a35679faff24dd2e43cde4ca58baf49f3e39415a011a889e6c2259ff09e1228e4c3a3db6449a62de59e80152fe1ce7398fde76
  languageName: node
  linkType: hard

"babylon@npm:^6.18.0":
  version: 6.18.0
  resolution: "babylon@npm:6.18.0"
  bin:
    babylon: ./bin/babylon.js
  checksum: 0777ae0c735ce1cbfc856d627589ed9aae212b84fb0c03c368b55e6c5d3507841780052808d0ad46e18a2ba516e93d55eeed8cd967f3b2938822dfeccfb2a16d
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 9706c088a283058a8a99e0bf91b0a2f75497f185980d9ffa8b304de1d9e58ebda7c72c07ebf01dadedaac5b2907b2c6f566f660d62bd336c3468e960403b9d65
  languageName: node
  linkType: hard

"base64-js@npm:^1.0.2":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 669632eb3745404c2f822a18fc3a0122d2f9a7a13f7fb8b5823ee19d1d2ff9ee5b52c53367176ea4ad093c332fd5ab4bd0ebae5a8e27917a4105a4cfc86b1005
  languageName: node
  linkType: hard

"base@npm:^0.11.1":
  version: 0.11.2
  resolution: "base@npm:0.11.2"
  dependencies:
    cache-base: ^1.0.1
    class-utils: ^0.3.5
    component-emitter: ^1.2.1
    define-property: ^1.0.0
    isobject: ^3.0.1
    mixin-deep: ^1.2.0
    pascalcase: ^0.1.1
  checksum: a4a146b912e27eea8f66d09cb0c9eab666f32ce27859a7dfd50f38cd069a2557b39f16dba1bc2aecb3b44bf096738dd207b7970d99b0318423285ab1b1994edd
  languageName: node
  linkType: hard

"batch@npm:0.6.1":
  version: 0.6.1
  resolution: "batch@npm:0.6.1"
  checksum: 61f9934c7378a51dce61b915586191078ef7f1c3eca707fdd58b96ff2ff56d9e0af2bdab66b1462301a73c73374239e6542d9821c0af787f3209a23365d07e7f
  languageName: node
  linkType: hard

"bcrypt-pbkdf@npm:^1.0.0":
  version: 1.0.2
  resolution: "bcrypt-pbkdf@npm:1.0.2"
  dependencies:
    tweetnacl: ^0.14.3
  checksum: 4edfc9fe7d07019609ccf797a2af28351736e9d012c8402a07120c4453a3b789a15f2ee1530dc49eee8f7eb9379331a8dd4b3766042b9e502f74a68e7f662291
  languageName: node
  linkType: hard

"bfj@npm:^6.1.1":
  version: 6.1.2
  resolution: "bfj@npm:6.1.2"
  dependencies:
    bluebird: ^3.5.5
    check-types: ^8.0.3
    hoopy: ^0.1.4
    tryer: ^1.0.1
  checksum: 569726dd6b6d2f8f3cf2af84a1ac9d14e2336a1c9c09094cb429cc988cf99aba52ae4498a3bc81673aaf6c81bda1143bba76e86e4b2128568f3aa61b08d1662c
  languageName: node
  linkType: hard

"big.js@npm:^3.1.3":
  version: 3.2.0
  resolution: "big.js@npm:3.2.0"
  checksum: 299449e40555625a308f01d74378677036b2ec98b30aaa89794b3afbd4eaa104b7456a989affadfd7f630dc14b3f1df250de9bddc4a6fc664e60727887bb33e7
  languageName: node
  linkType: hard

"big.js@npm:^5.2.2":
  version: 5.2.2
  resolution: "big.js@npm:5.2.2"
  checksum: b89b6e8419b097a8fb4ed2399a1931a68c612bce3cfd5ca8c214b2d017531191070f990598de2fc6f3f993d91c0f08aa82697717f6b3b8732c9731866d233c9e
  languageName: node
  linkType: hard

"binary-extensions@npm:^1.0.0":
  version: 1.13.1
  resolution: "binary-extensions@npm:1.13.1"
  checksum: ad7747f33c07e94ba443055de130b50c8b8b130a358bca064c580d91769ca6a69c7ac65ca008ff044ed4541d2c6ad45496e1fadbef5218a68770996b6a2194d7
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.2.0
  resolution: "binary-extensions@npm:2.2.0"
  checksum: ccd267956c58d2315f5d3ea6757cf09863c5fc703e50fbeb13a7dc849b812ef76e3cf9ca8f35a0c48498776a7478d7b4a0418e1e2b8cb9cb9731f2922aaad7f8
  languageName: node
  linkType: hard

"bindings@npm:^1.5.0":
  version: 1.5.0
  resolution: "bindings@npm:1.5.0"
  dependencies:
    file-uri-to-path: 1.0.0
  checksum: 65b6b48095717c2e6105a021a7da4ea435aa8d3d3cd085cb9e85bcb6e5773cf318c4745c3f7c504412855940b585bdf9b918236612a1c7a7942491de176f1ae7
  languageName: node
  linkType: hard

"bluebird@npm:^3.1.1, bluebird@npm:^3.5.0, bluebird@npm:^3.5.1, bluebird@npm:^3.5.5":
  version: 3.7.2
  resolution: "bluebird@npm:3.7.2"
  checksum: 869417503c722e7dc54ca46715f70e15f4d9c602a423a02c825570862d12935be59ed9c7ba34a9b31f186c017c23cac6b54e35446f8353059c101da73eac22ef
  languageName: node
  linkType: hard

"bn.js@npm:^4.0.0, bn.js@npm:^4.1.0, bn.js@npm:^4.11.9":
  version: 4.12.0
  resolution: "bn.js@npm:4.12.0"
  checksum: 39afb4f15f4ea537b55eaf1446c896af28ac948fdcf47171961475724d1bb65118cca49fa6e3d67706e4790955ec0e74de584e45c8f1ef89f46c812bee5b5a12
  languageName: node
  linkType: hard

"bn.js@npm:^5.0.0, bn.js@npm:^5.1.1":
  version: 5.2.0
  resolution: "bn.js@npm:5.2.0"
  checksum: 6117170393200f68b35a061ecbf55d01dd989302e7b3c798a3012354fa638d124f0b2f79e63f77be5556be80322a09c40339eda6413ba7468524c0b6d4b4cb7a
  languageName: node
  linkType: hard

"body-parser@npm:1.19.1":
  version: 1.19.1
  resolution: "body-parser@npm:1.19.1"
  dependencies:
    bytes: 3.1.1
    content-type: ~1.0.4
    debug: 2.6.9
    depd: ~1.1.2
    http-errors: 1.8.1
    iconv-lite: 0.4.24
    on-finished: ~2.3.0
    qs: 6.9.6
    raw-body: 2.4.2
    type-is: ~1.6.18
  checksum: 9197a300a6580b8723c7b6b1e22cebd5ba47cd4a6fd45c153350efcde79293869ddee8d17d95fb52724812d649d89d62775faab072608d3243a0cbb00582234e
  languageName: node
  linkType: hard

"bonjour@npm:^3.5.0":
  version: 3.5.0
  resolution: "bonjour@npm:3.5.0"
  dependencies:
    array-flatten: ^2.1.0
    deep-equal: ^1.0.1
    dns-equal: ^1.0.0
    dns-txt: ^2.0.2
    multicast-dns: ^6.0.1
    multicast-dns-service-types: ^1.1.0
  checksum: 2cfbe9fa861f4507b5ff3853eeae3ef03a231ede2b7363efedd80880ea3c0576f64416f98056c96e429ed68ff38dc4a70c0583d1eb4dab72e491ca44a0f03444
  languageName: node
  linkType: hard

"boolbase@npm:^1.0.0, boolbase@npm:~1.0.0":
  version: 1.0.0
  resolution: "boolbase@npm:1.0.0"
  checksum: 3e25c80ef626c3a3487c73dbfc70ac322ec830666c9ad915d11b701142fab25ec1e63eff2c450c74347acfd2de854ccde865cd79ef4db1683f7c7b046ea43bb0
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.11
  resolution: "brace-expansion@npm:1.1.11"
  dependencies:
    balanced-match: ^1.0.0
    concat-map: 0.0.1
  checksum: faf34a7bb0c3fcf4b59c7808bc5d2a96a40988addf2e7e09dfbb67a2251800e0d14cd2bfc1aa79174f2f5095c54ff27f46fb1289fe2d77dac755b5eb3434cc07
  languageName: node
  linkType: hard

"braces@npm:^1.8.2":
  version: 1.8.5
  resolution: "braces@npm:1.8.5"
  dependencies:
    expand-range: ^1.8.1
    preserve: ^0.2.0
    repeat-element: ^1.1.2
  checksum: 9ea4fb6af8c0a224e515678d7be27ddc450bd974620542a3436188d7fae263f7987406d12ea36c1d92a862e7317b089aa3a0ebda7f1f1663b9332beddc92249e
  languageName: node
  linkType: hard

"braces@npm:^2.2.2, braces@npm:^2.3.1, braces@npm:^2.3.2":
  version: 2.3.2
  resolution: "braces@npm:2.3.2"
  dependencies:
    arr-flatten: ^1.1.0
    array-unique: ^0.3.2
    extend-shallow: ^2.0.1
    fill-range: ^4.0.0
    isobject: ^3.0.1
    repeat-element: ^1.1.2
    snapdragon: ^0.8.1
    snapdragon-node: ^2.0.1
    split-string: ^3.0.2
    to-regex: ^3.0.1
  checksum: e30dcb6aaf4a31c8df17d848aa283a65699782f75ad61ae93ec25c9729c66cf58e66f0000a9fec84e4add1135bb7da40f7cb9601b36bebcfa9ca58e8d5c07de0
  languageName: node
  linkType: hard

"braces@npm:~3.0.2":
  version: 3.0.2
  resolution: "braces@npm:3.0.2"
  dependencies:
    fill-range: ^7.0.1
  checksum: e2a8e769a863f3d4ee887b5fe21f63193a891c68b612ddb4b68d82d1b5f3ff9073af066c343e9867a393fe4c2555dcb33e89b937195feb9c1613d259edfcd459
  languageName: node
  linkType: hard

"brorand@npm:^1.0.1, brorand@npm:^1.1.0":
  version: 1.1.0
  resolution: "brorand@npm:1.1.0"
  checksum: 8a05c9f3c4b46572dec6ef71012b1946db6cae8c7bb60ccd4b7dd5a84655db49fe043ecc6272e7ef1f69dc53d6730b9e2a3a03a8310509a3d797a618cbee52be
  languageName: node
  linkType: hard

"browser-process-hrtime@npm:^1.0.0":
  version: 1.0.0
  resolution: "browser-process-hrtime@npm:1.0.0"
  checksum: e30f868cdb770b1201afb714ad1575dd86366b6e861900884665fb627109b3cc757c40067d3bfee1ff2a29c835257ea30725a8018a9afd02ac1c24b408b1e45f
  languageName: node
  linkType: hard

"browser-resolve@npm:^1.11.3":
  version: 1.11.3
  resolution: "browser-resolve@npm:1.11.3"
  dependencies:
    resolve: 1.1.7
  checksum: 431bfc1a17406362a3010a2c35503eb7d1253dbcb8081c1ce236ddb0b954a33d52dcaf0b07f64c0f20394d6eeec1be4f6551da3734ce9ed5dcc38e876c96d5d5
  languageName: node
  linkType: hard

"browserify-aes@npm:^1.0.0, browserify-aes@npm:^1.0.4":
  version: 1.2.0
  resolution: "browserify-aes@npm:1.2.0"
  dependencies:
    buffer-xor: ^1.0.3
    cipher-base: ^1.0.0
    create-hash: ^1.1.0
    evp_bytestokey: ^1.0.3
    inherits: ^2.0.1
    safe-buffer: ^5.0.1
  checksum: 4a17c3eb55a2aa61c934c286f34921933086bf6d67f02d4adb09fcc6f2fc93977b47d9d884c25619144fccd47b3b3a399e1ad8b3ff5a346be47270114bcf7104
  languageName: node
  linkType: hard

"browserify-cipher@npm:^1.0.0":
  version: 1.0.1
  resolution: "browserify-cipher@npm:1.0.1"
  dependencies:
    browserify-aes: ^1.0.4
    browserify-des: ^1.0.0
    evp_bytestokey: ^1.0.0
  checksum: 2d8500acf1ee535e6bebe808f7a20e4c3a9e2ed1a6885fff1facbfd201ac013ef030422bec65ca9ece8ffe82b03ca580421463f9c45af6c8415fd629f4118c13
  languageName: node
  linkType: hard

"browserify-des@npm:^1.0.0":
  version: 1.0.2
  resolution: "browserify-des@npm:1.0.2"
  dependencies:
    cipher-base: ^1.0.1
    des.js: ^1.0.0
    inherits: ^2.0.1
    safe-buffer: ^5.1.2
  checksum: b15a3e358a1d78a3b62ddc06c845d02afde6fc826dab23f1b9c016e643e7b1fda41de628d2110b712f6a44fb10cbc1800bc6872a03ddd363fb50768e010395b7
  languageName: node
  linkType: hard

"browserify-rsa@npm:^4.0.0, browserify-rsa@npm:^4.0.1":
  version: 4.1.0
  resolution: "browserify-rsa@npm:4.1.0"
  dependencies:
    bn.js: ^5.0.0
    randombytes: ^2.0.1
  checksum: 155f0c135873efc85620571a33d884aa8810e40176125ad424ec9d85016ff105a07f6231650914a760cca66f29af0494087947b7be34880dd4599a0cd3c38e54
  languageName: node
  linkType: hard

"browserify-sign@npm:^4.0.0":
  version: 4.2.1
  resolution: "browserify-sign@npm:4.2.1"
  dependencies:
    bn.js: ^5.1.1
    browserify-rsa: ^4.0.1
    create-hash: ^1.2.0
    create-hmac: ^1.1.7
    elliptic: ^6.5.3
    inherits: ^2.0.4
    parse-asn1: ^5.1.5
    readable-stream: ^3.6.0
    safe-buffer: ^5.2.0
  checksum: 0221f190e3f5b2d40183fa51621be7e838d9caa329fe1ba773406b7637855f37b30f5d83e52ff8f244ed12ffe6278dd9983638609ed88c841ce547e603855707
  languageName: node
  linkType: hard

"browserify-zlib@npm:^0.2.0":
  version: 0.2.0
  resolution: "browserify-zlib@npm:0.2.0"
  dependencies:
    pako: ~1.0.5
  checksum: 5cd9d6a665190fedb4a97dfbad8dabc8698d8a507298a03f42c734e96d58ca35d3c7d4085e283440bbca1cd1938cff85031728079bedb3345310c58ab1ec92d6
  languageName: node
  linkType: hard

"browserslist@npm:^4.0.0, browserslist@npm:^4.12.0, browserslist@npm:^4.17.5, browserslist@npm:^4.19.1, browserslist@npm:^4.3.4, browserslist@npm:^4.5.4":
  version: 4.19.1
  resolution: "browserslist@npm:4.19.1"
  dependencies:
    caniuse-lite: ^1.0.30001286
    electron-to-chromium: ^1.4.17
    escalade: ^3.1.1
    node-releases: ^2.0.1
    picocolors: ^1.0.0
  bin:
    browserslist: cli.js
  checksum: c0777fd483691638fd6801e16c9d809e1d65f6d2b06db2e806654be51045cbab1452a89841a2c5caea2cbe19d621b4f1d391cffbb24512aa33280039ab345875
  languageName: node
  linkType: hard

"bser@npm:2.1.1":
  version: 2.1.1
  resolution: "bser@npm:2.1.1"
  dependencies:
    node-int64: ^0.4.0
  checksum: 9ba4dc58ce86300c862bffc3ae91f00b2a03b01ee07f3564beeeaf82aa243b8b03ba53f123b0b842c190d4399b94697970c8e7cf7b1ea44b61aa28c3526a4449
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"buffer-indexof@npm:^1.0.0":
  version: 1.1.1
  resolution: "buffer-indexof@npm:1.1.1"
  checksum: 0967abc2981a8e7d776324c6b84811e4d84a7ead89b54a3bb8791437f0c4751afd060406b06db90a436f1cf771867331b5ecf5c4aca95b4ccb9f6cb146c22ebc
  languageName: node
  linkType: hard

"buffer-xor@npm:^1.0.3":
  version: 1.0.3
  resolution: "buffer-xor@npm:1.0.3"
  checksum: 10c520df29d62fa6e785e2800e586a20fc4f6dfad84bcdbd12e1e8a83856de1cb75c7ebd7abe6d036bbfab738a6cf18a3ae9c8e5a2e2eb3167ca7399ce65373a
  languageName: node
  linkType: hard

"buffer@npm:^4.3.0":
  version: 4.9.2
  resolution: "buffer@npm:4.9.2"
  dependencies:
    base64-js: ^1.0.2
    ieee754: ^1.1.4
    isarray: ^1.0.0
  checksum: 8801bc1ba08539f3be70eee307a8b9db3d40f6afbfd3cf623ab7ef41dffff1d0a31de0addbe1e66e0ca5f7193eeb667bfb1ecad3647f8f1b0750de07c13295c3
  languageName: node
  linkType: hard

"builtin-status-codes@npm:^3.0.0":
  version: 3.0.0
  resolution: "builtin-status-codes@npm:3.0.0"
  checksum: 1119429cf4b0d57bf76b248ad6f529167d343156ebbcc4d4e4ad600484f6bc63002595cbb61b67ad03ce55cd1d3c4711c03bbf198bf24653b8392420482f3773
  languageName: node
  linkType: hard

"bytes@npm:3.0.0":
  version: 3.0.0
  resolution: "bytes@npm:3.0.0"
  checksum: a2b386dd8188849a5325f58eef69c3b73c51801c08ffc6963eddc9be244089ba32d19347caf6d145c86f315ae1b1fc7061a32b0c1aa6379e6a719090287ed101
  languageName: node
  linkType: hard

"bytes@npm:3.1.1":
  version: 3.1.1
  resolution: "bytes@npm:3.1.1"
  checksum: 949ab99a385d6acf4d2c69f1afc618615dc905936e0b0b9aa94a9e94d722baaba44d6a0851536585a0892ae4d462b5a270ccb1b04c774640742cbde5538ca328
  languageName: node
  linkType: hard

"cacache@npm:^10.0.4":
  version: 10.0.4
  resolution: "cacache@npm:10.0.4"
  dependencies:
    bluebird: ^3.5.1
    chownr: ^1.0.1
    glob: ^7.1.2
    graceful-fs: ^4.1.11
    lru-cache: ^4.1.1
    mississippi: ^2.0.0
    mkdirp: ^0.5.1
    move-concurrently: ^1.0.1
    promise-inflight: ^1.0.1
    rimraf: ^2.6.2
    ssri: ^5.2.4
    unique-filename: ^1.1.0
    y18n: ^4.0.0
  checksum: 787207714c7624f1569448db576fb7f595e5612b6c0a4d2eaa951d1bab70c01746e2d77942a7cbecd938222ce6cc55ef758c228aa9022741864aed8e097e487c
  languageName: node
  linkType: hard

"cacache@npm:^12.0.2":
  version: 12.0.4
  resolution: "cacache@npm:12.0.4"
  dependencies:
    bluebird: ^3.5.5
    chownr: ^1.1.1
    figgy-pudding: ^3.5.1
    glob: ^7.1.4
    graceful-fs: ^4.1.15
    infer-owner: ^1.0.3
    lru-cache: ^5.1.1
    mississippi: ^3.0.0
    mkdirp: ^0.5.1
    move-concurrently: ^1.0.1
    promise-inflight: ^1.0.1
    rimraf: ^2.6.3
    ssri: ^6.0.1
    unique-filename: ^1.1.1
    y18n: ^4.0.0
  checksum: c88a72f36939b2523533946ffb27828443db5bf5995d761b35ae17af1eb6c8e20ac55b00b74c2ca900b2e1e917f0afba6847bf8cc16bee05ccca6aa150e0830c
  languageName: node
  linkType: hard

"cacache@npm:^15.2.0":
  version: 15.3.0
  resolution: "cacache@npm:15.3.0"
  dependencies:
    "@npmcli/fs": ^1.0.0
    "@npmcli/move-file": ^1.0.1
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    glob: ^7.1.4
    infer-owner: ^1.0.4
    lru-cache: ^6.0.0
    minipass: ^3.1.1
    minipass-collect: ^1.0.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.2
    mkdirp: ^1.0.3
    p-map: ^4.0.0
    promise-inflight: ^1.0.1
    rimraf: ^3.0.2
    ssri: ^8.0.1
    tar: ^6.0.2
    unique-filename: ^1.1.1
  checksum: a07327c27a4152c04eb0a831c63c00390d90f94d51bb80624a66f4e14a6b6360bbf02a84421267bd4d00ca73ac9773287d8d7169e8d2eafe378d2ce140579db8
  languageName: node
  linkType: hard

"cache-base@npm:^1.0.1":
  version: 1.0.1
  resolution: "cache-base@npm:1.0.1"
  dependencies:
    collection-visit: ^1.0.0
    component-emitter: ^1.2.1
    get-value: ^2.0.6
    has-value: ^1.0.0
    isobject: ^3.0.1
    set-value: ^2.0.0
    to-object-path: ^0.3.0
    union-value: ^1.0.0
    unset-value: ^1.0.0
  checksum: 9114b8654fe2366eedc390bad0bcf534e2f01b239a888894e2928cb58cdc1e6ea23a73c6f3450dcfd2058aa73a8a981e723cd1e7c670c047bf11afdc65880107
  languageName: node
  linkType: hard

"cache-loader@npm:^2.0.1":
  version: 2.0.1
  resolution: "cache-loader@npm:2.0.1"
  dependencies:
    loader-utils: ^1.1.0
    mkdirp: ^0.5.1
    neo-async: ^2.6.0
    normalize-path: ^3.0.0
    schema-utils: ^1.0.0
  peerDependencies:
    webpack: ^4.0.0
  checksum: 9692fb409e5e067eddc67c142cabda452a1fddcd95229d5693184c0920178ac0f46d0159ec4aa539420feac7d8707565ce52f968e4829355b79c37027b9826c1
  languageName: node
  linkType: hard

"call-bind@npm:^1.0.0, call-bind@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind@npm:1.0.2"
  dependencies:
    function-bind: ^1.1.1
    get-intrinsic: ^1.0.2
  checksum: f8e31de9d19988a4b80f3e704788c4a2d6b6f3d17cfec4f57dc29ced450c53a49270dc66bf0fbd693329ee948dd33e6c90a329519aef17474a4d961e8d6426b0
  languageName: node
  linkType: hard

"call-me-maybe@npm:^1.0.1":
  version: 1.0.1
  resolution: "call-me-maybe@npm:1.0.1"
  checksum: d19e9d6ac2c6a83fb1215718b64c5e233f688ebebb603bdfe4af59cde952df1f2b648530fab555bf290ea910d69d7d9665ebc916e871e0e194f47c2e48e4886b
  languageName: node
  linkType: hard

"caller-callsite@npm:^2.0.0":
  version: 2.0.0
  resolution: "caller-callsite@npm:2.0.0"
  dependencies:
    callsites: ^2.0.0
  checksum: b685e9d126d9247b320cfdfeb3bc8da0c4be28d8fb98c471a96bc51aab3130099898a2fe3bf0308f0fe048d64c37d6d09f563958b9afce1a1e5e63d879c128a2
  languageName: node
  linkType: hard

"caller-path@npm:^0.1.0":
  version: 0.1.0
  resolution: "caller-path@npm:0.1.0"
  dependencies:
    callsites: ^0.2.0
  checksum: f4f2216897d2c150e30d06c6a9243115e500184433b42d597f0b8816fda8f6b7f782dba39fc37310dcc67c90e1112729709d3bb9e10983552e76632250b075f3
  languageName: node
  linkType: hard

"caller-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "caller-path@npm:2.0.0"
  dependencies:
    caller-callsite: ^2.0.0
  checksum: 3e12ccd0c71ec10a057aac69e3ec175b721ca858c640df021ef0d25999e22f7c1d864934b596b7d47038e9b56b7ec315add042abbd15caac882998b50102fb12
  languageName: node
  linkType: hard

"callsites@npm:^0.2.0":
  version: 0.2.0
  resolution: "callsites@npm:0.2.0"
  checksum: 7ed36d5565ec37600fd9642b6e1e00915c078bf2d1c536115c45fe9c07969b50a7d7db61384e533625fa9fee3e6187740784722f0636d52a99419111284cc236
  languageName: node
  linkType: hard

"callsites@npm:^2.0.0":
  version: 2.0.0
  resolution: "callsites@npm:2.0.0"
  checksum: be2f67b247df913732b7dec1ec0bbfcdbaea263e5a95968b19ec7965affae9496b970e3024317e6d4baa8e28dc6ba0cec03f46fdddc2fdcc51396600e53c2623
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 072d17b6abb459c2ba96598918b55868af677154bec7e73d222ef95a8fdb9bbf7dae96a8421085cdad8cd190d86653b5b6dc55a4484f2e5b2e27d5e0c3fc15b3
  languageName: node
  linkType: hard

"camel-case@npm:3.0.x":
  version: 3.0.0
  resolution: "camel-case@npm:3.0.0"
  dependencies:
    no-case: ^2.2.0
    upper-case: ^1.1.1
  checksum: 4190ed6ab8acf4f3f6e1a78ad4d0f3f15ce717b6bfa1b5686d58e4bcd29960f6e312dd746b5fa259c6d452f1413caef25aee2e10c9b9a580ac83e516533a961a
  languageName: node
  linkType: hard

"camelcase@npm:^4.1.0":
  version: 4.1.0
  resolution: "camelcase@npm:4.1.0"
  checksum: 9683356daf9b64fae4b30c91f8ceb1f34f22746e03d1804efdbe738357d38b47f206cdd71efcf2ed72018b2e88eeb8ec3f79adb09c02f1253a4b6d5d405ff2ae
  languageName: node
  linkType: hard

"camelcase@npm:^5.0.0":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: e6effce26b9404e3c0f301498184f243811c30dfe6d0b9051863bd8e4034d09c8c2923794f280d6827e5aa055f6c434115ff97864a16a963366fb35fd673024b
  languageName: node
  linkType: hard

"caniuse-api@npm:^3.0.0":
  version: 3.0.0
  resolution: "caniuse-api@npm:3.0.0"
  dependencies:
    browserslist: ^4.0.0
    caniuse-lite: ^1.0.0
    lodash.memoize: ^4.1.2
    lodash.uniq: ^4.5.0
  checksum: db2a229383b20d0529b6b589dde99d7b6cb56ba371366f58cbbfa2929c9f42c01f873e2b6ef641d4eda9f0b4118de77dbb2805814670bdad4234bf08e720b0b4
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.0, caniuse-lite@npm:^1.0.30001109, caniuse-lite@npm:^1.0.30001286":
  version: 1.0.30001309
  resolution: "caniuse-lite@npm:1.0.30001309"
  checksum: 95093d01dc0880a371fe6aaf54cccb01a979933181a3aad6fc08e80c4601e27e1578a44d8ab6c1d3d58526ed2a917ce5e643c0e8299a0330cdac7bc8a7b5d7f0
  languageName: node
  linkType: hard

"capture-exit@npm:^1.2.0":
  version: 1.2.0
  resolution: "capture-exit@npm:1.2.0"
  dependencies:
    rsvp: ^3.3.3
  checksum: 8d38f66d36cdc891fc5371315c0e6e8e094bfb86d769f0781d8acf1551ef513a43f5b8bd4c984255fc85ce4cc7a111f334d2aae454b86eef535d44ddec344ec7
  languageName: node
  linkType: hard

"case-sensitive-paths-webpack-plugin@npm:^2.2.0":
  version: 2.4.0
  resolution: "case-sensitive-paths-webpack-plugin@npm:2.4.0"
  checksum: bcf469446eeee9ac0046e30860074ebb9aa4803aab9140e6bb72b600b23b1d70635690754be4504ce35cd99cdf05226bee8d894ba362a3f5485d5f6310fc6d02
  languageName: node
  linkType: hard

"caseless@npm:~0.12.0":
  version: 0.12.0
  resolution: "caseless@npm:0.12.0"
  checksum: b43bd4c440aa1e8ee6baefee8063b4850fd0d7b378f6aabc796c9ec8cb26d27fb30b46885350777d9bd079c5256c0e1329ad0dc7c2817e0bb466810ebb353751
  languageName: node
  linkType: hard

"chalk@npm:2.3.0":
  version: 2.3.0
  resolution: "chalk@npm:2.3.0"
  dependencies:
    ansi-styles: ^3.1.0
    escape-string-regexp: ^1.0.5
    supports-color: ^4.0.0
  checksum: d348fc0f4f8d27c068a6ac492e708fa35a75e273d5f0004da61ea694e958981658c96693790f4d23e7b3712f9e3e4ca0988136cb0403876de5459a4c0d13078f
  languageName: node
  linkType: hard

"chalk@npm:2.4.2, chalk@npm:^2.0.0, chalk@npm:^2.0.1, chalk@npm:^2.1.0, chalk@npm:^2.4.1, chalk@npm:^2.4.2":
  version: 2.4.2
  resolution: "chalk@npm:2.4.2"
  dependencies:
    ansi-styles: ^3.2.1
    escape-string-regexp: ^1.0.5
    supports-color: ^5.3.0
  checksum: ec3661d38fe77f681200f878edbd9448821924e0f93a9cefc0e26a33b145f1027a2084bf19967160d11e1f03bfe4eaffcabf5493b89098b2782c3fe0b03d80c2
  languageName: node
  linkType: hard

"chalk@npm:^1.1.3":
  version: 1.1.3
  resolution: "chalk@npm:1.1.3"
  dependencies:
    ansi-styles: ^2.2.1
    escape-string-regexp: ^1.0.2
    has-ansi: ^2.0.0
    strip-ansi: ^3.0.0
    supports-color: ^2.0.0
  checksum: 9d2ea6b98fc2b7878829eec223abcf404622db6c48396a9b9257f6d0ead2acf18231ae368d6a664a83f272b0679158da12e97b5229f794939e555cc574478acd
  languageName: node
  linkType: hard

"chalk@npm:^3.0.0":
  version: 3.0.0
  resolution: "chalk@npm:3.0.0"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: 8e3ddf3981c4da405ddbd7d9c8d91944ddf6e33d6837756979f7840a29272a69a5189ecae0ff84006750d6d1e92368d413335eab4db5476db6e6703a1d1e0505
  languageName: node
  linkType: hard

"charcodes@npm:^0.2.0":
  version: 0.2.0
  resolution: "charcodes@npm:0.2.0"
  checksum: 972443ed359d54382e721b9db0a298eb95c4c454386f7e98886586f433e1e6686225416114e6f6bb2e6ef3facc9ba3b4ab9946a56a180fe64ef67816a05d4fe4
  languageName: node
  linkType: hard

"chardet@npm:^0.4.0":
  version: 0.4.2
  resolution: "chardet@npm:0.4.2"
  checksum: fec7a41f78b9c09ed29c44990a9a0fce7a946ab81298231045db5786719fef664cd9ff4217dd7159a9c35c81f32cede04619c45f9a96965ca2c1d8883f8cf433
  languageName: node
  linkType: hard

"chardet@npm:^0.7.0":
  version: 0.7.0
  resolution: "chardet@npm:0.7.0"
  checksum: 6fd5da1f5d18ff5712c1e0aed41da200d7c51c28f11b36ee3c7b483f3696dabc08927fc6b227735eb8f0e1215c9a8abd8154637f3eff8cada5959df7f58b024d
  languageName: node
  linkType: hard

"check-types@npm:^8.0.3":
  version: 8.0.3
  resolution: "check-types@npm:8.0.3"
  checksum: 9cf92c909ca13bfbfb51beb7bd660f7583d3445f2e4c2d5eb8043f44daf20b5fa48377516988a430098a555d9c15450178878879d1219fca6e2ee61afaabee2e
  languageName: node
  linkType: hard

"chokidar@npm:>=3.0.0 <4.0.0, chokidar@npm:^3.4.1":
  version: 3.5.3
  resolution: "chokidar@npm:3.5.3"
  dependencies:
    anymatch: ~3.1.2
    braces: ~3.0.2
    fsevents: ~2.3.2
    glob-parent: ~5.1.2
    is-binary-path: ~2.1.0
    is-glob: ~4.0.1
    normalize-path: ~3.0.0
    readdirp: ~3.6.0
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: b49fcde40176ba007ff361b198a2d35df60d9bb2a5aab228279eb810feae9294a6b4649ab15981304447afe1e6ffbf4788ad5db77235dc770ab777c6e771980c
  languageName: node
  linkType: hard

"chokidar@npm:^2.1.8":
  version: 2.1.8
  resolution: "chokidar@npm:2.1.8"
  dependencies:
    anymatch: ^2.0.0
    async-each: ^1.0.1
    braces: ^2.3.2
    fsevents: ^1.2.7
    glob-parent: ^3.1.0
    inherits: ^2.0.3
    is-binary-path: ^1.0.0
    is-glob: ^4.0.0
    normalize-path: ^3.0.0
    path-is-absolute: ^1.0.0
    readdirp: ^2.2.1
    upath: ^1.1.1
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 0c43e89cbf0268ef1e1f41ce8ec5233c7ba022c6f3282c2ef6530e351d42396d389a1148c5a040f291cf1f4083a4c6b2f51dad3f31c726442ea9a337de316bcf
  languageName: node
  linkType: hard

"chownr@npm:^1.0.1, chownr@npm:^1.1.1":
  version: 1.1.4
  resolution: "chownr@npm:1.1.4"
  checksum: 115648f8eb38bac5e41c3857f3e663f9c39ed6480d1349977c4d96c95a47266fcacc5a5aabf3cb6c481e22d72f41992827db47301851766c4fd77ac21a4f081d
  languageName: node
  linkType: hard

"chownr@npm:^2.0.0":
  version: 2.0.0
  resolution: "chownr@npm:2.0.0"
  checksum: c57cf9dd0791e2f18a5ee9c1a299ae6e801ff58fee96dc8bfd0dcb4738a6ce58dd252a3605b1c93c6418fe4f9d5093b28ffbf4d66648cb2a9c67eaef9679be2f
  languageName: node
  linkType: hard

"chrome-trace-event@npm:^1.0.0, chrome-trace-event@npm:^1.0.2":
  version: 1.0.3
  resolution: "chrome-trace-event@npm:1.0.3"
  checksum: cb8b1fc7e881aaef973bd0c4a43cd353c2ad8323fb471a041e64f7c2dd849cde4aad15f8b753331a32dda45c973f032c8a03b8177fc85d60eaa75e91e08bfb97
  languageName: node
  linkType: hard

"ci-info@npm:^1.5.0":
  version: 1.6.0
  resolution: "ci-info@npm:1.6.0"
  checksum: dfc058f60c3889793befe77349c3cd1a5452d21bed5ff60cb34382bee7bbdccc5c4c2ff2b77eab8c411c54d84f93963dacf593b9d901b43b93b7ad2a422aa163
  languageName: node
  linkType: hard

"ci-info@npm:^2.0.0":
  version: 2.0.0
  resolution: "ci-info@npm:2.0.0"
  checksum: 3b374666a85ea3ca43fa49aa3a048d21c9b475c96eb13c133505d2324e7ae5efd6a454f41efe46a152269e9b6a00c9edbe63ec7fa1921957165aae16625acd67
  languageName: node
  linkType: hard

"cipher-base@npm:^1.0.0, cipher-base@npm:^1.0.1, cipher-base@npm:^1.0.3":
  version: 1.0.4
  resolution: "cipher-base@npm:1.0.4"
  dependencies:
    inherits: ^2.0.1
    safe-buffer: ^5.0.1
  checksum: 47d3568dbc17431a339bad1fe7dff83ac0891be8206911ace3d3b818fc695f376df809bea406e759cdea07fff4b454fa25f1013e648851bec790c1d75763032e
  languageName: node
  linkType: hard

"circular-json@npm:^0.3.1":
  version: 0.3.3
  resolution: "circular-json@npm:0.3.3"
  checksum: 61c5e5c244cc752f2d81fa337260327bf9dcb0332eb801039f21b69383050318fd4b30541ce4ebfe6fdd62993a38cb1be3a0d777abfeadf723479a4fc2da70ed
  languageName: node
  linkType: hard

"class-utils@npm:^0.3.5":
  version: 0.3.6
  resolution: "class-utils@npm:0.3.6"
  dependencies:
    arr-union: ^3.1.0
    define-property: ^0.2.5
    isobject: ^3.0.0
    static-extend: ^0.1.1
  checksum: be108900801e639e50f96a7e4bfa8867c753a7750a7603879f3981f8b0a89cba657497a2d5f40cd4ea557ff15d535a100818bb486baf6e26fe5d7872e75f1078
  languageName: node
  linkType: hard

"clean-css@npm:4.2.x":
  version: 4.2.4
  resolution: "clean-css@npm:4.2.4"
  dependencies:
    source-map: ~0.6.0
  checksum: 045ff6fcf4b5c76a084b24e1633e0c78a13b24080338fc8544565a9751559aa32ff4ee5886d9e52c18a644a6ff119bd8e37bc58e574377c05382a1fb7dbe39f8
  languageName: node
  linkType: hard

"clean-stack@npm:^2.0.0":
  version: 2.2.0
  resolution: "clean-stack@npm:2.2.0"
  checksum: 2ac8cd2b2f5ec986a3c743935ec85b07bc174d5421a5efc8017e1f146a1cf5f781ae962618f416352103b32c9cd7e203276e8c28241bbe946160cab16149fb68
  languageName: node
  linkType: hard

"cli-cursor@npm:^2.1.0":
  version: 2.1.0
  resolution: "cli-cursor@npm:2.1.0"
  dependencies:
    restore-cursor: ^2.0.0
  checksum: d88e97bfdac01046a3ffe7d49f06757b3126559d7e44aa2122637eb179284dc6cd49fca2fac4f67c19faaf7e6dab716b6fe1dfcd309977407d8c7578ec2d044d
  languageName: node
  linkType: hard

"cli-spinners@npm:^2.0.0":
  version: 2.6.1
  resolution: "cli-spinners@npm:2.6.1"
  checksum: 423409baaa7a58e5104b46ca1745fbfc5888bbd0b0c5a626e052ae1387060839c8efd512fb127e25769b3dc9562db1dc1b5add6e0b93b7ef64f477feb6416a45
  languageName: node
  linkType: hard

"cli-width@npm:^2.0.0":
  version: 2.2.1
  resolution: "cli-width@npm:2.2.1"
  checksum: 3c21b897a2ff551ae5b3c3ab32c866ed2965dcf7fb442f81adf0e27f4a397925c8f84619af7bcc6354821303f6ee9b2aa31d248306174f32c287986158cf4eed
  languageName: node
  linkType: hard

"clipboard@npm:^2.0.6":
  version: 2.0.10
  resolution: "clipboard@npm:2.0.10"
  dependencies:
    good-listener: ^1.2.2
    select: ^1.1.2
    tiny-emitter: ^2.0.0
  checksum: 401ae9f27c9cb8f03f7a33e1a06ad8f1208a75dc36d037cc6542708c8e4a43974c4e4186d9154e0161541934bb2073ee2e078db80e5c7df3f0544aad2508262c
  languageName: node
  linkType: hard

"clipboardy@npm:^2.0.0":
  version: 2.3.0
  resolution: "clipboardy@npm:2.3.0"
  dependencies:
    arch: ^2.1.1
    execa: ^1.0.0
    is-wsl: ^2.1.1
  checksum: 2733790bc8bbb76a5be7706fa4632f655010774e579a9d3ebe31dc10cf44a2b82cf07b0b6f74162e63048ce32d912193c08c5b5311dce5c19fc641a3bda1292b
  languageName: node
  linkType: hard

"cliui@npm:^4.0.0":
  version: 4.1.0
  resolution: "cliui@npm:4.1.0"
  dependencies:
    string-width: ^2.1.1
    strip-ansi: ^4.0.0
    wrap-ansi: ^2.0.0
  checksum: 0f8a77e55c66ab4400f8cc24a46e496af186ebfbf301709341a24c26d398200c2ccc5cac892566d586c3c393a079974f34f0ce05210df336f97b70805c02865e
  languageName: node
  linkType: hard

"cliui@npm:^5.0.0":
  version: 5.0.0
  resolution: "cliui@npm:5.0.0"
  dependencies:
    string-width: ^3.1.0
    strip-ansi: ^5.2.0
    wrap-ansi: ^5.1.0
  checksum: 0bb8779efe299b8f3002a73619eaa8add4081eb8d1c17bc4fedc6240557fb4eacdc08fe87c39b002eacb6cfc117ce736b362dbfd8bf28d90da800e010ee97df4
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.1":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: ^2.0.4
    kind-of: ^6.0.2
    shallow-clone: ^3.0.0
  checksum: 770f912fe4e6f21873c8e8fbb1e99134db3b93da32df271d00589ea4a29dbe83a9808a322c93f3bcaf8584b8b4fa6fc269fc8032efbaa6728e0c9886c74467d2
  languageName: node
  linkType: hard

"clone@npm:2.x, clone@npm:^2.1.1":
  version: 2.1.2
  resolution: "clone@npm:2.1.2"
  checksum: aaf106e9bc025b21333e2f4c12da539b568db4925c0501a1bf4070836c9e848c892fa22c35548ce0d1132b08bbbfa17a00144fe58fccdab6fa900fec4250f67d
  languageName: node
  linkType: hard

"clone@npm:^1.0.2":
  version: 1.0.4
  resolution: "clone@npm:1.0.4"
  checksum: d06418b7335897209e77bdd430d04f882189582e67bd1f75a04565f3f07f5b3f119a9d670c943b6697d0afb100f03b866b3b8a1f91d4d02d72c4ecf2bb64b5dd
  languageName: node
  linkType: hard

"co@npm:^4.6.0":
  version: 4.6.0
  resolution: "co@npm:4.6.0"
  checksum: 5210d9223010eb95b29df06a91116f2cf7c8e0748a9013ed853b53f362ea0e822f1e5bb054fb3cefc645239a4cf966af1f6133a3b43f40d591f3b68ed6cf0510
  languageName: node
  linkType: hard

"coa@npm:^2.0.2":
  version: 2.0.2
  resolution: "coa@npm:2.0.2"
  dependencies:
    "@types/q": ^1.5.1
    chalk: ^2.4.1
    q: ^1.1.2
  checksum: 44736914aac2160d3d840ed64432a90a3bb72285a0cd6a688eb5cabdf15d15a85eee0915b3f6f2a4659d5075817b1cb577340d3c9cbb47d636d59ab69f819552
  languageName: node
  linkType: hard

"code-point-at@npm:^1.0.0":
  version: 1.1.0
  resolution: "code-point-at@npm:1.1.0"
  checksum: 17d5666611f9b16d64fdf48176d9b7fb1c7d1c1607a189f7e600040a11a6616982876af148230336adb7d8fe728a559f743a4e29db3747e3b1a32fa7f4529681
  languageName: node
  linkType: hard

"collection-visit@npm:^1.0.0":
  version: 1.0.0
  resolution: "collection-visit@npm:1.0.0"
  dependencies:
    map-visit: ^1.0.0
    object-visit: ^1.0.0
  checksum: 15d9658fe6eb23594728346adad5433b86bb7a04fd51bbab337755158722f9313a5376ef479de5b35fbc54140764d0d39de89c339f5d25b959ed221466981da9
  languageName: node
  linkType: hard

"color-convert@npm:^1.9.0, color-convert@npm:^1.9.3":
  version: 1.9.3
  resolution: "color-convert@npm:1.9.3"
  dependencies:
    color-name: 1.1.3
  checksum: fd7a64a17cde98fb923b1dd05c5f2e6f7aefda1b60d67e8d449f9328b4e53b228a428fd38bfeaeb2db2ff6b6503a776a996150b80cdf224062af08a5c8a3a203
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:1.1.3":
  version: 1.1.3
  resolution: "color-name@npm:1.1.3"
  checksum: 09c5d3e33d2105850153b14466501f2bfb30324a2f76568a408763a3b7433b0e50e5b4ab1947868e65cb101bb7cb75029553f2c333b6d4b8138a73fcc133d69d
  languageName: node
  linkType: hard

"color-name@npm:^1.0.0, color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"color-string@npm:^1.6.0":
  version: 1.9.0
  resolution: "color-string@npm:1.9.0"
  dependencies:
    color-name: ^1.0.0
    simple-swizzle: ^0.2.2
  checksum: 93c6678b847f8cfa47d19677fd19e1d4b19d7a33f100644400357c298266080b5bca64e5f874fa8ac8cc0aa0606ad44f7a838b4e6fd05e6affea190a68555bb4
  languageName: node
  linkType: hard

"color-support@npm:^1.1.2":
  version: 1.1.3
  resolution: "color-support@npm:1.1.3"
  bin:
    color-support: bin.js
  checksum: 9b7356817670b9a13a26ca5af1c21615463b500783b739b7634a0c2047c16cef4b2865d7576875c31c3cddf9dd621fa19285e628f20198b233a5cfdda6d0793b
  languageName: node
  linkType: hard

"color@npm:^3.0.0":
  version: 3.2.1
  resolution: "color@npm:3.2.1"
  dependencies:
    color-convert: ^1.9.3
    color-string: ^1.6.0
  checksum: f81220e8b774d35865c2561be921f5652117638dcda7ca4029262046e37fc2444ac7bbfdd110cf1fd9c074a4ee5eda8f85944ffbdda26186b602dd9bb05f6400
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.6, combined-stream@npm:~1.0.6":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: ~1.0.0
  checksum: 49fa4aeb4916567e33ea81d088f6584749fc90c7abec76fd516bf1c5aa5c79f3584b5ba3de6b86d26ddd64bae5329c4c7479343250cfe71c75bb366eae53bb7c
  languageName: node
  linkType: hard

"commander@npm:*":
  version: 9.0.0
  resolution: "commander@npm:9.0.0"
  checksum: 15066e433d528315ded8261d16bc600d1f3c5671c75021e685ae67e4d62f7551243ff28411b28dc0a6f8b23c2a0f033550ec6f3e66bdf9d11a4fdc2d33dd9802
  languageName: node
  linkType: hard

"commander@npm:2.17.x":
  version: 2.17.1
  resolution: "commander@npm:2.17.1"
  checksum: 22e7ed5b422079a13a496e5eb8f73f36c15b5809d46f738e168e20f9ad485c12951bdc2cb366a36eb5f4927dae4f17b355b8adb96a5b9093f5fa4c439e8b9419
  languageName: node
  linkType: hard

"commander@npm:^2.18.0, commander@npm:^2.19.0, commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: ab8c07884e42c3a8dbc5dd9592c606176c7eb5c1ca5ff274bcf907039b2c41de3626f684ea75ccf4d361ba004bbaff1f577d5384c155f3871e456bdf27becf9e
  languageName: node
  linkType: hard

"commander@npm:~2.19.0":
  version: 2.19.0
  resolution: "commander@npm:2.19.0"
  checksum: d52ffb0b31528784005356f879591b5a4875d3e88806c115fb30a8de0994d2fa9ca3f72a3cb880cdaf1bfb9df185f928cfcbbc656fa831f9c6109a209569ef6d
  languageName: node
  linkType: hard

"commondir@npm:^1.0.1":
  version: 1.0.1
  resolution: "commondir@npm:1.0.1"
  checksum: 59715f2fc456a73f68826285718503340b9f0dd89bfffc42749906c5cf3d4277ef11ef1cca0350d0e79204f00f1f6d83851ececc9095dc88512a697ac0b9bdcb
  languageName: node
  linkType: hard

"component-emitter@npm:^1.2.1":
  version: 1.3.0
  resolution: "component-emitter@npm:1.3.0"
  checksum: b3c46de38ffd35c57d1c02488355be9f218e582aec72d72d1b8bbec95a3ac1b38c96cd6e03ff015577e68f550fbb361a3bfdbd9bb248be9390b7b3745691be6b
  languageName: node
  linkType: hard

"compressible@npm:~2.0.16":
  version: 2.0.18
  resolution: "compressible@npm:2.0.18"
  dependencies:
    mime-db: ">= 1.43.0 < 2"
  checksum: 58321a85b375d39230405654721353f709d0c1442129e9a17081771b816302a012471a9b8f4864c7dbe02eef7f2aaac3c614795197092262e94b409c9be108f0
  languageName: node
  linkType: hard

"compression@npm:^1.7.4":
  version: 1.7.4
  resolution: "compression@npm:1.7.4"
  dependencies:
    accepts: ~1.3.5
    bytes: 3.0.0
    compressible: ~2.0.16
    debug: 2.6.9
    on-headers: ~1.0.2
    safe-buffer: 5.1.2
    vary: ~1.1.2
  checksum: 35c0f2eb1f28418978615dc1bc02075b34b1568f7f56c62d60f4214d4b7cc00d0f6d282b5f8a954f59872396bd770b6b15ffd8aa94c67d4bce9b8887b906999b
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 902a9f5d8967a3e2faf138d5cb784b9979bad2e6db5357c5b21c568df4ebe62bcb15108af1b2253744844eb964fc023fbd9afbbbb6ddd0bcc204c6fb5b7bf3af
  languageName: node
  linkType: hard

"concat-stream@npm:^1.5.0, concat-stream@npm:^1.6.0":
  version: 1.6.2
  resolution: "concat-stream@npm:1.6.2"
  dependencies:
    buffer-from: ^1.0.0
    inherits: ^2.0.3
    readable-stream: ^2.2.2
    typedarray: ^0.0.6
  checksum: 1ef77032cb4459dcd5187bd710d6fc962b067b64ec6a505810de3d2b8cc0605638551b42f8ec91edf6fcd26141b32ef19ad749239b58fae3aba99187adc32285
  languageName: node
  linkType: hard

"condense-newlines@npm:^0.2.1":
  version: 0.2.1
  resolution: "condense-newlines@npm:0.2.1"
  dependencies:
    extend-shallow: ^2.0.1
    is-whitespace: ^0.3.0
    kind-of: ^3.0.2
  checksum: 3c20ff6ee88b5d2e81c122f33b5ba5d6976cdf86d83527fadea308b3020ed70af7ed98c2e2d94d36f27fcd723a7a477941c19575e0d2c8db6afc4aac6926a54e
  languageName: node
  linkType: hard

"config-chain@npm:^1.1.12":
  version: 1.1.13
  resolution: "config-chain@npm:1.1.13"
  dependencies:
    ini: ^1.3.4
    proto-list: ~1.2.1
  checksum: 828137a28e7c2fc4b7fb229bd0cd6c1397bcf83434de54347e608154008f411749041ee392cbe42fab6307e02de4c12480260bf769b7d44b778fdea3839eafab
  languageName: node
  linkType: hard

"connect-history-api-fallback@npm:^1.6.0":
  version: 1.6.0
  resolution: "connect-history-api-fallback@npm:1.6.0"
  checksum: 804ca2be28c999032ecd37a9f71405e5d7b7a4b3defcebbe41077bb8c5a0a150d7b59f51dcc33b2de30bc7e217a31d10f8cfad27e8e74c2fc7655eeba82d6e7e
  languageName: node
  linkType: hard

"connect@npm:3.6.6":
  version: 3.6.6
  resolution: "connect@npm:3.6.6"
  dependencies:
    debug: 2.6.9
    finalhandler: 1.1.0
    parseurl: ~1.3.2
    utils-merge: 1.0.1
  checksum: b8038eee6d3febc7c36a1ef24879d9d7d8f596e0ec9b63189f955f615b40db1d83ae3812c6f122f21ad8ecbad1cee446b0a811457808f0cc136a1c80b8d0862f
  languageName: node
  linkType: hard

"console-browserify@npm:^1.1.0":
  version: 1.2.0
  resolution: "console-browserify@npm:1.2.0"
  checksum: 226591eeff8ed68e451dffb924c1fb750c654d54b9059b3b261d360f369d1f8f70650adecf2c7136656236a4bfeb55c39281b5d8a55d792ebbb99efd3d848d52
  languageName: node
  linkType: hard

"console-control-strings@npm:^1.0.0, console-control-strings@npm:^1.1.0":
  version: 1.1.0
  resolution: "console-control-strings@npm:1.1.0"
  checksum: 8755d76787f94e6cf79ce4666f0c5519906d7f5b02d4b884cf41e11dcd759ed69c57da0670afd9236d229a46e0f9cf519db0cd829c6dca820bb5a5c3def584ed
  languageName: node
  linkType: hard

"consolidate@npm:^0.15.1":
  version: 0.15.1
  resolution: "consolidate@npm:0.15.1"
  dependencies:
    bluebird: ^3.1.1
  checksum: 5a44ee975f8403dd3ff8ff3472fda7db0484b19f153eaac38e784465505a0741939c72d703befda7c75649739fc7a68f9659a86e2a62469336a8d531bd7a10df
  languageName: node
  linkType: hard

"constants-browserify@npm:^1.0.0":
  version: 1.0.0
  resolution: "constants-browserify@npm:1.0.0"
  checksum: f7ac8c6d0b6e4e0c77340a1d47a3574e25abd580bfd99ad707b26ff7618596cf1a5e5ce9caf44715e9e01d4a5d12cb3b4edaf1176f34c19adb2874815a56e64f
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: 5.2.1
  checksum: afb9d545e296a5171d7574fcad634b2fdf698875f4006a9dd04a3e1333880c5c0c98d47b560d01216fb6505a54a2ba6a843ee3a02ec86d7e911e8315255f56c3
  languageName: node
  linkType: hard

"content-type@npm:~1.0.4":
  version: 1.0.4
  resolution: "content-type@npm:1.0.4"
  checksum: 3d93585fda985d1554eca5ebd251994327608d2e200978fdbfba21c0c679914d5faf266d17027de44b34a72c7b0745b18584ecccaa7e1fdfb6a68ac7114f12e0
  languageName: node
  linkType: hard

"convert-source-map@npm:^1.1.0, convert-source-map@npm:^1.4.0, convert-source-map@npm:^1.5.1, convert-source-map@npm:^1.7.0":
  version: 1.8.0
  resolution: "convert-source-map@npm:1.8.0"
  dependencies:
    safe-buffer: ~5.1.1
  checksum: 985d974a2d33e1a2543ada51c93e1ba2f73eaed608dc39f229afc78f71dcc4c8b7d7c684aa647e3c6a3a204027444d69e53e169ce94e8d1fa8d7dee80c9c8fed
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: f4e1b0a98a27a0e6e66fd7ea4e4e9d8e038f624058371bf4499cfcd8f3980be9a121486995202ba3fca74fbed93a407d6d54d43a43f96fd28d0bd7a06761591a
  languageName: node
  linkType: hard

"cookie@npm:0.4.1":
  version: 0.4.1
  resolution: "cookie@npm:0.4.1"
  checksum: bd7c47f5d94ab70ccdfe8210cde7d725880d2fcda06d8e375afbdd82de0c8d3b73541996e9ce57d35f67f672c4ee6d60208adec06b3c5fc94cebb85196084cf8
  languageName: node
  linkType: hard

"copy-concurrently@npm:^1.0.0":
  version: 1.0.5
  resolution: "copy-concurrently@npm:1.0.5"
  dependencies:
    aproba: ^1.1.1
    fs-write-stream-atomic: ^1.0.8
    iferr: ^0.1.5
    mkdirp: ^0.5.1
    rimraf: ^2.5.4
    run-queue: ^1.0.0
  checksum: 63c169f582e09445260988f697b2d07793d439dfc31e97c8999707bd188dd94d1c7f2ca3533c7786fb75f03a3f2f54ad1ee08055f95f61bb8d2e862498c1d460
  languageName: node
  linkType: hard

"copy-descriptor@npm:^0.1.0":
  version: 0.1.1
  resolution: "copy-descriptor@npm:0.1.1"
  checksum: d4b7b57b14f1d256bb9aa0b479241048afd7f5bcf22035fc7b94e8af757adeae247ea23c1a774fe44869fd5694efba4a969b88d966766c5245fdee59837fe45b
  languageName: node
  linkType: hard

"copy-webpack-plugin@npm:^4.6.0":
  version: 4.6.0
  resolution: "copy-webpack-plugin@npm:4.6.0"
  dependencies:
    cacache: ^10.0.4
    find-cache-dir: ^1.0.0
    globby: ^7.1.1
    is-glob: ^4.0.0
    loader-utils: ^1.1.0
    minimatch: ^3.0.4
    p-limit: ^1.0.0
    serialize-javascript: ^1.4.0
  checksum: 0c745ca6cdbfa1458383a4db07a6a7002e6aff2183e9b45c99556075bec9ded1f54dae2e9a5e9017e4e805f38f61c6bc9dfce9af5548dea0a80d82bce039fdad
  languageName: node
  linkType: hard

"core-js-compat@npm:^3.21.0":
  version: 3.21.0
  resolution: "core-js-compat@npm:3.21.0"
  dependencies:
    browserslist: ^4.19.1
    semver: 7.0.0
  checksum: 7914d2f8a2f7c1b400e1c04c7560f4c96028bf23cec3cea6063ba594e38023cccbd38ad88af41c5d6b65450e97a989eb37598f609e3f7fbc6ebc1856d4195cbb
  languageName: node
  linkType: hard

"core-js@npm:^2.4.0, core-js@npm:^2.5.0, core-js@npm:^2.5.7, core-js@npm:^2.6.5":
  version: 2.6.12
  resolution: "core-js@npm:2.6.12"
  checksum: 44fa9934a85f8c78d61e0c8b7b22436330471ffe59ec5076fe7f324d6e8cf7f824b14b1c81ca73608b13bdb0fef035bd820989bf059767ad6fa13123bb8bd016
  languageName: node
  linkType: hard

"core-util-is@npm:1.0.2":
  version: 1.0.2
  resolution: "core-util-is@npm:1.0.2"
  checksum: 7a4c925b497a2c91421e25bf76d6d8190f0b2359a9200dbeed136e63b2931d6294d3b1893eda378883ed363cd950f44a12a401384c609839ea616befb7927dab
  languageName: node
  linkType: hard

"core-util-is@npm:~1.0.0":
  version: 1.0.3
  resolution: "core-util-is@npm:1.0.3"
  checksum: 9de8597363a8e9b9952491ebe18167e3b36e7707569eed0ebf14f8bba773611376466ae34575bca8cfe3c767890c859c74056084738f09d4e4a6f902b2ad7d99
  languageName: node
  linkType: hard

"cosmiconfig@npm:^5.0.0":
  version: 5.2.1
  resolution: "cosmiconfig@npm:5.2.1"
  dependencies:
    import-fresh: ^2.0.0
    is-directory: ^0.3.1
    js-yaml: ^3.13.1
    parse-json: ^4.0.0
  checksum: 8b6f1d3c8a5ffdf663a952f17af0761adf210b7a5933d0fe8988f3ca3a1f0e1e5cbbb74d5b419c15933dd2fdcaec31dbc5cc85cb8259a822342b93b529eff89c
  languageName: node
  linkType: hard

"create-ecdh@npm:^4.0.0":
  version: 4.0.4
  resolution: "create-ecdh@npm:4.0.4"
  dependencies:
    bn.js: ^4.1.0
    elliptic: ^6.5.3
  checksum: 0dd7fca9711d09e152375b79acf1e3f306d1a25ba87b8ff14c2fd8e68b83aafe0a7dd6c4e540c9ffbdd227a5fa1ad9b81eca1f233c38bb47770597ba247e614b
  languageName: node
  linkType: hard

"create-hash@npm:^1.1.0, create-hash@npm:^1.1.2, create-hash@npm:^1.2.0":
  version: 1.2.0
  resolution: "create-hash@npm:1.2.0"
  dependencies:
    cipher-base: ^1.0.1
    inherits: ^2.0.1
    md5.js: ^1.3.4
    ripemd160: ^2.0.1
    sha.js: ^2.4.0
  checksum: 02a6ae3bb9cd4afee3fabd846c1d8426a0e6b495560a977ba46120c473cb283be6aa1cace76b5f927cf4e499c6146fb798253e48e83d522feba807d6b722eaa9
  languageName: node
  linkType: hard

"create-hmac@npm:^1.1.0, create-hmac@npm:^1.1.4, create-hmac@npm:^1.1.7":
  version: 1.1.7
  resolution: "create-hmac@npm:1.1.7"
  dependencies:
    cipher-base: ^1.0.3
    create-hash: ^1.1.0
    inherits: ^2.0.1
    ripemd160: ^2.0.0
    safe-buffer: ^5.0.1
    sha.js: ^2.4.8
  checksum: ba12bb2257b585a0396108c72830e85f882ab659c3320c83584b1037f8ab72415095167ced80dc4ce8e446a8ecc4b2acf36d87befe0707d73b26cf9dc77440ed
  languageName: node
  linkType: hard

"cross-spawn@npm:^5.0.1, cross-spawn@npm:^5.1.0":
  version: 5.1.0
  resolution: "cross-spawn@npm:5.1.0"
  dependencies:
    lru-cache: ^4.0.1
    shebang-command: ^1.2.0
    which: ^1.2.9
  checksum: 726939c9954fc70c20e538923feaaa33bebc253247d13021737c3c7f68cdc3e0a57f720c0fe75057c0387995349f3f12e20e9bfdbf12274db28019c7ea4ec166
  languageName: node
  linkType: hard

"cross-spawn@npm:^6.0.0, cross-spawn@npm:^6.0.5":
  version: 6.0.5
  resolution: "cross-spawn@npm:6.0.5"
  dependencies:
    nice-try: ^1.0.4
    path-key: ^2.0.1
    semver: ^5.5.0
    shebang-command: ^1.2.0
    which: ^1.2.9
  checksum: f893bb0d96cd3d5751d04e67145bdddf25f99449531a72e82dcbbd42796bbc8268c1076c6b3ea51d4d455839902804b94bc45dfb37ecbb32ea8e54a6741c3ab9
  languageName: node
  linkType: hard

"crypto-browserify@npm:^3.11.0":
  version: 3.12.0
  resolution: "crypto-browserify@npm:3.12.0"
  dependencies:
    browserify-cipher: ^1.0.0
    browserify-sign: ^4.0.0
    create-ecdh: ^4.0.0
    create-hash: ^1.1.0
    create-hmac: ^1.1.0
    diffie-hellman: ^5.0.0
    inherits: ^2.0.1
    pbkdf2: ^3.0.3
    public-encrypt: ^4.0.0
    randombytes: ^2.0.0
    randomfill: ^1.0.3
  checksum: c1609af82605474262f3eaa07daa0b2140026bd264ab316d4bf1170272570dbe02f0c49e29407fe0d3634f96c507c27a19a6765fb856fed854a625f9d15618e2
  languageName: node
  linkType: hard

"css-color-names@npm:0.0.4, css-color-names@npm:^0.0.4":
  version: 0.0.4
  resolution: "css-color-names@npm:0.0.4"
  checksum: 9c6106320430a9da3a13daab8d8b4def39113edbfb68042444585d9a214af5fd5cb384b9be45124bc75f88261d461b517e00e278f4d2e0ab5a619b182f9f0e2d
  languageName: node
  linkType: hard

"css-declaration-sorter@npm:^4.0.1":
  version: 4.0.1
  resolution: "css-declaration-sorter@npm:4.0.1"
  dependencies:
    postcss: ^7.0.1
    timsort: ^0.3.0
  checksum: c38c00245c6706bd1127a6a2807bbdea3a2621c1f4e4bcb4710f6736c15c4ec414e02213adeab2171623351616090cb96374f683b90ec2aad18903066c4526d7
  languageName: node
  linkType: hard

"css-loader@npm:^1.0.1":
  version: 1.0.1
  resolution: "css-loader@npm:1.0.1"
  dependencies:
    babel-code-frame: ^6.26.0
    css-selector-tokenizer: ^0.7.0
    icss-utils: ^2.1.0
    loader-utils: ^1.0.2
    lodash: ^4.17.11
    postcss: ^6.0.23
    postcss-modules-extract-imports: ^1.2.0
    postcss-modules-local-by-default: ^1.2.0
    postcss-modules-scope: ^1.1.0
    postcss-modules-values: ^1.3.0
    postcss-value-parser: ^3.3.0
    source-list-map: ^2.0.0
  peerDependencies:
    webpack: ^4.0.0
  checksum: 548a4cdc427a104f269273343727a8eb88c4f0a16efc24222992839662bc1e36141bdf037c0a16c64ea59bee78bd6d24e8b44c0068b5abe63f8ab27cc747ac64
  languageName: node
  linkType: hard

"css-select-base-adapter@npm:^0.1.1":
  version: 0.1.1
  resolution: "css-select-base-adapter@npm:0.1.1"
  checksum: c107e9cfa53a23427e4537451a67358375e656baa3322345a982d3c2751fb3904002aae7e5d72386c59f766fe6b109d1ffb43eeab1c16f069f7a3828eb17851c
  languageName: node
  linkType: hard

"css-select@npm:^2.0.0":
  version: 2.1.0
  resolution: "css-select@npm:2.1.0"
  dependencies:
    boolbase: ^1.0.0
    css-what: ^3.2.1
    domutils: ^1.7.0
    nth-check: ^1.0.2
  checksum: 0c4099910f2411e2a9103cf92ea6a4ad738b57da75bcf73d39ef2c14a00ef36e5f16cb863211c901320618b24ace74da6333442d82995cafd5040077307de462
  languageName: node
  linkType: hard

"css-select@npm:^4.1.3":
  version: 4.2.1
  resolution: "css-select@npm:4.2.1"
  dependencies:
    boolbase: ^1.0.0
    css-what: ^5.1.0
    domhandler: ^4.3.0
    domutils: ^2.8.0
    nth-check: ^2.0.1
  checksum: 6617193ec7c332217204c4ea371d332c6845603fda415e36032e7e9e18206d7c368a14e3c57532082314d2689955b01122aa1097c1c52b6c1cab7ad90970d3c6
  languageName: node
  linkType: hard

"css-selector-tokenizer@npm:^0.7.0":
  version: 0.7.3
  resolution: "css-selector-tokenizer@npm:0.7.3"
  dependencies:
    cssesc: ^3.0.0
    fastparse: ^1.1.2
  checksum: 92560a9616a8bc073b88c678aa04f22c599ac23c5f8587e60f4861069e2d5aeb37b722af581ae3c5fbce453bed7a893d9c3e06830912e6d28badc3b8b99acd24
  languageName: node
  linkType: hard

"css-tree@npm:1.0.0-alpha.28":
  version: 1.0.0-alpha.28
  resolution: "css-tree@npm:1.0.0-alpha.28"
  dependencies:
    mdn-data: ~1.1.0
    source-map: ^0.5.3
  checksum: 4d5145270fb4b4da74dffafa87a6d6258e617fee8f5b5baf3df8f09a9b00280da5890777d724264d997fa6351233acd2eb3a0f9a23af8dd0884a829f51370ab8
  languageName: node
  linkType: hard

"css-tree@npm:1.0.0-alpha.29":
  version: 1.0.0-alpha.29
  resolution: "css-tree@npm:1.0.0-alpha.29"
  dependencies:
    mdn-data: ~1.1.0
    source-map: ^0.5.3
  checksum: 1693a0ddb85fe6f94c5d1b4c79a5dbc67d0c4a10e9992d9c6685bfc84b9d40380799e30b22bca42e15e60d927ac54ac500dec785e8c9245ee782c89eb4d924f4
  languageName: node
  linkType: hard

"css-tree@npm:1.0.0-alpha.37":
  version: 1.0.0-alpha.37
  resolution: "css-tree@npm:1.0.0-alpha.37"
  dependencies:
    mdn-data: 2.0.4
    source-map: ^0.6.1
  checksum: 0e419a1388ec0fbbe92885fba4a557f9fb0e077a2a1fad629b7245bbf7b4ef5df49e6877401b952b09b9057ffe1a3dba74f6fdfbf7b2223a5a35bce27ff2307d
  languageName: node
  linkType: hard

"css-tree@npm:^1.1.2":
  version: 1.1.3
  resolution: "css-tree@npm:1.1.3"
  dependencies:
    mdn-data: 2.0.14
    source-map: ^0.6.1
  checksum: 79f9b81803991b6977b7fcb1588799270438274d89066ce08f117f5cdb5e20019b446d766c61506dd772c839df84caa16042d6076f20c97187f5abe3b50e7d1f
  languageName: node
  linkType: hard

"css-url-regex@npm:^1.1.0":
  version: 1.1.0
  resolution: "css-url-regex@npm:1.1.0"
  checksum: d2398106514bbd1b2d3f28d6cbc06d441f32145a76bca9baed9fcc901fb106b8e9c85d4f5e834d1aa642c6541b2fa92c83a4d6013dbd093ed39a570c3b7541d3
  languageName: node
  linkType: hard

"css-what@npm:^3.2.1":
  version: 3.4.2
  resolution: "css-what@npm:3.4.2"
  checksum: 26bb5ec3ae718393d418016365c849fa14bd0de408c735dea3ddf58146b6cc54f3b336fb4afd31d95c06ca79583acbcdfec7ee93d31ff5c1a697df135b38dfeb
  languageName: node
  linkType: hard

"css-what@npm:^5.1.0":
  version: 5.1.0
  resolution: "css-what@npm:5.1.0"
  checksum: 0b75d1bac95c885c168573c85744a6c6843d8c33345f54f717218b37ea6296b0e99bb12105930ea170fd4a921990392a7c790c16c585c1d8960c49e2b7ec39f7
  languageName: node
  linkType: hard

"css@npm:^2.1.0":
  version: 2.2.4
  resolution: "css@npm:2.2.4"
  dependencies:
    inherits: ^2.0.3
    source-map: ^0.6.1
    source-map-resolve: ^0.5.2
    urix: ^0.1.0
  checksum: a35d483c5ccc04bcde3b1e7393d58ad3eee1dd6956df0f152de38e46a17c0ee193c30eec6b1e59831ad0e74599385732000e95987fcc9cb2b16c6d951bae49e1
  languageName: node
  linkType: hard

"cssesc@npm:^2.0.0":
  version: 2.0.0
  resolution: "cssesc@npm:2.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: 5e50886c2aca3f492fe808dbd146d30eb1c6f31fbe6093979a8376e39d171d989279199f6f3f1a42464109e082e0e42bc33eeff9467fb69bf346f5ba5853c3c6
  languageName: node
  linkType: hard

"cssesc@npm:^3.0.0":
  version: 3.0.0
  resolution: "cssesc@npm:3.0.0"
  bin:
    cssesc: bin/cssesc
  checksum: f8c4ababffbc5e2ddf2fa9957dda1ee4af6048e22aeda1869d0d00843223c1b13ad3f5d88b51caa46c994225eacb636b764eb807a8883e2fb6f99b4f4e8c48b2
  languageName: node
  linkType: hard

"cssnano-preset-default@npm:^4.0.0, cssnano-preset-default@npm:^4.0.8":
  version: 4.0.8
  resolution: "cssnano-preset-default@npm:4.0.8"
  dependencies:
    css-declaration-sorter: ^4.0.1
    cssnano-util-raw-cache: ^4.0.1
    postcss: ^7.0.0
    postcss-calc: ^7.0.1
    postcss-colormin: ^4.0.3
    postcss-convert-values: ^4.0.1
    postcss-discard-comments: ^4.0.2
    postcss-discard-duplicates: ^4.0.2
    postcss-discard-empty: ^4.0.1
    postcss-discard-overridden: ^4.0.1
    postcss-merge-longhand: ^4.0.11
    postcss-merge-rules: ^4.0.3
    postcss-minify-font-values: ^4.0.2
    postcss-minify-gradients: ^4.0.2
    postcss-minify-params: ^4.0.2
    postcss-minify-selectors: ^4.0.2
    postcss-normalize-charset: ^4.0.1
    postcss-normalize-display-values: ^4.0.2
    postcss-normalize-positions: ^4.0.2
    postcss-normalize-repeat-style: ^4.0.2
    postcss-normalize-string: ^4.0.2
    postcss-normalize-timing-functions: ^4.0.2
    postcss-normalize-unicode: ^4.0.1
    postcss-normalize-url: ^4.0.1
    postcss-normalize-whitespace: ^4.0.2
    postcss-ordered-values: ^4.1.2
    postcss-reduce-initial: ^4.0.3
    postcss-reduce-transforms: ^4.0.2
    postcss-svgo: ^4.0.3
    postcss-unique-selectors: ^4.0.1
  checksum: eb32c9fdd8bd4683e33d62284b6a9c4eb705b745235f4bb51a5571e1eb6738f636958fc9a6218fb51de43e0e2f74386a705b4c7ff2d1dcc611647953ba6ce159
  languageName: node
  linkType: hard

"cssnano-util-get-arguments@npm:^4.0.0":
  version: 4.0.0
  resolution: "cssnano-util-get-arguments@npm:4.0.0"
  checksum: 34222a1e848d573b74892eda7d7560c5422efa56f87d2b5242f9791593c6aa4ddc9d55e8e1708fb2f0d6f87c456314b78d93d3eec97d946ff756c63b09b72222
  languageName: node
  linkType: hard

"cssnano-util-get-match@npm:^4.0.0":
  version: 4.0.0
  resolution: "cssnano-util-get-match@npm:4.0.0"
  checksum: 56eacea0eb3d923359c9714ab25edde5eb4859e495954615d5529e81cdfabc2d41b57055c7f6a2f08e7d89df3a2794ef659306b539505d7f4e7202b897396fc2
  languageName: node
  linkType: hard

"cssnano-util-raw-cache@npm:^4.0.1":
  version: 4.0.1
  resolution: "cssnano-util-raw-cache@npm:4.0.1"
  dependencies:
    postcss: ^7.0.0
  checksum: 66a23e5e5255ff65d0f49f135d0ddfdb96433aeceb2708a31e4b4a652110755f103f6c91e0f439c8f3052818eb2b04ebf6334680a810296290e2c3467c14202b
  languageName: node
  linkType: hard

"cssnano-util-same-parent@npm:^4.0.0":
  version: 4.0.1
  resolution: "cssnano-util-same-parent@npm:4.0.1"
  checksum: 97c6b3f670ee9d1d6342b6a1daf9867d5c08644365dc146bd76defd356069112148e382ca86fc3e6c55adf0687974f03535bba34df95efb468b266d2319c7b66
  languageName: node
  linkType: hard

"cssnano@npm:^4.0.0, cssnano@npm:^4.1.10":
  version: 4.1.11
  resolution: "cssnano@npm:4.1.11"
  dependencies:
    cosmiconfig: ^5.0.0
    cssnano-preset-default: ^4.0.8
    is-resolvable: ^1.0.0
    postcss: ^7.0.0
  checksum: 2453fbe9f9f9e2ffe87dc5c718578f1b801fc7b82eaad12f5564c84bb0faf1774ea52e01874ecd29d1782aa7d0d84f0dbc95001eed9866ebd9bc523638999c9b
  languageName: node
  linkType: hard

"csso@npm:^3.5.1":
  version: 3.5.1
  resolution: "csso@npm:3.5.1"
  dependencies:
    css-tree: 1.0.0-alpha.29
  checksum: f5cca58d7b0a50cdab52c634d967f822c18aaa5f50dd1e145bb755f7ca4b32a029b72269a8a7e253e338e59833e6a934beca187172fb00efc6d096dba0d635b1
  languageName: node
  linkType: hard

"csso@npm:^4.0.2":
  version: 4.2.0
  resolution: "csso@npm:4.2.0"
  dependencies:
    css-tree: ^1.1.2
  checksum: 380ba9663da3bcea58dee358a0d8c4468bb6539be3c439dc266ac41c047217f52fd698fb7e4b6b6ccdfb8cf53ef4ceed8cc8ceccb8dfca2aa628319826b5b998
  languageName: node
  linkType: hard

"cssom@npm:0.3.x, cssom@npm:>= 0.3.2 < 0.4.0":
  version: 0.3.8
  resolution: "cssom@npm:0.3.8"
  checksum: 24beb3087c76c0d52dd458be9ee1fbc80ac771478a9baef35dd258cdeb527c68eb43204dd439692bb2b1ae5272fa5f2946d10946edab0d04f1078f85e06bc7f6
  languageName: node
  linkType: hard

"cssstyle@npm:^1.0.0":
  version: 1.4.0
  resolution: "cssstyle@npm:1.4.0"
  dependencies:
    cssom: 0.3.x
  checksum: 7efb9731d68dd042f32e0e3bbc7c1096653ba521f21ab1c5b158862321e4fcbfb51070641b834fadc8dd070a634dd43f328177e00d1b8481b5143a3e09f3d3f6
  languageName: node
  linkType: hard

"current-script-polyfill@npm:^1.0.0":
  version: 1.0.0
  resolution: "current-script-polyfill@npm:1.0.0"
  checksum: 811a3d4b15c1033024a8cb95ea0961d01006287fe69b1c82ab031d9de44a0682badc892881bd2523d9f54852b5440e2b2c8fea449ce9756a26bf4f072673c1e3
  languageName: node
  linkType: hard

"cyclist@npm:^1.0.1":
  version: 1.0.1
  resolution: "cyclist@npm:1.0.1"
  checksum: 3cc2fdeb358599ca0ea96f5ecf2fc530ccab7ed1f8aa1a894aebfacd2009281bd7380cb9b30db02a18cdd00b3ed1d7ce81a3b11fe56e33a6a0fe4424dc592fbe
  languageName: node
  linkType: hard

"dashdash@npm:^1.12.0":
  version: 1.14.1
  resolution: "dashdash@npm:1.14.1"
  dependencies:
    assert-plus: ^1.0.0
  checksum: 3634c249570f7f34e3d34f866c93f866c5b417f0dd616275decae08147dcdf8fccfaa5947380ccfb0473998ea3a8057c0b4cd90c875740ee685d0624b2983598
  languageName: node
  linkType: hard

"data-urls@npm:^1.0.0":
  version: 1.1.0
  resolution: "data-urls@npm:1.1.0"
  dependencies:
    abab: ^2.0.0
    whatwg-mimetype: ^2.2.0
    whatwg-url: ^7.0.0
  checksum: dc4bd9621df0dff336d7c4c0517c792488ef3cf11cd37e72ab80f3a7f0a0aa14bad677ac97cf22c87c6eb9518e58b98590e1c8c756b56240940f0e470c81612e
  languageName: node
  linkType: hard

"dayjs@npm:^1.8.17":
  version: 1.10.7
  resolution: "dayjs@npm:1.10.7"
  checksum: a0a4ca95abaa03d0702161dc2c35d16121188e342f5052b9c61cdf784dab68af766f477c04f87f71c6af666fd4d13db9b9853b87265850d6093b7b04e1bb1cd7
  languageName: node
  linkType: hard

"de-indent@npm:^1.0.2":
  version: 1.0.2
  resolution: "de-indent@npm:1.0.2"
  checksum: 8deacc0f4a397a4414a0fc4d0034d2b7782e7cb4eaf34943ea47754e08eccf309a0e71fa6f56cc48de429ede999a42d6b4bca761bf91683be0095422dbf24611
  languageName: node
  linkType: hard

"deasync@npm:^0.1.15":
  version: 0.1.24
  resolution: "deasync@npm:0.1.24"
  dependencies:
    bindings: ^1.5.0
    node-addon-api: ^1.7.1
  checksum: 7e87a1faa8511fb157620f9470160efd352cb7fda2c76e45e58acf40fdc96fde89f846f253ee714ec338c45b07b465196776a89947f29a261cbf28a5b3662f73
  languageName: node
  linkType: hard

"debug@npm:2.6.9, debug@npm:^2.2.0, debug@npm:^2.3.3, debug@npm:^2.6.8, debug@npm:^2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: 2.0.0
  checksum: d2f51589ca66df60bf36e1fa6e4386b318c3f1e06772280eea5b1ae9fd3d05e9c2b7fd8a7d862457d00853c75b00451aa2d7459b924629ee385287a650f58fe6
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4.0.1, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1":
  version: 4.3.3
  resolution: "debug@npm:4.3.3"
  dependencies:
    ms: 2.1.2
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 14472d56fe4a94dbcfaa6dbed2dd3849f1d72ba78104a1a328047bb564643ca49df0224c3a17fa63533fd11dd3d4c8636cd861191232a2c6735af00cc2d4de16
  languageName: node
  linkType: hard

"debug@npm:=3.1.0":
  version: 3.1.0
  resolution: "debug@npm:3.1.0"
  dependencies:
    ms: 2.0.0
  checksum: 0b52718ab957254a5b3ca07fc34543bc778f358620c206a08452251eb7fc193c3ea3505072acbf4350219c14e2d71ceb7bdaa0d3370aa630b50da790458d08b3
  languageName: node
  linkType: hard

"debug@npm:^3.1.0, debug@npm:^3.1.1, debug@npm:^3.2.6":
  version: 3.2.7
  resolution: "debug@npm:3.2.7"
  dependencies:
    ms: ^2.1.1
  checksum: b3d8c5940799914d30314b7c3304a43305fd0715581a919dacb8b3176d024a782062368405b47491516d2091d6462d4d11f2f4974a405048094f8bfebfa3071c
  languageName: node
  linkType: hard

"decamelize@npm:^1.1.1, decamelize@npm:^1.2.0":
  version: 1.2.0
  resolution: "decamelize@npm:1.2.0"
  checksum: ad8c51a7e7e0720c70ec2eeb1163b66da03e7616d7b98c9ef43cce2416395e84c1e9548dd94f5f6ffecfee9f8b94251fc57121a8b021f2ff2469b2bae247b8aa
  languageName: node
  linkType: hard

"decode-uri-component@npm:^0.2.0":
  version: 0.2.0
  resolution: "decode-uri-component@npm:0.2.0"
  checksum: f3749344ab9305ffcfe4bfe300e2dbb61fc6359e2b736812100a3b1b6db0a5668cba31a05e4b45d4d63dbf1a18dfa354cd3ca5bb3ededddabb8cd293f4404f94
  languageName: node
  linkType: hard

"deep-equal@npm:^1.0.1":
  version: 1.1.1
  resolution: "deep-equal@npm:1.1.1"
  dependencies:
    is-arguments: ^1.0.4
    is-date-object: ^1.0.1
    is-regex: ^1.0.4
    object-is: ^1.0.1
    object-keys: ^1.1.1
    regexp.prototype.flags: ^1.2.0
  checksum: f92686f2c5bcdf714a75a5fa7a9e47cb374a8ec9307e717b8d1ce61f56a75aaebf5619c2a12b8087a705b5a2f60d0292c35f8b58cb1f72e3268a3a15cab9f78d
  languageName: node
  linkType: hard

"deep-is@npm:~0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: edb65dd0d7d1b9c40b2f50219aef30e116cedd6fc79290e740972c132c09106d2e80aa0bc8826673dd5a00222d4179c84b36a790eef63a4c4bca75a37ef90804
  languageName: node
  linkType: hard

"deepmerge@npm:1.3.2":
  version: 1.3.2
  resolution: "deepmerge@npm:1.3.2"
  checksum: cdc9a7ac3ae8383d2974f85587b113cd16bed0b39b88b0bb091dd2ab247bed85288b290dbb9573d1a887819da415b4f7092e9dc2e8af73d743dc8f1a4e90fdd9
  languageName: node
  linkType: hard

"deepmerge@npm:^1.2.0, deepmerge@npm:^1.5.2":
  version: 1.5.2
  resolution: "deepmerge@npm:1.5.2"
  checksum: 5ecfe328e0105f2c554b90af555cbba052ab4468f1893e3b26800cb8869d3c1a1c590a5bbe1fdf481a8cc89b1bc47b5ac73a7153d5a0e4b702ea6eca081038a8
  languageName: node
  linkType: hard

"default-gateway@npm:^4.2.0":
  version: 4.2.0
  resolution: "default-gateway@npm:4.2.0"
  dependencies:
    execa: ^1.0.0
    ip-regex: ^2.1.0
  checksum: 1f5be765471689c6bab33e0c8b87363c3e2485cc1ab78904d383a8a8293a79f684da2a3303744b112503f986af4ea87d917c63a468ed913e9b0c31588c02d6a4
  languageName: node
  linkType: hard

"default-require-extensions@npm:^1.0.0":
  version: 1.0.0
  resolution: "default-require-extensions@npm:1.0.0"
  dependencies:
    strip-bom: ^2.0.0
  checksum: 8be10a3e1f997c8a579c3f00fdd8117c30fa3a12d2ac544dc1ee93fd6138c77fba69fe69546c76d0299d7f74c26416301b9a1dc775557e99991a6ebe2f850df4
  languageName: node
  linkType: hard

"defaults@npm:^1.0.3":
  version: 1.0.3
  resolution: "defaults@npm:1.0.3"
  dependencies:
    clone: ^1.0.2
  checksum: 96e2112da6553d376afd5265ea7cbdb2a3b45535965d71ab8bb1da10c8126d168fdd5268799625324b368356d21ba2a7b3d4ec50961f11a47b7feb9de3d4413e
  languageName: node
  linkType: hard

"define-properties@npm:^1.1.2, define-properties@npm:^1.1.3":
  version: 1.1.3
  resolution: "define-properties@npm:1.1.3"
  dependencies:
    object-keys: ^1.0.12
  checksum: da80dba55d0cd76a5a7ab71ef6ea0ebcb7b941f803793e4e0257b384cb772038faa0c31659d244e82c4342edef841c1a1212580006a05a5068ee48223d787317
  languageName: node
  linkType: hard

"define-property@npm:^0.2.5":
  version: 0.2.5
  resolution: "define-property@npm:0.2.5"
  dependencies:
    is-descriptor: ^0.1.0
  checksum: 85af107072b04973b13f9e4128ab74ddfda48ec7ad2e54b193c0ffb57067c4ce5b7786a7b4ae1f24bd03e87c5d18766b094571810b314d7540f86d4354dbd394
  languageName: node
  linkType: hard

"define-property@npm:^1.0.0":
  version: 1.0.0
  resolution: "define-property@npm:1.0.0"
  dependencies:
    is-descriptor: ^1.0.0
  checksum: 5fbed11dace44dd22914035ba9ae83ad06008532ca814d7936a53a09e897838acdad5b108dd0688cc8d2a7cf0681acbe00ee4136cf36743f680d10517379350a
  languageName: node
  linkType: hard

"define-property@npm:^2.0.2":
  version: 2.0.2
  resolution: "define-property@npm:2.0.2"
  dependencies:
    is-descriptor: ^1.0.2
    isobject: ^3.0.1
  checksum: 3217ed53fc9eed06ba8da6f4d33e28c68a82e2f2a8ab4d562c4920d8169a166fe7271453675e6c69301466f36a65d7f47edf0cf7f474b9aa52a5ead9c1b13c99
  languageName: node
  linkType: hard

"del@npm:^4.1.1":
  version: 4.1.1
  resolution: "del@npm:4.1.1"
  dependencies:
    "@types/glob": ^7.1.1
    globby: ^6.1.0
    is-path-cwd: ^2.0.0
    is-path-in-cwd: ^2.0.0
    p-map: ^2.0.0
    pify: ^4.0.1
    rimraf: ^2.6.3
  checksum: 521f7da44bd79da841c06d573923d1f64f423aee8b8219c973478d3150ce1dcc024d03ad605929292adbff56d6448bca60d96dcdd2d8a53b46dbcb27e265c94b
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 46fe6e83e2cb1d85ba50bd52803c68be9bd953282fa7096f51fc29edd5d67ff84ff753c51966061e5ba7cb5e47ef6d36a91924eddb7f3f3483b1c560f77a0020
  languageName: node
  linkType: hard

"delegate@npm:^3.1.2":
  version: 3.2.0
  resolution: "delegate@npm:3.2.0"
  checksum: d943058fe05897228b158cbd1bab05164df28c8f54127873231d6b03b0a5acc1b3ee1f98ac70ccc9b79cd84aa47118a7de111fee2923753491583905069da27d
  languageName: node
  linkType: hard

"delegates@npm:^1.0.0":
  version: 1.0.0
  resolution: "delegates@npm:1.0.0"
  checksum: a51744d9b53c164ba9c0492471a1a2ffa0b6727451bdc89e31627fdf4adda9d51277cfcbfb20f0a6f08ccb3c436f341df3e92631a3440226d93a8971724771fd
  languageName: node
  linkType: hard

"depd@npm:^1.1.2, depd@npm:~1.1.2":
  version: 1.1.2
  resolution: "depd@npm:1.1.2"
  checksum: 6b406620d269619852885ce15965272b829df6f409724415e0002c8632ab6a8c0a08ec1f0bd2add05dc7bd7507606f7e2cc034fa24224ab829580040b835ecd9
  languageName: node
  linkType: hard

"des.js@npm:^1.0.0":
  version: 1.0.1
  resolution: "des.js@npm:1.0.1"
  dependencies:
    inherits: ^2.0.1
    minimalistic-assert: ^1.0.0
  checksum: 1ec2eedd7ed6bd61dd5e0519fd4c96124e93bb22de8a9d211b02d63e5dd152824853d919bb2090f965cc0e3eb9c515950a9836b332020d810f9c71feb0fd7df4
  languageName: node
  linkType: hard

"destroy@npm:~1.0.4":
  version: 1.0.4
  resolution: "destroy@npm:1.0.4"
  checksum: da9ab4961dc61677c709da0c25ef01733042614453924d65636a7db37308fef8a24cd1e07172e61173d471ca175371295fbc984b0af5b2b4ff47cd57bd784c03
  languageName: node
  linkType: hard

"detect-indent@npm:^4.0.0":
  version: 4.0.0
  resolution: "detect-indent@npm:4.0.0"
  dependencies:
    repeating: ^2.0.0
  checksum: 328f273915c1610899bc7d4784ce874413d0a698346364cd3ee5d79afba1c5cf4dbc97b85a801e20f4d903c0598bd5096af32b800dfb8696b81464ccb3dfda2c
  languageName: node
  linkType: hard

"detect-newline@npm:^2.1.0":
  version: 2.1.0
  resolution: "detect-newline@npm:2.1.0"
  checksum: c55146fd5b97a9ce914f17f85a01466c9e8679289e2d390588b027a58f2e090dbc38457923072369c603b8904f982f87b78fee17e48d5706f35571642f4599f8
  languageName: node
  linkType: hard

"detect-node@npm:^2.0.4":
  version: 2.1.0
  resolution: "detect-node@npm:2.1.0"
  checksum: 832184ec458353e41533ac9c622f16c19f7c02d8b10c303dfd3a756f56be93e903616c0bb2d4226183c9351c15fc0b3dba41a17a2308262afabcfa3776e6ae6e
  languageName: node
  linkType: hard

"diff@npm:^3.2.0":
  version: 3.5.0
  resolution: "diff@npm:3.5.0"
  checksum: 00842950a6551e26ce495bdbce11047e31667deea546527902661f25cc2e73358967ebc78cf86b1a9736ec3e14286433225f9970678155753a6291c3bca5227b
  languageName: node
  linkType: hard

"diffie-hellman@npm:^5.0.0":
  version: 5.0.3
  resolution: "diffie-hellman@npm:5.0.3"
  dependencies:
    bn.js: ^4.1.0
    miller-rabin: ^4.0.0
    randombytes: ^2.0.0
  checksum: 0e620f322170c41076e70181dd1c24e23b08b47dbb92a22a644f3b89b6d3834b0f8ee19e37916164e5eb1ee26d2aa836d6129f92723995267250a0b541811065
  languageName: node
  linkType: hard

"dir-glob@npm:^2.0.0, dir-glob@npm:^2.2.2":
  version: 2.2.2
  resolution: "dir-glob@npm:2.2.2"
  dependencies:
    path-type: ^3.0.0
  checksum: 3aa48714a9f7845ffc30ab03a5c674fe760477cc55e67b0847333371549227d93953e6627ec160f75140c5bea5c5f88d13c01de79bd1997a588efbcf06980842
  languageName: node
  linkType: hard

"dns-equal@npm:^1.0.0":
  version: 1.0.0
  resolution: "dns-equal@npm:1.0.0"
  checksum: a8471ac849c7c13824f053babea1bc26e2f359394dd5a460f8340d8abd13434be01e3327a5c59d212f8c8997817450efd3f3ac77bec709b21979cf0235644524
  languageName: node
  linkType: hard

"dns-packet@npm:^1.3.1":
  version: 1.3.4
  resolution: "dns-packet@npm:1.3.4"
  dependencies:
    ip: ^1.1.0
    safe-buffer: ^5.0.1
  checksum: 7dd87f85cb4f9d1a99c03470730e3d9385e67dc94f6c13868c4034424a5378631e492f9f1fbc43d3c42f319fbbfe18b6488bb9527c32d34692c52bf1f5eedf69
  languageName: node
  linkType: hard

"dns-txt@npm:^2.0.2":
  version: 2.0.2
  resolution: "dns-txt@npm:2.0.2"
  dependencies:
    buffer-indexof: ^1.0.0
  checksum: 80130b665379ecd991687ae079fbee25d091e03e4c4cef41e7643b977849ac48c2f56bfcb3727e53594d29029b833749811110d9f3fbee1b26a6e6f8096a5cef
  languageName: node
  linkType: hard

"doctrine@npm:^2.1.0":
  version: 2.1.0
  resolution: "doctrine@npm:2.1.0"
  dependencies:
    esutils: ^2.0.2
  checksum: a45e277f7feaed309fe658ace1ff286c6e2002ac515af0aaf37145b8baa96e49899638c7cd47dccf84c3d32abfc113246625b3ac8f552d1046072adee13b0dc8
  languageName: node
  linkType: hard

"doctrine@npm:^3.0.0":
  version: 3.0.0
  resolution: "doctrine@npm:3.0.0"
  dependencies:
    esutils: ^2.0.2
  checksum: fd7673ca77fe26cd5cba38d816bc72d641f500f1f9b25b83e8ce28827fe2da7ad583a8da26ab6af85f834138cf8dae9f69b0cd6ab925f52ddab1754db44d99ce
  languageName: node
  linkType: hard

"dom-converter@npm:^0.2.0":
  version: 0.2.0
  resolution: "dom-converter@npm:0.2.0"
  dependencies:
    utila: ~0.4
  checksum: ea52fe303f5392e48dea563abef0e6fb3a478b8dbe3c599e99bb5d53981c6c38fc4944e56bb92a8ead6bb989d10b7914722ae11febbd2fd0910e33b9fc4aaa77
  languageName: node
  linkType: hard

"dom-event-types@npm:^1.0.0":
  version: 1.1.0
  resolution: "dom-event-types@npm:1.1.0"
  checksum: 58bb4d4b3a0caf719ce42ab8445576b6309d43a3db3ca45ca3dabaa4d69d99472e75b89127c25a177fe8fd7b390530fc43cb1252d57ab25d7e4845ed647cb159
  languageName: node
  linkType: hard

"dom-serializer@npm:0":
  version: 0.2.2
  resolution: "dom-serializer@npm:0.2.2"
  dependencies:
    domelementtype: ^2.0.1
    entities: ^2.0.0
  checksum: 376344893e4feccab649a14ca1a46473e9961f40fe62479ea692d4fee4d9df1c00ca8654811a79c1ca7b020096987e1ca4fb4d7f8bae32c1db800a680a0e5d5e
  languageName: node
  linkType: hard

"dom-serializer@npm:^1.0.1":
  version: 1.3.2
  resolution: "dom-serializer@npm:1.3.2"
  dependencies:
    domelementtype: ^2.0.1
    domhandler: ^4.2.0
    entities: ^2.0.0
  checksum: bff48714944d67b160db71ba244fb0f3fe72e77ef2ec8414e2eeb56f2d926e404a13456b8b83a5392e217ba47dec2ec0c368801b31481813e94d185276c3e964
  languageName: node
  linkType: hard

"domain-browser@npm:^1.1.1":
  version: 1.2.0
  resolution: "domain-browser@npm:1.2.0"
  checksum: 8f1235c7f49326fb762f4675795246a6295e7dd566b4697abec24afdba2460daa7dfbd1a73d31efbf5606b3b7deadb06ce47cf06f0a476e706153d62a4ff2b90
  languageName: node
  linkType: hard

"domelementtype@npm:1, domelementtype@npm:^1.3.1":
  version: 1.3.1
  resolution: "domelementtype@npm:1.3.1"
  checksum: 7893da40218ae2106ec6ffc146b17f203487a52f5228b032ea7aa470e41dfe03e1bd762d0ee0139e792195efda765434b04b43cddcf63207b098f6ae44b36ad6
  languageName: node
  linkType: hard

"domelementtype@npm:^2.0.1, domelementtype@npm:^2.2.0":
  version: 2.2.0
  resolution: "domelementtype@npm:2.2.0"
  checksum: 24cb386198640cd58aa36f8c987f2ea61859929106d06ffcc8f547e70cb2ed82a6dc56dcb8252b21fba1f1ea07df6e4356d60bfe57f77114ca1aed6828362629
  languageName: node
  linkType: hard

"domexception@npm:^1.0.1":
  version: 1.0.1
  resolution: "domexception@npm:1.0.1"
  dependencies:
    webidl-conversions: ^4.0.2
  checksum: f564a9c0915dcb83ceefea49df14aaed106b1468fbe505119e8bcb0b77e242534f3aba861978537c0fc9dc6f35b176d0ffc77b3e342820fb27a8f215e7ae4d52
  languageName: node
  linkType: hard

"domhandler@npm:^2.3.0":
  version: 2.4.2
  resolution: "domhandler@npm:2.4.2"
  dependencies:
    domelementtype: 1
  checksum: 49bd70c9c784f845cd047e1dfb3611bd10891c05719acfc93f01fc726a419ed09fbe0b69f9064392d556a63fffc5a02010856cedae9368f4817146d95a97011f
  languageName: node
  linkType: hard

"domhandler@npm:^4.0.0, domhandler@npm:^4.2.0, domhandler@npm:^4.3.0":
  version: 4.3.0
  resolution: "domhandler@npm:4.3.0"
  dependencies:
    domelementtype: ^2.2.0
  checksum: d2a2dbf40dd99abf936b65ad83c6b530afdb3605a87cad37a11b5d9220e68423ebef1b86c89e0f6d93ffaf315cc327cf1a988652e7a9a95cce539e3984f4c64d
  languageName: node
  linkType: hard

"domready@npm:1.0.8":
  version: 1.0.8
  resolution: "domready@npm:1.0.8"
  checksum: 5b0ee86650226e09f65c89b61d15fcfcda5925931dfffab5e9b82bf8ba96637286605fde1ac4addee19717bf7e486d4b49683e6f99038927560412113d96fde0
  languageName: node
  linkType: hard

"domutils@npm:^1.5.1, domutils@npm:^1.7.0":
  version: 1.7.0
  resolution: "domutils@npm:1.7.0"
  dependencies:
    dom-serializer: 0
    domelementtype: 1
  checksum: f60a725b1f73c1ae82f4894b691601ecc6ecb68320d87923ac3633137627c7865725af813ae5d188ad3954283853bcf46779eb50304ec5d5354044569fcefd2b
  languageName: node
  linkType: hard

"domutils@npm:^2.5.2, domutils@npm:^2.8.0":
  version: 2.8.0
  resolution: "domutils@npm:2.8.0"
  dependencies:
    dom-serializer: ^1.0.1
    domelementtype: ^2.2.0
    domhandler: ^4.2.0
  checksum: abf7434315283e9aadc2a24bac0e00eab07ae4313b40cc239f89d84d7315ebdfd2fb1b5bf750a96bc1b4403d7237c7b2ebf60459be394d625ead4ca89b934391
  languageName: node
  linkType: hard

"dot-prop@npm:^5.2.0":
  version: 5.3.0
  resolution: "dot-prop@npm:5.3.0"
  dependencies:
    is-obj: ^2.0.0
  checksum: d5775790093c234ef4bfd5fbe40884ff7e6c87573e5339432870616331189f7f5d86575c5b5af2dcf0f61172990f4f734d07844b1f23482fff09e3c4bead05ea
  languageName: node
  linkType: hard

"dotenv-expand@npm:^5.1.0":
  version: 5.1.0
  resolution: "dotenv-expand@npm:5.1.0"
  checksum: 8017675b7f254384915d55f9eb6388e577cf0a1231a28d54b0ca03b782be9501b0ac90ac57338636d395fa59051e6209e9b44b8ddf169ce6076dffb5dea227d3
  languageName: node
  linkType: hard

"dotenv@npm:^7.0.0":
  version: 7.0.0
  resolution: "dotenv@npm:7.0.0"
  checksum: 18a7b3ef0e90fd6fcce7c7cbdd48d923b0cb180807540b80c797bda4a098097e17820d6315ae28eec22f73954cd0ab9d81904d46370183817c09f694d40566ff
  languageName: node
  linkType: hard

"duplexer@npm:^0.1.1":
  version: 0.1.2
  resolution: "duplexer@npm:0.1.2"
  checksum: 62ba61a830c56801db28ff6305c7d289b6dc9f859054e8c982abd8ee0b0a14d2e9a8e7d086ffee12e868d43e2bbe8a964be55ddbd8c8957714c87373c7a4f9b0
  languageName: node
  linkType: hard

"duplexify@npm:^3.4.2, duplexify@npm:^3.6.0":
  version: 3.7.1
  resolution: "duplexify@npm:3.7.1"
  dependencies:
    end-of-stream: ^1.0.0
    inherits: ^2.0.1
    readable-stream: ^2.0.0
    stream-shift: ^1.0.0
  checksum: 3c2ed2223d956a5da713dae12ba8295acb61d9acd966ccbba938090d04f4574ca4dca75cca089b5077c2d7e66101f32e6ea9b36a78ca213eff574e7a8b8accf2
  languageName: node
  linkType: hard

"easy-stack@npm:^1.0.1":
  version: 1.0.1
  resolution: "easy-stack@npm:1.0.1"
  checksum: 161a99e497b3857b0be4ec9e1ebbe90b241ea9d84702f9881b8e5b3f6822065b8c4e33436996935103e191bffba3607de70712a792f4d406a050def48c6bc381
  languageName: node
  linkType: hard

"ecc-jsbn@npm:~0.1.1":
  version: 0.1.2
  resolution: "ecc-jsbn@npm:0.1.2"
  dependencies:
    jsbn: ~0.1.0
    safer-buffer: ^2.1.0
  checksum: 22fef4b6203e5f31d425f5b711eb389e4c6c2723402e389af394f8411b76a488fa414d309d866e2b577ce3e8462d344205545c88a8143cc21752a5172818888a
  languageName: node
  linkType: hard

"editorconfig@npm:^0.15.3":
  version: 0.15.3
  resolution: "editorconfig@npm:0.15.3"
  dependencies:
    commander: ^2.19.0
    lru-cache: ^4.1.5
    semver: ^5.6.0
    sigmund: ^1.0.1
  bin:
    editorconfig: bin/editorconfig
  checksum: a94afeda19f12a4bcc4a573f0858df13dd3a2d1a3268cc0f17a6326ebe7ddd6cb0c026f8e4e73c17d34f3892bf6f8b561512d9841e70063f61da71b4c57dc5f0
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 1b4cac778d64ce3b582a7e26b218afe07e207a0f9bfe13cc7395a6d307849cfe361e65033c3251e00c27dd060cab43014c2d6b2647676135e18b77d2d05b3f4f
  languageName: node
  linkType: hard

"ejs@npm:^2.6.1":
  version: 2.7.4
  resolution: "ejs@npm:2.7.4"
  checksum: a1d2bfc7d1f0b39e99ae19b20c9469a25aeddba1ffc225db098110b18d566f73772fcdcc740b108cfda7452276f67d7b64eb359f90285414c942f4ae70713371
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.4.17":
  version: 1.4.67
  resolution: "electron-to-chromium@npm:1.4.67"
  checksum: 687fa3b403504d5ecabac2a0ab59754d59187b8cfca2d5cf34c83eef0791a5d60a4378df84b811d81804c22e8c9dad02c89d9bc03b28fb547cb2bb52e1f7eeb9
  languageName: node
  linkType: hard

"element-ui@npm:^2.13.2":
  version: 2.15.7
  resolution: "element-ui@npm:2.15.7"
  dependencies:
    async-validator: ~1.8.1
    babel-helper-vue-jsx-merge-props: ^2.0.0
    deepmerge: ^1.2.0
    normalize-wheel: ^1.0.1
    resize-observer-polyfill: ^1.5.0
    throttle-debounce: ^1.0.1
  peerDependencies:
    vue: ^2.5.17
  checksum: 968ccda4e91f6147e6d4598126aae0d1c36c868a67525adb2fc1062f953a6a7d78e337ffcd8d38ddad898263a57d3e16d1d0d2f684a96afa75af847fd2e0d22f
  languageName: node
  linkType: hard

"elliptic@npm:^6.5.3":
  version: 6.5.4
  resolution: "elliptic@npm:6.5.4"
  dependencies:
    bn.js: ^4.11.9
    brorand: ^1.1.0
    hash.js: ^1.0.0
    hmac-drbg: ^1.0.1
    inherits: ^2.0.4
    minimalistic-assert: ^1.0.1
    minimalistic-crypto-utils: ^1.0.1
  checksum: d56d21fd04e97869f7ffcc92e18903b9f67f2d4637a23c860492fbbff5a3155fd9ca0184ce0c865dd6eb2487d234ce9551335c021c376cd2d3b7cb749c7d10f4
  languageName: node
  linkType: hard

"emoji-regex@npm:^7.0.1":
  version: 7.0.3
  resolution: "emoji-regex@npm:7.0.3"
  checksum: 9159b2228b1511f2870ac5920f394c7e041715429a68459ebe531601555f11ea782a8e1718f969df2711d38c66268174407cbca57ce36485544f695c2dfdc96e
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: d4c5c39d5a9868b5fa152f00cada8a936868fd3367f33f71be515ecee4c803132d11b31a6222b2571b1e5f7e13890156a94880345594d0ce7e3c9895f560f192
  languageName: node
  linkType: hard

"emojis-list@npm:^2.0.0":
  version: 2.1.0
  resolution: "emojis-list@npm:2.1.0"
  checksum: fb61fa6356dfcc9fbe6db8e334c29da365a34d3d82a915cb59621883d3023d804fd5edad5acd42b8eec016936e81d3b38e2faf921b32e073758374253afe1272
  languageName: node
  linkType: hard

"emojis-list@npm:^3.0.0":
  version: 3.0.0
  resolution: "emojis-list@npm:3.0.0"
  checksum: ddaaa02542e1e9436c03970eeed445f4ed29a5337dfba0fe0c38dfdd2af5da2429c2a0821304e8a8d1cadf27fdd5b22ff793571fa803ae16852a6975c65e8e70
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.1, encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: e50e3d508cdd9c4565ba72d2012e65038e5d71bdc9198cb125beb6237b5b1ade6c0d343998da9e170fb2eae52c1bed37d4d6d98a46ea423a0cddbed5ac3f780c
  languageName: node
  linkType: hard

"encoding@npm:^0.1.12":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: ^0.6.2
  checksum: bb98632f8ffa823996e508ce6a58ffcf5856330fde839ae42c9e1f436cc3b5cc651d4aeae72222916545428e54fd0f6aa8862fd8d25bdbcc4589f1e3f3715e7f
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.0.0, end-of-stream@npm:^1.1.0":
  version: 1.4.4
  resolution: "end-of-stream@npm:1.4.4"
  dependencies:
    once: ^1.4.0
  checksum: 530a5a5a1e517e962854a31693dbb5c0b2fc40b46dad2a56a2deec656ca040631124f4795823acc68238147805f8b021abbe221f4afed5ef3c8e8efc2024908b
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^4.1.0, enhanced-resolve@npm:^4.5.0":
  version: 4.5.0
  resolution: "enhanced-resolve@npm:4.5.0"
  dependencies:
    graceful-fs: ^4.1.2
    memory-fs: ^0.5.0
    tapable: ^1.0.0
  checksum: 4d87488584c4d67d356ef4ba04978af4b2d4d18190cb859efac8e8475a34d5d6c069df33faa5a0a22920b0586dbf330f6a08d52bb15a8771a9ce4d70a2da74ba
  languageName: node
  linkType: hard

"entities@npm:^1.1.1":
  version: 1.1.2
  resolution: "entities@npm:1.1.2"
  checksum: d537b02799bdd4784ffd714d000597ed168727bddf4885da887c5a491d735739029a00794f1998abbf35f3f6aeda32ef5c15010dca1817d401903a501b6d3e05
  languageName: node
  linkType: hard

"entities@npm:^2.0.0":
  version: 2.2.0
  resolution: "entities@npm:2.2.0"
  checksum: 19010dacaf0912c895ea262b4f6128574f9ccf8d4b3b65c7e8334ad0079b3706376360e28d8843ff50a78aabcb8f08f0a32dbfacdc77e47ed77ca08b713669b3
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 65b5df55a8bab92229ab2b40dad3b387fad24613263d103a97f91c9fe43ceb21965cd3392b1ccb5d77088021e525c4e0481adb309625d0cb94ade1d1fb8dc17e
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 8b7b1be20d2de12d2255c0bc2ca638b7af5171142693299416e6a9339bd7d88fc8d7707d913d78e0993176005405a236b066b45666b27b797252c771156ace54
  languageName: node
  linkType: hard

"errno@npm:^0.1.3, errno@npm:~0.1.7":
  version: 0.1.8
  resolution: "errno@npm:0.1.8"
  dependencies:
    prr: ~1.0.1
  bin:
    errno: cli.js
  checksum: 1271f7b9fbb3bcbec76ffde932485d1e3561856d21d847ec613a9722ee924cdd4e523a62dc71a44174d91e898fe21fdc8d5b50823f4b5e0ce8c35c8271e6ef4a
  languageName: node
  linkType: hard

"error-ex@npm:^1.2.0, error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: ^0.2.1
  checksum: c1c2b8b65f9c91b0f9d75f0debaa7ec5b35c266c2cac5de412c1a6de86d4cbae04ae44e510378cb14d032d0645a36925d0186f8bb7367bcc629db256b743a001
  languageName: node
  linkType: hard

"error-stack-parser@npm:^2.0.6":
  version: 2.0.6
  resolution: "error-stack-parser@npm:2.0.6"
  dependencies:
    stackframe: ^1.1.1
  checksum: bd8e048fcb1c0c74ab201271fec3b39c097a7c24bdef1718828d053c0584da5d7ad845253b5e4773803ee8e7450b23b0920e60a3b60dd403c1568c843058cb12
  languageName: node
  linkType: hard

"es-abstract@npm:^1.17.2, es-abstract@npm:^1.19.1":
  version: 1.19.1
  resolution: "es-abstract@npm:1.19.1"
  dependencies:
    call-bind: ^1.0.2
    es-to-primitive: ^1.2.1
    function-bind: ^1.1.1
    get-intrinsic: ^1.1.1
    get-symbol-description: ^1.0.0
    has: ^1.0.3
    has-symbols: ^1.0.2
    internal-slot: ^1.0.3
    is-callable: ^1.2.4
    is-negative-zero: ^2.0.1
    is-regex: ^1.1.4
    is-shared-array-buffer: ^1.0.1
    is-string: ^1.0.7
    is-weakref: ^1.0.1
    object-inspect: ^1.11.0
    object-keys: ^1.1.1
    object.assign: ^4.1.2
    string.prototype.trimend: ^1.0.4
    string.prototype.trimstart: ^1.0.4
    unbox-primitive: ^1.0.1
  checksum: b6be8410672c5364db3fb01eb786e30c7b4bb32b4af63d381c08840f4382c4a168e7855cd338bf59d4f1a1a1138f4d748d1fd40ec65aaa071876f9e9fbfed949
  languageName: node
  linkType: hard

"es-to-primitive@npm:^1.2.1":
  version: 1.2.1
  resolution: "es-to-primitive@npm:1.2.1"
  dependencies:
    is-callable: ^1.1.4
    is-date-object: ^1.0.1
    is-symbol: ^1.0.2
  checksum: 4ead6671a2c1402619bdd77f3503991232ca15e17e46222b0a41a5d81aebc8740a77822f5b3c965008e631153e9ef0580540007744521e72de8e33599fca2eed
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.1.1
  resolution: "escalade@npm:3.1.1"
  checksum: a3e2a99f07acb74b3ad4989c48ca0c3140f69f923e56d0cba0526240ee470b91010f9d39001f2a4a313841d237ede70a729e92125191ba5d21e74b106800b133
  languageName: node
  linkType: hard

"escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 6213ca9ae00d0ab8bccb6d8d4e0a98e76237b2410302cf7df70aaa6591d509a2a37ce8998008cbecae8fc8ffaadf3fb0229535e6a145f3ce0b211d060decbb24
  languageName: node
  linkType: hard

"escape-string-regexp@npm:1.0.5, escape-string-regexp@npm:^1.0.2, escape-string-regexp@npm:^1.0.5":
  version: 1.0.5
  resolution: "escape-string-regexp@npm:1.0.5"
  checksum: 6092fda75c63b110c706b6a9bfde8a612ad595b628f0bd2147eea1d3406723020810e591effc7db1da91d80a71a737a313567c5abb3813e8d9c71f4aa595b410
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^2.0.0":
  version: 2.0.0
  resolution: "escape-string-regexp@npm:2.0.0"
  checksum: 9f8a2d5743677c16e85c810e3024d54f0c8dea6424fad3c79ef6666e81dd0846f7437f5e729dfcdac8981bc9e5294c39b4580814d114076b8d36318f46ae4395
  languageName: node
  linkType: hard

"escodegen@npm:^1.9.1":
  version: 1.14.3
  resolution: "escodegen@npm:1.14.3"
  dependencies:
    esprima: ^4.0.1
    estraverse: ^4.2.0
    esutils: ^2.0.2
    optionator: ^0.8.1
    source-map: ~0.6.1
  dependenciesMeta:
    source-map:
      optional: true
  bin:
    escodegen: bin/escodegen.js
    esgenerate: bin/esgenerate.js
  checksum: 381cdc4767ecdb221206bbbab021b467bbc2a6f5c9a99c9e6353040080bdd3dfe73d7604ad89a47aca6ea7d58bc635f6bd3fbc8da9a1998e9ddfa8372362ccd0
  languageName: node
  linkType: hard

"eslint-loader@npm:^2.1.2":
  version: 2.2.1
  resolution: "eslint-loader@npm:2.2.1"
  dependencies:
    loader-fs-cache: ^1.0.0
    loader-utils: ^1.0.2
    object-assign: ^4.0.1
    object-hash: ^1.1.4
    rimraf: ^2.6.1
  peerDependencies:
    eslint: ">=1.6.0 <7.0.0"
    webpack: ">=2.0.0 <5.0.0"
  checksum: 8de51b7656ddb5e8b1fa1acd3d2025eddccf958344fde0217706cb32697de55c8e23f04bd4844aee81bc453b6dc04823d72d4ed9c89e19b9d92d4fa21bb55fb3
  languageName: node
  linkType: hard

"eslint-plugin-vue@npm:5.2.2":
  version: 5.2.2
  resolution: "eslint-plugin-vue@npm:5.2.2"
  dependencies:
    vue-eslint-parser: ^5.0.0
  peerDependencies:
    eslint: ^5.0.0
  checksum: b0b79e125d37911607f44d2e0912d7041d35463d034f1f1a9bb2f3787428ffbb44f37bfb578a875a341809f3c5ed9fe8bb21aeb92cdae56bf56b75d111fddfbe
  languageName: node
  linkType: hard

"eslint-plugin-vue@npm:^4.7.1":
  version: 4.7.1
  resolution: "eslint-plugin-vue@npm:4.7.1"
  dependencies:
    vue-eslint-parser: ^2.0.3
  peerDependencies:
    eslint: ^3.18.0 || ^4.0.0
  checksum: a74af4692fd00ac5a1925200ffaa24cea6cde363f546a3f9f3ae6142763a8d594a0b3fe6bada79d3f93364de835cb36f98b8ac64fabe4cca714c3d42a716a344
  languageName: node
  linkType: hard

"eslint-scope@npm:3.7.1":
  version: 3.7.1
  resolution: "eslint-scope@npm:3.7.1"
  dependencies:
    esrecurse: ^4.1.0
    estraverse: ^4.1.1
  checksum: dc10d4d0cba3652a9df2505fb4398c3a8ecc09e4911b2136a9360e7a06352514776aae975c98e940ec1d24c4c8402375addc04ba4f83add06e937afb43c7cd20
  languageName: node
  linkType: hard

"eslint-scope@npm:^3.7.1":
  version: 3.7.3
  resolution: "eslint-scope@npm:3.7.3"
  dependencies:
    esrecurse: ^4.1.0
    estraverse: ^4.1.1
  checksum: 9b9b81fc1056f02db2ad0130dc494d54a2bf6417e2777e38c7aca8a659261870ad1686c98adf2d5501558cf3fb5c6f977a7893027c99307b5c44e9b3fe53c3a5
  languageName: node
  linkType: hard

"eslint-scope@npm:^4.0.0, eslint-scope@npm:^4.0.3":
  version: 4.0.3
  resolution: "eslint-scope@npm:4.0.3"
  dependencies:
    esrecurse: ^4.1.0
    estraverse: ^4.1.1
  checksum: c5f835f681884469991fe58d76a554688d9c9e50811299ccd4a8f79993a039f5bcb0ee6e8de2b0017d97c794b5832ef3b21c9aac66228e3aa0f7a0485bcfb65b
  languageName: node
  linkType: hard

"eslint-utils@npm:^1.3.1":
  version: 1.4.3
  resolution: "eslint-utils@npm:1.4.3"
  dependencies:
    eslint-visitor-keys: ^1.1.0
  checksum: a20630e686034107138272f245c460f6d77705d1f4bb0628c1a1faf59fc800f441188916b3ec3b957394dc405aa200a3017dfa2b0fff0976e307a4e645a18d1e
  languageName: node
  linkType: hard

"eslint-visitor-keys@npm:^1.0.0, eslint-visitor-keys@npm:^1.1.0":
  version: 1.3.0
  resolution: "eslint-visitor-keys@npm:1.3.0"
  checksum: 37a19b712f42f4c9027e8ba98c2b06031c17e0c0a4c696cd429bd9ee04eb43889c446f2cd545e1ff51bef9593fcec94ecd2c2ef89129fcbbf3adadbef520376a
  languageName: node
  linkType: hard

"eslint@npm:5.15.3":
  version: 5.15.3
  resolution: "eslint@npm:5.15.3"
  dependencies:
    "@babel/code-frame": ^7.0.0
    ajv: ^6.9.1
    chalk: ^2.1.0
    cross-spawn: ^6.0.5
    debug: ^4.0.1
    doctrine: ^3.0.0
    eslint-scope: ^4.0.3
    eslint-utils: ^1.3.1
    eslint-visitor-keys: ^1.0.0
    espree: ^5.0.1
    esquery: ^1.0.1
    esutils: ^2.0.2
    file-entry-cache: ^5.0.1
    functional-red-black-tree: ^1.0.1
    glob: ^7.1.2
    globals: ^11.7.0
    ignore: ^4.0.6
    import-fresh: ^3.0.0
    imurmurhash: ^0.1.4
    inquirer: ^6.2.2
    js-yaml: ^3.12.0
    json-stable-stringify-without-jsonify: ^1.0.1
    levn: ^0.3.0
    lodash: ^4.17.11
    minimatch: ^3.0.4
    mkdirp: ^0.5.1
    natural-compare: ^1.4.0
    optionator: ^0.8.2
    path-is-inside: ^1.0.2
    progress: ^2.0.0
    regexpp: ^2.0.1
    semver: ^5.5.1
    strip-ansi: ^4.0.0
    strip-json-comments: ^2.0.1
    table: ^5.2.3
    text-table: ^0.2.0
  bin:
    eslint: ./bin/eslint.js
  checksum: 1c663a90ec567e06ec8b8e8bd092e2787b5b0c476a453be68cd657ad26fea4807440733ce820317f349a9b9d4dcb38b5e2aa697bbdce9e739a707910fdc1dbe0
  languageName: node
  linkType: hard

"eslint@npm:^4.19.1":
  version: 4.19.1
  resolution: "eslint@npm:4.19.1"
  dependencies:
    ajv: ^5.3.0
    babel-code-frame: ^6.22.0
    chalk: ^2.1.0
    concat-stream: ^1.6.0
    cross-spawn: ^5.1.0
    debug: ^3.1.0
    doctrine: ^2.1.0
    eslint-scope: ^3.7.1
    eslint-visitor-keys: ^1.0.0
    espree: ^3.5.4
    esquery: ^1.0.0
    esutils: ^2.0.2
    file-entry-cache: ^2.0.0
    functional-red-black-tree: ^1.0.1
    glob: ^7.1.2
    globals: ^11.0.1
    ignore: ^3.3.3
    imurmurhash: ^0.1.4
    inquirer: ^3.0.6
    is-resolvable: ^1.0.0
    js-yaml: ^3.9.1
    json-stable-stringify-without-jsonify: ^1.0.1
    levn: ^0.3.0
    lodash: ^4.17.4
    minimatch: ^3.0.2
    mkdirp: ^0.5.1
    natural-compare: ^1.4.0
    optionator: ^0.8.2
    path-is-inside: ^1.0.2
    pluralize: ^7.0.0
    progress: ^2.0.0
    regexpp: ^1.0.1
    require-uncached: ^1.0.3
    semver: ^5.3.0
    strip-ansi: ^4.0.0
    strip-json-comments: ~2.0.1
    table: 4.0.2
    text-table: ~0.2.0
  bin:
    eslint: ./bin/eslint.js
  checksum: f0c617ecc255bcf5bda30c248e9fe54a113d7396292a5bd5ab1e81e53ee87331646ed1991d5589efa5878f0d534ab04ba267a25c7f86c49f72bd1256ef6ad09d
  languageName: node
  linkType: hard

"espree@npm:^3.5.2, espree@npm:^3.5.4":
  version: 3.5.4
  resolution: "espree@npm:3.5.4"
  dependencies:
    acorn: ^5.5.0
    acorn-jsx: ^3.0.0
  checksum: cbc8da4cafcc45f802fc561adece3a74d86f7bfbc2434bca34fadcf9dabb3337c25d2d202e4e1a4ff73b0105e887945fb75c375081067fc23ecaae86f5cb6400
  languageName: node
  linkType: hard

"espree@npm:^4.1.0":
  version: 4.1.0
  resolution: "espree@npm:4.1.0"
  dependencies:
    acorn: ^6.0.2
    acorn-jsx: ^5.0.0
    eslint-visitor-keys: ^1.0.0
  checksum: 6f4da3a283c55811b3e84e63a883c0741e834f571da11bc0363b3df699836b105d9892a91b181bfd778aee97781eebf80be948fd61d4542b7c02d729cddc92bb
  languageName: node
  linkType: hard

"espree@npm:^5.0.1":
  version: 5.0.1
  resolution: "espree@npm:5.0.1"
  dependencies:
    acorn: ^6.0.7
    acorn-jsx: ^5.0.0
    eslint-visitor-keys: ^1.0.0
  checksum: a091aac2bddf872484b0a7e779e3a1ffab32d1c55a6c4f99e483613a0149443531272c191eda1c7c827e32a9e10f6ce7ea6b131c7b3f4e12471fe618ebbc5b7e
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0, esprima@npm:^4.0.1":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: b45bc805a613dbea2835278c306b91aff6173c8d034223fa81498c77dcbce3b2931bf6006db816f62eacd9fd4ea975dfd85a5b7f3c6402cfd050d4ca3c13a628
  languageName: node
  linkType: hard

"esquery@npm:^1.0.0, esquery@npm:^1.0.1":
  version: 1.4.0
  resolution: "esquery@npm:1.4.0"
  dependencies:
    estraverse: ^5.1.0
  checksum: a0807e17abd7fbe5fbd4fab673038d6d8a50675cdae6b04fbaa520c34581be0c5fa24582990e8acd8854f671dd291c78bb2efb9e0ed5b62f33bac4f9cf820210
  languageName: node
  linkType: hard

"esrecurse@npm:^4.1.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1, estraverse@npm:^4.2.0":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: a6299491f9940bb246124a8d44b7b7a413a8336f5436f9837aaa9330209bd9ee8af7e91a654a3545aee9c54b3308e78ee360cef1d777d37cfef77d2fa33b5827
  languageName: node
  linkType: hard

"estraverse@npm:^5.1.0, estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"esutils@npm:^2.0.2":
  version: 2.0.3
  resolution: "esutils@npm:2.0.3"
  checksum: 22b5b08f74737379a840b8ed2036a5fb35826c709ab000683b092d9054e5c2a82c27818f12604bfc2a9a76b90b6834ef081edbc1c7ae30d1627012e067c6ec87
  languageName: node
  linkType: hard

"etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 571aeb3dbe0f2bbd4e4fadbdb44f325fc75335cd5f6f6b6a091e6a06a9f25ed5392f0863c5442acb0646787446e816f13cbfc6edce5b07658541dff573cab1ff
  languageName: node
  linkType: hard

"event-pubsub@npm:4.3.0":
  version: 4.3.0
  resolution: "event-pubsub@npm:4.3.0"
  checksum: 6940f57790c01a967b7c637f1c9fd000ee968a1d5894186ffb3356ffbe174c70e22e62adbbcfcee3f305482d99b6abe7613c1c27c909b07adc9127dc16c8cf73
  languageName: node
  linkType: hard

"eventemitter3@npm:^4.0.0":
  version: 4.0.7
  resolution: "eventemitter3@npm:4.0.7"
  checksum: 1875311c42fcfe9c707b2712c32664a245629b42bb0a5a84439762dd0fd637fc54d078155ea83c2af9e0323c9ac13687e03cfba79b03af9f40c89b4960099374
  languageName: node
  linkType: hard

"events@npm:^3.0.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: f6f487ad2198aa41d878fa31452f1a3c00958f46e9019286ff4787c84aac329332ab45c9cdc8c445928fc6d7ded294b9e005a7fce9426488518017831b272780
  languageName: node
  linkType: hard

"eventsource@npm:^1.0.7":
  version: 1.1.0
  resolution: "eventsource@npm:1.1.0"
  dependencies:
    original: ^1.0.0
  checksum: 78338b7e75ec471cb793efb3319e0c4d2bf00fb638a2e3f888ad6d98cd1e3d4492a29f554c0921c7b2ac5130c3a732a1a0056739f6e2f548d714aec685e5da7e
  languageName: node
  linkType: hard

"evp_bytestokey@npm:^1.0.0, evp_bytestokey@npm:^1.0.3":
  version: 1.0.3
  resolution: "evp_bytestokey@npm:1.0.3"
  dependencies:
    md5.js: ^1.3.4
    node-gyp: latest
    safe-buffer: ^5.1.1
  checksum: ad4e1577f1a6b721c7800dcc7c733fe01f6c310732bb5bf2240245c2a5b45a38518b91d8be2c610611623160b9d1c0e91f1ce96d639f8b53e8894625cf20fa45
  languageName: node
  linkType: hard

"exec-sh@npm:^0.2.0":
  version: 0.2.2
  resolution: "exec-sh@npm:0.2.2"
  dependencies:
    merge: ^1.2.0
  checksum: 3ec5f99c8f7c4bebfeed1a797818f8754de00415d60a99f327e7f970834ca85252c449bf4b2efbff4c11e76eccc6b976a1956a5394a612d8e3d412185f59b0c0
  languageName: node
  linkType: hard

"execa@npm:^0.8.0":
  version: 0.8.0
  resolution: "execa@npm:0.8.0"
  dependencies:
    cross-spawn: ^5.0.1
    get-stream: ^3.0.0
    is-stream: ^1.1.0
    npm-run-path: ^2.0.0
    p-finally: ^1.0.0
    signal-exit: ^3.0.0
    strip-eof: ^1.0.0
  checksum: c2a4bf6e051737e46bee61a93ec286cb71a05f16650a1918c8d6262ba9f0bac031472252411baa8c78b7f432f10cb4c601349403774d69be2ebd864e9b1eca60
  languageName: node
  linkType: hard

"execa@npm:^1.0.0":
  version: 1.0.0
  resolution: "execa@npm:1.0.0"
  dependencies:
    cross-spawn: ^6.0.0
    get-stream: ^4.0.0
    is-stream: ^1.1.0
    npm-run-path: ^2.0.0
    p-finally: ^1.0.0
    signal-exit: ^3.0.0
    strip-eof: ^1.0.0
  checksum: ddf1342c1c7d02dd93b41364cd847640f6163350d9439071abf70bf4ceb1b9b2b2e37f54babb1d8dc1df8e0d8def32d0e81e74a2e62c3e1d70c303eb4c306bc4
  languageName: node
  linkType: hard

"exit@npm:^0.1.2":
  version: 0.1.2
  resolution: "exit@npm:0.1.2"
  checksum: abc407f07a875c3961e4781dfcb743b58d6c93de9ab263f4f8c9d23bb6da5f9b7764fc773f86b43dd88030444d5ab8abcb611cb680fba8ca075362b77114bba3
  languageName: node
  linkType: hard

"expand-brackets@npm:^0.1.4":
  version: 0.1.5
  resolution: "expand-brackets@npm:0.1.5"
  dependencies:
    is-posix-bracket: ^0.1.0
  checksum: 71b2971027eb026f055a1c310d24d18a266427b84fc18cadddcedb4de2e07aaef6084e252406b20e58f7aa7613f6bfbe6136962955562529a66675bf49bb10d7
  languageName: node
  linkType: hard

"expand-brackets@npm:^2.1.4":
  version: 2.1.4
  resolution: "expand-brackets@npm:2.1.4"
  dependencies:
    debug: ^2.3.3
    define-property: ^0.2.5
    extend-shallow: ^2.0.1
    posix-character-classes: ^0.1.0
    regex-not: ^1.0.0
    snapdragon: ^0.8.1
    to-regex: ^3.0.1
  checksum: 1781d422e7edfa20009e2abda673cadb040a6037f0bd30fcd7357304f4f0c284afd420d7622722ca4a016f39b6d091841ab57b401c1f7e2e5131ac65b9f14fa1
  languageName: node
  linkType: hard

"expand-range@npm:^1.8.1":
  version: 1.8.2
  resolution: "expand-range@npm:1.8.2"
  dependencies:
    fill-range: ^2.1.0
  checksum: ca773ec06838d7d53cfd835b7d58c9c662a3773e5d57647ca6f83e50218efd93e29b5ee6cc1ea9c5651794e9005562cad28c4911ea06aac27323a05f3c6b787d
  languageName: node
  linkType: hard

"expect@npm:^23.6.0":
  version: 23.6.0
  resolution: "expect@npm:23.6.0"
  dependencies:
    ansi-styles: ^3.2.0
    jest-diff: ^23.6.0
    jest-get-type: ^22.1.0
    jest-matcher-utils: ^23.6.0
    jest-message-util: ^23.4.0
    jest-regex-util: ^23.3.0
  checksum: 0b1a828052fd7877c58a684a102507785a4e086445411c1366dcd0c900c3c44e8643c930510089b45a0a9ff5474cf835945501d2a471bdeae602dfd063fbaaaa
  languageName: node
  linkType: hard

"express@npm:^4.16.3, express@npm:^4.17.1":
  version: 4.17.2
  resolution: "express@npm:4.17.2"
  dependencies:
    accepts: ~1.3.7
    array-flatten: 1.1.1
    body-parser: 1.19.1
    content-disposition: 0.5.4
    content-type: ~1.0.4
    cookie: 0.4.1
    cookie-signature: 1.0.6
    debug: 2.6.9
    depd: ~1.1.2
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    etag: ~1.8.1
    finalhandler: ~1.1.2
    fresh: 0.5.2
    merge-descriptors: 1.0.1
    methods: ~1.1.2
    on-finished: ~2.3.0
    parseurl: ~1.3.3
    path-to-regexp: 0.1.7
    proxy-addr: ~2.0.7
    qs: 6.9.6
    range-parser: ~1.2.1
    safe-buffer: 5.2.1
    send: 0.17.2
    serve-static: 1.14.2
    setprototypeof: 1.2.0
    statuses: ~1.5.0
    type-is: ~1.6.18
    utils-merge: 1.0.1
    vary: ~1.1.2
  checksum: 1535d56d20e65a1a39b5f056c025dd635290a744478ac69cc47633aeb4b2ce51458f8eb4080cfb7ba47c853ba5cfd794d404cff822a25127f1556b726ec3914a
  languageName: node
  linkType: hard

"extend-shallow@npm:^2.0.1":
  version: 2.0.1
  resolution: "extend-shallow@npm:2.0.1"
  dependencies:
    is-extendable: ^0.1.0
  checksum: 8fb58d9d7a511f4baf78d383e637bd7d2e80843bd9cd0853649108ea835208fb614da502a553acc30208e1325240bb7cc4a68473021612496bb89725483656d8
  languageName: node
  linkType: hard

"extend-shallow@npm:^3.0.0, extend-shallow@npm:^3.0.2":
  version: 3.0.2
  resolution: "extend-shallow@npm:3.0.2"
  dependencies:
    assign-symbols: ^1.0.0
    is-extendable: ^1.0.1
  checksum: a920b0cd5838a9995ace31dfd11ab5e79bf6e295aa566910ce53dff19f4b1c0fda2ef21f26b28586c7a2450ca2b42d97bd8c0f5cec9351a819222bf861e02461
  languageName: node
  linkType: hard

"extend@npm:~3.0.2":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: a50a8309ca65ea5d426382ff09f33586527882cf532931cb08ca786ea3146c0553310bda688710ff61d7668eba9f96b923fe1420cdf56a2c3eaf30fcab87b515
  languageName: node
  linkType: hard

"external-editor@npm:^2.0.4":
  version: 2.2.0
  resolution: "external-editor@npm:2.2.0"
  dependencies:
    chardet: ^0.4.0
    iconv-lite: ^0.4.17
    tmp: ^0.0.33
  checksum: 5e164e033ed93fcbfe311b5760b98e292685ea58e6e62737365de2d625d0043d60b36c5b537795c496b520db568d9e5f5109994e869ca0d9b6b443aacf533efe
  languageName: node
  linkType: hard

"external-editor@npm:^3.0.3":
  version: 3.1.0
  resolution: "external-editor@npm:3.1.0"
  dependencies:
    chardet: ^0.7.0
    iconv-lite: ^0.4.24
    tmp: ^0.0.33
  checksum: 1c2a616a73f1b3435ce04030261bed0e22d4737e14b090bb48e58865da92529c9f2b05b893de650738d55e692d071819b45e1669259b2b354bc3154d27a698c7
  languageName: node
  linkType: hard

"extglob@npm:^0.3.1":
  version: 0.3.2
  resolution: "extglob@npm:0.3.2"
  dependencies:
    is-extglob: ^1.0.0
  checksum: c1c8d5365fe4992fc5b007140cbb37292ffadcd767cb602606de4d572ff96f38620e42855f8cb75020c050aacf1eeb51212dd6312de46eab42e2200277b5fc45
  languageName: node
  linkType: hard

"extglob@npm:^2.0.2, extglob@npm:^2.0.4":
  version: 2.0.4
  resolution: "extglob@npm:2.0.4"
  dependencies:
    array-unique: ^0.3.2
    define-property: ^1.0.0
    expand-brackets: ^2.1.4
    extend-shallow: ^2.0.1
    fragment-cache: ^0.2.1
    regex-not: ^1.0.0
    snapdragon: ^0.8.1
    to-regex: ^3.0.1
  checksum: a41531b8934735b684cef5e8c5a01d0f298d7d384500ceca38793a9ce098125aab04ee73e2d75d5b2901bc5dddd2b64e1b5e3bf19139ea48bac52af4a92f1d00
  languageName: node
  linkType: hard

"extract-from-css@npm:^0.4.4":
  version: 0.4.4
  resolution: "extract-from-css@npm:0.4.4"
  dependencies:
    css: ^2.1.0
  checksum: f3628d14a736a363188881dc9b5c603f42d6a7bbbf1338ca5eb8362bff6a392edb07ab8a57e3cee1fd8191cd9795c671b6369523d69231c59395a94a62436d84
  languageName: node
  linkType: hard

"extsprintf@npm:1.3.0":
  version: 1.3.0
  resolution: "extsprintf@npm:1.3.0"
  checksum: cee7a4a1e34cffeeec18559109de92c27517e5641991ec6bab849aa64e3081022903dd53084f2080d0d2530803aa5ee84f1e9de642c365452f9e67be8f958ce2
  languageName: node
  linkType: hard

"extsprintf@npm:^1.2.0":
  version: 1.4.1
  resolution: "extsprintf@npm:1.4.1"
  checksum: a2f29b241914a8d2bad64363de684821b6b1609d06ae68d5b539e4de6b28659715b5bea94a7265201603713b7027d35399d10b0548f09071c5513e65e8323d33
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^1.0.0":
  version: 1.1.0
  resolution: "fast-deep-equal@npm:1.1.0"
  checksum: 69b4c9534d9805f13a341aa72f69641d0b9ae3cc8beb25c64e68a257241c7bb34370266db27ae4fc3c4da0518448c01a5f587a096a211471c86a38facd9a1486
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-glob@npm:^2.2.6":
  version: 2.2.7
  resolution: "fast-glob@npm:2.2.7"
  dependencies:
    "@mrmlnc/readdir-enhanced": ^2.2.1
    "@nodelib/fs.stat": ^1.1.2
    glob-parent: ^3.1.0
    is-glob: ^4.0.0
    merge2: ^1.2.3
    micromatch: ^3.1.10
  checksum: 304ccff1d437fcc44ae0168b0c3899054b92e0fd6af6ad7c3ccc82ab4ddd210b99c7c739d60ee3686da2aa165cd1a31810b31fd91f7c2a575d297342a9fc0534
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fast-levenshtein@npm:~2.0.6":
  version: 2.0.6
  resolution: "fast-levenshtein@npm:2.0.6"
  checksum: 92cfec0a8dfafd9c7a15fba8f2cc29cd0b62b85f056d99ce448bbcd9f708e18ab2764bda4dd5158364f4145a7c72788538994f0d1787b956ef0d1062b0f7c24c
  languageName: node
  linkType: hard

"fastparse@npm:^1.1.2":
  version: 1.1.2
  resolution: "fastparse@npm:1.1.2"
  checksum: c4d199809dc4e8acafeb786be49481cc9144de296e2d54df4540ccfd868d0df73afc649aba70a748925eb32bbc4208b723d6288adf92382275031a8c7e10c0aa
  languageName: node
  linkType: hard

"faye-websocket@npm:^0.11.3":
  version: 0.11.4
  resolution: "faye-websocket@npm:0.11.4"
  dependencies:
    websocket-driver: ">=0.5.1"
  checksum: d49a62caf027f871149fc2b3f3c7104dc6d62744277eb6f9f36e2d5714e847d846b9f7f0d0b7169b25a012e24a594cde11a93034b30732e4c683f20b8a5019fa
  languageName: node
  linkType: hard

"fb-watchman@npm:^2.0.0":
  version: 2.0.1
  resolution: "fb-watchman@npm:2.0.1"
  dependencies:
    bser: 2.1.1
  checksum: 8510230778ab3a51c27dffb1b76ef2c24fab672a42742d3c0a45c2e9d1e5f20210b1fbca33486088da4a9a3958bde96b5aec0a63aac9894b4e9df65c88b2cbd6
  languageName: node
  linkType: hard

"figgy-pudding@npm:^3.5.1":
  version: 3.5.2
  resolution: "figgy-pudding@npm:3.5.2"
  checksum: 4090bd66193693dcda605e44d6b8715d8fb5c92a67acd57826e55cf816a342f550d57e5638f822b39366e1b2fdb244e99b3068a37213aa1d6c1bf602b8fde5ae
  languageName: node
  linkType: hard

"figures@npm:^2.0.0":
  version: 2.0.0
  resolution: "figures@npm:2.0.0"
  dependencies:
    escape-string-regexp: ^1.0.5
  checksum: 081beb16ea57d1716f8447c694f637668322398b57017b20929376aaf5def9823b35245b734cdd87e4832dc96e9c6f46274833cada77bfe15e5f980fea1fd21f
  languageName: node
  linkType: hard

"file-entry-cache@npm:^2.0.0":
  version: 2.0.0
  resolution: "file-entry-cache@npm:2.0.0"
  dependencies:
    flat-cache: ^1.2.1
    object-assign: ^4.0.1
  checksum: e22ca2b848709b76ab80d3d4fb5908669a63f35f54d4129e9952959ab58217dabd6d34e5f385c2360c6bd6f561bf52fe4ad80aa3d27160a6454296d6f1e327bf
  languageName: node
  linkType: hard

"file-entry-cache@npm:^5.0.1":
  version: 5.0.1
  resolution: "file-entry-cache@npm:5.0.1"
  dependencies:
    flat-cache: ^2.0.1
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"file-loader@npm:^3.0.1":
  version: 3.0.1
  resolution: "file-loader@npm:3.0.1"
  dependencies:
    loader-utils: ^1.0.2
    schema-utils: ^1.0.0
  peerDependencies:
    webpack: ^4.0.0
  checksum: dbd818445d36453f2e79892b67fbb67fbe81aaa84c305eabffa86c82b2dfec26a6ec10068b0ed29f3fed789b0cf1574a9a86440a6a6adab8b40ec69698f22519
  languageName: node
  linkType: hard

"file-uri-to-path@npm:1.0.0":
  version: 1.0.0
  resolution: "file-uri-to-path@npm:1.0.0"
  checksum: b648580bdd893a008c92c7ecc96c3ee57a5e7b6c4c18a9a09b44fb5d36d79146f8e442578bc0e173dc027adf3987e254ba1dfd6e3ec998b7c282873010502144
  languageName: node
  linkType: hard

"filename-regex@npm:^2.0.0":
  version: 2.0.1
  resolution: "filename-regex@npm:2.0.1"
  checksum: ac181f8184cf49d2f08889a446995537c3967e6f6195752d903b69c7d85f134425fb1446c4e384e77390bac488b60a9658805debc5a91e8c0bc9f81340505371
  languageName: node
  linkType: hard

"fileset@npm:^2.0.2":
  version: 2.0.3
  resolution: "fileset@npm:2.0.3"
  dependencies:
    glob: ^7.0.3
    minimatch: ^3.0.3
  checksum: b083d3bcc0edd76ae7d413b8f2bd5a5205d9c54f92726e7d416d8b55bebdf03f25006e0f5fd130f5843e42cef7c7e0a3baed874b69e8a98671f1eef8a9e7e907
  languageName: node
  linkType: hard

"filesize@npm:^3.6.1":
  version: 3.6.1
  resolution: "filesize@npm:3.6.1"
  checksum: 9ba47e9df90cd6bb6c0434418123facf9dafbe92c850f29ed50bfa42d60d00f8501a8a9b962f77ec7d1ba30190d5dbda5f6f56c5e56bce9e09729988bf0613c4
  languageName: node
  linkType: hard

"fill-range@npm:^2.1.0":
  version: 2.2.4
  resolution: "fill-range@npm:2.2.4"
  dependencies:
    is-number: ^2.1.0
    isobject: ^2.0.0
    randomatic: ^3.0.0
    repeat-element: ^1.1.2
    repeat-string: ^1.5.2
  checksum: ee7cb386c983bf7ff8aa120164c8b857a937c9d2b9c4ddf47af22f9d2bb1bd03dfa821946d7246f1631e86816562dd60059e081948d0804ce2ac0ac83f7edc61
  languageName: node
  linkType: hard

"fill-range@npm:^4.0.0":
  version: 4.0.0
  resolution: "fill-range@npm:4.0.0"
  dependencies:
    extend-shallow: ^2.0.1
    is-number: ^3.0.0
    repeat-string: ^1.6.1
    to-regex-range: ^2.1.0
  checksum: dbb5102467786ab42bc7a3ec7380ae5d6bfd1b5177b2216de89e4a541193f8ba599a6db84651bd2c58c8921db41b8cc3d699ea83b477342d3ce404020f73c298
  languageName: node
  linkType: hard

"fill-range@npm:^7.0.1":
  version: 7.0.1
  resolution: "fill-range@npm:7.0.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: cc283f4e65b504259e64fd969bcf4def4eb08d85565e906b7d36516e87819db52029a76b6363d0f02d0d532f0033c9603b9e2d943d56ee3b0d4f7ad3328ff917
  languageName: node
  linkType: hard

"finalhandler@npm:1.1.0":
  version: 1.1.0
  resolution: "finalhandler@npm:1.1.0"
  dependencies:
    debug: 2.6.9
    encodeurl: ~1.0.1
    escape-html: ~1.0.3
    on-finished: ~2.3.0
    parseurl: ~1.3.2
    statuses: ~1.3.1
    unpipe: ~1.0.0
  checksum: fb22b420315378b5c5d8a3a96f50c16a3ba3cc56b1ffa0bc65be63de978d08dc255002e4348663a6b2813e3ec6c930b1f1387aa3a0545d9bf4727b0f90a83ff2
  languageName: node
  linkType: hard

"finalhandler@npm:~1.1.2":
  version: 1.1.2
  resolution: "finalhandler@npm:1.1.2"
  dependencies:
    debug: 2.6.9
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    on-finished: ~2.3.0
    parseurl: ~1.3.3
    statuses: ~1.5.0
    unpipe: ~1.0.0
  checksum: 617880460c5138dd7ccfd555cb5dde4d8f170f4b31b8bd51e4b646bb2946c30f7db716428a1f2882d730d2b72afb47d1f67cc487b874cb15426f95753a88965e
  languageName: node
  linkType: hard

"find-babel-config@npm:^1.1.0":
  version: 1.2.0
  resolution: "find-babel-config@npm:1.2.0"
  dependencies:
    json5: ^0.5.1
    path-exists: ^3.0.0
  checksum: 0a1785d3da9f38637885d9d65f183aaa072f51a834f733035e9694e4d0f6983ae8c8e75cd4e08b92af6f595b3b490ee813a1c5a9b14740685aa836fa1e878583
  languageName: node
  linkType: hard

"find-cache-dir@npm:^0.1.1":
  version: 0.1.1
  resolution: "find-cache-dir@npm:0.1.1"
  dependencies:
    commondir: ^1.0.1
    mkdirp: ^0.5.1
    pkg-dir: ^1.0.0
  checksum: b5d9d68c1ff8c222124bb19089a405be9a3d0333e713ae989d980342c35690dfddd05f0fb456ec11846579e30e0f0e18293d20632662506cd2fa2c7237783479
  languageName: node
  linkType: hard

"find-cache-dir@npm:^1.0.0":
  version: 1.0.0
  resolution: "find-cache-dir@npm:1.0.0"
  dependencies:
    commondir: ^1.0.1
    make-dir: ^1.0.0
    pkg-dir: ^2.0.0
  checksum: d3caf3b0f90a2f281acb44a8db257ceb52d7fd65cc23d75463dff2f260947a35f27ba6c0695ba075e769607e71b1414ca0900072a5c5afd21e7c97f948e4f5ad
  languageName: node
  linkType: hard

"find-cache-dir@npm:^2.1.0":
  version: 2.1.0
  resolution: "find-cache-dir@npm:2.1.0"
  dependencies:
    commondir: ^1.0.1
    make-dir: ^2.0.0
    pkg-dir: ^3.0.0
  checksum: 60ad475a6da9f257df4e81900f78986ab367d4f65d33cf802c5b91e969c28a8762f098693d7a571b6e4dd4c15166c2da32ae2d18b6766a18e2071079448fdce4
  languageName: node
  linkType: hard

"find-cache-dir@npm:^3.3.1":
  version: 3.3.2
  resolution: "find-cache-dir@npm:3.3.2"
  dependencies:
    commondir: ^1.0.1
    make-dir: ^3.0.2
    pkg-dir: ^4.1.0
  checksum: 1e61c2e64f5c0b1c535bd85939ae73b0e5773142713273818cc0b393ee3555fb0fd44e1a5b161b8b6c3e03e98c2fcc9c227d784850a13a90a8ab576869576817
  languageName: node
  linkType: hard

"find-up@npm:^1.0.0":
  version: 1.1.2
  resolution: "find-up@npm:1.1.2"
  dependencies:
    path-exists: ^2.0.0
    pinkie-promise: ^2.0.0
  checksum: a2cb9f4c9f06ee3a1e92ed71d5aed41ac8ae30aefa568132f6c556fac7678a5035126153b59eaec68da78ac409eef02503b2b059706bdbf232668d7245e3240a
  languageName: node
  linkType: hard

"find-up@npm:^2.1.0":
  version: 2.1.0
  resolution: "find-up@npm:2.1.0"
  dependencies:
    locate-path: ^2.0.0
  checksum: 43284fe4da09f89011f08e3c32cd38401e786b19226ea440b75386c1b12a4cb738c94969808d53a84f564ede22f732c8409e3cfc3f7fb5b5c32378ad0bbf28bd
  languageName: node
  linkType: hard

"find-up@npm:^3.0.0":
  version: 3.0.0
  resolution: "find-up@npm:3.0.0"
  dependencies:
    locate-path: ^3.0.0
  checksum: 38eba3fe7a66e4bc7f0f5a1366dc25508b7cfc349f852640e3678d26ad9a6d7e2c43eff0a472287de4a9753ef58f066a0ea892a256fa3636ad51b3fe1e17fae9
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"flat-cache@npm:^1.2.1":
  version: 1.3.4
  resolution: "flat-cache@npm:1.3.4"
  dependencies:
    circular-json: ^0.3.1
    graceful-fs: ^4.1.2
    rimraf: ~2.6.2
    write: ^0.2.1
  checksum: 95605618db585e09881579b28d7b7e53215654451103425d1eb3fde2427ede7d71abf791f624c8c24d9dc0f7a4a3a3fa4ce8d146c846a31aa3b089380a89b1f3
  languageName: node
  linkType: hard

"flat-cache@npm:^2.0.1":
  version: 2.0.1
  resolution: "flat-cache@npm:2.0.1"
  dependencies:
    flatted: ^2.0.0
    rimraf: 2.6.3
    write: 1.0.3
  checksum: 0f5e66467658039e6fcaaccb363b28f43906ba72fab7ff2a4f6fcd5b4899679e13ca46d9fc6cc48b68ac925ae93137106d4aaeb79874c13f21f87a361705f1b1
  languageName: node
  linkType: hard

"flatted@npm:^2.0.0":
  version: 2.0.2
  resolution: "flatted@npm:2.0.2"
  checksum: ********************************************************************************************************************************
  languageName: node
  linkType: hard

"flush-write-stream@npm:^1.0.0":
  version: 1.1.1
  resolution: "flush-write-stream@npm:1.1.1"
  dependencies:
    inherits: ^2.0.3
    readable-stream: ^2.3.6
  checksum: 42e07747f83bcd4e799da802e621d6039787749ffd41f5517f8c4f786ee967e31ba32b09f8b28a9c6f67bd4f5346772e604202df350e8d99f4141771bae31279
  languageName: node
  linkType: hard

"follow-redirects@npm:1.5.10":
  version: 1.5.10
  resolution: "follow-redirects@npm:1.5.10"
  dependencies:
    debug: =3.1.0
  checksum: 0edc4b74e37e7b88ee716188a8f2a790238877c1d954f00c7b78d560f3bef40061c130536d13bee8e47b4e8e71edf1175a2de2729e51ab8206e4646b2370e484
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.0.0":
  version: 1.14.8
  resolution: "follow-redirects@npm:1.14.8"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 40c67899c2e3149a27e8b6498a338ff27f39fe138fde8d7f0756cb44b073ba0bfec3d52af28f20c5bdd67263d564d0d8d7b5efefd431de95c18c42f7b4aef457
  languageName: node
  linkType: hard

"for-each@npm:^0.3.3":
  version: 0.3.3
  resolution: "for-each@npm:0.3.3"
  dependencies:
    is-callable: ^1.1.3
  checksum: 6c48ff2bc63362319c65e2edca4a8e1e3483a2fabc72fbe7feaf8c73db94fc7861bd53bc02c8a66a0c1dd709da6b04eec42e0abdd6b40ce47305ae92a25e5d28
  languageName: node
  linkType: hard

"for-in@npm:^1.0.1, for-in@npm:^1.0.2":
  version: 1.0.2
  resolution: "for-in@npm:1.0.2"
  checksum: 09f4ae93ce785d253ac963d94c7f3432d89398bf25ac7a24ed034ca393bf74380bdeccc40e0f2d721a895e54211b07c8fad7132e8157827f6f7f059b70b4043d
  languageName: node
  linkType: hard

"for-own@npm:^0.1.4":
  version: 0.1.5
  resolution: "for-own@npm:0.1.5"
  dependencies:
    for-in: ^1.0.1
  checksum: 07eb0a2e98eb55ce13b56dd11ef4fb5e619ba7380aaec388b9eec1946153d74fa734ce409e8434020557e9489a50c34bc004d55754f5863bf7d77b441d8dee8c
  languageName: node
  linkType: hard

"forever-agent@npm:~0.6.1":
  version: 0.6.1
  resolution: "forever-agent@npm:0.6.1"
  checksum: 766ae6e220f5fe23676bb4c6a99387cec5b7b62ceb99e10923376e27bfea72f3c3aeec2ba5f45f3f7ba65d6616965aa7c20b15002b6860833bb6e394dea546a8
  languageName: node
  linkType: hard

"form-data@npm:~2.3.2":
  version: 2.3.3
  resolution: "form-data@npm:2.3.3"
  dependencies:
    asynckit: ^0.4.0
    combined-stream: ^1.0.6
    mime-types: ^2.1.12
  checksum: 10c1780fa13dbe1ff3100114c2ce1f9307f8be10b14bf16e103815356ff567b6be39d70fc4a40f8990b9660012dc24b0f5e1dde1b6426166eb23a445ba068ca3
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: fd27e2394d8887ebd16a66ffc889dc983fbbd797d5d3f01087c020283c0f019a7d05ee85669383d8e0d216b116d720fc0cef2f6e9b7eb9f4c90c6e0bc7fd28e6
  languageName: node
  linkType: hard

"fragment-cache@npm:^0.2.1":
  version: 0.2.1
  resolution: "fragment-cache@npm:0.2.1"
  dependencies:
    map-cache: ^0.2.2
  checksum: 1cbbd0b0116b67d5790175de0038a11df23c1cd2e8dcdbade58ebba5594c2d641dade6b4f126d82a7b4a6ffc2ea12e3d387dbb64ea2ae97cf02847d436f60fdc
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 13ea8b08f91e669a64e3ba3a20eb79d7ca5379a81f1ff7f4310d54e2320645503cc0c78daedc93dfb6191287295f6479544a649c64d8e41a1c0fb0c221552346
  languageName: node
  linkType: hard

"from2@npm:^2.1.0":
  version: 2.3.0
  resolution: "from2@npm:2.3.0"
  dependencies:
    inherits: ^2.0.1
    readable-stream: ^2.0.0
  checksum: 6080eba0793dce32f475141fb3d54cc15f84ee52e420ee22ac3ab0ad639dc95a1875bc6eb9c0e1140e94972a36a89dc5542491b85f1ab8df0c126241e0f1a61b
  languageName: node
  linkType: hard

"fs-extra@npm:^7.0.1":
  version: 7.0.1
  resolution: "fs-extra@npm:7.0.1"
  dependencies:
    graceful-fs: ^4.1.2
    jsonfile: ^4.0.0
    universalify: ^0.1.0
  checksum: 141b9dccb23b66a66cefdd81f4cda959ff89282b1d721b98cea19ba08db3dcbe6f862f28841f3cf24bb299e0b7e6c42303908f65093cb7e201708e86ea5a8dcf
  languageName: node
  linkType: hard

"fs-minipass@npm:^2.0.0":
  version: 2.1.0
  resolution: "fs-minipass@npm:2.1.0"
  dependencies:
    minipass: ^3.0.0
  checksum: 1b8d128dae2ac6cc94230cc5ead341ba3e0efaef82dab46a33d171c044caaa6ca001364178d42069b2809c35a1c3c35079a32107c770e9ffab3901b59af8c8b1
  languageName: node
  linkType: hard

"fs-write-stream-atomic@npm:^1.0.8":
  version: 1.0.10
  resolution: "fs-write-stream-atomic@npm:1.0.10"
  dependencies:
    graceful-fs: ^4.1.2
    iferr: ^0.1.5
    imurmurhash: ^0.1.4
    readable-stream: 1 || 2
  checksum: 43c2d6817b72127793abc811ebf87a135b03ac7cbe41cdea9eeacf59b23e6e29b595739b083e9461303d525687499a1aaefcec3e5ff9bc82b170edd3dc467ccc
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 99ddea01a7e75aa276c250a04eedeffe5662bce66c65c07164ad6264f9de18fb21be9433ead460e54cff20e31721c811f4fb5d70591799df5f85dce6d6746fd0
  languageName: node
  linkType: hard

"fsevents@npm:^1.2.3, fsevents@npm:^1.2.7":
  version: 1.2.13
  resolution: "fsevents@npm:1.2.13"
  dependencies:
    bindings: ^1.5.0
    nan: ^2.12.1
  checksum: ae855aa737aaa2f9167e9f70417cf6e45a5cd11918e1fee9923709a0149be52416d765433b4aeff56c789b1152e718cd1b13ddec6043b78cdda68260d86383c1
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@npm:~2.3.2":
  version: 2.3.2
  resolution: "fsevents@npm:2.3.2"
  dependencies:
    node-gyp: latest
  checksum: 97ade64e75091afee5265e6956cb72ba34db7819b4c3e94c431d4be2b19b8bb7a2d4116da417950c3425f17c8fe693d25e20212cac583ac1521ad066b77ae31f
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@^1.2.3#~builtin<compat/fsevents>, fsevents@patch:fsevents@^1.2.7#~builtin<compat/fsevents>":
  version: 1.2.13
  resolution: "fsevents@patch:fsevents@npm%3A1.2.13#~builtin<compat/fsevents>::version=1.2.13&hash=18f3a7"
  dependencies:
    bindings: ^1.5.0
    nan: ^2.12.1
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@~2.3.2#~builtin<compat/fsevents>":
  version: 2.3.2
  resolution: "fsevents@patch:fsevents@npm%3A2.3.2#~builtin<compat/fsevents>::version=2.3.2&hash=18f3a7"
  dependencies:
    node-gyp: latest
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.1":
  version: 1.1.1
  resolution: "function-bind@npm:1.1.1"
  checksum: b32fbaebb3f8ec4969f033073b43f5c8befbb58f1a79e12f1d7490358150359ebd92f49e72ff0144f65f2c48ea2a605bff2d07965f548f6474fd8efd95bf361a
  languageName: node
  linkType: hard

"functional-red-black-tree@npm:^1.0.1":
  version: 1.0.1
  resolution: "functional-red-black-tree@npm:1.0.1"
  checksum: ca6c170f37640e2d94297da8bb4bf27a1d12bea3e00e6a3e007fd7aa32e37e000f5772acf941b4e4f3cf1c95c3752033d0c509af157ad8f526e7f00723b9eb9f
  languageName: node
  linkType: hard

"gauge@npm:^4.0.0":
  version: 4.0.0
  resolution: "gauge@npm:4.0.0"
  dependencies:
    ansi-regex: ^5.0.1
    aproba: ^1.0.3 || ^2.0.0
    color-support: ^1.1.2
    console-control-strings: ^1.0.0
    has-unicode: ^2.0.1
    signal-exit: ^3.0.0
    string-width: ^4.2.3
    strip-ansi: ^6.0.1
    wide-align: ^1.1.2
  checksum: 637b34c84f518defa89319dbef68211a24e9302182ad2a619e3be1be5b7dcf2a962c8359e889294af667440f4722e7e6e61671859e00bd8ec280a136ded89b25
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: a7437e58c6be12aa6c90f7730eac7fa9833dc78872b4ad2963d2031b00a3367a93f98aec75f9aaac7220848e4026d67a8655e870b24f20a543d103c0d65952ec
  languageName: node
  linkType: hard

"get-caller-file@npm:^1.0.1":
  version: 1.0.3
  resolution: "get-caller-file@npm:1.0.3"
  checksum: 2b90a7f848896abcebcdc0acc627a435bcf05b9cd280599bc980ebfcdc222416c3df12c24c4845f69adc4346728e8966f70b758f9369f3534182791dfbc25c05
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.1":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: b9769a836d2a98c3ee734a88ba712e62703f1df31b94b784762c433c27a386dd6029ff55c2a920c392e33657d80191edbf18c61487e198844844516f843496b9
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.0.2, get-intrinsic@npm:^1.1.0, get-intrinsic@npm:^1.1.1":
  version: 1.1.1
  resolution: "get-intrinsic@npm:1.1.1"
  dependencies:
    function-bind: ^1.1.1
    has: ^1.0.3
    has-symbols: ^1.0.1
  checksum: a9fe2ca8fa3f07f9b0d30fb202bcd01f3d9b9b6b732452e79c48e79f7d6d8d003af3f9e38514250e3553fdc83c61650851cb6870832ac89deaaceb08e3721a17
  languageName: node
  linkType: hard

"get-stream@npm:^3.0.0":
  version: 3.0.0
  resolution: "get-stream@npm:3.0.0"
  checksum: 36142f46005ed74ce3a45c55545ec4e7da8e243554179e345a786baf144e5c4a35fb7bdc49fadfa9f18bd08000589b6fe364abdadfc4e1eb0e1b9914a6bb9c56
  languageName: node
  linkType: hard

"get-stream@npm:^4.0.0":
  version: 4.1.0
  resolution: "get-stream@npm:4.1.0"
  dependencies:
    pump: ^3.0.0
  checksum: 443e1914170c15bd52ff8ea6eff6dfc6d712b031303e36302d2778e3de2506af9ee964d6124010f7818736dcfde05c04ba7ca6cc26883106e084357a17ae7d73
  languageName: node
  linkType: hard

"get-symbol-description@npm:^1.0.0":
  version: 1.0.0
  resolution: "get-symbol-description@npm:1.0.0"
  dependencies:
    call-bind: ^1.0.2
    get-intrinsic: ^1.1.1
  checksum: 9ceff8fe968f9270a37a1f73bf3f1f7bda69ca80f4f80850670e0e7b9444ff99323f7ac52f96567f8b5f5fbe7ac717a0d81d3407c7313e82810c6199446a5247
  languageName: node
  linkType: hard

"get-value@npm:^2.0.3, get-value@npm:^2.0.6":
  version: 2.0.6
  resolution: "get-value@npm:2.0.6"
  checksum: 5c3b99cb5398ea8016bf46ff17afc5d1d286874d2ad38ca5edb6e87d75c0965b0094cb9a9dddef2c59c23d250702323539a7fbdd870620db38c7e7d7ec87c1eb
  languageName: node
  linkType: hard

"getpass@npm:^0.1.1":
  version: 0.1.7
  resolution: "getpass@npm:0.1.7"
  dependencies:
    assert-plus: ^1.0.0
  checksum: ab18d55661db264e3eac6012c2d3daeafaab7a501c035ae0ccb193c3c23e9849c6e29b6ac762b9c2adae460266f925d55a3a2a3a3c8b94be2f222df94d70c046
  languageName: node
  linkType: hard

"glob-base@npm:^0.3.0":
  version: 0.3.0
  resolution: "glob-base@npm:0.3.0"
  dependencies:
    glob-parent: ^2.0.0
    is-glob: ^2.0.0
  checksum: d0e3054a7df6033936980a3454ee6c91bb6661300b86b7a616d822a521e089afff1f5fbbd2582f9cee9f5823aed31d90244ee2e2e55f425103d42558615df294
  languageName: node
  linkType: hard

"glob-parent@npm:^2.0.0":
  version: 2.0.0
  resolution: "glob-parent@npm:2.0.0"
  dependencies:
    is-glob: ^2.0.0
  checksum: 734fc461d9d2753dd490dd072df6ce41fe4ebb60e9319b108bc538707b21780af3a61c3961ec2264131fad5d3d9a493e013a775aef11a69ac2f49fd7d8f46457
  languageName: node
  linkType: hard

"glob-parent@npm:^3.1.0":
  version: 3.1.0
  resolution: "glob-parent@npm:3.1.0"
  dependencies:
    is-glob: ^3.1.0
    path-dirname: ^1.0.0
  checksum: 653d559237e89a11b9934bef3f392ec42335602034c928590544d383ff5ef449f7b12f3cfa539708e74bc0a6c28ab1fe51d663cc07463cdf899ba92afd85a855
  languageName: node
  linkType: hard

"glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: ^4.0.1
  checksum: f4f2bfe2425296e8a47e36864e4f42be38a996db40420fe434565e4480e3322f18eb37589617a98640c5dc8fdec1a387007ee18dbb1f3f5553409c34d17f425e
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.3.0":
  version: 0.3.0
  resolution: "glob-to-regexp@npm:0.3.0"
  checksum: d34b3219d860042d508c4893b67617cd16e2668827e445ff39cff9f72ef70361d3dc24f429e003cdfb6607c75c9664b8eadc41d2eeb95690af0b0d3113c1b23b
  languageName: node
  linkType: hard

"glob@npm:^7.0.3, glob@npm:^7.1.1, glob@npm:^7.1.2, glob@npm:^7.1.3, glob@npm:^7.1.4":
  version: 7.2.0
  resolution: "glob@npm:7.2.0"
  dependencies:
    fs.realpath: ^1.0.0
    inflight: ^1.0.4
    inherits: 2
    minimatch: ^3.0.4
    once: ^1.3.0
    path-is-absolute: ^1.0.0
  checksum: 78a8ea942331f08ed2e055cb5b9e40fe6f46f579d7fd3d694f3412fe5db23223d29b7fee1575440202e9a7ff9a72ab106a39fee39934c7bedafe5e5f8ae20134
  languageName: node
  linkType: hard

"globals@npm:^11.0.1, globals@npm:^11.1.0, globals@npm:^11.7.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 67051a45eca3db904aee189dfc7cd53c20c7d881679c93f6146ddd4c9f4ab2268e68a919df740d39c71f4445d2b38ee360fc234428baea1dbdfe68bbcb46979e
  languageName: node
  linkType: hard

"globals@npm:^9.18.0":
  version: 9.18.0
  resolution: "globals@npm:9.18.0"
  checksum: e9c066aecfdc5ea6f727344a4246ecc243aaf66ede3bffee10ddc0c73351794c25e727dd046090dcecd821199a63b9de6af299a6e3ba292c8b22f0a80ea32073
  languageName: node
  linkType: hard

"globby@npm:^6.1.0":
  version: 6.1.0
  resolution: "globby@npm:6.1.0"
  dependencies:
    array-union: ^1.0.1
    glob: ^7.0.3
    object-assign: ^4.0.1
    pify: ^2.0.0
    pinkie-promise: ^2.0.0
  checksum: 18109d6b9d55643d2b98b59c3cfae7073ccfe39829632f353d516cc124d836c2ddebe48a23f04af63d66a621b6d86dd4cbd7e6af906f2458a7fe510ffc4bd424
  languageName: node
  linkType: hard

"globby@npm:^7.1.1":
  version: 7.1.1
  resolution: "globby@npm:7.1.1"
  dependencies:
    array-union: ^1.0.1
    dir-glob: ^2.0.0
    glob: ^7.1.2
    ignore: ^3.3.5
    pify: ^3.0.0
    slash: ^1.0.0
  checksum: f0eba08a08ae7c98149a4411661c0bf08c4717d81e6f355cf624fb01880b249737eb8e951bf86124cb3af8ea1c793c0a9d363ed5cdec99bb2c6b68f8a323025f
  languageName: node
  linkType: hard

"globby@npm:^9.2.0":
  version: 9.2.0
  resolution: "globby@npm:9.2.0"
  dependencies:
    "@types/glob": ^7.1.1
    array-union: ^1.0.2
    dir-glob: ^2.2.2
    fast-glob: ^2.2.6
    glob: ^7.1.3
    ignore: ^4.0.3
    pify: ^4.0.1
    slash: ^2.0.0
  checksum: 9b4cb70aa0b43bf89b18cf0e543695185e16d8dd99c17bdc6a1df0a9f88ff9dc8d2467aebace54c3842fc451a564882948c87a3b4fbdb1cacf3e05fd54b6ac5d
  languageName: node
  linkType: hard

"good-listener@npm:^1.2.2":
  version: 1.2.2
  resolution: "good-listener@npm:1.2.2"
  dependencies:
    delegate: ^3.1.2
  checksum: f39fb82c4e41524f56104cfd2d7aef1a88e72f3f75139115fbdf98cc7d844e0c1b39218b2e83438c6188727bf904ed78c7f0f2feff67b32833bc3af7f0202b33
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.11, graceful-fs@npm:^4.1.15, graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.6":
  version: 4.2.9
  resolution: "graceful-fs@npm:4.2.9"
  checksum: 68ea4e07ff2c041ada184f9278b830375f8e0b75154e3f080af6b70f66172fabb4108d19b3863a96b53fc068a310b9b6493d86d1291acc5f3861eb4b79d26ad6
  languageName: node
  linkType: hard

"growly@npm:^1.3.0":
  version: 1.3.0
  resolution: "growly@npm:1.3.0"
  checksum: 53cdecd4c16d7d9154a9061a9ccb87d602e957502ca69b529d7d1b2436c2c0b700ec544fc6b3e4cd115d59b81e62e44ce86bd0521403b579d3a2a97d7ce72a44
  languageName: node
  linkType: hard

"gzip-size@npm:^5.0.0":
  version: 5.1.1
  resolution: "gzip-size@npm:5.1.1"
  dependencies:
    duplexer: ^0.1.1
    pify: ^4.0.1
  checksum: 6451ba2210877368f6d9ee9b4dc0d14501671472801323bf81fbd38bdeb8525f40a78be45a59d0182895d51e6b60c6314b7d02bd6ed40e7225a01e8d038aac1b
  languageName: node
  linkType: hard

"handle-thing@npm:^2.0.0":
  version: 2.0.1
  resolution: "handle-thing@npm:2.0.1"
  checksum: 68071f313062315cd9dce55710e9496873945f1dd425107007058fc1629f93002a7649fcc3e464281ce02c7e809a35f5925504ab8105d972cf649f1f47cb7d6c
  languageName: node
  linkType: hard

"handlebars@npm:^4.0.3":
  version: 4.7.7
  resolution: "handlebars@npm:4.7.7"
  dependencies:
    minimist: ^1.2.5
    neo-async: ^2.6.0
    source-map: ^0.6.1
    uglify-js: ^3.1.4
    wordwrap: ^1.0.0
  dependenciesMeta:
    uglify-js:
      optional: true
  bin:
    handlebars: bin/handlebars
  checksum: 1e79a43f5e18d15742977cb987923eab3e2a8f44f2d9d340982bcb69e1735ed049226e534d7c1074eaddaf37e4fb4f471a8adb71cddd5bc8cf3f894241df5cee
  languageName: node
  linkType: hard

"har-schema@npm:^2.0.0":
  version: 2.0.0
  resolution: "har-schema@npm:2.0.0"
  checksum: d8946348f333fb09e2bf24cc4c67eabb47c8e1d1aa1c14184c7ffec1140a49ec8aa78aa93677ae452d71d5fc0fdeec20f0c8c1237291fc2bcb3f502a5d204f9b
  languageName: node
  linkType: hard

"har-validator@npm:~5.1.3":
  version: 5.1.5
  resolution: "har-validator@npm:5.1.5"
  dependencies:
    ajv: ^6.12.3
    har-schema: ^2.0.0
  checksum: b998a7269ca560d7f219eedc53e2c664cd87d487e428ae854a6af4573fc94f182fe9d2e3b92ab968249baec7ebaf9ead69cf975c931dc2ab282ec182ee988280
  languageName: node
  linkType: hard

"has-ansi@npm:^2.0.0":
  version: 2.0.0
  resolution: "has-ansi@npm:2.0.0"
  dependencies:
    ansi-regex: ^2.0.0
  checksum: 1b51daa0214440db171ff359d0a2d17bc20061164c57e76234f614c91dbd2a79ddd68dfc8ee73629366f7be45a6df5f2ea9de83f52e1ca24433f2cc78c35d8ec
  languageName: node
  linkType: hard

"has-bigints@npm:^1.0.1":
  version: 1.0.1
  resolution: "has-bigints@npm:1.0.1"
  checksum: 44ab55868174470065d2e0f8f6def1c990d12b82162a8803c679699fa8a39f966e336f2a33c185092fe8aea7e8bf2e85f1c26add5f29d98f2318bd270096b183
  languageName: node
  linkType: hard

"has-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-flag@npm:1.0.0"
  checksum: ce3f8ae978e70f16e4bbe17d3f0f6d6c0a3dd3b62a23f97c91d0fda9ed8e305e13baf95cc5bee4463b9f25ac9f5255de113165c5fb285e01b8065b2ac079b301
  languageName: node
  linkType: hard

"has-flag@npm:^2.0.0":
  version: 2.0.0
  resolution: "has-flag@npm:2.0.0"
  checksum: 7d060d142ef6740c79991cb99afe5962b267e6e95538bf8b607026b9b1e7451288927bc8e7b4a9484a8b99935c0af023070f91ee49faef791ecd401dc58b2e8d
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 4a15638b454bf086c8148979aae044dd6e39d63904cd452d970374fa6a87623423da485dfb814e7be882e05c096a7ccf1ebd48e7e7501d0208d8384ff4dea73b
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.1, has-symbols@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-symbols@npm:1.0.2"
  checksum: 2309c426071731be792b5be43b3da6fb4ed7cbe8a9a6bcfca1862587709f01b33d575ce8f5c264c1eaad09fca2f9a8208c0a2be156232629daa2dd0c0740976b
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-tostringtag@npm:1.0.0"
  dependencies:
    has-symbols: ^1.0.2
  checksum: cc12eb28cb6ae22369ebaad3a8ab0799ed61270991be88f208d508076a1e99abe4198c965935ce85ea90b60c94ddda73693b0920b58e7ead048b4a391b502c1c
  languageName: node
  linkType: hard

"has-unicode@npm:^2.0.1":
  version: 2.0.1
  resolution: "has-unicode@npm:2.0.1"
  checksum: 1eab07a7436512db0be40a710b29b5dc21fa04880b7f63c9980b706683127e3c1b57cb80ea96d47991bdae2dfe479604f6a1ba410106ee1046a41d1bd0814400
  languageName: node
  linkType: hard

"has-value@npm:^0.3.1":
  version: 0.3.1
  resolution: "has-value@npm:0.3.1"
  dependencies:
    get-value: ^2.0.3
    has-values: ^0.1.4
    isobject: ^2.0.0
  checksum: 29e2a1e6571dad83451b769c7ce032fce6009f65bccace07c2962d3ad4d5530b6743d8f3229e4ecf3ea8e905d23a752c5f7089100c1f3162039fa6dc3976558f
  languageName: node
  linkType: hard

"has-value@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-value@npm:1.0.0"
  dependencies:
    get-value: ^2.0.6
    has-values: ^1.0.0
    isobject: ^3.0.0
  checksum: b9421d354e44f03d3272ac39fd49f804f19bc1e4fa3ceef7745df43d6b402053f828445c03226b21d7d934a21ac9cf4bc569396dc312f496ddff873197bbd847
  languageName: node
  linkType: hard

"has-values@npm:^0.1.4":
  version: 0.1.4
  resolution: "has-values@npm:0.1.4"
  checksum: ab1c4bcaf811ccd1856c11cfe90e62fca9e2b026ebe474233a3d282d8d67e3b59ed85b622c7673bac3db198cb98bd1da2b39300a2f98e453729b115350af49bc
  languageName: node
  linkType: hard

"has-values@npm:^1.0.0":
  version: 1.0.0
  resolution: "has-values@npm:1.0.0"
  dependencies:
    is-number: ^3.0.0
    kind-of: ^4.0.0
  checksum: 77e6693f732b5e4cf6c38dfe85fdcefad0fab011af74995c3e83863fabf5e3a836f406d83565816baa0bc0a523c9410db8b990fe977074d61aeb6d8f4fcffa11
  languageName: node
  linkType: hard

"has@npm:^1.0.0, has@npm:^1.0.3":
  version: 1.0.3
  resolution: "has@npm:1.0.3"
  dependencies:
    function-bind: ^1.1.1
  checksum: b9ad53d53be4af90ce5d1c38331e712522417d017d5ef1ebd0507e07c2fbad8686fffb8e12ddecd4c39ca9b9b47431afbb975b8abf7f3c3b82c98e9aad052792
  languageName: node
  linkType: hard

"hash-base@npm:^3.0.0":
  version: 3.1.0
  resolution: "hash-base@npm:3.1.0"
  dependencies:
    inherits: ^2.0.4
    readable-stream: ^3.6.0
    safe-buffer: ^5.2.0
  checksum: 26b7e97ac3de13cb23fc3145e7e3450b0530274a9562144fc2bf5c1e2983afd0e09ed7cc3b20974ba66039fad316db463da80eb452e7373e780cbee9a0d2f2dc
  languageName: node
  linkType: hard

"hash-sum@npm:^1.0.2":
  version: 1.0.2
  resolution: "hash-sum@npm:1.0.2"
  checksum: 268553ba6c84333f502481d101a7d65cd39f61963544f12fc3ce60264718f471796dbc37348cee08c5529f04fafeba041886a4d35721e34d6440a48a42629283
  languageName: node
  linkType: hard

"hash.js@npm:^1.0.0, hash.js@npm:^1.0.3":
  version: 1.1.7
  resolution: "hash.js@npm:1.1.7"
  dependencies:
    inherits: ^2.0.3
    minimalistic-assert: ^1.0.1
  checksum: e350096e659c62422b85fa508e4b3669017311aa4c49b74f19f8e1bc7f3a54a584fdfd45326d4964d6011f2b2d882e38bea775a96046f2a61b7779a979629d8f
  languageName: node
  linkType: hard

"he@npm:1.2.x, he@npm:^1.1.0, he@npm:^1.1.1":
  version: 1.2.0
  resolution: "he@npm:1.2.0"
  bin:
    he: bin/he
  checksum: 3d4d6babccccd79c5c5a3f929a68af33360d6445587d628087f39a965079d84f18ce9c3d3f917ee1e3978916fc833bb8b29377c3b403f919426f91bc6965e7a7
  languageName: node
  linkType: hard

"hex-color-regex@npm:^1.1.0":
  version: 1.1.0
  resolution: "hex-color-regex@npm:1.1.0"
  checksum: 44fa1b7a26d745012f3bfeeab8015f60514f72d2fcf10dce33068352456b8d71a2e6bc5a17f933ab470da2c5ab1e3e04b05caf3fefe3c1cabd7e02e516fc8784
  languageName: node
  linkType: hard

"hmac-drbg@npm:^1.0.1":
  version: 1.0.1
  resolution: "hmac-drbg@npm:1.0.1"
  dependencies:
    hash.js: ^1.0.3
    minimalistic-assert: ^1.0.0
    minimalistic-crypto-utils: ^1.0.1
  checksum: bd30b6a68d7f22d63f10e1888aee497d7c2c5c0bb469e66bbdac99f143904d1dfe95f8131f95b3e86c86dd239963c9d972fcbe147e7cffa00e55d18585c43fe0
  languageName: node
  linkType: hard

"home-or-tmp@npm:^2.0.0":
  version: 2.0.0
  resolution: "home-or-tmp@npm:2.0.0"
  dependencies:
    os-homedir: ^1.0.0
    os-tmpdir: ^1.0.1
  checksum: b783c6ffd22f716d82f53e8c781cbe49bc9f4109a89ea86a27951e54c0bd335caf06bd828be2958cd9f4681986df1739558ae786abda6298cdd6d3edc2c362f1
  languageName: node
  linkType: hard

"home-or-tmp@npm:^3.0.0":
  version: 3.0.0
  resolution: "home-or-tmp@npm:3.0.0"
  checksum: 1800f305bf8a230b2dcec564955efd60dc7b173f11f64060a78bad2910f82d6349e47b7b7a95ad203440e3654ce0cd72b9f22efff1bf43b34b131135ee0494e5
  languageName: node
  linkType: hard

"hoopy@npm:^0.1.4":
  version: 0.1.4
  resolution: "hoopy@npm:0.1.4"
  checksum: cfa60c7684c5e1ee4efe26e167bc54b73f839ffb59d1d44a5c4bf891e26b4f5bcc666555219a98fec95508fea4eda3a79540c53c05cc79afc1f66f9a238f4d9e
  languageName: node
  linkType: hard

"hosted-git-info@npm:^2.1.4":
  version: 2.8.9
  resolution: "hosted-git-info@npm:2.8.9"
  checksum: c955394bdab888a1e9bb10eb33029e0f7ce5a2ac7b3f158099dc8c486c99e73809dca609f5694b223920ca2174db33d32b12f9a2a47141dc59607c29da5a62dd
  languageName: node
  linkType: hard

"hpack.js@npm:^2.1.6":
  version: 2.1.6
  resolution: "hpack.js@npm:2.1.6"
  dependencies:
    inherits: ^2.0.1
    obuf: ^1.0.0
    readable-stream: ^2.0.1
    wbuf: ^1.1.0
  checksum: 2de144115197967ad6eeee33faf41096c6ba87078703c5cb011632dcfbffeb45784569e0cf02c317bd79c48375597c8ec88c30fff5bb0b023e8f654fb6e9c06e
  languageName: node
  linkType: hard

"hsl-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "hsl-regex@npm:1.0.0"
  checksum: de9ee1bf39de1b83cc3fa0fa1cc337f29f14911e79411d66347365c54fab6b109eea2dd741eaa02486e24de31627ad7bf4453f22224fb55a2fe2b58166fa63b8
  languageName: node
  linkType: hard

"hsla-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "hsla-regex@npm:1.0.0"
  checksum: 9aa6eb9ff6c102d2395435aa5d1d91eae20043c4b1497c543d8db501c05f3edacd9a07fb34a987059d7902dba415af4cb4e610f751859ae8e7525df4ffcd085f
  languageName: node
  linkType: hard

"html-encoding-sniffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "html-encoding-sniffer@npm:1.0.2"
  dependencies:
    whatwg-encoding: ^1.0.1
  checksum: b874df6750451b7642fbe8e998c6bdd2911b0f42ad2927814b717bf1f4b082b0904b6178a1bfbc40117bf5799777993b0825e7713ca0fca49844e5aec03aa0e2
  languageName: node
  linkType: hard

"html-entities@npm:^1.3.1":
  version: 1.4.0
  resolution: "html-entities@npm:1.4.0"
  checksum: 4b73ffb9eead200f99146e4fbe70acb0af2fea136901a131fc3a782e9ef876a7cbb07dec303ca1f8804232b812249dbf3643a270c9c524852065d9224a8dcdd0
  languageName: node
  linkType: hard

"html-minifier@npm:^3.2.3":
  version: 3.5.21
  resolution: "html-minifier@npm:3.5.21"
  dependencies:
    camel-case: 3.0.x
    clean-css: 4.2.x
    commander: 2.17.x
    he: 1.2.x
    param-case: 2.1.x
    relateurl: 0.2.x
    uglify-js: 3.4.x
  bin:
    html-minifier: ./cli.js
  checksum: 66a86841a8b919a11a13d9b80176845cfbc5dda6e88efea2cf312ecc07427d9eab4aca70537357583e5e66ee1e62da14e035792eea000f8f3a9ca1856b2fb2b2
  languageName: node
  linkType: hard

"html-tags@npm:^2.0.0":
  version: 2.0.0
  resolution: "html-tags@npm:2.0.0"
  checksum: a02b47dd71de5572f16c9a1d88e2876fcc4d60bb36b7effce48cd3cd0bdd8fdcbf2602d968d2268d134767620d876edc08d8a6fc0abd9dc59a05e89d56251fbb
  languageName: node
  linkType: hard

"html-webpack-plugin@npm:3.2.0, html-webpack-plugin@npm:^3.2.0":
  version: 3.2.0
  resolution: "html-webpack-plugin@npm:3.2.0"
  dependencies:
    html-minifier: ^3.2.3
    loader-utils: ^0.2.16
    lodash: ^4.17.3
    pretty-error: ^2.0.2
    tapable: ^1.0.0
    toposort: ^1.0.0
    util.promisify: 1.0.0
  peerDependencies:
    webpack: ^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0
  checksum: e769e69a975997e35e080ff6305c98411cc358fe025f257600ed0f0536dc9560d204450baeb2855d2575ed68d3242f05d56e3a8d576e0ff87f04785ea95dabe8
  languageName: node
  linkType: hard

"htmlparser2@npm:^3.8.3":
  version: 3.10.1
  resolution: "htmlparser2@npm:3.10.1"
  dependencies:
    domelementtype: ^1.3.1
    domhandler: ^2.3.0
    domutils: ^1.5.1
    entities: ^1.1.1
    inherits: ^2.0.1
    readable-stream: ^3.1.1
  checksum: 6875f7dd875aa10be17d9b130e3738cd8ed4010b1f2edaf4442c82dfafe9d9336b155870dcc39f38843cbf7fef5e4fcfdf0c4c1fd4db3a1b91a1e0ee8f6c3475
  languageName: node
  linkType: hard

"htmlparser2@npm:^6.1.0":
  version: 6.1.0
  resolution: "htmlparser2@npm:6.1.0"
  dependencies:
    domelementtype: ^2.0.1
    domhandler: ^4.0.0
    domutils: ^2.5.2
    entities: ^2.0.0
  checksum: 81a7b3d9c3bb9acb568a02fc9b1b81ffbfa55eae7f1c41ae0bf840006d1dbf54cb3aa245b2553e2c94db674840a9f0fdad7027c9a9d01a062065314039058c4e
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.0":
  version: 4.1.0
  resolution: "http-cache-semantics@npm:4.1.0"
  checksum: 974de94a81c5474be07f269f9fd8383e92ebb5a448208223bfb39e172a9dbc26feff250192ecc23b9593b3f92098e010406b0f24bd4d588d631f80214648ed42
  languageName: node
  linkType: hard

"http-deceiver@npm:^1.2.7":
  version: 1.2.7
  resolution: "http-deceiver@npm:1.2.7"
  checksum: 64d7d1ae3a6933eb0e9a94e6f27be4af45a53a96c3c34e84ff57113787105a89fff9d1c3df263ef63add823df019b0e8f52f7121e32393bb5ce9a713bf100b41
  languageName: node
  linkType: hard

"http-errors@npm:1.8.1":
  version: 1.8.1
  resolution: "http-errors@npm:1.8.1"
  dependencies:
    depd: ~1.1.2
    inherits: 2.0.4
    setprototypeof: 1.2.0
    statuses: ">= 1.5.0 < 2"
    toidentifier: 1.0.1
  checksum: d3c7e7e776fd51c0a812baff570bdf06fe49a5dc448b700ab6171b1250e4cf7db8b8f4c0b133e4bfe2451022a5790c1ca6c2cae4094dedd6ac8304a1267f91d2
  languageName: node
  linkType: hard

"http-errors@npm:~1.6.2":
  version: 1.6.3
  resolution: "http-errors@npm:1.6.3"
  dependencies:
    depd: ~1.1.2
    inherits: 2.0.3
    setprototypeof: 1.1.0
    statuses: ">= 1.4.0 < 2"
  checksum: a9654ee027e3d5de305a56db1d1461f25709ac23267c6dc28cdab8323e3f96caa58a9a6a5e93ac15d7285cee0c2f019378c3ada9026e7fe19c872d695f27de7c
  languageName: node
  linkType: hard

"http-parser-js@npm:>=0.5.1":
  version: 0.5.5
  resolution: "http-parser-js@npm:0.5.5"
  checksum: 85e67f12d99d67565be6c82dd86d4cf71939825fdf9826e10047b2443460bfef13235859ca67c0235d54e553db242204ec813febc86f11f83ed8ebd3cd475b65
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^4.0.1":
  version: 4.0.1
  resolution: "http-proxy-agent@npm:4.0.1"
  dependencies:
    "@tootallnate/once": 1
    agent-base: 6
    debug: 4
  checksum: c6a5da5a1929416b6bbdf77b1aca13888013fe7eb9d59fc292e25d18e041bb154a8dfada58e223fc7b76b9b2d155a87e92e608235201f77d34aa258707963a82
  languageName: node
  linkType: hard

"http-proxy-middleware@npm:0.19.1":
  version: 0.19.1
  resolution: "http-proxy-middleware@npm:0.19.1"
  dependencies:
    http-proxy: ^1.17.0
    is-glob: ^4.0.0
    lodash: ^4.17.11
    micromatch: ^3.1.10
  checksum: 64df0438417a613bb22b3689d9652a1b7a56f10b145a463f95f4e8a9b9a351f2c63bc5fd3a9cd710baec224897733b6f299cb7f974ea82769b2a4f1e074764ac
  languageName: node
  linkType: hard

"http-proxy@npm:^1.17.0":
  version: 1.18.1
  resolution: "http-proxy@npm:1.18.1"
  dependencies:
    eventemitter3: ^4.0.0
    follow-redirects: ^1.0.0
    requires-port: ^1.0.0
  checksum: f5bd96bf83e0b1e4226633dbb51f8b056c3e6321917df402deacec31dd7fe433914fc7a2c1831cf7ae21e69c90b3a669b8f434723e9e8b71fd68afe30737b6a5
  languageName: node
  linkType: hard

"http-signature@npm:~1.2.0":
  version: 1.2.0
  resolution: "http-signature@npm:1.2.0"
  dependencies:
    assert-plus: ^1.0.0
    jsprim: ^1.2.2
    sshpk: ^1.7.0
  checksum: 3324598712266a9683585bb84a75dec4fd550567d5e0dd4a0fff6ff3f74348793404d3eeac4918fa0902c810eeee1a86419e4a2e92a164132dfe6b26743fb47c
  languageName: node
  linkType: hard

"https-browserify@npm:^1.0.0":
  version: 1.0.0
  resolution: "https-browserify@npm:1.0.0"
  checksum: 09b35353e42069fde2435760d13f8a3fb7dd9105e358270e2e225b8a94f811b461edd17cb57594e5f36ec1218f121c160ddceeec6e8be2d55e01dcbbbed8cbae
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^5.0.0":
  version: 5.0.0
  resolution: "https-proxy-agent@npm:5.0.0"
  dependencies:
    agent-base: 6
    debug: 4
  checksum: 165bfb090bd26d47693597661298006841ab733d0c7383a8cb2f17373387a94c903a3ac687090aa739de05e379ab6f868bae84ab4eac288ad85c328cd1ec9e53
  languageName: node
  linkType: hard

"humanize-ms@npm:^1.2.1":
  version: 1.2.1
  resolution: "humanize-ms@npm:1.2.1"
  dependencies:
    ms: ^2.0.0
  checksum: 9c7a74a2827f9294c009266c82031030eae811ca87b0da3dceb8d6071b9bde22c9f3daef0469c3c533cc67a97d8a167cd9fc0389350e5f415f61a79b171ded16
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24, iconv-lite@npm:^0.4.17, iconv-lite@npm:^0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3"
  checksum: bd9f120f5a5b306f0bc0b9ae1edeb1577161503f5f8252a20f1a9e56ef8775c9959fd01c55f2d3a39d9a8abaf3e30c1abeb1895f367dcbbe0a8fd1c9ca01c4f6
  languageName: node
  linkType: hard

"iconv-lite@npm:^0.6.2":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: ">= 2.1.2 < 3.0.0"
  checksum: 3f60d47a5c8fc3313317edfd29a00a692cc87a19cac0159e2ce711d0ebc9019064108323b5e493625e25594f11c6236647d8e256fbe7a58f4a3b33b89e6d30bf
  languageName: node
  linkType: hard

"icss-replace-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "icss-replace-symbols@npm:1.1.0"
  checksum: 24575b2c2f7e762bfc6f4beee31be9ba98a01cad521b5aa9954090a5de2b5e1bf67814c17e22f9e51b7d798238db8215a173d6c2b4726ce634ce06b68ece8045
  languageName: node
  linkType: hard

"icss-utils@npm:^2.1.0":
  version: 2.1.0
  resolution: "icss-utils@npm:2.1.0"
  dependencies:
    postcss: ^6.0.1
  checksum: 9f88bfe963cb753877ce274385dc9b05070a98cc99867acb79f5850b0b4af653b57631cbbbfc0d8106e4f9eddb5a39be150483e1b631c5ec289b5e2e439d24be
  languageName: node
  linkType: hard

"ieee754@npm:^1.1.4":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 5144c0c9815e54ada181d80a0b810221a253562422e7c6c3a60b1901154184f49326ec239d618c416c1c5945a2e197107aee8d986a3dd836b53dffefd99b5e7e
  languageName: node
  linkType: hard

"iferr@npm:^0.1.5":
  version: 0.1.5
  resolution: "iferr@npm:0.1.5"
  checksum: a18d19b6ad06a2d5412c0d37f6364869393ef6d1688d59d00082c1f35c92399094c031798340612458cd832f4f2e8b13bc9615934a7d8b0c53061307a3816aa1
  languageName: node
  linkType: hard

"ignore@npm:^3.3.3, ignore@npm:^3.3.5":
  version: 3.3.10
  resolution: "ignore@npm:3.3.10"
  checksum: 23e8cc776e367b56615ab21b78decf973a35dfca5522b39d9b47643d8168473b0d1f18dd1321a1bab466a12ea11a2411903f3b21644f4d5461ee0711ec8678bd
  languageName: node
  linkType: hard

"ignore@npm:^4.0.3, ignore@npm:^4.0.6":
  version: 4.0.6
  resolution: "ignore@npm:4.0.6"
  checksum: 248f82e50a430906f9ee7f35e1158e3ec4c3971451dd9f99c9bc1548261b4db2b99709f60ac6c6cac9333494384176cc4cc9b07acbe42d52ac6a09cad734d800
  languageName: node
  linkType: hard

"image-size@npm:^0.5.1":
  version: 0.5.5
  resolution: "image-size@npm:0.5.5"
  bin:
    image-size: bin/image-size.js
  checksum: 6709d5cb73e96d5097ae5e9aa746dd36d6a9c8cf645e7eecac72ea07dbd6f312a65183752762fa92e2f3b698d4ed8d85dd55bf5207b6367245996bd16576d8fe
  languageName: node
  linkType: hard

"immutable@npm:^4.0.0":
  version: 4.0.0
  resolution: "immutable@npm:4.0.0"
  checksum: 4b5e9181e4d5fa06728a481835ec09c86367e5d03268666c95b522b7644ab891098022e4479a43c4c81a68f2ed82f10751ce5d33e208d7b873b6e7f9dfaf4d87
  languageName: node
  linkType: hard

"import-cwd@npm:^2.0.0":
  version: 2.1.0
  resolution: "import-cwd@npm:2.1.0"
  dependencies:
    import-from: ^2.1.0
  checksum: b8786fa3578f3df55370352bf61f99c2d8e6ee9b5741a07503d5a73d99281d141330a8faf87078e67527be4558f758356791ee5efb4b0112ac5eaed0f07de544
  languageName: node
  linkType: hard

"import-fresh@npm:^2.0.0":
  version: 2.0.0
  resolution: "import-fresh@npm:2.0.0"
  dependencies:
    caller-path: ^2.0.0
    resolve-from: ^3.0.0
  checksum: 610255f9753cc6775df00be08e9f43691aa39f7703e3636c45afe22346b8b545e600ccfe100c554607546fc8e861fa149a0d1da078c8adedeea30fff326eef79
  languageName: node
  linkType: hard

"import-fresh@npm:^3.0.0":
  version: 3.3.0
  resolution: "import-fresh@npm:3.3.0"
  dependencies:
    parent-module: ^1.0.0
    resolve-from: ^4.0.0
  checksum: 2cacfad06e652b1edc50be650f7ec3be08c5e5a6f6d12d035c440a42a8cc028e60a5b99ca08a77ab4d6b1346da7d971915828f33cdab730d3d42f08242d09baa
  languageName: node
  linkType: hard

"import-from@npm:^2.1.0":
  version: 2.1.0
  resolution: "import-from@npm:2.1.0"
  dependencies:
    resolve-from: ^3.0.0
  checksum: 91f6f89f46a07227920ef819181bb52eb93023ccc0bdf00224fdfb326f8f753e279ad06819f39a02bb88c9d3a4606adc85b0cc995285e5d65feeb59f1421a1d4
  languageName: node
  linkType: hard

"import-local@npm:^1.0.0":
  version: 1.0.0
  resolution: "import-local@npm:1.0.0"
  dependencies:
    pkg-dir: ^2.0.0
    resolve-cwd: ^2.0.0
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: e7918eb5e20dcc755d6aa6b710ef0a346478256a3d7d7cde9e813c8f33019eda0fa3fe7427a9874314512f514c66c8156b7859e75ce8639769f25f2509ebd013
  languageName: node
  linkType: hard

"import-local@npm:^2.0.0":
  version: 2.0.0
  resolution: "import-local@npm:2.0.0"
  dependencies:
    pkg-dir: ^3.0.0
    resolve-cwd: ^2.0.0
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: b8469252483624379fd65d53c82f3658b32a1136f7168bfeea961a4ea7ca10a45786ea2b02e0006408f9cd22d2f33305a6f17a64e4d5a03274a50942c5e7c949
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 7cae75c8cd9a50f57dadd77482359f659eaebac0319dd9368bcd1714f55e65badd6929ca58569da2b6494ef13fdd5598cd700b1eba23f8b79c5f19d195a3ecf7
  languageName: node
  linkType: hard

"indent-string@npm:^4.0.0":
  version: 4.0.0
  resolution: "indent-string@npm:4.0.0"
  checksum: 824cfb9929d031dabf059bebfe08cf3137365e112019086ed3dcff6a0a7b698cb80cf67ccccde0e25b9e2d7527aa6cc1fed1ac490c752162496caba3e6699612
  languageName: node
  linkType: hard

"indexes-of@npm:^1.0.1":
  version: 1.0.1
  resolution: "indexes-of@npm:1.0.1"
  checksum: 4f9799b1739a62f3e02d09f6f4162cf9673025282af7fa36e790146e7f4e216dad3e776a25b08536c093209c9fcb5ea7bd04b082d42686a45f58ff401d6da32e
  languageName: node
  linkType: hard

"infer-owner@npm:^1.0.3, infer-owner@npm:^1.0.4":
  version: 1.0.4
  resolution: "infer-owner@npm:1.0.4"
  checksum: 181e732764e4a0611576466b4b87dac338972b839920b2a8cde43642e4ed6bd54dc1fb0b40874728f2a2df9a1b097b8ff83b56d5f8f8e3927f837fdcb47d8a89
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: ^1.3.0
    wrappy: 1
  checksum: f4f76aa072ce19fae87ce1ef7d221e709afb59d445e05d47fba710e85470923a75de35bfae47da6de1b18afc3ce83d70facf44cfb0aff89f0a3f45c0a0244dfd
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.1, inherits@npm:^2.0.3, inherits@npm:^2.0.4, inherits@npm:~2.0.1, inherits@npm:~2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 4a48a733847879d6cf6691860a6b1e3f0f4754176e4d71494c41f3475553768b10f84b5ce1d40fbd0e34e6bfbb864ee35858ad4dd2cf31e02fc4a154b724d7f1
  languageName: node
  linkType: hard

"inherits@npm:2.0.1":
  version: 2.0.1
  resolution: "inherits@npm:2.0.1"
  checksum: 6536b9377296d4ce8ee89c5c543cb75030934e61af42dba98a428e7d026938c5985ea4d1e3b87743a5b834f40ed1187f89c2d7479e9d59e41d2d1051aefba07b
  languageName: node
  linkType: hard

"inherits@npm:2.0.3":
  version: 2.0.3
  resolution: "inherits@npm:2.0.3"
  checksum: 78cb8d7d850d20a5e9a7f3620db31483aa00ad5f722ce03a55b110e5a723539b3716a3b463e2b96ce3fe286f33afc7c131fa2f91407528ba80cea98a7545d4c0
  languageName: node
  linkType: hard

"ini@npm:^1.3.4":
  version: 1.3.8
  resolution: "ini@npm:1.3.8"
  checksum: dfd98b0ca3a4fc1e323e38a6c8eb8936e31a97a918d3b377649ea15bdb15d481207a0dda1021efbd86b464cae29a0d33c1d7dcaf6c5672bee17fa849bc50a1b3
  languageName: node
  linkType: hard

"inquirer@npm:^3.0.6":
  version: 3.3.0
  resolution: "inquirer@npm:3.3.0"
  dependencies:
    ansi-escapes: ^3.0.0
    chalk: ^2.0.0
    cli-cursor: ^2.1.0
    cli-width: ^2.0.0
    external-editor: ^2.0.4
    figures: ^2.0.0
    lodash: ^4.3.0
    mute-stream: 0.0.7
    run-async: ^2.2.0
    rx-lite: ^4.0.8
    rx-lite-aggregates: ^4.0.8
    string-width: ^2.1.0
    strip-ansi: ^4.0.0
    through: ^2.3.6
  checksum: e8fb13a639a704e83706c838c3a79ca66310a8707f6fa1ec799fa00abd5a95f307ef034cbd4910a0d4bc8efb3fe4af7dca411efa4b6065d205d90a8519856626
  languageName: node
  linkType: hard

"inquirer@npm:^6.2.2":
  version: 6.5.2
  resolution: "inquirer@npm:6.5.2"
  dependencies:
    ansi-escapes: ^3.2.0
    chalk: ^2.4.2
    cli-cursor: ^2.1.0
    cli-width: ^2.0.0
    external-editor: ^3.0.3
    figures: ^2.0.0
    lodash: ^4.17.12
    mute-stream: 0.0.7
    run-async: ^2.2.0
    rxjs: ^6.4.0
    string-width: ^2.1.0
    strip-ansi: ^5.1.0
    through: ^2.3.6
  checksum: 175ad4cd1ebed493b231b240185f1da5afeace5f4e8811dfa83cf55dcae59c3255eaed990aa71871b0fd31aa9dc212f43c44c50ed04fb529364405e72f484d28
  languageName: node
  linkType: hard

"internal-ip@npm:^4.3.0":
  version: 4.3.0
  resolution: "internal-ip@npm:4.3.0"
  dependencies:
    default-gateway: ^4.2.0
    ipaddr.js: ^1.9.0
  checksum: c970433c84d9a6b46e2c9f5ab7785d3105b856d0a566891bf919241b5a884c5c1c9bf8e915aebb822a86c14b1b6867e58c1eaf5cd49eb023368083069d1a4a9a
  languageName: node
  linkType: hard

"internal-slot@npm:^1.0.3":
  version: 1.0.3
  resolution: "internal-slot@npm:1.0.3"
  dependencies:
    get-intrinsic: ^1.1.0
    has: ^1.0.3
    side-channel: ^1.0.4
  checksum: 1944f92e981e47aebc98a88ff0db579fd90543d937806104d0b96557b10c1f170c51fb777b97740a8b6ddeec585fca8c39ae99fd08a8e058dfc8ab70937238bf
  languageName: node
  linkType: hard

"invariant@npm:^2.2.2, invariant@npm:^2.2.4":
  version: 2.2.4
  resolution: "invariant@npm:2.2.4"
  dependencies:
    loose-envify: ^1.0.0
  checksum: cc3182d793aad82a8d1f0af697b462939cb46066ec48bbf1707c150ad5fad6406137e91a262022c269702e01621f35ef60269f6c0d7fd178487959809acdfb14
  languageName: node
  linkType: hard

"invert-kv@npm:^2.0.0":
  version: 2.0.0
  resolution: "invert-kv@npm:2.0.0"
  checksum: 52ea317354101ad6127c6e4c1c6a2d27ae8d3010b6438b60d76d6a920e55410e03547f97f9d1f52031becf5656bbef91d36ee7daa9e26ebc374a9cb342e1f127
  languageName: node
  linkType: hard

"ip-regex@npm:^2.1.0":
  version: 2.1.0
  resolution: "ip-regex@npm:2.1.0"
  checksum: 331d95052aa53ce245745ea0fc3a6a1e2e3c8d6da65fa8ea52bf73768c1b22a9ac50629d1d2b08c04e7b3ac4c21b536693c149ce2c2615ee4796030e5b3e3cba
  languageName: node
  linkType: hard

"ip@npm:^1.1.0, ip@npm:^1.1.5":
  version: 1.1.5
  resolution: "ip@npm:1.1.5"
  checksum: 30133981f082a060a32644f6a7746e9ba7ac9e2bc07ecc8bbdda3ee8ca9bec1190724c390e45a1ee7695e7edfd2a8f7dda2c104ec5f7ac5068c00648504c7e5a
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1, ipaddr.js@npm:^1.9.0":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: f88d3825981486f5a1942414c8d77dd6674dd71c065adcfa46f578d677edcb99fda25af42675cb59db492fdf427b34a5abfcde3982da11a8fd83a500b41cfe77
  languageName: node
  linkType: hard

"is-absolute-url@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-absolute-url@npm:2.1.0"
  checksum: 781e8cf8a2af54b1b7a92f269244d96c66224030d91120e734ebeebbce044c167767e1389789d8aaf82f9e429cb20ae93d6d0acfe6c4b53d2bd6ebb47a236d76
  languageName: node
  linkType: hard

"is-absolute-url@npm:^3.0.3":
  version: 3.0.3
  resolution: "is-absolute-url@npm:3.0.3"
  checksum: 5159b51d065d9ad29e16a2f78d6c0e41c43227caf90a45e659c54ea6fd50ef0595b1871ce392e84b1df7cfdcad9a8e66eec0813a029112188435abf115accb16
  languageName: node
  linkType: hard

"is-accessor-descriptor@npm:^0.1.6":
  version: 0.1.6
  resolution: "is-accessor-descriptor@npm:0.1.6"
  dependencies:
    kind-of: ^3.0.2
  checksum: 3d629a086a9585bc16a83a8e8a3416f400023301855cafb7ccc9a1d63145b7480f0ad28877dcc2cce09492c4ec1c39ef4c071996f24ee6ac626be4217b8ffc8a
  languageName: node
  linkType: hard

"is-accessor-descriptor@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-accessor-descriptor@npm:1.0.0"
  dependencies:
    kind-of: ^6.0.0
  checksum: 8e475968e9b22f9849343c25854fa24492dbe8ba0dea1a818978f9f1b887339190b022c9300d08c47fe36f1b913d70ce8cbaca00369c55a56705fdb7caed37fe
  languageName: node
  linkType: hard

"is-arguments@npm:^1.0.4":
  version: 1.1.1
  resolution: "is-arguments@npm:1.1.1"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: 7f02700ec2171b691ef3e4d0e3e6c0ba408e8434368504bb593d0d7c891c0dbfda6d19d30808b904a6cb1929bca648c061ba438c39f296c2a8ca083229c49f27
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: eef4417e3c10e60e2c810b6084942b3ead455af16c4509959a27e490e7aee87cfb3f38e01bbde92220b528a0ee1a18d52b787e1458ee86174d8c7f0e58cd488f
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.3.1":
  version: 0.3.2
  resolution: "is-arrayish@npm:0.3.2"
  checksum: 977e64f54d91c8f169b59afcd80ff19227e9f5c791fa28fa2e5bce355cbaf6c2c356711b734656e80c9dd4a854dd7efcf7894402f1031dfc5de5d620775b4d5f
  languageName: node
  linkType: hard

"is-bigint@npm:^1.0.1":
  version: 1.0.4
  resolution: "is-bigint@npm:1.0.4"
  dependencies:
    has-bigints: ^1.0.1
  checksum: c56edfe09b1154f8668e53ebe8252b6f185ee852a50f9b41e8d921cb2bed425652049fbe438723f6cb48a63ca1aa051e948e7e401e093477c99c84eba244f666
  languageName: node
  linkType: hard

"is-binary-path@npm:^1.0.0":
  version: 1.0.1
  resolution: "is-binary-path@npm:1.0.1"
  dependencies:
    binary-extensions: ^1.0.0
  checksum: a803c99e9d898170c3b44a86fbdc0736d3d7fcbe737345433fb78e810b9fe30c982657782ad0e676644ba4693ddf05601a7423b5611423218663d6b533341ac9
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: ^2.0.0
  checksum: 84192eb88cff70d320426f35ecd63c3d6d495da9d805b19bc65b518984b7c0760280e57dbf119b7e9be6b161784a5a673ab2c6abe83abb5198a432232ad5b35c
  languageName: node
  linkType: hard

"is-boolean-object@npm:^1.1.0":
  version: 1.1.2
  resolution: "is-boolean-object@npm:1.1.2"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: c03b23dbaacadc18940defb12c1c0e3aaece7553ef58b162a0f6bba0c2a7e1551b59f365b91e00d2dbac0522392d576ef322628cb1d036a0fe51eb466db67222
  languageName: node
  linkType: hard

"is-buffer@npm:^1.1.5":
  version: 1.1.6
  resolution: "is-buffer@npm:1.1.6"
  checksum: 4a186d995d8bbf9153b4bd9ff9fd04ae75068fe695d29025d25e592d9488911eeece84eefbd8fa41b8ddcc0711058a71d4c466dcf6f1f6e1d83830052d8ca707
  languageName: node
  linkType: hard

"is-buffer@npm:^2.0.2":
  version: 2.0.5
  resolution: "is-buffer@npm:2.0.5"
  checksum: 764c9ad8b523a9f5a32af29bdf772b08eb48c04d2ad0a7240916ac2688c983bf5f8504bf25b35e66240edeb9d9085461f9b5dae1f3d2861c6b06a65fe983de42
  languageName: node
  linkType: hard

"is-callable@npm:^1.1.3, is-callable@npm:^1.1.4, is-callable@npm:^1.2.4":
  version: 1.2.4
  resolution: "is-callable@npm:1.2.4"
  checksum: 1a28d57dc435797dae04b173b65d6d1e77d4f16276e9eff973f994eadcfdc30a017e6a597f092752a083c1103cceb56c91e3dadc6692fedb9898dfaba701575f
  languageName: node
  linkType: hard

"is-ci@npm:^1.0.10":
  version: 1.2.1
  resolution: "is-ci@npm:1.2.1"
  dependencies:
    ci-info: ^1.5.0
  bin:
    is-ci: bin.js
  checksum: eca06c5626e54ec01be6f9114a8f19b3f571602cfe66458e42ccc42e401e2ebbe1bd3b2fcaa93b5896b9c759e964f3c7f4d9b2d0f4fc4ef5dba78a7c4825e0be
  languageName: node
  linkType: hard

"is-ci@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-ci@npm:2.0.0"
  dependencies:
    ci-info: ^2.0.0
  bin:
    is-ci: bin.js
  checksum: 77b869057510f3efa439bbb36e9be429d53b3f51abd4776eeea79ab3b221337fe1753d1e50058a9e2c650d38246108beffb15ccfd443929d77748d8c0cc90144
  languageName: node
  linkType: hard

"is-color-stop@npm:^1.0.0":
  version: 1.1.0
  resolution: "is-color-stop@npm:1.1.0"
  dependencies:
    css-color-names: ^0.0.4
    hex-color-regex: ^1.1.0
    hsl-regex: ^1.0.0
    hsla-regex: ^1.0.0
    rgb-regex: ^1.0.1
    rgba-regex: ^1.0.0
  checksum: 778dd52a603ab8da827925aa4200fe6733b667b216495a04110f038b925dc5ef58babe759b94ffc4e44fcf439328695770873937f59d6045f676322b97f3f92d
  languageName: node
  linkType: hard

"is-core-module@npm:^2.8.1":
  version: 2.8.1
  resolution: "is-core-module@npm:2.8.1"
  dependencies:
    has: ^1.0.3
  checksum: 418b7bc10768a73c41c7ef497e293719604007f88934a6ffc5f7c78702791b8528102fb4c9e56d006d69361549b3d9519440214a74aefc7e0b79e5e4411d377f
  languageName: node
  linkType: hard

"is-data-descriptor@npm:^0.1.4":
  version: 0.1.4
  resolution: "is-data-descriptor@npm:0.1.4"
  dependencies:
    kind-of: ^3.0.2
  checksum: 5c622e078ba933a78338ae398a3d1fc5c23332b395312daf4f74bab4afb10d061cea74821add726cb4db8b946ba36217ee71a24fe71dd5bca4632edb7f6aad87
  languageName: node
  linkType: hard

"is-data-descriptor@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-data-descriptor@npm:1.0.0"
  dependencies:
    kind-of: ^6.0.0
  checksum: e705e6816241c013b05a65dc452244ee378d1c3e3842bd140beabe6e12c0d700ef23c91803f971aa7b091fb0573c5da8963af34a2b573337d87bc3e1f53a4e6d
  languageName: node
  linkType: hard

"is-date-object@npm:^1.0.1":
  version: 1.0.5
  resolution: "is-date-object@npm:1.0.5"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: baa9077cdf15eb7b58c79398604ca57379b2fc4cf9aa7a9b9e295278648f628c9b201400c01c5e0f7afae56507d741185730307cbe7cad3b9f90a77e5ee342fc
  languageName: node
  linkType: hard

"is-descriptor@npm:^0.1.0":
  version: 0.1.6
  resolution: "is-descriptor@npm:0.1.6"
  dependencies:
    is-accessor-descriptor: ^0.1.6
    is-data-descriptor: ^0.1.4
    kind-of: ^5.0.0
  checksum: 0f780c1b46b465f71d970fd7754096ffdb7b69fd8797ca1f5069c163eaedcd6a20ec4a50af669075c9ebcfb5266d2e53c8b227e485eefdb0d1fee09aa1dd8ab6
  languageName: node
  linkType: hard

"is-descriptor@npm:^1.0.0, is-descriptor@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-descriptor@npm:1.0.2"
  dependencies:
    is-accessor-descriptor: ^1.0.0
    is-data-descriptor: ^1.0.0
    kind-of: ^6.0.2
  checksum: 2ed623560bee035fb67b23e32ce885700bef8abe3fbf8c909907d86507b91a2c89a9d3a4d835a4d7334dd5db0237a0aeae9ca109c1e4ef1c0e7b577c0846ab5a
  languageName: node
  linkType: hard

"is-directory@npm:^0.3.1":
  version: 0.3.1
  resolution: "is-directory@npm:0.3.1"
  checksum: dce9a9d3981e38f2ded2a80848734824c50ee8680cd09aa477bef617949715cfc987197a2ca0176c58a9fb192a1a0d69b535c397140d241996a609d5906ae524
  languageName: node
  linkType: hard

"is-docker@npm:^2.0.0":
  version: 2.2.1
  resolution: "is-docker@npm:2.2.1"
  bin:
    is-docker: cli.js
  checksum: 3fef7ddbf0be25958e8991ad941901bf5922ab2753c46980b60b05c1bf9c9c2402d35e6dc32e4380b980ef5e1970a5d9d5e5aa2e02d77727c3b6b5e918474c56
  languageName: node
  linkType: hard

"is-dotfile@npm:^1.0.0":
  version: 1.0.3
  resolution: "is-dotfile@npm:1.0.3"
  checksum: 5257760d998d50ba2d5b4707007c043c69da0b62172f2221505edc6671ff634581c05df494a19d8d7206d248154c2f29f8432193ee199d33b09733e66371fb35
  languageName: node
  linkType: hard

"is-equal-shallow@npm:^0.1.3":
  version: 0.1.3
  resolution: "is-equal-shallow@npm:0.1.3"
  dependencies:
    is-primitive: ^2.0.0
  checksum: 1a296b660b8749ba1449017d9572e81fe8a96764877d5f9739c523a20cc7cdfa49594c16fa17052d0c3ee4711e35fd6919b06bf1b11b7126feab61abb9503ce6
  languageName: node
  linkType: hard

"is-extendable@npm:^0.1.0, is-extendable@npm:^0.1.1":
  version: 0.1.1
  resolution: "is-extendable@npm:0.1.1"
  checksum: 3875571d20a7563772ecc7a5f36cb03167e9be31ad259041b4a8f73f33f885441f778cee1f1fe0085eb4bc71679b9d8c923690003a36a6a5fdf8023e6e3f0672
  languageName: node
  linkType: hard

"is-extendable@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-extendable@npm:1.0.1"
  dependencies:
    is-plain-object: ^2.0.4
  checksum: db07bc1e9de6170de70eff7001943691f05b9d1547730b11be01c0ebfe67362912ba743cf4be6fd20a5e03b4180c685dad80b7c509fe717037e3eee30ad8e84f
  languageName: node
  linkType: hard

"is-extglob@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-extglob@npm:1.0.0"
  checksum: 5eea8517feeae5206547c0fc838c1416ec763b30093c286e1965a05f46b74a59ad391f912565f3b67c9c31cab4769ab9c35420e016b608acb47309be8d0d6e94
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.0, is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: df033653d06d0eb567461e58a7a8c9f940bd8c22274b94bf7671ab36df5719791aae15eef6d83bbb5e23283967f2f984b8914559d4449efda578c775c4be6f85
  languageName: node
  linkType: hard

"is-finite@npm:^1.0.0":
  version: 1.1.0
  resolution: "is-finite@npm:1.1.0"
  checksum: 532b97ed3d03e04c6bd203984d9e4ba3c0c390efee492bad5d1d1cd1802a68ab27adbd3ef6382f6312bed6c8bb1bd3e325ea79a8dc8fe080ed7a06f5f97b93e7
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-fullwidth-code-point@npm:1.0.0"
  dependencies:
    number-is-nan: ^1.0.0
  checksum: 4d46a7465a66a8aebcc5340d3b63a56602133874af576a9ca42c6f0f4bd787a743605771c5f246db77da96605fefeffb65fc1dbe862dcc7328f4b4d03edf5a57
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-fullwidth-code-point@npm:2.0.0"
  checksum: eef9c6e15f68085fec19ff6a978a6f1b8f48018fd1265035552078ee945573594933b09bbd6f562553e2a241561439f1ef5339276eba68d272001343084cfab8
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 44a30c29457c7fb8f00297bce733f0a64cd22eca270f83e58c105e0d015e45c019491a4ab2faef91ab51d4738c670daff901c799f6a700e27f7314029e99e348
  languageName: node
  linkType: hard

"is-generator-fn@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-generator-fn@npm:1.0.0"
  checksum: 9ea6404bdcbafc1edf2881e8fc57a7e2e8f48b2c04f24e4e889e670837d538c7e5a6ee645ee5217abb3e327ae511ed143824f8fde9c5c8e83129b20df9d2c78e
  languageName: node
  linkType: hard

"is-glob@npm:^2.0.0, is-glob@npm:^2.0.1":
  version: 2.0.1
  resolution: "is-glob@npm:2.0.1"
  dependencies:
    is-extglob: ^1.0.0
  checksum: 089f5f93640072491396a5f075ce73e949a90f35832b782bc49a6b7637d58e392d53cb0b395e059ccab70fcb82ff35d183f6f9ebbcb43227a1e02e3fed5430c9
  languageName: node
  linkType: hard

"is-glob@npm:^3.1.0":
  version: 3.1.0
  resolution: "is-glob@npm:3.1.0"
  dependencies:
    is-extglob: ^2.1.0
  checksum: 9d483bca84f16f01230f7c7c8c63735248fe1064346f292e0f6f8c76475fd20c6f50fc19941af5bec35f85d6bf26f4b7768f39a48a5f5fdc72b408dc74e07afc
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.0, is-glob@npm:^4.0.1, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: ^2.1.1
  checksum: d381c1319fcb69d341cc6e6c7cd588e17cd94722d9a32dbd60660b993c4fb7d0f19438674e68dfec686d09b7c73139c9166b47597f846af387450224a8101ab4
  languageName: node
  linkType: hard

"is-lambda@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-lambda@npm:1.0.1"
  checksum: 93a32f01940220532e5948538699ad610d5924ac86093fcee83022252b363eb0cc99ba53ab084a04e4fb62bf7b5731f55496257a4c38adf87af9c4d352c71c35
  languageName: node
  linkType: hard

"is-negative-zero@npm:^2.0.1":
  version: 2.0.2
  resolution: "is-negative-zero@npm:2.0.2"
  checksum: f3232194c47a549da60c3d509c9a09be442507616b69454716692e37ae9f37c4dea264fb208ad0c9f3efd15a796a46b79df07c7e53c6227c32170608b809149a
  languageName: node
  linkType: hard

"is-number-object@npm:^1.0.4":
  version: 1.0.6
  resolution: "is-number-object@npm:1.0.6"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: c697704e8fc2027fc41cb81d29805de4e8b6dc9c3efee93741dbf126a8ecc8443fef85adbc581415ae7e55d325e51d0a942324ae35c829131748cce39cba55f3
  languageName: node
  linkType: hard

"is-number@npm:^2.1.0":
  version: 2.1.0
  resolution: "is-number@npm:2.1.0"
  dependencies:
    kind-of: ^3.0.2
  checksum: d80e041a43a8de31ecc02037d532f1f448ec9c5b6c02fe7ee67bdd45d21cd9a4b3b4cf07e428ae5adafc2f17408c49fcb0a227915916d94a16d576c39e689f60
  languageName: node
  linkType: hard

"is-number@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-number@npm:3.0.0"
  dependencies:
    kind-of: ^3.0.2
  checksum: 0c62bf8e9d72c4dd203a74d8cfc751c746e75513380fef420cda8237e619a988ee43e678ddb23c87ac24d91ac0fe9f22e4ffb1301a50310c697e9d73ca3994e9
  languageName: node
  linkType: hard

"is-number@npm:^4.0.0":
  version: 4.0.0
  resolution: "is-number@npm:4.0.0"
  checksum: e71962a5ae97400211e6be5946eff2b81d3fa85154dad498bfe2704999e63ac6b3f8591fdb7971a121122cc6e25915c2cfe882ff7b77e243d51b92ca6961267e
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-obj@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-obj@npm:2.0.0"
  checksum: c9916ac8f4621962a42f5e80e7ffdb1d79a3fab7456ceaeea394cd9e0858d04f985a9ace45be44433bf605673c8be8810540fe4cc7f4266fc7526ced95af5a08
  languageName: node
  linkType: hard

"is-path-cwd@npm:^2.0.0":
  version: 2.2.0
  resolution: "is-path-cwd@npm:2.2.0"
  checksum: 46a840921bb8cc0dc7b5b423a14220e7db338072a4495743a8230533ce78812dc152548c86f4b828411fe98c5451959f07cf841c6a19f611e46600bd699e8048
  languageName: node
  linkType: hard

"is-path-in-cwd@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-path-in-cwd@npm:2.1.0"
  dependencies:
    is-path-inside: ^2.1.0
  checksum: 6b01b3f8c9172e9682ea878d001836a0cc5a78cbe6236024365d478c2c9e384da2417e5f21f2ad2da2761d0465309fc5baf6e71187d2a23f0058da69790f7f48
  languageName: node
  linkType: hard

"is-path-inside@npm:^2.1.0":
  version: 2.1.0
  resolution: "is-path-inside@npm:2.1.0"
  dependencies:
    path-is-inside: ^1.0.2
  checksum: 6ca34dbd84d5c50a3ee1547afb6ada9b06d556a4ff42da9b303797e4acc3ac086516a4833030aa570f397f8c58dacabd57ee8e6c2ce8b2396a986ad2af10fcaf
  languageName: node
  linkType: hard

"is-plain-obj@npm:^1.0.0, is-plain-obj@npm:^1.1":
  version: 1.1.0
  resolution: "is-plain-obj@npm:1.1.0"
  checksum: 0ee04807797aad50859652a7467481816cbb57e5cc97d813a7dcd8915da8195dc68c436010bf39d195226cde6a2d352f4b815f16f26b7bf486a5754290629931
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.3, is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: ^3.0.1
  checksum: 2a401140cfd86cabe25214956ae2cfee6fbd8186809555cd0e84574f88de7b17abacb2e477a6a658fa54c6083ecbda1e6ae404c7720244cd198903848fca70ca
  languageName: node
  linkType: hard

"is-posix-bracket@npm:^0.1.0":
  version: 0.1.1
  resolution: "is-posix-bracket@npm:0.1.1"
  checksum: 8a6391b41e7acef6898e64b00e06885b28c14b0c76398d3251a6ab1e5350d495dd32ec3f7f88f3f877558ce4b970939356fb315ee607f8f99a1716d4db7bdd40
  languageName: node
  linkType: hard

"is-primitive@npm:^2.0.0":
  version: 2.0.0
  resolution: "is-primitive@npm:2.0.0"
  checksum: 4d63fe952e31a4bc1d1a65d72f8485f5952407dce8d1cd8d8f070586936ea9ae2df79e0a83956b224aa7776cbbf5767eba3277f28119c36a616b20a439f057c0
  languageName: node
  linkType: hard

"is-regex@npm:^1.0.4, is-regex@npm:^1.1.4":
  version: 1.1.4
  resolution: "is-regex@npm:1.1.4"
  dependencies:
    call-bind: ^1.0.2
    has-tostringtag: ^1.0.0
  checksum: 362399b33535bc8f386d96c45c9feb04cf7f8b41c182f54174c1a45c9abbbe5e31290bbad09a458583ff6bf3b2048672cdb1881b13289569a7c548370856a652
  languageName: node
  linkType: hard

"is-resolvable@npm:^1.0.0":
  version: 1.1.0
  resolution: "is-resolvable@npm:1.1.0"
  checksum: 2ddff983be0cabc2c8d60246365755f8fb322f5fb9db834740d3e694c635c1b74c1bd674cf221e072fc4bd911ef3f08f2247d390e476f7e80af9092443193c68
  languageName: node
  linkType: hard

"is-shared-array-buffer@npm:^1.0.1":
  version: 1.0.1
  resolution: "is-shared-array-buffer@npm:1.0.1"
  checksum: 2ffb92533e64e2876e6cfe6906871d28400b6f1a53130fe652ec8007bc0e5044d05e7af8e31bdc992fbba520bd92938cfbeedd0f286be92f250c7c76191c4d90
  languageName: node
  linkType: hard

"is-stream@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-stream@npm:1.1.0"
  checksum: 063c6bec9d5647aa6d42108d4c59723d2bd4ae42135a2d4db6eadbd49b7ea05b750fd69d279e5c7c45cf9da753ad2c00d8978be354d65aa9f6bb434969c6a2ae
  languageName: node
  linkType: hard

"is-string@npm:^1.0.5, is-string@npm:^1.0.7":
  version: 1.0.7
  resolution: "is-string@npm:1.0.7"
  dependencies:
    has-tostringtag: ^1.0.0
  checksum: 323b3d04622f78d45077cf89aab783b2f49d24dc641aa89b5ad1a72114cfeff2585efc8c12ef42466dff32bde93d839ad321b26884cf75e5a7892a938b089989
  languageName: node
  linkType: hard

"is-symbol@npm:^1.0.2, is-symbol@npm:^1.0.3":
  version: 1.0.4
  resolution: "is-symbol@npm:1.0.4"
  dependencies:
    has-symbols: ^1.0.2
  checksum: 92805812ef590738d9de49d677cd17dfd486794773fb6fa0032d16452af46e9b91bb43ffe82c983570f015b37136f4b53b28b8523bfb10b0ece7a66c31a54510
  languageName: node
  linkType: hard

"is-typedarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "is-typedarray@npm:1.0.0"
  checksum: 3508c6cd0a9ee2e0df2fa2e9baabcdc89e911c7bd5cf64604586697212feec525aa21050e48affb5ffc3df20f0f5d2e2cf79b08caa64e1ccc9578e251763aef7
  languageName: node
  linkType: hard

"is-utf8@npm:^0.2.0":
  version: 0.2.1
  resolution: "is-utf8@npm:0.2.1"
  checksum: 167ccd2be869fc228cc62c1a28df4b78c6b5485d15a29027d3b5dceb09b383e86a3522008b56dcac14b592b22f0a224388718c2505027a994fd8471465de54b3
  languageName: node
  linkType: hard

"is-weakref@npm:^1.0.1":
  version: 1.0.2
  resolution: "is-weakref@npm:1.0.2"
  dependencies:
    call-bind: ^1.0.2
  checksum: 95bd9a57cdcb58c63b1c401c60a474b0f45b94719c30f548c891860f051bc2231575c290a6b420c6bc6e7ed99459d424c652bd5bf9a1d5259505dc35b4bf83de
  languageName: node
  linkType: hard

"is-whitespace@npm:^0.3.0":
  version: 0.3.0
  resolution: "is-whitespace@npm:0.3.0"
  checksum: dac8fc9a9b797afeef703f625269601715552883790d1385d6bb27dd04ffdafd5fddca8f2d85ee96913850211595da2ba483dac1f166829c4078fb58ce815140
  languageName: node
  linkType: hard

"is-windows@npm:^1.0.2":
  version: 1.0.2
  resolution: "is-windows@npm:1.0.2"
  checksum: 438b7e52656fe3b9b293b180defb4e448088e7023a523ec21a91a80b9ff8cdb3377ddb5b6e60f7c7de4fa8b63ab56e121b6705fe081b3cf1b828b0a380009ad7
  languageName: node
  linkType: hard

"is-wsl@npm:^1.1.0":
  version: 1.1.0
  resolution: "is-wsl@npm:1.1.0"
  checksum: ea157d232351e68c92bd62fc541771096942fe72f69dff452dd26dcc31466258c570a3b04b8cda2e01cd2968255b02951b8670d08ea4ed76d6b1a646061ac4fe
  languageName: node
  linkType: hard

"is-wsl@npm:^2.1.1":
  version: 2.2.0
  resolution: "is-wsl@npm:2.2.0"
  dependencies:
    is-docker: ^2.0.0
  checksum: 20849846ae414997d290b75e16868e5261e86ff5047f104027026fd61d8b5a9b0b3ade16239f35e1a067b3c7cc02f70183cb661010ed16f4b6c7c93dad1b19d8
  languageName: node
  linkType: hard

"isarray@npm:1.0.0, isarray@npm:^1.0.0, isarray@npm:~1.0.0":
  version: 1.0.0
  resolution: "isarray@npm:1.0.0"
  checksum: f032df8e02dce8ec565cf2eb605ea939bdccea528dbcf565cdf92bfa2da9110461159d86a537388ef1acef8815a330642d7885b29010e8f7eac967c9993b65ab
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isobject@npm:^2.0.0, isobject@npm:^2.1.0":
  version: 2.1.0
  resolution: "isobject@npm:2.1.0"
  dependencies:
    isarray: 1.0.0
  checksum: 811c6f5a866877d31f0606a88af4a45f282544de886bf29f6a34c46616a1ae2ed17076cc6bf34c0128f33eecf7e1fcaa2c82cf3770560d3e26810894e96ae79f
  languageName: node
  linkType: hard

"isobject@npm:^3.0.0, isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: db85c4c970ce30693676487cca0e61da2ca34e8d4967c2e1309143ff910c207133a969f9e4ddb2dc6aba670aabce4e0e307146c310350b298e74a31f7d464703
  languageName: node
  linkType: hard

"isstream@npm:~0.1.2":
  version: 0.1.2
  resolution: "isstream@npm:0.1.2"
  checksum: 1eb2fe63a729f7bdd8a559ab552c69055f4f48eb5c2f03724430587c6f450783c8f1cd936c1c952d0a927925180fcc892ebd5b174236cf1065d4bd5bdb37e963
  languageName: node
  linkType: hard

"istanbul-api@npm:^1.3.1":
  version: 1.3.7
  resolution: "istanbul-api@npm:1.3.7"
  dependencies:
    async: ^2.1.4
    fileset: ^2.0.2
    istanbul-lib-coverage: ^1.2.1
    istanbul-lib-hook: ^1.2.2
    istanbul-lib-instrument: ^1.10.2
    istanbul-lib-report: ^1.1.5
    istanbul-lib-source-maps: ^1.2.6
    istanbul-reports: ^1.5.1
    js-yaml: ^3.7.0
    mkdirp: ^0.5.1
    once: ^1.4.0
  checksum: fb8d42546a9105f100f1772fc5ff616aa0295ca05c6ad2d59b019e742d52e744d4b6d980ec3a2031601648025e02459a2294a250e57595a18dfaf25ab3fc4d73
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^1.2.0, istanbul-lib-coverage@npm:^1.2.1":
  version: 1.2.1
  resolution: "istanbul-lib-coverage@npm:1.2.1"
  checksum: 72bfeaa9212f5a6abb243cbce4933712599ba9a6fbdee819f4f5a4cf87ed15cb92772fcab219e93c3712c578774d6d8e54084440423356b3da5d9f8ecaba9888
  languageName: node
  linkType: hard

"istanbul-lib-hook@npm:^1.2.2":
  version: 1.2.2
  resolution: "istanbul-lib-hook@npm:1.2.2"
  dependencies:
    append-transform: ^0.4.0
  checksum: 356028b9f2436936fd7d36306460347da47d85e33678547203e809df88cc5b9747c23d629887e7c8fc6d28414f5a65ec0edddf056f5999740c2bdcdd9bf4c537
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^1.10.1, istanbul-lib-instrument@npm:^1.10.2":
  version: 1.10.2
  resolution: "istanbul-lib-instrument@npm:1.10.2"
  dependencies:
    babel-generator: ^6.18.0
    babel-template: ^6.16.0
    babel-traverse: ^6.18.0
    babel-types: ^6.18.0
    babylon: ^6.18.0
    istanbul-lib-coverage: ^1.2.1
    semver: ^5.3.0
  checksum: c299d73820b0ac93d1c53f436181da09579083dc4a0febadbda93f598f9a5591fe4888c3071a913eede36148d6481fdf163fa0b6ec7156fffe2a95cff965fc51
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^1.1.5":
  version: 1.1.5
  resolution: "istanbul-lib-report@npm:1.1.5"
  dependencies:
    istanbul-lib-coverage: ^1.2.1
    mkdirp: ^0.5.1
    path-parse: ^1.0.5
    supports-color: ^3.1.2
  checksum: 9a16d1fc1aa502f0c7594851637af99bc6e2db768b5eec171e04c5f1eb0cae5ca81cf7c31ad6c8138ec8528723603c123ff262abfa365f1d08859e512ab075e9
  languageName: node
  linkType: hard

"istanbul-lib-source-maps@npm:^1.2.4, istanbul-lib-source-maps@npm:^1.2.6":
  version: 1.2.6
  resolution: "istanbul-lib-source-maps@npm:1.2.6"
  dependencies:
    debug: ^3.1.0
    istanbul-lib-coverage: ^1.2.1
    mkdirp: ^0.5.1
    rimraf: ^2.6.1
    source-map: ^0.5.3
  checksum: 70a9811233a1558e2b3efcdfa0177b39ea84693843e181f7df1ba77c031a3b8dc5ab6cc0a21bd3b1840db1c3f73fec5a936f23d9727da46a08ae5a6b150c7949
  languageName: node
  linkType: hard

"istanbul-reports@npm:^1.5.1":
  version: 1.5.1
  resolution: "istanbul-reports@npm:1.5.1"
  dependencies:
    handlebars: ^4.0.3
  checksum: 7ac5c4cea6f81a5511047836906847177861ad5f8d23ddd84a4535f9ca47ee511b9cb0c07b5c72a928ab147645b41ab7942659e83d166e94dccf1868e6282435
  languageName: node
  linkType: hard

"javascript-stringify@npm:^1.6.0":
  version: 1.6.0
  resolution: "javascript-stringify@npm:1.6.0"
  checksum: bcf0516485e7ebe5318aa4ee27b6875c1d12bcef180fb6a7b5e0c9d4fba9fb7b2050fbe84ec1c27b3980cb34816d9d6c092b6c91ef354d61a8e5f9bfa73f191c
  languageName: node
  linkType: hard

"jest-changed-files@npm:^23.4.2":
  version: 23.4.2
  resolution: "jest-changed-files@npm:23.4.2"
  dependencies:
    throat: ^4.0.0
  checksum: 07dad83c96f8caea732884cc8c328145f146c17ee9a288d5d72d6db3e83b4e74fa009ae576b8bdc82849c21cae035fb87fe4dec0c3724f64d0dbe61a0b1ecc61
  languageName: node
  linkType: hard

"jest-cli@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest-cli@npm:23.6.0"
  dependencies:
    ansi-escapes: ^3.0.0
    chalk: ^2.0.1
    exit: ^0.1.2
    glob: ^7.1.2
    graceful-fs: ^4.1.11
    import-local: ^1.0.0
    is-ci: ^1.0.10
    istanbul-api: ^1.3.1
    istanbul-lib-coverage: ^1.2.0
    istanbul-lib-instrument: ^1.10.1
    istanbul-lib-source-maps: ^1.2.4
    jest-changed-files: ^23.4.2
    jest-config: ^23.6.0
    jest-environment-jsdom: ^23.4.0
    jest-get-type: ^22.1.0
    jest-haste-map: ^23.6.0
    jest-message-util: ^23.4.0
    jest-regex-util: ^23.3.0
    jest-resolve-dependencies: ^23.6.0
    jest-runner: ^23.6.0
    jest-runtime: ^23.6.0
    jest-snapshot: ^23.6.0
    jest-util: ^23.4.0
    jest-validate: ^23.6.0
    jest-watcher: ^23.4.0
    jest-worker: ^23.2.0
    micromatch: ^2.3.11
    node-notifier: ^5.2.1
    prompts: ^0.1.9
    realpath-native: ^1.0.0
    rimraf: ^2.5.4
    slash: ^1.0.0
    string-length: ^2.0.0
    strip-ansi: ^4.0.0
    which: ^1.2.12
    yargs: ^11.0.0
  bin:
    jest: ./bin/jest.js
  checksum: 2b694763ce11d3af13c9445e7715c0cc1d836119859770089cfeadfe8f09bf9abb5a3d740eb263818853fc4888d05f724f03a1019bb4c5a9750fdf151e418d0a
  languageName: node
  linkType: hard

"jest-config@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest-config@npm:23.6.0"
  dependencies:
    babel-core: ^6.0.0
    babel-jest: ^23.6.0
    chalk: ^2.0.1
    glob: ^7.1.1
    jest-environment-jsdom: ^23.4.0
    jest-environment-node: ^23.4.0
    jest-get-type: ^22.1.0
    jest-jasmine2: ^23.6.0
    jest-regex-util: ^23.3.0
    jest-resolve: ^23.6.0
    jest-util: ^23.4.0
    jest-validate: ^23.6.0
    micromatch: ^2.3.11
    pretty-format: ^23.6.0
  checksum: a2f57a0e1395b87f5ad3106fcb2dd01a69628ad02d2a317f1de7117136903aba75c2a137ab7fc90f28eba1a7c617f0259bf21a7e6e1746236125811cd6b25598
  languageName: node
  linkType: hard

"jest-diff@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest-diff@npm:23.6.0"
  dependencies:
    chalk: ^2.0.1
    diff: ^3.2.0
    jest-get-type: ^22.1.0
    pretty-format: ^23.6.0
  checksum: 0a91d5a407c5ab8421656c9aef7c7f6a18d0ea38fa0efc4f3726ed68594c4ca0be23b6c85830ee01d6a470ef7518a6942e0f3369b46b52ed9eeb96c8b176a134
  languageName: node
  linkType: hard

"jest-docblock@npm:^23.2.0":
  version: 23.2.0
  resolution: "jest-docblock@npm:23.2.0"
  dependencies:
    detect-newline: ^2.1.0
  checksum: 1b225189884751ad3503f901ebc157457d495a53a033390ac0093b20312bff18593bb3c963cbe860b0c2b9cb33e0da5761ce1f444498e5013ac80b5c06e4e52c
  languageName: node
  linkType: hard

"jest-each@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest-each@npm:23.6.0"
  dependencies:
    chalk: ^2.0.1
    pretty-format: ^23.6.0
  checksum: a9c0fe8f43ad8dc4948a3a8f691ca98a594c713fae064bc16cf5ae312c1f621f20c5e8ae530aadd06638ce42832679ef950f68dfdce19d5ba78a26398cf3bc28
  languageName: node
  linkType: hard

"jest-environment-jsdom@npm:^23.4.0":
  version: 23.4.0
  resolution: "jest-environment-jsdom@npm:23.4.0"
  dependencies:
    jest-mock: ^23.2.0
    jest-util: ^23.4.0
    jsdom: ^11.5.1
  checksum: b13e29cfdfd566a5976d8c91507409de64097aea81be2782042904960e29c1ca746966688b5e5e6b222629bd1434304b6d4d3b099ec620786bd014a95f40d3f5
  languageName: node
  linkType: hard

"jest-environment-node@npm:^23.4.0":
  version: 23.4.0
  resolution: "jest-environment-node@npm:23.4.0"
  dependencies:
    jest-mock: ^23.2.0
    jest-util: ^23.4.0
  checksum: a0f117a3ab2c25e26c55a1c02cf48439459a36f5889211483999c64d602d8a9169a7e3dbfcd174256a31536a6520c891b2736462ac6fa9d4357476f5e6b3e65e
  languageName: node
  linkType: hard

"jest-get-type@npm:^22.1.0":
  version: 22.4.3
  resolution: "jest-get-type@npm:22.4.3"
  checksum: f2daab7cb762457cdcd5500e3b0222cc96856649b685c40a621fe7395147ee67bbb594a2285b28c0b3cb9d80819eca4f99dd450df1bcac8a7cffa8a2dbaeec31
  languageName: node
  linkType: hard

"jest-haste-map@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest-haste-map@npm:23.6.0"
  dependencies:
    fb-watchman: ^2.0.0
    graceful-fs: ^4.1.11
    invariant: ^2.2.4
    jest-docblock: ^23.2.0
    jest-serializer: ^23.0.1
    jest-worker: ^23.2.0
    micromatch: ^2.3.11
    sane: ^2.0.0
  checksum: 392e9095b4bde8fed3c26cd7f84bce537e9184b2eea2ce3fcbf3b5a9a87017b21cf974030a0bd7ac33a81fad82cf5235b80cabca473fc431148bb9291230d623
  languageName: node
  linkType: hard

"jest-jasmine2@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest-jasmine2@npm:23.6.0"
  dependencies:
    babel-traverse: ^6.0.0
    chalk: ^2.0.1
    co: ^4.6.0
    expect: ^23.6.0
    is-generator-fn: ^1.0.0
    jest-diff: ^23.6.0
    jest-each: ^23.6.0
    jest-matcher-utils: ^23.6.0
    jest-message-util: ^23.4.0
    jest-snapshot: ^23.6.0
    jest-util: ^23.4.0
    pretty-format: ^23.6.0
  checksum: 2d14a6b90486aa4482d0146f2eb62352e0e1afa9c127c1d6c2e218779d314fd2da97f4ed33c08ec43a2a7593882386a497b2578d54f45b06ce5a1d0d14d0229b
  languageName: node
  linkType: hard

"jest-leak-detector@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest-leak-detector@npm:23.6.0"
  dependencies:
    pretty-format: ^23.6.0
  checksum: 23bfb5d6bc41b495f5fa7c12facf7331b93a03abd698988c8ddca7f458beca79885cd8277ca04e2a08cdca34eac41205b04182632b63f707190a6b6678fb600c
  languageName: node
  linkType: hard

"jest-matcher-utils@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest-matcher-utils@npm:23.6.0"
  dependencies:
    chalk: ^2.0.1
    jest-get-type: ^22.1.0
    pretty-format: ^23.6.0
  checksum: ae2d864c8fda68f32f783d0bf22b6793915e1ce0a3fc038104cc12df43adb0cca02715642998c7a90d425fd2ad4fb40ae9affa09024b604221c642d85fcb5fe4
  languageName: node
  linkType: hard

"jest-message-util@npm:^23.4.0":
  version: 23.4.0
  resolution: "jest-message-util@npm:23.4.0"
  dependencies:
    "@babel/code-frame": ^7.0.0-beta.35
    chalk: ^2.0.1
    micromatch: ^2.3.11
    slash: ^1.0.0
    stack-utils: ^1.0.1
  checksum: b3c602623b691bc582daf94f909d6a9fd91fd2a6d7223494ddd1cb644202b4e8cb77a9c49729ca2bb8b23e22e58ae109ede57d6b60c2270f2918df4345dcf6ac
  languageName: node
  linkType: hard

"jest-message-util@npm:^24.9.0":
  version: 24.9.0
  resolution: "jest-message-util@npm:24.9.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    "@jest/test-result": ^24.9.0
    "@jest/types": ^24.9.0
    "@types/stack-utils": ^1.0.1
    chalk: ^2.0.1
    micromatch: ^3.1.10
    slash: ^2.0.0
    stack-utils: ^1.0.1
  checksum: c173117b245090967db4853c28c3452ad2987a10caf28161abbfeb8d96be13f0d9e25422df10162bcc5e46860887e35ec4b4963f85392c4a625e4c37ad242f0b
  languageName: node
  linkType: hard

"jest-mock@npm:^23.2.0":
  version: 23.2.0
  resolution: "jest-mock@npm:23.2.0"
  checksum: 3ae0be23466999d7654f0bba79e15e19d58cb78f42ddd28439bcb2972dcc1556cb2b6146eadb31012b8c67bd23a7f39fe32f56c7cfc1c9c0f1202226d78e87c9
  languageName: node
  linkType: hard

"jest-mock@npm:^24.9.0":
  version: 24.9.0
  resolution: "jest-mock@npm:24.9.0"
  dependencies:
    "@jest/types": ^24.9.0
  checksum: 823feac37b003543fe81e05d5d8a1ec69cdf9ae5b797582a3e90424ec476120ce42a11e6b1d8231958e01232d4e40e57207cf2c56197d63d309bdeaf63fcf804
  languageName: node
  linkType: hard

"jest-regex-util@npm:^23.3.0":
  version: 23.3.0
  resolution: "jest-regex-util@npm:23.3.0"
  checksum: 5cdf684fe11d224a38c4191fae158edb879f93e3b8b21e3554df1b4171e84f5113f61ce085ea8ef8b2a286d7d8af59d64a1c04a9da484aeacb70040a455517bc
  languageName: node
  linkType: hard

"jest-resolve-dependencies@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest-resolve-dependencies@npm:23.6.0"
  dependencies:
    jest-regex-util: ^23.3.0
    jest-snapshot: ^23.6.0
  checksum: a6de3b97a2f637c8af4876eba544bc43af907aa017e2efcc8d42ba432daa2d4e8cff63052938d6db05d077054161542278e39e0631735af163ef1b25036a6a5e
  languageName: node
  linkType: hard

"jest-resolve@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest-resolve@npm:23.6.0"
  dependencies:
    browser-resolve: ^1.11.3
    chalk: ^2.0.1
    realpath-native: ^1.0.0
  checksum: a2e304162092b042a37de424db9296f5bc64c56e6ef7d7a545afa6929b67ecf4a2fcfba2444735b1103c1e437a1dafe4cf88922bf42843d471413a0e8403cb22
  languageName: node
  linkType: hard

"jest-runner@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest-runner@npm:23.6.0"
  dependencies:
    exit: ^0.1.2
    graceful-fs: ^4.1.11
    jest-config: ^23.6.0
    jest-docblock: ^23.2.0
    jest-haste-map: ^23.6.0
    jest-jasmine2: ^23.6.0
    jest-leak-detector: ^23.6.0
    jest-message-util: ^23.4.0
    jest-runtime: ^23.6.0
    jest-util: ^23.4.0
    jest-worker: ^23.2.0
    source-map-support: ^0.5.6
    throat: ^4.0.0
  checksum: 72606741af87e7a4e54aed398d2f386971b3ba15d007a28acabcf1f5c7772283bdc333e8cc82de498d0c52e8389ef8d9065b3de4c071ff6c351830380c34aa20
  languageName: node
  linkType: hard

"jest-runtime@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest-runtime@npm:23.6.0"
  dependencies:
    babel-core: ^6.0.0
    babel-plugin-istanbul: ^4.1.6
    chalk: ^2.0.1
    convert-source-map: ^1.4.0
    exit: ^0.1.2
    fast-json-stable-stringify: ^2.0.0
    graceful-fs: ^4.1.11
    jest-config: ^23.6.0
    jest-haste-map: ^23.6.0
    jest-message-util: ^23.4.0
    jest-regex-util: ^23.3.0
    jest-resolve: ^23.6.0
    jest-snapshot: ^23.6.0
    jest-util: ^23.4.0
    jest-validate: ^23.6.0
    micromatch: ^2.3.11
    realpath-native: ^1.0.0
    slash: ^1.0.0
    strip-bom: 3.0.0
    write-file-atomic: ^2.1.0
    yargs: ^11.0.0
  bin:
    jest-runtime: ./bin/jest-runtime.js
  checksum: 4eacd27dc2f4ec1cb17aed6626acb2e95eddea51b8b0ba618ca7cf4136d7e55e699f134609e332b70a48c01b2737bf96e241c3eef7abe062921be8a308d6188b
  languageName: node
  linkType: hard

"jest-serializer-vue@npm:^2.0.2":
  version: 2.0.2
  resolution: "jest-serializer-vue@npm:2.0.2"
  dependencies:
    pretty: 2.0.0
  checksum: a2f66e2b4f8b13f190c6272826da7ec2936841050e08d644b223f619c15e313f109125492d1fa8e7d2d0a13dfce2ff3cd9777f3e68388967a294badae37ef5bc
  languageName: node
  linkType: hard

"jest-serializer@npm:^23.0.1":
  version: 23.0.1
  resolution: "jest-serializer@npm:23.0.1"
  checksum: 3140d6cc723cda3c931c9ae4f855c8997dc229d68eab455551c4874ee0701d1e8e3d3410b8da7a889a968de9d9bb2800a5a216a80a031e5ed830a81efb69bc50
  languageName: node
  linkType: hard

"jest-snapshot@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest-snapshot@npm:23.6.0"
  dependencies:
    babel-types: ^6.0.0
    chalk: ^2.0.1
    jest-diff: ^23.6.0
    jest-matcher-utils: ^23.6.0
    jest-message-util: ^23.4.0
    jest-resolve: ^23.6.0
    mkdirp: ^0.5.1
    natural-compare: ^1.4.0
    pretty-format: ^23.6.0
    semver: ^5.5.0
  checksum: a844554aca9c9869a0325b9d2ee0dd3cb016de87811196b6e676f8215cc6ccea41d7897b9866515eecc86948b05c56e28c2c39bc9f5370f54a26cff7a736d804
  languageName: node
  linkType: hard

"jest-transform-stub@npm:^2.0.0":
  version: 2.0.0
  resolution: "jest-transform-stub@npm:2.0.0"
  checksum: c46134257e5fb047f5952e4a4a1339925dd009da15aa8d3310ba1d3e4d22266ed6c675cd18e58c9d5ca2246718d6cc8ff00ea6a4b6f3b2a6b285b6426f6ae8fe
  languageName: node
  linkType: hard

"jest-util@npm:^23.4.0":
  version: 23.4.0
  resolution: "jest-util@npm:23.4.0"
  dependencies:
    callsites: ^2.0.0
    chalk: ^2.0.1
    graceful-fs: ^4.1.11
    is-ci: ^1.0.10
    jest-message-util: ^23.4.0
    mkdirp: ^0.5.1
    slash: ^1.0.0
    source-map: ^0.6.0
  checksum: 383ac200f9f80cc555bd3f176de7d681969dac42d9b6b747ff01c7b3d12b840b9a360d5bf3b9b2c7504c209a1c2c304e2c5f6cc17dc7bd8aa3f10ea2516984d8
  languageName: node
  linkType: hard

"jest-util@npm:^24.9.0":
  version: 24.9.0
  resolution: "jest-util@npm:24.9.0"
  dependencies:
    "@jest/console": ^24.9.0
    "@jest/fake-timers": ^24.9.0
    "@jest/source-map": ^24.9.0
    "@jest/test-result": ^24.9.0
    "@jest/types": ^24.9.0
    callsites: ^3.0.0
    chalk: ^2.0.1
    graceful-fs: ^4.1.15
    is-ci: ^2.0.0
    mkdirp: ^0.5.1
    slash: ^2.0.0
    source-map: ^0.6.0
  checksum: ee84238bfb8c4aa60830b546e0e5dbdff53bbe55a1462f023182130ee7f1f3aac2dce0ab8395ab72b93e5a889fa12a55cebeeab04352a623d00d29c262dfbeb0
  languageName: node
  linkType: hard

"jest-validate@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest-validate@npm:23.6.0"
  dependencies:
    chalk: ^2.0.1
    jest-get-type: ^22.1.0
    leven: ^2.1.0
    pretty-format: ^23.6.0
  checksum: 31b06de6e41f0c1fa58e424bf6b76b9a751836b9f46f0cd0c43b49efd48b72e671e73f41a24e74d77d79e9fd73e4b8529a001fcb7fa143d80c6cd6d6ae9c9b52
  languageName: node
  linkType: hard

"jest-watch-typeahead@npm:^0.3.0":
  version: 0.3.1
  resolution: "jest-watch-typeahead@npm:0.3.1"
  dependencies:
    ansi-escapes: ^3.0.0
    chalk: ^2.4.1
    jest-watcher: ^24.3.0
    slash: ^2.0.0
    string-length: ^2.0.0
    strip-ansi: ^5.0.0
  checksum: 2c5cfcd1235681dc2819c862a8569ce0c078aa920793deb3a53e3b65edaffb5736707263e456b39be837bd5938af6725124d697431ff156d294cffbee55a1e48
  languageName: node
  linkType: hard

"jest-watcher@npm:^23.4.0":
  version: 23.4.0
  resolution: "jest-watcher@npm:23.4.0"
  dependencies:
    ansi-escapes: ^3.0.0
    chalk: ^2.0.1
    string-length: ^2.0.0
  checksum: c7285cd595644f824ca60bbcb1b342ae0c72f8a4f87f89a20c1326fa19efc80dbe0b14972660f1e037ffa64c3541ea1d06990836b2cf6b767ffa0006505d6c24
  languageName: node
  linkType: hard

"jest-watcher@npm:^24.3.0":
  version: 24.9.0
  resolution: "jest-watcher@npm:24.9.0"
  dependencies:
    "@jest/test-result": ^24.9.0
    "@jest/types": ^24.9.0
    "@types/yargs": ^13.0.0
    ansi-escapes: ^3.0.0
    chalk: ^2.0.1
    jest-util: ^24.9.0
    string-length: ^2.0.0
  checksum: c0ceec6e854ee73a196064e51471fe01ff743ca78df8f4ef1c78194a0fd4f43ece26d2c55d011e258ac7ae0f37eaecbe3cc100defb604124d90cd9473538a97b
  languageName: node
  linkType: hard

"jest-worker@npm:^23.2.0":
  version: 23.2.0
  resolution: "jest-worker@npm:23.2.0"
  dependencies:
    merge-stream: ^1.0.1
  checksum: 356a0c8528069a38bca8c3d85c16c13a147ce28faa52229c10d20f46b4e7e591322f5e7fa6a33929ee90f06997c6162bffa66b216737dc6f89afd51ff55044e9
  languageName: node
  linkType: hard

"jest@npm:^23.6.0":
  version: 23.6.0
  resolution: "jest@npm:23.6.0"
  dependencies:
    import-local: ^1.0.0
    jest-cli: ^23.6.0
  bin:
    jest: ./bin/jest.js
  checksum: a8b0e0229cbd6e00bc1e56900229bdb6523a6298901809bd0ececd8665af8cd006c6f0640d7919650ea18a32dca16d8e977fc98a3a0e2398ffddb3347780e48b
  languageName: node
  linkType: hard

"js-base64@npm:^2.1.9":
  version: 2.6.4
  resolution: "js-base64@npm:2.6.4"
  checksum: 5f4084078d6c46f8529741d110df84b14fac3276b903760c21fa8cc8521370d607325dfe1c1a9fbbeaae1ff8e602665aaeef1362427d8fef704f9e3659472ce8
  languageName: node
  linkType: hard

"js-beautify@npm:^1.6.12, js-beautify@npm:^1.6.14":
  version: 1.14.0
  resolution: "js-beautify@npm:1.14.0"
  dependencies:
    config-chain: ^1.1.12
    editorconfig: ^0.15.3
    glob: ^7.1.3
    nopt: ^5.0.0
  bin:
    css-beautify: js/bin/css-beautify.js
    html-beautify: js/bin/html-beautify.js
    js-beautify: js/bin/js-beautify.js
  checksum: 86a32c61364f9266d070b0a3b56c451b09d418d26030216ffdcb770b5bd06184c00ac9eb53ccf7765503bb74022a9e177b72bc043b9e6b8a8f22ca8569c3aef6
  languageName: node
  linkType: hard

"js-cookie@npm:2.2.0":
  version: 2.2.0
  resolution: "js-cookie@npm:2.2.0"
  checksum: e1b05f73b30e63bef7b4c6f82e9bc8ab673e8c70cd0498fc3cdb1dcf97540105ec14abf5d645b997006b9a0d2679f47b8ed97441f794f6f4923185c175f6a097
  languageName: node
  linkType: hard

"js-levenshtein@npm:^1.1.3":
  version: 1.1.6
  resolution: "js-levenshtein@npm:1.1.6"
  checksum: 409f052a7f1141be4058d97da7860e08efd97fc588b7a4c5cfa0548bc04f6d576644dae65ab630266dff685d56fb90d494e03d4d79cb484c287746b4f1bf0694
  languageName: node
  linkType: hard

"js-message@npm:1.0.7":
  version: 1.0.7
  resolution: "js-message@npm:1.0.7"
  checksum: 18dcc4d80356e8b5be978ca7838d96d4e350a1cb8adc5741c229dec6df09f53bfed7c75c1f360171d2d791a14e2f077d6c2b1013ba899ded7a27d7dfcd4f3784
  languageName: node
  linkType: hard

"js-queue@npm:2.0.2":
  version: 2.0.2
  resolution: "js-queue@npm:2.0.2"
  dependencies:
    easy-stack: ^1.0.1
  checksum: 5049c3f648315ed13e46755704ff5453df70f7e8e1812acf1f98d6700efbec32421f76294a0e63fd2a9f8aabaf124233bbb308f9a2caec9d9f3d833ab5a73079
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0, js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.2":
  version: 3.0.2
  resolution: "js-tokens@npm:3.0.2"
  checksum: ff24cf90e6e4ac446eba56e604781c1aaf3bdaf9b13a00596a0ebd972fa3b25dc83c0f0f67289c33252abb4111e0d14e952a5d9ffb61f5c22532d555ebd8d8a9
  languageName: node
  linkType: hard

"js-yaml@npm:^3.12.0, js-yaml@npm:^3.13.1, js-yaml@npm:^3.7.0, js-yaml@npm:^3.9.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: ^1.0.7
    esprima: ^4.0.0
  bin:
    js-yaml: bin/js-yaml.js
  checksum: bef146085f472d44dee30ec34e5cf36bf89164f5d585435a3d3da89e52622dff0b188a580e4ad091c3341889e14cb88cac6e4deb16dc5b1e9623bb0601fc255c
  languageName: node
  linkType: hard

"jsbn@npm:~0.1.0":
  version: 0.1.1
  resolution: "jsbn@npm:0.1.1"
  checksum: e5ff29c1b8d965017ef3f9c219dacd6e40ad355c664e277d31246c90545a02e6047018c16c60a00f36d561b3647215c41894f5d869ada6908a2e0ce4200c88f2
  languageName: node
  linkType: hard

"jsdom@npm:^11.5.1":
  version: 11.12.0
  resolution: "jsdom@npm:11.12.0"
  dependencies:
    abab: ^2.0.0
    acorn: ^5.5.3
    acorn-globals: ^4.1.0
    array-equal: ^1.0.0
    cssom: ">= 0.3.2 < 0.4.0"
    cssstyle: ^1.0.0
    data-urls: ^1.0.0
    domexception: ^1.0.1
    escodegen: ^1.9.1
    html-encoding-sniffer: ^1.0.2
    left-pad: ^1.3.0
    nwsapi: ^2.0.7
    parse5: 4.0.0
    pn: ^1.1.0
    request: ^2.87.0
    request-promise-native: ^1.0.5
    sax: ^1.2.4
    symbol-tree: ^3.2.2
    tough-cookie: ^2.3.4
    w3c-hr-time: ^1.0.1
    webidl-conversions: ^4.0.2
    whatwg-encoding: ^1.0.3
    whatwg-mimetype: ^2.1.0
    whatwg-url: ^6.4.1
    ws: ^5.2.0
    xml-name-validator: ^3.0.0
  checksum: 1dab757e92ce857df648ebec3dbe487954f886652faf9d97953c3b502958b1e4487e147baef5494718294e8625ae238e68354db710456fa73c394fb93dbfc68b
  languageName: node
  linkType: hard

"jsencrypt@npm:^3.0.0-rc.1":
  version: 3.2.1
  resolution: "jsencrypt@npm:3.2.1"
  checksum: daf813bac2f54b71afd4a3283e3725a759ef32ab1c4d5f8871f08c19975e943d2ff71e2adb89379192a990d985ccd7dc58f409ac3784d945b8b2522aeb193dcb
  languageName: node
  linkType: hard

"jsesc@npm:^1.3.0":
  version: 1.3.0
  resolution: "jsesc@npm:1.3.0"
  bin:
    jsesc: bin/jsesc
  checksum: 9384cc72bf8ef7f2eb75fea64176b8b0c1c5e77604854c72cb4670b7072e112e3baaa69ef134be98cb078834a7812b0bfe676ad441ccd749a59427f5ed2127f1
  languageName: node
  linkType: hard

"jsesc@npm:^2.5.1":
  version: 2.5.2
  resolution: "jsesc@npm:2.5.2"
  bin:
    jsesc: bin/jsesc
  checksum: 4dc190771129e12023f729ce20e1e0bfceac84d73a85bc3119f7f938843fe25a4aeccb54b6494dce26fcf263d815f5f31acdefac7cc9329efb8422a4f4d9fa9d
  languageName: node
  linkType: hard

"jsesc@npm:~0.5.0":
  version: 0.5.0
  resolution: "jsesc@npm:0.5.0"
  bin:
    jsesc: bin/jsesc
  checksum: b8b44cbfc92f198ad972fba706ee6a1dfa7485321ee8c0b25f5cedd538dcb20cde3197de16a7265430fce8277a12db066219369e3d51055038946039f6e20e17
  languageName: node
  linkType: hard

"json-parse-better-errors@npm:^1.0.1, json-parse-better-errors@npm:^1.0.2":
  version: 1.0.2
  resolution: "json-parse-better-errors@npm:1.0.2"
  checksum: ff2b5ba2a70e88fd97a3cb28c1840144c5ce8fae9cbeeddba15afa333a5c407cf0e42300cd0a2885dbb055227fe68d405070faad941beeffbfde9cf3b2c78c5d
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.3.0":
  version: 0.3.1
  resolution: "json-schema-traverse@npm:0.3.1"
  checksum: a685c36222023471c25c86cddcff506306ecb8f8941922fd356008419889c41c38e1c16d661d5499d0a561b34f417693e9bb9212ba2b2b2f8f8a345a49e4ec1a
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"json-schema@npm:0.4.0":
  version: 0.4.0
  resolution: "json-schema@npm:0.4.0"
  checksum: 66389434c3469e698da0df2e7ac5a3281bcff75e797a5c127db7c5b56270e01ae13d9afa3c03344f76e32e81678337a8c912bdbb75101c62e487dc3778461d72
  languageName: node
  linkType: hard

"json-stable-stringify-without-jsonify@npm:^1.0.1":
  version: 1.0.1
  resolution: "json-stable-stringify-without-jsonify@npm:1.0.1"
  checksum: cff44156ddce9c67c44386ad5cddf91925fe06b1d217f2da9c4910d01f358c6e3989c4d5a02683c7a5667f9727ff05831f7aa8ae66c8ff691c556f0884d49215
  languageName: node
  linkType: hard

"json-stringify-safe@npm:~5.0.1":
  version: 5.0.1
  resolution: "json-stringify-safe@npm:5.0.1"
  checksum: 48ec0adad5280b8a96bb93f4563aa1667fd7a36334f79149abd42446d0989f2ddc58274b479f4819f1f00617957e6344c886c55d05a4e15ebb4ab931e4a6a8ee
  languageName: node
  linkType: hard

"json3@npm:^3.3.3":
  version: 3.3.3
  resolution: "json3@npm:3.3.3"
  checksum: 55eda204a4c70d11b7d5caa5cb64c76a3aa54d5df72d07bdf446b922fd7cb8657b0732f68e0c36790f55e195e0a429c299144ff05430bbe93bc2a7c81ad3472b
  languageName: node
  linkType: hard

"json5@npm:^0.5.0, json5@npm:^0.5.1":
  version: 0.5.1
  resolution: "json5@npm:0.5.1"
  bin:
    json5: lib/cli.js
  checksum: 9b85bf06955b23eaa4b7328aa8892e3887e81ca731dd27af04a5f5f1458fbc5e1de57a24442e3272f8a888dd1abe1cb68eb693324035f6b3aeba4fcab7667d62
  languageName: node
  linkType: hard

"json5@npm:^1.0.1":
  version: 1.0.1
  resolution: "json5@npm:1.0.1"
  dependencies:
    minimist: ^1.2.0
  bin:
    json5: lib/cli.js
  checksum: e76ea23dbb8fc1348c143da628134a98adf4c5a4e8ea2adaa74a80c455fc2cdf0e2e13e6398ef819bfe92306b610ebb2002668ed9fc1af386d593691ef346fc3
  languageName: node
  linkType: hard

"json5@npm:^2.1.2":
  version: 2.2.0
  resolution: "json5@npm:2.2.0"
  dependencies:
    minimist: ^1.2.5
  bin:
    json5: lib/cli.js
  checksum: e88fc5274bb58fc99547baa777886b069d2dd96d9cfc4490b305fd16d711dabd5979e35a4f90873cefbeb552e216b041a304fe56702bedba76e19bc7845f208d
  languageName: node
  linkType: hard

"jsonfile@npm:^4.0.0":
  version: 4.0.0
  resolution: "jsonfile@npm:4.0.0"
  dependencies:
    graceful-fs: ^4.1.6
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 6447d6224f0d31623eef9b51185af03ac328a7553efcee30fa423d98a9e276ca08db87d71e17f2310b0263fd3ffa6c2a90a6308367f661dc21580f9469897c9e
  languageName: node
  linkType: hard

"jsprim@npm:^1.2.2":
  version: 1.4.2
  resolution: "jsprim@npm:1.4.2"
  dependencies:
    assert-plus: 1.0.0
    extsprintf: 1.3.0
    json-schema: 0.4.0
    verror: 1.10.0
  checksum: 2ad1b9fdcccae8b3d580fa6ced25de930eaa1ad154db21bbf8478a4d30bbbec7925b5f5ff29b933fba9412b16a17bd484a8da4fdb3663b5e27af95dd693bab2a
  languageName: node
  linkType: hard

"killable@npm:^1.0.1":
  version: 1.0.1
  resolution: "killable@npm:1.0.1"
  checksum: 911a85c6e390c19d72c4e3149347cf44042cbd7d18c3c6c5e4f706fdde6e0ed532473392e282c7ef27f518407e6cb7d2a0e71a2ae8d8d8f8ffdb68891a29a68a
  languageName: node
  linkType: hard

"kind-of@npm:^3.0.2, kind-of@npm:^3.0.3, kind-of@npm:^3.2.0":
  version: 3.2.2
  resolution: "kind-of@npm:3.2.2"
  dependencies:
    is-buffer: ^1.1.5
  checksum: e898df8ca2f31038f27d24f0b8080da7be274f986bc6ed176f37c77c454d76627619e1681f6f9d2e8d2fd7557a18ecc419a6bb54e422abcbb8da8f1a75e4b386
  languageName: node
  linkType: hard

"kind-of@npm:^4.0.0":
  version: 4.0.0
  resolution: "kind-of@npm:4.0.0"
  dependencies:
    is-buffer: ^1.1.5
  checksum: 1b9e7624a8771b5a2489026e820f3bbbcc67893e1345804a56b23a91e9069965854d2a223a7c6ee563c45be9d8c6ff1ef87f28ed5f0d1a8d00d9dcbb067c529f
  languageName: node
  linkType: hard

"kind-of@npm:^5.0.0, kind-of@npm:^5.0.2":
  version: 5.1.0
  resolution: "kind-of@npm:5.1.0"
  checksum: f2a0102ae0cf19c4a953397e552571bad2b588b53282874f25fca7236396e650e2db50d41f9f516bd402536e4df968dbb51b8e69e4d5d4a7173def78448f7bab
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.0, kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 3ab01e7b1d440b22fe4c31f23d8d38b4d9b91d9f291df683476576493d5dfd2e03848a8b05813dd0c3f0e835bc63f433007ddeceb71f05cb25c45ae1b19c6d3b
  languageName: node
  linkType: hard

"kleur@npm:^2.0.1":
  version: 2.0.2
  resolution: "kleur@npm:2.0.2"
  checksum: 897eb39f3711d8d2e3b14744aa815886535d5153e9ce0d8433fac1def3778183de6762ecaee02cab71b512cddc91069ab517d0872be21a888eb8714d69b23f62
  languageName: node
  linkType: hard

"launch-editor-middleware@npm:^2.2.1":
  version: 2.3.0
  resolution: "launch-editor-middleware@npm:2.3.0"
  dependencies:
    launch-editor: ^2.3.0
  checksum: b62a697294a2d0c76a69151578b41b3eb2927faf7c6e8e90783a81440717c96a3524a8504a9b81c0bbb3f4f8176e2b411912401a6286c79a38d17ce5ccdce4ee
  languageName: node
  linkType: hard

"launch-editor@npm:^2.2.1, launch-editor@npm:^2.3.0":
  version: 2.3.0
  resolution: "launch-editor@npm:2.3.0"
  dependencies:
    picocolors: ^1.0.0
    shell-quote: ^1.6.1
  checksum: 64fec34e5c7b2a26ca048c7ed79f51b662684221259de88d8c592c65691bb84ed80310cb0f6a36e423883022bf680efb69c6ee29089680b523d013c6826c1116
  languageName: node
  linkType: hard

"lcid@npm:^2.0.0":
  version: 2.0.0
  resolution: "lcid@npm:2.0.0"
  dependencies:
    invert-kv: ^2.0.0
  checksum: 278e27b5a0707cf9ab682146963ebff2328795be10cd6f8ea8edae293439325d345ac5e33079cce77ac3a86a3dcfb97a34f279dbc46b03f3e419aa39b5915a16
  languageName: node
  linkType: hard

"left-pad@npm:^1.3.0":
  version: 1.3.0
  resolution: "left-pad@npm:1.3.0"
  checksum: 13fa96e17b70a54836490de22d4bab706e2ed508338bbabecfac72ecce445a74139c5b009a8112252cab8fc4ab7ac4ebd870e5b35bd236b443b12be96f8745ac
  languageName: node
  linkType: hard

"leven@npm:^2.1.0":
  version: 2.1.0
  resolution: "leven@npm:2.1.0"
  checksum: f7b4a01b15c0ee2f92a04c0367ea025d10992b044df6f0d4ee1a845d4a488b343e99799e2f31212d72a2b1dea67124f57c1bb1b4561540df45190e44b5b8b394
  languageName: node
  linkType: hard

"levn@npm:^0.3.0, levn@npm:~0.3.0":
  version: 0.3.0
  resolution: "levn@npm:0.3.0"
  dependencies:
    prelude-ls: ~1.1.2
    type-check: ~0.3.2
  checksum: 0d084a524231a8246bb10fec48cdbb35282099f6954838604f3c7fc66f2e16fa66fd9cc2f3f20a541a113c4dafdf181e822c887c8a319c9195444e6c64ac395e
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 0c37f9f7fa212b38912b7145e1cd16a5f3cd34d782441c3e6ca653485d326f58b3caccda66efce1c5812bde4961bbde3374fae4b0d11bf1226152337f3894aa5
  languageName: node
  linkType: hard

"load-json-file@npm:^1.0.0":
  version: 1.1.0
  resolution: "load-json-file@npm:1.1.0"
  dependencies:
    graceful-fs: ^4.1.2
    parse-json: ^2.2.0
    pify: ^2.0.0
    pinkie-promise: ^2.0.0
    strip-bom: ^2.0.0
  checksum: 0e4e4f380d897e13aa236246a917527ea5a14e4fc34d49e01ce4e7e2a1e08e2740ee463a03fb021c04f594f29a178f4adb994087549d7c1c5315fcd29bf9934b
  languageName: node
  linkType: hard

"loader-fs-cache@npm:^1.0.0":
  version: 1.0.3
  resolution: "loader-fs-cache@npm:1.0.3"
  dependencies:
    find-cache-dir: ^0.1.1
    mkdirp: ^0.5.1
  checksum: 39781412e10bb0d6b5ca1afa9a4bd65e1827c5c51ef9ff746ae3fe8ce0e2cfa3fb96492d6619d8ab305407d20be82a9b244c439df0207f6ced4b98f2861bd372
  languageName: node
  linkType: hard

"loader-runner@npm:^2.3.0, loader-runner@npm:^2.3.1, loader-runner@npm:^2.4.0":
  version: 2.4.0
  resolution: "loader-runner@npm:2.4.0"
  checksum: e27eebbca5347a03f6b1d1bce5b2736a4984fb742f872c0a4d68e62de10f7637613e79a464d3bcd77c246d9c70fcac112bb4a3123010eb527e8b203a614647db
  languageName: node
  linkType: hard

"loader-utils@npm:^0.2.16":
  version: 0.2.17
  resolution: "loader-utils@npm:0.2.17"
  dependencies:
    big.js: ^3.1.3
    emojis-list: ^2.0.0
    json5: ^0.5.0
    object-assign: ^4.0.1
  checksum: 3045c83ef8b19d66d4c25e3245120c579883f473fe0d0559552f55502be913725c4d558a7c866191a74b19ef2af20b094afe3b144ae1e717ea4c245d52f60a09
  languageName: node
  linkType: hard

"loader-utils@npm:^1.0.1, loader-utils@npm:^1.0.2, loader-utils@npm:^1.1.0, loader-utils@npm:^1.2.3, loader-utils@npm:^1.4.0":
  version: 1.4.0
  resolution: "loader-utils@npm:1.4.0"
  dependencies:
    big.js: ^5.2.2
    emojis-list: ^3.0.0
    json5: ^1.0.1
  checksum: d150b15e7a42ac47d935c8b484b79e44ff6ab4c75df7cc4cb9093350cf014ec0b17bdb60c5d6f91a37b8b218bd63b973e263c65944f58ca2573e402b9a27e717
  languageName: node
  linkType: hard

"locate-path@npm:^2.0.0":
  version: 2.0.0
  resolution: "locate-path@npm:2.0.0"
  dependencies:
    p-locate: ^2.0.0
    path-exists: ^3.0.0
  checksum: 02d581edbbbb0fa292e28d96b7de36b5b62c2fa8b5a7e82638ebb33afa74284acf022d3b1e9ae10e3ffb7658fbc49163fcd5e76e7d1baaa7801c3e05a81da755
  languageName: node
  linkType: hard

"locate-path@npm:^3.0.0":
  version: 3.0.0
  resolution: "locate-path@npm:3.0.0"
  dependencies:
    p-locate: ^3.0.0
    path-exists: ^3.0.0
  checksum: 53db3996672f21f8b0bf2a2c645ae2c13ffdae1eeecfcd399a583bce8516c0b88dcb4222ca6efbbbeb6949df7e46860895be2c02e8d3219abd373ace3bfb4e11
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"lodash.debounce@npm:^4.0.8":
  version: 4.0.8
  resolution: "lodash.debounce@npm:4.0.8"
  checksum: a3f527d22c548f43ae31c861ada88b2637eb48ac6aa3eb56e82d44917971b8aa96fbb37aa60efea674dc4ee8c42074f90f7b1f772e9db375435f6c83a19b3bc6
  languageName: node
  linkType: hard

"lodash.defaultsdeep@npm:^4.6.0":
  version: 4.6.1
  resolution: "lodash.defaultsdeep@npm:4.6.1"
  checksum: 1f346f16158b760545ca99553cb13e907a28b281425751af6bfe681387b9e5685438a7ddbfd36a8d5cc8bda066867a134aa31416f17e318db8c461c377810a76
  languageName: node
  linkType: hard

"lodash.kebabcase@npm:^4.1.1":
  version: 4.1.1
  resolution: "lodash.kebabcase@npm:4.1.1"
  checksum: 5a6c59161914e1bae23438a298c7433e83d935e0f59853fa862e691164696bc07f6dfa4c313d499fbf41ba8d53314e9850416502376705a357d24ee6ca33af78
  languageName: node
  linkType: hard

"lodash.mapvalues@npm:^4.6.0":
  version: 4.6.0
  resolution: "lodash.mapvalues@npm:4.6.0"
  checksum: 0ff1b252fda318fc36e47c296984925e98fbb0fc5a2ecc4ef458f3c739a9476d47e40c95ac653e8314d132aa59c746d4276527b99d6e271940555c6e12d2babd
  languageName: node
  linkType: hard

"lodash.memoize@npm:^4.1.2":
  version: 4.1.2
  resolution: "lodash.memoize@npm:4.1.2"
  checksum: 9ff3942feeccffa4f1fafa88d32f0d24fdc62fd15ded5a74a5f950ff5f0c6f61916157246744c620173dddf38d37095a92327d5fd3861e2063e736a5c207d089
  languageName: node
  linkType: hard

"lodash.padend@npm:4.6.1":
  version: 4.6.1
  resolution: "lodash.padend@npm:4.6.1"
  checksum: c2e6e789debf83b98f5c085305cdcfff1067e7a31bda2a110fd765d3c11a99edfbeef570d9ef737ab3212006bdb8114e77622e518c18c1fce52b8fdfd9dab685
  languageName: node
  linkType: hard

"lodash.sortby@npm:^4.7.0":
  version: 4.7.0
  resolution: "lodash.sortby@npm:4.7.0"
  checksum: db170c9396d29d11fe9a9f25668c4993e0c1331bcb941ddbd48fb76f492e732add7f2a47cfdf8e9d740fa59ac41bbfaf931d268bc72aab3ab49e9f89354d718c
  languageName: node
  linkType: hard

"lodash.transform@npm:^4.6.0":
  version: 4.6.0
  resolution: "lodash.transform@npm:4.6.0"
  checksum: f9d0f583409212e4e94c08c0de1c9e71679e26658d2645be16ee6db55ee2572db5a8395c76f471c00c7d18f3a86c781f7ac51238a7cfa29e9cca253aa0b97149
  languageName: node
  linkType: hard

"lodash.uniq@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.uniq@npm:4.5.0"
  checksum: a4779b57a8d0f3c441af13d9afe7ecff22dd1b8ce1129849f71d9bbc8e8ee4e46dfb4b7c28f7ad3d67481edd6e51126e4e2a6ee276e25906d10f7140187c392d
  languageName: node
  linkType: hard

"lodash@npm:4.17.11":
  version: 4.17.11
  resolution: "lodash@npm:4.17.11"
  checksum: 0611590f43371f1dc93f8ad6d73feab7e301417ff7bbd4f7af4421def41d7b3210230539ae952884a0c7b944ba3266dd2723c02b79f36a7906e8d9e2e280a498
  languageName: node
  linkType: hard

"lodash@npm:^4.17.10, lodash@npm:^4.17.11, lodash@npm:^4.17.12, lodash@npm:^4.17.14, lodash@npm:^4.17.15, lodash@npm:^4.17.19, lodash@npm:^4.17.20, lodash@npm:^4.17.21, lodash@npm:^4.17.3, lodash@npm:^4.17.4, lodash@npm:^4.3.0":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"log-symbols@npm:^2.2.0":
  version: 2.2.0
  resolution: "log-symbols@npm:2.2.0"
  dependencies:
    chalk: ^2.0.1
  checksum: 4c95e3b65f0352dbe91dc4989c10baf7a44e2ef5b0db7e6721e1476268e2b6f7090c3aa880d4f833a05c5c3ff18f4ec5215a09bd0099986d64a8186cfeb48ac8
  languageName: node
  linkType: hard

"loglevel@npm:^1.6.8":
  version: 1.8.0
  resolution: "loglevel@npm:1.8.0"
  checksum: 41aeea17de24aba8dba68084a31fe9189648bce4f39c1277e021bb276c3c53a75b0d337395919cf271068ad40ecefabad0e4fdeb4a8f11908beee532b898f4a7
  languageName: node
  linkType: hard

"loose-envify@npm:^1.0.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lower-case@npm:^1.1.1":
  version: 1.1.4
  resolution: "lower-case@npm:1.1.4"
  checksum: 1ca9393b5eaef94a64e3f89e38b63d15bc7182a91171e6ad1550f51d710ec941540a065b274188f2e6b4576110cc2d11b50bc4bb7c603a040ddeb1db4ca95197
  languageName: node
  linkType: hard

"lru-cache@npm:^4.0.1, lru-cache@npm:^4.1.1, lru-cache@npm:^4.1.2, lru-cache@npm:^4.1.5":
  version: 4.1.5
  resolution: "lru-cache@npm:4.1.5"
  dependencies:
    pseudomap: ^1.0.2
    yallist: ^2.1.2
  checksum: 4bb4b58a36cd7dc4dcec74cbe6a8f766a38b7426f1ff59d4cf7d82a2aa9b9565cd1cb98f6ff60ce5cd174524868d7bc9b7b1c294371851356066ca9ac4cf135a
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: ^3.0.2
  checksum: c154ae1cbb0c2206d1501a0e94df349653c92c8cbb25236d7e85190bcaf4567a03ac6eb43166fabfa36fd35623694da7233e88d9601fbf411a9a481d85dbd2cb
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: ^4.0.0
  checksum: f97f499f898f23e4585742138a22f22526254fdba6d75d41a1c2526b3b6cc5747ef59c5612ba7375f42aca4f8461950e925ba08c991ead0651b4918b7c978297
  languageName: node
  linkType: hard

"make-dir@npm:^1.0.0":
  version: 1.3.0
  resolution: "make-dir@npm:1.3.0"
  dependencies:
    pify: ^3.0.0
  checksum: c564f6e7bb5ace1c02ad56b3a5f5e07d074af0c0b693c55c7b2c2b148882827c8c2afc7b57e43338a9f90c125b58d604e8cf3e6990a48bf949dfea8c79668c0b
  languageName: node
  linkType: hard

"make-dir@npm:^2.0.0":
  version: 2.1.0
  resolution: "make-dir@npm:2.1.0"
  dependencies:
    pify: ^4.0.1
    semver: ^5.6.0
  checksum: 043548886bfaf1820323c6a2997e6d2fa51ccc2586ac14e6f14634f7458b4db2daf15f8c310e2a0abd3e0cddc64df1890d8fc7263033602c47bb12cbfcf86aab
  languageName: node
  linkType: hard

"make-dir@npm:^3.0.2, make-dir@npm:^3.1.0":
  version: 3.1.0
  resolution: "make-dir@npm:3.1.0"
  dependencies:
    semver: ^6.0.0
  checksum: 484200020ab5a1fdf12f393fe5f385fc8e4378824c940fba1729dcd198ae4ff24867bc7a5646331e50cead8abff5d9270c456314386e629acec6dff4b8016b78
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^9.1.0":
  version: 9.1.0
  resolution: "make-fetch-happen@npm:9.1.0"
  dependencies:
    agentkeepalive: ^4.1.3
    cacache: ^15.2.0
    http-cache-semantics: ^4.1.0
    http-proxy-agent: ^4.0.1
    https-proxy-agent: ^5.0.0
    is-lambda: ^1.0.1
    lru-cache: ^6.0.0
    minipass: ^3.1.3
    minipass-collect: ^1.0.2
    minipass-fetch: ^1.3.2
    minipass-flush: ^1.0.5
    minipass-pipeline: ^1.2.4
    negotiator: ^0.6.2
    promise-retry: ^2.0.1
    socks-proxy-agent: ^6.0.0
    ssri: ^8.0.0
  checksum: 0eb371c85fdd0b1584fcfdf3dc3c62395761b3c14658be02620c310305a9a7ecf1617a5e6fb30c1d081c5c8aaf177fa133ee225024313afabb7aa6a10f1e3d04
  languageName: node
  linkType: hard

"makeerror@npm:1.0.12":
  version: 1.0.12
  resolution: "makeerror@npm:1.0.12"
  dependencies:
    tmpl: 1.0.5
  checksum: b38a025a12c8146d6eeea5a7f2bf27d51d8ad6064da8ca9405fcf7bf9b54acd43e3b30ddd7abb9b1bfa4ddb266019133313482570ddb207de568f71ecfcf6060
  languageName: node
  linkType: hard

"map-age-cleaner@npm:^0.1.1":
  version: 0.1.3
  resolution: "map-age-cleaner@npm:0.1.3"
  dependencies:
    p-defer: ^1.0.0
  checksum: cb2804a5bcb3cbdfe4b59066ea6d19f5e7c8c196cd55795ea4c28f792b192e4c442426ae52524e5e1acbccf393d3bddacefc3d41f803e66453f6c4eda3650bc1
  languageName: node
  linkType: hard

"map-cache@npm:^0.2.2":
  version: 0.2.2
  resolution: "map-cache@npm:0.2.2"
  checksum: 3067cea54285c43848bb4539f978a15dedc63c03022abeec6ef05c8cb6829f920f13b94bcaf04142fc6a088318e564c4785704072910d120d55dbc2e0c421969
  languageName: node
  linkType: hard

"map-visit@npm:^1.0.0":
  version: 1.0.0
  resolution: "map-visit@npm:1.0.0"
  dependencies:
    object-visit: ^1.0.0
  checksum: c27045a5021c344fc19b9132eb30313e441863b2951029f8f8b66f79d3d8c1e7e5091578075a996f74e417479506fe9ede28c44ca7bc351a61c9d8073daec36a
  languageName: node
  linkType: hard

"math-random@npm:^1.0.1":
  version: 1.0.4
  resolution: "math-random@npm:1.0.4"
  checksum: 9edf31ea337bba21994eb968218fd571d55fce86b51661158d8e241886b73121d9e1a35a5bb8997dba8ce67417a83c8dbd0811917248f886840035b7f1c667b9
  languageName: node
  linkType: hard

"md5.js@npm:^1.3.4":
  version: 1.3.5
  resolution: "md5.js@npm:1.3.5"
  dependencies:
    hash-base: ^3.0.0
    inherits: ^2.0.1
    safe-buffer: ^5.1.2
  checksum: 098494d885684bcc4f92294b18ba61b7bd353c23147fbc4688c75b45cb8590f5a95fd4584d742415dcc52487f7a1ef6ea611cfa1543b0dc4492fe026357f3f0c
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.14":
  version: 2.0.14
  resolution: "mdn-data@npm:2.0.14"
  checksum: 9d0128ed425a89f4cba8f787dca27ad9408b5cb1b220af2d938e2a0629d17d879a34d2cb19318bdb26c3f14c77dd5dfbae67211f5caaf07b61b1f2c5c8c7dc16
  languageName: node
  linkType: hard

"mdn-data@npm:2.0.4":
  version: 2.0.4
  resolution: "mdn-data@npm:2.0.4"
  checksum: add3c95e6d03d301b8a8bcfee3de33f4d07e4c5eee5b79f18d6d737de717e22472deadf67c1a8563983c0b603e10d7df40aa8e5fddf18884dfe118ccec7ae329
  languageName: node
  linkType: hard

"mdn-data@npm:~1.1.0":
  version: 1.1.4
  resolution: "mdn-data@npm:1.1.4"
  checksum: 146dbea4c8bd68547f6ffec22868f099f82cead2a7a55eb70f80cf1a4958e3504c2d9bf17f3f0675f76f2b5a396b4ef2a5e9998af6c070625e9650771101c139
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: af1b38516c28ec95d6b0826f6c8f276c58aec391f76be42aa07646b4e39d317723e869700933ca6995b056db4b09a78c92d5440dc23657e6764be5d28874bba1
  languageName: node
  linkType: hard

"mem@npm:^4.0.0":
  version: 4.3.0
  resolution: "mem@npm:4.3.0"
  dependencies:
    map-age-cleaner: ^0.1.1
    mimic-fn: ^2.0.0
    p-is-promise: ^2.0.0
  checksum: cf488608e5d59c6cb68004b70de317222d4be9f857fd535dfa6a108e04f40821479c080bc763c417b1030569d303538c59d441280078cfce07fefd1c523f98ef
  languageName: node
  linkType: hard

"memory-fs@npm:^0.4.1, memory-fs@npm:~0.4.1":
  version: 0.4.1
  resolution: "memory-fs@npm:0.4.1"
  dependencies:
    errno: ^0.1.3
    readable-stream: ^2.0.1
  checksum: 6db6c8682eff836664ca9b5b6052ae38d21713dda9d0ef4700fa5c0599a8bc16b2093bee75ac3dedbe59fb2222d368f25bafaa62ba143c41051359cbcb005044
  languageName: node
  linkType: hard

"memory-fs@npm:^0.5.0":
  version: 0.5.0
  resolution: "memory-fs@npm:0.5.0"
  dependencies:
    errno: ^0.1.3
    readable-stream: ^2.0.1
  checksum: a9f25b0a8ecfb7324277393f19ef68e6ba53b9e6e4b526bbf2ba23055c5440fbf61acc7bf66bfd980e9eb4951a4790f6f777a9a3abd36603f22c87e8a64d3d6b
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.1":
  version: 1.0.1
  resolution: "merge-descriptors@npm:1.0.1"
  checksum: 5abc259d2ae25bb06d19ce2b94a21632583c74e2a9109ee1ba7fd147aa7362b380d971e0251069f8b3eb7d48c21ac839e21fa177b335e82c76ec172e30c31a26
  languageName: node
  linkType: hard

"merge-options@npm:1.0.1":
  version: 1.0.1
  resolution: "merge-options@npm:1.0.1"
  dependencies:
    is-plain-obj: ^1.1
  checksum: 7e3d5d658879038cdc225107205dacd68fd8e22cf4f54fb37fd9e0687f7eb9efd7f0f2163577675325a3a72c9df0566e23911d0d8a2448ca8f83eee5199dd990
  languageName: node
  linkType: hard

"merge-source-map@npm:^1.1.0":
  version: 1.1.0
  resolution: "merge-source-map@npm:1.1.0"
  dependencies:
    source-map: ^0.6.1
  checksum: 945a83dcc59eff77dde709be1d3d6cb575c11cd7164a7ccdc1c6f0d463aad7c12750a510bdf84af2c05fac4615c4305d97ac90477975348bb901a905c8e92c4b
  languageName: node
  linkType: hard

"merge-stream@npm:^1.0.1":
  version: 1.0.1
  resolution: "merge-stream@npm:1.0.1"
  dependencies:
    readable-stream: ^2.0.1
  checksum: 3be7887dffd8899da0f930c0f85812ab8993252f467dcd61e60a8d085836ebbb23756b8e481a7f71824206342afe1b1a2b80c05a1cd0ed0e792a09c5812a9082
  languageName: node
  linkType: hard

"merge2@npm:^1.2.3":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 7268db63ed5169466540b6fb947aec313200bcf6d40c5ab722c22e242f651994619bcd85601602972d3c85bd2cc45a358a4c61937e9f11a061919a1da569b0c2
  languageName: node
  linkType: hard

"merge@npm:^1.2.0":
  version: 1.2.1
  resolution: "merge@npm:1.2.1"
  checksum: 2298c4fdcf64561f320b92338681f7ffcafafb579a6e294066ae3e7bd10ae25df363903d2f028072733b9f79a1f75d2b999aef98ad5d73de13641da39cda0913
  languageName: node
  linkType: hard

"methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 0917ff4041fa8e2f2fda5425a955fe16ca411591fbd123c0d722fcf02b73971ed6f764d85f0a6f547ce49ee0221ce2c19a5fa692157931cecb422984f1dcd13a
  languageName: node
  linkType: hard

"microargs@npm:1.1.2":
  version: 1.1.2
  resolution: "microargs@npm:1.1.2"
  checksum: a47cc868f8ea591dec09e9229729a001d18d6a0852fb196d62806e085f41f909c52020d06e7a967d121674939f02547f4ae4516ea0fcb9da06e31be3f89a06b3
  languageName: node
  linkType: hard

"microcli@npm:1.3.3":
  version: 1.3.3
  resolution: "microcli@npm:1.3.3"
  dependencies:
    lodash: 4.17.11
    microargs: 1.1.2
  checksum: 01e40e7062f3a1fb4fbf2b688a66a1e1061e2fbbcd10ac9b5d1c83cdd8eee6cd9d1fcfadaa6cf0fed8f7ed03c1a70ef9f3423392aa4cbf136928af7fa349b34a
  languageName: node
  linkType: hard

"micromatch@npm:3.1.0":
  version: 3.1.0
  resolution: "micromatch@npm:3.1.0"
  dependencies:
    arr-diff: ^4.0.0
    array-unique: ^0.3.2
    braces: ^2.2.2
    define-property: ^1.0.0
    extend-shallow: ^2.0.1
    extglob: ^2.0.2
    fragment-cache: ^0.2.1
    kind-of: ^5.0.2
    nanomatch: ^1.2.1
    object.pick: ^1.3.0
    regex-not: ^1.0.0
    snapdragon: ^0.8.1
    to-regex: ^3.0.1
  checksum: 4c28b7c9e49a510f62ced8ec70dde03871931bfdae8a594762404dddd7666f3acdf1d14cadddda609d8114648a702738a0f9672a31ac4e0f4896b9e4962c6bd6
  languageName: node
  linkType: hard

"micromatch@npm:^2.3.11":
  version: 2.3.11
  resolution: "micromatch@npm:2.3.11"
  dependencies:
    arr-diff: ^2.0.0
    array-unique: ^0.2.1
    braces: ^1.8.2
    expand-brackets: ^0.1.4
    extglob: ^0.3.1
    filename-regex: ^2.0.0
    is-extglob: ^1.0.0
    is-glob: ^2.0.1
    kind-of: ^3.0.2
    normalize-path: ^2.0.1
    object.omit: ^2.0.0
    parse-glob: ^3.0.4
    regex-cache: ^0.4.2
  checksum: 562681808a3149c77ba90947cb8cf1874ea6d07da6fa86416a4f9454f847fb6329aea5234e1af060d9465d9bb14eaaf4bc6c482bf24bc561649042f2b81d3092
  languageName: node
  linkType: hard

"micromatch@npm:^3.1.10, micromatch@npm:^3.1.4, micromatch@npm:^3.1.8":
  version: 3.1.10
  resolution: "micromatch@npm:3.1.10"
  dependencies:
    arr-diff: ^4.0.0
    array-unique: ^0.3.2
    braces: ^2.3.1
    define-property: ^2.0.2
    extend-shallow: ^3.0.2
    extglob: ^2.0.4
    fragment-cache: ^0.2.1
    kind-of: ^6.0.2
    nanomatch: ^1.2.9
    object.pick: ^1.3.0
    regex-not: ^1.0.0
    snapdragon: ^0.8.1
    to-regex: ^3.0.2
  checksum: ad226cba4daa95b4eaf47b2ca331c8d2e038d7b41ae7ed0697cde27f3f1d6142881ab03d4da51b65d9d315eceb5e4cdddb3fbb55f5f72cfa19cf3ea469d054dc
  languageName: node
  linkType: hard

"miller-rabin@npm:^4.0.0":
  version: 4.0.1
  resolution: "miller-rabin@npm:4.0.1"
  dependencies:
    bn.js: ^4.0.0
    brorand: ^1.0.1
  bin:
    miller-rabin: bin/miller-rabin
  checksum: 00cd1ab838ac49b03f236cc32a14d29d7d28637a53096bf5c6246a032a37749c9bd9ce7360cbf55b41b89b7d649824949ff12bc8eee29ac77c6b38eada619ece
  languageName: node
  linkType: hard

"mime-db@npm:1.51.0, mime-db@npm:>= 1.43.0 < 2":
  version: 1.51.0
  resolution: "mime-db@npm:1.51.0"
  checksum: 613b1ac9d6e725cc24444600b124a7f1ce6c60b1baa654f39a3e260d0995a6dffc5693190217e271af7e2a5612dae19f2a73f3e316707d797a7391165f7ef423
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:~2.1.17, mime-types@npm:~2.1.19, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.34
  resolution: "mime-types@npm:2.1.34"
  dependencies:
    mime-db: 1.51.0
  checksum: 67013de9e9d6799bde6d669d18785b7e18bcd212e710d3e04a4727f92f67a8ad4e74aee24be28b685adb794944814bde649119b58ee3282ffdbee58f9278d9f3
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: fef25e39263e6d207580bdc629f8872a3f9772c923c7f8c7e793175cee22777bbe8bba95e5d509a40aaa292d8974514ce634ae35769faa45f22d17edda5e8557
  languageName: node
  linkType: hard

"mime@npm:^2.0.3, mime@npm:^2.4.4":
  version: 2.6.0
  resolution: "mime@npm:2.6.0"
  bin:
    mime: cli.js
  checksum: 1497ba7b9f6960694268a557eae24b743fd2923da46ec392b042469f4b901721ba0adcf8b0d3c2677839d0e243b209d76e5edcbd09cfdeffa2dfb6bb4df4b862
  languageName: node
  linkType: hard

"mimic-fn@npm:^1.0.0":
  version: 1.2.0
  resolution: "mimic-fn@npm:1.2.0"
  checksum: 69c08205156a1f4906d9c46f9b4dc08d18a50176352e77fdeb645cedfe9f20c0b19865d465bd2dec27a5c432347f24dc07fc3695e11159d193f892834233e939
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.0.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: d2421a3444848ce7f84bd49115ddacff29c15745db73f54041edc906c14b131a38d05298dae3081667627a59b2eb1ca4b436ff2e1b80f69679522410418b478a
  languageName: node
  linkType: hard

"mini-css-extract-plugin@npm:^0.6.0":
  version: 0.6.0
  resolution: "mini-css-extract-plugin@npm:0.6.0"
  dependencies:
    loader-utils: ^1.1.0
    normalize-url: ^2.0.1
    schema-utils: ^1.0.0
    webpack-sources: ^1.1.0
  peerDependencies:
    webpack: ^4.4.0
  checksum: 6703d4de8c788c467dea1baacaa8843ddfd3e6895912635f01d568d2f60017df631badd3a757089ab5c4cdac97c60cc7167a6e941b2c36f6f634d00254ea6d6a
  languageName: node
  linkType: hard

"minimalistic-assert@npm:^1.0.0, minimalistic-assert@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-assert@npm:1.0.1"
  checksum: cc7974a9268fbf130fb055aff76700d7e2d8be5f761fb5c60318d0ed010d839ab3661a533ad29a5d37653133385204c503bfac995aaa4236f4e847461ea32ba7
  languageName: node
  linkType: hard

"minimalistic-crypto-utils@npm:^1.0.1":
  version: 1.0.1
  resolution: "minimalistic-crypto-utils@npm:1.0.1"
  checksum: 6e8a0422b30039406efd4c440829ea8f988845db02a3299f372fceba56ffa94994a9c0f2fd70c17f9969eedfbd72f34b5070ead9656a34d3f71c0bd72583a0ed
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.2, minimatch@npm:^3.0.3, minimatch@npm:^3.0.4":
  version: 3.0.5
  resolution: "minimatch@npm:3.0.5"
  dependencies:
    brace-expansion: ^1.1.7
  checksum: a3b84b426eafca947741b864502cee02860c4e7b145de11ad98775cfcf3066fef422583bc0ffce0952ddf4750c1ccf4220b1556430d4ce10139f66247d87d69e
  languageName: node
  linkType: hard

"minimist@npm:^1.1.1, minimist@npm:^1.2.0, minimist@npm:^1.2.5":
  version: 1.2.5
  resolution: "minimist@npm:1.2.5"
  checksum: 86706ce5b36c16bfc35c5fe3dbb01d5acdc9a22f2b6cc810b6680656a1d2c0e44a0159c9a3ba51fb072bb5c203e49e10b51dcd0eec39c481f4c42086719bae52
  languageName: node
  linkType: hard

"minipass-collect@npm:^1.0.2":
  version: 1.0.2
  resolution: "minipass-collect@npm:1.0.2"
  dependencies:
    minipass: ^3.0.0
  checksum: 14df761028f3e47293aee72888f2657695ec66bd7d09cae7ad558da30415fdc4752bbfee66287dcc6fd5e6a2fa3466d6c484dc1cbd986525d9393b9523d97f10
  languageName: node
  linkType: hard

"minipass-fetch@npm:^1.3.2":
  version: 1.4.1
  resolution: "minipass-fetch@npm:1.4.1"
  dependencies:
    encoding: ^0.1.12
    minipass: ^3.1.0
    minipass-sized: ^1.0.3
    minizlib: ^2.0.0
  dependenciesMeta:
    encoding:
      optional: true
  checksum: ec93697bdb62129c4e6c0104138e681e30efef8c15d9429dd172f776f83898471bc76521b539ff913248cc2aa6d2b37b652c993504a51cc53282563640f29216
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: ^3.0.0
  checksum: 56269a0b22bad756a08a94b1ffc36b7c9c5de0735a4dd1ab2b06c066d795cfd1f0ac44a0fcae13eece5589b908ecddc867f04c745c7009be0b566421ea0944cf
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.2, minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: ^3.0.0
  checksum: b14240dac0d29823c3d5911c286069e36d0b81173d7bdf07a7e4a91ecdef92cdff4baaf31ea3746f1c61e0957f652e641223970870e2353593f382112257971b
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: ^3.0.0
  checksum: 79076749fcacf21b5d16dd596d32c3b6bf4d6e62abb43868fac21674078505c8b15eaca4e47ed844985a4514854f917d78f588fcd029693709417d8f98b2bd60
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0, minipass@npm:^3.1.0, minipass@npm:^3.1.1, minipass@npm:^3.1.3":
  version: 3.1.6
  resolution: "minipass@npm:3.1.6"
  dependencies:
    yallist: ^4.0.0
  checksum: 57a04041413a3531a65062452cb5175f93383ef245d6f4a2961d34386eb9aa8ac11ac7f16f791f5e8bbaf1dfb1ef01596870c88e8822215db57aa591a5bb0a77
  languageName: node
  linkType: hard

"minizlib@npm:^2.0.0, minizlib@npm:^2.1.1":
  version: 2.1.2
  resolution: "minizlib@npm:2.1.2"
  dependencies:
    minipass: ^3.0.0
    yallist: ^4.0.0
  checksum: f1fdeac0b07cf8f30fcf12f4b586795b97be856edea22b5e9072707be51fc95d41487faec3f265b42973a304fe3a64acd91a44a3826a963e37b37bafde0212c3
  languageName: node
  linkType: hard

"mississippi@npm:^2.0.0":
  version: 2.0.0
  resolution: "mississippi@npm:2.0.0"
  dependencies:
    concat-stream: ^1.5.0
    duplexify: ^3.4.2
    end-of-stream: ^1.1.0
    flush-write-stream: ^1.0.0
    from2: ^2.1.0
    parallel-transform: ^1.1.0
    pump: ^2.0.1
    pumpify: ^1.3.3
    stream-each: ^1.1.0
    through2: ^2.0.0
  checksum: 441540c44c67bdf4fbb49ab9904b782ed2038a28f8ca91fa2e837d5cdf55a1f141364ea82c7913e881f0a09868619c64e755e4057b1a6b34bbd896aa9d88274a
  languageName: node
  linkType: hard

"mississippi@npm:^3.0.0":
  version: 3.0.0
  resolution: "mississippi@npm:3.0.0"
  dependencies:
    concat-stream: ^1.5.0
    duplexify: ^3.4.2
    end-of-stream: ^1.1.0
    flush-write-stream: ^1.0.0
    from2: ^2.1.0
    parallel-transform: ^1.1.0
    pump: ^3.0.0
    pumpify: ^1.3.3
    stream-each: ^1.1.0
    through2: ^2.0.0
  checksum: 84b3d9889621d293f9a596bafe60df863b330c88fc19215ced8f603c605fc7e1bf06f8e036edf301bd630a03fd5d9d7d23d5d6b9a4802c30ca864d800f0bd9f8
  languageName: node
  linkType: hard

"mitt@npm:1.1.2":
  version: 1.1.2
  resolution: "mitt@npm:1.1.2"
  checksum: 53cd01c9db1eadb87755df49ed8078103665174299927b139ead3038943b3046494da02938ee5737e768e19a7026b0c19753609c6c7bb5b6ce9d08bb38bd7c31
  languageName: node
  linkType: hard

"mixin-deep@npm:^1.2.0":
  version: 1.3.2
  resolution: "mixin-deep@npm:1.3.2"
  dependencies:
    for-in: ^1.0.2
    is-extendable: ^1.0.1
  checksum: 820d5a51fcb7479f2926b97f2c3bb223546bc915e6b3a3eb5d906dda871bba569863595424a76682f2b15718252954644f3891437cb7e3f220949bed54b1750d
  languageName: node
  linkType: hard

"mkdirp@npm:^0.5.1, mkdirp@npm:^0.5.3, mkdirp@npm:^0.5.5, mkdirp@npm:~0.5.0, mkdirp@npm:~0.5.1":
  version: 0.5.5
  resolution: "mkdirp@npm:0.5.5"
  dependencies:
    minimist: ^1.2.5
  bin:
    mkdirp: bin/cmd.js
  checksum: 3bce20ea525f9477befe458ab85284b0b66c8dc3812f94155af07c827175948cdd8114852ac6c6d82009b13c1048c37f6d98743eb019651ee25c39acc8aabe7d
  languageName: node
  linkType: hard

"mkdirp@npm:^1.0.3, mkdirp@npm:^1.0.4":
  version: 1.0.4
  resolution: "mkdirp@npm:1.0.4"
  bin:
    mkdirp: bin/cmd.js
  checksum: a96865108c6c3b1b8e1d5e9f11843de1e077e57737602de1b82030815f311be11f96f09cce59bd5b903d0b29834733e5313f9301e3ed6d6f6fba2eae0df4298f
  languageName: node
  linkType: hard

"mockjs@npm:1.0.1-beta3":
  version: 1.0.1-beta3
  resolution: "mockjs@npm:1.0.1-beta3"
  dependencies:
    commander: "*"
  bin:
    random: bin/random
  checksum: b507b7b07c0763ace213feb3a0fc6c0c15f0dd655ff96f2f449e1f80d11e6d11f8e05883a735da69d5cc41c1483d8807ed609e6eb10d69ecf8cc80dbea1bed5f
  languageName: node
  linkType: hard

"move-concurrently@npm:^1.0.1":
  version: 1.0.1
  resolution: "move-concurrently@npm:1.0.1"
  dependencies:
    aproba: ^1.1.1
    copy-concurrently: ^1.0.0
    fs-write-stream-atomic: ^1.0.8
    mkdirp: ^0.5.1
    rimraf: ^2.5.4
    run-queue: ^1.0.3
  checksum: 4ea3296c150b09e798177847f673eb5783f8ca417ba806668d2c631739f653e1a735f19fb9b6e2f5e25ee2e4c0a6224732237a8e4f84c764e99d7462d258209e
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 0e6a22b8b746d2e0b65a430519934fefd41b6db0682e3477c10f60c76e947c4c0ad06f63ffdf1d78d335f83edee8c0aa928aa66a36c7cd95b69b26f468d527f4
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 673cdb2c3133eb050c745908d8ce632ed2c02d85640e2edb3ace856a2266a813b30c613569bf3354fdf4ea7d1a1494add3bfa95e2713baa27d0c2c71fc44f58f
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.0.0, ms@npm:^2.1.1":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: aa92de608021b242401676e35cfa5aa42dd70cbdc082b916da7fb925c542173e36bce97ea3e804923fe92c0ad991434e4a38327e15a1b5b5f945d66df615ae6d
  languageName: node
  linkType: hard

"multicast-dns-service-types@npm:^1.1.0":
  version: 1.1.0
  resolution: "multicast-dns-service-types@npm:1.1.0"
  checksum: 0979fca1cce85484d256e4db3af591d941b41a61f134da3607213d2624c12ed5b8a246565cb19a9b3cb542819e8fbc71a90b07e77023ee6a9515540fe1d371f7
  languageName: node
  linkType: hard

"multicast-dns@npm:^6.0.1":
  version: 6.2.3
  resolution: "multicast-dns@npm:6.2.3"
  dependencies:
    dns-packet: ^1.3.1
    thunky: ^1.0.2
  bin:
    multicast-dns: cli.js
  checksum: f515b49ca964429ab48a4ac8041fcf969c927aeb49ab65288bd982e52c849a870fc3b03565780b0d194a1a02da8821f28b6425e48e95b8107bc9fcc92f571a6f
  languageName: node
  linkType: hard

"mute-stream@npm:0.0.7":
  version: 0.0.7
  resolution: "mute-stream@npm:0.0.7"
  checksum: a9d4772c1c84206aa37c218ed4751cd060239bf1d678893124f51e037f6f22f4a159b2918c030236c93252638a74beb29c9b1fd3267c9f24d4b3253cf1eaa86f
  languageName: node
  linkType: hard

"nan@npm:^2.12.1":
  version: 2.15.0
  resolution: "nan@npm:2.15.0"
  dependencies:
    node-gyp: latest
  checksum: 33e1bb4dfca447fe37d4bb5889be55de154828632c8d38646db67293a21afd61ed9909cdf1b886214a64707d935926c4e60e2b09de9edfc2ad58de31d6ce8f39
  languageName: node
  linkType: hard

"nanomatch@npm:^1.2.1, nanomatch@npm:^1.2.9":
  version: 1.2.13
  resolution: "nanomatch@npm:1.2.13"
  dependencies:
    arr-diff: ^4.0.0
    array-unique: ^0.3.2
    define-property: ^2.0.2
    extend-shallow: ^3.0.2
    fragment-cache: ^0.2.1
    is-windows: ^1.0.2
    kind-of: ^6.0.2
    object.pick: ^1.3.0
    regex-not: ^1.0.0
    snapdragon: ^0.8.1
    to-regex: ^3.0.1
  checksum: 54d4166d6ef08db41252eb4e96d4109ebcb8029f0374f9db873bd91a1f896c32ec780d2a2ea65c0b2d7caf1f28d5e1ea33746a470f32146ac8bba821d80d38d8
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 23ad088b08f898fc9b53011d7bb78ec48e79de7627e01ab5518e806033861bef68d5b0cd0e2205c2f36690ac9571ff6bcb05eb777ced2eeda8d4ac5b44592c3d
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3, negotiator@npm:^0.6.2":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: b8ffeb1e262eff7968fc90a2b6767b04cfd9842582a9d0ece0af7049537266e7b2506dfb1d107a32f06dd849ab2aea834d5830f7f4d0e5cb7d36e1ae55d021d9
  languageName: node
  linkType: hard

"neo-async@npm:^2.5.0, neo-async@npm:^2.6.0, neo-async@npm:^2.6.1":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: deac9f8d00eda7b2e5cd1b2549e26e10a0faa70adaa6fdadca701cc55f49ee9018e427f424bac0c790b7c7e2d3068db97f3093f1093975f2acb8f8818b936ed9
  languageName: node
  linkType: hard

"nice-try@npm:^1.0.4":
  version: 1.0.5
  resolution: "nice-try@npm:1.0.5"
  checksum: 0b4af3b5bb5d86c289f7a026303d192a7eb4417231fe47245c460baeabae7277bcd8fd9c728fb6bd62c30b3e15cd6620373e2cf33353b095d8b403d3e8a15aff
  languageName: node
  linkType: hard

"no-case@npm:^2.2.0":
  version: 2.3.2
  resolution: "no-case@npm:2.3.2"
  dependencies:
    lower-case: ^1.1.1
  checksum: 856487731936fef44377ca74fdc5076464aba2e0734b56a4aa2b2a23d5b154806b591b9b2465faa59bb982e2b5c9391e3685400957fb4eeb38f480525adcf3dd
  languageName: node
  linkType: hard

"node-addon-api@npm:^1.7.1":
  version: 1.7.2
  resolution: "node-addon-api@npm:1.7.2"
  dependencies:
    node-gyp: latest
  checksum: 938922b3d7cb34ee137c5ec39df6289a3965e8cab9061c6848863324c21a778a81ae3bc955554c56b6b86962f6ccab2043dd5fa3f33deab633636bd28039333f
  languageName: node
  linkType: hard

"node-cache@npm:^4.1.1":
  version: 4.2.1
  resolution: "node-cache@npm:4.2.1"
  dependencies:
    clone: 2.x
    lodash: ^4.17.15
  checksum: 9dbce4452e075e0d75dee1a2bfd6d8c1d21c0c5fb436e1691378d9abfd463151d9a592812a1fae07755ee9cc20d1eff659e21dd762bb567a0d941acd0b202595
  languageName: node
  linkType: hard

"node-forge@npm:^0.10.0":
  version: 0.10.0
  resolution: "node-forge@npm:0.10.0"
  checksum: 5aa6dc9922e424a20ef101d2f517418e2bc9cfc0255dd22e0701c0fad1568445f510ee67f6f3fcdf085812c4ca1b847b8ba45683b34776828e41f5c1794e42e1
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 8.4.1
  resolution: "node-gyp@npm:8.4.1"
  dependencies:
    env-paths: ^2.2.0
    glob: ^7.1.4
    graceful-fs: ^4.2.6
    make-fetch-happen: ^9.1.0
    nopt: ^5.0.0
    npmlog: ^6.0.0
    rimraf: ^3.0.2
    semver: ^7.3.5
    tar: ^6.1.2
    which: ^2.0.2
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 341710b5da39d3660e6a886b37e210d33f8282047405c2e62c277bcc744c7552c5b8b972ebc3a7d5c2813794e60cc48c3ebd142c46d6e0321db4db6c92dd0355
  languageName: node
  linkType: hard

"node-int64@npm:^0.4.0":
  version: 0.4.0
  resolution: "node-int64@npm:0.4.0"
  checksum: d0b30b1ee6d961851c60d5eaa745d30b5c95d94bc0e74b81e5292f7c42a49e3af87f1eb9e89f59456f80645d679202537de751b7d72e9e40ceea40c5e449057e
  languageName: node
  linkType: hard

"node-ipc@npm:^9.1.1":
  version: 9.2.1
  resolution: "node-ipc@npm:9.2.1"
  dependencies:
    event-pubsub: 4.3.0
    js-message: 1.0.7
    js-queue: 2.0.2
  checksum: a38aa4c8ca4317b293e0ce21f0a3a4941fc51c054800b35e263fcfe3e0feeb60e7d2c497f015054b28783316c6e7d9cc3837af9d9958bcbd8c577d0cdf6964b7
  languageName: node
  linkType: hard

"node-libs-browser@npm:^2.0.0, node-libs-browser@npm:^2.2.1":
  version: 2.2.1
  resolution: "node-libs-browser@npm:2.2.1"
  dependencies:
    assert: ^1.1.1
    browserify-zlib: ^0.2.0
    buffer: ^4.3.0
    console-browserify: ^1.1.0
    constants-browserify: ^1.0.0
    crypto-browserify: ^3.11.0
    domain-browser: ^1.1.1
    events: ^3.0.0
    https-browserify: ^1.0.0
    os-browserify: ^0.3.0
    path-browserify: 0.0.1
    process: ^0.11.10
    punycode: ^1.2.4
    querystring-es3: ^0.2.0
    readable-stream: ^2.3.3
    stream-browserify: ^2.0.1
    stream-http: ^2.7.2
    string_decoder: ^1.0.0
    timers-browserify: ^2.0.4
    tty-browserify: 0.0.0
    url: ^0.11.0
    util: ^0.11.0
    vm-browserify: ^1.0.1
  checksum: 41fa7927378edc0cb98a8cc784d3f4a47e43378d3b42ec57a23f81125baa7287c4b54d6d26d062072226160a3ce4d8b7a62e873d2fb637aceaddf71f5a26eca0
  languageName: node
  linkType: hard

"node-notifier@npm:^5.2.1":
  version: 5.4.5
  resolution: "node-notifier@npm:5.4.5"
  dependencies:
    growly: ^1.3.0
    is-wsl: ^1.1.0
    semver: ^5.5.0
    shellwords: ^0.1.1
    which: ^1.3.0
  checksum: 8de174eb055a2ec55c1b0beede9328e8f9d4e32e7eacb7e3e2fddff48534105d0e2e10b4947dd422cc0602c65141317499c6fb1dc3b8ba03c775fb159e360bef
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.1":
  version: 2.0.2
  resolution: "node-releases@npm:2.0.2"
  checksum: da858bf86b4d512842379749f5a5e4196ddab05ba18ffcf29f05bf460beceaca927f070f4430bb5046efec18941ddbc85e4c5fdbb83afc28a38dd6069a2f255e
  languageName: node
  linkType: hard

"nopt@npm:^5.0.0":
  version: 5.0.0
  resolution: "nopt@npm:5.0.0"
  dependencies:
    abbrev: 1
  bin:
    nopt: bin/nopt.js
  checksum: d35fdec187269503843924e0114c0c6533fb54bbf1620d0f28b4b60ba01712d6687f62565c55cc20a504eff0fbe5c63e22340c3fad549ad40469ffb611b04f2f
  languageName: node
  linkType: hard

"normalize-package-data@npm:^2.3.2, normalize-package-data@npm:^2.5.0":
  version: 2.5.0
  resolution: "normalize-package-data@npm:2.5.0"
  dependencies:
    hosted-git-info: ^2.1.4
    resolve: ^1.10.0
    semver: 2 || 3 || 4 || 5
    validate-npm-package-license: ^3.0.1
  checksum: 7999112efc35a6259bc22db460540cae06564aa65d0271e3bdfa86876d08b0e578b7b5b0028ee61b23f1cae9fc0e7847e4edc0948d3068a39a2a82853efc8499
  languageName: node
  linkType: hard

"normalize-path@npm:^1.0.0":
  version: 1.0.0
  resolution: "normalize-path@npm:1.0.0"
  checksum: b8b66ac272c7bbf63a3e47a050a5570b137c3e5085e410e2a0fffe1ff663e24e53c51364666586bd38f931e357d3cb5760a30e3c2f902a03f84bd7bf06e7f727
  languageName: node
  linkType: hard

"normalize-path@npm:^2.0.1, normalize-path@npm:^2.1.1":
  version: 2.1.1
  resolution: "normalize-path@npm:2.1.1"
  dependencies:
    remove-trailing-separator: ^1.0.1
  checksum: 7e9cbdcf7f5b8da7aa191fbfe33daf290cdcd8c038f422faf1b8a83c972bf7a6d94c5be34c4326cb00fb63bc0fd97d9fbcfaf2e5d6142332c2cd36d2e1b86cea
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 88eeb4da891e10b1318c4b2476b6e2ecbeb5ff97d946815ffea7794c31a89017c70d7f34b3c2ebf23ef4e9fc9fb99f7dffe36da22011b5b5c6ffa34f4873ec20
  languageName: node
  linkType: hard

"normalize-range@npm:^0.1.2":
  version: 0.1.2
  resolution: "normalize-range@npm:0.1.2"
  checksum: 9b2f14f093593f367a7a0834267c24f3cb3e887a2d9809c77d8a7e5fd08738bcd15af46f0ab01cc3a3d660386f015816b5c922cea8bf2ee79777f40874063184
  languageName: node
  linkType: hard

"normalize-url@npm:^2.0.1":
  version: 2.0.1
  resolution: "normalize-url@npm:2.0.1"
  dependencies:
    prepend-http: ^2.0.0
    query-string: ^5.0.1
    sort-keys: ^2.0.0
  checksum: 30e337ee03fc7f360c7d2b966438657fabd2628925cc58bffc893982fe4d2c59b397ae664fa2c319cd83565af73eee88906e80bc5eec91bc32b601920e770d75
  languageName: node
  linkType: hard

"normalize-url@npm:^3.0.0":
  version: 3.3.0
  resolution: "normalize-url@npm:3.3.0"
  checksum: f6aa4a1a94c3b799812f3e7fc987fb4599d869bfa8e9a160b6f2c5a2b4e62ada998d64dca30d9e20769d8bd95d3da1da3d4841dba2cc3c4d85364e1eb46219a2
  languageName: node
  linkType: hard

"normalize-wheel@npm:^1.0.1":
  version: 1.0.1
  resolution: "normalize-wheel@npm:1.0.1"
  checksum: 00b32efa040bad9438e732385a4ca27f8532fa2c8c06b54be43b9f75b2da6642bf41a0b4f81e542639dc8d682cde8b059b0a02ae0723fb8ebc3c8f036c8e51d8
  languageName: node
  linkType: hard

"normalize.css@npm:7.0.0":
  version: 7.0.0
  resolution: "normalize.css@npm:7.0.0"
  checksum: 8023436d5f6113fa0989c639addc3056c8c42c3074d00ed035e3b45ce6e25e0fc55171c3606efa1a77e144a96ee44fb4d705e0067d6051b7761dd5fe13fde481
  languageName: node
  linkType: hard

"npm-run-path@npm:^2.0.0":
  version: 2.0.2
  resolution: "npm-run-path@npm:2.0.2"
  dependencies:
    path-key: ^2.0.0
  checksum: acd5ad81648ba4588ba5a8effb1d98d2b339d31be16826a118d50f182a134ac523172101b82eab1d01cb4c2ba358e857d54cfafd8163a1ffe7bd52100b741125
  languageName: node
  linkType: hard

"npmlog@npm:^6.0.0":
  version: 6.0.0
  resolution: "npmlog@npm:6.0.0"
  dependencies:
    are-we-there-yet: ^2.0.0
    console-control-strings: ^1.1.0
    gauge: ^4.0.0
    set-blocking: ^2.0.0
  checksum: 33d8a7fe3d63bf83b16655b6588ae7ba10b5f37b067a661e7cab6508660d7c3204ae716ee2c5ce4eb9626fd1489cf2fa7645d789bc3b704f8c3ccb04a532a50b
  languageName: node
  linkType: hard

"nprogress@npm:0.2.0":
  version: 0.2.0
  resolution: "nprogress@npm:0.2.0"
  checksum: 66b7bec5d563ecf2d1c3d2815e6d5eb74ed815eee8563e0afa63d3f185ab1b9cf2ddd97e1ded263b9995c5019d26d600320e849e50f3747984daa033744619dc
  languageName: node
  linkType: hard

"nth-check@npm:^1.0.2":
  version: 1.0.2
  resolution: "nth-check@npm:1.0.2"
  dependencies:
    boolbase: ~1.0.0
  checksum: 59e115fdd75b971d0030f42ada3aac23898d4c03aa13371fa8b3339d23461d1badf3fde5aad251fb956aaa75c0a3b9bfcd07c08a34a83b4f9dadfdce1d19337c
  languageName: node
  linkType: hard

"nth-check@npm:^2.0.1":
  version: 2.0.1
  resolution: "nth-check@npm:2.0.1"
  dependencies:
    boolbase: ^1.0.0
  checksum: 5386d035c48438ff304fe687704d93886397349d1bed136de97aeae464caba10e8ffac55a04b215b86b3bc8897f33e0a5aa1045a9d8b2f251ae61b2a3ad3e450
  languageName: node
  linkType: hard

"num2fraction@npm:^1.2.2":
  version: 1.2.2
  resolution: "num2fraction@npm:1.2.2"
  checksum: 1da9c6797b505d3f5b17c7f694c4fa31565bdd5c0e5d669553253aed848a580804cd285280e8a73148bd9628839267daee4967f24b53d4e893e44b563e412635
  languageName: node
  linkType: hard

"number-is-nan@npm:^1.0.0":
  version: 1.0.1
  resolution: "number-is-nan@npm:1.0.1"
  checksum: 13656bc9aa771b96cef209ffca31c31a03b507ca6862ba7c3f638a283560620d723d52e626d57892c7fff475f4c36ac07f0600f14544692ff595abff214b9ffb
  languageName: node
  linkType: hard

"nwsapi@npm:^2.0.7":
  version: 2.2.0
  resolution: "nwsapi@npm:2.2.0"
  checksum: 5ef4a9bc0c1a5b7f2e014aa6a4b359a257503b796618ed1ef0eb852098f77e772305bb0e92856e4bbfa3e6c75da48c0113505c76f144555ff38867229c2400a7
  languageName: node
  linkType: hard

"oauth-sign@npm:~0.9.0":
  version: 0.9.0
  resolution: "oauth-sign@npm:0.9.0"
  checksum: 8f5497a127967866a3c67094c21efd295e46013a94e6e828573c62220e9af568cc1d2d04b16865ba583e430510fa168baf821ea78f355146d8ed7e350fc44c64
  languageName: node
  linkType: hard

"object-assign@npm:^4.0.1, object-assign@npm:^4.1.0, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"object-copy@npm:^0.1.0":
  version: 0.1.0
  resolution: "object-copy@npm:0.1.0"
  dependencies:
    copy-descriptor: ^0.1.0
    define-property: ^0.2.5
    kind-of: ^3.0.3
  checksum: a9e35f07e3a2c882a7e979090360d1a20ab51d1fa19dfdac3aa8873b328a7c4c7683946ee97c824ae40079d848d6740a3788fa14f2185155dab7ed970a72c783
  languageName: node
  linkType: hard

"object-hash@npm:^1.1.4":
  version: 1.3.1
  resolution: "object-hash@npm:1.3.1"
  checksum: fdcb957a2f15a9060e30655a9f683ba1fc25dfb8809a73d32e9634bec385a2f1d686c707ac1e5f69fb773bc12df03fb64c77ce3faeed83e35f4eb1946cb1989e
  languageName: node
  linkType: hard

"object-inspect@npm:^1.11.0, object-inspect@npm:^1.9.0":
  version: 1.12.0
  resolution: "object-inspect@npm:1.12.0"
  checksum: 2b36d4001a9c921c6b342e2965734519c9c58c355822243c3207fbf0aac271f8d44d30d2d570d450b2cc6f0f00b72bcdba515c37827d2560e5f22b1899a31cf4
  languageName: node
  linkType: hard

"object-is@npm:^1.0.1":
  version: 1.1.5
  resolution: "object-is@npm:1.1.5"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
  checksum: 989b18c4cba258a6b74dc1d74a41805c1a1425bce29f6cabb50dcb1a6a651ea9104a1b07046739a49a5bb1bc49727bcb00efd5c55f932f6ea04ec8927a7901fe
  languageName: node
  linkType: hard

"object-keys@npm:^1.0.12, object-keys@npm:^1.1.1":
  version: 1.1.1
  resolution: "object-keys@npm:1.1.1"
  checksum: b363c5e7644b1e1b04aa507e88dcb8e3a2f52b6ffd0ea801e4c7a62d5aa559affe21c55a07fd4b1fd55fc03a33c610d73426664b20032405d7b92a1414c34d6a
  languageName: node
  linkType: hard

"object-visit@npm:^1.0.0":
  version: 1.0.1
  resolution: "object-visit@npm:1.0.1"
  dependencies:
    isobject: ^3.0.0
  checksum: b0ee07f5bf3bb881b881ff53b467ebbde2b37ebb38649d6944a6cd7681b32eedd99da9bd1e01c55facf81f54ed06b13af61aba6ad87f0052982995e09333f790
  languageName: node
  linkType: hard

"object.assign@npm:^4.1.0, object.assign@npm:^4.1.2":
  version: 4.1.2
  resolution: "object.assign@npm:4.1.2"
  dependencies:
    call-bind: ^1.0.0
    define-properties: ^1.1.3
    has-symbols: ^1.0.1
    object-keys: ^1.1.1
  checksum: d621d832ed7b16ac74027adb87196804a500d80d9aca536fccb7ba48d33a7e9306a75f94c1d29cbfa324bc091bfc530bc24789568efdaee6a47fcfa298993814
  languageName: node
  linkType: hard

"object.getownpropertydescriptors@npm:^2.0.3, object.getownpropertydescriptors@npm:^2.1.0, object.getownpropertydescriptors@npm:^2.1.1":
  version: 2.1.3
  resolution: "object.getownpropertydescriptors@npm:2.1.3"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
    es-abstract: ^1.19.1
  checksum: 1467873456fd367a0eb91350caff359a8f05ceb069b4535a1846aa1f74f477a49ae704f6c89c0c14cc0ae1518ee3a0aa57c7f733a8e7b2b06b34a818e9593d2f
  languageName: node
  linkType: hard

"object.omit@npm:^2.0.0":
  version: 2.0.1
  resolution: "object.omit@npm:2.0.1"
  dependencies:
    for-own: ^0.1.4
    is-extendable: ^0.1.1
  checksum: 581de24e16b72388ad294693daef29072943ef8db3da16aaeb580b5ecefacabe58a744893e9d1564e29130d3465c96ba3e13a03fd130d14f3e06525b3176cac4
  languageName: node
  linkType: hard

"object.pick@npm:^1.3.0":
  version: 1.3.0
  resolution: "object.pick@npm:1.3.0"
  dependencies:
    isobject: ^3.0.1
  checksum: 77fb6eed57c67adf75e9901187e37af39f052ef601cb4480386436561357eb9e459e820762f01fd02c5c1b42ece839ad393717a6d1850d848ee11fbabb3e580a
  languageName: node
  linkType: hard

"object.values@npm:^1.1.0":
  version: 1.1.5
  resolution: "object.values@npm:1.1.5"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
    es-abstract: ^1.19.1
  checksum: 0f17e99741ebfbd0fa55ce942f6184743d3070c61bd39221afc929c8422c4907618c8da694c6915bc04a83ab3224260c779ba37fc07bb668bdc5f33b66a902a4
  languageName: node
  linkType: hard

"obuf@npm:^1.0.0, obuf@npm:^1.1.2":
  version: 1.1.2
  resolution: "obuf@npm:1.1.2"
  checksum: 41a2ba310e7b6f6c3b905af82c275bf8854896e2e4c5752966d64cbcd2f599cfffd5932006bcf3b8b419dfdacebb3a3912d5d94e10f1d0acab59876c8757f27f
  languageName: node
  linkType: hard

"omelette@npm:0.4.5":
  version: 0.4.5
  resolution: "omelette@npm:0.4.5"
  checksum: 4def3ed9e444d16407f50f27101c8f172271a3d69a1e692fbf9a718261131f576900fc44307f32720c8d6ca9adaffc711a0dde7290994e2a588080d4347ab9f9
  languageName: node
  linkType: hard

"on-finished@npm:~2.3.0":
  version: 2.3.0
  resolution: "on-finished@npm:2.3.0"
  dependencies:
    ee-first: 1.1.1
  checksum: 1db595bd963b0124d6fa261d18320422407b8f01dc65863840f3ddaaf7bcad5b28ff6847286703ca53f4ec19595bd67a2f1253db79fc4094911ec6aa8df1671b
  languageName: node
  linkType: hard

"on-headers@npm:~1.0.2":
  version: 1.0.2
  resolution: "on-headers@npm:1.0.2"
  checksum: 2bf13467215d1e540a62a75021e8b318a6cfc5d4fc53af8e8f84ad98dbcea02d506c6d24180cd62e1d769c44721ba542f3154effc1f7579a8288c9f7873ed8e5
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: 1
  checksum: cd0a88501333edd640d95f0d2700fbde6bff20b3d4d9bdc521bdd31af0656b5706570d6c6afe532045a20bb8dc0849f8332d6f2a416e0ba6d3d3b98806c7db68
  languageName: node
  linkType: hard

"onetime@npm:^2.0.0":
  version: 2.0.1
  resolution: "onetime@npm:2.0.1"
  dependencies:
    mimic-fn: ^1.0.0
  checksum: bb44015ac7a525d0fb43b029a583d4ad359834632b4424ca209b438aacf6d669dda81b5edfbdb42c22636e607b276ba5589f46694a729e3bc27948ce26f4cc1a
  languageName: node
  linkType: hard

"open@npm:^6.3.0":
  version: 6.4.0
  resolution: "open@npm:6.4.0"
  dependencies:
    is-wsl: ^1.1.0
  checksum: e5037facf3e03ed777537db3e2511ada37f351c4394e1dadccf9cac11d63b28447ae8b495b7b138659910fd78d918bafed546e47163673c4a4e43dbb5ac53c5d
  languageName: node
  linkType: hard

"opener@npm:^1.5.1":
  version: 1.5.2
  resolution: "opener@npm:1.5.2"
  bin:
    opener: bin/opener-bin.js
  checksum: 33b620c0d53d5b883f2abc6687dd1c5fd394d270dbe33a6356f2d71e0a2ec85b100d5bac94694198ccf5c30d592da863b2292c5539009c715a9c80c697b4f6cc
  languageName: node
  linkType: hard

"opn@npm:^5.5.0":
  version: 5.5.0
  resolution: "opn@npm:5.5.0"
  dependencies:
    is-wsl: ^1.1.0
  checksum: 35b677b5a1fd6c8cb1996b0607671ba79f7ce9fa029217d54eafaf6bee13eb7e700691c6a415009140fd02a435fffdfd143875f3b233b60f3f9d631c6f6b81a0
  languageName: node
  linkType: hard

"optionator@npm:^0.8.1, optionator@npm:^0.8.2":
  version: 0.8.3
  resolution: "optionator@npm:0.8.3"
  dependencies:
    deep-is: ~0.1.3
    fast-levenshtein: ~2.0.6
    levn: ~0.3.0
    prelude-ls: ~1.1.2
    type-check: ~0.3.2
    word-wrap: ~1.2.3
  checksum: b8695ddf3d593203e25ab0900e265d860038486c943ff8b774f596a310f8ceebdb30c6832407a8198ba3ec9debe1abe1f51d4aad94843612db3b76d690c61d34
  languageName: node
  linkType: hard

"ora@npm:^3.4.0":
  version: 3.4.0
  resolution: "ora@npm:3.4.0"
  dependencies:
    chalk: ^2.4.2
    cli-cursor: ^2.1.0
    cli-spinners: ^2.0.0
    log-symbols: ^2.2.0
    strip-ansi: ^5.2.0
    wcwidth: ^1.0.1
  checksum: f1f8e7f290b766276dcd19ddf2159a1971b1ec37eec4a5556b8f5e4afbe513a965ed65c183d38956724263b6a20989b3d8fb71b95ac4a2d6a01db2f1ed8899e4
  languageName: node
  linkType: hard

"original@npm:^1.0.0":
  version: 1.0.2
  resolution: "original@npm:1.0.2"
  dependencies:
    url-parse: ^1.4.3
  checksum: 8dca9311dab50c8953366127cb86b7c07bf547d6aa6dc6873a75964b7563825351440557e5724d9c652c5e99043b8295624f106af077f84bccf19592e421beb9
  languageName: node
  linkType: hard

"os-browserify@npm:^0.3.0":
  version: 0.3.0
  resolution: "os-browserify@npm:0.3.0"
  checksum: 16e37ba3c0e6a4c63443c7b55799ce4066d59104143cb637ecb9fce586d5da319cdca786ba1c867abbe3890d2cbf37953f2d51eea85e20dd6c4570d6c54bfebf
  languageName: node
  linkType: hard

"os-homedir@npm:^1.0.0":
  version: 1.0.2
  resolution: "os-homedir@npm:1.0.2"
  checksum: af609f5a7ab72de2f6ca9be6d6b91a599777afc122ac5cad47e126c1f67c176fe9b52516b9eeca1ff6ca0ab8587fe66208bc85e40a3940125f03cdb91408e9d2
  languageName: node
  linkType: hard

"os-locale@npm:^3.1.0":
  version: 3.1.0
  resolution: "os-locale@npm:3.1.0"
  dependencies:
    execa: ^1.0.0
    lcid: ^2.0.0
    mem: ^4.0.0
  checksum: 53c542b11af3c5fe99624b09c7882b6944f9ae7c69edbc6006b7d42cff630b1f7fd9d63baf84ed31d1ef02b34823b6b31f23a1ecdd593757873d716bc6374099
  languageName: node
  linkType: hard

"os-tmpdir@npm:^1.0.1, os-tmpdir@npm:~1.0.2":
  version: 1.0.2
  resolution: "os-tmpdir@npm:1.0.2"
  checksum: 5666560f7b9f10182548bf7013883265be33620b1c1b4a4d405c25be2636f970c5488ff3e6c48de75b55d02bde037249fe5dbfbb4c0fb7714953d56aed062e6d
  languageName: node
  linkType: hard

"p-defer@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-defer@npm:1.0.0"
  checksum: 4271b935c27987e7b6f229e5de4cdd335d808465604644cb7b4c4c95bef266735859a93b16415af8a41fd663ee9e3b97a1a2023ca9def613dba1bad2a0da0c7b
  languageName: node
  linkType: hard

"p-finally@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-finally@npm:1.0.0"
  checksum: 93a654c53dc805dd5b5891bab16eb0ea46db8f66c4bfd99336ae929323b1af2b70a8b0654f8f1eae924b2b73d037031366d645f1fd18b3d30cbd15950cc4b1d4
  languageName: node
  linkType: hard

"p-is-promise@npm:^2.0.0":
  version: 2.1.0
  resolution: "p-is-promise@npm:2.1.0"
  checksum: c9a8248c8b5e306475a5d55ce7808dbce4d4da2e3d69526e4991a391a7809bfd6cfdadd9bf04f1c96a3db366c93d9a0f5ee81d949e7b1684c4e0f61f747199ef
  languageName: node
  linkType: hard

"p-limit@npm:^1.0.0, p-limit@npm:^1.1.0":
  version: 1.3.0
  resolution: "p-limit@npm:1.3.0"
  dependencies:
    p-try: ^1.0.0
  checksum: 281c1c0b8c82e1ac9f81acd72a2e35d402bf572e09721ce5520164e9de07d8274451378a3470707179ad13240535558f4b277f02405ad752e08c7d5b0d54fbfd
  languageName: node
  linkType: hard

"p-limit@npm:^2.0.0, p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-locate@npm:^2.0.0":
  version: 2.0.0
  resolution: "p-locate@npm:2.0.0"
  dependencies:
    p-limit: ^1.1.0
  checksum: e2dceb9b49b96d5513d90f715780f6f4972f46987dc32a0e18bc6c3fc74a1a5d73ec5f81b1398af5e58b99ea1ad03fd41e9181c01fa81b4af2833958696e3081
  languageName: node
  linkType: hard

"p-locate@npm:^3.0.0":
  version: 3.0.0
  resolution: "p-locate@npm:3.0.0"
  dependencies:
    p-limit: ^2.0.0
  checksum: 83991734a9854a05fe9dbb29f707ea8a0599391f52daac32b86f08e21415e857ffa60f0e120bfe7ce0cc4faf9274a50239c7895fc0d0579d08411e513b83a4ae
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-map@npm:^2.0.0":
  version: 2.1.0
  resolution: "p-map@npm:2.1.0"
  checksum: 9e3ad3c9f6d75a5b5661bcad78c91f3a63849189737cd75e4f1225bf9ac205194e5c44aac2ef6f09562b1facdb9bd1425584d7ac375bfaa17b3f1a142dab936d
  languageName: node
  linkType: hard

"p-map@npm:^4.0.0":
  version: 4.0.0
  resolution: "p-map@npm:4.0.0"
  dependencies:
    aggregate-error: ^3.0.0
  checksum: cb0ab21ec0f32ddffd31dfc250e3afa61e103ef43d957cc45497afe37513634589316de4eb88abdfd969fe6410c22c0b93ab24328833b8eb1ccc087fc0442a1c
  languageName: node
  linkType: hard

"p-retry@npm:^3.0.1":
  version: 3.0.1
  resolution: "p-retry@npm:3.0.1"
  dependencies:
    retry: ^0.12.0
  checksum: 702efc63fc13ef7fc0bab9a1b08432ab38a0236efcbce64af0cf692030ba6ed8009f29ba66e3301cb98dc69ef33e7ccab29ba1ac2bea897f802f81f4f7e468dd
  languageName: node
  linkType: hard

"p-try@npm:^1.0.0":
  version: 1.0.0
  resolution: "p-try@npm:1.0.0"
  checksum: 3b5303f77eb7722144154288bfd96f799f8ff3e2b2b39330efe38db5dd359e4fb27012464cd85cb0a76e9b7edd1b443568cb3192c22e7cffc34989df0bafd605
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"pako@npm:~1.0.5":
  version: 1.0.11
  resolution: "pako@npm:1.0.11"
  checksum: 1be2bfa1f807608c7538afa15d6f25baa523c30ec870a3228a89579e474a4d992f4293859524e46d5d87fd30fa17c5edf34dbef0671251d9749820b488660b16
  languageName: node
  linkType: hard

"parallel-transform@npm:^1.1.0":
  version: 1.2.0
  resolution: "parallel-transform@npm:1.2.0"
  dependencies:
    cyclist: ^1.0.1
    inherits: ^2.0.3
    readable-stream: ^2.1.5
  checksum: ab6ddc1a662cefcfb3d8d546a111763d3b223f484f2e9194e33aefd8f6760c319d0821fd22a00a3adfbd45929b50d2c84cc121389732f013c2ae01c226269c27
  languageName: node
  linkType: hard

"param-case@npm:2.1.x":
  version: 2.1.1
  resolution: "param-case@npm:2.1.1"
  dependencies:
    no-case: ^2.2.0
  checksum: 3a63dcb8d8dc7995a612de061afdc7bb6fe7bd0e6db994db8d4cae999ed879859fd24389090e1a0d93f4c9207ebf8c048c870f468a3f4767161753e03cb9ab58
  languageName: node
  linkType: hard

"parent-module@npm:^1.0.0":
  version: 1.0.1
  resolution: "parent-module@npm:1.0.1"
  dependencies:
    callsites: ^3.0.0
  checksum: 6ba8b255145cae9470cf5551eb74be2d22281587af787a2626683a6c20fbb464978784661478dd2a3f1dad74d1e802d403e1b03c1a31fab310259eec8ac560ff
  languageName: node
  linkType: hard

"parse-asn1@npm:^5.0.0, parse-asn1@npm:^5.1.5":
  version: 5.1.6
  resolution: "parse-asn1@npm:5.1.6"
  dependencies:
    asn1.js: ^5.2.0
    browserify-aes: ^1.0.0
    evp_bytestokey: ^1.0.0
    pbkdf2: ^3.0.3
    safe-buffer: ^5.1.1
  checksum: 9243311d1f88089bc9f2158972aa38d1abd5452f7b7cabf84954ed766048fe574d434d82c6f5a39b988683e96fb84cd933071dda38927e03469dc8c8d14463c7
  languageName: node
  linkType: hard

"parse-glob@npm:^3.0.4":
  version: 3.0.4
  resolution: "parse-glob@npm:3.0.4"
  dependencies:
    glob-base: ^0.3.0
    is-dotfile: ^1.0.0
    is-extglob: ^1.0.0
    is-glob: ^2.0.0
  checksum: 447bc442d76522c5e03b5babc8582d4a37fe9d59b3e5ef8d7ddae4e03060637ae38d5d28686e03c27e4d20be34983b5cb053cf8b066d34be0f9d1867eb677e45
  languageName: node
  linkType: hard

"parse-json@npm:^2.2.0":
  version: 2.2.0
  resolution: "parse-json@npm:2.2.0"
  dependencies:
    error-ex: ^1.2.0
  checksum: dda78a63e57a47b713a038630868538f718a7ca0cd172a36887b0392ccf544ed0374902eb28f8bf3409e8b71d62b79d17062f8543afccf2745f9b0b2d2bb80ca
  languageName: node
  linkType: hard

"parse-json@npm:^4.0.0":
  version: 4.0.0
  resolution: "parse-json@npm:4.0.0"
  dependencies:
    error-ex: ^1.3.1
    json-parse-better-errors: ^1.0.1
  checksum: 0fe227d410a61090c247e34fa210552b834613c006c2c64d9a05cfe9e89cf8b4246d1246b1a99524b53b313e9ac024438d0680f67e33eaed7e6f38db64cfe7b5
  languageName: node
  linkType: hard

"parse-json@npm:^5.0.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": ^7.0.0
    error-ex: ^1.3.1
    json-parse-even-better-errors: ^2.3.0
    lines-and-columns: ^1.1.6
  checksum: 62085b17d64da57f40f6afc2ac1f4d95def18c4323577e1eced571db75d9ab59b297d1d10582920f84b15985cbfc6b6d450ccbf317644cfa176f3ed982ad87e2
  languageName: node
  linkType: hard

"parse5@npm:4.0.0":
  version: 4.0.0
  resolution: "parse5@npm:4.0.0"
  checksum: 2123cec690689fed44e6c76aa8a08215d2dadece7eff7b35156dda7485e6a232c9b737313688ee715eb0678b6a87a31026927dd74690154f8a0811059845ba46
  languageName: node
  linkType: hard

"parseurl@npm:~1.3.2, parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 407cee8e0a3a4c5cd472559bca8b6a45b82c124e9a4703302326e9ab60fc1081442ada4e02628efef1eb16197ddc7f8822f5a91fd7d7c86b51f530aedb17dfa2
  languageName: node
  linkType: hard

"pascalcase@npm:^0.1.1":
  version: 0.1.1
  resolution: "pascalcase@npm:0.1.1"
  checksum: f83681c3c8ff75fa473a2bb2b113289952f802ff895d435edd717e7cb898b0408cbdb247117a938edcbc5d141020909846cc2b92c47213d764e2a94d2ad2b925
  languageName: node
  linkType: hard

"path-browserify@npm:0.0.1":
  version: 0.0.1
  resolution: "path-browserify@npm:0.0.1"
  checksum: ae8dcd45d0d3cfbaf595af4f206bf3ed82d77f72b4877ae7e77328079e1468c84f9386754bb417d994d5a19bf47882fd253565c18441cd5c5c90ae5187599e35
  languageName: node
  linkType: hard

"path-dirname@npm:^1.0.0":
  version: 1.0.2
  resolution: "path-dirname@npm:1.0.2"
  checksum: 0d2f6604ae05a252a0025318685f290e2764ecf9c5436f203cdacfc8c0b17c24cdedaa449d766beb94ab88cc7fc70a09ec21e7933f31abc2b719180883e5e33f
  languageName: node
  linkType: hard

"path-exists@npm:^2.0.0":
  version: 2.1.0
  resolution: "path-exists@npm:2.1.0"
  dependencies:
    pinkie-promise: ^2.0.0
  checksum: fdb734f1d00f225f7a0033ce6d73bff6a7f76ea08936abf0e5196fa6e54a645103538cd8aedcb90d6d8c3fa3705ded0c58a4da5948ae92aa8834892c1ab44a84
  languageName: node
  linkType: hard

"path-exists@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-exists@npm:3.0.0"
  checksum: 96e92643aa34b4b28d0de1cd2eba52a1c5313a90c6542d03f62750d82480e20bfa62bc865d5cfc6165f5fcd5aeb0851043c40a39be5989646f223300021bae0a
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0, path-is-absolute@npm:^1.0.1":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 060840f92cf8effa293bcc1bea81281bd7d363731d214cbe5c227df207c34cd727430f70c6037b5159c8a870b9157cba65e775446b0ab06fd5ecc7e54615a3b8
  languageName: node
  linkType: hard

"path-is-inside@npm:^1.0.2":
  version: 1.0.2
  resolution: "path-is-inside@npm:1.0.2"
  checksum: 0b5b6c92d3018b82afb1f74fe6de6338c4c654de4a96123cb343f2b747d5606590ac0c890f956ed38220a4ab59baddfd7b713d78a62d240b20b14ab801fa02cb
  languageName: node
  linkType: hard

"path-key@npm:^2.0.0, path-key@npm:^2.0.1":
  version: 2.0.1
  resolution: "path-key@npm:2.0.1"
  checksum: f7ab0ad42fe3fb8c7f11d0c4f849871e28fbd8e1add65c370e422512fc5887097b9cf34d09c1747d45c942a8c1e26468d6356e2df3f740bf177ab8ca7301ebfd
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.5, path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.7":
  version: 0.1.7
  resolution: "path-to-regexp@npm:0.1.7"
  checksum: 69a14ea24db543e8b0f4353305c5eac6907917031340e5a8b37df688e52accd09e3cebfe1660b70d76b6bd89152f52183f28c74813dbf454ba1a01c82a38abce
  languageName: node
  linkType: hard

"path-to-regexp@npm:2.4.0":
  version: 2.4.0
  resolution: "path-to-regexp@npm:2.4.0"
  checksum: 581175bf2968e51452f2b8c71f10e75c995693668b4ecf7d0b48962fbe0c56830661ca5dd5fd6d8e2f0cc9a045ce07e89af504ab133e1d21887c2712df85b1f4
  languageName: node
  linkType: hard

"path-type@npm:^1.0.0":
  version: 1.1.0
  resolution: "path-type@npm:1.1.0"
  dependencies:
    graceful-fs: ^4.1.2
    pify: ^2.0.0
    pinkie-promise: ^2.0.0
  checksum: 59a4b2c0e566baf4db3021a1ed4ec09a8b36fca960a490b54a6bcefdb9987dafe772852982b6011cd09579478a96e57960a01f75fa78a794192853c9d468fc79
  languageName: node
  linkType: hard

"path-type@npm:^3.0.0":
  version: 3.0.0
  resolution: "path-type@npm:3.0.0"
  dependencies:
    pify: ^3.0.0
  checksum: 735b35e256bad181f38fa021033b1c33cfbe62ead42bb2222b56c210e42938eecb272ae1949f3b6db4ac39597a61b44edd8384623ec4d79bfdc9a9c0f12537a6
  languageName: node
  linkType: hard

"pbkdf2@npm:^3.0.3":
  version: 3.1.2
  resolution: "pbkdf2@npm:3.1.2"
  dependencies:
    create-hash: ^1.1.2
    create-hmac: ^1.1.4
    ripemd160: ^2.0.1
    safe-buffer: ^5.0.1
    sha.js: ^2.4.8
  checksum: 2c950a100b1da72123449208e231afc188d980177d021d7121e96a2de7f2abbc96ead2b87d03d8fe5c318face097f203270d7e27908af9f471c165a4e8e69c92
  languageName: node
  linkType: hard

"performance-now@npm:^2.1.0":
  version: 2.1.0
  resolution: "performance-now@npm:2.1.0"
  checksum: 534e641aa8f7cba160f0afec0599b6cecefbb516a2e837b512be0adbe6c1da5550e89c78059c7fabc5c9ffdf6627edabe23eb7c518c4500067a898fa65c2b550
  languageName: node
  linkType: hard

"picocolors@npm:^0.2.1":
  version: 0.2.1
  resolution: "picocolors@npm:0.2.1"
  checksum: 3b0f441f0062def0c0f39e87b898ae7461c3a16ffc9f974f320b44c799418cabff17780ee647fda42b856a1dc45897e2c62047e1b546d94d6d5c6962f45427b2
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0":
  version: 1.0.0
  resolution: "picocolors@npm:1.0.0"
  checksum: a2e8092dd86c8396bdba9f2b5481032848525b3dc295ce9b57896f931e63fc16f79805144321f72976383fc249584672a75cc18d6777c6b757603f372f745981
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"pify@npm:^2.0.0":
  version: 2.3.0
  resolution: "pify@npm:2.3.0"
  checksum: 9503aaeaf4577acc58642ad1d25c45c6d90288596238fb68f82811c08104c800e5a7870398e9f015d82b44ecbcbef3dc3d4251a1cbb582f6e5959fe09884b2ba
  languageName: node
  linkType: hard

"pify@npm:^3.0.0":
  version: 3.0.0
  resolution: "pify@npm:3.0.0"
  checksum: 6cdcbc3567d5c412450c53261a3f10991665d660961e06605decf4544a61a97a54fefe70a68d5c37080ff9d6f4cf51444c90198d1ba9f9309a6c0d6e9f5c4fde
  languageName: node
  linkType: hard

"pify@npm:^4.0.1":
  version: 4.0.1
  resolution: "pify@npm:4.0.1"
  checksum: 9c4e34278cb09987685fa5ef81499c82546c033713518f6441778fbec623fc708777fe8ac633097c72d88470d5963094076c7305cafc7ad340aae27cfacd856b
  languageName: node
  linkType: hard

"pinkie-promise@npm:^2.0.0":
  version: 2.0.1
  resolution: "pinkie-promise@npm:2.0.1"
  dependencies:
    pinkie: ^2.0.0
  checksum: b53a4a2e73bf56b6f421eef711e7bdcb693d6abb474d57c5c413b809f654ba5ee750c6a96dd7225052d4b96c4d053cdcb34b708a86fceed4663303abee52fcca
  languageName: node
  linkType: hard

"pinkie@npm:^2.0.0":
  version: 2.0.4
  resolution: "pinkie@npm:2.0.4"
  checksum: b12b10afea1177595aab036fc220785488f67b4b0fc49e7a27979472592e971614fa1c728e63ad3e7eb748b4ec3c3dbd780819331dad6f7d635c77c10537b9db
  languageName: node
  linkType: hard

"pirates@npm:^4.0.0":
  version: 4.0.5
  resolution: "pirates@npm:4.0.5"
  checksum: c9994e61b85260bec6c4fc0307016340d9b0c4f4b6550a957afaaff0c9b1ad58fbbea5cfcf083860a25cb27a375442e2b0edf52e2e1e40e69934e08dcc52d227
  languageName: node
  linkType: hard

"pkg-dir@npm:^1.0.0":
  version: 1.0.0
  resolution: "pkg-dir@npm:1.0.0"
  dependencies:
    find-up: ^1.0.0
  checksum: ce49878797dd81a5cee1cb7f05fdd431729309e4854c9f83d7748491b9d25c5f8ef04b3b7658134361fa036934c0aaa7fc7f984e46970dd227aa490f3869d36a
  languageName: node
  linkType: hard

"pkg-dir@npm:^2.0.0":
  version: 2.0.0
  resolution: "pkg-dir@npm:2.0.0"
  dependencies:
    find-up: ^2.1.0
  checksum: 8c72b712305b51e1108f0ffda5ec1525a8307e54a5855db8fb1dcf77561a5ae98e2ba3b4814c9806a679f76b2f7e5dd98bde18d07e594ddd9fdd25e9cf242ea1
  languageName: node
  linkType: hard

"pkg-dir@npm:^3.0.0":
  version: 3.0.0
  resolution: "pkg-dir@npm:3.0.0"
  dependencies:
    find-up: ^3.0.0
  checksum: 70c9476ffefc77552cc6b1880176b71ad70bfac4f367604b2b04efd19337309a4eec985e94823271c7c0e83946fa5aeb18cd360d15d10a5d7533e19344bfa808
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.1.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: ^4.0.0
  checksum: 9863e3f35132bf99ae1636d31ff1e1e3501251d480336edb1c211133c8d58906bed80f154a1d723652df1fda91e01c7442c2eeaf9dc83157c7ae89087e43c8d6
  languageName: node
  linkType: hard

"pkg-up@npm:^2.0.0":
  version: 2.0.0
  resolution: "pkg-up@npm:2.0.0"
  dependencies:
    find-up: ^2.1.0
  checksum: de4b418175281a082e366ce1a919f032520ee53cf421578b35173f03816f6ec4c19e1552066840bb0988c3e1215859653948efd6ca3507a23f4f44229269500d
  languageName: node
  linkType: hard

"pluralize@npm:^7.0.0":
  version: 7.0.0
  resolution: "pluralize@npm:7.0.0"
  checksum: e3f694924b7c8c03dc9fa40b2312e17787998ac6e20fccace11efa1146046eb9931541bfd247b3ec5535e730d902a5aee7c32681d5bf9a00fc74a72039a3e609
  languageName: node
  linkType: hard

"pn@npm:^1.1.0":
  version: 1.1.0
  resolution: "pn@npm:1.1.0"
  checksum: e4654186dc92a187c8c7fe4ccda902f4d39dd9c10f98d1c5a08ce5fad5507ef1e33ddb091240c3950bee81bd201b4c55098604c433a33b5e8bdd97f38b732fa0
  languageName: node
  linkType: hard

"portfinder@npm:^1.0.20, portfinder@npm:^1.0.26":
  version: 1.0.28
  resolution: "portfinder@npm:1.0.28"
  dependencies:
    async: ^2.6.2
    debug: ^3.1.1
    mkdirp: ^0.5.5
  checksum: 91fef602f13f8f4c64385d0ad2a36cc9dc6be0b8d10a2628ee2c3c7b9917ab4fefb458815b82cea2abf4b785cd11c9b4e2d917ac6fa06f14b6fa880ca8f8928c
  languageName: node
  linkType: hard

"posix-character-classes@npm:^0.1.0":
  version: 0.1.1
  resolution: "posix-character-classes@npm:0.1.1"
  checksum: dedb99913c60625a16050cfed2fb5c017648fc075be41ac18474e1c6c3549ef4ada201c8bd9bd006d36827e289c571b6092e1ef6e756cdbab2fd7046b25c6442
  languageName: node
  linkType: hard

"postcss-calc@npm:^7.0.1":
  version: 7.0.5
  resolution: "postcss-calc@npm:7.0.5"
  dependencies:
    postcss: ^7.0.27
    postcss-selector-parser: ^6.0.2
    postcss-value-parser: ^4.0.2
  checksum: 03640d493fb0e557634ab23e5d1eb527b014fb491ac3e62b45e28f5a6ef57e25a209f82040ce54c40d5a1a7307597a55d3fa6e8cece0888261a66bc75e39a68b
  languageName: node
  linkType: hard

"postcss-colormin@npm:^4.0.3":
  version: 4.0.3
  resolution: "postcss-colormin@npm:4.0.3"
  dependencies:
    browserslist: ^4.0.0
    color: ^3.0.0
    has: ^1.0.0
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
  checksum: 9b2eab73cd227cbf296f1a2a6466047f6c70b918c3844535531fd87f31d7878e1a8d81e8803ffe2ee8c3330ea5bec65e358a0e0f33defcd758975064e07fe928
  languageName: node
  linkType: hard

"postcss-convert-values@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-convert-values@npm:4.0.1"
  dependencies:
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
  checksum: 71cac73f5befeb8bc16274e2aaabe1b8e0cb42a8b8641dc2aa61b1c502697b872a682c36f370cce325553bbfc859c38f2b064fae6f6469b1cada79e733559261
  languageName: node
  linkType: hard

"postcss-discard-comments@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-discard-comments@npm:4.0.2"
  dependencies:
    postcss: ^7.0.0
  checksum: b087d47649160b7c6236aba028d27f1796a0dcb21e9ffd0da62271171fc31b7f150ee6c7a24fa97e3f5cd1af92e0dc41cb2e2680a175da53f1e536c441bda56a
  languageName: node
  linkType: hard

"postcss-discard-duplicates@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-discard-duplicates@npm:4.0.2"
  dependencies:
    postcss: ^7.0.0
  checksum: bd83647a8e5ea34b0cfe563d0c1410a0c9e742011aa67955709c5ecd2d2bb03b7016053781e975e4c802127d2f9a0cd9c22f1f2783b9d7b1c35487d60f7ea540
  languageName: node
  linkType: hard

"postcss-discard-empty@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-discard-empty@npm:4.0.1"
  dependencies:
    postcss: ^7.0.0
  checksum: 529b177bd2417fa5c8887891369b4538b858d767461192974a796814265794e08e0e624a9f4c566ed9f841af3faddb7e7a9c05c45cbbe2fb1f092f65bd227f5c
  languageName: node
  linkType: hard

"postcss-discard-overridden@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-discard-overridden@npm:4.0.1"
  dependencies:
    postcss: ^7.0.0
  checksum: b34d8cf58e4d13d99a3a9459f4833f1248ca897316bbb927375590feba35c24a0304084a6174a7bf3fe4ba3d5e5e9baf15ea938e7e5744e56915fa7ef6d91ee0
  languageName: node
  linkType: hard

"postcss-load-config@npm:^2.0.0":
  version: 2.1.2
  resolution: "postcss-load-config@npm:2.1.2"
  dependencies:
    cosmiconfig: ^5.0.0
    import-cwd: ^2.0.0
  checksum: 2e6d3a499512a03c19b0090f4143861612d613511d57122879d9fd545558d2a9fcbe85a2b0faf2ec32bbce0e62d22d2b544d91cbc4d4dfb3f22f841f8271fbc6
  languageName: node
  linkType: hard

"postcss-loader@npm:^3.0.0":
  version: 3.0.0
  resolution: "postcss-loader@npm:3.0.0"
  dependencies:
    loader-utils: ^1.1.0
    postcss: ^7.0.0
    postcss-load-config: ^2.0.0
    schema-utils: ^1.0.0
  checksum: a6a922cbcc225ef57fb88c8248f91195869cd11e0d2b0b0fe84bc89a3074437d592d79a9fc39e50218677b7ba3a41b0e1c7e8f9666e59d41a196d7ab022c5805
  languageName: node
  linkType: hard

"postcss-merge-longhand@npm:^4.0.11":
  version: 4.0.11
  resolution: "postcss-merge-longhand@npm:4.0.11"
  dependencies:
    css-color-names: 0.0.4
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
    stylehacks: ^4.0.0
  checksum: 45082b492d4d771c1607707d04dbcaece85a100011109886af9460a7868720de1121e290a6442360e2668db510edef579194197d1b534e9fb6c8df7a6cb86a4d
  languageName: node
  linkType: hard

"postcss-merge-rules@npm:^4.0.3":
  version: 4.0.3
  resolution: "postcss-merge-rules@npm:4.0.3"
  dependencies:
    browserslist: ^4.0.0
    caniuse-api: ^3.0.0
    cssnano-util-same-parent: ^4.0.0
    postcss: ^7.0.0
    postcss-selector-parser: ^3.0.0
    vendors: ^1.0.0
  checksum: ed0f3880e1076e5b2a08e4cff35b50dc7dfbd337e6ba16a0ca157e28268cfa1d6c6d821e902d319757f32a7d36f944cad51be76f8b34858d1d7a637e7b585919
  languageName: node
  linkType: hard

"postcss-minify-font-values@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-minify-font-values@npm:4.0.2"
  dependencies:
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
  checksum: add296b3bc88501283d65b54ad83552f47c98dd403740a70d8dfeef6d30a21d4a1f40191ffef1029a9474e9580a73e84ef644e99ede76c5a2474579b583f4b34
  languageName: node
  linkType: hard

"postcss-minify-gradients@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-minify-gradients@npm:4.0.2"
  dependencies:
    cssnano-util-get-arguments: ^4.0.0
    is-color-stop: ^1.0.0
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
  checksum: b83de019cc392192d64182fa6f609383904ef69013d71cda5d06fadab92b4daa73f5be0d0254c5eb0805405e5e1b9c44e49ca6bc629c4c7a24a8164a30b40d46
  languageName: node
  linkType: hard

"postcss-minify-params@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-minify-params@npm:4.0.2"
  dependencies:
    alphanum-sort: ^1.0.0
    browserslist: ^4.0.0
    cssnano-util-get-arguments: ^4.0.0
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
    uniqs: ^2.0.0
  checksum: 15e7f196b3408ab3f55f1a7c9fa8aeea7949fdd02be28af232dd2e47bb7722e0e0a416d6b2c4550ba333a485b775da1bc35c19c9be7b6de855166d2e85d7b28f
  languageName: node
  linkType: hard

"postcss-minify-selectors@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-minify-selectors@npm:4.0.2"
  dependencies:
    alphanum-sort: ^1.0.0
    has: ^1.0.0
    postcss: ^7.0.0
    postcss-selector-parser: ^3.0.0
  checksum: a214809b620e50296417838804c3978d5f0a5ddfd48916780d77c1e0348c9ed0baa4b1f3905511b0f06b77340b5378088cc3188517c0848e8b7a53a71ef36c2b
  languageName: node
  linkType: hard

"postcss-modules-extract-imports@npm:^1.2.0":
  version: 1.2.1
  resolution: "postcss-modules-extract-imports@npm:1.2.1"
  dependencies:
    postcss: ^6.0.1
  checksum: 30c4d70da8e8754885facc94ccd235f4d0d4816d1978a31abe6210a66ae8b0b70bc0beefb7f4caa28be9b27077f948126364bbff0eb6feebe150b82a0c10283e
  languageName: node
  linkType: hard

"postcss-modules-local-by-default@npm:^1.2.0":
  version: 1.2.0
  resolution: "postcss-modules-local-by-default@npm:1.2.0"
  dependencies:
    css-selector-tokenizer: ^0.7.0
    postcss: ^6.0.1
  checksum: c8bbe0a9584e0a02339f4143125bf5febbcbfdbabedc33a5f2debdc5b0089f5c238b236101dbf923ea66c11637c0dee8bcf91d1692ed0443762203286b864ea2
  languageName: node
  linkType: hard

"postcss-modules-scope@npm:^1.1.0":
  version: 1.1.0
  resolution: "postcss-modules-scope@npm:1.1.0"
  dependencies:
    css-selector-tokenizer: ^0.7.0
    postcss: ^6.0.1
  checksum: e1b7dd8b1aabb0dc719015352835c6865a5b80ef469cf956749540847b751ccac860d7f0f5659aa2c4b8a484c4a9291098895e5c91c9707e02c7f79a7288297e
  languageName: node
  linkType: hard

"postcss-modules-values@npm:^1.3.0":
  version: 1.3.0
  resolution: "postcss-modules-values@npm:1.3.0"
  dependencies:
    icss-replace-symbols: ^1.1.0
    postcss: ^6.0.1
  checksum: c1d542f71df43ec8b998808ea8de5e74e215a2428e92a8c157da436724aacf246b77440da1cd3d5daae610c875b46e7f8a845b52e1a49afdc37668093de8e3e7
  languageName: node
  linkType: hard

"postcss-normalize-charset@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-normalize-charset@npm:4.0.1"
  dependencies:
    postcss: ^7.0.0
  checksum: f233f48d61eb005da217e5bfa58f4143165cb525ceea2de4fd88e4172a33712e8b63258ffa089c867875a498c408f293a380ea9e6f40076de550d8053f50e5bc
  languageName: node
  linkType: hard

"postcss-normalize-display-values@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-normalize-display-values@npm:4.0.2"
  dependencies:
    cssnano-util-get-match: ^4.0.0
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
  checksum: c5b857ca05f30a3efc6211cdaa5c9306f3eb0dbac141047d451a418d2bfd3e54be0bd4481d61c640096152d3078881a8dc3dec61913ff7f01ab4fc6df1a14732
  languageName: node
  linkType: hard

"postcss-normalize-positions@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-normalize-positions@npm:4.0.2"
  dependencies:
    cssnano-util-get-arguments: ^4.0.0
    has: ^1.0.0
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
  checksum: 291612d0879e6913010937f1193ab56ae1cfd8a274665330ccbedbe72f59c36db3f688b0a3faa4c6689cfd03dff0c27702c6acfce9b1f697a022bfcee3cd4fc4
  languageName: node
  linkType: hard

"postcss-normalize-repeat-style@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-normalize-repeat-style@npm:4.0.2"
  dependencies:
    cssnano-util-get-arguments: ^4.0.0
    cssnano-util-get-match: ^4.0.0
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
  checksum: 2160b2a6fe4f9671ad5d044755f0e04cfb5f255db607505fd4c74e7c806315c9dca914e74bb02f5f768de7b70939359d05c3f9b23ae8f72551d8fdeabf79a1fb
  languageName: node
  linkType: hard

"postcss-normalize-string@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-normalize-string@npm:4.0.2"
  dependencies:
    has: ^1.0.0
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
  checksum: 9d40753ceb4f7854ed690ecd5fe4ea142280b14441dd11e188e573e58af93df293efdc77311f1c599431df785a3bb614dfe4bdacc3081ee3fe8c95916c849b2f
  languageName: node
  linkType: hard

"postcss-normalize-timing-functions@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-normalize-timing-functions@npm:4.0.2"
  dependencies:
    cssnano-util-get-match: ^4.0.0
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
  checksum: 8dfd711f5cdb49b823a92d1cd56d40f66f3686e257804495ef59d5d7f71815b6d19412a1ff25d40971bf6e146b1fa0517a6cc1a4c286b36c5cee6ed08a1952db
  languageName: node
  linkType: hard

"postcss-normalize-unicode@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-normalize-unicode@npm:4.0.1"
  dependencies:
    browserslist: ^4.0.0
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
  checksum: 2b1da17815f8402651a72012fd385b5111e84002baf98b649e0c1fc91298b65bb0e431664f6df8a99b23217259ecec242b169c0f18bf26e727af02eaf475fb07
  languageName: node
  linkType: hard

"postcss-normalize-url@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-normalize-url@npm:4.0.1"
  dependencies:
    is-absolute-url: ^2.0.0
    normalize-url: ^3.0.0
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
  checksum: fcaab832d8b773568197b41406517a9e5fc7704f2fac7185bd0e13b19961e1ce9f1c762e4ffa470de7baa6a82ae8ae5ccf6b1bbeec6e95216d22ce6ab514fe04
  languageName: node
  linkType: hard

"postcss-normalize-whitespace@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-normalize-whitespace@npm:4.0.2"
  dependencies:
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
  checksum: 378a6eadb09ccc5ca2289e8daf98ce7366ae53342c4df7898ef5fae68138884d6c1241493531635458351b2805218bf55ceecae0fd289e5696ab15c78966abbb
  languageName: node
  linkType: hard

"postcss-ordered-values@npm:^4.1.2":
  version: 4.1.2
  resolution: "postcss-ordered-values@npm:4.1.2"
  dependencies:
    cssnano-util-get-arguments: ^4.0.0
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
  checksum: 4a6f6a427a0165e1fa4f04dbe53a88708c73ea23e5b23ce312366ca8d85d83af450154a54f0e5df6c5712f945c180b6a364c3682dc995940b93228bb26658a96
  languageName: node
  linkType: hard

"postcss-prefix-selector@npm:^1.6.0":
  version: 1.14.0
  resolution: "postcss-prefix-selector@npm:1.14.0"
  peerDependencies:
    postcss: 7.x || 8.x
  checksum: 93f01dee193a1187dbedc23c4750d577daeec61ce7b3720f87c9f0f86b26231a14a12e311f7060f95c48424831c58cea231938cad4e48d57a1dc96542ce9bbd2
  languageName: node
  linkType: hard

"postcss-reduce-initial@npm:^4.0.3":
  version: 4.0.3
  resolution: "postcss-reduce-initial@npm:4.0.3"
  dependencies:
    browserslist: ^4.0.0
    caniuse-api: ^3.0.0
    has: ^1.0.0
    postcss: ^7.0.0
  checksum: 5ad1a955cb20f5b1792ff8cc35894621edc23ee77397cc7e9692d269882fb4451655633947e0407fe20bd127d09d0b7e693034c64417bf8bf1034a83c6e71668
  languageName: node
  linkType: hard

"postcss-reduce-transforms@npm:^4.0.2":
  version: 4.0.2
  resolution: "postcss-reduce-transforms@npm:4.0.2"
  dependencies:
    cssnano-util-get-match: ^4.0.0
    has: ^1.0.0
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
  checksum: e6a351d5da7ecf276ddda350635b15bce8e14af08aee1c8a0e8d9c2ab2631eab33b06f3c2f31c6f9c76eedbfc23f356d86da3539e011cde3e335a2cac9d91dc1
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^3.0.0":
  version: 3.1.2
  resolution: "postcss-selector-parser@npm:3.1.2"
  dependencies:
    dot-prop: ^5.2.0
    indexes-of: ^1.0.1
    uniq: ^1.0.1
  checksum: 85b754bf3b5f671cddd75a199589e5b03da114ec119aa4628ab7f35f76134b25296d18a68f745e39780c379d66d3919ae7a1b6129aeec5049cedb9ba4c660803
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^5.0.0":
  version: 5.0.0
  resolution: "postcss-selector-parser@npm:5.0.0"
  dependencies:
    cssesc: ^2.0.0
    indexes-of: ^1.0.1
    uniq: ^1.0.1
  checksum: e49d21455e06d2cb9bf2a615bf3e605e0603c2c430a84c37a34f8baedaf3e8f9d0059a085d3e0483cbfa04c0d4153c7da28e7ac0ada319efdefe407df11dc1d4
  languageName: node
  linkType: hard

"postcss-selector-parser@npm:^6.0.2":
  version: 6.0.9
  resolution: "postcss-selector-parser@npm:6.0.9"
  dependencies:
    cssesc: ^3.0.0
    util-deprecate: ^1.0.2
  checksum: f8161ab4d3e5c76b8467189c6d164ba0f6b6e74677435f29e34caa1df01e052b582b4ae4f7468b2243c4befdd8bdcdb7685542d1b2fca8deae21b3e849c78802
  languageName: node
  linkType: hard

"postcss-svgo@npm:^4.0.3":
  version: 4.0.3
  resolution: "postcss-svgo@npm:4.0.3"
  dependencies:
    postcss: ^7.0.0
    postcss-value-parser: ^3.0.0
    svgo: ^1.0.0
  checksum: 6f5264241193ca3ba748fdf43c88ef692948d2ae38787398dc90089061fed884064ec14ee244fce07f19c419d1b058c77e135407d0932b09e93e528581ce3e10
  languageName: node
  linkType: hard

"postcss-unique-selectors@npm:^4.0.1":
  version: 4.0.1
  resolution: "postcss-unique-selectors@npm:4.0.1"
  dependencies:
    alphanum-sort: ^1.0.0
    postcss: ^7.0.0
    uniqs: ^2.0.0
  checksum: 272eb1fa17d6ea513b5f4d2f694ef30fa690795ce388aef7bf3967fd3bcec7a9a3c8da380e74961ded8d98253a6ed18fb380b29da00e2fe03e74813e7765ea71
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^3.0.0, postcss-value-parser@npm:^3.3.0":
  version: 3.3.1
  resolution: "postcss-value-parser@npm:3.3.1"
  checksum: 62cd26e1cdbcf2dcc6bcedf3d9b409c9027bc57a367ae20d31dd99da4e206f730689471fd70a2abe866332af83f54dc1fa444c589e2381bf7f8054c46209ce16
  languageName: node
  linkType: hard

"postcss-value-parser@npm:^4.0.2, postcss-value-parser@npm:^4.1.0":
  version: 4.2.0
  resolution: "postcss-value-parser@npm:4.2.0"
  checksum: 819ffab0c9d51cf0acbabf8996dffbfafbafa57afc0e4c98db88b67f2094cb44488758f06e5da95d7036f19556a4a732525e84289a425f4f6fd8e412a9d7442f
  languageName: node
  linkType: hard

"postcss@npm:^5.2.17":
  version: 5.2.18
  resolution: "postcss@npm:5.2.18"
  dependencies:
    chalk: ^1.1.3
    js-base64: ^2.1.9
    source-map: ^0.5.6
    supports-color: ^3.2.3
  checksum: 0cb88e7c887b9b55d0362159846ec9fbf330892c5853b0e346929e723d215295ffae48d9a0f219f64f74767f9114802dc1b5cd21c327184f958b7efaa93dd629
  languageName: node
  linkType: hard

"postcss@npm:^6.0.1, postcss@npm:^6.0.23":
  version: 6.0.23
  resolution: "postcss@npm:6.0.23"
  dependencies:
    chalk: ^2.4.1
    source-map: ^0.6.1
    supports-color: ^5.4.0
  checksum: cc6cb2c1dbcdefa6f57a71d67fe535c9e96543298bbe28f9a6a64c4f1e21b6127113890dd4cda8873d3f4e6613a0566b7b4bbb230204f3a9a309190bda065d81
  languageName: node
  linkType: hard

"postcss@npm:^7.0.0, postcss@npm:^7.0.1, postcss@npm:^7.0.14, postcss@npm:^7.0.27, postcss@npm:^7.0.32, postcss@npm:^7.0.36":
  version: 7.0.39
  resolution: "postcss@npm:7.0.39"
  dependencies:
    picocolors: ^0.2.1
    source-map: ^0.6.1
  checksum: 4ac793f506c23259189064bdc921260d869a115a82b5e713973c5af8e94fbb5721a5cc3e1e26840500d7e1f1fa42a209747c5b1a151918a9bc11f0d7ed9048e3
  languageName: node
  linkType: hard

"posthtml-parser@npm:^0.2.0, posthtml-parser@npm:^0.2.1":
  version: 0.2.1
  resolution: "posthtml-parser@npm:0.2.1"
  dependencies:
    htmlparser2: ^3.8.3
    isobject: ^2.1.0
  checksum: 1111cced3ea995de4f72bedace828b733e7eefa953573202e596cac7c82b3ced6cae2849c00f2ed1bb801ff544f4cf85a7b94f5f23392727dc4e0a0b26a8b15f
  languageName: node
  linkType: hard

"posthtml-rename-id@npm:^1.0":
  version: 1.0.12
  resolution: "posthtml-rename-id@npm:1.0.12"
  dependencies:
    escape-string-regexp: 1.0.5
  checksum: 5bfb88f9063e1057c6f5342d7100584cdcb55f4344ed3cfd68db8249fb25cc06f89b048fbf170cfb64c9a771994a2c3e79457f3bcc49988611a59769fc0a3a6b
  languageName: node
  linkType: hard

"posthtml-render@npm:^1.0.5, posthtml-render@npm:^1.0.6":
  version: 1.4.0
  resolution: "posthtml-render@npm:1.4.0"
  checksum: 68c5c85834d57d54bb797ae81a4ab74ad1d87f55e6a327dac9804fbed96214b57d437d7e255e9396184ab976ab7e77aed6efda9315c156ab25ef8ab2c095c16b
  languageName: node
  linkType: hard

"posthtml-svg-mode@npm:^1.0.3":
  version: 1.0.3
  resolution: "posthtml-svg-mode@npm:1.0.3"
  dependencies:
    merge-options: 1.0.1
    posthtml: ^0.9.2
    posthtml-parser: ^0.2.1
    posthtml-render: ^1.0.6
  checksum: a9f88294dd7fe862a360a04d5e003fc250175bcb43f6fbd80f384f9daa6f39877a16026d00b39107a6201abe237fbfb591a0deea3bda19c606d493c96deff640
  languageName: node
  linkType: hard

"posthtml@npm:^0.9.2":
  version: 0.9.2
  resolution: "posthtml@npm:0.9.2"
  dependencies:
    posthtml-parser: ^0.2.0
    posthtml-render: ^1.0.5
  checksum: 1464440239cc8ab745b6682142f509acc3a8837ef01e0398d7f482221030cd06c39f396feb301c4d337c920ce3281788782870c35a11349551c3a418cdc55487
  languageName: node
  linkType: hard

"prelude-ls@npm:~1.1.2":
  version: 1.1.2
  resolution: "prelude-ls@npm:1.1.2"
  checksum: c4867c87488e4a0c233e158e4d0d5565b609b105d75e4c05dc760840475f06b731332eb93cc8c9cecb840aa8ec323ca3c9a56ad7820ad2e63f0261dadcb154e4
  languageName: node
  linkType: hard

"prepend-http@npm:^2.0.0":
  version: 2.0.0
  resolution: "prepend-http@npm:2.0.0"
  checksum: 7694a9525405447662c1ffd352fcb41b6410c705b739b6f4e3a3e21cf5fdede8377890088e8934436b8b17ba55365a615f153960f30877bf0d0392f9e93503ea
  languageName: node
  linkType: hard

"preserve@npm:^0.2.0":
  version: 0.2.0
  resolution: "preserve@npm:0.2.0"
  checksum: dd31d4fd0e6b218cac7178712ae0683c96e6eaa3e5490a37aba6d47095f87c47ffe980a3061ebe72ca07cb2a9a4f1a41bbeecee8944ec77be311c294e05b2e43
  languageName: node
  linkType: hard

"prettier@npm:1.16.3":
  version: 1.16.3
  resolution: "prettier@npm:1.16.3"
  bin:
    prettier: ./bin-prettier.js
  checksum: 12d6161d64bf896646b3d78a5f21bdafae044a3e127bd227e291664398649b0eae9d08d4677e7be9e88bf5d27e8dd8b34c7cbf614b09a165590af6a80a65cd89
  languageName: node
  linkType: hard

"prettier@npm:^1.18.2 || ^2.0.0":
  version: 2.5.1
  resolution: "prettier@npm:2.5.1"
  bin:
    prettier: bin-prettier.js
  checksum: 21b9408476ea1c544b0e45d51ceb94a84789ff92095abb710942d780c862d0daebdb29972d47f6b4d0f7ebbfb0ffbf56cc2cfa3e3e9d1cca54864af185b15b66
  languageName: node
  linkType: hard

"pretty-error@npm:^2.0.2":
  version: 2.1.2
  resolution: "pretty-error@npm:2.1.2"
  dependencies:
    lodash: ^4.17.20
    renderkid: ^2.0.4
  checksum: 16775d06f9a695d17103414d610b1281f9535ee1f2da1ce1e1b9be79584a114aa7eac6dcdcc5ef151756d3c014dfd4ac1c7303ed8016d0cec12437cfdf4021c6
  languageName: node
  linkType: hard

"pretty-format@npm:^23.6.0":
  version: 23.6.0
  resolution: "pretty-format@npm:23.6.0"
  dependencies:
    ansi-regex: ^3.0.0
    ansi-styles: ^3.2.0
  checksum: b668eac9fb19d12cf27098206d587b0be8da9f7fdc56998ace9bad9b6b6f5a5be5004d9fec3c2dc215d4128ef3db901e7329e0e8e081b0732a781bddfa9e2b66
  languageName: node
  linkType: hard

"pretty@npm:2.0.0":
  version: 2.0.0
  resolution: "pretty@npm:2.0.0"
  dependencies:
    condense-newlines: ^0.2.1
    extend-shallow: ^2.0.1
    js-beautify: ^1.6.12
  checksum: 9c41ae0559195af2fb2496d84c6f442843e045d269d4008a6dd336f8372d7481395ed5ab23e5711b6172682c27cb0542e1ab3ca11b38da48f1109c0b701d0ef9
  languageName: node
  linkType: hard

"private@npm:^0.1.8":
  version: 0.1.8
  resolution: "private@npm:0.1.8"
  checksum: a00abd713d25389f6de7294f0e7879b8a5d09a9ec5fd81cc2f21b29d4f9a80ec53bc4222927d3a281d4aadd4cd373d9a28726fca3935921950dc75fd71d1fdbb
  languageName: node
  linkType: hard

"process-nextick-args@npm:~2.0.0":
  version: 2.0.1
  resolution: "process-nextick-args@npm:2.0.1"
  checksum: 1d38588e520dab7cea67cbbe2efdd86a10cc7a074c09657635e34f035277b59fbb57d09d8638346bf7090f8e8ebc070c96fa5fd183b777fff4f5edff5e9466cf
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: bfcce49814f7d172a6e6a14d5fa3ac92cc3d0c3b9feb1279774708a719e19acd673995226351a082a9ae99978254e320ccda4240ddc474ba31a76c79491ca7c3
  languageName: node
  linkType: hard

"progress@npm:^2.0.0":
  version: 2.0.3
  resolution: "progress@npm:2.0.3"
  checksum: f67403fe7b34912148d9252cb7481266a354bd99ce82c835f79070643bb3c6583d10dbcfda4d41e04bbc1d8437e9af0fb1e1f2135727878f5308682a579429b7
  languageName: node
  linkType: hard

"promise-inflight@npm:^1.0.1":
  version: 1.0.1
  resolution: "promise-inflight@npm:1.0.1"
  checksum: 22749483091d2c594261517f4f80e05226d4d5ecc1fc917e1886929da56e22b5718b7f2a75f3807e7a7d471bc3be2907fe92e6e8f373ddf5c64bae35b5af3981
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: ^2.0.2
    retry: ^0.12.0
  checksum: f96a3f6d90b92b568a26f71e966cbbc0f63ab85ea6ff6c81284dc869b41510e6cdef99b6b65f9030f0db422bf7c96652a3fff9f2e8fb4a0f069d8f4430359429
  languageName: node
  linkType: hard

"prompts@npm:^0.1.9":
  version: 0.1.14
  resolution: "prompts@npm:0.1.14"
  dependencies:
    kleur: ^2.0.1
    sisteransi: ^0.1.1
  checksum: a480ceda3bf7d3b2b49eeec0c02296922cc57dc48575caf67fce6eb1baabfbc79a84333c2bc5ca8e5045b005efe5274cd3ee937ede004697b1b74ce8da7fe181
  languageName: node
  linkType: hard

"proto-list@npm:~1.2.1":
  version: 1.2.4
  resolution: "proto-list@npm:1.2.4"
  checksum: 4d4826e1713cbfa0f15124ab0ae494c91b597a3c458670c9714c36e8baddf5a6aad22842776f2f5b137f259c8533e741771445eb8df82e861eea37a6eaba03f7
  languageName: node
  linkType: hard

"proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: 0.2.0
    ipaddr.js: 1.9.1
  checksum: 29c6990ce9364648255454842f06f8c46fcd124d3e6d7c5066df44662de63cdc0bad032e9bf5a3d653ff72141cc7b6019873d685708ac8210c30458ad99f2b74
  languageName: node
  linkType: hard

"prr@npm:~1.0.1":
  version: 1.0.1
  resolution: "prr@npm:1.0.1"
  checksum: 3bca2db0479fd38f8c4c9439139b0c42dcaadcc2fbb7bb8e0e6afaa1383457f1d19aea9e5f961d5b080f1cfc05bfa1fe9e45c97a1d3fd6d421950a73d3108381
  languageName: node
  linkType: hard

"pseudomap@npm:^1.0.2":
  version: 1.0.2
  resolution: "pseudomap@npm:1.0.2"
  checksum: 856c0aae0ff2ad60881168334448e898ad7a0e45fe7386d114b150084254c01e200c957cf378378025df4e052c7890c5bd933939b0e0d2ecfcc1dc2f0b2991f5
  languageName: node
  linkType: hard

"psl@npm:^1.1.28":
  version: 1.8.0
  resolution: "psl@npm:1.8.0"
  checksum: 6150048ed2da3f919478bee8a82f3828303bc0fc730fb015a48f83c9977682c7b28c60ab01425a72d82a2891a1681627aa530a991d50c086b48a3be27744bde7
  languageName: node
  linkType: hard

"public-encrypt@npm:^4.0.0":
  version: 4.0.3
  resolution: "public-encrypt@npm:4.0.3"
  dependencies:
    bn.js: ^4.1.0
    browserify-rsa: ^4.0.0
    create-hash: ^1.1.0
    parse-asn1: ^5.0.0
    randombytes: ^2.0.1
    safe-buffer: ^5.1.2
  checksum: 215d446e43cef021a20b67c1df455e5eea134af0b1f9b8a35f9e850abf32991b0c307327bc5b9bc07162c288d5cdb3d4a783ea6c6640979ed7b5017e3e0c9935
  languageName: node
  linkType: hard

"pump@npm:^2.0.0, pump@npm:^2.0.1":
  version: 2.0.1
  resolution: "pump@npm:2.0.1"
  dependencies:
    end-of-stream: ^1.1.0
    once: ^1.3.1
  checksum: e9f26a17be00810bff37ad0171edb35f58b242487b0444f92fb7d78bc7d61442fa9b9c5bd93a43fd8fd8ddd3cc75f1221f5e04c790f42907e5baab7cf5e2b931
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.0
  resolution: "pump@npm:3.0.0"
  dependencies:
    end-of-stream: ^1.1.0
    once: ^1.3.1
  checksum: e42e9229fba14732593a718b04cb5e1cfef8254544870997e0ecd9732b189a48e1256e4e5478148ecb47c8511dca2b09eae56b4d0aad8009e6fac8072923cfc9
  languageName: node
  linkType: hard

"pumpify@npm:^1.3.3":
  version: 1.5.1
  resolution: "pumpify@npm:1.5.1"
  dependencies:
    duplexify: ^3.6.0
    inherits: ^2.0.3
    pump: ^2.0.0
  checksum: 26ca412ec8d665bd0d5e185c1b8f627728eff603440d75d22a58e421e3c66eaf86ec6fc6a6efc54808ecef65979279fa8e99b109a23ec1fa8d79f37e6978c9bd
  languageName: node
  linkType: hard

"punycode@npm:1.3.2":
  version: 1.3.2
  resolution: "punycode@npm:1.3.2"
  checksum: b8807fd594b1db33335692d1f03e8beeddde6fda7fbb4a2e32925d88d20a3aa4cd8dcc0c109ccaccbd2ba761c208dfaaada83007087ea8bfb0129c9ef1b99ed6
  languageName: node
  linkType: hard

"punycode@npm:^1.2.4":
  version: 1.4.1
  resolution: "punycode@npm:1.4.1"
  checksum: fa6e698cb53db45e4628559e557ddaf554103d2a96a1d62892c8f4032cd3bc8871796cae9eabc1bc700e2b6677611521ce5bb1d9a27700086039965d0cf34518
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0, punycode@npm:^2.1.1":
  version: 2.1.1
  resolution: "punycode@npm:2.1.1"
  checksum: 823bf443c6dd14f669984dea25757b37993f67e8d94698996064035edd43bed8a5a17a9f12e439c2b35df1078c6bec05a6c86e336209eb1061e8025c481168e8
  languageName: node
  linkType: hard

"q@npm:^1.1.2":
  version: 1.5.1
  resolution: "q@npm:1.5.1"
  checksum: 147baa93c805bc1200ed698bdf9c72e9e42c05f96d007e33a558b5fdfd63e5ea130e99313f28efc1783e90e6bdb4e48b67a36fcc026b7b09202437ae88a1fb12
  languageName: node
  linkType: hard

"qs@npm:6.9.6":
  version: 6.9.6
  resolution: "qs@npm:6.9.6"
  checksum: cb6df402bb8a3dbefa4bd46eba0dfca427079baca923e6b8d28a03e6bfb16a5c1dcdb96e69388f9c5813ac8ff17bb8bbca22f2ecd31fe1e344a55cb531b5fabf
  languageName: node
  linkType: hard

"qs@npm:~6.5.2":
  version: 6.5.3
  resolution: "qs@npm:6.5.3"
  checksum: 6f20bf08cabd90c458e50855559539a28d00b2f2e7dddcb66082b16a43188418cb3cb77cbd09268bcef6022935650f0534357b8af9eeb29bf0f27ccb17655692
  languageName: node
  linkType: hard

"query-string@npm:^4.3.2":
  version: 4.3.4
  resolution: "query-string@npm:4.3.4"
  dependencies:
    object-assign: ^4.1.0
    strict-uri-encode: ^1.0.0
  checksum: 3b2bae6a8454cf0edf11cf1aa4d1f920398bbdabc1c39222b9bb92147e746fcd97faf00e56f494728fb66b2961b495ba0fde699d5d3bd06b11472d664b36c6cf
  languageName: node
  linkType: hard

"query-string@npm:^5.0.1":
  version: 5.1.1
  resolution: "query-string@npm:5.1.1"
  dependencies:
    decode-uri-component: ^0.2.0
    object-assign: ^4.1.0
    strict-uri-encode: ^1.0.0
  checksum: 4ac760d9778d413ef5f94f030ed14b1a07a1708dd13fd3bc54f8b9ef7b425942c7577f30de0bf5a7d227ee65a9a0350dfa3a43d1d266880882fb7ce4c434a4dd
  languageName: node
  linkType: hard

"querystring-es3@npm:^0.2.0":
  version: 0.2.1
  resolution: "querystring-es3@npm:0.2.1"
  checksum: 691e8d6b8b157e7cd49ae8e83fcf86de39ab3ba948c25abaa94fba84c0986c641aa2f597770848c64abce290ed17a39c9df6df737dfa7e87c3b63acc7d225d61
  languageName: node
  linkType: hard

"querystring@npm:0.2.0":
  version: 0.2.0
  resolution: "querystring@npm:0.2.0"
  checksum: 8258d6734f19be27e93f601758858c299bdebe71147909e367101ba459b95446fbe5b975bf9beb76390156a592b6f4ac3a68b6087cea165c259705b8b4e56a69
  languageName: node
  linkType: hard

"querystringify@npm:^2.1.1":
  version: 2.2.0
  resolution: "querystringify@npm:2.2.0"
  checksum: 5641ea231bad7ef6d64d9998faca95611ed4b11c2591a8cae741e178a974f6a8e0ebde008475259abe1621cb15e692404e6b6626e927f7b849d5c09392604b15
  languageName: node
  linkType: hard

"randomatic@npm:^3.0.0":
  version: 3.1.1
  resolution: "randomatic@npm:3.1.1"
  dependencies:
    is-number: ^4.0.0
    kind-of: ^6.0.0
    math-random: ^1.0.1
  checksum: 1952baed71801d3698fe84f3ab01e25ea124fc20ce91e133aa1981268c1347647f9ae1fdc62389db2411ebdad61c0f7cea0ce840dee260ad2adadfcf27299018
  languageName: node
  linkType: hard

"randombytes@npm:^2.0.0, randombytes@npm:^2.0.1, randombytes@npm:^2.0.5, randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: ^5.1.0
  checksum: d779499376bd4cbb435ef3ab9a957006c8682f343f14089ed5f27764e4645114196e75b7f6abf1cbd84fd247c0cb0651698444df8c9bf30e62120fbbc52269d6
  languageName: node
  linkType: hard

"randomfill@npm:^1.0.3":
  version: 1.0.4
  resolution: "randomfill@npm:1.0.4"
  dependencies:
    randombytes: ^2.0.5
    safe-buffer: ^5.1.0
  checksum: 33734bb578a868d29ee1b8555e21a36711db084065d94e019a6d03caa67debef8d6a1bfd06a2b597e32901ddc761ab483a85393f0d9a75838f1912461d4dbfc7
  languageName: node
  linkType: hard

"range-parser@npm:^1.2.1, range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 0a268d4fea508661cf5743dfe3d5f47ce214fd6b7dec1de0da4d669dd4ef3d2144468ebe4179049eff253d9d27e719c88dae55be64f954e80135a0cada804ec9
  languageName: node
  linkType: hard

"raw-body@npm:2.4.2":
  version: 2.4.2
  resolution: "raw-body@npm:2.4.2"
  dependencies:
    bytes: 3.1.1
    http-errors: 1.8.1
    iconv-lite: 0.4.24
    unpipe: 1.0.0
  checksum: c6f8d6a75c65c0a047f888cb29efc97f60fb36e950ba2cb31fefce694f98186e844a03367920faa7dc5bffaf33df08aee0b9dd935280e366439fa6492a5b163e
  languageName: node
  linkType: hard

"raw-loader@npm:~0.5.1":
  version: 0.5.1
  resolution: "raw-loader@npm:0.5.1"
  checksum: 8051ec0b804ee72fbeee9a0f6183df8c0f764ba23a78ed5229c981cfb3a560dabc7926670fb0125b1c3831998d053ae39d578f3fb46187538226ceedad8cf1ab
  languageName: node
  linkType: hard

"read-pkg-up@npm:^1.0.1":
  version: 1.0.1
  resolution: "read-pkg-up@npm:1.0.1"
  dependencies:
    find-up: ^1.0.0
    read-pkg: ^1.0.0
  checksum: d18399a0f46e2da32beb2f041edd0cda49d2f2cc30195a05c759ef3ed9b5e6e19ba1ad1bae2362bdec8c6a9f2c3d18f4d5e8c369e808b03d498d5781cb9122c7
  languageName: node
  linkType: hard

"read-pkg@npm:^1.0.0":
  version: 1.1.0
  resolution: "read-pkg@npm:1.1.0"
  dependencies:
    load-json-file: ^1.0.0
    normalize-package-data: ^2.3.2
    path-type: ^1.0.0
  checksum: a0f5d5e32227ec8e6a028dd5c5134eab229768dcb7a5d9a41a284ed28ad4b9284fecc47383dc1593b5694f4de603a7ffaee84b738956b9b77e0999567485a366
  languageName: node
  linkType: hard

"read-pkg@npm:^5.0.0":
  version: 5.2.0
  resolution: "read-pkg@npm:5.2.0"
  dependencies:
    "@types/normalize-package-data": ^2.4.0
    normalize-package-data: ^2.5.0
    parse-json: ^5.0.0
    type-fest: ^0.6.0
  checksum: eb696e60528b29aebe10e499ba93f44991908c57d70f2d26f369e46b8b9afc208ef11b4ba64f67630f31df8b6872129e0a8933c8c53b7b4daf0eace536901222
  languageName: node
  linkType: hard

"readable-stream@npm:1 || 2, readable-stream@npm:^2.0.0, readable-stream@npm:^2.0.1, readable-stream@npm:^2.0.2, readable-stream@npm:^2.1.5, readable-stream@npm:^2.2.2, readable-stream@npm:^2.3.3, readable-stream@npm:^2.3.6, readable-stream@npm:~2.3.6":
  version: 2.3.7
  resolution: "readable-stream@npm:2.3.7"
  dependencies:
    core-util-is: ~1.0.0
    inherits: ~2.0.3
    isarray: ~1.0.0
    process-nextick-args: ~2.0.0
    safe-buffer: ~5.1.1
    string_decoder: ~1.1.1
    util-deprecate: ~1.0.1
  checksum: e4920cf7549a60f8aaf694d483a0e61b2a878b969d224f89b3bc788b8d920075132c4b55a7494ee944c7b6a9a0eada28a7f6220d80b0312ece70bbf08eeca755
  languageName: node
  linkType: hard

"readable-stream@npm:^3.0.6, readable-stream@npm:^3.1.1, readable-stream@npm:^3.6.0":
  version: 3.6.0
  resolution: "readable-stream@npm:3.6.0"
  dependencies:
    inherits: ^2.0.3
    string_decoder: ^1.1.1
    util-deprecate: ^1.0.1
  checksum: d4ea81502d3799439bb955a3a5d1d808592cf3133350ed352aeaa499647858b27b1c4013984900238b0873ec8d0d8defce72469fb7a83e61d53f5ad61cb80dc8
  languageName: node
  linkType: hard

"readdirp@npm:^2.2.1":
  version: 2.2.1
  resolution: "readdirp@npm:2.2.1"
  dependencies:
    graceful-fs: ^4.1.11
    micromatch: ^3.1.10
    readable-stream: ^2.0.2
  checksum: 3879b20f1a871e0e004a14fbf1776e65ee0b746a62f5a416010808b37c272ac49b023c47042c7b1e281cba75a449696635bc64c397ed221ea81d853a8f2ed79a
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: ^2.2.1
  checksum: 1ced032e6e45670b6d7352d71d21ce7edf7b9b928494dcaba6f11fba63180d9da6cd7061ebc34175ffda6ff529f481818c962952004d273178acd70f7059b320
  languageName: node
  linkType: hard

"realpath-native@npm:^1.0.0":
  version: 1.1.0
  resolution: "realpath-native@npm:1.1.0"
  dependencies:
    util.promisify: ^1.0.0
  checksum: 75ef0595dea6186384b785a9e0993c58ec604f8be2e39b602fec6d7837c7f770af4a4eb3c81f864a7d81c518a7167a6eaabbc7695b7a88c56e1ef04b91c1d586
  languageName: node
  linkType: hard

"regenerate-unicode-properties@npm:^10.0.1":
  version: 10.0.1
  resolution: "regenerate-unicode-properties@npm:10.0.1"
  dependencies:
    regenerate: ^1.4.2
  checksum: 1b638b7087d8143e5be3e20e2cda197ea0440fa0bc2cc49646b2f50c5a2b1acdc54b21e4215805a5a2dd487c686b2291accd5ad00619534098d2667e76247754
  languageName: node
  linkType: hard

"regenerate@npm:^1.4.2":
  version: 1.4.2
  resolution: "regenerate@npm:1.4.2"
  checksum: 3317a09b2f802da8db09aa276e469b57a6c0dd818347e05b8862959c6193408242f150db5de83c12c3fa99091ad95fb42a6db2c3329bfaa12a0ea4cbbeb30cb0
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.11.0":
  version: 0.11.1
  resolution: "regenerator-runtime@npm:0.11.1"
  checksum: 3c97bd2c7b2b3247e6f8e2147a002eb78c995323732dad5dc70fac8d8d0b758d0295e7015b90d3d444446ae77cbd24b9f9123ec3a77018e81d8999818301b4f4
  languageName: node
  linkType: hard

"regenerator-runtime@npm:^0.13.4":
  version: 0.13.9
  resolution: "regenerator-runtime@npm:0.13.9"
  checksum: 65ed455fe5afd799e2897baf691ca21c2772e1a969d19bb0c4695757c2d96249eb74ee3553ea34a91062b2a676beedf630b4c1551cc6299afb937be1426ec55e
  languageName: node
  linkType: hard

"regenerator-transform@npm:^0.14.2":
  version: 0.14.5
  resolution: "regenerator-transform@npm:0.14.5"
  dependencies:
    "@babel/runtime": ^7.8.4
  checksum: a467a3b652b4ec26ff964e9c5f1817523a73fc44cb928b8d21ff11aebeac5d10a84d297fe02cea9f282bcec81a0b0d562237da69ef0f40a0160b30a4fa98bc94
  languageName: node
  linkType: hard

"regex-cache@npm:^0.4.2":
  version: 0.4.4
  resolution: "regex-cache@npm:0.4.4"
  dependencies:
    is-equal-shallow: ^0.1.3
  checksum: fdaf756fbd7048a34dc454ab6da678828148d34ac8e3701636bd747fd9d2df1191f6f80669f7ce7c4173e4631a92d3943ce4dc2a43a1acfa7c5308cdd49a1587
  languageName: node
  linkType: hard

"regex-not@npm:^1.0.0, regex-not@npm:^1.0.2":
  version: 1.0.2
  resolution: "regex-not@npm:1.0.2"
  dependencies:
    extend-shallow: ^3.0.2
    safe-regex: ^1.1.0
  checksum: 3081403de79559387a35ef9d033740e41818a559512668cef3d12da4e8a29ef34ee13c8ed1256b07e27ae392790172e8a15c8a06b72962fd4550476cde3d8f77
  languageName: node
  linkType: hard

"regexp.prototype.flags@npm:^1.2.0":
  version: 1.4.1
  resolution: "regexp.prototype.flags@npm:1.4.1"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
  checksum: 77944a3ea5ae84f391fa80bff9babfedc47eadc9dc38e282b5fd746368fb787deec89c68ce3114195bf6b5782b160280a278b62d41ccc6e125afab1a7f816de8
  languageName: node
  linkType: hard

"regexpp@npm:^1.0.1":
  version: 1.1.0
  resolution: "regexpp@npm:1.1.0"
  checksum: 78c5b75a63fce24447aa26184150779c6c1552e0e468270e142a2361d8c6bd6ad26cbfa67ad869f98541d104b10fa49506e30cd98015bf57e21ffb7495130d7c
  languageName: node
  linkType: hard

"regexpp@npm:^2.0.1":
  version: 2.0.1
  resolution: "regexpp@npm:2.0.1"
  checksum: 1f41cf80ac08514c6665812e3dcc0673569431d3285db27053f8b237a758992fb55d6ddfbc264db399ff4f7a7db432900ca3a029daa28a75e0436231872091b1
  languageName: node
  linkType: hard

"regexpu-core@npm:^5.0.1":
  version: 5.0.1
  resolution: "regexpu-core@npm:5.0.1"
  dependencies:
    regenerate: ^1.4.2
    regenerate-unicode-properties: ^10.0.1
    regjsgen: ^0.6.0
    regjsparser: ^0.8.2
    unicode-match-property-ecmascript: ^2.0.0
    unicode-match-property-value-ecmascript: ^2.0.0
  checksum: 6151a9700dad512fadb5564ad23246d54c880eb9417efa5e5c3658b910c1ff894d622dfd159af2ed527ffd44751bfe98682ae06c717155c254d8e2b4bab62785
  languageName: node
  linkType: hard

"regjsgen@npm:^0.6.0":
  version: 0.6.0
  resolution: "regjsgen@npm:0.6.0"
  checksum: c5158ebd735e75074e41292ade1ff05d85566d205426cc61501e360c450a63baced8512ee3ae238e5c0a0e42969563c7875b08fa69d6f0402daf36bcb3e4d348
  languageName: node
  linkType: hard

"regjsparser@npm:^0.8.2":
  version: 0.8.4
  resolution: "regjsparser@npm:0.8.4"
  dependencies:
    jsesc: ~0.5.0
  bin:
    regjsparser: bin/parser
  checksum: d069b932491761cda127ce11f6bd2729c3b1b394a35200ec33f1199e937423db28ceb86cf33f0a97c76ecd7c0f8db996476579eaf0d80a1f74c1934f4ca8b27a
  languageName: node
  linkType: hard

"relateurl@npm:0.2.x":
  version: 0.2.7
  resolution: "relateurl@npm:0.2.7"
  checksum: 5891e792eae1dfc3da91c6fda76d6c3de0333a60aa5ad848982ebb6dccaa06e86385fb1235a1582c680a3d445d31be01c6bfc0804ebbcab5aaf53fa856fde6b6
  languageName: node
  linkType: hard

"remove-trailing-separator@npm:^1.0.1":
  version: 1.1.0
  resolution: "remove-trailing-separator@npm:1.1.0"
  checksum: d3c20b5a2d987db13e1cca9385d56ecfa1641bae143b620835ac02a6b70ab88f68f117a0021838db826c57b31373d609d52e4f31aca75fc490c862732d595419
  languageName: node
  linkType: hard

"renderkid@npm:^2.0.4":
  version: 2.0.7
  resolution: "renderkid@npm:2.0.7"
  dependencies:
    css-select: ^4.1.3
    dom-converter: ^0.2.0
    htmlparser2: ^6.1.0
    lodash: ^4.17.21
    strip-ansi: ^3.0.1
  checksum: d3d7562531fb8104154d4aa6aa977707783616318014088378a6c5bbc36318ada9289543d380ede707e531b7f5b96229e87d1b8944f675e5ec3686e62692c7c7
  languageName: node
  linkType: hard

"repeat-element@npm:^1.1.2":
  version: 1.1.4
  resolution: "repeat-element@npm:1.1.4"
  checksum: 1edd0301b7edad71808baad226f0890ba709443f03a698224c9ee4f2494c317892dc5211b2ba8cbea7194a9ddbcac01e283bd66de0467ab24ee1fc1a3711d8a9
  languageName: node
  linkType: hard

"repeat-string@npm:^1.5.2, repeat-string@npm:^1.6.1":
  version: 1.6.1
  resolution: "repeat-string@npm:1.6.1"
  checksum: 1b809fc6db97decdc68f5b12c4d1a671c8e3f65ec4a40c238bc5200e44e85bcc52a54f78268ab9c29fcf5fe4f1343e805420056d1f30fa9a9ee4c2d93e3cc6c0
  languageName: node
  linkType: hard

"repeating@npm:^2.0.0":
  version: 2.0.1
  resolution: "repeating@npm:2.0.1"
  dependencies:
    is-finite: ^1.0.0
  checksum: d2db0b69c5cb0c14dd750036e0abcd6b3c3f7b2da3ee179786b755cf737ca15fa0fff417ca72de33d6966056f4695440e680a352401fc02c95ade59899afbdd0
  languageName: node
  linkType: hard

"request-promise-core@npm:1.1.4":
  version: 1.1.4
  resolution: "request-promise-core@npm:1.1.4"
  dependencies:
    lodash: ^4.17.19
  peerDependencies:
    request: ^2.34
  checksum: c798bafd552961e36fbf5023b1d081e81c3995ab390f1bc8ef38a711ba3fe4312eb94dbd61887073d7356c3499b9380947d7f62faa805797c0dc50f039425699
  languageName: node
  linkType: hard

"request-promise-native@npm:^1.0.5, request-promise-native@npm:^1.0.7":
  version: 1.0.9
  resolution: "request-promise-native@npm:1.0.9"
  dependencies:
    request-promise-core: 1.1.4
    stealthy-require: ^1.1.1
    tough-cookie: ^2.3.3
  peerDependencies:
    request: ^2.34
  checksum: 3e2c694eefac88cb20beef8911ad57a275ab3ccbae0c4ca6c679fffb09d5fd502458aab08791f0814ca914b157adab2d4e472597c97a73be702918e41725ed69
  languageName: node
  linkType: hard

"request@npm:^2.87.0":
  version: 2.88.2
  resolution: "request@npm:2.88.2"
  dependencies:
    aws-sign2: ~0.7.0
    aws4: ^1.8.0
    caseless: ~0.12.0
    combined-stream: ~1.0.6
    extend: ~3.0.2
    forever-agent: ~0.6.1
    form-data: ~2.3.2
    har-validator: ~5.1.3
    http-signature: ~1.2.0
    is-typedarray: ~1.0.0
    isstream: ~0.1.2
    json-stringify-safe: ~5.0.1
    mime-types: ~2.1.19
    oauth-sign: ~0.9.0
    performance-now: ^2.1.0
    qs: ~6.5.2
    safe-buffer: ^5.1.2
    tough-cookie: ~2.5.0
    tunnel-agent: ^0.6.0
    uuid: ^3.3.2
  checksum: 4e112c087f6eabe7327869da2417e9d28fcd0910419edd2eb17b6acfc4bfa1dad61954525949c228705805882d8a98a86a0ea12d7f739c01ee92af7062996983
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: fb47e70bf0001fdeabdc0429d431863e9475e7e43ea5f94ad86503d918423c1543361cc5166d713eaa7029dd7a3d34775af04764bebff99ef413111a5af18c80
  languageName: node
  linkType: hard

"require-main-filename@npm:^1.0.1":
  version: 1.0.1
  resolution: "require-main-filename@npm:1.0.1"
  checksum: 1fef30754da961f4e13c450c3eb60c7ae898a529c6ad6fa708a70bd2eed01564ceb299187b2899f5562804d797a059f39a5789884d0ac7b7ae1defc68fba4abf
  languageName: node
  linkType: hard

"require-main-filename@npm:^2.0.0":
  version: 2.0.0
  resolution: "require-main-filename@npm:2.0.0"
  checksum: e9e294695fea08b076457e9ddff854e81bffbe248ed34c1eec348b7abbd22a0d02e8d75506559e2265e96978f3c4720bd77a6dad84755de8162b357eb6c778c7
  languageName: node
  linkType: hard

"require-uncached@npm:^1.0.3":
  version: 1.0.3
  resolution: "require-uncached@npm:1.0.3"
  dependencies:
    caller-path: ^0.1.0
    resolve-from: ^1.0.0
  checksum: ace5261d38072130d1fefcfe9662b0d038fe1e38988a801be3e90fbfcab9f6786eeadcf53ac36d6d81b676b29649c7dc5719be0ee571f63058f842838d704bee
  languageName: node
  linkType: hard

"requires-port@npm:^1.0.0":
  version: 1.0.0
  resolution: "requires-port@npm:1.0.0"
  checksum: eee0e303adffb69be55d1a214e415cf42b7441ae858c76dfc5353148644f6fd6e698926fc4643f510d5c126d12a705e7c8ed7e38061113bdf37547ab356797ff
  languageName: node
  linkType: hard

"reselect@npm:^3.0.1":
  version: 3.0.1
  resolution: "reselect@npm:3.0.1"
  checksum: c7ce544bae92db2dc6e768bc2dcc1248f9a21ada22ab5906c5e3c472e1dbc5f080889e5a715b8a2535e652d74c599dd090f5a68bcbe7e6f88053b7599ef4e8d3
  languageName: node
  linkType: hard

"resize-observer-polyfill@npm:^1.5.0":
  version: 1.5.1
  resolution: "resize-observer-polyfill@npm:1.5.1"
  checksum: 57e7f79489867b00ba43c9c051524a5c8f162a61d5547e99333549afc23e15c44fd43f2f318ea0261ea98c0eb3158cca261e6f48d66e1ed1cd1f340a43977094
  languageName: node
  linkType: hard

"resolve-cwd@npm:^2.0.0":
  version: 2.0.0
  resolution: "resolve-cwd@npm:2.0.0"
  dependencies:
    resolve-from: ^3.0.0
  checksum: e7c16880c460656e77f102d537a6dc82b3657d9173697cd6ea82ffce37df96f6c1fc79d0bb35fd73fff8871ac13f21b4396958b5f0a13e5b99c97d69f5e319fa
  languageName: node
  linkType: hard

"resolve-from@npm:^1.0.0":
  version: 1.0.1
  resolution: "resolve-from@npm:1.0.1"
  checksum: 10134654dd6e758d4a4ad60acf69a90731673058a1a96068afc5f2ee84ac373df4d0237e0f052b5c81cc076273213ed50d228fc09723e0840c5c61ea37eb8854
  languageName: node
  linkType: hard

"resolve-from@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-from@npm:3.0.0"
  checksum: fff9819254d2d62b57f74e5c2ca9c0bdd425ca47287c4d801bc15f947533148d858229ded7793b0f59e61e49e782fffd6722048add12996e1bd4333c29669062
  languageName: node
  linkType: hard

"resolve-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "resolve-from@npm:4.0.0"
  checksum: f4ba0b8494846a5066328ad33ef8ac173801a51739eb4d63408c847da9a2e1c1de1e6cbbf72699211f3d13f8fc1325648b169bd15eb7da35688e30a5fb0e4a7f
  languageName: node
  linkType: hard

"resolve-url@npm:^0.2.1":
  version: 0.2.1
  resolution: "resolve-url@npm:0.2.1"
  checksum: 7b7035b9ed6e7bc7d289e90aef1eab5a43834539695dac6416ca6e91f1a94132ae4796bbd173cdacfdc2ade90b5f38a3fb6186bebc1b221cd157777a23b9ad14
  languageName: node
  linkType: hard

"resolve@npm:1.1.7":
  version: 1.1.7
  resolution: "resolve@npm:1.1.7"
  checksum: afd20873fbde7641c9125efe3f940c2a99f6b1f90f1b7b743e744bdaac1cb105b2e4e0317bcc052ed7e31d57afa86b394a4dc9a1b33a297977be134fdf0250ab
  languageName: node
  linkType: hard

"resolve@npm:^1.10.0, resolve@npm:^1.12.0, resolve@npm:^1.14.2, resolve@npm:^1.3.2, resolve@npm:^1.4.0":
  version: 1.22.0
  resolution: "resolve@npm:1.22.0"
  dependencies:
    is-core-module: ^2.8.1
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: a2d14cc437b3a23996f8c7367eee5c7cf8149c586b07ca2ae00e96581ce59455555a1190be9aa92154785cf9f2042646c200d0e00e0bbd2b8a995a93a0ed3e4e
  languageName: node
  linkType: hard

"resolve@patch:resolve@1.1.7#~builtin<compat/resolve>":
  version: 1.1.7
  resolution: "resolve@patch:resolve@npm%3A1.1.7#~builtin<compat/resolve>::version=1.1.7&hash=07638b"
  checksum: e9dbca78600ae56835c43a09f1276876c883e4b4bbd43e2683fa140671519d2bdebeb1c1576ca87c8c508ae2987b3ec481645ac5d3054b0f23254cfc1ce49942
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.10.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.12.0#~builtin<compat/resolve>, resolve@patch:resolve@^1.14.2#~builtin<compat/resolve>, resolve@patch:resolve@^1.3.2#~builtin<compat/resolve>, resolve@patch:resolve@^1.4.0#~builtin<compat/resolve>":
  version: 1.22.0
  resolution: "resolve@patch:resolve@npm%3A1.22.0#~builtin<compat/resolve>::version=1.22.0&hash=07638b"
  dependencies:
    is-core-module: ^2.8.1
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: c79ecaea36c872ee4a79e3db0d3d4160b593f2ca16e031d8283735acd01715a203607e9ded3f91f68899c2937fa0d49390cddbe0fb2852629212f3cda283f4a7
  languageName: node
  linkType: hard

"restore-cursor@npm:^2.0.0":
  version: 2.0.0
  resolution: "restore-cursor@npm:2.0.0"
  dependencies:
    onetime: ^2.0.0
    signal-exit: ^3.0.2
  checksum: 482e13d02d834b6e5e3aa90304a8b5e840775d6f06916cc92a50038adf9f098dcc72405b567da8a37e137ae40ad3e31896fa3136ae62f7a426c2fbf53d036536
  languageName: node
  linkType: hard

"ret@npm:~0.1.10":
  version: 0.1.15
  resolution: "ret@npm:0.1.15"
  checksum: d76a9159eb8c946586567bd934358dfc08a36367b3257f7a3d7255fdd7b56597235af23c6afa0d7f0254159e8051f93c918809962ebd6df24ca2a83dbe4d4151
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 623bd7d2e5119467ba66202d733ec3c2e2e26568074923bc0585b6b99db14f357e79bdedb63cab56cec47491c4a0da7e6021a7465ca6dc4f481d3898fdd3158c
  languageName: node
  linkType: hard

"rgb-regex@npm:^1.0.1":
  version: 1.0.1
  resolution: "rgb-regex@npm:1.0.1"
  checksum: b270ce8bc14782d2d21d3184c1e6c65b465476d8f03e72b93ef57c95710a452b2fe280e1d516c88873aec06efd7f71373e673f114b9d99f3a4f9a0393eb00126
  languageName: node
  linkType: hard

"rgba-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "rgba-regex@npm:1.0.0"
  checksum: 7f2cd271572700faea50753d82524cb2b98f17a5b9722965c7076f6cd674fe545f28145b7ef2cccabc9eca2475c793db16862cd5e7b3784a9f4b8d6496431057
  languageName: node
  linkType: hard

"rimraf@npm:2.6.3, rimraf@npm:~2.6.2":
  version: 2.6.3
  resolution: "rimraf@npm:2.6.3"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: ./bin.js
  checksum: 3ea587b981a19016297edb96d1ffe48af7e6af69660e3b371dbfc73722a73a0b0e9be5c88089fbeeb866c389c1098e07f64929c7414290504b855f54f901ab10
  languageName: node
  linkType: hard

"rimraf@npm:^2.5.4, rimraf@npm:^2.6.1, rimraf@npm:^2.6.2, rimraf@npm:^2.6.3":
  version: 2.7.1
  resolution: "rimraf@npm:2.7.1"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: ./bin.js
  checksum: cdc7f6eacb17927f2a075117a823e1c5951792c6498ebcce81ca8203454a811d4cf8900314154d3259bb8f0b42ab17f67396a8694a54cae3283326e57ad250cd
  languageName: node
  linkType: hard

"rimraf@npm:^3.0.2":
  version: 3.0.2
  resolution: "rimraf@npm:3.0.2"
  dependencies:
    glob: ^7.1.3
  bin:
    rimraf: bin.js
  checksum: 87f4164e396f0171b0a3386cc1877a817f572148ee13a7e113b238e48e8a9f2f31d009a92ec38a591ff1567d9662c6b67fd8818a2dbbaed74bc26a87a2a4a9a0
  languageName: node
  linkType: hard

"ripemd160@npm:^2.0.0, ripemd160@npm:^2.0.1":
  version: 2.0.2
  resolution: "ripemd160@npm:2.0.2"
  dependencies:
    hash-base: ^3.0.0
    inherits: ^2.0.1
  checksum: 006accc40578ee2beae382757c4ce2908a826b27e2b079efdcd2959ee544ddf210b7b5d7d5e80467807604244e7388427330f5c6d4cd61e6edaddc5773ccc393
  languageName: node
  linkType: hard

"rsvp@npm:^3.3.3":
  version: 3.6.2
  resolution: "rsvp@npm:3.6.2"
  checksum: 08504ea7ab3dba0349ff820011a460da69de08edf7149ee672f4511310ee4bd3767bfa83b6db019fa99b144125e1e93e6fba122d75a702a005360393f4352864
  languageName: node
  linkType: hard

"run-async@npm:^2.2.0":
  version: 2.4.1
  resolution: "run-async@npm:2.4.1"
  checksum: a2c88aa15df176f091a2878eb840e68d0bdee319d8d97bbb89112223259cebecb94bc0defd735662b83c2f7a30bed8cddb7d1674eb48ae7322dc602b22d03797
  languageName: node
  linkType: hard

"run-queue@npm:^1.0.0, run-queue@npm:^1.0.3":
  version: 1.0.3
  resolution: "run-queue@npm:1.0.3"
  dependencies:
    aproba: ^1.1.1
  checksum: c4541e18b5e056af60f398f2f1b3d89aae5c093d1524bf817c5ee68bcfa4851ad9976f457a9aea135b1d0d72ee9a91c386e3d136bcd95b699c367cd09c70be53
  languageName: node
  linkType: hard

"runjs@npm:^4.3.2":
  version: 4.4.1
  resolution: "runjs@npm:4.4.1"
  dependencies:
    chalk: 2.3.0
    lodash.padend: 4.6.1
    microcli: 1.3.3
    omelette: 0.4.5
  bin:
    run: bin/run.js
  checksum: f553d22af14023382019e68de1f03ed4dcf5852d3cfd0bbff0f550cb164a79360e8d0202121d85b1253ae23b6b8834a934ae436517d3a517b74aa6c7b62ffb1f
  languageName: node
  linkType: hard

"rx-lite-aggregates@npm:^4.0.8":
  version: 4.0.8
  resolution: "rx-lite-aggregates@npm:4.0.8"
  dependencies:
    rx-lite: "*"
  checksum: e25fde5fa17c2160f3f2948e13096b3e20992ab732786c2b9103a092c9251d65d8b262a8d98a0d63317655fbbf95cd428a400cbeecdd4e58e9c2c11fd8b709d2
  languageName: node
  linkType: hard

"rx-lite@npm:*, rx-lite@npm:^4.0.8":
  version: 4.0.8
  resolution: "rx-lite@npm:4.0.8"
  checksum: 9caeaa6d6dbb1256eaf0b5207a90c3d57d79fc13ddc8ca7303847c157259b1d5913a4408ea37b7b2799a00c0ec7ec3925b2612198103387e471e0b4f8ca6f8b2
  languageName: node
  linkType: hard

"rxjs@npm:^6.4.0":
  version: 6.6.7
  resolution: "rxjs@npm:6.6.7"
  dependencies:
    tslib: ^1.9.0
  checksum: bc334edef1bb8bbf56590b0b25734ba0deaf8825b703256a93714308ea36dff8a11d25533671adf8e104e5e8f256aa6fdfe39b2e248cdbd7a5f90c260acbbd1b
  languageName: node
  linkType: hard

"safe-buffer@npm:5.1.2, safe-buffer@npm:~5.1.0, safe-buffer@npm:~5.1.1":
  version: 5.1.2
  resolution: "safe-buffer@npm:5.1.2"
  checksum: f2f1f7943ca44a594893a852894055cf619c1fbcb611237fc39e461ae751187e7baf4dc391a72125e0ac4fb2d8c5c0b3c71529622e6a58f46b960211e704903c
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:>=5.1.0, safe-buffer@npm:^5.0.1, safe-buffer@npm:^5.1.0, safe-buffer@npm:^5.1.1, safe-buffer@npm:^5.1.2, safe-buffer@npm:^5.2.0, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"safe-regex@npm:^1.1.0":
  version: 1.1.0
  resolution: "safe-regex@npm:1.1.0"
  dependencies:
    ret: ~0.1.10
  checksum: 9a8bba57c87a841f7997b3b951e8e403b1128c1a4fd1182f40cc1a20e2d490593d7c2a21030fadfea320c8e859219019e136f678c6689ed5960b391b822f01d5
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0, safer-buffer@npm:^2.0.2, safer-buffer@npm:^2.1.0, safer-buffer@npm:~2.1.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: cab8f25ae6f1434abee8d80023d7e72b598cf1327164ddab31003c51215526801e40b66c5e65d658a0af1e9d6478cadcb4c745f4bd6751f97d8644786c0978b0
  languageName: node
  linkType: hard

"sane@npm:^2.0.0":
  version: 2.5.2
  resolution: "sane@npm:2.5.2"
  dependencies:
    anymatch: ^2.0.0
    capture-exit: ^1.2.0
    exec-sh: ^0.2.0
    fb-watchman: ^2.0.0
    fsevents: ^1.2.3
    micromatch: ^3.1.4
    minimist: ^1.1.1
    walker: ~1.0.5
    watch: ~0.18.0
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    sane: ./src/cli.js
  checksum: 814d11e7728821aad2ca12a090825ca7ce94440c6e137fd1205af511563c26e5d1fd0fcb208021bb66deaee0336f57c1c5c92d9b72ecafc78a20c6d9a22ce3b3
  languageName: node
  linkType: hard

"sass-loader@npm:^7.1.0":
  version: 7.3.1
  resolution: "sass-loader@npm:7.3.1"
  dependencies:
    clone-deep: ^4.0.1
    loader-utils: ^1.0.1
    neo-async: ^2.5.0
    pify: ^4.0.1
    semver: ^6.3.0
  peerDependencies:
    webpack: ^3.0.0 || ^4.0.0
  checksum: 9e7b438cbc6197970f8db7973de5904c92d6914ace03eee158b5d8a07fdc611fb97105d8fa15edea13727ec2f01cecba708662a04a70a4bfe73a7ec5d6266f8d
  languageName: node
  linkType: hard

"sass@npm:^1.27.1":
  version: 1.49.7
  resolution: "sass@npm:1.49.7"
  dependencies:
    chokidar: ">=3.0.0 <4.0.0"
    immutable: ^4.0.0
    source-map-js: ">=0.6.2 <2.0.0"
  bin:
    sass: sass.js
  checksum: 514d1abff594aa56afdc9eb7e40d1cbd9cf1ed6954c4e0ef494266d18965151c6f936e137cd118a138f7677a7bb98f2bbf2d1a485a3f5f66c78be0007506cc9b
  languageName: node
  linkType: hard

"sax@npm:^1.2.4, sax@npm:~1.2.4":
  version: 1.2.4
  resolution: "sax@npm:1.2.4"
  checksum: d3df7d32b897a2c2f28e941f732c71ba90e27c24f62ee918bd4d9a8cfb3553f2f81e5493c7f0be94a11c1911b643a9108f231dd6f60df3fa9586b5d2e3e9e1fe
  languageName: node
  linkType: hard

"schema-utils@npm:^0.4.4":
  version: 0.4.7
  resolution: "schema-utils@npm:0.4.7"
  dependencies:
    ajv: ^6.1.0
    ajv-keywords: ^3.1.0
  checksum: acee0b7aee127374099846114ee01e3e0eec057e27f8451b2dbdfa43f17ea42ed1e6af876f2a28f5212cb5adef263f99661d0475208417226e5c83c648235b0e
  languageName: node
  linkType: hard

"schema-utils@npm:^1.0.0":
  version: 1.0.0
  resolution: "schema-utils@npm:1.0.0"
  dependencies:
    ajv: ^6.1.0
    ajv-errors: ^1.0.0
    ajv-keywords: ^3.1.0
  checksum: e8273b4f6eff9ddf4a4f4c11daf7b96b900237bf8859c86fa1e9b4fab416b72d7ea92468f8db89c18a3499a1070206e1c8a750c83b42d5325fc659cbb55eee88
  languageName: node
  linkType: hard

"schema-utils@npm:^2.6.5":
  version: 2.7.1
  resolution: "schema-utils@npm:2.7.1"
  dependencies:
    "@types/json-schema": ^7.0.5
    ajv: ^6.12.4
    ajv-keywords: ^3.5.2
  checksum: 32c62fc9e28edd101e1bd83453a4216eb9bd875cc4d3775e4452b541908fa8f61a7bbac8ffde57484f01d7096279d3ba0337078e85a918ecbeb72872fb09fb2b
  languageName: node
  linkType: hard

"script-ext-html-webpack-plugin@npm:2.1.3":
  version: 2.1.3
  resolution: "script-ext-html-webpack-plugin@npm:2.1.3"
  dependencies:
    debug: ^4.1.0
  peerDependencies:
    html-webpack-plugin: ^3.0.0 || ^4.0.0
    webpack: ^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0
  checksum: a956bf541b58fff10047c0bbe3f2056ee2436ed0f30873cf8f945e569b37fdb95f071a4582083824c8948981d41f949898062f35b3445fe52fc220073496a658
  languageName: node
  linkType: hard

"script-loader@npm:0.7.2":
  version: 0.7.2
  resolution: "script-loader@npm:0.7.2"
  dependencies:
    raw-loader: ~0.5.1
  checksum: e01b3fb3e58b5e777f418e26a6ec4f9ad633c94ce86eff51f39e341d846c524111643cc4770d89c7e3d0863ab5292485a69a0f7cb664d9bc20c9bbe4ca7035de
  languageName: node
  linkType: hard

"select-hose@npm:^2.0.0":
  version: 2.0.0
  resolution: "select-hose@npm:2.0.0"
  checksum: d7e5fcc695a4804209d232a1b18624a5134be334d4e1114b0721f7a5e72bd73da483dcf41528c1af4f4f4892ad7cfd6a1e55c8ffb83f9c9fe723b738db609dbb
  languageName: node
  linkType: hard

"select@npm:^1.1.2":
  version: 1.1.2
  resolution: "select@npm:1.1.2"
  checksum: 4346151e94f226ea6131e44e68e6d837f3fdee64831b756dd657cc0b02f4cb5107f867cb34a1d1216ab7737d0bf0645d44546afb030bbd8d64e891f5e4c4814e
  languageName: node
  linkType: hard

"selfsigned@npm:^1.10.8":
  version: 1.10.14
  resolution: "selfsigned@npm:1.10.14"
  dependencies:
    node-forge: ^0.10.0
  checksum: 616d131b18516ba2876398f0230987511d50a13816e0709b9f0d20246a524a2e83dfb27ea46ce2bfe331519583a156afa67bc3ece8bf0f9804aec06e2e8c7a21
  languageName: node
  linkType: hard

"semver@npm:2 || 3 || 4 || 5, semver@npm:^5.3.0, semver@npm:^5.4.1, semver@npm:^5.5.0, semver@npm:^5.5.1, semver@npm:^5.6.0":
  version: 5.7.1
  resolution: "semver@npm:5.7.1"
  bin:
    semver: ./bin/semver
  checksum: 57fd0acfd0bac382ee87cd52cd0aaa5af086a7dc8d60379dfe65fea491fb2489b6016400813930ecd61fd0952dae75c115287a1b16c234b1550887117744dfaf
  languageName: node
  linkType: hard

"semver@npm:7.0.0":
  version: 7.0.0
  resolution: "semver@npm:7.0.0"
  bin:
    semver: bin/semver.js
  checksum: 272c11bf8d083274ef79fe40a81c55c184dff84dd58e3c325299d0927ba48cece1f020793d138382b85f89bab5002a35a5ba59a3a68a7eebbb597eb733838778
  languageName: node
  linkType: hard

"semver@npm:^6.0.0, semver@npm:^6.1.1, semver@npm:^6.1.2, semver@npm:^6.3.0":
  version: 6.3.0
  resolution: "semver@npm:6.3.0"
  bin:
    semver: ./bin/semver.js
  checksum: 1b26ecf6db9e8292dd90df4e781d91875c0dcc1b1909e70f5d12959a23c7eebb8f01ea581c00783bbee72ceeaad9505797c381756326073850dc36ed284b21b9
  languageName: node
  linkType: hard

"semver@npm:^7.3.5":
  version: 7.3.5
  resolution: "semver@npm:7.3.5"
  dependencies:
    lru-cache: ^6.0.0
  bin:
    semver: bin/semver.js
  checksum: 5eafe6102bea2a7439897c1856362e31cc348ccf96efd455c8b5bc2c61e6f7e7b8250dc26b8828c1d76a56f818a7ee907a36ae9fb37a599d3d24609207001d60
  languageName: node
  linkType: hard

"send@npm:0.17.2":
  version: 0.17.2
  resolution: "send@npm:0.17.2"
  dependencies:
    debug: 2.6.9
    depd: ~1.1.2
    destroy: ~1.0.4
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    etag: ~1.8.1
    fresh: 0.5.2
    http-errors: 1.8.1
    mime: 1.6.0
    ms: 2.1.3
    on-finished: ~2.3.0
    range-parser: ~1.2.1
    statuses: ~1.5.0
  checksum: c28f36deb4ccba9b8d6e6a1e472b8e7c40a1f51575bdf8f67303568cc9e71131faa3adc36fdb72611616ccad1584358bbe4c3ebf419e663ecc5de868ad3d3f03
  languageName: node
  linkType: hard

"serialize-javascript@npm:^1.4.0":
  version: 1.9.1
  resolution: "serialize-javascript@npm:1.9.1"
  checksum: a52ad24ce6ce3ece82ff294566a6929af1bf646345ac78a8a452832fa887db7bade7fcf95412241d207d119aff45e99fabf933f92e07574741c90258f2df3832
  languageName: node
  linkType: hard

"serialize-javascript@npm:^4.0.0":
  version: 4.0.0
  resolution: "serialize-javascript@npm:4.0.0"
  dependencies:
    randombytes: ^2.1.0
  checksum: 3273b3394b951671fcf388726e9577021870dfbf85e742a1183fb2e91273e6101bdccea81ff230724f6659a7ee4cef924b0ff9baca32b79d9384ec37caf07302
  languageName: node
  linkType: hard

"serve-index@npm:^1.9.1":
  version: 1.9.1
  resolution: "serve-index@npm:1.9.1"
  dependencies:
    accepts: ~1.3.4
    batch: 0.6.1
    debug: 2.6.9
    escape-html: ~1.0.3
    http-errors: ~1.6.2
    mime-types: ~2.1.17
    parseurl: ~1.3.2
  checksum: e2647ce13379485b98a53ba2ea3fbad4d44b57540d00663b02b976e426e6194d62ac465c0d862cb7057f65e0de8ab8a684aa095427a4b8612412eca0d300d22f
  languageName: node
  linkType: hard

"serve-static@npm:1.14.2, serve-static@npm:^1.13.2":
  version: 1.14.2
  resolution: "serve-static@npm:1.14.2"
  dependencies:
    encodeurl: ~1.0.2
    escape-html: ~1.0.3
    parseurl: ~1.3.3
    send: 0.17.2
  checksum: d97f3183b1dfcd8ce9c0e37e18e87fd31147ed6c8ee0b2c3a089d795e44ee851ca5061db01574f806d54f4e4b70bc694d9ca64578653514e04a28cbc97a1de05
  languageName: node
  linkType: hard

"set-blocking@npm:^2.0.0":
  version: 2.0.0
  resolution: "set-blocking@npm:2.0.0"
  checksum: 6e65a05f7cf7ebdf8b7c75b101e18c0b7e3dff4940d480efed8aad3a36a4005140b660fa1d804cb8bce911cac290441dc728084a30504d3516ac2ff7ad607b02
  languageName: node
  linkType: hard

"set-value@npm:^2.0.0, set-value@npm:^2.0.1":
  version: 2.0.1
  resolution: "set-value@npm:2.0.1"
  dependencies:
    extend-shallow: ^2.0.1
    is-extendable: ^0.1.1
    is-plain-object: ^2.0.3
    split-string: ^3.0.1
  checksum: 09a4bc72c94641aeae950eb60dc2755943b863780fcc32e441eda964b64df5e3f50603d5ebdd33394ede722528bd55ed43aae26e9df469b4d32e2292b427b601
  languageName: node
  linkType: hard

"setimmediate@npm:^1.0.4":
  version: 1.0.5
  resolution: "setimmediate@npm:1.0.5"
  checksum: c9a6f2c5b51a2dabdc0247db9c46460152ffc62ee139f3157440bd48e7c59425093f42719ac1d7931f054f153e2d26cf37dfeb8da17a794a58198a2705e527fd
  languageName: node
  linkType: hard

"setprototypeof@npm:1.1.0":
  version: 1.1.0
  resolution: "setprototypeof@npm:1.1.0"
  checksum: 27cb44304d6c9e1a23bc6c706af4acaae1a7aa1054d4ec13c05f01a99fd4887109a83a8042b67ad90dbfcd100d43efc171ee036eb080667172079213242ca36e
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: be18cbbf70e7d8097c97f713a2e76edf84e87299b40d085c6bf8b65314e994cc15e2e317727342fa6996e38e1f52c59720b53fe621e2eb593a6847bf0356db89
  languageName: node
  linkType: hard

"sha.js@npm:^2.4.0, sha.js@npm:^2.4.8":
  version: 2.4.11
  resolution: "sha.js@npm:2.4.11"
  dependencies:
    inherits: ^2.0.1
    safe-buffer: ^5.0.1
  bin:
    sha.js: ./bin.js
  checksum: ebd3f59d4b799000699097dadb831c8e3da3eb579144fd7eb7a19484cbcbb7aca3c68ba2bb362242eb09e33217de3b4ea56e4678184c334323eca24a58e3ad07
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: ^6.0.2
  checksum: 39b3dd9630a774aba288a680e7d2901f5c0eae7b8387fc5c8ea559918b29b3da144b7bdb990d7ccd9e11be05508ac9e459ce51d01fd65e583282f6ffafcba2e7
  languageName: node
  linkType: hard

"shebang-command@npm:^1.2.0":
  version: 1.2.0
  resolution: "shebang-command@npm:1.2.0"
  dependencies:
    shebang-regex: ^1.0.0
  checksum: 9eed1750301e622961ba5d588af2212505e96770ec376a37ab678f965795e995ade7ed44910f5d3d3cb5e10165a1847f52d3348c64e146b8be922f7707958908
  languageName: node
  linkType: hard

"shebang-regex@npm:^1.0.0":
  version: 1.0.0
  resolution: "shebang-regex@npm:1.0.0"
  checksum: 404c5a752cd40f94591dfd9346da40a735a05139dac890ffc229afba610854d8799aaa52f87f7e0c94c5007f2c6af55bdcaeb584b56691926c5eaf41dc8f1372
  languageName: node
  linkType: hard

"shell-quote@npm:^1.6.1":
  version: 1.7.3
  resolution: "shell-quote@npm:1.7.3"
  checksum: aca58e73a3a5d933d02e0bdddedc53ee14f7c2ec264f97ac915b9d4482d077a38e422aa664631d60a672cd3cdb4054eb2e6c0303f54882453dacb6483e482d34
  languageName: node
  linkType: hard

"shellwords@npm:^0.1.1":
  version: 0.1.1
  resolution: "shellwords@npm:0.1.1"
  checksum: 8d73a5e9861f5e5f1068e2cfc39bc0002400fe58558ab5e5fa75630d2c3adf44ca1fac81957609c8320d5533e093802fcafc72904bf1a32b95de3c19a0b1c0d4
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4":
  version: 1.0.4
  resolution: "side-channel@npm:1.0.4"
  dependencies:
    call-bind: ^1.0.0
    get-intrinsic: ^1.0.2
    object-inspect: ^1.9.0
  checksum: 351e41b947079c10bd0858364f32bb3a7379514c399edb64ab3dce683933483fc63fb5e4efe0a15a2e8a7e3c436b6a91736ddb8d8c6591b0460a24bb4a1ee245
  languageName: node
  linkType: hard

"sigmund@npm:^1.0.1":
  version: 1.0.1
  resolution: "sigmund@npm:1.0.1"
  checksum: 793f81f8083ad75ff3903ffd93cf35be8d797e872822cf880aea27ce6db522b508d93ea52ae292bccf357ce34dd5c7faa544cc51c2216e70bbf5fcf09b62707c
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.0, signal-exit@npm:^3.0.2":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: a2f098f247adc367dffc27845853e9959b9e88b01cb301658cfe4194352d8d2bb32e18467c786a7fe15f1d44b233ea35633d076d5e737870b7139949d1ab6318
  languageName: node
  linkType: hard

"simple-swizzle@npm:^0.2.2":
  version: 0.2.2
  resolution: "simple-swizzle@npm:0.2.2"
  dependencies:
    is-arrayish: ^0.3.1
  checksum: a7f3f2ab5c76c4472d5c578df892e857323e452d9f392e1b5cf74b74db66e6294a1e1b8b390b519fa1b96b5b613f2a37db6cffef52c3f1f8f3c5ea64eb2d54c0
  languageName: node
  linkType: hard

"sisteransi@npm:^0.1.1":
  version: 0.1.1
  resolution: "sisteransi@npm:0.1.1"
  checksum: 7bb2c4e54c43834a91352f12ca65a94c13a8c93cb1139ac62f383056c45cc9e44850cc4e5b6e0a3e3c16cf4ae1d00233917a2bfa6c8a0882210a2187cd21d01c
  languageName: node
  linkType: hard

"slash@npm:^1.0.0":
  version: 1.0.0
  resolution: "slash@npm:1.0.0"
  checksum: 4b6e21b1fba6184a7e2efb1dd173f692d8a845584c1bbf9dc818ff86f5a52fc91b413008223d17cc684604ee8bb9263a420b1182027ad9762e35388434918860
  languageName: node
  linkType: hard

"slash@npm:^2.0.0":
  version: 2.0.0
  resolution: "slash@npm:2.0.0"
  checksum: 512d4350735375bd11647233cb0e2f93beca6f53441015eea241fe784d8068281c3987fbaa93e7ef1c38df68d9c60013045c92837423c69115297d6169aa85e6
  languageName: node
  linkType: hard

"slice-ansi@npm:1.0.0":
  version: 1.0.0
  resolution: "slice-ansi@npm:1.0.0"
  dependencies:
    is-fullwidth-code-point: ^2.0.0
  checksum: b4239e885803d9e35c6a3a17bb530f1d76349753abaf88594ab57dfd666afe8e927efff152d5e010b51d134dd47b6118bb9c47d24c99ed75841c29beae82d9b9
  languageName: node
  linkType: hard

"slice-ansi@npm:^2.1.0":
  version: 2.1.0
  resolution: "slice-ansi@npm:2.1.0"
  dependencies:
    ansi-styles: ^3.2.0
    astral-regex: ^1.0.0
    is-fullwidth-code-point: ^2.0.0
  checksum: 4e82995aa59cef7eb03ef232d73c2239a15efa0ace87a01f3012ebb942e963fbb05d448ce7391efcd52ab9c32724164aba2086f5143e0445c969221dde3b6b1e
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: b5167a7142c1da704c0e3af85c402002b597081dd9575031a90b4f229ca5678e9a36e8a374f1814c8156a725d17008ae3bde63b92f9cfd132526379e580bec8b
  languageName: node
  linkType: hard

"snapdragon-node@npm:^2.0.1":
  version: 2.1.1
  resolution: "snapdragon-node@npm:2.1.1"
  dependencies:
    define-property: ^1.0.0
    isobject: ^3.0.0
    snapdragon-util: ^3.0.1
  checksum: 9bb57d759f9e2a27935dbab0e4a790137adebace832b393e350a8bf5db461ee9206bb642d4fe47568ee0b44080479c8b4a9ad0ebe3712422d77edf9992a672fd
  languageName: node
  linkType: hard

"snapdragon-util@npm:^3.0.1":
  version: 3.0.1
  resolution: "snapdragon-util@npm:3.0.1"
  dependencies:
    kind-of: ^3.2.0
  checksum: 684997dbe37ec995c03fd3f412fba2b711fc34cb4010452b7eb668be72e8811a86a12938b511e8b19baf853b325178c56d8b78d655305e5cfb0bb8b21677e7b7
  languageName: node
  linkType: hard

"snapdragon@npm:^0.8.1":
  version: 0.8.2
  resolution: "snapdragon@npm:0.8.2"
  dependencies:
    base: ^0.11.1
    debug: ^2.2.0
    define-property: ^0.2.5
    extend-shallow: ^2.0.1
    map-cache: ^0.2.2
    source-map: ^0.5.6
    source-map-resolve: ^0.5.0
    use: ^3.1.0
  checksum: a197f242a8f48b11036563065b2487e9b7068f50a20dd81d9161eca6af422174fc158b8beeadbe59ce5ef172aa5718143312b3aebaae551c124b7824387c8312
  languageName: node
  linkType: hard

"sockjs-client@npm:^1.5.0":
  version: 1.5.2
  resolution: "sockjs-client@npm:1.5.2"
  dependencies:
    debug: ^3.2.6
    eventsource: ^1.0.7
    faye-websocket: ^0.11.3
    inherits: ^2.0.4
    json3: ^3.3.3
    url-parse: ^1.5.3
  checksum: b3c3966ca8ebe72454e3bbb83b21b0f58dda1c725815f2897162104afc42b779de9a6d964fb2b164ea290cb4c0c94cb3542bd7f788f21fe5df018da963826f96
  languageName: node
  linkType: hard

"sockjs@npm:^0.3.21":
  version: 0.3.24
  resolution: "sockjs@npm:0.3.24"
  dependencies:
    faye-websocket: ^0.11.3
    uuid: ^8.3.2
    websocket-driver: ^0.7.4
  checksum: 355309b48d2c4e9755349daa29cea1c0d9ee23e49b983841c6bf7a20276b00d3c02343f9f33f26d2ee8b261a5a02961b52a25c8da88b2538c5b68d3071b4934c
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^6.0.0":
  version: 6.1.1
  resolution: "socks-proxy-agent@npm:6.1.1"
  dependencies:
    agent-base: ^6.0.2
    debug: ^4.3.1
    socks: ^2.6.1
  checksum: 9a8a4f791bba0060315cf7291ca6f9db37d6fc280fd0860d73d8887d3efe4c22e823aa25a8d5375f6079279f8dc91b50c075345179bf832bfe3c7c26d3582e3c
  languageName: node
  linkType: hard

"socks@npm:^2.6.1":
  version: 2.6.2
  resolution: "socks@npm:2.6.2"
  dependencies:
    ip: ^1.1.5
    smart-buffer: ^4.2.0
  checksum: dd9194293059d737759d5c69273850ad4149f448426249325c4bea0e340d1cf3d266c3b022694b0dcf5d31f759de23657244c481fc1e8322add80b7985c36b5e
  languageName: node
  linkType: hard

"sort-keys@npm:^2.0.0":
  version: 2.0.0
  resolution: "sort-keys@npm:2.0.0"
  dependencies:
    is-plain-obj: ^1.0.0
  checksum: f0fd827fa9f8f866e98588d2a38c35209afbf1e9a05bb0e4ceeeb8bbf31d923c8902b0a7e0f561590ddb65e58eba6a74f74b991c85360bcc52e83a3f0d1cffd7
  languageName: node
  linkType: hard

"source-list-map@npm:^2.0.0":
  version: 2.0.1
  resolution: "source-list-map@npm:2.0.1"
  checksum: 806efc6f75e7cd31e4815e7a3aaf75a45c704871ea4075cb2eb49882c6fca28998f44fc5ac91adb6de03b2882ee6fb02f951fdc85e6a22b338c32bfe19557938
  languageName: node
  linkType: hard

"source-map-js@npm:>=0.6.2 <2.0.0":
  version: 1.0.2
  resolution: "source-map-js@npm:1.0.2"
  checksum: c049a7fc4deb9a7e9b481ae3d424cc793cb4845daa690bc5a05d428bf41bf231ced49b4cf0c9e77f9d42fdb3d20d6187619fc586605f5eabe995a316da8d377c
  languageName: node
  linkType: hard

"source-map-resolve@npm:^0.5.0, source-map-resolve@npm:^0.5.2":
  version: 0.5.3
  resolution: "source-map-resolve@npm:0.5.3"
  dependencies:
    atob: ^2.1.2
    decode-uri-component: ^0.2.0
    resolve-url: ^0.2.1
    source-map-url: ^0.4.0
    urix: ^0.1.0
  checksum: c73fa44ac00783f025f6ad9e038ab1a2e007cd6a6b86f47fe717c3d0765b4a08d264f6966f3bd7cd9dbcd69e4832783d5472e43247775b2a550d6f2155d24bae
  languageName: node
  linkType: hard

"source-map-support@npm:^0.4.15":
  version: 0.4.18
  resolution: "source-map-support@npm:0.4.18"
  dependencies:
    source-map: ^0.5.6
  checksum: 669aa7e992fec586fac0ba9a8dea8ce81b7328f92806335f018ffac5709afb2920e3870b4e56c68164282607229f04b8bbcf5d0e5c845eb1b5119b092e7585c0
  languageName: node
  linkType: hard

"source-map-support@npm:^0.5.6, source-map-support@npm:^0.5.9, source-map-support@npm:~0.5.12":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 43e98d700d79af1d36f859bdb7318e601dfc918c7ba2e98456118ebc4c4872b327773e5a1df09b0524e9e5063bb18f0934538eace60cca2710d1fa687645d137
  languageName: node
  linkType: hard

"source-map-url@npm:^0.4.0":
  version: 0.4.1
  resolution: "source-map-url@npm:0.4.1"
  checksum: 64c5c2c77aff815a6e61a4120c309ae4cac01298d9bcbb3deb1b46a4dd4c46d4a1eaeda79ec9f684766ae80e8dc86367b89326ce9dd2b89947bd9291fc1ac08c
  languageName: node
  linkType: hard

"source-map@npm:^0.5.0, source-map@npm:^0.5.3, source-map@npm:^0.5.6, source-map@npm:^0.5.7":
  version: 0.5.7
  resolution: "source-map@npm:0.5.7"
  checksum: 5dc2043b93d2f194142c7f38f74a24670cd7a0063acdaf4bf01d2964b402257ae843c2a8fa822ad5b71013b5fcafa55af7421383da919752f22ff488bc553f4d
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1, source-map@npm:~0.6.0, source-map@npm:~0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"spdx-correct@npm:^3.0.0":
  version: 3.1.1
  resolution: "spdx-correct@npm:3.1.1"
  dependencies:
    spdx-expression-parse: ^3.0.0
    spdx-license-ids: ^3.0.0
  checksum: 77ce438344a34f9930feffa61be0eddcda5b55fc592906ef75621d4b52c07400a97084d8701557b13f7d2aae0cb64f808431f469e566ef3fe0a3a131dcb775a6
  languageName: node
  linkType: hard

"spdx-exceptions@npm:^2.1.0":
  version: 2.3.0
  resolution: "spdx-exceptions@npm:2.3.0"
  checksum: cb69a26fa3b46305637123cd37c85f75610e8c477b6476fa7354eb67c08128d159f1d36715f19be6f9daf4b680337deb8c65acdcae7f2608ba51931540687ac0
  languageName: node
  linkType: hard

"spdx-expression-parse@npm:^3.0.0":
  version: 3.0.1
  resolution: "spdx-expression-parse@npm:3.0.1"
  dependencies:
    spdx-exceptions: ^2.1.0
    spdx-license-ids: ^3.0.0
  checksum: a1c6e104a2cbada7a593eaa9f430bd5e148ef5290d4c0409899855ce8b1c39652bcc88a725259491a82601159d6dc790bedefc9016c7472f7de8de7361f8ccde
  languageName: node
  linkType: hard

"spdx-license-ids@npm:^3.0.0":
  version: 3.0.11
  resolution: "spdx-license-ids@npm:3.0.11"
  checksum: 1da1acb090257773e60b022094050e810ae9fec874dc1461f65dc0400cd42dd830ab2df6e64fb49c2db3dce386dd0362110780e1b154db7c0bb413488836aaeb
  languageName: node
  linkType: hard

"spdy-transport@npm:^3.0.0":
  version: 3.0.0
  resolution: "spdy-transport@npm:3.0.0"
  dependencies:
    debug: ^4.1.0
    detect-node: ^2.0.4
    hpack.js: ^2.1.6
    obuf: ^1.1.2
    readable-stream: ^3.0.6
    wbuf: ^1.7.3
  checksum: 0fcaad3b836fb1ec0bdd39fa7008b9a7a84a553f12be6b736a2512613b323207ffc924b9551cef0378f7233c85916cff1118652e03a730bdb97c0e042243d56c
  languageName: node
  linkType: hard

"spdy@npm:^4.0.2":
  version: 4.0.2
  resolution: "spdy@npm:4.0.2"
  dependencies:
    debug: ^4.1.0
    handle-thing: ^2.0.0
    http-deceiver: ^1.2.7
    select-hose: ^2.0.0
    spdy-transport: ^3.0.0
  checksum: 2c739d0ff6f56ad36d2d754d0261d5ec358457bea7cbf77b1b05b0c6464f2ce65b85f196305f50b7bd9120723eb94bae9933466f28e67e5cd8cde4e27f1d75f8
  languageName: node
  linkType: hard

"split-string@npm:^3.0.1, split-string@npm:^3.0.2":
  version: 3.1.0
  resolution: "split-string@npm:3.1.0"
  dependencies:
    extend-shallow: ^3.0.0
  checksum: ae5af5c91bdc3633628821bde92fdf9492fa0e8a63cf6a0376ed6afde93c701422a1610916f59be61972717070119e848d10dfbbd5024b7729d6a71972d2a84c
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 19d79aec211f09b99ec3099b5b2ae2f6e9cdefe50bc91ac4c69144b6d3928a640bb6ae5b3def70c2e85a2c3d9f5ec2719921e3a59d3ca3ef4b2fd1a4656a0df3
  languageName: node
  linkType: hard

"sshpk@npm:^1.7.0":
  version: 1.17.0
  resolution: "sshpk@npm:1.17.0"
  dependencies:
    asn1: ~0.2.3
    assert-plus: ^1.0.0
    bcrypt-pbkdf: ^1.0.0
    dashdash: ^1.12.0
    ecc-jsbn: ~0.1.1
    getpass: ^0.1.1
    jsbn: ~0.1.0
    safer-buffer: ^2.0.2
    tweetnacl: ~0.14.0
  bin:
    sshpk-conv: bin/sshpk-conv
    sshpk-sign: bin/sshpk-sign
    sshpk-verify: bin/sshpk-verify
  checksum: ba109f65c8e6c35133b8e6ed5576abeff8aa8d614824b7275ec3ca308f081fef483607c28d97780c1e235818b0f93ed8c8b56d0a5968d5a23fd6af57718c7597
  languageName: node
  linkType: hard

"ssri@npm:^5.2.4":
  version: 5.3.0
  resolution: "ssri@npm:5.3.0"
  dependencies:
    safe-buffer: ^5.1.1
  checksum: 18902cf582d15075b31dddd2508404cd688a7bceb0026765b7a9ed1d0c8422eab548e06427d850e048ad72643f8382a1dda2a4b00d2381bb8622e2e19b8a92d5
  languageName: node
  linkType: hard

"ssri@npm:^6.0.1":
  version: 6.0.2
  resolution: "ssri@npm:6.0.2"
  dependencies:
    figgy-pudding: ^3.5.1
  checksum: 7c2e5d442f6252559c8987b7114bcf389fe5614bf65de09ba3e6f9a57b9b65b2967de348fcc3acccff9c069adb168140dd2c5fc2f6f4a779e604a27ef1f7d551
  languageName: node
  linkType: hard

"ssri@npm:^8.0.0, ssri@npm:^8.0.1":
  version: 8.0.1
  resolution: "ssri@npm:8.0.1"
  dependencies:
    minipass: ^3.1.1
  checksum: bc447f5af814fa9713aa201ec2522208ae0f4d8f3bda7a1f445a797c7b929a02720436ff7c478fb5edc4045adb02b1b88d2341b436a80798734e2494f1067b36
  languageName: node
  linkType: hard

"stable@npm:^0.1.8":
  version: 0.1.8
  resolution: "stable@npm:0.1.8"
  checksum: 2ff482bb100285d16dd75cd8f7c60ab652570e8952c0bfa91828a2b5f646a0ff533f14596ea4eabd48bb7f4aeea408dce8f8515812b975d958a4cc4fa6b9dfeb
  languageName: node
  linkType: hard

"stack-utils@npm:^1.0.1":
  version: 1.0.5
  resolution: "stack-utils@npm:1.0.5"
  dependencies:
    escape-string-regexp: ^2.0.0
  checksum: f82baf8d89536252a55c76866d5be3d04c96b09693a8d2ab3794b9fdec3674e05bd3f3d19345093e2cbba116a1f8f413858e0537bc3c81c605249261c3d26182
  languageName: node
  linkType: hard

"stackframe@npm:^1.1.1":
  version: 1.2.0
  resolution: "stackframe@npm:1.2.0"
  checksum: 37d659bdd574e118a48c445a9a054a2b8dee6d6ad54eb16c51c7dae622c0f4994b9ff4e47d744aa6cfd14c00b477e145f34db3df78771f3e783ce8f357616d00
  languageName: node
  linkType: hard

"static-extend@npm:^0.1.1":
  version: 0.1.2
  resolution: "static-extend@npm:0.1.2"
  dependencies:
    define-property: ^0.2.5
    object-copy: ^0.1.0
  checksum: 8657485b831f79e388a437260baf22784540417a9b29e11572c87735df24c22b84eda42107403a64b30861b2faf13df9f7fc5525d51f9d1d2303aba5cbf4e12c
  languageName: node
  linkType: hard

"statuses@npm:>= 1.4.0 < 2, statuses@npm:>= 1.5.0 < 2, statuses@npm:~1.5.0":
  version: 1.5.0
  resolution: "statuses@npm:1.5.0"
  checksum: c469b9519de16a4bb19600205cffb39ee471a5f17b82589757ca7bd40a8d92ebb6ed9f98b5a540c5d302ccbc78f15dc03cc0280dd6e00df1335568a5d5758a5c
  languageName: node
  linkType: hard

"statuses@npm:~1.3.1":
  version: 1.3.1
  resolution: "statuses@npm:1.3.1"
  checksum: da573f84ee32303ccb06f51dc1fc2ef592f4837d2d3fde8a9d1440058c6ae05805bca7cd3567c7fb9d6c4455a546ed8582a4ec647c8ceeae1654be8cd77e5a24
  languageName: node
  linkType: hard

"stealthy-require@npm:^1.1.1":
  version: 1.1.1
  resolution: "stealthy-require@npm:1.1.1"
  checksum: 6805b857a9f3a6a1079fc6652278038b81011f2a5b22cbd559f71a6c02087e6f1df941eb10163e3fdc5391ab5807aa46758d4258547c1f5ede31e6d9bfda8dd3
  languageName: node
  linkType: hard

"stream-browserify@npm:^2.0.1":
  version: 2.0.2
  resolution: "stream-browserify@npm:2.0.2"
  dependencies:
    inherits: ~2.0.1
    readable-stream: ^2.0.2
  checksum: 8de7bcab5582e9a931ae1a4768be7efe8fa4b0b95fd368d16d8cf3e494b897d6b0a7238626de5d71686e53bddf417fd59d106cfa3af0ec055f61a8d1f8fc77b3
  languageName: node
  linkType: hard

"stream-each@npm:^1.1.0":
  version: 1.2.3
  resolution: "stream-each@npm:1.2.3"
  dependencies:
    end-of-stream: ^1.1.0
    stream-shift: ^1.0.0
  checksum: f243de78e9fcc60757994efc4e8ecae9f01a4b2c6a505d786b11fcaa68b1a75ca54afc1669eac9e08f19ff0230792fc40d0f3e3e2935d76971b4903af18b76ab
  languageName: node
  linkType: hard

"stream-http@npm:^2.7.2":
  version: 2.8.3
  resolution: "stream-http@npm:2.8.3"
  dependencies:
    builtin-status-codes: ^3.0.0
    inherits: ^2.0.1
    readable-stream: ^2.3.6
    to-arraybuffer: ^1.0.0
    xtend: ^4.0.0
  checksum: f57dfaa21a015f72e6ce6b199cf1762074cfe8acf0047bba8f005593754f1743ad0a91788f95308d9f3829ad55742399ad27b4624432f2752a08e62ef4346e05
  languageName: node
  linkType: hard

"stream-shift@npm:^1.0.0":
  version: 1.0.1
  resolution: "stream-shift@npm:1.0.1"
  checksum: 59b82b44b29ec3699b5519a49b3cedcc6db58c72fb40c04e005525dfdcab1c75c4e0c180b923c380f204bed78211b9bad8faecc7b93dece4d004c3f6ec75737b
  languageName: node
  linkType: hard

"strict-uri-encode@npm:^1.0.0":
  version: 1.1.0
  resolution: "strict-uri-encode@npm:1.1.0"
  checksum: 9466d371f7b36768d43f7803f26137657559e4c8b0161fb9e320efb8edba3ae22f8e99d4b0d91da023b05a13f62ec5412c3f4f764b5788fac11d1fea93720bb3
  languageName: node
  linkType: hard

"string-length@npm:^2.0.0":
  version: 2.0.0
  resolution: "string-length@npm:2.0.0"
  dependencies:
    astral-regex: ^1.0.0
    strip-ansi: ^4.0.0
  checksum: 3a339b63fd39d6a1077dfbbe3279545e1b67fa4b0a558906158cf0121632b280f34c8768ec7270fb25db732d6323eceb9c7254f6026509694b6a7533ca8cb89e
  languageName: node
  linkType: hard

"string-width@npm:^1.0.1":
  version: 1.0.2
  resolution: "string-width@npm:1.0.2"
  dependencies:
    code-point-at: ^1.0.0
    is-fullwidth-code-point: ^1.0.0
    strip-ansi: ^3.0.0
  checksum: 5c79439e95bc3bd7233a332c5f5926ab2ee90b23816ed4faa380ce3b2576d7800b0a5bb15ae88ed28737acc7ea06a518c2eef39142dd727adad0e45c776cd37e
  languageName: node
  linkType: hard

"string-width@npm:^1.0.2 || 2 || 3 || 4, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: ^8.0.0
    is-fullwidth-code-point: ^3.0.0
    strip-ansi: ^6.0.1
  checksum: e52c10dc3fbfcd6c3a15f159f54a90024241d0f149cf8aed2982a2d801d2e64df0bf1dc351cf8e95c3319323f9f220c16e740b06faecd53e2462df1d2b5443fb
  languageName: node
  linkType: hard

"string-width@npm:^2.0.0, string-width@npm:^2.1.0, string-width@npm:^2.1.1":
  version: 2.1.1
  resolution: "string-width@npm:2.1.1"
  dependencies:
    is-fullwidth-code-point: ^2.0.0
    strip-ansi: ^4.0.0
  checksum: d6173abe088c615c8dffaf3861dc5d5906ed3dc2d6fd67ff2bd2e2b5dce7fd683c5240699cf0b1b8aa679a3b3bd6b28b5053c824cb89b813d7f6541d8f89064a
  languageName: node
  linkType: hard

"string-width@npm:^3.0.0, string-width@npm:^3.1.0":
  version: 3.1.0
  resolution: "string-width@npm:3.1.0"
  dependencies:
    emoji-regex: ^7.0.1
    is-fullwidth-code-point: ^2.0.0
    strip-ansi: ^5.1.0
  checksum: 57f7ca73d201682816d573dc68bd4bb8e1dff8dc9fcf10470fdfc3474135c97175fec12ea6a159e67339b41e86963112355b64529489af6e7e70f94a7caf08b2
  languageName: node
  linkType: hard

"string.prototype.padend@npm:^3.0.0":
  version: 3.1.3
  resolution: "string.prototype.padend@npm:3.1.3"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
    es-abstract: ^1.19.1
  checksum: ef9ee0542c17975629bc6d21497e8faaa142d873e9f07fb65de2a955df402a1eac45cbed375045a759501e9d4ef80e589e11f0e12103c20df0770e47f6b59bc7
  languageName: node
  linkType: hard

"string.prototype.padstart@npm:^3.0.0":
  version: 3.1.3
  resolution: "string.prototype.padstart@npm:3.1.3"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
    es-abstract: ^1.19.1
  checksum: 8bf8bc1d25edc79c4db285aa8dfd5d269dac4024631e8ae13202c2126348a07e00b153d6bf7b858c5bd716e44675a7fbb50baedd3e8970e1034bb86be22c9475
  languageName: node
  linkType: hard

"string.prototype.trimend@npm:^1.0.4":
  version: 1.0.4
  resolution: "string.prototype.trimend@npm:1.0.4"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
  checksum: 17e5aa45c3983f582693161f972c1c1fa4bbbdf22e70e582b00c91b6575f01680dc34e83005b98e31abe4d5d29e0b21fcc24690239c106c7b2315aade6a898ac
  languageName: node
  linkType: hard

"string.prototype.trimstart@npm:^1.0.4":
  version: 1.0.4
  resolution: "string.prototype.trimstart@npm:1.0.4"
  dependencies:
    call-bind: ^1.0.2
    define-properties: ^1.1.3
  checksum: 3fb06818d3cccac5fa3f5f9873d984794ca0e9f6616fae6fcc745885d9efed4e17fe15f832515d9af5e16c279857fdbffdfc489ca4ed577811b017721b30302f
  languageName: node
  linkType: hard

"string_decoder@npm:^1.0.0, string_decoder@npm:^1.1.1":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: ~5.2.0
  checksum: 8417646695a66e73aefc4420eb3b84cc9ffd89572861fe004e6aeb13c7bc00e2f616247505d2dbbef24247c372f70268f594af7126f43548565c68c117bdeb56
  languageName: node
  linkType: hard

"string_decoder@npm:~1.1.1":
  version: 1.1.1
  resolution: "string_decoder@npm:1.1.1"
  dependencies:
    safe-buffer: ~5.1.0
  checksum: 9ab7e56f9d60a28f2be697419917c50cac19f3e8e6c28ef26ed5f4852289fe0de5d6997d29becf59028556f2c62983790c1d9ba1e2a3cc401768ca12d5183a5b
  languageName: node
  linkType: hard

"strip-ansi@npm:^3.0.0, strip-ansi@npm:^3.0.1":
  version: 3.0.1
  resolution: "strip-ansi@npm:3.0.1"
  dependencies:
    ansi-regex: ^2.0.0
  checksum: 9b974de611ce5075c70629c00fa98c46144043db92ae17748fb780f706f7a789e9989fd10597b7c2053ae8d1513fd707816a91f1879b2f71e6ac0b6a863db465
  languageName: node
  linkType: hard

"strip-ansi@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-ansi@npm:4.0.0"
  dependencies:
    ansi-regex: ^3.0.0
  checksum: d9186e6c0cf78f25274f6750ee5e4a5725fb91b70fdd79aa5fe648eab092a0ec5b9621b22d69d4534a56319f75d8944efbd84e3afa8d4ad1b9a9491f12c84eca
  languageName: node
  linkType: hard

"strip-ansi@npm:^5.0.0, strip-ansi@npm:^5.1.0, strip-ansi@npm:^5.2.0":
  version: 5.2.0
  resolution: "strip-ansi@npm:5.2.0"
  dependencies:
    ansi-regex: ^4.1.0
  checksum: bdb5f76ade97062bd88e7723aa019adbfacdcba42223b19ccb528ffb9fb0b89a5be442c663c4a3fb25268eaa3f6ea19c7c3fbae830bd1562d55adccae1fcec46
  languageName: node
  linkType: hard

"strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: ^5.0.1
  checksum: f3cd25890aef3ba6e1a74e20896c21a46f482e93df4a06567cebf2b57edabb15133f1f94e57434e0a958d61186087b1008e89c94875d019910a213181a14fc8c
  languageName: node
  linkType: hard

"strip-bom@npm:3.0.0, strip-bom@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-bom@npm:3.0.0"
  checksum: 8d50ff27b7ebe5ecc78f1fe1e00fcdff7af014e73cf724b46fb81ef889eeb1015fc5184b64e81a2efe002180f3ba431bdd77e300da5c6685d702780fbf0c8d5b
  languageName: node
  linkType: hard

"strip-bom@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-bom@npm:2.0.0"
  dependencies:
    is-utf8: ^0.2.0
  checksum: 08efb746bc67b10814cd03d79eb31bac633393a782e3f35efbc1b61b5165d3806d03332a97f362822cf0d4dd14ba2e12707fcff44fe1c870c48a063a0c9e4944
  languageName: node
  linkType: hard

"strip-eof@npm:^1.0.0":
  version: 1.0.0
  resolution: "strip-eof@npm:1.0.0"
  checksum: 40bc8ddd7e072f8ba0c2d6d05267b4e0a4800898c3435b5fb5f5a21e6e47dfaff18467e7aa0d1844bb5d6274c3097246595841fbfeb317e541974ee992cac506
  languageName: node
  linkType: hard

"strip-indent@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-indent@npm:2.0.0"
  checksum: 7d9080d02ddace616ebbc17846e41d3880cb147e2a81e51142281322ded6b05b230a4fb12c2e5266f62735cf8f5fb9839e55d74799d11f26bcc8c71ca049a0ba
  languageName: node
  linkType: hard

"strip-json-comments@npm:^2.0.0, strip-json-comments@npm:^2.0.1, strip-json-comments@npm:~2.0.1":
  version: 2.0.1
  resolution: "strip-json-comments@npm:2.0.1"
  checksum: 1074ccb63270d32ca28edfb0a281c96b94dc679077828135141f27d52a5a398ef5e78bcf22809d23cadc2b81dfbe345eb5fd8699b385c8b1128907dec4a7d1e1
  languageName: node
  linkType: hard

"stylehacks@npm:^4.0.0":
  version: 4.0.3
  resolution: "stylehacks@npm:4.0.3"
  dependencies:
    browserslist: ^4.0.0
    postcss: ^7.0.0
    postcss-selector-parser: ^3.0.0
  checksum: 8acf28ea609bee6d7ba40121bcf53af8d899c1ec04f2c08de9349b8292b84b8aa7f82e14c623ae6956decf5b7a7eeea5472ab8e48de7bdcdb6d76640444f6753
  languageName: node
  linkType: hard

"supports-color@npm:^2.0.0":
  version: 2.0.0
  resolution: "supports-color@npm:2.0.0"
  checksum: 602538c5812b9006404370b5a4b885d3e2a1f6567d314f8b4a41974ffe7d08e525bf92ae0f9c7030e3b4c78e4e34ace55d6a67a74f1571bc205959f5972f88f0
  languageName: node
  linkType: hard

"supports-color@npm:^3.1.2, supports-color@npm:^3.2.3":
  version: 3.2.3
  resolution: "supports-color@npm:3.2.3"
  dependencies:
    has-flag: ^1.0.0
  checksum: 56afc05fa87d00100d90148c4d0a6e20a0af0d56dca5c54d4d40b2553ee737dab0ca4e8b53c4471afc035227b5b44dfa4824747a7f01ad733173536f7da6fbbb
  languageName: node
  linkType: hard

"supports-color@npm:^4.0.0":
  version: 4.5.0
  resolution: "supports-color@npm:4.5.0"
  dependencies:
    has-flag: ^2.0.0
  checksum: 6da4f498d5c71e8619f06e4a11d16f044105faf7590b5b005fc84933fbefdf72c2b4e5b7174c66da6ddc68e7f6ef56cc960a5ebd6f2d542d910e259e61b02335
  languageName: node
  linkType: hard

"supports-color@npm:^5.3.0, supports-color@npm:^5.4.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 95f6f4ba5afdf92f495b5a912d4abee8dcba766ae719b975c56c084f5004845f6f5a5f7769f52d53f40e21952a6d87411bafe34af4a01e65f9926002e38e1dac
  languageName: node
  linkType: hard

"supports-color@npm:^6.1.0":
  version: 6.1.0
  resolution: "supports-color@npm:6.1.0"
  dependencies:
    has-flag: ^3.0.0
  checksum: 74358f9535c83ee113fbaac354b11e808060f6e7d8722082ee43af3578469134e89d00026dce2a6b93ce4e5b89d0e9a10f638b2b9f64c7838c2fb2883a47b3d5
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"svg-baker-runtime@npm:^1.4.0":
  version: 1.4.7
  resolution: "svg-baker-runtime@npm:1.4.7"
  dependencies:
    deepmerge: 1.3.2
    mitt: 1.1.2
    svg-baker: ^1.7.0
  checksum: 32fbb512093b77cc43928bef72054a501e3ad81799d0689571f6caa3b794a0992b051796c5b496f96fec0569b9215f351c6119f9628a5a14d56bed45147b5433
  languageName: node
  linkType: hard

"svg-baker@npm:^1.4.0, svg-baker@npm:^1.7.0":
  version: 1.7.0
  resolution: "svg-baker@npm:1.7.0"
  dependencies:
    bluebird: ^3.5.0
    clone: ^2.1.1
    he: ^1.1.1
    image-size: ^0.5.1
    loader-utils: ^1.1.0
    merge-options: 1.0.1
    micromatch: 3.1.0
    postcss: ^5.2.17
    postcss-prefix-selector: ^1.6.0
    posthtml-rename-id: ^1.0
    posthtml-svg-mode: ^1.0.3
    query-string: ^4.3.2
    traverse: ^0.6.6
  checksum: 06724dd6cd098016a11a778cfa2771defac6bdcdee1e4c61669aa32ebfa52815d66cea989ea1f1d31b440634ceda54637e2d7d57687925b0d256adf9ba176b50
  languageName: node
  linkType: hard

"svg-sprite-loader@npm:4.1.3":
  version: 4.1.3
  resolution: "svg-sprite-loader@npm:4.1.3"
  dependencies:
    bluebird: ^3.5.0
    deepmerge: 1.3.2
    domready: 1.0.8
    escape-string-regexp: 1.0.5
    html-webpack-plugin: ^3.2.0
    loader-utils: ^1.1.0
    svg-baker: ^1.4.0
    svg-baker-runtime: ^1.4.0
    url-slug: 2.0.0
  checksum: b947d6a341e69e3b49159f799ed1137f62a1c9e7b098f18ded5054b289688299a1f4880df92f8094571a136fb5606511ddb1f0e7ebee51ff6477039368220cc4
  languageName: node
  linkType: hard

"svg-tags@npm:^1.0.0":
  version: 1.0.0
  resolution: "svg-tags@npm:1.0.0"
  checksum: 407e5ef87cfa2fb81c61d738081c2decd022ce13b922d035b214b49810630bf5d1409255a4beb3a940b77b32f6957806deff16f1bf0ce1ab11c7a184115a0b7f
  languageName: node
  linkType: hard

"svgo@npm:1.2.2":
  version: 1.2.2
  resolution: "svgo@npm:1.2.2"
  dependencies:
    chalk: ^2.4.1
    coa: ^2.0.2
    css-select: ^2.0.0
    css-select-base-adapter: ^0.1.1
    css-tree: 1.0.0-alpha.28
    css-url-regex: ^1.1.0
    csso: ^3.5.1
    js-yaml: ^3.13.1
    mkdirp: ~0.5.1
    object.values: ^1.1.0
    sax: ~1.2.4
    stable: ^0.1.8
    unquote: ~1.1.1
    util.promisify: ~1.0.0
  bin:
    svgo: ./bin/svgo
  checksum: 2e7d8a9f9620d14f5f1c113aac693dd191815295f4a2f16f0e1d20cc7f0d90269437ee82779d993a349365688c4fb52ceee75f8ca437a7022e32a2cca7dde33e
  languageName: node
  linkType: hard

"svgo@npm:^1.0.0":
  version: 1.3.2
  resolution: "svgo@npm:1.3.2"
  dependencies:
    chalk: ^2.4.1
    coa: ^2.0.2
    css-select: ^2.0.0
    css-select-base-adapter: ^0.1.1
    css-tree: 1.0.0-alpha.37
    csso: ^4.0.2
    js-yaml: ^3.13.1
    mkdirp: ~0.5.1
    object.values: ^1.1.0
    sax: ~1.2.4
    stable: ^0.1.8
    unquote: ~1.1.1
    util.promisify: ~1.0.0
  bin:
    svgo: ./bin/svgo
  checksum: 28a5680a61245eb4a1603bc03459095bb01ad5ebd23e95882d886c3c81752313c0a9a9fe48dd0bcbb9a27c52e11c603640df952971573b2b550d9e15a9ee6116
  languageName: node
  linkType: hard

"symbol-tree@npm:^3.2.2":
  version: 3.2.4
  resolution: "symbol-tree@npm:3.2.4"
  checksum: 6e8fc7e1486b8b54bea91199d9535bb72f10842e40c79e882fc94fb7b14b89866adf2fd79efa5ebb5b658bc07fb459ccce5ac0e99ef3d72f474e74aaf284029d
  languageName: node
  linkType: hard

"table@npm:4.0.2":
  version: 4.0.2
  resolution: "table@npm:4.0.2"
  dependencies:
    ajv: ^5.2.3
    ajv-keywords: ^2.1.0
    chalk: ^2.1.0
    lodash: ^4.17.4
    slice-ansi: 1.0.0
    string-width: ^2.1.1
  checksum: 66eecf4992ac8c8d336b6de9b9e771e9ee86e3481f3695f01d227255c458af37fec55894afffd7d0a2caa15eccd4fccd3dbaf973012af85d91f449045693327b
  languageName: node
  linkType: hard

"table@npm:^5.2.3":
  version: 5.4.6
  resolution: "table@npm:5.4.6"
  dependencies:
    ajv: ^6.10.2
    lodash: ^4.17.14
    slice-ansi: ^2.1.0
    string-width: ^3.0.0
  checksum: 9e35d3efa788edc17237eef8852f8e4b9178efd65a7d115141777b2ee77df4b7796c05f4ed3712d858f98894ac5935a481ceeb6dcb9895e2f67a61cce0e63b6c
  languageName: node
  linkType: hard

"tapable@npm:^1.0.0, tapable@npm:^1.1.0, tapable@npm:^1.1.3":
  version: 1.1.3
  resolution: "tapable@npm:1.1.3"
  checksum: 53ff4e7c3900051c38cc4faab428ebfd7e6ad0841af5a7ac6d5f3045c5b50e88497bfa8295b4b3fbcadd94993c9e358868b78b9fb249a76cb8b018ac8dccafd7
  languageName: node
  linkType: hard

"tar@npm:^6.0.2, tar@npm:^6.1.2":
  version: 6.1.11
  resolution: "tar@npm:6.1.11"
  dependencies:
    chownr: ^2.0.0
    fs-minipass: ^2.0.0
    minipass: ^3.0.0
    minizlib: ^2.1.1
    mkdirp: ^1.0.3
    yallist: ^4.0.0
  checksum: a04c07bb9e2d8f46776517d4618f2406fb977a74d914ad98b264fc3db0fe8224da5bec11e5f8902c5b9bcb8ace22d95fbe3c7b36b8593b7dfc8391a25898f32f
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^1.1.0, terser-webpack-plugin@npm:^1.2.3, terser-webpack-plugin@npm:^1.4.3":
  version: 1.4.5
  resolution: "terser-webpack-plugin@npm:1.4.5"
  dependencies:
    cacache: ^12.0.2
    find-cache-dir: ^2.1.0
    is-wsl: ^1.1.0
    schema-utils: ^1.0.0
    serialize-javascript: ^4.0.0
    source-map: ^0.6.1
    terser: ^4.1.2
    webpack-sources: ^1.4.0
    worker-farm: ^1.7.0
  peerDependencies:
    webpack: ^4.0.0
  checksum: 02aada80927d3c8105d69cb00384d307b73aed67d180db5d20023a8d649149f3803ad50f9cd2ef9eb2622005de87e677198ecc5088f51422bfac5d4d57472d0e
  languageName: node
  linkType: hard

"terser@npm:^4.1.2":
  version: 4.8.0
  resolution: "terser@npm:4.8.0"
  dependencies:
    commander: ^2.20.0
    source-map: ~0.6.1
    source-map-support: ~0.5.12
  bin:
    terser: bin/terser
  checksum: f980789097d4f856c1ef4b9a7ada37beb0bb022fb8aa3057968862b5864ad7c244253b3e269c9eb0ab7d0caf97b9521273f2d1cf1e0e942ff0016e0583859c71
  languageName: node
  linkType: hard

"test-exclude@npm:^4.2.1":
  version: 4.2.3
  resolution: "test-exclude@npm:4.2.3"
  dependencies:
    arrify: ^1.0.1
    micromatch: ^2.3.11
    object-assign: ^4.1.0
    read-pkg-up: ^1.0.1
    require-main-filename: ^1.0.1
  checksum: a026932ff069f48cb9ba96a05c9c3eab660ad159b2d56138f69e3e28390e26ac3aa742349d6d7e6cdb2acfb1a57cff0c32d0388732f89761aa6efcced43dca47
  languageName: node
  linkType: hard

"text-table@npm:^0.2.0, text-table@npm:~0.2.0":
  version: 0.2.0
  resolution: "text-table@npm:0.2.0"
  checksum: b6937a38c80c7f84d9c11dd75e49d5c44f71d95e810a3250bd1f1797fc7117c57698204adf676b71497acc205d769d65c16ae8fa10afad832ae1322630aef10a
  languageName: node
  linkType: hard

"thread-loader@npm:^2.1.2":
  version: 2.1.3
  resolution: "thread-loader@npm:2.1.3"
  dependencies:
    loader-runner: ^2.3.1
    loader-utils: ^1.1.0
    neo-async: ^2.6.0
  peerDependencies:
    webpack: ^2.0.0 || ^3.0.0 || ^4.0.0
  checksum: da11b7d0188add50becdffaf3ad44f19bcbae05bfd359af7f08a3a35a85995df73455ea90f45ac14885b299d98faeceaaf7e23f3d508b20d3d9b3ea588af5174
  languageName: node
  linkType: hard

"throat@npm:^4.0.0":
  version: 4.1.0
  resolution: "throat@npm:4.1.0"
  checksum: 43519b0cea6d3b2a8fe056fcbc319e289037be67d2204d4d33513d20d6ee9da6255f7ba8c89e2ec8c97b0f188a910b8666def38d1058d2bf4a39613812c36d98
  languageName: node
  linkType: hard

"throttle-debounce@npm:^1.0.1":
  version: 1.1.0
  resolution: "throttle-debounce@npm:1.1.0"
  checksum: f465f06b11aa6d4b70281615b3d13b610f685220a63d56f40c0caf7257a7be55c72868b974f5438591f509cb1b9756cf10cfad5b409e487e4ed022e9bb61e61f
  languageName: node
  linkType: hard

"through2@npm:^2.0.0":
  version: 2.0.5
  resolution: "through2@npm:2.0.5"
  dependencies:
    readable-stream: ~2.3.6
    xtend: ~4.0.1
  checksum: beb0f338aa2931e5660ec7bf3ad949e6d2e068c31f4737b9525e5201b824ac40cac6a337224856b56bd1ddd866334bbfb92a9f57cd6f66bc3f18d3d86fc0fe50
  languageName: node
  linkType: hard

"through@npm:^2.3.6":
  version: 2.3.8
  resolution: "through@npm:2.3.8"
  checksum: a38c3e059853c494af95d50c072b83f8b676a9ba2818dcc5b108ef252230735c54e0185437618596c790bbba8fcdaef5b290405981ffa09dce67b1f1bf190cbd
  languageName: node
  linkType: hard

"thunky@npm:^1.0.2":
  version: 1.1.0
  resolution: "thunky@npm:1.1.0"
  checksum: 993096c472b6b8f30e29dc777a8d17720e4cab448375041f20c0cb802a09a7fb2217f2a3e8cdc11851faa71c957e2db309357367fc9d7af3cb7a4d00f4b66034
  languageName: node
  linkType: hard

"timers-browserify@npm:^2.0.4":
  version: 2.0.12
  resolution: "timers-browserify@npm:2.0.12"
  dependencies:
    setimmediate: ^1.0.4
  checksum: ec37ae299066bef6c464dcac29c7adafba1999e7227a9bdc4e105a459bee0f0b27234a46bfd7ab4041da79619e06a58433472867a913d01c26f8a203f87cee70
  languageName: node
  linkType: hard

"timsort@npm:^0.3.0":
  version: 0.3.0
  resolution: "timsort@npm:0.3.0"
  checksum: 1a66cb897dacabd7dd7c91b7e2301498ca9e224de2edb9e42d19f5b17c4b6dc62a8d4cbc64f28be82aaf1541cb5a78ab49aa818f42a2989ebe049a64af731e2a
  languageName: node
  linkType: hard

"tiny-emitter@npm:^2.0.0":
  version: 2.1.0
  resolution: "tiny-emitter@npm:2.1.0"
  checksum: fbcfb5145751a0e3b109507a828eb6d6d4501352ab7bb33eccef46e22e9d9ad3953158870a6966a59e57ab7c3f9cfac7cab8521db4de6a5e757012f4677df2dd
  languageName: node
  linkType: hard

"tinymce@npm:^5.8.2":
  version: 5.10.3
  resolution: "tinymce@npm:5.10.3"
  checksum: cdccabec38545a5807f8e06faf828616d37fd958fd571244256937342e880c51711ba13bdb1a95aae89c9ea70014806c8cf70b125abfad6dd488380e4bc53f27
  languageName: node
  linkType: hard

"tmp@npm:^0.0.33":
  version: 0.0.33
  resolution: "tmp@npm:0.0.33"
  dependencies:
    os-tmpdir: ~1.0.2
  checksum: 902d7aceb74453ea02abbf58c203f4a8fc1cead89b60b31e354f74ed5b3fb09ea817f94fb310f884a5d16987dd9fa5a735412a7c2dd088dd3d415aa819ae3a28
  languageName: node
  linkType: hard

"tmpl@npm:1.0.5":
  version: 1.0.5
  resolution: "tmpl@npm:1.0.5"
  checksum: cd922d9b853c00fe414c5a774817be65b058d54a2d01ebb415840960406c669a0fc632f66df885e24cb022ec812739199ccbdb8d1164c3e513f85bfca5ab2873
  languageName: node
  linkType: hard

"to-arraybuffer@npm:^1.0.0":
  version: 1.0.1
  resolution: "to-arraybuffer@npm:1.0.1"
  checksum: 31433c10b388722729f5da04c6b2a06f40dc84f797bb802a5a171ced1e599454099c6c5bc5118f4b9105e7d049d3ad9d0f71182b77650e4fdb04539695489941
  languageName: node
  linkType: hard

"to-fast-properties@npm:^1.0.3":
  version: 1.0.3
  resolution: "to-fast-properties@npm:1.0.3"
  checksum: bd0abb58c4722851df63419de3f6d901d5118f0440d3f71293ed776dd363f2657edaaf2dc470e3f6b7b48eb84aa411193b60db8a4a552adac30de9516c5cc580
  languageName: node
  linkType: hard

"to-fast-properties@npm:^2.0.0":
  version: 2.0.0
  resolution: "to-fast-properties@npm:2.0.0"
  checksum: be2de62fe58ead94e3e592680052683b1ec986c72d589e7b21e5697f8744cdbf48c266fa72f6c15932894c10187b5f54573a3bcf7da0bfd964d5caf23d436168
  languageName: node
  linkType: hard

"to-object-path@npm:^0.3.0":
  version: 0.3.0
  resolution: "to-object-path@npm:0.3.0"
  dependencies:
    kind-of: ^3.0.2
  checksum: 9425effee5b43e61d720940fa2b889623f77473d459c2ce3d4a580a4405df4403eec7be6b857455908070566352f9e2417304641ed158dda6f6a365fe3e66d70
  languageName: node
  linkType: hard

"to-regex-range@npm:^2.1.0":
  version: 2.1.1
  resolution: "to-regex-range@npm:2.1.1"
  dependencies:
    is-number: ^3.0.0
    repeat-string: ^1.6.1
  checksum: 46093cc14be2da905cc931e442d280b2e544e2bfdb9a24b3cf821be8d342f804785e5736c108d5be026021a05d7b38144980a61917eee3c88de0a5e710e10320
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"to-regex@npm:^3.0.1, to-regex@npm:^3.0.2":
  version: 3.0.2
  resolution: "to-regex@npm:3.0.2"
  dependencies:
    define-property: ^2.0.2
    extend-shallow: ^3.0.2
    regex-not: ^1.0.2
    safe-regex: ^1.1.0
  checksum: 4ed4a619059b64e204aad84e4e5f3ea82d97410988bcece7cf6cbfdbf193d11bff48cf53842d88b8bb00b1bfc0d048f61f20f0709e6f393fd8fe0122662d9db4
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 952c29e2a85d7123239b5cfdd889a0dde47ab0497f0913d70588f19c53f7e0b5327c95f4651e413c74b785147f9637b17410ac8c846d5d4a20a5a33eb6dc3a45
  languageName: node
  linkType: hard

"toposort@npm:^1.0.0":
  version: 1.0.7
  resolution: "toposort@npm:1.0.7"
  checksum: 68c074586ae4ad8eb518d5b5ebee7b3ef903ace180d822b9846b52bd945b14db00c1c5e1107ed77ea48705806291a6efda95c90ff5b31ce416257cdc21461a86
  languageName: node
  linkType: hard

"tough-cookie@npm:^2.3.3, tough-cookie@npm:^2.3.4, tough-cookie@npm:~2.5.0":
  version: 2.5.0
  resolution: "tough-cookie@npm:2.5.0"
  dependencies:
    psl: ^1.1.28
    punycode: ^2.1.1
  checksum: 16a8cd090224dd176eee23837cbe7573ca0fa297d7e468ab5e1c02d49a4e9a97bb05fef11320605eac516f91d54c57838a25864e8680e27b069a5231d8264977
  languageName: node
  linkType: hard

"tr46@npm:^1.0.1":
  version: 1.0.1
  resolution: "tr46@npm:1.0.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 96d4ed46bc161db75dbf9247a236ea0bfcaf5758baae6749e92afab0bc5a09cb59af21788ede7e55080f2bf02dce3e4a8f2a484cc45164e29f4b5e68f7cbcc1a
  languageName: node
  linkType: hard

"traverse@npm:^0.6.6":
  version: 0.6.6
  resolution: "traverse@npm:0.6.6"
  checksum: e2afa72f11efa9ba31ed763d2d9d2aa244612f22015d16c0ea3ba5f6ca8bf071de87f8108b721885cce06ea4a36ef4605d9228c67e431d9015ea4685cb364420
  languageName: node
  linkType: hard

"trim-right@npm:^1.0.1":
  version: 1.0.1
  resolution: "trim-right@npm:1.0.1"
  checksum: 9120af534e006a7424a4f9358710e6e707887b6ccf7ea69e50d6ac6464db1fe22268400def01752f09769025d480395159778153fb98d4a2f6f40d4cf5d4f3b6
  languageName: node
  linkType: hard

"tryer@npm:^1.0.1":
  version: 1.0.1
  resolution: "tryer@npm:1.0.1"
  checksum: 1cf14d7f67c79613f054b569bfc9a89c7020d331573a812dfcf7437244e8f8e6eb6893b210cbd9cc217f67c1d72617f89793df231e4fe7d53634ed91cf3a89d1
  languageName: node
  linkType: hard

"tsconfig@npm:^7.0.0":
  version: 7.0.0
  resolution: "tsconfig@npm:7.0.0"
  dependencies:
    "@types/strip-bom": ^3.0.0
    "@types/strip-json-comments": 0.0.30
    strip-bom: ^3.0.0
    strip-json-comments: ^2.0.0
  checksum: 8bce05e93c673defd56d93d83d4055e49651d3947c076339c4bc15d47b7eb5029bed194087e568764213a2e4bf45c477ba9f4da16adfd92cd901af7c09e4517e
  languageName: node
  linkType: hard

"tslib@npm:^1.9.0":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: dbe628ef87f66691d5d2959b3e41b9ca0045c3ee3c7c7b906cc1e328b39f199bb1ad9e671c39025bd56122ac57dfbf7385a94843b1cc07c60a4db74795829acd
  languageName: node
  linkType: hard

"tty-browserify@npm:0.0.0":
  version: 0.0.0
  resolution: "tty-browserify@npm:0.0.0"
  checksum: a06f746acc419cb2527ba19b6f3bd97b4a208c03823bfb37b2982629d2effe30ebd17eaed0d7e2fc741f3c4f2a0c43455bd5fb4194354b378e78cfb7ca687f59
  languageName: node
  linkType: hard

"tunnel-agent@npm:^0.6.0":
  version: 0.6.0
  resolution: "tunnel-agent@npm:0.6.0"
  dependencies:
    safe-buffer: ^5.0.1
  checksum: 05f6510358f8afc62a057b8b692f05d70c1782b70db86d6a1e0d5e28a32389e52fa6e7707b6c5ecccacc031462e4bc35af85ecfe4bbc341767917b7cf6965711
  languageName: node
  linkType: hard

"tweetnacl@npm:^0.14.3, tweetnacl@npm:~0.14.0":
  version: 0.14.5
  resolution: "tweetnacl@npm:0.14.5"
  checksum: 6061daba1724f59473d99a7bb82e13f211cdf6e31315510ae9656fefd4779851cb927adad90f3b488c8ed77c106adc0421ea8055f6f976ff21b27c5c4e918487
  languageName: node
  linkType: hard

"type-check@npm:~0.3.2":
  version: 0.3.2
  resolution: "type-check@npm:0.3.2"
  dependencies:
    prelude-ls: ~1.1.2
  checksum: dd3b1495642731bc0e1fc40abe5e977e0263005551ac83342ecb6f4f89551d106b368ec32ad3fb2da19b3bd7b2d1f64330da2ea9176d8ddbfe389fb286eb5124
  languageName: node
  linkType: hard

"type-fest@npm:^0.6.0":
  version: 0.6.0
  resolution: "type-fest@npm:0.6.0"
  checksum: b2188e6e4b21557f6e92960ec496d28a51d68658018cba8b597bd3ef757721d1db309f120ae987abeeda874511d14b776157ff809f23c6d1ce8f83b9b2b7d60f
  languageName: node
  linkType: hard

"type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: 0.3.0
    mime-types: ~2.1.24
  checksum: 2c8e47675d55f8b4e404bcf529abdf5036c537a04c2b20177bcf78c9e3c1da69da3942b1346e6edb09e823228c0ee656ef0e033765ec39a70d496ef601a0c657
  languageName: node
  linkType: hard

"typedarray@npm:^0.0.6":
  version: 0.0.6
  resolution: "typedarray@npm:0.0.6"
  checksum: 33b39f3d0e8463985eeaeeacc3cb2e28bc3dfaf2a5ed219628c0b629d5d7b810b0eb2165f9f607c34871d5daa92ba1dc69f49051cf7d578b4cbd26c340b9d1b1
  languageName: node
  linkType: hard

"uglify-js@npm:3.4.x":
  version: 3.4.10
  resolution: "uglify-js@npm:3.4.10"
  dependencies:
    commander: ~2.19.0
    source-map: ~0.6.1
  bin:
    uglifyjs: bin/uglifyjs
  checksum: dfc61c85b0660216432e021aac6a5f3ea0331720003d4d929b95f297daceb73bc9615875ca150516b49bc57ab60d3cf32415fc006cccf20f275c806f6686da0d
  languageName: node
  linkType: hard

"uglify-js@npm:^3.1.4":
  version: 3.15.1
  resolution: "uglify-js@npm:3.15.1"
  bin:
    uglifyjs: bin/uglifyjs
  checksum: cf88574ec8af4d69368142a3f9fb83ac11b1344a117dff08890fcf99ed12c782c810f02e71a0c2a7e8666ea6225894f1c171cbd90e1a1fe4b2c4a198f8ad61a3
  languageName: node
  linkType: hard

"unbox-primitive@npm:^1.0.1":
  version: 1.0.1
  resolution: "unbox-primitive@npm:1.0.1"
  dependencies:
    function-bind: ^1.1.1
    has-bigints: ^1.0.1
    has-symbols: ^1.0.2
    which-boxed-primitive: ^1.0.2
  checksum: 89d950e18fb45672bc6b3c961f1e72c07beb9640c7ceed847b571ba6f7d2af570ae1a2584cfee268b9d9ea1e3293f7e33e0bc29eaeb9f8e8a0bab057ff9e6bba
  languageName: node
  linkType: hard

"unicode-canonical-property-names-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-canonical-property-names-ecmascript@npm:2.0.0"
  checksum: 39be078afd014c14dcd957a7a46a60061bc37c4508ba146517f85f60361acf4c7539552645ece25de840e17e293baa5556268d091ca6762747fdd0c705001a45
  languageName: node
  linkType: hard

"unicode-match-property-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-ecmascript@npm:2.0.0"
  dependencies:
    unicode-canonical-property-names-ecmascript: ^2.0.0
    unicode-property-aliases-ecmascript: ^2.0.0
  checksum: 1f34a7434a23df4885b5890ac36c5b2161a809887000be560f56ad4b11126d433c0c1c39baf1016bdabed4ec54829a6190ee37aa24919aa116dc1a5a8a62965a
  languageName: node
  linkType: hard

"unicode-match-property-value-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-match-property-value-ecmascript@npm:2.0.0"
  checksum: 8fe6a09d9085a625cabcead5d95bdbc1a2d5d481712856092ce0347231e81a60b93a68f1b69e82b3076a07e415a72c708044efa2aa40ae23e2e7b5c99ed4a9ea
  languageName: node
  linkType: hard

"unicode-property-aliases-ecmascript@npm:^2.0.0":
  version: 2.0.0
  resolution: "unicode-property-aliases-ecmascript@npm:2.0.0"
  checksum: dda4d39128cbbede2ac60fbb85493d979ec65913b8a486bf7cb7a375a2346fa48cbf9dc6f1ae23376e7e8e684c2b411434891e151e865a661b40a85407db51d0
  languageName: node
  linkType: hard

"unidecode@npm:0.1.8":
  version: 0.1.8
  resolution: "unidecode@npm:0.1.8"
  checksum: 8d07a15a28e6d2b3fb595aa3439d710c968b98a6d7fa5b2d5fdbd3498042f27e1ba1d5925541912ee14368b8a2d35767d66e3f56199f8cde1feafd62a8acd6a2
  languageName: node
  linkType: hard

"union-value@npm:^1.0.0":
  version: 1.0.1
  resolution: "union-value@npm:1.0.1"
  dependencies:
    arr-union: ^3.1.0
    get-value: ^2.0.6
    is-extendable: ^0.1.1
    set-value: ^2.0.1
  checksum: a3464097d3f27f6aa90cf103ed9387541bccfc006517559381a10e0dffa62f465a9d9a09c9b9c3d26d0f4cbe61d4d010e2fbd710fd4bf1267a768ba8a774b0ba
  languageName: node
  linkType: hard

"uniq@npm:^1.0.1":
  version: 1.0.1
  resolution: "uniq@npm:1.0.1"
  checksum: 8206535f83745ea83f9da7035f3b983fd6ed5e35b8ed7745441944e4065b616bc67cf0d0a23a86b40ee0074426f0607f0a138f9b78e124eb6a7a6a6966055709
  languageName: node
  linkType: hard

"uniqs@npm:^2.0.0":
  version: 2.0.0
  resolution: "uniqs@npm:2.0.0"
  checksum: 5ace63e0521fd1ae2c161b3fa167cf6846fc45a71c00496729e0146402c3ae467c6f025a68fbd6766300a9bfbac9f240f2f0198164283bef48012b39db83f81f
  languageName: node
  linkType: hard

"unique-filename@npm:^1.1.0, unique-filename@npm:^1.1.1":
  version: 1.1.1
  resolution: "unique-filename@npm:1.1.1"
  dependencies:
    unique-slug: ^2.0.0
  checksum: cf4998c9228cc7647ba7814e255dec51be43673903897b1786eff2ac2d670f54d4d733357eb08dea969aa5e6875d0e1bd391d668fbdb5a179744e7c7551a6f80
  languageName: node
  linkType: hard

"unique-slug@npm:^2.0.0":
  version: 2.0.2
  resolution: "unique-slug@npm:2.0.2"
  dependencies:
    imurmurhash: ^0.1.4
  checksum: 5b6876a645da08d505dedb970d1571f6cebdf87044cb6b740c8dbb24f0d6e1dc8bdbf46825fd09f994d7cf50760e6f6e063cfa197d51c5902c00a861702eb75a
  languageName: node
  linkType: hard

"universalify@npm:^0.1.0":
  version: 0.1.2
  resolution: "universalify@npm:0.1.2"
  checksum: 40cdc60f6e61070fe658ca36016a8f4ec216b29bf04a55dce14e3710cc84c7448538ef4dad3728d0bfe29975ccd7bfb5f414c45e7b78883567fb31b246f02dff
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 4fa18d8d8d977c55cb09715385c203197105e10a6d220087ec819f50cb68870f02942244f1017565484237f1f8c5d3cd413631b1ae104d3096f24fdfde1b4aa2
  languageName: node
  linkType: hard

"unquote@npm:~1.1.1":
  version: 1.1.1
  resolution: "unquote@npm:1.1.1"
  checksum: 71745867d09cba44ba2d26cb71d6dda7045a98b14f7405df4faaf2b0c90d24703ad027a9d90ba9a6e0d096de2c8d56f864fd03f1c0498c0b7a3990f73b4c8f5f
  languageName: node
  linkType: hard

"unset-value@npm:^1.0.0":
  version: 1.0.0
  resolution: "unset-value@npm:1.0.0"
  dependencies:
    has-value: ^0.3.1
    isobject: ^3.0.0
  checksum: 5990ecf660672be2781fc9fb322543c4aa592b68ed9a3312fa4df0e9ba709d42e823af090fc8f95775b4cd2c9a5169f7388f0cec39238b6d0d55a69fc2ab6b29
  languageName: node
  linkType: hard

"upath@npm:^1.1.1":
  version: 1.2.0
  resolution: "upath@npm:1.2.0"
  checksum: 4c05c094797cb733193a0784774dbea5b1889d502fc9f0572164177e185e4a59ba7099bf0b0adf945b232e2ac60363f9bf18aac9b2206fb99cbef971a8455445
  languageName: node
  linkType: hard

"upper-case@npm:^1.1.1":
  version: 1.1.3
  resolution: "upper-case@npm:1.1.3"
  checksum: 991c845de75fa56e5ad983f15e58494dd77b77cadd79d273cc11e8da400067e9881ae1a52b312aed79b3d754496e2e0712e08d22eae799e35c7f9ba6f3d8a85d
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"urix@npm:^0.1.0":
  version: 0.1.0
  resolution: "urix@npm:0.1.0"
  checksum: 4c076ecfbf3411e888547fe844e52378ab5ada2d2f27625139011eada79925e77f7fbf0e4016d45e6a9e9adb6b7e64981bd49b22700c7c401c5fc15f423303b3
  languageName: node
  linkType: hard

"url-loader@npm:^1.1.2":
  version: 1.1.2
  resolution: "url-loader@npm:1.1.2"
  dependencies:
    loader-utils: ^1.1.0
    mime: ^2.0.3
    schema-utils: ^1.0.0
  peerDependencies:
    webpack: ^3.0.0 || ^4.0.0
  bin:
    url-loader: ""
  checksum: 4bce3d5502863b208d5645df247c6f66c75050d32890970e11d33dbdad1d13218cefeef975f9fce98bd1c043314b182aea7e714c67a913755f9bf6af92965701
  languageName: node
  linkType: hard

"url-parse@npm:^1.4.3, url-parse@npm:^1.5.3":
  version: 1.5.4
  resolution: "url-parse@npm:1.5.4"
  dependencies:
    querystringify: ^2.1.1
    requires-port: ^1.0.0
  checksum: 4e627dca06e649e366a6e0a2becd2df36fd5d37baa9b3362bb5b29bb2e41bba9c62ed37d24d5a4cb5a642cfaf5232163ab6794c9779f85e4ed804366bb8c377e
  languageName: node
  linkType: hard

"url-slug@npm:2.0.0":
  version: 2.0.0
  resolution: "url-slug@npm:2.0.0"
  dependencies:
    unidecode: 0.1.8
  checksum: f71efd9c6d52429103c2d75898325752c20ebdb795866960e988335667bc3153d46d4a4d7818cc50bffb6a9581b3b0707a38039a20b2e1626a9292212255c849
  languageName: node
  linkType: hard

"url@npm:^0.11.0":
  version: 0.11.0
  resolution: "url@npm:0.11.0"
  dependencies:
    punycode: 1.3.2
    querystring: 0.2.0
  checksum: 50d100d3dd2d98b9fe3ada48cadb0b08aa6be6d3ac64112b867b56b19be4bfcba03c2a9a0d7922bfd7ac17d4834e88537749fe182430dfd9b68e520175900d90
  languageName: node
  linkType: hard

"use@npm:^3.1.0":
  version: 3.1.1
  resolution: "use@npm:3.1.1"
  checksum: 08a130289f5238fcbf8f59a18951286a6e660d17acccc9d58d9b69dfa0ee19aa038e8f95721b00b432c36d1629a9e32a464bf2e7e0ae6a244c42ddb30bdd8b33
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1, util-deprecate@npm:^1.0.2, util-deprecate@npm:~1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 474acf1146cb2701fe3b074892217553dfcf9a031280919ba1b8d651a068c9b15d863b7303cb15bd00a862b498e6cf4ad7b4a08fb134edd5a6f7641681cb54a2
  languageName: node
  linkType: hard

"util.promisify@npm:1.0.0":
  version: 1.0.0
  resolution: "util.promisify@npm:1.0.0"
  dependencies:
    define-properties: ^1.1.2
    object.getownpropertydescriptors: ^2.0.3
  checksum: 482e857d676adee506c5c3a10212fd6a06a51d827a9b6d5396a8e593db53b4bb7064f77c5071357d8cd76072542de5cc1c08bc6d7c10cf43fa22dc3bc67556f1
  languageName: node
  linkType: hard

"util.promisify@npm:^1.0.0":
  version: 1.1.1
  resolution: "util.promisify@npm:1.1.1"
  dependencies:
    call-bind: ^1.0.0
    define-properties: ^1.1.3
    for-each: ^0.3.3
    has-symbols: ^1.0.1
    object.getownpropertydescriptors: ^2.1.1
  checksum: ea371c30b90576862487ae4efd7182aa5855019549a4019d82629acc2709e8ccb0f38944403eebec622fff8ebb44ac3f46a52d745d5f543d30606132a4905f96
  languageName: node
  linkType: hard

"util.promisify@npm:~1.0.0":
  version: 1.0.1
  resolution: "util.promisify@npm:1.0.1"
  dependencies:
    define-properties: ^1.1.3
    es-abstract: ^1.17.2
    has-symbols: ^1.0.1
    object.getownpropertydescriptors: ^2.1.0
  checksum: d823c75b3fc66510018596f128a6592c98991df38bc0464a633bdf9134e2de0a1a33199c5c21cc261048a3982d7a19e032ecff8835b3c587f843deba96063e37
  languageName: node
  linkType: hard

"util@npm:0.10.3":
  version: 0.10.3
  resolution: "util@npm:0.10.3"
  dependencies:
    inherits: 2.0.1
  checksum: bd800f5d237a82caddb61723a6cbe45297d25dd258651a31335a4d5d981fd033cb4771f82db3d5d59b582b187cb69cfe727dc6f4d8d7826f686ee6c07ce611e0
  languageName: node
  linkType: hard

"util@npm:^0.11.0":
  version: 0.11.1
  resolution: "util@npm:0.11.1"
  dependencies:
    inherits: 2.0.3
  checksum: 80bee6a2edf5ab08dcb97bfe55ca62289b4e66f762ada201f2c5104cb5e46474c8b334f6504d055c0e6a8fda10999add9bcbd81ba765e7f37b17dc767331aa55
  languageName: node
  linkType: hard

"utila@npm:~0.4":
  version: 0.4.0
  resolution: "utila@npm:0.4.0"
  checksum: 97ffd3bd2bb80c773429d3fb8396469115cd190dded1e733f190d8b602bd0a1bcd6216b7ce3c4395ee3c79e3c879c19d268dbaae3093564cb169ad1212d436f4
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: c81095493225ecfc28add49c106ca4f09cdf56bc66731aa8dabc2edbbccb1e1bfe2de6a115e5c6a380d3ea166d1636410b62ef216bb07b3feb1cfde1d95d5080
  languageName: node
  linkType: hard

"uuid@npm:^3.3.2":
  version: 3.4.0
  resolution: "uuid@npm:3.4.0"
  bin:
    uuid: ./bin/uuid
  checksum: 58de2feed61c59060b40f8203c0e4ed7fd6f99d42534a499f1741218a1dd0c129f4aa1de797bcf822c8ea5da7e4137aa3673431a96dae729047f7aca7b27866f
  languageName: node
  linkType: hard

"uuid@npm:^8.3.2":
  version: 8.3.2
  resolution: "uuid@npm:8.3.2"
  bin:
    uuid: dist/bin/uuid
  checksum: 5575a8a75c13120e2f10e6ddc801b2c7ed7d8f3c8ac22c7ed0c7b2ba6383ec0abda88c905085d630e251719e0777045ae3236f04c812184b7c765f63a70e58df
  languageName: node
  linkType: hard

"validate-npm-package-license@npm:^3.0.1":
  version: 3.0.4
  resolution: "validate-npm-package-license@npm:3.0.4"
  dependencies:
    spdx-correct: ^3.0.0
    spdx-expression-parse: ^3.0.0
  checksum: 35703ac889d419cf2aceef63daeadbe4e77227c39ab6287eeb6c1b36a746b364f50ba22e88591f5d017bc54685d8137bc2d328d0a896e4d3fd22093c0f32a9ad
  languageName: node
  linkType: hard

"vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: ae0123222c6df65b437669d63dfa8c36cee20a504101b2fcd97b8bf76f91259c17f9f2b4d70a1e3c6bbcee7f51b28392833adb6b2770b23b01abec84e369660b
  languageName: node
  linkType: hard

"vendors@npm:^1.0.0":
  version: 1.0.4
  resolution: "vendors@npm:1.0.4"
  checksum: 4b16e0bc18dbdd7ac8dd745c776c08f6c73e9a7f620ffd9faf94a3d86a35feaf4c6cb1bbdb304d2381548a30d0abe69b83eeb1b7b1bf5bb33935e64b28812681
  languageName: node
  linkType: hard

"verror@npm:1.10.0":
  version: 1.10.0
  resolution: "verror@npm:1.10.0"
  dependencies:
    assert-plus: ^1.0.0
    core-util-is: 1.0.2
    extsprintf: ^1.2.0
  checksum: c431df0bedf2088b227a4e051e0ff4ca54df2c114096b0c01e1cbaadb021c30a04d7dd5b41ab277bcd51246ca135bf931d4c4c796ecae7a4fef6d744ecef36ea
  languageName: node
  linkType: hard

"vm-browserify@npm:^1.0.1":
  version: 1.1.2
  resolution: "vm-browserify@npm:1.1.2"
  checksum: 10a1c50aab54ff8b4c9042c15fc64aefccce8d2fb90c0640403242db0ee7fb269f9b102bdb69cfb435d7ef3180d61fd4fb004a043a12709abaf9056cfd7e039d
  languageName: node
  linkType: hard

"vue-admin-template@workspace:.":
  version: 0.0.0-use.local
  resolution: "vue-admin-template@workspace:."
  dependencies:
    "@babel/core": 7.0.0
    "@babel/register": 7.0.0
    "@vue/cli-plugin-babel": 3.6.0
    "@vue/cli-plugin-eslint": ^3.9.1
    "@vue/cli-plugin-unit-jest": 3.6.3
    "@vue/cli-service": 3.6.0
    "@vue/test-utils": 1.0.0-beta.29
    autoprefixer: ^9.5.1
    axios: 0.18.1
    babel-core: 7.0.0-bridge.0
    babel-eslint: 10.0.1
    babel-jest: 23.6.0
    chalk: 2.4.2
    clipboard: ^2.0.6
    connect: 3.6.6
    dayjs: ^1.8.17
    element-ui: ^2.13.2
    eslint: 5.15.3
    eslint-plugin-vue: 5.2.2
    html-webpack-plugin: 3.2.0
    js-cookie: 2.2.0
    jsencrypt: ^3.0.0-rc.1
    mockjs: 1.0.1-beta3
    normalize.css: 7.0.0
    nprogress: 0.2.0
    path-to-regexp: 2.4.0
    runjs: ^4.3.2
    sass: ^1.27.1
    sass-loader: ^7.1.0
    script-ext-html-webpack-plugin: 2.1.3
    script-loader: 0.7.2
    serve-static: ^1.13.2
    svg-sprite-loader: 4.1.3
    svgo: 1.2.2
    tinymce: ^5.8.2
    vue: 2.6.10
    vue-i18n: ^8.15.0
    vue-router: 3.0.6
    vue-sticky: ^3.3.4
    vue-template-compiler: 2.6.10
    vuex: 3.1.0
  languageName: unknown
  linkType: soft

"vue-eslint-parser@npm:^2.0.3":
  version: 2.0.3
  resolution: "vue-eslint-parser@npm:2.0.3"
  dependencies:
    debug: ^3.1.0
    eslint-scope: ^3.7.1
    eslint-visitor-keys: ^1.0.0
    espree: ^3.5.2
    esquery: ^1.0.0
    lodash: ^4.17.4
  peerDependencies:
    eslint: ">=3.9.0"
  checksum: 5930e62e788901ad30beb3cc34848496e6f01ad0724bf548f46972d0625e4060eb81d40dcffb64da4e70098ee4f9de410332564d5700689ffe17795e9c06e77f
  languageName: node
  linkType: hard

"vue-eslint-parser@npm:^5.0.0":
  version: 5.0.0
  resolution: "vue-eslint-parser@npm:5.0.0"
  dependencies:
    debug: ^4.1.0
    eslint-scope: ^4.0.0
    eslint-visitor-keys: ^1.0.0
    espree: ^4.1.0
    esquery: ^1.0.1
    lodash: ^4.17.11
  peerDependencies:
    eslint: ^5.0.0
  checksum: 16a1a99b599d10366add2b54f42e3b5775b5f55fdcdf3a77af3c38b3648c41c42d461cdcbd7ea2f42664c57ce08771543bc0d91d5014b50878432018b681296e
  languageName: node
  linkType: hard

"vue-hot-reload-api@npm:^2.3.0":
  version: 2.3.4
  resolution: "vue-hot-reload-api@npm:2.3.4"
  checksum: 9befc0b3d6c1cc69430813fb7cfd2125c6a228730a36fad0653e4ddb60c8d4cf3ddc9649d2c9105c3d6044b42e8c8dce62b3c245bc65a6f187c1e2ca82a79252
  languageName: node
  linkType: hard

"vue-i18n@npm:^8.15.0":
  version: 8.27.0
  resolution: "vue-i18n@npm:8.27.0"
  checksum: 6d5af73e3ef219fa2948f508b2fcbee5d669459293f2fab2a08bebaad80f93b6190b0fcae7b5f7b1aa7eab61c896e6b2d4a7e52483282d64f9f5e1d9d0d3af10
  languageName: node
  linkType: hard

"vue-jest@npm:^3.0.4":
  version: 3.0.7
  resolution: "vue-jest@npm:3.0.7"
  dependencies:
    babel-plugin-transform-es2015-modules-commonjs: ^6.26.0
    chalk: ^2.1.0
    deasync: ^0.1.15
    extract-from-css: ^0.4.4
    find-babel-config: ^1.1.0
    js-beautify: ^1.6.14
    node-cache: ^4.1.1
    object-assign: ^4.1.1
    source-map: ^0.5.6
    tsconfig: ^7.0.0
    vue-template-es2015-compiler: ^1.6.0
  peerDependencies:
    babel-core: ^6.25.0 || ^7.0.0-0
    vue: ^2.x
    vue-template-compiler: ^2.x
  checksum: f08a228c345b28d314cf6cf40420eedff988f6274a6a3ad9525776644b4116f7bbc9aa34c04d5ee9c32a4440d919c7b6c5628ed20f543ba9c585bf221e7f4050
  languageName: node
  linkType: hard

"vue-loader@npm:^15.7.0":
  version: 15.9.8
  resolution: "vue-loader@npm:15.9.8"
  dependencies:
    "@vue/component-compiler-utils": ^3.1.0
    hash-sum: ^1.0.2
    loader-utils: ^1.1.0
    vue-hot-reload-api: ^2.3.0
    vue-style-loader: ^4.1.0
  peerDependencies:
    css-loader: "*"
    webpack: ^3.0.0 || ^4.1.0 || ^5.0.0-0
  peerDependenciesMeta:
    cache-loader:
      optional: true
    vue-template-compiler:
      optional: true
  checksum: ca4c99b2617b207eb96925b889669f8bfecb6e82d22ed59220b324b6caaccc38bf3bc1d7961353155ab19ec71b791e887e8a06109ec719e8a791a2b00a2420bc
  languageName: node
  linkType: hard

"vue-router@npm:3.0.6":
  version: 3.0.6
  resolution: "vue-router@npm:3.0.6"
  checksum: 1517adb3890a8d1b0e7c5f2435ce1f6717ca4a21f2377f51e9c0fce435b63bd6ea61eeade2069713dafac226e91c34a0cf9c2befa03da109819fc36eda80226d
  languageName: node
  linkType: hard

"vue-sticky@npm:^3.3.4":
  version: 3.3.4
  resolution: "vue-sticky@npm:3.3.4"
  checksum: d2bbf81b146fa0135fa41ce60a16aa09cf97b6eb1ba988edd2b0fd6b68d002c97cfd126a9d10509c2cdc5b247cdf72d16c9ce66c0e30fe3245d3b28d53e3175b
  languageName: node
  linkType: hard

"vue-style-loader@npm:^4.1.0":
  version: 4.1.3
  resolution: "vue-style-loader@npm:4.1.3"
  dependencies:
    hash-sum: ^1.0.2
    loader-utils: ^1.0.2
  checksum: ef79d0c6329303d69c87f128f67e486bd37e9a8d416aa662edafae62fab727117b7452f50be8b11fe0c4cb43992344d5ef6a46b206f375fca4d37ae5a5b99185
  languageName: node
  linkType: hard

"vue-template-compiler@npm:2.6.10":
  version: 2.6.10
  resolution: "vue-template-compiler@npm:2.6.10"
  dependencies:
    de-indent: ^1.0.2
    he: ^1.1.0
  checksum: 9cec9364c5fe289269754e01b171d8fb5cc8fb21a6a864be6647a1aaf0f8505cb1385333c3fe6bf65433913d93d4fc7c45e6d6af91ec84ea4730cff8847fddde
  languageName: node
  linkType: hard

"vue-template-es2015-compiler@npm:^1.6.0, vue-template-es2015-compiler@npm:^1.9.0":
  version: 1.9.1
  resolution: "vue-template-es2015-compiler@npm:1.9.1"
  checksum: ad1e85662783be3ee262c323b05d12e6a5036fca24f16dc0f7ab92736b675919cb4fa4b79b28753eac73119b709d1b36789bf60e8ae423f50c4db35de9370e8b
  languageName: node
  linkType: hard

"vue@npm:2.6.10":
  version: 2.6.10
  resolution: "vue@npm:2.6.10"
  checksum: 9a3c35561ce750df9cf90ee4f3cdbfce63ff843262557400144d6b3d0fbfaf7e4fb3a06d002c5efdfa0e81301f1118707593e0e75984259fb81c951987224597
  languageName: node
  linkType: hard

"vuex@npm:3.1.0":
  version: 3.1.0
  resolution: "vuex@npm:3.1.0"
  checksum: 61c2e305eb993bf59b9ba29faab5a1ee41c42d83f298fea3eaeb75dd73615a976ea8a47174d8ff3079db422b80fb884095834df9ccba6f99ac0789e1a34b756f
  languageName: node
  linkType: hard

"w3c-hr-time@npm:^1.0.1":
  version: 1.0.2
  resolution: "w3c-hr-time@npm:1.0.2"
  dependencies:
    browser-process-hrtime: ^1.0.0
  checksum: ec3c2dacbf8050d917bbf89537a101a08c2e333b4c19155f7d3bedde43529d4339db6b3d049d9610789cb915f9515f8be037e0c54c079e9d4735c50b37ed52b9
  languageName: node
  linkType: hard

"walker@npm:~1.0.5":
  version: 1.0.8
  resolution: "walker@npm:1.0.8"
  dependencies:
    makeerror: 1.0.12
  checksum: ad7a257ea1e662e57ef2e018f97b3c02a7240ad5093c392186ce0bcf1f1a60bbadd520d073b9beb921ed99f64f065efb63dfc8eec689a80e569f93c1c5d5e16c
  languageName: node
  linkType: hard

"watch@npm:~0.18.0":
  version: 0.18.0
  resolution: "watch@npm:0.18.0"
  dependencies:
    exec-sh: ^0.2.0
    minimist: ^1.2.0
  bin:
    watch: ./cli.js
  checksum: 8efc9b0f1b71ab854d121f70b361aea6032abf0bc7a28ec68f64be5a9e939086ffbf53df2370cd77c71e75e2c39d0025d59f6e0a52779a6748c3ad6863889094
  languageName: node
  linkType: hard

"watchpack-chokidar2@npm:^2.0.1":
  version: 2.0.1
  resolution: "watchpack-chokidar2@npm:2.0.1"
  dependencies:
    chokidar: ^2.1.8
  checksum: acf0f9ebca0c0b2fd1fe87ba557670477a6c0410bf1a653a726e68eb0620aa94fd9a43027a160a76bc793a21ea12e215e1e87dafe762682c13ef92ad4daf7b58
  languageName: node
  linkType: hard

"watchpack@npm:^1.5.0, watchpack@npm:^1.7.4":
  version: 1.7.5
  resolution: "watchpack@npm:1.7.5"
  dependencies:
    chokidar: ^3.4.1
    graceful-fs: ^4.1.2
    neo-async: ^2.5.0
    watchpack-chokidar2: ^2.0.1
  dependenciesMeta:
    chokidar:
      optional: true
    watchpack-chokidar2:
      optional: true
  checksum: 8b7cb8c8df8f4dd0e8ac47693c0141c4f020a4b031411247d600eca31522fde6f1f9a3a6f6518b46e71f7971b0ed5734c08c60d7fdd2530e7262776286f69236
  languageName: node
  linkType: hard

"wbuf@npm:^1.1.0, wbuf@npm:^1.7.3":
  version: 1.7.3
  resolution: "wbuf@npm:1.7.3"
  dependencies:
    minimalistic-assert: ^1.0.0
  checksum: 2abc306c96930b757972a1c4650eb6b25b5d99f24088714957f88629e137db569368c5de0e57986c89ea70db2f1df9bba11a87cb6d0c8694b6f53a0159fab3bf
  languageName: node
  linkType: hard

"wcwidth@npm:^1.0.1":
  version: 1.0.1
  resolution: "wcwidth@npm:1.0.1"
  dependencies:
    defaults: ^1.0.3
  checksum: 814e9d1ddcc9798f7377ffa448a5a3892232b9275ebb30a41b529607691c0491de47cba426e917a4d08ded3ee7e9ba2f3fe32e62ee3cd9c7d3bafb7754bd553c
  languageName: node
  linkType: hard

"webidl-conversions@npm:^4.0.2":
  version: 4.0.2
  resolution: "webidl-conversions@npm:4.0.2"
  checksum: c93d8dfe908a0140a4ae9c0ebc87a33805b416a33ee638a605b551523eec94a9632165e54632f6d57a39c5f948c4bab10e0e066525e9a4b87a79f0d04fbca374
  languageName: node
  linkType: hard

"webpack-bundle-analyzer@npm:^3.3.0":
  version: 3.9.0
  resolution: "webpack-bundle-analyzer@npm:3.9.0"
  dependencies:
    acorn: ^7.1.1
    acorn-walk: ^7.1.1
    bfj: ^6.1.1
    chalk: ^2.4.1
    commander: ^2.18.0
    ejs: ^2.6.1
    express: ^4.16.3
    filesize: ^3.6.1
    gzip-size: ^5.0.0
    lodash: ^4.17.19
    mkdirp: ^0.5.1
    opener: ^1.5.1
    ws: ^6.0.0
  bin:
    webpack-bundle-analyzer: lib/bin/analyzer.js
  checksum: 7fb83afb137bca5102a07c998c70cffdc2e212334ff43cb057fe1b12d0d555b5aa05183eac4b7ad34c2b59a4e387fed1872d2b8ec658c870c985310e79795d61
  languageName: node
  linkType: hard

"webpack-chain@npm:^4.11.0":
  version: 4.12.1
  resolution: "webpack-chain@npm:4.12.1"
  dependencies:
    deepmerge: ^1.5.2
    javascript-stringify: ^1.6.0
  checksum: ec329d316dd944445f589ec93f97269870cba6fa6e5609d5d84680a4fabc172ef47c98ce0bea829c9eede0ceeb1be7f8c0f821aabda3d5bc502fc20979ea32ed
  languageName: node
  linkType: hard

"webpack-dev-middleware@npm:^3.7.2":
  version: 3.7.3
  resolution: "webpack-dev-middleware@npm:3.7.3"
  dependencies:
    memory-fs: ^0.4.1
    mime: ^2.4.4
    mkdirp: ^0.5.1
    range-parser: ^1.2.1
    webpack-log: ^2.0.0
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  checksum: faa3cdd7b82d23c35b8f45903556eadd92b0795c76f3e08e234d53f7bab3de13331096a71968e7e9905770ae5de7a4f75ddf09f66d1e0bbabfecbb30db0f71e3
  languageName: node
  linkType: hard

"webpack-dev-server@npm:^3.3.1":
  version: 3.11.3
  resolution: "webpack-dev-server@npm:3.11.3"
  dependencies:
    ansi-html-community: 0.0.8
    bonjour: ^3.5.0
    chokidar: ^2.1.8
    compression: ^1.7.4
    connect-history-api-fallback: ^1.6.0
    debug: ^4.1.1
    del: ^4.1.1
    express: ^4.17.1
    html-entities: ^1.3.1
    http-proxy-middleware: 0.19.1
    import-local: ^2.0.0
    internal-ip: ^4.3.0
    ip: ^1.1.5
    is-absolute-url: ^3.0.3
    killable: ^1.0.1
    loglevel: ^1.6.8
    opn: ^5.5.0
    p-retry: ^3.0.1
    portfinder: ^1.0.26
    schema-utils: ^1.0.0
    selfsigned: ^1.10.8
    semver: ^6.3.0
    serve-index: ^1.9.1
    sockjs: ^0.3.21
    sockjs-client: ^1.5.0
    spdy: ^4.0.2
    strip-ansi: ^3.0.1
    supports-color: ^6.1.0
    url: ^0.11.0
    webpack-dev-middleware: ^3.7.2
    webpack-log: ^2.0.0
    ws: ^6.2.1
    yargs: ^13.3.2
  peerDependencies:
    webpack: ^4.0.0 || ^5.0.0
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack-dev-server: bin/webpack-dev-server.js
  checksum: ae2dbcfcd9e8064b00b9c369343b4d4ff31c30a37c459f00b40d27fd6008188edd20ab8497155cd39f0ba704682fc60ca065b6458b54d2dac938b290e0df8cd9
  languageName: node
  linkType: hard

"webpack-log@npm:^2.0.0":
  version: 2.0.0
  resolution: "webpack-log@npm:2.0.0"
  dependencies:
    ansi-colors: ^3.0.0
    uuid: ^3.3.2
  checksum: 4757179310995e20633ec2d77a8c1ac11e4135c84745f57148692f8195f1c0f8ec122c77d0dc16fc484b7d301df6674f36c9fc6b1ff06b5cf142abaaf5d24f4f
  languageName: node
  linkType: hard

"webpack-merge@npm:^4.2.1":
  version: 4.2.2
  resolution: "webpack-merge@npm:4.2.2"
  dependencies:
    lodash: ^4.17.15
  checksum: ce58bc8ab53a3dd5d9a0df65684571349eef53372bf8f224521072110485391335b26ab097c5f07829b88d0c146056944149566e5a953f05997b0fe2cbaf8dd6
  languageName: node
  linkType: hard

"webpack-sources@npm:^1.1.0, webpack-sources@npm:^1.3.0, webpack-sources@npm:^1.4.0, webpack-sources@npm:^1.4.1":
  version: 1.4.3
  resolution: "webpack-sources@npm:1.4.3"
  dependencies:
    source-list-map: ^2.0.0
    source-map: ~0.6.1
  checksum: 37463dad8d08114930f4bc4882a9602941f07c9f0efa9b6bc78738cd936275b990a596d801ef450d022bb005b109b9f451dd087db2f3c9baf53e8e22cf388f79
  languageName: node
  linkType: hard

"webpack@npm:>=4 < 4.29":
  version: 4.28.4
  resolution: "webpack@npm:4.28.4"
  dependencies:
    "@webassemblyjs/ast": 1.7.11
    "@webassemblyjs/helper-module-context": 1.7.11
    "@webassemblyjs/wasm-edit": 1.7.11
    "@webassemblyjs/wasm-parser": 1.7.11
    acorn: ^5.6.2
    acorn-dynamic-import: ^3.0.0
    ajv: ^6.1.0
    ajv-keywords: ^3.1.0
    chrome-trace-event: ^1.0.0
    enhanced-resolve: ^4.1.0
    eslint-scope: ^4.0.0
    json-parse-better-errors: ^1.0.2
    loader-runner: ^2.3.0
    loader-utils: ^1.1.0
    memory-fs: ~0.4.1
    micromatch: ^3.1.8
    mkdirp: ~0.5.0
    neo-async: ^2.5.0
    node-libs-browser: ^2.0.0
    schema-utils: ^0.4.4
    tapable: ^1.1.0
    terser-webpack-plugin: ^1.1.0
    watchpack: ^1.5.0
    webpack-sources: ^1.3.0
  bin:
    webpack: ./bin/webpack.js
  checksum: d21fd4b9573d1a2342ec090105aafe5636edf420cc81de932f1abbade4ea83aab90cdaf12610ed2312736bec932773cfc9eca96c7535780f91d0b9394d11f3fb
  languageName: node
  linkType: hard

"webpack@npm:^4.0.0":
  version: 4.46.0
  resolution: "webpack@npm:4.46.0"
  dependencies:
    "@webassemblyjs/ast": 1.9.0
    "@webassemblyjs/helper-module-context": 1.9.0
    "@webassemblyjs/wasm-edit": 1.9.0
    "@webassemblyjs/wasm-parser": 1.9.0
    acorn: ^6.4.1
    ajv: ^6.10.2
    ajv-keywords: ^3.4.1
    chrome-trace-event: ^1.0.2
    enhanced-resolve: ^4.5.0
    eslint-scope: ^4.0.3
    json-parse-better-errors: ^1.0.2
    loader-runner: ^2.4.0
    loader-utils: ^1.2.3
    memory-fs: ^0.4.1
    micromatch: ^3.1.10
    mkdirp: ^0.5.3
    neo-async: ^2.6.1
    node-libs-browser: ^2.2.1
    schema-utils: ^1.0.0
    tapable: ^1.1.3
    terser-webpack-plugin: ^1.4.3
    watchpack: ^1.7.4
    webpack-sources: ^1.4.1
  peerDependenciesMeta:
    webpack-cli:
      optional: true
    webpack-command:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: 013fa24c00d4261e16ebca60353fa6f848e417b5a44bdf28c16ebebd67fa61e960420bb314c8df05cfe2dad9b90efabcf38fd6875f2361922769a0384085ef1e
  languageName: node
  linkType: hard

"websocket-driver@npm:>=0.5.1, websocket-driver@npm:^0.7.4":
  version: 0.7.4
  resolution: "websocket-driver@npm:0.7.4"
  dependencies:
    http-parser-js: ">=0.5.1"
    safe-buffer: ">=5.1.0"
    websocket-extensions: ">=0.1.1"
  checksum: fffe5a33fe8eceafd21d2a065661d09e38b93877eae1de6ab5d7d2734c6ed243973beae10ae48c6613cfd675f200e5a058d1e3531bc9e6c5d4f1396ff1f0bfb9
  languageName: node
  linkType: hard

"websocket-extensions@npm:>=0.1.1":
  version: 0.1.4
  resolution: "websocket-extensions@npm:0.1.4"
  checksum: 5976835e68a86afcd64c7a9762ed85f2f27d48c488c707e67ba85e717b90fa066b98ab33c744d64255c9622d349eedecf728e65a5f921da71b58d0e9591b9038
  languageName: node
  linkType: hard

"whatwg-encoding@npm:^1.0.1, whatwg-encoding@npm:^1.0.3":
  version: 1.0.5
  resolution: "whatwg-encoding@npm:1.0.5"
  dependencies:
    iconv-lite: 0.4.24
  checksum: 5be4efe111dce29ddee3448d3915477fcc3b28f991d9cf1300b4e50d6d189010d47bca2f51140a844cf9b726e8f066f4aee72a04d687bfe4f2ee2767b2f5b1e6
  languageName: node
  linkType: hard

"whatwg-mimetype@npm:^2.1.0, whatwg-mimetype@npm:^2.2.0":
  version: 2.3.0
  resolution: "whatwg-mimetype@npm:2.3.0"
  checksum: 23eb885940bcbcca4ff841c40a78e9cbb893ec42743993a42bf7aed16085b048b44b06f3402018931687153550f9a32d259dfa524e4f03577ab898b6965e5383
  languageName: node
  linkType: hard

"whatwg-url@npm:^6.4.1":
  version: 6.5.0
  resolution: "whatwg-url@npm:6.5.0"
  dependencies:
    lodash.sortby: ^4.7.0
    tr46: ^1.0.1
    webidl-conversions: ^4.0.2
  checksum: a10bd5e29f4382cd19789c2a7bbce25416e606b6fefc241c7fe34a2449de5bc5709c165bd13634eda433942d917ca7386a52841780b82dc37afa8141c31a8ebd
  languageName: node
  linkType: hard

"whatwg-url@npm:^7.0.0":
  version: 7.1.0
  resolution: "whatwg-url@npm:7.1.0"
  dependencies:
    lodash.sortby: ^4.7.0
    tr46: ^1.0.1
    webidl-conversions: ^4.0.2
  checksum: fecb07c87290b47d2ec2fb6d6ca26daad3c9e211e0e531dd7566e7ff95b5b3525a57d4f32640ad4adf057717e0c215731db842ad761e61d947e81010e05cf5fd
  languageName: node
  linkType: hard

"which-boxed-primitive@npm:^1.0.2":
  version: 1.0.2
  resolution: "which-boxed-primitive@npm:1.0.2"
  dependencies:
    is-bigint: ^1.0.1
    is-boolean-object: ^1.1.0
    is-number-object: ^1.0.4
    is-string: ^1.0.5
    is-symbol: ^1.0.3
  checksum: 53ce774c7379071729533922adcca47220228405e1895f26673bbd71bdf7fb09bee38c1d6399395927c6289476b5ae0629863427fd151491b71c4b6cb04f3a5e
  languageName: node
  linkType: hard

"which-module@npm:^2.0.0":
  version: 2.0.0
  resolution: "which-module@npm:2.0.0"
  checksum: 809f7fd3dfcb2cdbe0180b60d68100c88785084f8f9492b0998c051d7a8efe56784492609d3f09ac161635b78ea29219eb1418a98c15ce87d085bce905705c9c
  languageName: node
  linkType: hard

"which@npm:^1.2.12, which@npm:^1.2.9, which@npm:^1.3.0":
  version: 1.3.1
  resolution: "which@npm:1.3.1"
  dependencies:
    isexe: ^2.0.0
  bin:
    which: ./bin/which
  checksum: f2e185c6242244b8426c9df1510e86629192d93c1a986a7d2a591f2c24869e7ffd03d6dac07ca863b2e4c06f59a4cc9916c585b72ee9fa1aa609d0124df15e04
  languageName: node
  linkType: hard

"which@npm:^2.0.2":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"wide-align@npm:^1.1.2":
  version: 1.1.5
  resolution: "wide-align@npm:1.1.5"
  dependencies:
    string-width: ^1.0.2 || 2 || 3 || 4
  checksum: d5fc37cd561f9daee3c80e03b92ed3e84d80dde3365a8767263d03dacfc8fa06b065ffe1df00d8c2a09f731482fcacae745abfbb478d4af36d0a891fad4834d3
  languageName: node
  linkType: hard

"word-wrap@npm:~1.2.3":
  version: 1.2.3
  resolution: "word-wrap@npm:1.2.3"
  checksum: 30b48f91fcf12106ed3186ae4fa86a6a1842416df425be7b60485de14bec665a54a68e4b5156647dec3a70f25e84d270ca8bc8cd23182ed095f5c7206a938c1f
  languageName: node
  linkType: hard

"wordwrap@npm:^1.0.0":
  version: 1.0.0
  resolution: "wordwrap@npm:1.0.0"
  checksum: 2a44b2788165d0a3de71fd517d4880a8e20ea3a82c080ce46e294f0b68b69a2e49cff5f99c600e275c698a90d12c5ea32aff06c311f0db2eb3f1201f3e7b2a04
  languageName: node
  linkType: hard

"worker-farm@npm:^1.7.0":
  version: 1.7.0
  resolution: "worker-farm@npm:1.7.0"
  dependencies:
    errno: ~0.1.7
  checksum: eab917530e1feddf157ec749e9c91b73a886142daa7fdf3490bccbf7b548b2576c43ab8d0a98e72ac755cbc101ca8647a7b1ff2485fddb9e8f53c40c77f5a719
  languageName: node
  linkType: hard

"wrap-ansi@npm:^2.0.0":
  version: 2.1.0
  resolution: "wrap-ansi@npm:2.1.0"
  dependencies:
    string-width: ^1.0.1
    strip-ansi: ^3.0.1
  checksum: 2dacd4b3636f7a53ee13d4d0fe7fa2ed9ad81e9967e17231924ea88a286ec4619a78288de8d41881ee483f4449ab2c0287cde8154ba1bd0126c10271101b2ee3
  languageName: node
  linkType: hard

"wrap-ansi@npm:^5.1.0":
  version: 5.1.0
  resolution: "wrap-ansi@npm:5.1.0"
  dependencies:
    ansi-styles: ^3.2.0
    string-width: ^3.0.0
    strip-ansi: ^5.0.0
  checksum: 9b48c862220e541eb0daa22661b38b947973fc57054e91be5b0f2dcc77741a6875ccab4ebe970a394b4682c8dfc17e888266a105fb8b0a9b23c19245e781ceae
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 159da4805f7e84a3d003d8841557196034155008f817172d4e986bd591f74aa82aa7db55929a54222309e01079a65a92a9e6414da5a6aa4b01ee44a511ac3ee5
  languageName: node
  linkType: hard

"write-file-atomic@npm:^2.1.0":
  version: 2.4.3
  resolution: "write-file-atomic@npm:2.4.3"
  dependencies:
    graceful-fs: ^4.1.11
    imurmurhash: ^0.1.4
    signal-exit: ^3.0.2
  checksum: 2db81f92ae974fd87ab4a5e7932feacaca626679a7c98fcc73ad8fcea5a1950eab32fa831f79e9391ac99b562ca091ad49be37a79045bd65f595efbb8f4596ae
  languageName: node
  linkType: hard

"write@npm:1.0.3":
  version: 1.0.3
  resolution: "write@npm:1.0.3"
  dependencies:
    mkdirp: ^0.5.1
  checksum: 6496197ceb2d6faeeb8b5fe2659ca804e801e4989dff9fb8a66fe76179ce4ccc378c982ef906733caea1220c8dbe05a666d82127959ac4456e70111af8b8df73
  languageName: node
  linkType: hard

"write@npm:^0.2.1":
  version: 0.2.1
  resolution: "write@npm:0.2.1"
  dependencies:
    mkdirp: ^0.5.1
  checksum: 91bf45a4cf5c2a23fb56ca6cd3b1583295dafe7633f5fdf247f45ca212364cb2bcfe0c3040775b9c1efea613a49104da73d4ae5c28accb9d822aeb176fc7731e
  languageName: node
  linkType: hard

"ws@npm:^5.2.0":
  version: 5.2.3
  resolution: "ws@npm:5.2.3"
  dependencies:
    async-limiter: ~1.0.0
  checksum: bdb2223a40c2c68cf91b25a6c9b8c67d5275378ec6187f343314d3df7530e55b77cb9fe79fb1c6a9758389ac5aefc569d24236924b5c65c5dbbaff409ef739fc
  languageName: node
  linkType: hard

"ws@npm:^6.0.0, ws@npm:^6.2.1":
  version: 6.2.2
  resolution: "ws@npm:6.2.2"
  dependencies:
    async-limiter: ~1.0.0
  checksum: aec3154ec51477c094ac2cb5946a156e17561a581fa27005cbf22c53ac57f8d4e5f791dd4bbba6a488602cb28778c8ab7df06251d590507c3c550fd8ebeee949
  languageName: node
  linkType: hard

"xml-name-validator@npm:^3.0.0":
  version: 3.0.0
  resolution: "xml-name-validator@npm:3.0.0"
  checksum: b3ac459afed783c285bb98e4960bd1f3ba12754fd4f2320efa0f9181ca28928c53cc75ca660d15d205e81f92304419afe94c531c7cfb3e0649aa6d140d53ecb0
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0, xtend@npm:~4.0.1":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: ac5dfa738b21f6e7f0dd6e65e1b3155036d68104e67e5d5d1bde74892e327d7e5636a076f625599dc394330a731861e87343ff184b0047fef1360a7ec0a5a36a
  languageName: node
  linkType: hard

"y18n@npm:^3.2.1":
  version: 3.2.2
  resolution: "y18n@npm:3.2.2"
  checksum: 6154fd7544f8bbf5b18cdf77692ed88d389be49c87238ecb4e0d6a5276446cd2a5c29cc4bdbdddfc7e4e498b08df9d7e38df4a1453cf75eecfead392246ea74a
  languageName: node
  linkType: hard

"y18n@npm:^4.0.0":
  version: 4.0.3
  resolution: "y18n@npm:4.0.3"
  checksum: 014dfcd9b5f4105c3bb397c1c8c6429a9df004aa560964fb36732bfb999bfe83d45ae40aeda5b55d21b1ee53d8291580a32a756a443e064317953f08025b1aa4
  languageName: node
  linkType: hard

"yallist@npm:^2.1.2":
  version: 2.1.2
  resolution: "yallist@npm:2.1.2"
  checksum: 9ba99409209f485b6fcb970330908a6d41fa1c933f75e08250316cce19383179a6b70a7e0721b89672ebb6199cc377bf3e432f55100da6a7d6e11902b0a642cb
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 48f7bb00dc19fc635a13a39fe547f527b10c9290e7b3e836b9a8f1ca04d4d342e85714416b3c2ab74949c9c66f9cebb0473e6bc353b79035356103b47641285d
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard

"yargs-parser@npm:^13.1.2":
  version: 13.1.2
  resolution: "yargs-parser@npm:13.1.2"
  dependencies:
    camelcase: ^5.0.0
    decamelize: ^1.2.0
  checksum: c8bb6f44d39a4acd94462e96d4e85469df865de6f4326e0ab1ac23ae4a835e5dd2ddfe588317ebf80c3a7e37e741bd5cb0dc8d92bcc5812baefb7df7c885e86b
  languageName: node
  linkType: hard

"yargs-parser@npm:^9.0.2":
  version: 9.0.2
  resolution: "yargs-parser@npm:9.0.2"
  dependencies:
    camelcase: ^4.1.0
  checksum: 59276dc17b3f842b7b531ea380a915f3c6d32d314d58de02408f6b2b1657cab0c50d9ad2d422491396cf59c0b0c3ba9d5d9fa1077a61c441ae79a5cd79eef3c2
  languageName: node
  linkType: hard

"yargs@npm:^11.0.0":
  version: 11.1.1
  resolution: "yargs@npm:11.1.1"
  dependencies:
    cliui: ^4.0.0
    decamelize: ^1.1.1
    find-up: ^2.1.0
    get-caller-file: ^1.0.1
    os-locale: ^3.1.0
    require-directory: ^2.1.1
    require-main-filename: ^1.0.1
    set-blocking: ^2.0.0
    string-width: ^2.0.0
    which-module: ^2.0.0
    y18n: ^3.2.1
    yargs-parser: ^9.0.2
  checksum: 19cee86190e309f854eed176c668d453291568ebb37d8a466507ac41e6d93867e7e4fd881db869f50673615b28f881a87de3e0b6190e06ecf6d925f41f433962
  languageName: node
  linkType: hard

"yargs@npm:^13.3.2":
  version: 13.3.2
  resolution: "yargs@npm:13.3.2"
  dependencies:
    cliui: ^5.0.0
    find-up: ^3.0.0
    get-caller-file: ^2.0.1
    require-directory: ^2.1.1
    require-main-filename: ^2.0.0
    set-blocking: ^2.0.0
    string-width: ^3.0.0
    which-module: ^2.0.0
    y18n: ^4.0.0
    yargs-parser: ^13.1.2
  checksum: 75c13e837eb2bb25717957ba58d277e864efc0cca7f945c98bdf6477e6ec2f9be6afa9ed8a876b251a21423500c148d7b91e88dee7adea6029bdec97af1ef3e8
  languageName: node
  linkType: hard

"yorkie@npm:^2.0.0":
  version: 2.0.0
  resolution: "yorkie@npm:2.0.0"
  dependencies:
    execa: ^0.8.0
    is-ci: ^1.0.10
    normalize-path: ^1.0.0
    strip-indent: ^2.0.0
  checksum: 6810adaf6be7b6cc115ed9454e5a4e8e0949deecb107d65dc0c196051e862f04d6394ad12fbef0d51e6c730329992ae98625ba82cba5e34775023ddb6dc921b8
  languageName: node
  linkType: hard
