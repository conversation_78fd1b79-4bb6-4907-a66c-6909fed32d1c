<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">签约状态：</span>
          <el-radio-group v-model="searchForm.signStatus">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button
              v-for="(item, index) in $dict('SignStatusEnum')"
              :key="index"
              :label="item.code">
              {{ item.desc }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">姓名：</span>
          <el-input v-model="searchForm.receiveName"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">证件号码：</span>
          <el-input v-model="searchForm.receiveIdCardNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">手机号：</span>
          <el-input v-model="searchForm.receivePhoneNo"></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">商户编号：</span>
          <el-input v-model="searchForm.employerNo"></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            v-model="createTimeRange"
            ref="timepicker"
            type="datetimerange"
            @change="getTimeRange">
          </date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button type="primary" @click="search(true)">查询</el-button>
        <el-button @click="exportSignRecord" v-permission="'sign:signRecord:view'">导出</el-button>
        <el-button type="text" @click="getExportList">查看已导出列表</el-button>
        <el-button type="text" @click="resetField">清空筛选条件</el-button>
      </div>
    </div>

    <el-main class="content-container">
      <el-table :data="response.data">
        <el-table-column type="index" :index="getIndex" label="序号"></el-table-column>
        <el-table-column label="创建时间" width="150" prop="createTime"></el-table-column>
        <el-table-column label="更新时间" width="150" prop="updateTime"></el-table-column>
        <el-table-column label="姓名" prop="receiveName"></el-table-column>
        <el-table-column label="证件号码" width="180" prop="receiveIdCardNo"></el-table-column>
        <el-table-column label="预签约手机号" width="150" prop="receivePhoneNo"></el-table-column>

        <el-table-column label="信息校验状态" prop="infoStatus" width="120">
          <template v-slot="{row}">
            【{{ $dictCode('SuccessFailCodeEnum', row.infoStatus).desc }}】{{ row.infoStatus == '101' ? row.errMsg : ''}}
          </template>
        </el-table-column>

        <el-table-column label="签约状态" width="100">
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.signStatus)">
              {{ $dictCode('SignStatusEnum', row.signStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="商户编号/商户名称" width="140">
          <template v-slot="{row}">
            {{ row.employerNo }} <br/> {{ row.employerName }}
          </template>
        </el-table-column>

        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button
              v-if="row.fileUrl"
              type="text"
              @click="openFile(row.fileUrl)">
              查看文件
            </el-button>
          </template>
        </el-table-column>

      </el-table>
    </el-main>

    <el-footer class="pagination-container">
      <el-pagination
        :total="response.totalRecord"
        :current-page.sync="pageCurrent"
        :page-size.sync="pageSize"
        :page-sizes="[10, 50]"
        @size-change="search(true)"
        @current-change="search()"
        background
        layout="total, sizes, prev, pager, next, jumper"
      ></el-pagination>
    </el-footer>

    <export-record ref="exportRecord" />
  </div>
</template>

<script>
import { getSignList, exportSignRecord } from '@/api/sign'
import ExportRecord from '@/components/ExportRecord';

export default {
  components: {
    ExportRecord
  },
  data() {
    return {
      mainstayList: [],

      searchForm: {
        signStatus: '',
        receiveName: '',
        receiveIdCardNo: '',
        receivePhoneNo: '',
        employerNo: '',
        createBeginDate: '',
        createEndDate: '',
      },
      createTimeRange: [],

      response: {
        data: [],
        totalRecord: 0,
      },

      pageSize: 10,
      pageCurrent: 1,
    }
  },
  mounted() {
    this.search();
  },
  methods: {
    async search(initFlag) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      const { data } = await getSignList({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      });
      this.response.data = data.data;
      this.response.totalRecord = data.totalRecord;
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    resetField() {
      this.searchForm = {
        signStatus: '',
        receiveName: '',
        receiveIdCardNo: '',
        receivePhoneNo: '',
        employerNo: '',
        createBeginDate: '',
        createEndDate: '',
      };
      this.$refs.timepicker.clearTime();
    },
    async exportSignRecord() {
      const { data } = await exportSignRecord(this.searchForm);
      data && this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('13');
    },
    getTimeRange(val) {
      this.searchForm.createBeginDate = val[0];
      this.searchForm.createEndDate = val[1];
    },
    getTagType(status) {
      switch (Number(status)) {
        case 100:
          return 'success';
        case 200:
          return 'danger';
        case 400:
          return 'info';
        default:
          return ''
      }
    },
    async openFile(url) {
      const fileMsg = await this.formatFileMsg(url)
      window.open(fileMsg.fileUrl)
    }
  }
}
</script>

<style lang="scss" scoped>
</style>