<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">用工企业编号：</span>
          <el-input v-model="searchForm.employerNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业名称：</span>
          <el-input v-model="searchForm.employerName"></el-input>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            :start-time.sync="searchForm.createBeginDate"
            :end-time.sync="searchForm.createEndDate"
            type="datetime"
            picker="separate"
            ref="datepicker"
          />
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">税款所属时期：</span>
          <date-picker
            :start-time.sync="searchForm.dateBegin"
            :end-time.sync="searchForm.dateEnd"
            :is-show-fast-time="false"
            :auto-fix="false"
            picker="separate"
            type="month"
            dayjs-format="YYYY-MM-DD"
            ref="monthpicker"
          />
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)">查询
        </el-button>
        <el-button
          type="text"
          @click="resetField">清空筛选条件
        </el-button>
      </div>
    </div>
    <div class="func-container">
      <el-button
        type="primary"
        @click="openTaxDialog()">上传完税证明
      </el-button>
    </div>
    <div class="content-container">
      <el-table :data="list">
        <el-table-column
          label="税款所属时期"
          width="240">
          <template v-slot="{row}">
            {{ row.dateBegin }} 到 {{ row.dateEnd }}
          </template>
        </el-table-column>
        <el-table-column
          label="用工企业"
          width="150">
          <template v-slot="{row}">
            {{ row.employerNo }} <br> {{ row.employerName }}
          </template>
        </el-table-column>
        <el-table-column
          label="开票方"
          prop="mainstayName"></el-table-column>
        <el-table-column
          label="备注"
          prop="remark"></el-table-column>
        <el-table-column
          label="创建时间"
          width="180"
          prop="createTime"></el-table-column>
        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="openTaxDialog(row)">编辑
            </el-button>
            <el-button
              type="text"
              class="red-btn"
              @click="deleteRecord(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-footer class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :page-sizes="[10, 30, 50]"
        :total="page.total"
        :page-size.sync="page.size"
        :current-page.sync="page.current"
        @size-change="search(true)"
        @current-change="search()" />
    </el-footer>

    <tax-cert-dialog
      ref="taxDialog"
      @change="search"
    ></tax-cert-dialog>
  </div>
</template>


<script>
import { deleteCert, getTaxCertList } from "@/api/invoice";
import { toPromise } from "@/utils";
import TaxCertDialog from "./Component/TaxCertDialog";

export default {
  name: 'TaxProfile',
  components: { TaxCertDialog },
  data() {
    return {
      searchForm: {},
      list: [],
      page: {
        current: 1,
        totalRecord: 0,
        size: 10,
      },
    }
  },
  mounted() {
    this.search()
  },
  methods: {
    async search(init) {
      if (init === true) {
        this.page.pageCurrent = 1
      }
      const { data } = await getTaxCertList({
        ...this.searchForm,
        ...this.page,
      })
      this.list = data.records || []
      this.page.total = data.total || 0
    },
    resetField() {
      this.searchForm = {}
      this.$refs.datepicker.clearTime()
      this.$refs.monthpicker.clearTime()
    },
    async deleteRecord({ id }) {
      const [err, _] = await toPromise(
        this.$confirm('此操作将删除该条数据，是否继续？', '提示', {
          type: 'warning'
        })
      )
      if (err) return
      await deleteCert({ id })
      this.search()
    },
    openTaxDialog(row) {
      this.$refs.taxDialog.open(row)
    },

  }
}
</script>
