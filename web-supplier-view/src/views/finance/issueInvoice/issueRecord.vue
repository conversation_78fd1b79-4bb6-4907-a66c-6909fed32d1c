<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">状态：</span>
            <el-select v-model="searchForm.invoiceStatus" clearable>
              <el-option
                v-for="(item, index) in $dict('InvoiceStatusEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">发票流水号：</span>
            <el-input v-model="searchForm.trxNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label middle">用工企业编号：</span>
            <el-input v-model="searchForm.employerMchNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">岗位类目：</span>
            <el-select clearable
              v-model="searchForm.workCategoryCode">
              <el-option
                v-for="(item, index) in workCategoryOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
          <!-- <div class="flex-item">
            <span class="flex-item__label">申请方式：</span>
            <el-input></el-input>
          </div> -->
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label middle">用工企业名称：</span>
            <el-input v-model="searchForm.employerMchNameLike" placeholder="模糊查询"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">发票类型：</span>
            <el-select clearable v-model="searchForm.invoiceType">
              <el-option
                v-for="(item, index) in $dict('InvoiceTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code">
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">订单来源：</span>
            <el-select
              v-model="searchForm.source"
              clearable>
              <el-option
                v-for="item in $dict('InvoiceSourceEnum')"
                :key="item.code"
                :value="item.code"
                :label="item.desc"></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">金额类型：</span>
            <el-select v-model="searchForm.amountType" clearable>
              <el-option
                v-for="(item, index) in $dict('InvoiceAmountTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-half">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              :start-time.sync="searchForm.createTimeBegin"
              :end-time.sync="searchForm.createTimeEnd"
              :is-show-fast-time="false"
              type="month"
              picker="separate"
              ref="datepicker1"
            />
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-half">
            <span class="flex-item__label">完成时间：</span>
            <date-picker
              :start-time.sync="searchForm.completeTimeBegin"
              :end-time.sync="searchForm.completeTimeEnd"
              :is-show-fast-time="false"
              type="month"
              picker="separate"
              ref="datepicker2"
            />
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="search(true)"><i class="el-icon-search"/>查询</el-button>
            <el-button @click="exportInvoiceList">导出</el-button>
            <el-button type="text" @click="getExportList">查看已导出列表</el-button>
            <el-button type="text" @click="resetField">清空筛选条件</el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="content-container">
      <el-table :data="pageResult.data">
        <el-table-column label="发票流水号" width="150" prop="trxNo"></el-table-column>
        <el-table-column label="产品名称" width="150" prop="productName"></el-table-column>
        <el-table-column label="创建时间/完成时间" width="180">
          <template v-slot="{row}">
            {{ row.createTime }} <br/> {{ row.completeTime }}
          </template>
        </el-table-column>

        <el-table-column label="用工企业" width="150">
          <template v-slot="{row}">
            {{ row.employerMchNo }} <br/> {{ row.employerMchName }}
          </template>
        </el-table-column>
        <el-table-column label="开票方" width="120" prop="mainstayMchName"></el-table-column>
        <el-table-column label="发票类型" prop="invoiceType">
          <template v-slot="{row}">
            {{ $dictCode('InvoiceTypeEnum', row.invoiceType).desc }}
            <el-tag size="small" type="success" v-if="row.category === 1 && row.productNo === 'CKH'">服务费</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="岗位类别"
          width="150"
          prop="workCategoryName"></el-table-column>
        <el-table-column label="开票类目" width="120" prop="invoiceCategoryName"></el-table-column>

        <el-table-column label="开票总金额" width="150">
          <template v-slot="{row}">
            {{ row.invoiceAmount | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column label="申请方式" width="130">
          <template v-slot="{row}">
            {{ $dictCode('ApplyTypeEnum', row.applyType).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="订单来源"
          width="100">
          <template v-slot="{row}">
            <el-tag
              effect="plain"
              :type="row.source === 1 ? 'primary' : 'danger'">
              {{ $dictCode('InvoiceSourceEnum', row.source).desc }}
            </el-tag>
          </template>
        </el-table-column>


        <el-table-column label="状态" width="100">
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.invoiceStatus)">{{ $dictCode('InvoiceStatusEnum', row.invoiceStatus).desc }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column label="反馈" prop="errorDesc" show-overflow-tooltip></el-table-column>

        <el-table-column label="金额类型" width="100">
          <template v-slot="{row}">
            {{ $dictCode('InvoiceAmountTypeEnum', row.amountType).desc }}
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right">
          <template v-slot="{row}">
            <el-button type="text" @click="goDetail(row)">查看详情</el-button>
            <el-button type="text" @click="confirm(row)">确认</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :page-sizes="[10, 30, 50]"
        :total="pageResult.totalRecord"
        :page-size.sync="pageSize"
        :current-page.sync="pageCurrent"
        @size-change="search(true)"
        @current-change="search()"/>
    </el-footer>
    <ExportRecord ref="exportRecord"></ExportRecord>
    <confirm-dialog :visible.sync="visible" :rowData="rowData" :getTagType="getTagType" @refresh="search"/>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import ExportRecord from '@/components/ExportRecord'
import { getInvoiceList,exportInvoiceList } from '@/api/invoice'
import ConfirmDialog from './Component/ConfirmDialog'
import { getAllPosition } from '@/api/merchant'
import {exportInvoceInfo} from "../../../api/invoice";

export default {
  components: {
    ConfirmDialog,
    ExportRecord
  },
  data() {
    return {
      createTimeRange: [],

      pageResult: {
        data: [],
        totalRecord: 0,
      },

      searchForm: {
        invoiceStatus: '',
        amountType: null,
        trxNo: '',
        employerMchNo: '',
        employerMchNameLike: '',
        createTimeBegin: '',
        createTimeEnd: '',
        completeTimeBegin: '',
        completeTimeEnd: '',
      },
      pageCurrent: 1,
      pageSize: 10,

      visible: false,

      mainStayList: [],

      rowData: {},
      workCategoryOptions:[]
    }
  },
  mounted() {
    this.getPositionList();
    // 默认设置三个月
    this.$refs.datepicker.changeTime([
      dayjs().subtract(3, 'month').startOf('date').format('YYYY-MM-DD HH:mm:ss'),
      dayjs().endOf('date').format('YYYY-MM-DD HH:mm:ss'),
    ])

    this.search()
  },
  methods: {
    async search(initFlag) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      const { data } = await getInvoiceList({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      })
      this.pageResult.data = data.data;
      this.pageResult.totalRecord = data.totalRecord;
    },
    async getPositionList() {
      const { data } = await getAllPosition();
      this.workCategoryOptions = data.map((e) => {
        return {
          label: e.workCategoryName,
          value: e.workCategoryCode,
        };
      });
    },
    handleTimeChange(val) {
      this.searchForm.createTimeBegin = val ? val[0] : '';
      this.searchForm.createTimeEnd = val ? val[1] : '';
    },
    resetField() {
      this.$refs.datepicker1.clearTime();
      this.$refs.datepicker2.clearTime();
      this.searchForm = {};
    },
    goDetail(data) {
      this.$router.push({
        path: '/finance/issueInvoice/issueDetail',
        query: {
          trxNo: data.trxNo || ''
        }
      });
    },
    getTagType(status) {
      switch (status) {
        case 1:
          return 'primary';
        case 3:
          return 'success';
        case 4:
          return 'danger';
        default:
          return 'info'
      }
    },
    confirm(data) {
      this.rowData = data;
      this.visible = true;
    },
    async exportInvoiceList() {
      const { data } = await exportInvoceInfo(this.searchForm);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(46);
    }
  }


}
</script>

<style>

</style>
