<template>
  <el-dialog
    :visible="visible"
    title="确认"
    :close-on-click-modal="false"
    :before-close="close"
  >

    <el-form
      label-width="200px"
      :model="form"
      ref="form"
      :rules="rules"
    >
      <el-form-item label="申请记录当前状态：">
        <el-tag :type="getTagType(rowData.invoiceStatus)">
          {{ $dictCode('InvoiceStatusEnum', rowData.invoiceStatus).desc }}
        </el-tag>
      </el-form-item>

      <el-form-item
        label="确认新状态："
        prop="invoiceStatus"
      >
        <el-select v-model="form.invoiceStatus">
          <el-option
            v-for="(item, index) in $dict('InvoiceStatusEnum')"
            :key="index"
            :label="item.desc"
            :value="Number(item.code)"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 异常 -->
      <el-form-item
        label="异常原因反馈："
        prop="errorDesc"
        key="errorDesc"
        v-if="form.invoiceStatus == 4"
      >
        <el-input
          v-model="form.errorDesc"
          type="textarea"
          rows="6"
          maxlength="200"
          show-word-limit
        ></el-input>
      </el-form-item>
      <!-- 发票 -->
      <el-form-item
        label="已申请发票影像："
        v-show="(form.invoiceStatus == 2 || form.invoiceStatus == 3)&&(rowData.productNo=='' ||rowData.productNo==null || rowData.productNo=='ZXH' || rowData.productNo=='CEP' ||from=='detail'||(rowData.productNo=='CKH'&&rowData.category=='1'))"
      >
        <el-upload
          v-toggle:10="fileList"
          list-type="picture-card"
          accept=".bmp,.png,.gif,.jpg,.jpeg,.gif,.pdf"
          :action="baseUrl + '/file/upload'"
          :headers="uploadHeader"
          :file-list="fileList"
          :limit="10"
          :before-upload="beforeUpload"
          :on-success="(response, file, fileList) => handleSuccess(response, file, fileList)"
          :on-remove="handleRemove"
        >
          <i class="el-icon-plus"></i>
          <div
            slot="file"
            slot-scope="{file}"
            style="height: 100%;"
          >
            <div
              v-if="isPDF(file)"
              class="fileBg"
            >
              <span class="file-name">{{ file.name || '发票文件' }}</span>
            </div>
            <div v-else>
              <img
                :src="file.url"
                alt=""
              >
            </div>
            <span class="el-upload-list__item-actions">
              <span
                class="el-upload-list__item-delete"
                @click="handleFileRemove(file)"
              >
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
          <div
            slot="tip"
            class="el-upload__tip"
          >
            <el-popover
              trigger="click"
              width="400"
            >
              <el-button
                type="text"
                slot="reference"
              >
                查看示例
              </el-button>
              <div>
                <img
                  class="example-image"
                  src="@/assets/examplefee.png"
                >
              </div>
            </el-popover>
          </div>
        </el-upload>
      </el-form-item>

      <el-form-item
        label="快递单号："
        prop="expressNo"
        key="expressNo"
        v-if="form.invoiceStatus == 3"
      >
        <el-input v-model="form.expressNo"></el-input>
      </el-form-item>

    </el-form>

    <div slot="footer">
      <el-button
        type="primary"
        @click="confirm"
        :loading="loading"
      >确认</el-button>
      <el-button @click="close">取消</el-button>
    </div>

  </el-dialog>
</template>

<script>
  import { updateInvoiceStatus, confirmInvoiceRecordDetail } from '@/api/invoice';
  import { toPromise } from '@/utils';

  export default {
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      rowData: {
        type: Object,
        default: () => ({})
      },
      getTagType: {
        type: Function,
        default: () => { }
      },
      from:{
        type:String,
        default:'record'
      }
    },
    data() {
      return {
        fileList: [],
        loading: false,
        form: {
          invoiceStatus: '',
          trxNo: '',
          invoiceFileUrlList: [],
          errorDesc: '',
          expressNo: '',
        },

        rules: {
          invoiceStatus: [
            { required: true, message: '请选择发票状态', trigger: 'blur' }
          ],
          expressNo: [
            { required: false, message: '请输入快递单号', trigger: 'blur' }
          ],
          errorDesc: [
            { required: true, message: '请输入异常原因', trigger: 'blur' }
          ]
        }
      }
    },
    watch: {
      visible: {
        handler(val) {
          if (val) {
            for (let p in this.form) {
              this.form[p] = this.rowData[p] || '';
            }
            if (this.form.invoiceFileUrlList) {
              this.fileList = this.form.invoiceFileUrlList.map(item => {
                return {
                  url: this.fileUrl + item
                }
              })
            }
          } else {
            this.form = {
              invoiceStatus: '',
              trxNo: '',
              invoiceFileUrlList: [],
              errorDesc: '',
              expressNo: '',
            }
            this.fileList = [];
          }
        }
      }
    },
    methods: {
      isPDF(file) {
        return this.isPdf(file.name) || this.isPdf(file.url);
      },
      handleFileRemove(file) {
        this.fileList = this.fileList.filter(item => {
          return item.uid !== file.uid
        })
      },
      beforeUpload(file) {
        this.loading = true;
        return true;
      },
      async handleSuccess(response, file, fileList) {
        let formatFileList = []
        for (let i = 0; i < fileList.length; i++) {
          const item = fileList[i];
          const url = item.response ? item.response.data : item.origin
          const fileMsg = await this.formatFileMsg(url);
          formatFileList.push({
            url: fileMsg.fileUrl,
            origin: url,
            uid: item.uid,
            name: item.name,
          });
        }
        this.loading = false;
        this.fileList = formatFileList;
      },
      handleRemove(file, fileList) {
        this.fileList = fileList;
        this.loading = false;
      },
      async confirm() {
        const [err] = await toPromise(this.$refs.form.validate());
        if (err) return;
        this.form.invoiceFileUrl = '';
        this.form.invoiceFileUrlList = this.fileList.map(item => {
          if (item.response) {
            return item.response.data
          } else {
            return item.origin
          }
        });
        if (this.from=='record') {
          const { data } = await updateInvoiceStatus(this.form);
          data && this.$message.success(data);
        } else {
          const { data } = await confirmInvoiceRecordDetail({
            invoiceTrxNo: this.rowData.invoiceTrxNo,
            idCardNo: this.rowData.receiveIdCardNo,
            invoiceStatus: this.form.invoiceStatus,
            invoiceFileUrls: this.form.invoiceFileUrlList,
            errorDesc: this.form.errorDesc,
            expressNo: this.form.expressNo,
          })
          data && this.$message.success(data);
        }
        this.$emit("refresh");
        this.close();
      },
      close() {
        this.$emit('update:visible', false);
      }
    }
  }
</script>

<style lang="scss" scoped>
  .el-dialog__wrapper {
    ::v-deep .el-dialog__header {
      border-bottom: 1px solid $borderColor;
    }
    ::v-deep .el-dialog__footer {
      border-top: 1px solid $borderColor;
    }
  }
  .example-image {
    width: 400px;
    height: 200px;
  }
</style>
