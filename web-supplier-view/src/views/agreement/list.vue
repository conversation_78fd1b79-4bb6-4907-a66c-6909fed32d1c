<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">签署方：</span>
            <el-select
              clearable
              v-model="searchForm.signerNoList"
              multiple
              filterable>
              <el-option
                v-for="item in activeMchList"
                :key="item.employerNo"
                :value="item.employerNo"
                :label="item.employerName"
              ></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">协议名称：</span>
            <el-input
              v-model="searchForm.topicLike"
              placeholder="模糊查询"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">协议状态：</span>
            <el-select
              clearable
              v-model="searchForm.status">
              <el-option
                v-for="item in $dict('AgreementStatusEnum')"
                :key="item.code"
                :value="item.code"
                :label="item.desc"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">发起时间：</span>
            <date-picker
              :start-time.sync="searchForm.beginDate"
              :end-time.sync="searchForm.endDate"
              ref="datepicker1"
              type="datetime"
              picker="separate"
            ></date-picker>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">完成时间：</span>
            <date-picker
              ref="datepicker2"
              type="datetime"
              picker="separate"
              :start-time.sync="searchForm.beginFinishTime"
              :end-time.sync="searchForm.endFinishTime"
            ></date-picker>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">签署截止时间：</span>
            <date-picker
              ref="datepicker3"
              type="datetime"
              picker="separate"
              :use-option="false"
              :start-time.sync="searchForm.beginDeadLine"
              :end-time.sync="searchForm.endDeadLine"
            ></date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="search(true)">查询
            </el-button>
            <el-button
              type="text"
              @click="getExportList">查看导出列表
            </el-button>
            <el-button
              type="text"
              @click="resetForm">清空筛选条件
            </el-button>
            <el-button
              @click="exportFile"
              :disabled="selection.length === 0">批量下载
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="content-container">
      <el-table
        class="content-main"
        :data="list"
        @selection-change="val => {selection = val}">
        <el-table-column type="selection"></el-table-column>
        <el-table-column
          label="序号"
          type="index"
          :index="getIndex">
        </el-table-column>

        <el-table-column
          label="协议名称"
          width="100px"
          prop="topic">
        </el-table-column>

        <el-table-column
          label="签署方"
          width="140px">
          <template v-slot="{row}">
            <div
              class="signer-item"
              v-for="(item, index) in row.signerList"
              :key="index"
            >
              {{ $dictCode('AgreementSignerTypeEnum', item.signerType).desc }}：{{ item.signerMchName }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="签署状态">
          <template v-slot="{row}">
            <div
              class="signer-item"
              v-for="(item, index) in row.signerList"
              :key="index"
            >
              {{ $dictCode('AgreementSignerStatusEnum', item.status).desc }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="协议负责人">
          <template v-slot="{row}">
            <div
              class="signer-item"
              v-for="(item, index) in row.signerList"
              :key="index"
            >
              {{ item.signerName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="手机号"
          width="150">
          <template v-slot="{row}">
            <div
              class="signer-item"
              v-for="(item, index) in row.signerList"
              :key="index"
            >
              {{ item.signerPhone }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="签署截止时间"
          width="120"
          prop="deadline">
          <template v-slot="{row}">
            <p v-html="renderTime(row.deadline)"></p>
          </template>
        </el-table-column>
        <el-table-column
          label="协议到期时间"
          width="120"
          prop="expireTime">
          <template v-slot="{row}">
            <p v-html="renderTime(row.expireTime)"></p>
          </template>
        </el-table-column>
        <el-table-column
          label="发起时间"
          width="120"
          prop="createTime">
          <template v-slot="{row}">
            <p v-html="renderTime(row.createTime)"></p>
          </template>
        </el-table-column>
        <el-table-column
          label="完成时间"
          width="120"
          prop="finishTime">
          <template v-slot="{row}">
            <p v-html="renderTime(row.finishTime)"></p>
          </template>
        </el-table-column>

        <el-table-column
          label="流程状态"
          min-width="70px"
          prop="status">
          <template v-slot="{row}">
            {{ $dictCode('AgreementStatusEnum', row.status).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="描述"
          min-width="100px"
          prop="description">

        </el-table-column>

        <el-table-column
          label="操作"
          min-width="150px"
          fixed="right">
          <!--<template v-slot="{row}">-->
          <!--  <el-button type="text">签署</el-button>-->
          <!--  <el-button-->
          <!--    type="text"-->
          <!--    @click="download(scope.row)"-->
          <!--  >-->
          <!--    下载-->
          <!--  </el-button>-->
          <!--</template>-->
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="download(row)"
              v-if="row.status == 100">下载
            </el-button>
            <el-button
              type="text"
              @click="sign(row)"
              v-if="row.status == 103 && row.signType == 100">签署
            </el-button>
            <el-button
              type="text"
              @click="openOriginFile(row)">查看初始协议
            </el-button>
            <el-button
              type="text"
              v-if="row.status==103 && row.signType == 101 "
              @click="handleFinish(row)"
            >
              归档
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <ExportRecord ref="exportRecord"></ExportRecord>

    <div class="pagination-container">
      <el-pagination
        v-if="list"
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10,50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </div>

  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import { exportFile, getAgreementList, getSignUrl } from '@/api/agreement';
import { getMchByMainstayNo } from '@/api/merchant';

export default {
  name: 'agreementList',
  components: {
    ExportRecord
  },
  data() {
    return {
      searchForm: {
        topicLike: '',
        signerNoList: [],
        status: '',
        beginDate: '',
        endDate: '',
        beginDeadLine: '',
        endDeadLine: '',
        beginFinishTime: '',
        endFinishTime: ''
      },
      pageCurrent: 1,
      pageSize: 10,
      totalRecord: 0,

      list: [],

      activeMchList: [],
      selection: [],
    }
  },
  mounted() {
    getMchByMainstayNo().then(res => {
      this.activeMchList = res.data
    })
    this.search()
  },
  methods: {
    async search(init) {
      if (init) {
        this.pageCurrent = 1;
      }
      const { data } = await getAgreementList({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent
      });
      this.list = data.data;
      this.totalRecord = data.totalRecord;
    },
    resetForm() {
      this.searchForm = {
        topicLike: '',
        signerNoList: [],
        status: '',
        beginDate: '',
        endDate: '',
        beginDeadLine: '',
        endDeadLine: '',
        beginFinishTime: '',
        endFinishTime: ''
      };
      this.$refs.datepicker1.clearTime();
      this.$refs.datepicker2.clearTime();
      this.$refs.datepicker3.clearTime();
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('49');
    },
    handleFinish(data) {
      this.$router.push('/merchant/agreement/finishAgreement?agreementId=' + data.id);
    },
    async download(row) {
      // const {data} = await downloadArchiveFile({id: row.id})
      // this.$message.success(data);
      let file = row.fileList.find(item => item.type == 101)
      if (file) {
        const fileMsg = await this.formatFileMsg(file.fileUrl)
        const suffix = file.fileUrl.split('.').pop()
        this.downloadFile(fileMsg.fileUrl, file.fileName, suffix)
      } else {
        this.$message.error('没有对应文件')
      }
    },
    async sign({ signType, id }) {
      if (signType === 100) {
        const { data } = await getSignUrl({ id })
        if (data && typeof data === 'string') {
          window.open(data)
        }
      } else {
        this.$alert('此协议已转线下签署，如有疑问请联系销售经理', '注意', {
          type: 'warning',
          confirmButtonText: '知道了',
          showClose: false
        })
      }
    },
    async exportFile() {
      await exportFile({
        pageCurrent: this.pageCurrent,
        pageSize: this.pageSize,
        ...this.form,
        idList: this.selection.map(item => item.id)
      })
      this.$message.success('操作成功，请查看导出列表')
    },
    async openOriginFile({ fileList }) {
      let file = fileList.filter(f => f.type === 100)[0]
      if (file) {
        const fileMsg = await this.formatFileMsg(file.fileUrl)
        window.open(fileMsg.fileUrl)
      } else {
        this.$message.error('初始协议文件缺失')
      }
    }
  },
}
</script>

<style
  scoped
  lang="scss">
.signer-item {
  &:first-child {
    border-bottom: 1px solid #ddd;
  }
}
</style>
