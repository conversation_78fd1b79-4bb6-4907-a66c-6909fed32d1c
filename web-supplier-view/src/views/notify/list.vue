<template>
  <div
    class="box-container"
    v-loading="loading"
  >
    <el-tabs
      v-model="activeName"
      @tab-click="handleClick"
      type="card"
    >
      <el-tab-pane
        v-for="(item, index) in statusList"
        :key="index"
        :label="item.code == -1 ? '全部' : (item.code == 0 ? '未读' : '已读')"
        :name="item.code"
      ></el-tab-pane>
    </el-tabs>

    <el-button
      type="primary"
      :disabled="selection.length == 0"
      @click="updateNotifyStatus()"
    >标记为已读</el-button>

    <el-table
      :data="list"
      @selection-change="onSelect"
      ref="table"
    >
      <el-table-column
        type="selection"
        :selectable="selectableFn"
      ></el-table-column>
      <el-table-column label="通知类型">
        <template v-slot="{row}">
          {{ $dictCode('NotificationTypeEnum', row.notificationRecordDetail.notificationType).desc }}
        </template>
      </el-table-column>
      <el-table-column label="通知标题">
        <template v-slot="{row}">
          {{ row.notificationRecord.notificationTitle }}
        </template>
      </el-table-column>
      <el-table-column label="通知内容">
        <template v-slot="{row}">
          <div
            style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; cursor: pointer; height: 40px;"
            v-html="row.notificationRecord.notificationContent"
            @click="openMessage(row)"
          >
          </div>
        </template>
      </el-table-column>
      <el-table-column label="时间">
        <template v-slot="{row}">
          <div v-html="renderTime(row.notificationRecord.pushTime || row.notificationRecord.creationTime)"></div>
        </template>
      </el-table-column>

      <el-table-column label="操作">
        <template v-slot="{row}">
          <el-button
            type="text"
            v-if="row.notificationRecordDetail.readStatus == 0"
            @click="updateNotifyStatus(row)"
          >标记为已读</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="page.total"
        :current-page.sync="page.current"
        :page-size.sync="page.size"
        :page-sizes="[10, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>
    
    <el-dialog
      :show-close="false"
      :visible.sync="visible"
      :title="dialogContent.notificationTitle"
      top="2vh"
    >
      <div
        class="notify-content"
        v-html="dialogContent.notificationContent"
      ></div>
      <template #footer>
        <el-button
          @click="visible = false"
          type="primary"
        >确定</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script>
  import { getNotifyList, updateNotifyStatus } from '@/api/notify'
  export default {
    name: 'notifyList',
    data() {
      return {
        loading: false,
        list: [],
        selection: [],
        page: {
          size: 10,
          current: 1,
          total: 0,
        },
        activeName: '-1',
        statusList: [
          {
            code: '-1',
            desc: '全部',
            flag: '',
          },
          ...this.$dict('YesNoCodeEnum')
        ],
        visible: false, // 弹窗显示
        dialogContent: {}, // 弹窗内容

      }
    },
    mounted() {
      this.search()
    },
    methods: {
      handleClick() {
        this.search(true)
      },
      async search(init) {
        if (init) {
          this.page.current = 1
        }
        const { data: { records, total } } = await getNotifyList({
          ...this.page,
          readStatus: this.activeName == -1 ? '' : this.activeName
        })
        this.list = records
        this.page.total = total
      },
      onSelect(selection) {
        this.selection = selection
      },
      async updateNotifyStatus(row) {
        try {
          await this.$confirm('确认标记消息为已读吗？', '标记已读', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
        } catch {
          return
        }
        let ids = null
        if (row) {
          // 单个
          ids = [row.notificationRecord.id]
        } else {
          // 批量
          ids = this.selection.map(item => item.notificationRecord.id)
        }

        this.loading = true
        try {
          const { data } = await updateNotifyStatus({
            ids: ids.join(','),
            status: 1
          })
          data && this.$message.success(data)
          this.$refs.table.clearSelection()
          this.search(true)
        } finally {
          this.loading = false
        }
      },
      openMessage(row) {
        this.dialogContent = row.notificationRecord
        this.visible = !this.visible
      },
      selectableFn(row, index) {
        return row.notificationRecordDetail.readStatus == 0
      },
    },
  }
</script>

<style>
</style>