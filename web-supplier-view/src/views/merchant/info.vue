<template>
  <el-card v-loading="loading">
    <div slot="header">
      基本信息
      <div
        v-show="!canEdit"
        class="fr func-content"
        @click="toggleEdit(true)">
        编辑
      </div>
    </div>
    <el-main>
      <div class="flex-container">
        <div class="flex-wrapper">
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">商户编号</div>
            <div>{{ merchantInfo.mchNo }}</div>
          </div>
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">商户名称</div>
            <div>{{ merchantInfo.mchName }}</div>
          </div>
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">商户类型</div>
            <div>{{ $dictCode('MerchantTypeEnum', merchantInfo.merchantType).desc }}</div>
          </div>
        </div>
        <div class="flex-wrapper">
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">用户状态</div>
            <div>{{ $dictCode('MchStatusEnum', merchantInfo.mchStatus).desc }}</div>
          </div>
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">资质状态</div>
            <div>
              {{ $dictCode('AuthStatusEnum', merchantInfo.authStatus).desc }}
              <span
                class="flex-func-link"
                v-if="$dictCode('AuthStatusEnum', merchantInfo.authStatus).desc == '未认证'"
                @click="goAuth">
                去认证
              </span>
              <span
                class="flex-func-link"
                v-if="$dictCode('AuthStatusEnum', merchantInfo.authStatus).desc == '认证失败' && canAuth"
                @click="goAuth">
                查看
              </span>
            </div>
          </div>
        </div>
        <div class="flex-wrapper">
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">创建时间</div>
            <div>{{ merchantInfo.createTime }}</div>
          </div>
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">激活时间</div>
            <div>{{ merchantInfo.activeTime }}</div>
          </div>
        </div>
        <div class="flex-wrapper">
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">负责人</div>
            <div>
              {{ merchantInfo.contactName }}({{ merchantInfo.contactPhone }})
              <router-link
                to="/safe/leaderChange"
                replace
                class="flex-func-link">更换
              </router-link>
            </div>
          </div>
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">常用邮箱</div>
            <div>{{ merchantInfo.contactEmail }}</div>
          </div>
        </div>
        <div class="flex-wrapper">
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">工作台企业LOGO</div>
            <file-upload
              :disabled="!canEdit"
              :url.sync="merchantInfo.platformLogoUrl"
              :max="1"
              :show-tip="false"></file-upload>
          </div>
          <div class="flex-vertical-item">
            <div class="flex-vertical-label">登录页LOGO</div>
            <file-upload
              :disabled="!canEdit"
              :url.sync="merchantInfo.loginLogoUrl"
              :max="1"
              :show-tip="false"></file-upload>
          </div>
        </div>
      </div>
      <div
        class="text-center"
        style="margin-top: 16px"
        v-if="canEdit">
        <el-button
          type="primary"
          @click="confirm">确认
        </el-button>
        <el-button @click="toggleEdit(false)">取消</el-button>
      </div>
    </el-main>
  </el-card>
</template>

<script>
import { changeLogo, checkAuthBtn, getMerchant } from '@/api/merchant.js'
import FileUpload from "@/components/FileUpload/index.vue";

export default {
  name: 'merchantInfo',
  components: { FileUpload },
  data() {
    return {
      loading: false,
      merchantInfo: {},

      canAuth: false,
      canEdit: false,
    }
  },
  async mounted() {
    await this.getMerchant()
    if (this.merchantInfo.authStatus == 101) {
      this.getApprovalFlow()
    }
  },
  methods: {
    async getMerchant() {
      this.loading = true;
      const { data } = await getMerchant()
      this.merchantInfo = data;
      this.loading = false;
    },
    goAuth() {
      sessionStorage.setItem('mainInfo', JSON.stringify(this.merchantInfo));
      if (this.$dictCode('AuthStatusEnum', this.merchantInfo.authStatus).desc == '未认证') {
        this.$router.push(`/merchant/auth?mchNo=${ this.merchantInfo.mchNo }`);
      }
      if (this.$dictCode('AuthStatusEnum', this.merchantInfo.authStatus).desc == '认证失败') {
        this.$router.push({
          path: '/merchant/auth',
          query: {
            mchNo: this.merchantInfo.mchNo,
            result: 'fail'
          }
        })
      }
    },
    async getApprovalFlow() {
      const { code } = await checkAuthBtn();
      if (code == 100004) {
        this.canAuth = false;
      } else {
        this.canAuth = true;
      }
    },
    toggleEdit(status = false) {
      this.canEdit = status
    },
    async confirm() {
      const { data } = await changeLogo({
        platformLogoUrl: this.merchantInfo.platformLogoUrl,
        loginLogoUrl: this.merchantInfo.loginLogoUrl
      })
      data && this.$message.success(data)
      this.toggleEdit()
      this.getMerchant()
    }
  }
}
</script>

<style>

</style>
