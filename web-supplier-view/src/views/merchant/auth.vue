<template>
  <div class="page-container">
    <div class="header-container">
      <el-steps
        :active="activeStep"
        align-center
      >
        <el-step title="填写主体信息"></el-step>
        <el-step title="填写法定代表人信息"></el-step>
        <el-step title="填写经营信息"></el-step>
        <el-step title="审核与公示"></el-step>
      </el-steps>
    </div>
    <div class="content-container">
      <div>
        <!--step_1-->
        <el-form
          ref="form_1"
          label-width="300px"
          :model="form_1"
          :rules="rules"
          class="step_1"
          v-show="activeStep == 1"
        >
          <div class="hgroup">
            <h2 style="font-size: 40px">填写主体信息</h2>
            <h4>已为您自动识别部分信息，请仔细核对并完善</h4>
          </div>
          <!--企业信息-->
          <div class="company_info">
            <p class="subTitle">企业信息</p>
            <div>
              <el-form-item
                label="企业名称："
                prop="mchName"
              >
                <el-input
                  v-model="form_1.mchName"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item
                label="统一社会信用代码："
                prop="taxNo"
              >
                <el-input v-model="form_1.taxNo"></el-input>
                <div>请输入营业执照上18位统一社会信用代码</div>
              </el-form-item>
              <el-form-item
                label="注册地址："
                prop="registerAddrDetail"
              >
                <el-select
                  v-model="form_1.registerAddrProvince"
                  @change="getCityList('register', ['registerAddrCity', 'registerAddrTown'], $event)"
                >
                  <el-option
                    v-for="item in provinceList"
                    :key="item.id"
                    :value="[item.provinceNo, item.provinceName]"
                    :label="item.provinceName"
                  ></el-option>
                </el-select>
                <el-select
                  v-model="form_1.registerAddrCity"
                  @change="getTownList('register', ['registerAddrTown'], $event)"
                  :disabled="!form_1.registerAddrProvince"
                >
                  <el-option
                    v-for="item in registerCityList"
                    :key="item.id"
                    :value="[item.cityNo, item.cityName]"
                    :label="item.cityName"
                  ></el-option>
                </el-select>
                <el-select
                  v-model="form_1.registerAddrTown"
                  :disabled="!form_1.registerAddrCity"
                >
                  <el-option
                    v-for="item in registerTownList"
                    :key="item.id"
                    :value="item.townName"
                    :label="item.townName"
                  ></el-option>
                </el-select>
                <el-input v-model="form_1.registerAddrDetail"></el-input>
              </el-form-item>
              <el-form-item
                label="注册资本（万）："
                prop="registerAmount"
              >
                <el-input v-model="form_1.registerAmount"></el-input>
              </el-form-item>
              <el-form-item
                label="经营范围："
                prop="managementScope"
              >
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="form_1.managementScope">
                </el-input>
                <p class="form-tip color-gray">
                  与企业工商营业执照上一致
                </p>
              </el-form-item>
              <el-form-item
                label="营业期限："
                prop="managementValidityDateType"
              >
                <el-radio-group v-model="form_1.managementValidityDateType">
                  <el-radio
                    v-for="item in $dict('ValidityDateTypeEnum')"
                    :key="item.code"
                    :label="Number(item.code)"
                  >{{ item.desc }}</el-radio>
                </el-radio-group>
                <date-picker
                  v-if="$dictCode('ValidityDateTypeEnum',form_1.managementValidityDateType).desc == '区间有效'"
                  :timeRange="manageTimeRange"
                  :is-show-fast-time="false"
                  :use-option="false"
                  type="daterange"
                  @change="val => getTimeRange(val, 'managementTerm')"
                ></date-picker>

                <div
                  v-if="$dictCode('ValidityDateTypeEnum',form_1.managementValidityDateType).desc == '长期有效'"
                >
                  <el-date-picker
                    type="date"
                    v-model="form_1.managementTermBegin"
                    placeholder="选择起始时间"
                    :value-format="'yyyy-MM-dd'"
                  ></el-date-picker>
                </div>
              </el-form-item>
            </div>
          </div>
          <!--企业证件-->
          <div class="job_info">
            <p class="subTitle">企业证件</p>
            <div>
              <el-form-item
                label="营业执照"
                prop="businessLicenseFileUrl"
              >
                <el-upload
                  v-toggle="form_1.businessLicenseFileUrl"
                  class="upload-demo"
                  data-type="businessLicenseFileUrl"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  list-type="picture-card"
                  accept=".bmp,.png,.gif,.jpg,.jpeg,.gif,.pdf"
                  :limit="1"
                  :file-list="businessLicenseFileUrlShow"
                  :before-upload="validateUploadFile({img: true, pdf: true, size: 6})"
                  :on-remove="(file, fileList) => handleRemove(file, fileList, 'businessLicenseFileUrl')"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'businessLicenseFileUrl')"
                >
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <template v-slot:file="{file}">
                    <div class="fileBg" v-if="isPdf(file.name)">
                      <span class="file-name">{{ file.name }}</span>
                      <span class="el-upload-list__item-actions">
                          <span
                            class="el-upload-list__item-delete"
                            @click="handleFileRemove(file, 'businessLicenseFileUrl')"
                          >
                            <i class="el-icon-delete"></i>
                          </span>
                        </span>
                    </div>
                  </template>
                  <div
                    slot="tip"
                    class="el-upload__tip"
                  >请上传彩色原件或加盖公司公章的复印件，小于6M，文件格式为pdf、bmp、png、jpeg、jpg或gif。
                    <el-popover
                      trigger="click"
                      width="300">
                      <el-button type="text" slot="reference">
                        查看示例
                      </el-button>
                      <div>
                        <img
                          class="example-image"
                          src="@/assets/example-1.png"
                        >
                      </div>
                    </el-popover>
                  </div>
                </el-upload>
              </el-form-item>
            </div>
          </div>
          <!--银行卡信息-->
          <div class="bankCard_info">
            <p class="subTitle">银行卡信息</p>
            <div>
              <el-form-item
                label="银行卡号："
                prop="accountNo"
              >
                <el-input v-model="form_1.accountNo"></el-input>
              </el-form-item>
              <el-form-item
                label="联行号："
                prop="bankChannelNo"
              >
                <el-input
                  v-model="form_1.bankChannelNo"
                  prefix-icon="el-icon-search"
                  @click.native="searchBankNumberInfo"
                >
                </el-input>
              </el-form-item>
              <el-form-item
                label="开户银行："
                prop="bankName"
              >
                <el-input
                  v-model="form_1.bankName"
                  disabled
                ></el-input>
              </el-form-item>
            </div>
          </div>
          <div class="footer-container">
            <el-button
              type="primary"
              @click="nextStep"
            >保存并下一步</el-button>
            <el-button @click="$router.push('/merchant/info')">取消</el-button>
          </div>
        </el-form>

        <!--step_2-->
        <el-form
          ref="form_2"
          label-width="300px"
          :model="form_2"
          :rules="rules"
          class="step_2"
          v-show="activeStep == 2"
        >
          <div class="hgroup">
            <h2 style="font-size: 40px">填写法定代表人信息</h2>
            <h4>依据相关要求，切实履行账户实名管理，商户身份核实、交易风险管理等职责，需核实相关信息，感谢你的配合</h4>
          </div>
          <!--法定代表人证件-->
          <div class="company_info">
            <p class="subTitle">法定代表人证件</p>
            <div>
              <el-form-item
                label="证件类型："
                prop="certificateType"
              >
                <el-select
                  clearable
                  v-model="form_2.certificateType"
                >
                  <el-option
                    v-for="item in $dict('CertificateTypeEnum')"
                    :key="item.code"
                    :value="Number(item.code)"
                    :label="item.desc"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="证件照："
                prop="idFileType"
              >
                <el-radio-group v-model="form_2.idFileType">
                  <el-radio label="100">彩色原件</el-radio>
                  <el-radio label="101">复印件</el-radio>
                </el-radio-group>
                <div v-show="form_2.idFileType == '100'">
                  <p>
                    人像面
                    <el-popover
                      width="300"
                      trigger="click">
                      <img
                        class="example-image"
                        src="@/assets/example-2.png"
                      >
                      <el-button slot="reference" type="text">查看示例</el-button>
                    </el-popover>
                  </p>
                  <el-upload
                    v-toggle="form_2.idCardHeadFileUrl"
                    key="idCardHeadFileUrl"
                    class="upload-demo"
                    data-type="idCardHeadFileUrl"
                    :action="baseUrl + '/file/upload'"
                    :headers="uploadHeader"
                    list-type="picture-card"
                    accept=".png,.gif,.jpg,bmp"
                    :limit="1"
                    :file-list="idCardHeadFileUrlShow"
                    :before-upload="beforeAvatarUpload"
                    :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardHeadFileUrl')"
                    :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardHeadFileUrl')"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                  <p>
                    国徽面
                    <el-popover
                      width="300"
                      trigger="click">
                      <img
                        class="example-image"
                        src="@/assets/example-3.png"
                      >
                      <el-button slot="reference" type="text">查看示例</el-button>
                    </el-popover>
                  </p>
                  <el-upload
                    v-toggle="form_2.idCardEmblemFileUrl"
                    key="idCardEmblemFileUrl"
                    class="upload-demo"
                    data-type="idCardEmblemFileUrl"
                    :action="baseUrl + '/file/upload'"
                    :headers="uploadHeader"
                    list-type="picture-card"
                    accept=".png,.gif,.jpg,bmp"
                    :limit="1"
                    :file-list="idCardEmblemFileUrlShow"
                    :before-upload="beforeAvatarUpload"
                    :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardEmblemFileUrl')"
                    :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardEmblemFileUrl')"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </div>
                <div v-show="form_2.idFileType == '101'">
                  <p>加盖公司公章的法人身份证复印件
                    <el-popover
                      width="300"
                      trigger="click">
                      <img
                        class="example-image"
                        src="@/assets/idCardCopyFileExample.png"
                      >
                      <el-button slot="reference" type="text">查看示例</el-button>
                    </el-popover>
                  </p>
                  <el-upload
                    v-toggle="form_2.idCardCopyFileUrl"
                    key="idCardCopyFileUrl"
                    class="upload-demo"
                    data-type="idCardCopyFileUrl"
                    :action="baseUrl + '/file/upload'"
                    :headers="uploadHeader"
                    list-type="picture-card"
                    accept=".png,.gif,.jpg,bmp"
                    :limit="1"
                    :file-list="idCardCopyFileUrlShow"
                    :before-upload="beforeAvatarUpload"
                    :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardCopyFileUrl')"
                    :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardCopyFileUrl')"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                  </el-upload>
                </div>
              </el-form-item>
            </div>
          </div>
          <!--法定代表人信息-->
          <div class="bankCard_info">
            <p class="subTitle">法定代表人信息</p>
            <div>
              <el-form-item
                label="法定代表人姓名："
                prop="legalPersonName"
              >
                <el-input v-model="form_2.legalPersonName"></el-input>
              </el-form-item>
              <el-form-item
                label="证件号码："
                prop="certificateNumber"
                :rules="certificateRules"
              >
                <el-input v-model="form_2.certificateNumber"></el-input>
              </el-form-item>
              <el-form-item
                label="证件有效期："
                prop="certificateValidityDateType"
              >
                <el-radio-group v-model="form_2.certificateValidityDateType">
                  <el-radio
                    v-for="item in $dict('ValidityDateTypeEnum')"
                    :key="item.code"
                    :label="Number(item.code)"
                  >{{ item.desc }}</el-radio>
                </el-radio-group>
                <date-picker
                  v-if="$dictCode('ValidityDateTypeEnum',form_2.certificateValidityDateType).desc == '区间有效'"
                  type="daterange"
                  @change="val => getTimeRange(val, 'certificateTerm')"
                  :is-show-fast-time="false"
                  :timeRange="certificateTimeRange"
                  :use-option="false"
                ></date-picker>

                <div v-else-if="$dictCode('ValidityDateTypeEnum',form_2.certificateValidityDateType).desc == '长期有效'">
                  <el-date-picker
                    v-model="form_2.certificateTermBegin"
                    type="date"
                    placeholder="请选择起始时间"
                    :value-format="'yyyy-MM-dd'"
                  ></el-date-picker>
                </div>
              </el-form-item>
            </div>
          </div>
          <el-footer class="footer-container">
            <el-button
              type="primary"
              @click="nextStep"
            >保存并下一步</el-button>
            <el-button @click="activeStep--">上一步</el-button>
          </el-footer>
        </el-form>

        <!--step_3-->
        <el-form
          ref="form_3"
          label-width="300px"
          :model="form_3"
          :rules="rules"
          class="step_3"
          v-show="activeStep == 3"
        >
          <div class="hgroup">
            <h2 style="font-size: 40px">填写经营信息</h2>
            <h4>已为您自动识别部分信息，请仔细核对并完善</h4>
          </div>
          <!--联系人信息-->
          <div class="company_info">
            <p class="subTitle">联系人信息</p>
            <div>
              <el-form-item
                label="负责人姓名："
                prop="contactName"
              >
                <el-input
                  v-model="form_3.contactName"
                  :disabled="!editAccess"
                ></el-input>
                请填写公司运营负责人
              </el-form-item>
              <el-form-item
                label="负责人手机号："
                prop="contactPhone"
              >
                <el-input
                  v-model="form_3.contactPhone"
                  :disabled="!editAccess"
                ></el-input>
                <el-button
                  type="text"
                  @click="() => { editAccess = true }"
                >更改</el-button>
              </el-form-item>
              <el-form-item
                label="常用邮箱："
                prop="contactEmail"
              >
                <el-input v-model="form_3.contactEmail"></el-input>
                用于接收认证及风控相关重要通知
              </el-form-item>
              <el-form-item
                label="客服电话："
                prop="servicePhone"
              >
                <el-input v-model="form_3.servicePhone"></el-input>
                请注意填写格式，举例（座机：0660-********；手机：***********；400电话：**********
              </el-form-item>
            </div>
          </div>
          <!--经营信息-->
          <div class="bankCard_info">
            <p class="subTitle">经营信息</p>
            <div>
              <el-form-item
                label=" 企业简称："
                prop="shortName"
              >
                <el-input v-model="form_3.shortName"></el-input>
              </el-form-item>
              <el-form-item
                label="实际经营地址："
                prop="managementAddrDetail"
              >
                <el-select
                  v-model="form_3.managementAddrProvince"
                  @change="getCityList('management', ['managementAddrCity', 'managementAddrTown'], $event)"
                >
                  <el-option
                    v-for="item in provinceList"
                    :key="item.id"
                    :value="[item.provinceNo, item.provinceName]"
                    :label="item.provinceName"
                  ></el-option>
                </el-select>
                <el-select
                  v-model="form_3.managementAddrCity"
                  @change="getTownList('management', ['managementAddrTown'], $event)"
                  :disabled="!form_3.managementAddrProvince"
                >
                  <el-option
                    v-for="item in managementCityList"
                    :key="item.id"
                    :value="[item.cityNo, item.cityName]"
                    :label="item.cityName"
                  ></el-option>
                </el-select>
                <el-select
                  v-model="form_3.managementAddrTown"
                  :disabled="!form_3.managementAddrCity"
                >
                  <el-option
                    v-for="item in managementTownList"
                    :key="item.id"
                    :value="item.townName"
                    :label="item.townName"
                  ></el-option>
                </el-select>
                <el-input v-model="form_3.managementAddrDetail"></el-input>
              </el-form-item>
              <el-form-item
                label="门头照片"
                prop="doorPhotoFileUrl"
              >
                <el-upload
                  v-toggle="form_3.doorPhotoFileUrl"
                  class="upload-demo"
                  data-type="doorPhotoFileUrl"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  list-type="picture-card"
                  accept=".png,.gif,.jpg,bmp"
                  :limit="1"
                  :file-list="doorPhotoFileUrlShow"
                  :before-upload="beforeAvatarUpload"
                  :on-remove="(file, fileList) => handleRemove(file, fileList, 'doorPhotoFileUrl')"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'doorPhotoFileUrl')"
                >
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <div
                    slot="tip"
                    class="el-upload__tip"
                  >最多1张，单张照片不超过6M。</div>
                </el-upload>
              </el-form-item>
              <el-form-item
                label="办公内景照片"
                prop="workIndoorFileUrl"
              >
                <el-upload
                  v-toggle="form_3.workIndoorFileUrl"
                  data-type="workIndoorFileUrl"
                  class="upload-demo"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  list-type="picture-card"
                  accept=".png,.gif,.jpg,bmp"
                  :limit="1"
                  :file-list="workIndoorFileUrlShow"
                  :before-upload="beforeAvatarUpload"
                  :on-remove="(file, fileList) => handleRemove(file, fileList, 'workIndoorFileUrl')"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'workIndoorFileUrl')"
                >
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <div
                    slot="tip"
                    class="el-upload__tip"
                  >最多1张，单张照片不超过6M。</div>
                </el-upload>
              </el-form-item>
              <el-form-item
                label="前台照片"
                prop="receptionFileUrl"
              >
                <el-upload
                  v-toggle="form_3.receptionFileUrl"
                  data-type="receptionFileUrl"
                  class="upload-demo"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  list-type="picture-card"
                  accept=".png,.gif,.jpg,bmp"
                  :limit="1"
                  :file-list="receptionFileUrlShow"
                  :before-upload="beforeAvatarUpload"
                  :on-remove="(file, fileList) => handleRemove(file, fileList, 'receptionFileUrl')"
                  :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'receptionFileUrl')"
                >
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                  <div
                    slot="tip"
                    class="el-upload__tip"
                  >最多1张，单张照片不超过6M。</div>
                </el-upload>
              </el-form-item>
            </div>
          </div>

          <div class="invoice_info">
            <p class="subTitle">开票信息</p>
            <el-form-item label="纳税人类型：" prop="taxPayerType">
              <el-radio-group v-model="form_3.taxPayerType">
                <el-radio v-for="(item, index) in $dict('TaxPayerTypeEnum')" :key="index" :label="Number(item.code)">
                  {{ item.desc }}
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="开票地址：" prop="invoiceAddress">
              <el-input v-model="form_3.invoiceAddress"></el-input>
            </el-form-item>
            <el-form-item label="联系电话：" prop="invoicePhone">
              <el-input v-model="form_3.invoicePhone"></el-input>
            </el-form-item>
            <el-form-item label="开票银行：" prop="invoiceBankName">
              <el-input v-model="form_3.invoiceBankName"></el-input>
            </el-form-item>
            <el-form-item label="开票账户：" prop="invoiceAccountNo">
              <el-input v-model="form_3.invoiceAccountNo"></el-input>
            </el-form-item>
          </div>

          <el-footer class="footer-container">
            <div class="protocal-container">
              <el-checkbox v-model="protocalCheck">
                <span class="el-checkbox-label">
                  我已仔细阅读并同意
                </span>
              </el-checkbox>
              <span
                class="protocal-link"
                @click="getProtocal(1)"
              >《汇聚支付分账服务协议》</span>
              <span
                class="protocal-link"
                @click="getProtocal(2)"
              >《平台商户入驻框架协议》</span>
            </div>
            <el-button
              type="primary"
              @click="nextStep"
              :disabled="!protocalCheck"
            >提交</el-button>
            <el-button @click="activeStep--">上一步</el-button>
          </el-footer>
        </el-form>

        <!--step_4-->
        <div
          class="step_4"
          v-show="activeStep == 4"
        >
          <div v-if="resultStatus == 'pend'">
            <svg-icon icon-class="waiting"></svg-icon>
            <p class="result-title">认证审核中</p>
            <p>资料审核中，审核结果将在2个工作日内通知您</p>
            <div class="result-detail">
              <p class="result-detail-title">主体信息认证</p>
              <p>工作流水号：{{ result.commonFlowId }}</p>
              <p>发起人：{{ result.submitName }}</p>
              <p>创建时间：{{ result.createTime }}</p>
            </div>
          </div>
          <div v-else-if="resultStatus == 'fail'">
            <svg-icon icon-class="fail"></svg-icon>
            <p class="result-title">认证失败</p>
            <div class="result-detail">
              <p class="result-detail-title">主体信息认证</p>
              <p>工作流水号：{{ errMsg.commonFlowId }}</p>
              <p>发起人：{{ errMsg.initiatorName }}</p>
              <p>创建时间：{{ errMsg.createTime }}</p>
            </div>
          </div>
          <div class="footer-container">
            <el-button
              v-if="resultStatus == 'pend'"
              type="primary"
              @click="$router.push('/merchant/info')"
            >返回</el-button>
            <el-button v-else-if="resultStatus == 'fail'" type="primary" @click="goEdit">去修改</el-button>
          </div>
        </div>

      </div>
    </div>

    <!--  表格弹窗  -->
    <bankNumberInfo
      ref="bankNumberInfo"
      @closeDialog="closeBankNumberInfoDialog"
    >
    </bankNumberInfo>

    <!--审批意见-->
    <flow-opinion
      :visible.sync="flowVisible"
      @change="flowConfirm"
    ></flow-opinion>

  </div>
</template>

<script>
  import { merchantAuth, updateBankCardInfo, updateMerchantMain, merchantAuthAgain, getErrorMsg } from '@/api/merchant'
  import { changeLeader } from '@/api/safeSetting'
  import { validateParams } from '@/utils/validate'
  import { getCityList, getProvinceList, getTownList } from '@/api/common'
  import { editBusinessData } from '@/api/flow';
  import bankNumberInfo from '@/components/BankNumberInfo';
  import FlowOpinion from '@/components/Flow/FlowOpinion'
  import { clearVoid } from "@/utils";

  export default {
    name: 'createMerchant',
    components: {
      bankNumberInfo,
      FlowOpinion,
    },
    data() {
      const _self = this;
      return {
        isEdit: false,
        isShowTip: false,
        activeStep: 1,
        industryList: [], // 行业类别
        industryListOptions: [],
        workCategoryList: [], // 工作类目
        workCategoryOptions: [], // 工作类目树
        mainstayList: [], // 代征主体
        provinceList: [],
        managementCityList: [],
        managementTownList: [],
        registerCityList: [],
        registerTownList: [],

        resultStatus: 'pend',

        businessLicenseFileUrlShow: [], // el-upload插件绑定数组xxxShow，作用于回显图片
        receptionFileUrlShow: [],
        workIndoorFileUrlShow: [],
        doorPhotoFileUrlShow: [],
        idCardCopyFileUrlShow: [],
        idCardEmblemFileUrlShow: [],
        idCardHeadFileUrlShow: [],
        form_1: {
          mchNo: this.$route.query.mchNo,
          mchName: '',
          taxNo: '',
          registerAddrProvince: '',
          registerAddrCity: '',
          registerAddrTown: '',
          registerAddrDetail: '',
          registerAmount: '',
          managementScope: '',
          managementValidityDateType: '',
          managementTermBegin: '',
          managementTermEnd: '',
          businessLicenseFileUrl: '',
          accountNo: '',
          bankChannelNo: '',
          bankName: '',
        },
        form_2: {
          certificateType: "",
          idFileType: '',
          idCardHeadFileUrl: '', // 人像面
          idCardEmblemFileUrl: '', // 国徽面
          idCardCopyFileUrl: '',
          legalPersonName: '',
          certificateNumber: '',
          certificateValidityDateType: '',
          certificateTermBegin: '',
          certificateTermEnd: '',
        },
        form_3: {
          contactName: '',
          contactPhone: '',
          contactEmail: '',
          servicePhone: '',

          shortName: '',
          managementAddrProvince: '',
          managementAddrCity: '',
          managementAddrTown: '',
          managementAddrDetail: '',
          doorPhotoFileUrl: '', // 门头照
          workIndoorFileUrl: '',
          receptionFileUrl: '',

          // 开票信息
          taxPayerType: '',
          invoiceAddress: '',
          invoiceBankName: '',
          invoiceAccountNo: '',
          invoicePhone: '',
        },
        form: {},
        rules: {
          // step 1
          mchName: [
            { required: true, message: "请输入企业名称", trigger: "blur" },
            // { validator: validateParams({type: 'Chinese', msg: '请输入正确的企业名称'}), trigger: 'blur' },
            { validator: validateParams({ type: 'Length', max: '30', msg: '企业名称最多可输入15个字' }), trigger: 'blur' },
          ],
          taxNo: [
            { required: true, message: '请输入统一社会信用代码' },
            { validator: validateParams({ type: 'Reg', pattern: /^[a-zA-Z0-9]{1,20}$/, msg: '统一社会信用代码最多可输入20个字' }), trigger: 'blur' }
          ],
          registerAddrDetail: [
            { required: true, message: '请选择注册地址', trigger: 'blur' },
          ],
          registerAmount: [
            { required: true, message: "请输入注册资本", trigger: "blur" },
            { validator: validateParams({ type: 'Number', msg: '请输入正确的金额' }), trigger: 'blur' },
            { validator: validateParams({ type: 'Length', max: '20', msg: '注册资本最多可输入20个字' }), trigger: 'blur' },
          ],
          managementScope: [
            { required: true, message: "请输入经营范围", trigger: "blur" },
            { validator: validateParams({ type: 'Length', max: '500', msg: '经营范围最多可输入500个字' }), trigger: 'blur' },
          ],
          managementValidityDateType: [
            { required: true, message: "请选择营业期限", trigger: "change" },
            { validator: (rule, value, cb) => {
                let result = true;
                if (value == 1) {
                  if (!_self.form_1.managementTermBegin || !_self.form_1.managementTermEnd) {
                    result = false
                  }
                } else if (value == 2) {
                  if (!_self.form_1.managementTermBegin) {
                    result = false;
                  }
                }
                if (result) {
                  cb()
                } else {
                  cb(new Error('请选择营业期限'))
                }
              }, trigger: 'blur'}
          ],

          businessLicenseFileUrl: [
            { required: true, message: "请上传营业执照", trigger: "change" },
            { validator: validateParams({ type: 'Length', min: 1 }) }
          ],

          accountNo: [
            { required: true, message: "请输入银行卡号", trigger: "blur" },
          ],
          bankChannelNo: [
            { required: true, message: "请输入联行号", trigger: "change" },
          ],
          bankName: [
            { required: true, message: "请输入开户银行", trigger: "blur" },
          ],

          // step 2
          certificateType: [
            { required: true, message: "请选择证件类型", trigger: "change" },
          ],
          idFileType: [
            { required: true, message: "请上传证件照", trigger: "change" },
            { validator: function(rule, value, callback) {
              if (value == '100') {
                if (_self.form_2.idCardHeadFileUrl.length == 0 && _self.form_2.idCardEmblemFileUrl.length == 0) {
                  callback(new Error('请上传证件照'))
                } else {
                  callback()
                }
              } else if (value == '101') {
                if (_self.form_2.idCardCopyFileUrl.length == 0) {
                  callback(new Error('请上传证件照'))
                } else {
                  callback()
                }
              }
            }, trigger: 'blur'}
          ],

          legalPersonName: [
            { required: true, message: "请输入法定代表人姓名", trigger: "blur" },
            { validator: validateParams({ type: 'Chinese', msg: '请输入正确的姓名' }), trigger: 'blur' },
            { validator: validateParams({ type: 'Length', max: '30', msg: '姓名最多可输入15个字' }), trigger: 'blur' },
          ],
          // certificateNumber: [
          //   { required: true, message: "请输入证件号码", trigger: "blur" },
          //   { validator: validateParams({type: 'IdCard', msg: '请输入正确的证件号码'}), trigger: 'blur' },
          // ],
          certificateValidityDateType: [
            { required: true, message: "请选择证件有效期", trigger: "change" },
            { validator: (rule, value, cb) => {
                let result = true;
                if (value == 1) {
                  if (!_self.form_2.certificateTermBegin || !_self.form_2.certificateTermEnd) {
                    result = false
                  }
                } else if (value == 2) {
                  if (!_self.form_2.certificateTermBegin) {
                    result = false;
                  }
                }
                if (result) {
                  cb()
                } else {
                  cb(new Error('请选择证件有效期'))
                }
              }, trigger: 'blur'}
          ],

          // step 3
          contactName: [
            { required: true, message: "请输入负责人姓名", trigger: "blur" },
            { validator: validateParams({ type: 'Chinese', msg: '请输入正确的姓名' }), trigger: 'blur' },
            { validator: validateParams({ type: 'Length', max: '15', msg: '姓名最多可输入15个字' }), trigger: 'blur' },
          ],
          contactPhone: [
            { required: true, message: "请输入负责人手机号码", trigger: "blur" },
            { validator: validateParams({ type: 'Phone', msg: '请输入正确的手机号码' }), trigger: 'blur' },
          ],
          contactEmail: [
            { required: true, message: "请输入负责人常用邮箱", trigger: "blur" },
            { validator: validateParams({ type: 'Email', msg: '请输入正确的邮箱' }), trigger: 'blur' },
          ],
          servicePhone: [
            { required: true, message: "请输入客服电话", trigger: "blur" },
            { validator: validateParams({ type: 'Reg', pattern: /[0-9-]+/, msg: '请输入正确的客服电话' }), trigger: 'blur' },
          ],

          shortName: [
            { required: true, message: "请输入企业简称", trigger: "blur" },
            { validator: validateParams({ type: 'Chinese', msg: '请输入正确的企业简称' }), trigger: 'blur' },
            { validator: validateParams({ type: 'Length', max: '15', msg: '企业简称最多可输入15个字' }), trigger: 'blur' },
          ],
          managementAddrDetail: [
            { required: true, message: "请选择实际经营地址", trigger: 'blur' },
          ],
          doorPhotoFileUrl: [
            { required: true, message: '请上传公司照片' },
            { validator: validateParams({ type: 'Length', min: 1 }) }
          ],
          workIndoorFileUrl: [
            { required: true, message: '请上传公司照片' },
            { validator: validateParams({ type: 'Length', min: 1 }) }
          ],
          receptionFileUrl: [
            { required: true, message: '请上传公司照片' },
            { validator: validateParams({ type: 'Length', min: 1 }) }
          ],
          taxPayerType: [
            { required: true, message: '请选择纳税人类型', trigger: 'change' },
          ],
          invoiceAddress: [
            { required: true, message: '请输入开票地址', trigger: 'blur' },
          ],
          invoiceBankName: [
            { required: true, message: '请输入开票银行', trigger: 'blur' },
          ],
          invoiceAccountNo: [
            { required: true, message: '请输入开票银行账户', trigger: 'blur' },
          ],
          invoicePhone: [
            { required: true, message: '请输入开票联系电话', trigger: 'blur' },
          ],
        },
        // step 3
        result: '',

        editAccess: false, // 是否可以编辑负责人

        protocalCheck: false, // 是否勾选确认协议

        errMsg: {
          id: '',
          createTime: '',
          approvalOpinion: '',
          initiatorName: '',
        },
        flowVisible: false, // 审批意见显示控制
        flowRemark: '',
      }
    },
    computed: {
      manageTimeRange() {
        if (this.form_1.managementTermBegin && this.form_1.managementTermEnd) {
          return [this.form_1.managementTermBegin, this.form_1.managementTermEnd];
        } else {
          return [];
        }
      },
      certificateTimeRange() {
        if (this.form_2.certificateTermBegin && this.form_2.certificateTermEnd) {
          return [this.form_2.certificateTermBegin, this.form_2.certificateTermEnd];
        } else {
          return [];
        }
      },
      certificateRules() {
        if (this.$dictCode('CertificateTypeEnum', this.form_2.certificateType).desc == '身份证') {
          return [
            { required: true, message: "请输入证件号码", trigger: "blur" },
            { validator: validateParams({ type: 'IdCard', msg: '请输入正确的证件号码' }), trigger: 'blur' },
          ]
        } else {
          return [
            { required: true, message: "请输入证件号码", trigger: "blur" },
            { validator: validateParams({ type: 'Length', msg: '请输入正确的证件号码', min: 1 }), trigger: 'blur' },
          ]
        }
      },
      processId() {
        return this.$route.query.processId || '';
      },
      taskId() {
        return this.$route.query.taskId || '';
      }
    },
    watch: {
      activeStep(val) {
        if (val == 3) {
          if (!this.form_3.taxNo) {
            this.form_3.taxNo = this.form_1.taxNo;
          }
          if (!this.form_3.invoiceAddress) {
            this.form_3.invoiceAddress = this.form_1.registerAddrProvince + this.form_1.registerAddrCity + this.form_1.registerAddrTown + this.form_1.registerAddrDetail
          }
          if (!this.form_3.invoiceAccountNo) {
            this.form_3.invoiceAccountNo = this.form_1.accountNo
          }
          if (!this.form_3.invoiceBankName) {
            this.form_3.invoiceBankName = this.form_1.bankName
          }
        }
      }
    },
    mounted() {
      this.getProvinceList();
      if (sessionStorage.getItem('mainInfo')) {
        this.form = JSON.parse(sessionStorage.getItem('mainInfo'));
        // 处理图片
        this.businessLicenseFileUrlShow = this.form.businessLicenseFileUrl
          ? [{ url: this.fileUrl + this.form.businessLicenseFileUrl, name: this.isPdf(this.form.businessLicenseFileUrl) ? '营业执照.pdf' : '' }]
          : [];
        this.idCardHeadFileUrlShow = this.form.idCardHeadFileUrl ? [{ url: this.fileUrl + this.form.idCardHeadFileUrl }] : [];
        this.idCardEmblemFileUrlShow = this.form.idCardEmblemFileUrl ? [{ url: this.fileUrl + this.form.idCardEmblemFileUrl }] : [];
        this.idCardCopyFileUrlShow = this.form.idCardCopyFileUrl ? [{ url: this.fileUrl + this.form.idCardCopyFileUrl }] : [];
        this.doorPhotoFileUrlShow = this.form.doorPhotoFileUrl ? [{ url: this.fileUrl + this.form.doorPhotoFileUrl }] : [];
        this.workIndoorFileUrlShow = this.form.workIndoorFileUrl ? [{ url: this.fileUrl + this.form.workIndoorFileUrl }] : [];
        this.receptionFileUrlShow = this.form.receptionFileUrl ? [{ url: this.fileUrl + this.form.receptionFileUrl }] : [];
        // 处理字段类型
        if (this.form.idCardCopyFileUrl) {
          this.form.idFileType = '101';
        } else if (this.form.idCardHeadFileUrl) {
          this.form.idFileType = '100';
        }

        if (this.processId) {
          this.isEdit = true;
        } else {
          this.isEdit = false;
        }

      }
      if (this.$route.query.result == 'fail') {
        this.activeStep = 4;
        this.resultStatus = 'fail';
        this.getApprovalFlow()
      }
      for (let i = 1; i <= 3; i++) {
        let form = 'form_' + i;
        for (let p in this[form]) {
          if (p in this.form) {
            this[form][p] = this.form[p];
          }
        }
      }
    },
    destroyed() {
      sessionStorage.removeItem('mainInfo');
      sessionStorage.removeItem('bankInfo');
    },
    methods: {
      getProvinceList() {
        getProvinceList().then(response => {
          this.provinceList = response.data;
        })
      },
      async getCityList(code, resetItems, val) {
        let form = this['form_' + this.activeStep]
        form[code + 'AddrProvince'] = val[1];
        const response = await getCityList({
          provinceNo: val[0]
        })
        this[code + 'CityList'] = response.data;
        resetItems.forEach(item => {
          this.$set(form, item, '');
        })
        return this[code + 'CityList']
      },
      async getTownList(code, resetItems, val) {
        let form = this['form_' + this.activeStep]
        form[code + 'AddrCity'] = val[1];
        const response = await getTownList({
          cityNo: val[0]
        })
        this[code + 'TownList'] = response.data;
        resetItems.forEach(item => {
          this.$set(form, item, '');
        })
        return this[code + 'TownList']
      },
      searchBankNumberInfo() {
        this.$refs.bankNumberInfo.search()
        this.$refs.bankNumberInfo.isShow = true;
      },
      closeBankNumberInfoDialog(item) {
        let form = this['form_' + this.activeStep]
        if (item) {
          this.$set(form, 'bankChannelNo', item.bankChannelNo)
          this.$set(form, 'bankName', item.bankName)
        }
      },
      getTimeRange(val, param) {
        let form = this['form_' + this.activeStep]
        form[param + 'Begin'] = val[0];
        form[param + 'End'] = val[1];
      },
      beforeAvatarUpload(file) {
        const isTrueType = ['image/jpeg', 'image/png', 'image/bmp', 'image/gif'].includes(file.type);
        const isLt6M = file.size / 1024 / 1024 < 6;

        if (!isTrueType) {
          this.$message.error('上传文件只能是 bmp、png、jpeg、jpg或gif 格式!');
        }
        if (!isLt6M) {
          this.$message.error('上传文件大小不能超过 6MB!');
        }
        return isTrueType && isLt6M;
      },
      handleRemove(file, fileList, param) {
        let form = this['form_' + this.activeStep]
        form[param] = fileList;
      },
      handleSuccess(response, file, fileList, param) {
        let form = this['form_' + this.activeStep]
        form[param] = fileList;
      },
      nextStep() {
        if (this.activeStep == 3) {
          this.$refs.form_3.validate(async (valid, obj) => {
            if (!valid) {
              for (let key in obj) {
                this.$message.error(obj[key][0].message);
                return;
              }
            }
            if (!this.flowVisible) {
              this.flowVisible = true;
              return;
            }
            this.flowVisible = false;
            let form = { ...this.form, ...this.form_1, ...this.form_2, ...this.form_3 };
            // 遍历上传图片的fileUrl，拼装数据
            form.businessLicenseFileUrl && (form.businessLicenseFileUrl = (form['businessLicenseFileUrl'][0].response && form['businessLicenseFileUrl'][0].response.data) || form.businessLicenseFileUrl);
            form.idCardHeadFileUrl && (form.idCardHeadFileUrl = (form['idCardHeadFileUrl'][0].response && form['idCardHeadFileUrl'][0].response.data) || form.idCardHeadFileUrl);
            form.idCardEmblemFileUrl && (form.idCardEmblemFileUrl = (form['idCardEmblemFileUrl'][0].response && form['idCardEmblemFileUrl'][0].response.data) || form.idCardEmblemFileUrl);
            form.idCardCopyFileUrl && (form.idCardCopyFileUrl = (form['idCardCopyFileUrl'][0].response && form['idCardCopyFileUrl'][0].response.data) || form.idCardCopyFileUrl);
            form.doorPhotoFileUrl && (form.doorPhotoFileUrl = (form['doorPhotoFileUrl'][0].response && form['doorPhotoFileUrl'][0].response.data) || form.doorPhotoFileUrl);
            form.workIndoorFileUrl && (form.workIndoorFileUrl = (form['workIndoorFileUrl'][0].response && form['workIndoorFileUrl'][0].response.data) || form.workIndoorFileUrl);
            form.receptionFileUrl && (form.receptionFileUrl = (form['receptionFileUrl'][0].response && form['receptionFileUrl'][0].response.data) || form.receptionFileUrl);

            delete form.idFileType;

            for (let p in form) {
              if (typeof form[p] == 'string' && !(/Term/.test(p))) {
                form[p] = form[p].replace(/\s+/, '')
              }
            }

            clearVoid(form);

            if (this.isEdit) {
              const { data } = await editBusinessData({
                extInfo: JSON.stringify(form),
                commonFlowId: this.processId,
                taskId: this.taskId,
                remark: this.flowRemark,
              });
              data && this.$message.success(data);
              this.$router.push({
                path: '/audit/detailProcess',
                query: {
                  processId: this.processId,
                  taskId: this.taskId,
                }
              })
            } else {
              const { data } = await merchantAuth({
                extObj: form,
                participant: {},
                condition: {},
                remark: this.flowRemark,
              });
              this.activeStep ++;
              this.result = data;
            }
          });
        } else {
          let form = this.$refs['form_' + this.activeStep];
          form.validate(valid => {
            if (valid) {
              this.activeStep++;
              this.$nextTick(() => {
                window.scrollTo(0, 0);
              })
            }
          })
        }
      },
      goDetailProcess() {
        this.$router.push('/waitingHandle/detailProcess?processId=' + this.result.id);
      },
      async changeLeader() {
        let activeForm = this['form_' + this.activeStep]
        const form = {
          newLeaderPhone: activeForm.contactPhone,
          newLeaderName: activeForm.contactName,
          mchNo: this.form_1.mchNo,
        }
        await changeLeader(form);
        this.editAccess = false;
      },
      goEdit() {
        this.activeStep = 1;
        this.resultStatus = 'pend';
        this.getBackMainInfo();
      },
      async getApprovalFlow() {
        const {data} = await getErrorMsg();
        this.errMsg = data.approvalFlow;
      },
      async getBackMainInfo() {
        this.form = Object.assign({}, JSON.parse(this.errMsg.extInfo));
        this.businessLicenseFileUrlShow = this.form.businessLicenseFileUrl
          ? [{ url: this.fileUrl + this.form.businessLicenseFileUrl, name: this.isPdf(this.form.businessLicenseFileUrl) ? '营业执照.pdf' : '' }]
          : [];
        this.idCardHeadFileUrlShow = this.form.idCardHeadFileUrl ? [{url: this.fileUrl + this.form.idCardHeadFileUrl}] : [];
        this.idCardEmblemFileUrlShow = this.form.idCardEmblemFileUrl ? [{url: this.fileUrl + this.form.idCardEmblemFileUrl}] : [];
        this.idCardCopyFileUrlShow = this.form.idCardCopyFileUrl ? [{url: this.fileUrl + this.form.idCardCopyFileUrl}] : [];
        this.doorPhotoFileUrlShow = this.form.doorPhotoFileUrl ? [{url: this.fileUrl + this.form.doorPhotoFileUrl}] : [];
        this.workIndoorFileUrlShow = this.form.workIndoorFileUrl ? [{url: this.fileUrl + this.form.workIndoorFileUrl}] : [];
        this.receptionFileUrlShow = this.form.receptionFileUrl ? [{url: this.fileUrl + this.form.receptionFileUrl}] : [];

        this.form.businessLicenseFileUrl = this.businessLicenseFileUrlShow;
        if (this.form.managementValidityDateType) {
          this.form.managementValidityDateType = Number(this.form.managementValidityDateType)
        }
        if (this.form.certificateValidityDateType) {
          this.form.certificateValidityDateType = Number(this.form.certificateValidityDateType)
        }
        if (this.form.certificateType) {
          this.form.certificateType = Number(this.form.certificateType)
        }
        this.form.idFileType = this.form.idCardCopyFileUrl ? '101' : '100';
        // 需要重新获取省市地列表
        if (this.provinceList.length > 0) {
          await this.reGetAddressList('Province', 'register');
          await this.reGetAddressList('Province', 'management');
        }
        for (let i = 1; i <= 3; i++) {
          let form = 'form_' + i;
          for (let p in this[form]) {
            if (p in this.form) {
              this[form][p] = this.form[p];
            }
          }
        }
      },
      async reGetAddressList(next, type, parent) {
        let temp = parent ? parent : [];
        let lowerNext = next.toLowerCase()
        let list = next == 'Province' ? this[lowerNext + 'List'] : this[type + next + 'List'];
        if (list.length <= 0) {
          list = await this['get' + next + 'List'](type, [], temp);
        }
        // 拿到对应的上级列表项
        for (let item of list) {
          if (this.form[type + 'Addr' + next] == item[lowerNext + 'Name']) {
            temp = [item[lowerNext + 'No'], item[lowerNext + 'Name']];
            break;
          }
        }
        if (next == 'Province') {
          await this.reGetAddressList('City', type, temp);
        }
        if (next == 'City') {
          await this.reGetAddressList('Town', type, temp);
        }
      },
      getProtocal(index) {
        let doc;
        const a = document.createElement('a');
        if (index == 1) {
          doc = require('@/assets/doc/protocal.docx');
          a.download = '汇聚支付分账服务协议.docx';
        } else {
          doc = require('@/assets/doc/protocal2.docx');
          a.download = '平台商户入驻框架协议.docx';
        }
        a.href = doc;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      },
      handleFileRemove(file, param) {
        this['form_' + this.activeStep][param] = this['form_' + this.activeStep][param].filter(item => {
          return item.uid !== file.uid;
        });
        this[param + 'Show'] = this['form_' + this.activeStep][param]
      },
      flowConfirm(remark) {
        this.flowRemark = remark;
        this.nextStep();
      },
    }
  }
</script>

<style lang="scss" scoped>
  .page-container {
    .content-container {
      .example-image {
        float: left;
        width: 148px;
        height: 148px;
        margin-right: 20px;
      }
      .footer-container {
        text-align: center;
      }
    }
    .step_1 {
      .job-box {
        padding-bottom: 50px;
      }
    }
    .step_4 {
      padding: 50px 0;
      text-align: center;
      svg {
        width: 100px;
        height: 100px;
      }
      p {
        margin-bottom: 20px;
      }
    }
    .protocal-container {
      margin: 20px 0;
    }
    .protocal-link {
      cursor: pointer;
      color: $mainColor;
    }
    .el-checkbox-label {
      color: #000;
    }
  }
</style>
