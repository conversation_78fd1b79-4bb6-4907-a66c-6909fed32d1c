<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <div class="flex-item__label">商户编号：</div>
          <el-input v-model="searchForm.mchNo"></el-input>
        </div>
        <div class="flex-item">
          <div class="flex-item__label">商户名称：</div>
          <el-input v-model="searchForm.mchNameLike" placeholder="模糊查询"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">代征关系状态：</span>
          <el-select
            collapse-tags
            clearable
            v-model="searchForm.bindStatus">
            <el-option
              v-for="(item, index) in $dict('OpenOffEnum')"
              :key="index"
              :label="searchForm.bindStatus.length == 1 ? item.desc : item.desc.slice(0, 6)"
              :value="item.code">
              <span>{{ item.desc }}</span>
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <div class="flex-item__label">创建时间：</div>
          <date-picker
            ref="datepicker"
            type="datetimerange"
            :start-time.sync="searchForm.createTimeBegin"
            :end-time.sync="searchForm.createTimeEnd"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button type="primary" @click="search(true)">查询</el-button>
        <el-button @click="exportList">导出</el-button>
        <el-button type="text" @click="resetForm">清空筛选条件</el-button>
        <el-button type="text" @click="getExportList">查看导出列表</el-button>
      </div>
    </div>


    <div class="func-container">
      <el-tabs
        v-model="searchForm.mchStatus"
        @tab-click="search(true)"
        type="card">
        <el-tab-pane
          v-for="(item, index) in $dict('MchStatusEnum')"
          :key="index"
          :label="item.desc"
          :name="item.code"
        ></el-tab-pane>
      </el-tabs>
    </div>

    <div class="content-container">
      <el-table class="content-main" :data="list" :key="searchForm.mchStatus + '-Table'">
        <el-table-column label="创建时间" prop="createTime" min-width="100px">
          <template v-slot="{row}">
            <p v-html="renderTime(row.createTime)"></p>
          </template>
        </el-table-column>
        <el-table-column label="激活时间" prop="activeTime" min-width="100px">
          <template v-slot="{row}">
            <p v-html="renderTime(row.activeTime)"></p>
          </template>
        </el-table-column>

        <el-table-column label="商户名称" prop="mchName" min-width="160px">
          <template v-slot="{row}">
            {{ row.mchNo }}<br/>
            {{ row.mchName }}
          </template>
        </el-table-column>

        <el-table-column label="商户类型" prop="merchantType" min-width="160px">
          <template v-slot="{row}">
            {{ $dictCode('MerchantTypeEnum', row.merchantType).desc }}
          </template>
        </el-table-column>

        <el-table-column label="商户状态" prop="mchStatus" min-width="80px">
          <template v-slot="{row}">
            {{ $dictCode('MchStatusEnum', row.mchStatus).desc }}
          </template>
        </el-table-column>
        <el-table-column label="代征关系状态" prop="bindStatus" min-width="80px">
          <template v-slot="{row}">
            {{ $dictCode('OpenOffEnum', row.bindStatus).desc }}
          </template>
        </el-table-column>
        <el-table-column label="资质状态" prop="authStatus" min-width="80px">
          <template v-slot="{row}">
            {{ $dictCode('AuthStatusEnum', row.authStatus).desc }}
          </template>
        </el-table-column>
        <el-table-column label="操作" prop="data" min-width="140px" fixed="right">
          <template v-slot="{row}">
            <el-button
              type="text" @click="goMerchantDetail(row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>

    <!--导出dialog-->
    <export-record ref="exportRecord"></export-record>
    <export-select ref="exportSelect" dict="MainstayEmployerInfo" @confirm="submitExportForm"></export-select>

  </div>
</template>

<script>
  import ExportSelect from '@/components/ExportSelect'
  import ExportRecord from '@/components/ExportRecord'
  import { getMerchantList, exportMerchantList } from '@/api/merchant';

  export default {
    name: 'merchantList',
    components: {
      ExportRecord,
      ExportSelect,
    },
    data() {
      return {
        searchForm: {
          bindStatus:'',
          mchNo: '',
          mchNameLike: '',
          mchStatus: '100',
          createTimeBegin: '',
          createTimeEnd: '',
        },

        pageCurrent: 1,
        pageSize: 10,
        totalRecord: 0,

        list: [],

      }
    },
    mounted(){
      this.search()
    },
    methods: {
      async search(init) {
        if (init) {
          this.pageCurrent = 1;
        }
        const { data } = await getMerchantList({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        })
        this.list = data.data;
        this.totalRecord = data.totalRecord;
      },
      resetForm() {
        this.searchForm = {
          mchNo: '',
          mchNameLike: '',
          mchStatus: this.searchForm.mchStatus,
          bindStatus:'',
        };
        this.$refs.datepicker.clearTime()
      },
      goMerchantDetail(row) {
        this.$router.push({
          path: '/merchant/manage/merchantInfo',
          query: {
            mchNo: row.mchNo
          }
        })
      },
      async exportList() {

        this.$refs.exportSelect.visible = true;
      },
      async submitExportForm(form) {
        let paramMap = {
          ...this.searchForm,
        };

        if (paramMap.mchStatus == 0) {
          paramMap.mchStatus = '';
        }

        for (let p in paramMap) {
          if (!paramMap[p]) {
            delete paramMap[p]
          }
        }

        // 标准报表，传所有字段，自定义报表，将下标转换成数据
        await exportMerchantList({
          fieldListDataName: 'MainstayEmployerInfo',
          fieldInfoList: form.fieldInfoList,
          paramMap,
        });
        this.$message.success('导出成功，请到导出列表进行导出');
      },
      getExportList() {
        this.$refs.exportRecord.isShow = true;
        this.$refs.exportRecord.getExportRecord('31');
      }
    },
  }
</script>

<style scoped lang="scss">
  .box-container {
    .func-container {
      margin-top: 16px;
    }
  }
</style>
