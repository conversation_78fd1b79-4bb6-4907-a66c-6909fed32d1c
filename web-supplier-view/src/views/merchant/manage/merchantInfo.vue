<template>
  <div class="merchant-info-box">
    <merchant-info-comp
      :loading="loading"
      :base-info="baseInfo"
      :cooperate-info="cooperateInfo"
      :main-info="mainInfo"
      :business-info="businessInfo"
      :account-info="accountInfo"
      :agreement-info="agreementInfo"
      :quote-info="quoteInfo"
      :record-info="recordInfo"
      :show-sale="false"
      :show-leader="false"
      :show-arrow="false"
      :show-edit="false"
      :onlyShow="true"
      disabled
      @export-info="exportInfo"
      @get-export="getExportList"
    ></merchant-info-comp>

    <div class="form-btn-group text-center">
      <el-button @click="$router.push('/merchant/manage/list')">返回</el-button>
    </div>

    <!--导出dialog-->
    <export-select ref="exportSelect" dict="MainstayEmployerInfo" @confirm="submitExportForm"></export-select>

    <export-record ref="exportRecord"></export-record>

  </div>
</template>

<script>
  import {
    exportMerchantInfo,
    getBaseInfo, getCooperateInfo, getMainInfo, getAccountInfo, getBusinessInfo, getAgreementInfo, getQuoteInfo, getSystemRecord, getRecordInfo
  } from '@/api/merchant';

  import ExportSelect from '@/components/ExportSelect'
  import ExportRecord from '@/components/ExportRecord/index'
  import MerchantInfoComp from '@/components/MerchantInfoComp'
  const permissionMap = {
    'merchantEmployer:base:view': {
      api: getBaseInfo,
      result: 'baseInfo'
    },
    'merchantEmployer:coop:view': {
      api: getCooperateInfo,
      result: 'cooperateInfo'
    },
    'merchantEmployer:quote:view': {
      api: getQuoteInfo,
      result: 'quoteInfo'
    },
    'merchantEmployer:main:view': {
      api: getMainInfo,
      result: 'mainInfo'
    },
    'merchantEmployer:agreement:view': {
      api: getAgreementInfo,
      result: 'agreementInfo'
    },
    'merchantEmployer:account:view': {
      api: getAccountInfo,
      result: 'accountInfo'
    },
    'merchantEmployer:business:view': {
      api: getBusinessInfo,
      result: 'businessInfo'
    },
    // 'void': {
    //   api: getRecordInfo,
    //   result: 'recordInfo'
    // },
  };
  export default {
    name: 'merchantInfo',
    components: {
      ExportRecord,
      ExportSelect,
      MerchantInfoComp,
    },
    data() {
      return {
        merchantInfo: {},
        baseInfo: {},
        cooperateInfo: {},
        mainInfo: {},
        accountInfo: {},
        businessInfo: {},
        systemInfo: {},
        quoteInfo: [],
        recordInfo: [],
        agreementInfo: [],

        loading: false,
      }
    },
    computed: {
      mchNo() {
        return this.$route.query.mchNo || '';
      },
      actionType() {
        return this.$route.query.actionType
      },
      isView() {
        return this.actionType === 'VIEW'
      },
      isEdit() {
        return this.actionType === 'EDIT'
      },
    },
    mounted() {
      if (this.mchNo) {
        this.init();
      }
    },
    methods: {
      async init() {
        this.loading = true;
        // 获取商户各个模块下的信息
        await this.getMerchantAllInfo();
        this.loading = false;
      },
      async getMerchantAllInfo() {
        let promiseList = [];
        let resultList = [];
        for (let p in permissionMap) {
          if (p === 'void' || this.hadPermission(p)) {
            promiseList.push(permissionMap[p]['api']({
              mchNo: this.mchNo
            }));
            resultList.push(permissionMap[p]['result'])
          }
        }
        this.loading = true;
        // promiseList.push(getSystemRecord({
        //   mchNo: this.mchNo,
        //   type: this.mchNo.startsWith('M') ? 100 : 101,
        // }));
        // resultList.push('systemInfo');


        const res = await Promise.all(promiseList).finally(() => this.loading = false);
        res.forEach((item, index) => {
          this[resultList[index]] = item.data || {}
        })
      },
      exportInfo() {
        this.$refs.exportSelect.visible = true
      },
      async submitExportForm(form) {
        // 标准报表，传所有字段，自定义报表，将下标转换成数据
        await exportMerchantInfo({
          ...form,
          paramMap: {
            mchNo: this.mchNo
          }
        })
        this.$message.success('导出成功，请到导出列表进行导出');
      },
      getExportList() {
        this.$refs.exportRecord.isShow = true;
        this.$refs.exportRecord.getExportRecord('30');
      },
    }
  }
</script>

<style lang="scss" scoped>
  .merchant-info-box {
    padding-bottom: 100px;
    .flex-wrapper {
      margin: 0 32px;
      .el-tag {
        max-width: 90%;
        margin-right: 5px;
        margin-bottom: 5px;
        overflow-x: hidden;
        text-overflow: ellipsis;
      }
    }

    // 切换tab
    .tab-container {
      display: flex;
      width: 100%;
      margin-top: 16px;
      border-radius: 10px;
      overflow: hidden;

      .tab-item {
        display: inline-block;
        flex: 1;
        padding: 16px 0;
        border-right: 1px solid $deepGray;
        background: #fff;
        cursor: pointer;

        &:last-of-type {
          border-right: none;
        }

        &.active-item {
          background: $mainColor;
          color: #fff;
        }
      }
    }

    .form-btn-group {
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      padding: 16px 0;
      background: #fff;
      z-index: 999;
    }

  }

  .export-checkbox-item {
    margin-left: 0 !important;
    margin-right: 10px;
    margin-bottom: 10px;
  }
</style>
