<template>
  <el-container class="role-container" v-loading="loading">
    <div class="box-container">
      <el-button type="primary" class="create-btn" @click="showAddRoleForm" v-permission="'pms:role:add'">新增角色</el-button>
      <div class="flex-container search-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">角色：</span>
            <el-input v-model="searchParam.roleName"  placeholder="模糊查询"></el-input>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="search">搜索</el-button>
          </div>
        </div>
      </div>
      <el-main class="content-container">
        <el-table :data="roleList">
          <el-table-column type="index" label="序号"/>
          <el-table-column label="角色名称" prop="roleName">
            <template v-slot="{row}">
              {{ row.roleName }}
              <el-tag style="margin-left: 16px" type="info" v-if="row.roleType != 2">{{ $dictCode('RoleTypeEnum', row.roleType).desc }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="描述" prop="remark"/>
          <el-table-column label="员工数量">
            <template v-slot="{row}">
              <span class="func-content" @click="goStaff(row.id)">
                {{ row.employerNumber }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template v-slot="{row}">
              <div v-if="row.roleType == 2">
                <el-button
                  type="text"
                  size="small"
                  @click="showAssignFunctionForm(row, 'edit')"
                  v-permission="'pms:role:edit'">分配权限
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="showEditRoleForm(row)"
                  v-permission="'pms:role:edit'">编辑
                </el-button>
                <el-button
                  type="text"
                  size="small"
                  @click="deleteRole(row)"
                  v-permission="'pms:role:delete'">删除
                </el-button>
              </div>
              <el-button
                v-if="row.roleType == 1"
                type="text"
                size="small"
                @click="showAssignFunctionForm(row, 'view')"
              >查看权限</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>

      <el-footer class="pagination-container">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          background
          :page-sizes="[10,50,100]"
          :total="totalRecord"
          :page-size="pageSize"
          :current-page="pageCurrent"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-footer>
    </div>

    <role-form @success="search" ref="roleForm"/>
    <assign-function-form :type="type" ref="assignForm"/>
  </el-container>
</template>

<script>
  import roleForm from "./roleForm";
  import assignFunctionForm from "./assignFunctionForm";
  import { listRolePage, deleteRole } from "@/api/role";

  export default {
    name: "role",
    components: {
      roleForm,
      assignFunctionForm,
    },
    data() {
      return {
        loading: false,
        searchParam: {
          roleName: ""
        },
        roleList: [],
        pageCurrent: 1,
        pageSize: 10,
        totalRecord: 0,
        type: '',

      };
    },
    mounted() {
      this.search();
    },
    methods: {
      search(initFlag) {
        if (initFlag) {
          this.pageCurrent = 1;
        }
        this.loading = true;
        listRolePage({
          roleName: this.searchParam.roleName,
          pageCurrent: this.pageCurrent,
          pageSize: this.pageSize,
        }).then(({data}) => {
          if (data == null) {
            this.roleList = null;
            this.totalRecord = 0;
          }else {
            this.roleList = data.data;
            this.totalRecord = data.totalRecord;
          }
        }).finally(() => {
          this.loading = false;
        });
      },

      showAddRoleForm() {
        this.$refs.roleForm.actionType = "ADD";
        this.$refs.roleForm.form = {};
        this.$refs.roleForm.show = true;
      },

      showEditRoleForm(row) {
        this.$refs.roleForm.actionType = "EDIT";
        this.$refs.roleForm.form = {...row};
        this.$refs.roleForm.show = true;
      },

      showAssignFunctionForm(row, type) {
        this.type = type;
        this.$refs.assignForm.initAndShow(row.id);
      },
      deleteRole(row) {
        this.$confirm("确认删除角色?")
        .then(() => {
          deleteRole({id: row.id}).then(({data}) => {
            this.$message.success(data);
            this.search();
          })
        });
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.search(true);
      },
      handleCurrentChange(val) {
        this.pageCurrent = val;
        this.search();
      },
      goStaff(id) {
        this.$router.push({
          path: this.permissionToPath('pms:staff:view'),
          query: {
            role: id
          }
        })
      },
    },
  };
</script>
