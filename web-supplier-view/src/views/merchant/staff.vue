<template>
  <el-container class="staff-container">
    <div class="box-container">
      <el-button class="create-btn" type="primary" @click="toStaffForm('create')">添加员工</el-button>
      <div class="flex-container search-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">账号：</span>
            <el-input v-model="searchForm.phone"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">员工姓名：</span>
            <el-input v-model="searchForm.nameLike" placeholder="模糊查询"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">角色：</span>
            <el-select clearable v-model="searchForm.roleIds" multiple>
              <el-option
                v-for="item in roleList"
                :key="item.roleName"
                :value="item.id"
                :label="item.roleName">
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button type="text" @click="clearCondition">清空筛选条件</el-button>
          </div>
        </div>
      </div>

      <el-main class="content-container">
        <el-table :data="staffList">
          <el-table-column label="账号" prop="phone"></el-table-column>
          <el-table-column label="员工姓名" prop="name"></el-table-column>
          <el-table-column label="员工角色" prop="roleName"></el-table-column>
          <el-table-column
            label="用户类型"
            prop="type"
            :formatter="row => (row.type===1&&'超级管理员')||(row.type===2&&'普通操作员')">
          </el-table-column>

          <el-table-column label="添加人" prop="creator"></el-table-column>
          <el-table-column label="添加时间" prop="createTime" width="180"></el-table-column>
          <el-table-column label="操作" prop="data">
            <template slot-scope="scope">
              <el-button type="text" @click="toStaffForm('edit', scope.row)">编辑</el-button>
              <el-button type="text" @click="showDeleteStaffDialog(scope.row.id)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-main>

      <div class="pagination-container">
        <el-pagination
          v-if="totalNum"
          ref="pagination"
          layout="total, sizes, prev, pager, next, jumper"
          background
          :total="totalNum"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]" :page-size="pageSize"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>

      <el-dialog title="移除" :visible.sync="isShowDeleteDialog" width="30%" @close="deleteDialogClose">
        <el-alert title="员工移除后，该员工将无法管理商户，请谨慎操作" type="warning" :closable="false"/>

        <div class="delete-staff-form">
          <div class="delete-staff-item">
            <span class="delete-staff-item-label">支付密码：</span>
            <el-input v-model="deleteTradePwd" type="password"/>
          </div>
        </div>

        <div class="delete-staff-footer">
          <el-button type="primary" @click="deleteStaff">确定</el-button>
          <el-button type="text" @click="deleteDialogClose">取消</el-button>
        </div>
      </el-dialog>
    </div>
  </el-container>
</template>

<script>
  import { listStaffPage, deleteStaff } from "@/api/staff";
  import { getRoleList } from "../../api/common";
  import { encryptParam } from "@/utils/jsencrypt";
  import { getKey } from '@/utils/publicKey'

  export default {
    name: "staff",
    data() {
      return {
        searchForm: {
          phone: null,
          nameLike: null,
          roleIds: [],
        },
        pageCurrent: 1,
        pageSize: 10,
        totalNum: 0,
        staffList: [],
        roleList: [],

        isShowDeleteDialog: false,
        deleteId: null,
        deleteTradePwd: "",
      }
    },
    mounted() {
      if (this.$route.query.role) {
        this.searchForm.roleIds.push(Number(this.$route.query.role));
      }
      this.search();
      this.getRoleList();

    },
    methods: {
      search() {
        listStaffPage({
          pageCurrent: this.pageCurrent,
          pageSize: this.pageSize,
          ...this.searchForm,
        }).then(({data}) => {
          this.staffList = data.data;
          this.totalNum = data.totalRecord;
        })
      },
      clearCondition() {
        this.searchForm.phone = "";
        this.searchForm.nameLike = "";
        this.searchForm.roleIds = [];
        this.search();
      },
      handleCurrentChange(val) {
        this.pageCurrent = val;
        this.search();
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.search();
      },
      toStaffForm(type, row) {
        var redirect = "/merchant/staffForm?type=" + type;
        if (type === "edit") {
          redirect += "&id=" + row.id;
        }
        this.$router.push(redirect);
      },
      showDeleteStaffDialog(id) {
        this.isShowDeleteDialog = true;
        this.deleteId = id;
      },
      deleteDialogClose() {
        this.deleteId = null;
        this.deleteTradePwd = "";
        this.isShowDeleteDialog = false;
      },
      deleteStaff() {
        deleteStaff({
          id: this.deleteId,
          tradePwd: encryptParam({
            publicKey: getKey(),
            pwd: this.deleteTradePwd
          })
        }).then(({data}) => {
          this.$message.success(data);
        }).finally(() => {
          this.isShowDeleteDialog = false;
          this.search();
        });
      },
      getRoleList() {
        getRoleList().then(response => {
          this.roleList = response.data;
        })
      },
    }
  }
</script>

<style lang="scss" scoped>
  .box-container {

    .delete-staff-form {
      padding: 20px 0px 20px 0px;
      .delete-staff-item {
        display: flex;
        align-items: center;

        .delete-staff-item-label {
          width: 100px;
        }
      }
    }

    .delete-staff-footer {
      width: 100%;
      display: flex;
      justify-content: flex-end;
    }
  }
</style>
