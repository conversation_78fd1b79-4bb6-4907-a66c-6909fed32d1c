<template>
  <el-container class="layout">
    <el-header class="layout-header">
      <div class="layout-header-left">
        <div class="logo-container">
          <img src="@/assets/logo.png" alt class="logo"/>
        </div>
        <div>
          <span>选择商户</span>
        </div>
      </div>
      <div class="layout-header-right">
        <span>{{ userData && userData.operator.name }} </span>
        <el-button type="text" @click="onLogout()">退出</el-button>
      </div>
    </el-header>

    <el-main class="layout-main">
      <el-form ref="form" :rules="rules" :model="form" label-width="120px">
        <el-form-item label="原登录密码" prop="pwd">
          <el-input v-model="form.pwd" type="password" autocomplete="new-password"></el-input>
        </el-form-item>
        <el-form-item label="新登录密码" prop="newPwd">
          <el-input v-model="form.newPwd" type="password" autocomplete="new-password"></el-input>
        </el-form-item>
        <el-form-item label="确认新登录密码" prop="confirmPwd">
          <el-input v-model="form.confirmPwd" type="password" autocomplete="new-password"></el-input>
        </el-form-item>
      </el-form>
      <div class="btn-group">
        <el-button type="primary" @click="submit">确定修改</el-button>
        <el-button @click="goBack">返回</el-button>
      </div>
    </el-main>
  </el-container>
</template>

<script>
  import {mapGetters} from "vuex"
  import {updateLoginPwd} from "../../api/common";
  import {removeToken} from "../../utils/loginToken";
  import { removeKey, getKey } from "@/utils/publicKey"
  import { encryptParam } from "@/utils/jsencrypt";
  export default {
    name: "changePwd",
    computed: {
      ...mapGetters(["userData"])
    },
    data() {
      const newPwdConfirm = (rule, value, callback) => {
        this.form.newPwd == this.form.confirmPwd ? callback() : callback(new Error('两次输入密码不一致，请重新输入'));
      };
      const pwdValidate = (rule, value, callback) => {
        if (!value) {
          callback(new Error('请输入密码'))
        } else {
          if (value.length < 8 || value.length > 16 || !/\d/g.test(value) || !/[a-zA-Z]/g.test(value)) {
            callback(new Error('请输入符合规范的密码（8 - 16位长度，包含字母和数字，区分大小写）'))
          } else {
            callback()
          }
        }
      };
      return {
        form: {
          pwd: '',
          newPwd: '',
          confirmPwd: ''
        },
        rules: {
          pwd: [
            { required: true, message: '请输入原登录密码', trigger: 'blur' },
          ],
          newPwd: [
            { required: true, message: '请输入新登录密码', trigger: 'blur' },
            { validator: pwdValidate, trigger: 'blur' }
          ],
          confirmPwd: [
            { required: true, message: '请确认新登录密码', trigger: 'blur' },
            { validator: newPwdConfirm, trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      onLogout() {
        removeToken();
        removeKey();
        this.$router.push({path: "/login"});
      },
      submit() {
        this.$refs.form.validate(valid => {
          if(valid) {
            updateLoginPwd({
              originPwd: encryptParam({
                pwd: this.form.pwd,
                publicKey: getKey(),
              }),
              pwd: encryptParam({
                pwd: this.form.newPwd,
                publicKey: getKey(),
              }),
            }).then(res => {
              this.$message.success(res.data);
              this.$router.push('/login');
            })
          }
        })
      },
      goBack() {
        this.$router.back();
      }
    }
  }
</script>

<style lang="scss" scoped>
  .layout {
    width: 100%;
    background: $lightGray;

    .layout-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      }
    .layout-header-left {
      display: flex;
      justify-self: start;
      align-items: center;

    }
    .logo-container .logo {
      height: 60px;
      padding: 15px 15px 15px 15px;
      box-sizing: border-box;
    }

    .layout-header-right {
      display: flex;
      justify-self: start;
      align-items: center;
    }

    .layout-main {
      width: 500px;
      margin: 0 auto;
      .item {
        margin-bottom: 10px;
        line-height: 1.5;
      }
      .text {
        display: inline-block;
        width: 120px;
        margin-right: 50px;
        text-align: right;
      }
      .btn-group {
        margin-top: 30px;
        text-align: center;
      }
      .el-form-item {
        margin-bottom: 36px;
      }
    }
  }
</style>
