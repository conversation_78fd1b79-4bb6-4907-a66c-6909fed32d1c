<template>
  <div class="trade-pwd-change-warpper">
    <el-form
      class="trade-pwd-change-form"
      ref="tradePwdChangeForm"
      :model="tradePwdChangeForm"
      :rules="tradePwdChangeRules"
      label-position="right"
      label-width="150px"
    >
      <el-form-item
        label="支付密码："
        prop="tradePwd"
      >
        <el-input
          v-model="tradePwdChangeForm.tradePwd"
          type="password"
          autocomplete="new-password"
          placeholder="请输入6位数字作为支付密码"
          maxlength="6"
        ></el-input>
      </el-form-item>

      <el-form-item
        label="确认支付密码"
        prop="confirmTradePwd"
      >
        <el-input
          v-model="tradePwdChangeForm.confirmTradePwd"
          type="password"
          autocomplete="new-password"
          maxlength="6"
        ></el-input>
      </el-form-item>

      <el-form-item
        label="短信验证码："
        prop="smsCode"
      >
        <el-input v-model="tradePwdChangeForm.smsCode">
          <template slot="append">
            <SmsCode :phone="phone"></SmsCode>
          </template>
        </el-input>
        <span class="remark">验证短信将发送到手机号{{ phone }}</span>
      </el-form-item>

      <el-button
        class="submit-btn"
        type="primary"
        @click="onSubmit"
      >保存
      </el-button>

      <el-button
        class="cancel-btn"
        @click="onCancel"
      >取消
      </el-button>
    </el-form>
  </div>
</template>

<script>
  import { changeTradePwd } from "@/api/safeSetting";
  import { sendSmsCode } from "@/api/common";
  import { encryptParam } from "@/utils/jsencrypt";
  import store from '@/store';
  import SmsCode from "../../components/SmsCode/index";
  import { getKey } from '@/utils/publicKey'

  export default {
    name: "tradePwdChange",
    components: {
      SmsCode
    },
    data() {
      const newPwdConfirm = (rule, value, callback) => {
        this.tradePwdChangeForm.tradePwd == this.tradePwdChangeForm.confirmTradePwd ? callback() : callback(new Error('两次输入密码不一致，请重新输入'));
      };
      const pwdValid = (rule, value, callback) => {
        if (value.length !== 6 || /[^\d]/g.test(value)) {
          callback(new Error('支付密码必须为6位数字'));
        } else {
          callback();
        }
      }
      return {
        phone: "",
        tradePwdChangeForm: {
          tradePwd: null,
          confirmTradePwd: null,
          smsCode: null
        },
        tradePwdChangeRules: {
          smsCode: [
            {required: true, message: '请输入短信验证码', trigger: 'blur'},
          ],
          tradePwd: [
            {required: true, message: '请输入支付密码', trigger: 'blur'},
            {validator: pwdValid, message: '请输入6位数字支付密码', trigger: 'blur'}
          ],
          confirmTradePwd: [
            {required: true, message: '请确认支付密码', trigger: 'blur'},
            {validator: newPwdConfirm, trigger: 'blur'}
          ],
        }
      };
    },
    mounted() {
      this.phone = store.getters.userData.staff.phone;
    },
    methods: {
      onSendSms() {
        sendSmsCode({phone: this.phone})
        .then(({data}) => {

        });
      },
      onSubmit() {
        this.$refs.tradePwdChangeForm.validate(async (valid) => {
          if (valid) {
            let form = {...this.tradePwdChangeForm}
            form.tradePwd = encryptParam({
              pwd: form.tradePwd,
              publicKey: getKey(),
            })
            form.confirmTradePwd = encryptParam({
              pwd: form.confirmTradePwd,
              publicKey: getKey(),
            })
            const {code, data} = await changeTradePwd(form);
            if (code === 20000) {
              this.$message.success(data);
              this.$router.push("/system/safe");
            }
          }
        })
      },
      onCancel() {
        this.$router.push("/system/safe");
      }
    }
  };
</script>

<style scoped lang="scss">
  .trade-pwd-change-warpper {
    text-align: center;
    background: #ffffff;

    .trade-pwd-change-form {
      width: 500px;
      box-sizing: content-box;
      padding: 50px;
      ::v-deep .el-form-item__content {
        text-align: left;
      }
      .el-form-item {
        margin-bottom: 24px;
      }
      .remark {
        font-size: 14px;
        color: #00000072;
      }
      .submit-btn {
        width: 120px;
      }

      .cancel-btn {
        width: 120px;
      }
    }
  }
</style>
