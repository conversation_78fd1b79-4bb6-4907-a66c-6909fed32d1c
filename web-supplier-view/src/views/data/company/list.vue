<template>
  <div class="box-container">
    <warning-block>数据次日生成，当前数据时间截至 {{ lastTime }}</warning-block>
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">商户编号：</span>
          <el-input
            clearable
            v-model="form.employerNo"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">商户名称：</span>
          <el-input
            clearable placeholder="模糊查询"
            v-model="form.employerName"
          ></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">账单月份：</span>
          <date-picker
            :start-time.sync="form.beginDate"
            :end-time.sync="form.endDate"
            :is-show-fast-time="false"
            :auto-fix="false"
            ref="datePicker"
            picker="separate"
            type="month"
          >
          </date-picker>
        </div>
      </div>

      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询</el-button>
        <el-button @click="exportRecord">导出</el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件</el-button>
        <el-button
          type="text"
          @click="getExport"
        >查看导出列表</el-button>
      </div>
    </div>

    <div class="func-container">
      <el-button
        type="text"
        @click="showResult = !showResult"
      >
        统计查询结果
        <i
          class="el-icon-instance"
          :class="{
            'el-icon-arrow-up': showResult,
            'el-icon-arrow-down': !showResult,
          }"
        ></i>
      </el-button>
      <div v-if="showResult">
        <div>
          商户
          <span style="color: #409eff">{{ countData.employerCount || 0 }}</span>
          个，发单数量
          <span style="color: #409eff">{{ countData.orderCount || 0 }}</span>
          个，自由职业者
          <span style="color: #409eff">{{
            countData.freelanceCount || 0
          }}</span>
          个；
        </div>
        <div>
          实发金额汇总
          <span style="color: #fbb84e">{{
            countData.orderItemNetAmountSum || 0
          }}</span>
          元，订单金额
          <span style="color: #fbb84e">{{
            countData.orderAmountSum || 0
          }}</span>
          元。
        </div>
      </div>
    </div>

    <div class="content-container">
      <el-table
        :data="list"
        @sort-change="onSortChange"
      >
        <el-table-column label="商户名称">
          <template v-slot="{ row }">
            <div
              class="func-content"
              @click="goOrderDetail(row, { employerNameLike: row.employerName })"
            >
              {{ row.employerName }}
            </div>
          </template>
        </el-table-column>
        <!--
        <el-table-column
          label="销售"
          prop="saleName"
        ></el-table-column>
        -->
        <el-table-column
          label="代征主体"
          prop="mainstayName"
        ></el-table-column>
        <el-table-column
          label="月份"
          prop="createDate"
        >
          <template v-slot="{ row }">
            <span
              class="func-content"
              @click="goOrderDetail(row)"
            >
              {{ row.createDate }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="订单金额"
          prop="orderItemAmount"
          sortable="custom"
        >
          <template v-slot="{ row }">
            <div class="text-right">￥{{ row.orderItemAmount }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="实发金额"
          prop="orderItemNetAmount"
          sortable="custom"
        >
          <template v-slot="{ row }">
            <div class="text-right">￥{{ row.orderItemNetAmount }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="发单数量"
          align="center"
          prop="orderAmount"
          sortable="custom"
        >
          <template v-slot="{ row }">
            <div
              class="func-content"
              @click="goOrderDetail(row)"
            >
              {{ row.orderAmount }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="发单人数"
          align="center"
          prop="receiverNumber"
          sortable="custom"
        >
          <template v-slot="{ row }">
            <div
              class="func-content"
              @click="goOrderDetail(row)"
            >
              {{ row.receiverNumber }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </div>

    <export-record ref="exportRecord"></export-record>
  </div>
</template>

<script>
import dayjs from "dayjs";
import { analyzeMerchant, countMerchant, exportCompanyData } from "@/api/data";
import ExportRecord from '@/components/ExportRecord'

export default {
  name: "company-data",
  components: {
    ExportRecord,
  },
  data() {
    return {
      form: {
        beginDate: dayjs().format("YYYY-MM"),
        endDate: dayjs().format("YYYY-MM"),
      },

      totalRecord: 0,
      pageCurrent: 1,
      pageSize: 10,

      list: [],
      countData: {},
      showResult: false,
      sortObj: {
        order: "",
        sortColumn: "",
      },

      sale: [],

      lastTime: dayjs()
        .subtract(1, "day")
        .endOf("day")
        .endOf("hour")
        .format("YYYY-MM-DD HH:mm:ss"),
    };
  },
  async mounted() {
    this.search(true);
  },
  methods: {
    async search(init, opts) {
      if (init === true) {
        this.pageCurrent = 1;
      }
      const [
        {
          data: { data: list, totalRecord },
        },
        { data: countData },
      ] = await Promise.all([
        analyzeMerchant({
          ...this.form,
          ...this.sortObj,
          pageCurrent: this.pageCurrent,
          pageSize: this.pageSize,
        }),
        countMerchant({
          ...this.form,
        }),
      ]);

      this.list = list;
      this.totalRecord = totalRecord;
      this.countData = countData;
    },
    resetForm() {
      this.form = {};
      this.$refs.datePicker.clearTime();
    },
    goOrderDetail(row, param = {}) {
      this.$router.push({
        path: "/trade/order/detail",
        query: {
          employerNameLike: row.employerName,
          createBeginDate:
            dayjs(row.createDate)
              .subtract(1, "month")
              .startOf("month")
              .format("YYYY-MM-DD") + " 00:00:00",
          createEndDate:
            dayjs(row.createDate).endOf("month").format("YYYY-MM-DD") + " 23:59:59",
          completeBeginDate:
            dayjs(row.createDate).startOf("month").format("YYYY-MM-DD") + " 00:00:00",
          completeEndDate:
            dayjs(row.createDate).endOf("month").format("YYYY-MM-DD") + " 23:59:59",
          mainstayNo: row.mainstayNo,
          employerNo: row.employerNo,
          orderItemStatusList: ["100"],
          ...param,
        },
      });
    },
    onSortChange({ prop, order }) {
      this.sortObj = {
        order: order ? order.replace("ending", "") : "",
        sortColumn: prop,
      };
      this.search();
    },
    async exportRecord() {
      const { data } = await exportCompanyData(this.form)
      data && this.$message.success(data)
    },
    getExport() {
      this.$refs.exportRecord.open('58')
    }
  },
};
</script>

<style scoped lang="scss">
</style>
