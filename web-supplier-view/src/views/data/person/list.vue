<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">姓名：</span>
          <el-input
            clearable
            v-model="form.receiveName"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">证件号码：</span>
          <el-input
            clearable
            v-model="form.idCard"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业：</span>
          <el-input
            clearable placeholder="模糊查询"
            v-model="form.employerName"
          ></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">账单月份：</span>
          <date-picker
            :start-time.sync="form.beginDate"
            :end-time.sync="form.endDate"
            :is-show-fast-time="false"
            :auto-fix="false"
            ref="datePicker"
            picker="separate"
            type="month"
          >
          </date-picker>
          <div class="flex-item">
            <span
              class="flex-item__label"
              style="width: 150px"
            >筛选大于2.7万：</span>
            <el-switch
              v-model="form.amountLimit"
              :active-value="1"
              :inactive-value="0"
              active-text="是"
              inactive-text="否"
            ></el-switch>
          </div>
        </div>
      </div>

      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询</el-button>
        <el-button @click="exportRecord">导出</el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件</el-button>
        <el-button
          type="text"
          @click="getExport"
        >查看导出列表</el-button>
        <div style="text-align: left; margin-top: 8px">
          <el-button
            type="primary"
            @click="batchDownloadID"
          >批量下载身份证</el-button>
          <el-button @click="getIdExport">查看身份证导出列表</el-button>
        </div>
      </div>
    </div>

    <div class="func-container">
      <el-button
        type="text"
        @click="showResult = !showResult"
      >
        统计查询结果
        <i
          class="el-icon-instance"
          :class="{
            'el-icon-arrow-up': showResult,
            'el-icon-arrow-down': !showResult,
          }"
        ></i>
      </el-button>
      <div v-if="showResult">
        <div>
          自由职业者
          <span style="color: #409eff">{{
            countData.freelanceCount || 0
          }}</span>
          个，接单数量
          <span style="color: #409eff">{{ countData.orderCount || 0 }}</span>
          个；
        </div>
        <div>
          实发金额汇总
          <span style="color: #fbb84e">{{
            countData.orderItemNetAmountSum || 0
          }}</span>
          元。
        </div>
      </div>
    </div>

    <div class="content-container">
      <el-table
        :data="list"
        @sort-change="onSortChange"
        @selection-change="onSelectChange"
        @filter-change="onFilterChange"
      >
        <!-- <el-table-column type="selection"></el-table-column> -->
        <el-table-column
          label="姓名"
          prop="receiveName"
        ></el-table-column>
        <el-table-column
          label="证件号码"
          prop="receiveIdCardNo"
          width="180"
        >
          <template v-slot="{ row }">
            {{ row.receiveIdCardNo }}
            <div
              v-if="row.idCardBackUrl"
              @click="openIdCard(row)"
              class="func-content"
            >
              查看证件照片
            </div>
            <div
              v-if="row.idCardCopyUrl"
              @click="openIdCopy(row)"
              class="func-content"
            >
              查看证件复印件
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="手机号"
          prop="receivePhoneNo"
          width="180"></el-table-column>
        <el-table-column
          label="月份"
          prop="createDate"
        ></el-table-column>
        <el-table-column
          label="用工企业"
          prop="employer"
          width="180"
        >
          <template v-slot="{ row }">
            <div
              class="func-content"
              @click="goOrderDetail(row, { employerNameLike: row.employerName })"
            >
              {{ row.employerName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="代征主体"
          prop="mainstayName"
        >
          <template v-slot="{ row }">
            <div
              class="func-content"
              @click="goOrderDetail(row)"
            >
              {{ row.mainstayName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="接单数量"
          prop="conditionOrder"
        >
          <template v-slot="{ row }">
            <el-button
              type="text"
              @click="goOrderDetail(row, {
                  employerNameLike: row.employerName,
                  receiveIdCardNo: row.receiveIdCardNo,
                })"
            >
              {{ row.conditionOrder }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="实发金额"
          sortable="custom"
          width="150"
          prop="orderItemNetAmount"
        >
          <template v-slot="{ row }">
            <div class="text-right">￥{{ row.orderItemNetAmount }}</div>
          </template>
        </el-table-column>
          <!-- :filters="signStatusFilter"
          :filter-multiple="false" -->
        <el-table-column
          width="100"
          label="电子签约"
          prop="signRecord"
          column-key="hasSign"
        >
          <template v-slot="{ row }">
            <el-tag type="success">
            <!-- <el-tag :type="row.signRecord == 1 ? 'success' : 'danger'"> -->
              <!-- {{ $dictCode('SupplierDeliverData', row.signRecord).desc }} -->
              {{ signStatusMap[row.signRecord] }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          width="180"
          label="身份证复印件"
          prop="idCard"
          column-key="hasUploadIdCard"
          :filters="[
            { text: '已上传', value: 1 },
            { text: '未上传', value: 0 },
          ]"
        >
          <template v-slot="{ row }">
            <!-- <el-tag :type="row.idCard == 1 ? 'success' : 'danger'"> -->
            <el-tag type="success">
              {{ $dictCode('SupplierDeliverAuthData', row.idCard).desc }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </div>

    <export-record ref="exportRecord" type="open"></export-record>
  </div>
</template>

<script>
  import dayjs from "dayjs";
  import { analyzeFreelance, countFreelance, batchDownloadID, exportPersonData } from "@/api/data";
  import ExportRecord from "@/components/ExportRecord";
  export default {
    name: "freelance-data",
    components: {
      ExportRecord,
    },
    data() {
      return {
        form: {
          beginDate: dayjs().format("YYYY-MM"),
          endDate: dayjs().format("YYYY-MM"),
        },
        totalRecord: 0,
        pageCurrent: 1,
        pageSize: 10,

        list: [],
        countData: {},
        showResult: false,

        editRow: {},

        sortObj: {
          order: "",
          sortColumn: "",
        },

        selection: [],
      };
    },
    computed: {
      signStatusDict() {
        let dict = [...this.$dict('SupplierDeliverData')]
        dict.forEach(item => {
          item.code = Number(item.code) || null
        })
        return dict
      },
      signStatusMap() {
        let map = {}
        this.signStatusDict.forEach(item => {
          item.code = Number(item.code) || null
          map[item.code] = item.desc
        })
        return map
      },
      signStatusFilter() {
        let arr = []
        this.signStatusDict.forEach(item => {
          arr.push({
            text: item.desc,
            value: item.code,
          })
        })
        return arr
      },
    },
    mounted() {
      this.search(true);
    },
    methods: {
      async search(init, opts) {
        if (init === true) {
          this.pageCurrent = 1;
        }
        const [
          {
            data: { data: list, totalRecord },
          },
          { data: countData },
        ] = await Promise.all([
          analyzeFreelance({
            ...this.form,
            ...this.sortObj,
            pageCurrent: this.pageCurrent,
            pageSize: this.pageSize,
          }),
          countFreelance({
            ...this.form,
          }),
        ]);
        this.list = list;
        this.totalRecord = totalRecord;
        this.countData = countData;
      },
      resetForm() {
        this.form = {};
        this.$refs.datePicker.clearTime();
      },
      goOrderDetail(row, param = {}) {
        this.$router.push({
          path: "/trade/order/detail",
          query: {
            createBeginDate:
              dayjs(row.createDate)
                .subtract(1, "month")
                .startOf("month")
                .format("YYYY-MM-DD") + " 00:00:00",
            createEndDate:
              dayjs(row.createDate).endOf("month").format("YYYY-MM-DD") + " 23:59:59",
            completeBeginDate:
              dayjs(row.createDate).startOf("month").format("YYYY-MM-DD") + " 00:00:00",
            completeEndDate:
              dayjs(row.createDate).endOf("month").format("YYYY-MM-DD") + " 23:59:59",
            orderItemStatusList: ["100"],
            ...param,
          },
        });
      },
      onSortChange({ prop, order }) {
        this.sortObj = {
          order: order ? order.replace("ending", "") : "",
          sortColumn: prop,
        };
        this.search();
      },
      async openIdCard(row) {
        let urls = [];
        if (row.idCardBackUrl && row.idCardFrontUrl) {
          urls = [
            row.idCardBackUrl,
            row.idCardFrontUrl,
          ];
        }
        if (row.cerFaceUrl) {
          urls.push(row.cerFaceUrl);
        }
        const formatUrls = []
        for (let i = 0; i < urls.length; i++) {
          const fileMsg = await this.formatFileMsg(urls[i])
          formatUrls.push(fileMsg.fileUrl)
        }
        this.$preview(formatUrls);
      },
      async openIdCopy(row) {
        let urls = [];
        if (row.idCardCopyUrl) {
          urls = [row.idCardCopyUrl];
        }
        const formatUrls = []
        for (let i = 0; i < urls.length; i++) {
          const fileMsg = await this.formatFileMsg(urls[i])
          formatUrls.push(fileMsg.fileUrl)
        }
        this.$preview(formatUrls);
      },
      onFilterChange(filters) {
        let keys = Object.keys(filters)
        keys.forEach(key => {
          this.form[key] = filters[key][0]
        })
        this.search()
      },
      async exportRecord() {
        const { data } = await exportPersonData(this.form)
        data && this.$message.success(data)
      },
      getExport() {
        this.$refs.exportRecord.open("32");
      },
      async batchDownloadID() {
        // const idList = this.selection.map((item) => item.id);
        // const { data } = await batchDownloadID({ idList });
        const { data } = await batchDownloadID(this.form);
        data && this.$message.success(data);
      },
      onSelectChange(val) {
        this.selection = val;
      },
      getIdExport() {
        this.$refs.exportRecord.open("35");
      },
    },
  };
</script>

