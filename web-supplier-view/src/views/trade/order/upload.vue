<template>
  <div class="page-container">
    <div class="header-container">
      <el-steps :active="activeStep" align-center>
        <el-step title="上传文件"></el-step>
        <el-step title="上传完成"></el-step>
      </el-steps>
    </div>

    <div class="content-container">
      <el-form class="step_1" v-show="activeStep == 1" :model="form" :rules="rules" ref="uploadForm">
        <el-form-item prop="workCategoryName" class="inline-form-item">
          <p class="form-item-label">请选择自由职业者服务类型</p>
          <el-select clearable
            v-model="form.workCategoryName"
            @change="changeWorkCategory"
          >
            <el-option
              v-for="(item, index) in workCategoryList"
              :key="index"
              :label="item.workCategoryName"
              :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="inline-form-item" prop="invoiceCategoryCode">
          <el-select
            clearable
            v-model="form.invoiceCategoryCode"
            @change="changeInvoiceCategory">
            <el-option
              v-for="(item,index) in form.invoiceCategoryList"
              :key="index"
              :label="item.invoiceCategoryName"
              :value="item.invoiceCategoryCode">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item prop="serviceDesc">
          <p class="form-item-label">服务描述</p>
          <el-input
            disabled
            type="textarea"
            v-model="form.serviceDesc"
            :autosize="{ minRows: 5, maxRows: 5}"
            placeholder="为使落地供应商匹配到合适的自由职业者，请按实际描述个人具体需要做什么">
          </el-input>
        </el-form-item>

        <el-form-item prop="mainstayName" class="inline-form-item">
          <p class="form-item-label">请选择代征主体和发放方式</p>
          <el-select clearable
            v-model="form.mainstayName"
            @change="changeMainstay">
            <el-option
              v-for="(item, index) in mainstayList"
              :key="index"
              :label="item.mainstayName"
              :value="[item.mainstayNo, item.mainstayName]">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item prop="channelType" class="inline-form-item">
          <el-select clearable
            v-model="form.channelType"
            :disabled="!form.mainstayNo">
            <el-option
              v-for="(type, key) in channelTypeList"
              :key="key"
              :label="$dictCode('ChannelTypeEnum', type).desc"
              :value="$dictCode('ChannelTypeEnum', type).code">
            </el-option>
          </el-select>
          <el-button
            type="text"
            @click="downloadTemplate"
            v-if="form.channelType">
            下载{{ $dictCode('ChannelTypeEnum', form.channelType).desc }}模板
          </el-button>
        </el-form-item>

        <el-form-item prop="file">
          <p class="form-item-label">上传填好的发放名单</p>
          <el-upload
            class="upload-demo"
            data-type="businessLicenseFileUrl"
            action=""
            :auto-upload="false"
            :headers="uploadHeader"
            accept=".xlsx,.xls"
            :limit="1"
            :file-list="fileList"
            :on-change="handlerChange">
            <el-button size="small">选择需要上传的文件</el-button>
          </el-upload>
        </el-form-item>

        <div class="form-footer">
          <el-button type="primary" @click="confirmData">上传</el-button>
        </div>
      </el-form>

      <div class="step_2" v-show="activeStep == 2">
        <img src="@/assets/success.png" alt="">
        <p class="result-title">已上传</p>
        <div class="result-wrapper">
          <p class="result-detail">您的文件校验中，请返回发放名单查看批次受理状态，并对批次进行进一步操作</p>
          <p class="result-detail">批次号：{{ result.platBatchNo }}</p>
        </div>
        <div class="footer-container">
          <el-button type="primary" @click="goRoster">返回发放名单</el-button>
          <el-button @click="continueUpload">继续上传</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { uploadOrderList } from '@/api/order'
import { getEmployerPosition, getMainstayList, getOpenChannelType } from '@/api/merchant'


export default {
  name: 'tradeOrderRoster',
  data() {
    return {
      activeStep: 1,

      form: { // 上传表单
        workCategoryCode: '',
        workCategoryName: '',
        serviceDesc: '',
        invoiceCategoryCode: '',
        invoiceCategoryName: '',
        invoiceCategoryList: [],
        mainstayNo: '',
        mainstayName: '',
        channelType: '',
        file: undefined,
      },

      workCategoryList: [],

      mainstayList: [],
      channelTypeList: [],

      fileList: [],

      rules: {
        workCategoryName: [
          { required: true, message: '请选择自由职业者服务类型', trigger: 'change' }
        ],
        serviceDesc: [
          { required: true, message: '请输入服务描述', trigger: 'blur' }
        ],
        mainstayName: [
          { required: true, message: '请选择代征主体', trigger: 'change' }
        ],
        channelType: [
          { required: true, message: '请选择发放方式', trigger: 'change' }
        ],
        file: [
          { required: true, message: '请选择上传文件' }
        ],
        invoiceCategoryCode: [
          { required: true, message: '请选择发票类目', trigger: 'change' }
        ]
      },

      result: '',
    }
  },
  mounted() {
    this.getListAllWorkCategory();
    this.getMainstayList();
  },
  methods: {
    // 获取工作类目
    async getListAllWorkCategory() {
      const { data } = await getEmployerPosition();
      this.workCategoryList = data;
    },
    async getMainstayList() {
      const { data } = await getMainstayList();
      this.mainstayList = data;
      let item = this.mainstayList[0];
      if (item) {
        this.changeMainstay([item.mainstayNo, item.mainstayName])
      }
    },
    changeWorkCategory(value) {
      this.form.invoiceCategoryCode = '';
      this.form.invoiceCategoryName = '';
      this.form = Object.assign({}, this.form, value);
    },
    changeInvoiceCategory(value) {
      this.form.invoiceCategoryList.forEach(item => {
        if (item.invoiceCategoryCode == value) {
          this.form.invoiceCategoryCode = item.invoiceCategoryCode;
          this.form.invoiceCategoryName = item.invoiceCategoryName;
        }
      })
    },
    async changeMainstay(val) {
      this.form.mainstayNo = val[0];
      this.form.mainstayName = val[1];
      const { data } = await getOpenChannelType();
      this.channelTypeList = data.map(type => String(type));
      this.form.channelType = this.channelTypeList[0];
    },
    handlerChange(file, fileList) {
      this.form.file = fileList[0].raw
    },
    confirmData() {
      this.$refs.uploadForm.validate(valid => {
        if (valid) {
          this.uploadData();
        }
      })
    },
    async uploadData() {
      let form = { ...this.form };
      delete form.invoiceCategoryList;
      let formData = new FormData();
      for (let p in form) {
        formData.append(p, form[p]);
      }
      const { data } = await uploadOrderList(formData);
      this.result = data;
      this.activeStep++;
    },
    continueUpload() {
      this.$refs.uploadForm.resetFields();
      this.fileList = [];
      this.activeStep = 1;
    },
    goRoster() {
      this.$router.push('/trade/order/roster')
    },
    handleFileRemove(file) {
      this.fileList = this.fileList.filter(item => {
        return item.name !== file.name;
      })
    },
    downloadTemplate() {
      let type = this.$dictCode('ChannelTypeEnum', this.form.channelType).desc;
      let template;
      if (type == '支付宝') {
        template = require('@/assets/template/template-zfb.xlsx');
      } else if (type == '银行卡') {
        template = require('@/assets/template/template-bank.xlsx');
      }
      const a = document.createElement('a');
      a.download = `发放方式-${type}模板.xlsx`;
      a.href = template;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    },
    cancel() {
      this.$router.push('/trade/order/roster');
    }
  }
}
</script>

<style lang="scss" scoped>
  .page-container {
    .el-form {
      width: 600px;
    }
    .el-form-item {
      margin-bottom: 36px;
    }
    .inline-form-item {
      display: inline-block;
    }
    .form-item-label {
      position: relative;
      padding-left: 10px;
      font-weight: bold;

      &::before {
        content: '*';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        color: #f00;
      }
    }
    .step_2 {
      text-align: center;
      img {
        width: 100px;
      }
    }

    .item-work-category {
      .el-input {
        display: inline-block;
        width: 200px;
      }
    }

    .result-title {
      font-size: 30px;
      font-weight: 600;
    }
    .result-detail {
      color: #ddd
    }
    .footer-container {
      margin-top: 40px;
    }
  }
</style>
