<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户订单号：</span>
            <el-input v-model="searchForm.mchOrderNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">平台流水号：</span>
            <el-input v-model="searchForm.platTrxNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">姓名：</span>
            <el-input v-model="searchForm.receiveName"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">发放方式：</span>
            <el-select clearable v-model="searchForm.channelType">
              <el-option
                v-for="(item, index) in $dict('ChannelTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code">
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">收款账号：</span>
            <el-input v-model="searchForm.receiveAccountNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              type="datetimerange"
              ref="startPicker"
              v-model="createTimeRange"
              fast-time="today"
              @change="getTimeRange('create', $event)">
            </date-picker>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item search-btn-group">
            <el-button type="primary" @click="search(true)">查询</el-button>
            <el-button @click="exportOrderItem">导出</el-button>
            <el-button type="text" @click="getExportList">查看导出列表</el-button>
            <el-button type="text" @click="clearField">清空搜索条件</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table :data="response.data">

        <el-table-column label="创建时间/完成时间">
          <template v-slot="{row}">
            <p>{{ row.createTime}}<br>{{ row.completeTime }}</p>
          </template>
        </el-table-column>

        <el-table-column label="商户订单号/平台流水号">
          <template v-slot="{row}">
            <p>{{ row.mchOrderNo }}<br>{{ row.platTrxNo }}</p>
          </template>
        </el-table-column>


        <el-table-column
          label="发放方式"
          prop="channelType">
          <template v-slot="{row}">
            {{ $dictCode('ChannelTypeEnum', row.channelType).desc }}
          </template>
        </el-table-column>
        <el-table-column label="代征主体" prop="mainstayName"></el-table-column>

        <el-table-column label="收款账户" prop="receiveAccountNo"></el-table-column>
        <el-table-column label="姓名" prop="receiveName"></el-table-column>
        <el-table-column label="身份证" prop="receiveIdCardNo"></el-table-column>

        <el-table-column
          label="收款账户"
          prop="receiveAccountNo"></el-table-column>
        <el-table-column
          label="姓名"
          prop="receiveName"></el-table-column>
        <el-table-column
          label="身份证"
          prop="receiveIdCardNo"></el-table-column>

        <el-table-column
          label="订单明细(总)金额"
          prop="orderItemAmount">
          <template v-slot="{row}">
            {{ row.orderItemAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          label="实发金额"
          prop="orderItemNetAmount">
          <template v-slot="{row}">
            {{ row.orderItemNetAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          label="服务费"
          prop="orderItemFee">
          <template v-slot="{row}">
            {{ row.orderItemFee | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          label="失败原因"
          prop="errorDesc"></el-table-column>

      </el-table>

      <el-footer class="pagination-container">
        <el-pagination
          v-if="response"
          :total="response.totalRecord"
          :current-page="pageCurrent"
          :page-size="pageSize"
          :page-sizes="[10, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"></el-pagination>
      </el-footer>
    </div>
    <export-record ref="exportRecord"></export-record>
  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import { getMainstayList } from '@/api/merchant'
import { getHangList, exportHangOrder } from '@/api/order'
export default {
  components: {
    ExportRecord,
  },
  data() {
    return {
      searchForm: { // 搜索表单
        platTrxNo: '',
        channelType: '',
        mchOrderNo: '',
        receiveName: '',
        receiveAccountNo: '',
        createBeginDate: '',
        createEndDate: '',
      },
      response: { // 查询结果
        data: [],
        totalRecord: 0,
      },
      pageSize: 10,
      pageCurrent: 1,

        mainstayList: [],

        createTimeRange: [],
      }
    },
    mounted() {
      this.getMainstayList();
      if (this.$route.query.mchBatchNo) {
        this.searchForm.mchBatchNo = this.$route.query.mchBatchNo;
        this.search()
      }
    },
    methods: {
      async search(initFlag) {
        if (initFlag === true) {
          this.pageCurrent = 1;
        }
        const {data} = await getHangList({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        });
        this.response = data;
      },
      clearField() {
        this.searchForm = {
          platTrxNo: '',
          channelType: '',
          mchOrderNo: '',
          receiveName: '',
          receiveAccountNo: '',
          createBeginDate: '',
          createEndDate: '',
        };
        this.$refs.startPicker.clearTime();
      },
      getTimeRange(type, val) {
        this.searchForm[type + 'BeginDate'] = val && val[0];
        this.searchForm[type + 'EndDate'] = val && val[1];
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.search(true);
      },
      handleCurrentChange(val) {
        this.pageCurrent = val;
        this.search();
      },
      async getMainstayList() {
        const {data} = await getMainstayList();
        this.mainstayList = data;
      },
      async exportOrderItem() {
        const { data } = await exportHangOrder(this.searchForm);
        this.$message.success(data);
      },
      getExportList() {
        this.$refs.exportRecord.isShow = true;
        this.$refs.exportRecord.getExportRecord(this.$dictFlag('ReportTypeEnum', 'TRADE_HANG_ORDER_MER').code);
      },
    },
  }
</script>

<style
  lang="scss"
  scoped>
  .box-container {
    padding: 20px 20px;
  }
</style>
