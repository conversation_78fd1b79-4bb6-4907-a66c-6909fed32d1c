<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">订单状态：</span>
            <el-select
              multiple
              collapse-tags
              clearable
              v-model="searchForm.orderItemStatusList">
              <template v-if="isOuter">
                <el-option
                  v-for="(item, index) in $dict('OfflineOrderItemStatusEnum')"
                  :key="index"
                  :label="searchForm.orderItemStatusList.length == 1 ? item.desc : item.desc.slice(0, 6)"
                  :value="item.code">
                  <span>{{ item.desc }}</span>
                </el-option>
              </template>
              <template v-else>
                <el-option
                  v-for="(item, index) in $dict('OrderItemStatusEnum')"
                  :key="index"
                  :label="searchForm.orderItemStatusList.length == 1 ? item.desc : item.desc.slice(0, 6)"
                  :value="item.code">
                  <span>{{ item.desc }}</span>
                </el-option>
              </template>
            </el-select>
          </div>
          <div
            class="flex-item"
            v-if="!isOuter">
            <span class="flex-item__label">发放方式：</span>
            <el-select
              clearable
              v-model="searchForm.channelType">
              <el-option
                v-for="(item, index) in $dict('ChannelTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code">
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户批次号：</span>
            <el-input v-model="searchForm.mchBatchNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">商户订单号：</span>
            <el-input v-model="searchForm.mchOrderNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">实发金额：</span>
            <el-input v-model="searchForm.orderItemNetAmountMin"></el-input>
            -
            <el-input v-model="searchForm.orderItemNetAmountMax"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户编号：</span>
            <el-input v-model="searchForm.employerNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">商户名称：</span>
            <el-input
              v-model="searchForm.employerNameLike"
              placeholder="模糊查询"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">姓名：</span>
            <el-input v-model="searchForm.receiveName"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">身份证：</span>
            <el-input v-model="searchForm.receiveIdCardNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">收款账户：</span>
            <el-input v-model="searchForm.receiveAccountNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              type="datetimerange"
              ref="startPicker"
              :start-time.sync="searchForm.createBeginDate"
              :end-time.sync="searchForm.createEndDate"
              :fast-time="fastTime"
              @change="getTimeRange('create', $event)">
            </date-picker>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">完成时间：</span>
            <date-picker
              type="datetimerange"
              ref="endPicker"
              :start-time.sync="searchForm.completeBeginDate"
              :end-time.sync="searchForm.completeEndDate"
              @change="getTimeRange('complete', $event)">
            </date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="search(true)">查询
            </el-button>
            <el-button @click="exportOrderList">导出</el-button>
            <el-button
              type="text"
              @click="getExportList">查看已导出列表
            </el-button>
            <el-button
              type="text"
              @click="clearField">清空搜索条件
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="func-container">
      <el-dropdown trigger="click">
        <el-button
          type="primary">
          点击筛选表格列
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-checkbox-group v-model="checkedColumns">
            <el-checkbox
              style="display: block; margin-left: 8px"
              v-for="(item, key) in tableColumns"
              :key="key"
              :label="key">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
          <div
            style="margin: 5px 5px 0"
            class="text-right">
            <el-button
              size="mini"
              @click="clearCheckedCol">
              清空
            </el-button>
            <el-button
              type="primary"
              size="mini"
              @click="checkAllCol">全选
            </el-button>
          </div>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button
        v-permission="'order:listOrderItemPage:sum'"
        type="text"
        @click="() => {showResult = !showResult}">
        统计查询结果
        <i
          class="el-icon-instance el-icon-arrow-down"
          :class="{'icon-up' : showResult}"></i>
      </el-button>
      <div v-if="showResult">
        <el-tag type="info">
          合计 {{ totalResult.totalNum || 0 }} 笔，实发金额汇总 {{ totalResult.totalNetAmount || 0 }} 元，代征主体服务费 {{ totalResult.totalFee || 0 }} 元，共计 {{ totalResult.totalOrderAmount || 0 }} 元。
        </el-tag>
      </div>
    </div>
    <div class="content-container">
      <el-table
        :data="response.data"
        fix>
        <template
          v-for="(col, key) in tableColumns">
          <filter-table-column
            v-if="checkedColumns.includes(key)"
            :key="key"
            :prop="col.prop"
            :width="col.width"
            :label="col.label"
            :render-fn="col.renderFn"
            v-bind="col"></filter-table-column>
        </template>
        <!--<el-table-column-->
        <!--  label="商户批次号/平台批次号"-->
        <!--  prop="mchBatchNo"-->
        <!--  width="170">-->
        <!--  <template v-slot="{row}">-->
        <!--    {{ row.mchBatchNo }} <br> {{ row.platBatchNo }}-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="创建时间/完成时间"-->
        <!--  width="170">-->
        <!--  <template v-slot="{row}">-->
        <!--    <p>{{ row.createTime }}<br>{{ row.completeTime }}</p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="商户订单号/平台流水号"-->
        <!--  width="170">-->
        <!--  <template v-slot="{row}">-->
        <!--    <p>{{ row.mchOrderNo }}<br>{{ row.platTrxNo }}</p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="用工企业"-->
        <!--  prop="employerName">-->
        <!--  <template v-slot="{row}">-->
        <!--    {{ row.employerNo }}-->
        <!--    <br>-->
        <!--    {{ row.employerName }}-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="代征主体"-->
        <!--  prop="mainstayName">-->
        <!--  <template v-slot="{row}">-->
        <!--    {{ row.mainstayNo }}-->
        <!--    <br>-->
        <!--    {{ row.mainstayName }}-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  v-if="!isOuter"-->
        <!--  label="发放方式"-->
        <!--  prop="channelType">-->
        <!--  <template v-slot="{row}">-->
        <!--    {{ $dictCode('ChannelTypeEnum', row.channelType).desc }}-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  v-if="!isOuter"-->
        <!--  label="支付通道"-->
        <!--  prop="channelName">-->
        <!--  <template v-slot="{row}">-->
        <!--    {{ row.channelNo }}<br>-->
        <!--    {{ row.channelName }}-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="收款账户"-->
        <!--  prop="receiveAccountNo"-->
        <!--  width="150">-->
        <!--  <template v-slot="{row}">-->
        <!--    <p>{{ row.receiveAccountNo }}<br />{{ row.bankName }}</p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column width="130">-->
        <!--  <template v-slot:header>-->
        <!--    姓名<br>身份证-->
        <!--  </template>-->
        <!--  <template v-slot="{row}">-->
        <!--    {{ row.receiveName }} <br>-->
        <!--    {{ row.receiveIdCardNo }}-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="手机号"-->
        <!--  prop="receivePhoneNo"-->
        <!--  width="130"></el-table-column>-->

        <!--<el-table-column-->
        <!--  label="订单明细（总）金额"-->
        <!--  prop="orderItemAmount"-->
        <!--  width="150"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <p class="text-right">-->
        <!--      {{ row.orderItemAmount | moneyFormat }}-->
        <!--    </p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="任务金额"-->
        <!--  prop="orderItemTaskAmount"-->
        <!--  width="100"-->
        <!--  align="right"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <p class="text-right">-->
        <!--      {{ row.orderItemTaskAmount | moneyFormat }}-->
        <!--    </p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="实发金额"-->
        <!--  prop="orderItemNetAmount"-->
        <!--  width="100"-->
        <!--  align="right"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <p class="text-right">-->
        <!--      {{ row.orderItemNetAmount | moneyFormat }}-->
        <!--    </p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="服务费"-->
        <!--  prop="orderItemFee"-->
        <!--  width="100"-->
        <!--  align="right"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <p class="text-right">-->
        <!--      {{ row.orderItemFee | moneyFormat }}-->
        <!--    </p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="个税"-->
        <!--  prop="orderItemTaxAmount"-->
        <!--  width="100"-->
        <!--  align="right"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <p class="text-right">-->
        <!--      {{ row.orderItemTaxAmount | moneyFormat }}-->
        <!--    </p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="备注"-->
        <!--  prop="remark"></el-table-column>-->
        <!--<el-table-column-->
        <!--  label="失败原因"-->
        <!--  prop="errorDesc"-->
        <!--  width="100"></el-table-column>-->

        <!--<el-table-column-->
        <!--  label="订单状态"-->
        <!--  prop="orderItemStatus"-->
        <!--  width="120"-->
        <!--  fixed="right">-->
        <!--  <template v-slot="{row}">-->
        <!--    <el-tooltip-->
        <!--      :content="$dictCode('OrderItemStatusEnum', row.orderItemStatus).desc"-->
        <!--      :disabled="$dictCode('OrderItemStatusEnum', row.orderItemStatus).desc.length < 5 ">-->
        <!--      <el-tag :type="getTagType(row.orderItemStatus)">-->
        <!--        {{ $dictCode('OrderItemStatusEnum', row.orderItemStatus).desc.slice(0, 5) }}-->
        <!--      </el-tag>-->
        <!--    </el-tooltip>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <el-table-column
          v-if="isOuter && checkedColumns.length > 0"
          label="操作"
          fixed="right"
          width="150">
          <template v-slot="{row}">
            <el-button
              v-if="row.workerBillFilePath"
              type="text"
              @click="downloadBillFile(row)">
              下载支付回单
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-footer class="pagination-container">
        <el-pagination
          v-if="response"
          :total="totalRecord"
          :current-page="pageCurrent"
          :page-size="pageSize"
          :page-sizes="[10, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <span
            @click="forceSearch"
            class="force-next-btn">下一页</span>
        </el-pagination>
      </el-footer>
      <export-record ref="exportRecord"></export-record>
    </div>
  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import {
  countOrderItem,
  exportOfflineOrderList,
  exportOrderList,
  getOrderDetail,
  offlineOrderCount,
  offlineOrderItemList,
  offlineOrderSum,
  sumOrderItem
} from '@/api/order'
import FilterTableColumn from "@/components/FilterTableColumn/index.vue";
import { formatMoney } from "@/filter/filter";

export default {
  components: {
    FilterTableColumn,
    ExportRecord
  },
  data(vm) {
    const tableColumns = [
      {
        label: '商户批次号/平台批次号',
        width: 170,
        prop: 'mchBatchNo',
        renderFn(h, row, col) {
          return h('div', [h('div', row.mchBatchNo), h('div', row.platBatchNo)])
        }
      }, {
        label: '创建时间/完成时间',
        width: 180,
        prop: 'createTime',
        renderFn(h, row, col) {
          return h('div', [h('div', row.createTime), h('div', row.completeTime)])
        }
      }, {
        label: '商户订单号/平台流水号',
        width: 180,
        prop: 'mchOrderNo',
        renderFn(h, row, col) {
          return h('div', [h('div', row.mchOrderNo), h('div', row.platTrxNo)])
        }
      }, {
        label: '用工企业',
        width: 100,
        prop: 'employerNo',
        renderFn(h, row, col) {
          return h('div', [h('div', row.employerNo), h('div', row.employerName)])
        }
      }, {
        label: '代征主体',
        width: 100,
        prop: 'mainstayName',
      }, {
        label: '发放方式',
        prop: 'channelType',
        renderFn(h, row, col) {
          if (!vm.isOuter) {
            return h('div', vm.$dictCode('ChannelTypeEnum', row.channelType).desc)
          } else {
            return '-'
          }
        }
      }, {
        label: '支付通道',
        width: 120,
        prop: 'channelNo',
        renderFn(h, row, col) {
          if (!vm.isOuter) {
            return h('div', [h('div', row.channelNo), h('div', row.channelName)])
          } else {
            return '-'
          }
        }
      }, {
        label: '收款账户',
        width: 150,
        prop: 'receiveAccountNo',
        renderFn(h, row, col) {
          return h('div', [h('div', row.receiveAccountNo), h('div', row.bankName)])
        }
      }, {
        label: '姓名/身份证',
        width: 150,
        prop: 'receiveName',
        renderFn(h, row, col) {
          return h('div', [h('div', row.receiveName), h('div', row.receiveIdCardNo)])
        }
      }, {
        label: '手机号',
        width: 150,
        prop: 'receivePhoneNo',
      }, {
        label: '订单明细（总）金额',
        width: 150,
        prop: 'orderItemAmount',
        renderFn(h, row) {
          return h('div', { class: 'text-right' }, [formatMoney(row.orderItemAmount)])
        }
      }, {
        label: '任务金额',
        width: 150,
        align: 'right',
        prop: 'orderItemTaskAmount',
        renderFn(h, row) {
          return h('div', { class: 'text-right' }, [formatMoney(row.orderItemTaskAmount)])
        }
      }, {
        label: '实发金额',
        width: 150,
        align: 'right',
        prop: 'orderItemNetAmount',
        renderFn(h, row) {
          return h('div', { class: 'text-right' }, [formatMoney(row.orderItemNetAmount)])
        }
      }, {
        label: '服务费',
        width: 150,
        prop: 'orderItemFee', align: 'right',
        renderFn(h, row) {
          return h('div', { class: 'text-right' }, [formatMoney(row.orderItemFee)])
        }
      }, {
        label: '个税',
        width: 150,
        prop: 'orderItemTaxAmount', align: 'right',
        renderFn(h, row) {
          return h('div', { class: 'text-right' }, [formatMoney(row.orderItemTaxAmount)])
        }
      }, {
        label: '备注',
        prop: 'remark',
      }, {
        label: '失败原因',
        prop: 'errorDesc',
      }, {
        label: '商户备忘录',
        prop: 'memo',
      }, {
        label: '订单状态',
        prop: 'orderItemStatus',
        width: 120,
        fixed: 'right',
        renderFn(h, row, col) {
          return h('el-tooltip', {
            props: {
              content: vm.$dictCode('OrderItemStatusEnum', row.orderItemStatus).desc,
              disabled: vm.$dictCode('OrderItemStatusEnum', row.orderItemStatus).desc.length < 5,
            },
          }, [h('el-tag', {
            attrs: { slot: 'default' },
            props: { type: vm.getTagType(row.orderItemStatus) }
          }, [vm.$dictCode('OrderItemStatusEnum', row.orderItemStatus).desc.slice(0, 5)])])
        }
      }
    ]
    return {
      searchForm: { // 搜索表单
        orderItemStatusList: [],
        channelType: '',
        mchBatchNo: '',
        mchOrderNo: '',
        receiveName: '',
        receiveIdCardNo: '',
        receiveAccountNo: '',
        createBeginDate: '',
        createEndDate: '',
        completeBeginDate: '',
        completeEndDate: '',
        orderItemNetAmountMin: '',
        orderItemNetAmountMax: '',
        employerNo: '',
        employerNameLike: '',
      },
      response: { // 查询结果
        data: [],
      },
      totalRecord: 0,
      pageSize: 10,
      pageCurrent: 1,

      mainstayList: [],

      createTimeRange: [],
      completeTimeRange: [],

      showResult: false,
      totalResult: {},
      fastTime: 'today',

      tableColumns,
      checkedColumns: []
    }
  },
  computed: {
    isOuter() {
      return this.$route.path === '/trade/outer/detail'
    }
  },
  created() {
    if (this.$route.query) {
      for (let p in this.$route.query) {
        this.searchForm[p] = this.$route.query[p];
      }
      if (this.searchForm.createBeginDate) {
        this.fastTime = '';
      }
    }
  },
  mounted() {
    if (sessionStorage.getItem('createTime')) {
      this.$refs.startPicker.changeTime([sessionStorage.getItem('createTime'), ''])
    }
    if (this.$route.query.platBatchNo) {
      this.searchForm.platBatchNo = this.$route.query.platBatchNo;
    }
    this.checkAllCol()
    this.$nextTick(() => {
      this.search(); // 如有时间范围改变要等下一帧
    })
  },
  destroyed() {
    sessionStorage.removeItem('createTime');
  },
  methods: {
    async search(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      const listApi = this.isOuter ? offlineOrderItemList : getOrderDetail
      const countApi = this.isOuter ? offlineOrderCount : countOrderItem
      const [{ data }, { data: { totalRecord } }] = await Promise.all([
        listApi({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        }),
        countApi(this.searchForm)
      ])
      this.response = data;
      this.totalRecord = totalRecord;

      this.hadPermission('order:listOrderItemPage:sum') && this.checkResult();
    },
    clearField() {
      this.searchForm = {
        orderItemStatusList: [],
        channelType: '',
        mchBatchNo: '',
        mchOrderNo: '',
        receiveName: '',
        receiveIdCardNo: '',
        receiveAccountNo: '',
        createBeginDate: '',
        createEndDate: '',
        completeBeginDate: '',
        completeEndDate: '',
        orderItemNetAmountMin: '',
        orderItemNetAmountMax: '',
        employerNo: '',
        employerNameLike: '',
      };
      this.$refs.startPicker.clearTime();
      this.$refs.endPicker.clearTime();
    },
    getTimeRange(type, val) {
      this.searchForm[type + 'BeginDate'] = val && val[0];
      this.searchForm[type + 'EndDate'] = val && val[1];
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    async exportOrderList() {
      const api = this.isOuter ? exportOfflineOrderList : exportOrderList
      const { data } = await api(this.searchForm);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      if (this.isOuter) {
        this.$refs.exportRecord.getExportRecord(54);
      } else {
        this.$refs.exportRecord.getExportRecord(this.$dictFlag('ReportTypeEnum', 'TRADE_ORDER_ITEM_SUP').code);
      }
    },
    forceSearch() {
      this.pageCurrent++;
      this.search();
    },
    getTagType(status) {
      switch (Number(status)) {
        case 100:
          return 'success';
        case 200:
        case 600:
        case 700:
          return 'danger';
        case 400:
        case 800:
          return 'info';
        default:
          return '';
      }
    },
    async checkResult() {
      const sumApi = this.isOuter ? offlineOrderSum : sumOrderItem
      const { data } = await sumApi(this.searchForm)
      this.$set(this, 'totalResult', data);
    },
    downloadBillFile(row) {
      this.downloadFile(this.fileUrl + row.workerBillFilePath, '支付回单')
    },
    clearCheckedCol() {
      this.checkedColumns = []
    },
    checkAllCol() {
      this.checkedColumns = this.tableColumns.map((_, index) => index)
    }
  },
}
</script>

<style
  lang="scss"
  scoped>
.box-container {
  .func-container {
    padding-top: 16px;
    padding-left: 16px
  }

  .el-icon-instance {
    transition: transform .2s linear;
  }

  .icon-up {
    transform: rotate(180deg);
  }
}
</style>
