<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div
            class="flex-item"
            v-if="!isOuter">
            <span class="flex-item__label">发放方式：</span>
            <el-select
              clearable
              v-model="searchForm.channelType"
            >
              <el-option
                v-for="(item, index) in $dict('ChannelTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">平台批次号：</span>
            <el-input v-model="searchForm.platBatchNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户编号：</span>
            <el-input v-model="searchForm.employerNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">商户名称：</span>
            <el-input
              v-model="searchForm.employerNameLike"
              placeholder="模糊查询"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">岗位类别：</span>
            <el-cascader
              filterable
              clearable
              data-index="index"
              v-model="searchForm.workCategoryCode"
              :options="workCategoryOptions"
              :props="{ expandTrigger: 'hover', 'emitPath': false, 'value': 'workCategoryCode', 'label': 'workCategoryName', 'leaf': 'workCategoryCode' }"
            >
              <template v-slot="{data}">
                <el-tooltip
                  :content="data.workCategoryName"
                  :disabled="data.workCategoryName.length < 14"
                >
                  <span class="cascader-content-item">
                    {{ data.workCategoryName }}
                  </span>
                </el-tooltip>
              </template>
            </el-cascader>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              type="datetimerange"
              ref="startPicker"
              v-model="createTimeRange"
              fast-time="today"
              @change="getTimeRange('create', $event)"
            >
            </date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="search(true)"
            >查询
            </el-button>
            <el-button
              type="text"
              @click="clearField"
            >清空搜索条件
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-radio-group
        v-model="searchForm.batchStatus"
        @change="statusChange"
      >
        <el-radio-button label="">全部</el-radio-button>
        <template v-if="isOuter">
          <el-radio-button
            v-for="(item, index) in $dict('OfflineOrderStatusEnum')"
            :key="index"
            :label="item.code"
          >
            {{ item.desc }}
          </el-radio-button>
        </template>
        <template v-else>
          <el-radio-button
            v-for="(item, index) in $dict('OrderStatusEnum')"
            :key="index"
            :label="item.code"
          >
            {{ item.desc }}
          </el-radio-button>
        </template>
      </el-radio-group>

      <el-table :data="list">
        <el-table-column
          type="index"
          :index="getIndex"
          label="序号"
        />
        <el-table-column
          label="创建时间"
          prop="createTime"
          width="180"
        ></el-table-column>
        <el-table-column
          label="完成时间"
          prop="completeTime"
          width="180"
        ></el-table-column>
        <el-table-column
          label="平台批次号"
          prop="platBatchNo"
          width="180"
        ></el-table-column>
        <el-table-column
          label="商户编号/商户名称"
          width="150"
        >
          <template v-slot="{row}">
            {{ row.employerNo }} <br> {{ row.employerName }}
          </template>
        </el-table-column>
        <el-table-column
          label="代征主体"
          prop="mainstayName"
        ></el-table-column>
        <el-table-column
          label="岗位类别"
          prop="workCategoryName"
          width="100"></el-table-column>
        <el-table-column
          label="发放方式"
          prop="channelType"
        >
          <template
            v-slot="{row}"
            v-if="!isOuter">
            {{ $dictCode('ChannelTypeEnum', row.channelType).desc }}
          </template>
          <div v-else>线下发放</div>
        </el-table-column>

        <el-table-column
          label="发放模式"
          prop="launchWay"
        >
          <template v-slot="{row}">
            {{ $dictCode('LaunchWayEnum', row.launchWay).desc }}
          </template>
        </el-table-column>

        <el-table-column
          label="已创建总数"
          prop="requestCount"
          width="150"
        >
          <template v-slot="{row}">
            <p>笔数：{{ row.requestCount }}</p>
            <p>金额：{{ row.requestTaskAmount | moneyFormat }}</p>
          </template>
        </el-table-column>
        <el-table-column
          label="已受理总数"
          prop="acceptedCount"
          width="150"
        >
          <template v-slot="{row}">
            <p>笔数：{{ row.acceptedCount }}</p>
            <p>金额：{{ row.acceptedTaskAmount | moneyFormat }}</p>
          </template>
        </el-table-column>
        <el-table-column
          label="预算代征主体服务费"
          prop="acceptedFee"
          width="150"
        >
          <template v-slot="{row}">
            <p>金额：{{ row.acceptedFee | moneyFormat }}</p>
          </template>
        </el-table-column>

        <el-table-column
          label="订单金额"
          width="150"
        >
          <template v-slot="{row}">
            {{ row.acceptedOrderAmount | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column
          label="批次状态"
          prop="batchStatus"
          width="120"
        >
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.batchStatus)">
              {{ $dictCode('OrderStatusEnum', row.batchStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          fixed="right"
        >
          <template v-slot="{row}">
            <el-button
              v-if="row.batchStatus == 101
                || row.batchStatus == 100"
              @click="goDetail(row)"
              type="text"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

    </div>

    <el-footer class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :page-sizes="[10, 50]"
        :total="totalRecord"
        @size-change="handleSizeChange"
        @current-change="handleCurrnetChange"
      >
        <span
          @click="forceSearch"
          class="force-next-btn"
        >下一页</span>
      </el-pagination>
    </el-footer>
  </div>
</template>

<script>
import { getOrderList, countOrder, offlineOrderList } from '@/api/order'
import { convert } from '@/utils'
import { listAllWorkCategory } from '@/api/business'

export default {
  data() {
    return {
      searchForm: { // 搜索表单
        platBatchNo: '',
        employerNo: '',
        employerNameLike: '',
        channelType: '',
        createBeginDate: '',
        createEndDate: '',
        batchStatus: '',
      },
      list: [],
      totalRecord: 0,
      pageSize: 10,
      pageCurrent: 1,

      createTimeRange: [],
      completeTimeRange: [],
      workCategoryList: [],
      workCategoryOptions: [],

    }
  },
  computed: {
    isOuter() {
      return this.$route.path === '/trade/outer/roster'
    }
  },
  mounted() {
    this.search();
    this.getListAllWorkCategory();

  },
  methods: {
    clearField() {
      this.searchForm = {
        batchStatus: '',
      }
      this.$refs.startPicker.clearTime();
    },
    getTimeRange(type, val) {
      this.searchForm[type + 'BeginDate'] = val && val[0];
      this.searchForm[type + 'EndDate'] = val && val[1];
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    handleCurrnetChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    async search(initFlag) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      if (this.isOuter) {
        const { data: { data, totalRecord } } = await offlineOrderList({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent,
        })
        this.list = data
        this.totalRecord = totalRecord
      } else {
        const [{ data }, { data: { totalRecord } }] = await Promise.all([
          getOrderList({
            ...this.searchForm,
            pageSize: this.pageSize,
            pageCurrent: this.pageCurrent,
          }),
          countOrder(this.searchForm)
        ])
        this.list = data.data;
        this.totalRecord = totalRecord;
      }
    },
    goDetail(data) {
      sessionStorage.setItem('createTime', data.createTime);
      const path = this.isOuter ? '/trade/outer/detail' : '/trade/order/detail'
      this.$router.push({
        path,
        query: {
          platBatchNo: data.platBatchNo,
        }
      })
    },
    goDeliver(data) {
      sessionStorage.setItem('deliverInfo', JSON.stringify(data))
      this.$router.push({
        path: '/trade/order/deliver',
        query: {
          platBatchNo: data.platBatchNo,
        }
      })
    },
    statusChange(val) {
      this.searchForm.batchStatus = val;
      this.search(true);
    },
    forceSearch() {
      this.pageCurrent++;
      this.search();
    },
    getTagType(status) {
      switch (Number(status)) {
        case 100:
          return 'success';
        case 101:
        case 105:
          return 'danger';
        case 103:
          return 'info';
        default:
          return '';
      }
    },
    // 获取工作类目
    async getListAllWorkCategory() {
      const { data } = await listAllWorkCategory()
      this.workCategoryList = data;
      this.workCategoryOptions = convert(data, 0);
    },

  }
}
</script>

<style
  lang="scss"
  scoped>
.box-container {
  padding: 20px 20px;
}
</style>
