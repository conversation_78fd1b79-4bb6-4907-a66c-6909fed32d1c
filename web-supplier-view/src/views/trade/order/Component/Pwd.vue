<template>
  <el-dialog
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="close"
    width="500px">
    <div slot="title">确认发放</div>
    <el-form ref="form" :rules="rules" :model="form">
      <p class="confirm-tip">确认发放将向收款人发起打款，请谨慎操作</p>
      <el-form-item prop="pwd">
        <el-input type="password" v-model="form.pwd"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button type="primary" @click="confirm">确认发放</el-button>
      <el-button @click="close">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    visible: Boolean
  },
  data() {
    return {
      form: {
        pwd: '',
      },
      rules: {
        pwd: [
          { required: true, message: '请输入支付密码', validate: 'blur' }
        ]
      }
    }
  },
  methods: {
    close() {
      this.form.pwd = '';
      this.$emit('update:visible', false);
    },
    confirm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit('confirm', this.form.pwd);
          this.close();
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .el-form {
    width: 400px;
    .confirm-tip {
      color: #f00;
    }
  }
</style>