<template>
  <div class="page-container">
    <div class="header-container">
      <el-steps :active="activeStep" align-center>
        <el-step title="审核信息"></el-step>
        <el-step title="确认发放"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>
    <div class="content-container">
      <div v-show="activeStep == 1">
        <div class="batch-info-container">
          <div class="clearfix">
            {{ responseData.batchName }} | {{ $dictCode('OrderStatusEnum', responseData.batchStatus).desc }} | 合计{{ responseData.requestCount }}笔/{{ responseData.requestNetAmount | moneyFormat }}
            <div class="batch-button-group">
              <el-button type="text" @click="cancel">取消发放</el-button>
              <el-button type="text" @click="getExportList">查看已导出列表</el-button>
              <el-button @click="exportOrderItem">导出</el-button>
              <el-button type="primary" @click="nextStep">下一步</el-button>
            </div>
          </div>
          <div class="batch-info-graph">
            <div class="batch-graph success-graph">
              <p class="batch-graph-title">已受理订单（笔|元）</p>
              <p>{{responseData.acceptedCount}} | {{responseData.acceptedNetAmount}}</p>
            </div>
            <div class="batch-graph fail-graph">
              <p class="batch-graph-title">失败订单（笔|元）</p>
              <p>{{responseData.failCount}} | {{responseData.failNetAmount}}</p>
            </div>
            <div class="batch-graph hang-graph">
              <p class="batch-graph-title">挂单订单（笔|元）</p>
              <p>0 | 0</p>
            </div>
          </div>
        </div>

        <div class="batch-table-container">
          <el-table
            :data="responseTable"
            @filter-change="filterChange">
            <el-table-column label="序号" type="index" width="50" :index="getIndex"></el-table-column>
            <el-table-column label="商户订单号" prop="mchOrderNo"></el-table-column>
            <el-table-column label="平台流水号" prop="platTrxNo"></el-table-column>
            <el-table-column label="收款账户" prop="receiveAccountNo"></el-table-column>
            <el-table-column label="姓名" prop="receiveName"></el-table-column>
            <el-table-column label="身份证号" prop="receiveIdCardNo"></el-table-column>
            <el-table-column label="手机号" prop="receivePhoneNo"></el-table-column>

            <el-table-column label="实发金额" prop="orderItemNetAmount">
              <template v-slot="{row}">
                {{ row.orderItemNetAmount | moneyFormat }}
              </template>
            </el-table-column>

            <el-table-column
              label="状态"
              :filters="statusFilters"
              :filter-method="filterHandler"
              column-key="status-column">
              <template v-slot="{row}">
                {{ $dictCode('OrderItemStatusEnum', row.orderItemStatus).desc }}
              </template>
            </el-table-column>

            <el-table-column label="错误提示" prop="errorDesc"></el-table-column>

          </el-table>
        </div>
      </div>

      <div v-show="activeStep == 2">
        <div class="clearfix">
          {{ responseData.batchName }} | {{ $dictCode('OrderStatusEnum', responseData.batchStatus).desc }} | 合计{{ responseData.acceptedCount }}笔/{{ responseData.acceptedNetAmount | moneyFormat }}
          <div class="batch-button-group">
            <el-button type="text" @click="exportOrderItem">导出</el-button>
            <el-button type="text" @click="cancel">取消发放</el-button>
            <el-button type="primary" @click="nextStep">确认发放</el-button>
          </div>
        </div>

        <warning-block>
          <span class="warning-title">温馨提示</span>
          <p>合计{{ responseData.acceptedCount }}笔，实发金额汇总{{ responseData.acceptedNetAmount }}元，代征主体服务费{{ responseData.acceptedFee }}元，共计{{ responseData.acceptedOrderAmount }}元</p>
          <p>账户余额{{ amount }}元</p>
        </warning-block>

        <div class="batch-table-container">
          <el-table :data="responseTable">
            <el-table-column label="序号" type="index" width="50" :index="getIndex"></el-table-column>
            <el-table-column label="商户订单号" prop="mchOrderNo"></el-table-column>
            <el-table-column label="平台流水号" prop="platTrxNo"></el-table-column>
            <el-table-column label="收款账户" prop="receiveAccountNo"></el-table-column>
            <el-table-column label="姓名" prop="receiveName"></el-table-column>
            <el-table-column label="身份证号" prop="receiveIdCardNo"></el-table-column>
            <el-table-column label="手机号" prop="receivePhoneNo"></el-table-column>

            <el-table-column label="实发金额" prop="orderItemNetAmount">
              <template v-slot="{row}">
                {{ row.orderItemNetAmount | moneyFormat }}
              </template>
            </el-table-column>

            <el-table-column label="代征主体服务费" prop="orderItemFee">
              <template v-slot="{row}">
                {{ row.orderItemFee | moneyFormat }}
              </template>
            </el-table-column>

            <el-table-column label="订单金额" prop="orderItemAmount">
              <template v-slot="{row}">
                {{ row.orderItemAmount | moneyFormat }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <el-main v-if="activeStep == 3">
        <div class="result-container">
          <img src="@/assets/success.png" alt="">
          <p class="result-title">确认发放成功</p>
          <p class="result-slogan">发放名单正在进行打款，可返回发放名单查看进度</p>
          <div class="result-detail clearfix">
            <p class="result-detail-title">批次详情</p>
            <div class="result-detail-left">
              <p>批次号：{{ platBatchNo }}</p>
              <p>已受理笔数：{{ responseData.acceptedCount }}笔</p>
              <p>实发金额汇总：{{ responseData.acceptedNetAmount }}（元）</p>
            </div>
            <div class="result-detail-right">
              <p>创建时间：{{ responseData.createTime }}</p>
              <p>请求笔数：{{ responseData.requestCount }}笔</p>
              <p>服务费：{{ responseData.acceptedFee }}（元）</p>
            </div>
          </div>
          <div class="result-button">
            <el-button type="primary" @click="goRoster">返回发放名单</el-button>
          </div>
        </div>
      </el-main>


      <div class="pagination-container" v-if="activeStep < 3">
        <el-pagination
          v-if="responseData"
          :total="totalRecord"
          :current-page="pageCurrent"
          :page-size="pageSize"
          :page-sizes="[10, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"></el-pagination>
      </div>
    </div>
    <export-record ref="exportRecord"></export-record>
    <pwd :visible.sync="visible" @confirm="confirm"/>
  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import { getOrderByBatchNo, getDeliverList, cancelOrderGrant, confirmOrderGrant, getOrderListByStatus, exportOrderItem, getAmount } from '@/api/order'
import { getKey } from '@/utils/publicKey'
import { encryptParam } from '@/utils/jsencrypt'
import Pwd from './Component/Pwd'
export default {
  components: {
    ExportRecord,
    Pwd
  },
  data() {
    return {
      activeStep: 1,

      platBatchNo: '',

      responseData: {
        platBatchNo: '',
        batchName: '',
        batchStatus: '',
        requestCount: '',
        requestNetAmount: '',
        acceptedCount: '',
        acceptedNetAmount: '',
        acceptedFee: '',
        failCount: '',
        failNetAmount: '',
        hangCount: '',
        hangNetAmount: '',
        createTime: '',
        confirmTime: '',
        orderItemStatus: ''
      },
      responseTable: [],

      pageCurrent: 1,
      pageSize: 10,
      totalRecord: 0,

      filterStatusCode: '',

      visible: false,

      deliverInfo: {},
      amount: 0, // 余额
    }
  },
  computed: {
    statusFilters() {
      let dict = this.$dict('OrderItemStatusEnum');
      let list = [];
      for (let i of dict) {
        list.push({
          text: i.desc,
          value: i.code
        })
      }
      return list;
    }
  },
  mounted() {
    this.platBatchNo = this.$route.query.platBatchNo || '';
    this.getOrderByBatchNo();
    this.getDeliverList();

    if (sessionStorage.getItem('deliverInfo')) {
      const data = JSON.parse(sessionStorage.getItem('deliverInfo'));
      this.deliverInfo = {
        mainstayNo: data.mainstayNo,
        payChannelNo: data.payChannelNo,
        channelType: data.channelType,
      }
      this.getAmount();
    }
  },
  destroyed() {
    sessionStorage.removeItem('deliverInfo');
  },
  methods: {
    async getOrderByBatchNo() {
      const { data } = await getOrderByBatchNo({
        platBatchNo: this.platBatchNo
      });
      this.responseData = Object.assign(this.responseData, data);
    },
    async getDeliverList(initFlag) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      let form = {
        platBatchNo: this.platBatchNo,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      }
      const { data } = await getDeliverList(form);
      this.responseTable = data.data;
      this.totalRecord = data.totalRecord || 0;
    },
    async getOrderList(initFlag, code) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      let form = {
        platBatchNo: this.platBatchNo,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      }
      if (code) {
        form.orderItemStatus = code;
      }

      if (this.activeStep == 2) {
        form.orderItemStatus = this.$dictDesc('OrderItemStatusEnum', '已受理').code;
      }
      const { data } = await getOrderListByStatus(form);
      this.responseTable = data.data;
      this.totalRecord = data.totalRecord || 0;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getDeliverList(true);
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.getDeliverList();
    },
    async cancel() {
      const value = await this.$confirm('此操作将取消当前批次的发放，是否继续', '提示',{
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      const { data } = await cancelOrderGrant({
        platBatchNo: this.platBatchNo,
      })
      this.$message.success('取消成功')
      this.$router.push('/trade/order/roster')
    },
    async nextStep() {
      if (this.activeStep == 2) {
        this.visible = true
      } else {
        this.activeStep++;
        this.getOrderList(true);
      }
    },
    goRoster() {
      this.$router.push('/trade/order/roster');
    },
    filterHandler(val, row) {
      return val == row['orderItemStatus'];
    },
    filterChange(val) {
      let list = val['status-column'];
      if (list.length > 1) {
        return this.$message('目前仅支持筛选一项');
      }
      this.filterStatusCode = list[0];
      this.getOrderList(true, list[0]);
    },
    async exportOrderItem() {
      let form = {
        platBatchNo: this.platBatchNo,
      }
      if (this.activeStep == 1) {
        if (this.filterStatusCode) {
          form.orderItemStatus = this.filterStatusCode
        };
      } else if (this.activeStep == 2) {
        form.orderItemStatus = this.$dictDesc('OrderItemStatusEnum', '已受理').code
      }
      const { data } = await exportOrderItem(form);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(this.$dictFlag('ReportTypeEnum', 'TRADE_ORDER_ITEM_MER').code);
    },
    async confirm(pwd) {
      const { data } = await confirmOrderGrant({
        platBatchNo: this.platBatchNo,
        tradePwd: encryptParam({
          publicKey: getKey(),
          pwd,
        })
      })
      this.$message.success(data);
      this.activeStep++;
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    async getAmount() {
      const { data } = await getAmount(this.deliverInfo);
      this.amount = data.amount || '';
    }
  }
}
</script>

<style lang="scss" scoped>
  .page-container {
    .header-container {
      margin-bottom: 40px;
      padding: 20px 0;
      background: #fff;
    }

    .content-container {
      padding: 20px;
      background: #fff;
    }

    .batch-button-group {
      float: right;
    }

    .batch-info-graph {
      display: flex;
      margin-top: 24px;
    }
    .batch-graph {
      flex: 1;
      padding: 24px;
    }
    .batch-graph-title {
      margin-bottom: 16px;
      color: #000
    }
    .success-graph {
      border: 1px solid #eee;
      border-top: 2px solid $mainColor;
      color: $mainColor;
    }
    .fail-graph {
      border: 1px solid #eee;
      border-top: 2px solid $errorColor;
      color: $errorColor;
    }
    .hang-graph {
      border: 1px solid #eee;
      border-top: 2px solid $warningColor;
      color: $warningColor;
    }

    .result-container {
      text-align: center;
    }
    .result-title {
      font-size: 30px;
      font-weight: 600;
    }
    .result-detail-title {
      font-weight: 600;
    }
    .result-slogan {
      color: #ccc
    }
    .result-detail {
      width: 500px;
      margin: 40px auto;
      padding: 20px;
      text-align: left;
    }
    .result-detail-left {
      float: left;
    }
    .result-detail-right {
      float: right;
    }

    .el-table {
      margin-top: 20px;
    }
  }
</style>