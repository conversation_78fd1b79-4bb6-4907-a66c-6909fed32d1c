<template>
  <div class="box-container">
    <warning-block>温馨提示：订单完成的次日 09:00 后可查，特殊情况可能会稍有延迟。微信通道不支持下载企业转账回单。</warning-block>

    <div class="search-container flex-container">
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">商户批次号：</span>
          <el-input v-model="searchForm.mchBatchNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">商户订单号：</span>
          <el-input v-model="searchForm.mchOrderNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">平台流水号：</span>
          <el-input v-model="searchForm.platTrxNo"></el-input>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">姓名：</span>
          <el-input v-model="searchForm.receiveName"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">发放方式：</span>
          <el-select clearable v-model="searchForm.channelType">
            <el-option
              v-for="(item, index) in $dict('ChannelTypeEnum')"
              :key="index"
              :label="item.desc"
              :value="item.code">
            </el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">收款账户：</span>
          <el-input v-model="searchForm.receiveAccountNo"></el-input>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">企业编号：</span>
          <el-input v-model="searchForm.employerNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">企业名称：</span>
          <el-input v-model="searchForm.employerNameLike" placeholder="模糊查询"></el-input>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            type="datetime"
            ref="datePicker1"
            picker="separate"
            fast-time="yesterday"
            :fast-time-arr="['yesterday', 'sevenDay', 'thirtyDay', 'month', 'lastMonth']"
            :timeDisable="-1"
            :start-time.sync="searchForm.createBeginDate"
            :end-time.sync="searchForm.createEndDate"
          ></date-picker>
        </div>
      </div>  <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">完成时间：</span>
          <date-picker
            type="datetime"
            ref="datePicker2"
            picker="separate"
            fast-time="yesterday"
            :fast-time-arr="['yesterday', 'sevenDay', 'thirtyDay', 'month', 'lastMonth']"
            :timeDisable="-1"
            :start-time.sync="searchForm.completeBeginDate"
            :end-time.sync="searchForm.completeEndDate"
          ></date-picker>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="search-btn-group">
          <el-button type="primary" @click="search(true)">查询</el-button>
          <el-button @click="exportList">批量下载</el-button>
          <el-button @click="exportExcel">导出</el-button>
          <el-button type="text" @click="getExportList">查看下载列表</el-button>
          <el-button type="text" @click="clearField">清空筛选条件</el-button>
        </div>
      </div>
    </div>
    <el-tabs v-model="activeType">
      <el-tab-pane
        v-for="item in $dict('ReceiptOrderEnum')"
        :key="item.code"
        :name="item.code"
        :label="item.desc"
      ></el-tab-pane>
    </el-tabs>

    <div class="content-container">
      <el-table :data="response.data" v-if="activeType === '100'" key="100">
        <el-table-column
          label="平台流水号/通道流水号"
          prop="platTrxNo"
          width="170"
        >
          <template
            v-slot="{row}">
            {{ row.platTrxNo }} <br> {{ row.channelTrxNo }}
          </template>
        </el-table-column>

        <el-table-column label="创建时间/完成时间" width="160">
          <template v-slot="{row}">
            <p>{{ row.createTime}}<br>{{ row.completeTime }}</p>
          </template>
        </el-table-column>

        <el-table-column label="商户批次号" width="150" prop="mchBatchNo"></el-table-column>
        <el-table-column label="商户订单号" prop="mchOrderNo" width="150"></el-table-column>

        <el-table-column label="用工企业" width="130">
          <template v-slot="{row}">
            {{ row.employerNo }}
            <br>
            {{ row.employerName }}
          </template>
        </el-table-column>

        <el-table-column label="支付通道" prop="channelName"></el-table-column>
        <!--<el-table-column-->
        <!--  label="付款方"-->
        <!--  prop="employerName"-->
        <!--  width="150"></el-table-column>-->

        <el-table-column label="发放方式">
          <template v-slot="{row}">
            {{ $dictCode('ChannelTypeEnum', row.channelType).desc }}
          </template>
        </el-table-column>

        <el-table-column label="收款账户" width="150">
          <template v-slot="{row}">
            <p v-if="row.channelType == 1">
              {{ row.receiveAccountNo }} <br> {{ row.bankName }}
            </p>
            <p v-else>
              {{ row.receiveAccountNo }} <br> {{ $dictCode('ChannelTypeEnum', row.channelType).desc }}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="姓名" prop="receiveName"></el-table-column>
        <el-table-column label="实发金额">
          <template v-slot="{row}">
            {{ row.orderItemNetAmount | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right">
          <template v-slot="{row}">
            <el-button type="text" @click="downLoad(row)">下载回单</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-table :data="response.data" v-else-if="activeType === '101'" key="101">
        <el-table-column
          label="平台流水号"
          prop="platTrxNo"
          width="130"></el-table-column>
        <el-table-column
          label="创建时间/完成时间"
          width="160">
          <template v-slot="{row}">
            <div>{{ row.createTime }}</div>
            <div>{{ row.completeTime }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="商户批次号"
          prop="mchBatchNo"
          width="150"></el-table-column>
        <el-table-column
          label="商户订单号"
          prop="mchOrderNo"
          width="150"></el-table-column>
        <el-table-column
          label="支付通道"
          prop="channelName"></el-table-column>
        <el-table-column
          label="收款方"
          prop="mainstayName"
          width="150"></el-table-column>
        <el-table-column
          label="付款方"
          prop="employerName"></el-table-column>
        <el-table-column
          label="交易金额（含服务费）"
          prop="orderItemAmount"
          width="180">
          <template v-slot="{row}">
            <p class="text-right">{{ row.orderItemAmount | moneyFormat }}</p>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template v-slot="{row}">
            <el-button
              @click="downLoad(row)"
              type="text"
              v-if="!(row.channelNo === 'WXPAY' || row.productNo === 'CKH')">下载回单
            </el-button>
          </template>
        </el-table-column>
      </el-table>

    </div>

    <el-footer class="pagination-container">
      <el-pagination
        v-if="response"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-size.sync="pageSize"
        :page-sizes="[10, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()">
      </el-pagination>
    </el-footer>

    <export-record ref="exportRecord"></export-record>
  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import { getOrderDetail, batchDownloadCertificateFile, countOrderItem, exportExcel } from '@/api/order'
import { getToken } from '@/utils/loginToken';

export default {
  components: {
    ExportRecord,
  },
  data(vm) {
    return {
      searchForm: {
        mchBatchNo: '',
        mchOrderNo: '',
        receiveName: '',
        platTrxNo: '',
        channelType: '',
        receiveAccountNo: '',
        createBeginDate: '',
        createEndDate: '',
        employerNo: '',
        employerNameLike: '',
      },
      pageSize: 10,
      pageCurrent: 1,

      response: {
        data: []
      },
      totalRecord: 0,

      activeType: vm.$dict('ReceiptOrderEnum')[0].code,
    }
  },

  mounted() {
    this.search()
  },

  methods: {
    async search(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      const [{ data }, { data: {totalRecord}}] = await Promise.all([
        getOrderDetail({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent,
          orderItemStatus: 100, // 已成功的状态
        }),
        countOrderItem({ ...this.searchForm, orderItemStatus: 100 })
      ])
      this.response = data;
      this.totalRecord = totalRecord;
    },
    getTimeRange(val) {
      this.searchForm.createBeginDate = val && val[0];
      this.searchForm.createEndDate = val && val[1];
    },
    clearField() {
      this.searchForm = {
        mchBatchNo: '',
        mchOrderNo: '',
        receiveName: '',
        platTrxNo: '',
        channelType: '',
        receiveAccountNo: '',
        createBeginDate: '',
        createEndDate: '',
        employerNo: '',
        employerNameLike: '',
      };
      this.$refs.datePicker1.resetTime();
      this.$refs.datePicker2.resetTime();
    },
    downLoad(row) {
      window.open(this.baseUrl + `/download/downloadCertificateFile?platTrxNo=${row.platTrxNo}&x-token=${getToken()}&type=${this.activeType}`)
    },
    async exportList() {
      const { data } = await batchDownloadCertificateFile(this.searchForm, this.activeType);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(this.activeType === '100' ? '51' : '52');
    },
    async exportExcel() {
      const {data} = await exportExcel(this.searchForm, this.activeType);
      this.$message.success(data);
    },
  }
}
</script>

<style>

</style>
