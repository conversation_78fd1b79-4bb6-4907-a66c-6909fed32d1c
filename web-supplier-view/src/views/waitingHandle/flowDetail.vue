<template>
  <div class="page-container">
    <div class="flex-container">
      <h2>{{ processInfo.id }}</h2>
      <div class="flex-wrapper">
        <div class="flex-item">
          <span class="flex-label">发起人：</span>
          <span class="flex-item__content">{{processInfo.initiatorName}}</span>
        </div>
        <div class="flex-item">
          <span class="flex-label">流程主题：</span>
          <span class="flex-item__content">
            {{ processInfo.flowTopicName}}
          </span>
        </div>
      </div>
      <div class="flex-wrapper">
        <div class="flex-item">
          <span class="flex-label">创建时间：</span>
          <span class="flex-item__content">
            {{processInfo.createTime}}
          </span>
        </div>
        <div class="flex-item">
          <span class="flex-label">最后跟进时间：</span>
          <span class="flex-item__content">
            {{processInfo.updateTime}}
          </span>
        </div>
      </div>
      <div class="flex-wrapper">
        <div class="flex-item flex-item-main">
          <span class="flex-label">流程备注：</span>
          <span class="flex-item__content">
            {{processInfo.remark}}
          </span>
        </div>
      </div>
    </div>
    <!--入网-->
    <div
      class="box-container"
      v-if="processType == 'MCH_CREATE' || processType == 'MAIN_AUTH'"
    >
      <el-button
        class="tabs-edit-btn"
        v-if="isEdit"
        type="text"
        @click="editMainInfo"
      >编辑</el-button>
      <el-tabs
        class="tabs-container"
        v-model="activeName"
      >
        <el-tab-pane
          label="合作信息表"
          name="first"
          v-if="processType == 'MCH_CREATE'"
        >
          <div class="flex-container">
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">商户编号：</span>
                {{ form.mchNo }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">商户名称：</span>
                {{ form.mchName }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">企业行业类别：</span>
                {{ form.industryTypeName }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">预计用工人数：</span>
                {{ form.workerNum }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">预期可实现C端签约率区间：</span>
                {{ $dictCode('SignRateLevelEnum', form.signRateLevel).desc }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">自由职业者单人月经营所得9.7万以下占比：</span>
                {{ form.workerMonthIncomeRate }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">月流水预估：</span>
                {{ form.monthMoneySlip }}万元
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">是否可提供服务记录及经营所得计算明细：</span>
                {{ $dictCode('ProvideIncomeDetailTypeEnum', form.signRateLevel).desc }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">公司自有业务平台名称：</span>
                {{ form.bizPlatformName }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item flex-vertical-main">
                <span class="flex-vertical-label">公司网站：</span>
                {{ form.companyWebsite }}
              </div>
            </div>

            <div
              class="flex-wrapper"
              v-for="(item, index) in form.positionVoList"
              :key="index"
            >
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">自由职业者的工作场所：</span>
                {{ $dictCode('WorkPlaceEnum', item.workplaceCode).desc }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">自由职业者服务类型：</span>
                {{ item.workCategoryName }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">建议发票类目：</span>
                <template v-for="(cate, key) in item.invoiceCategoryList">
                  <el-tooltip
                    :content="cate.invoiceCategoryName"
                    :disabled="cate.invoiceCategoryName.length < 9"
                    :key="key"
                  >
                    <el-tag type="info">
                      {{ cate.invoiceCategoryName.length >= 9 ? cate.invoiceCategoryName.slice(0, 9) + '...' : cate.invoiceCategoryName }}
                    </el-tag>
                  </el-tooltip>
                </template>
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">企业从事业务：</span>
                {{ item.businessDesc }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">自由职业者服务描述：</span>
                {{ item.serviceDesc }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">自由职业者服务所得计算规则：</span>
                {{ item.chargeRuleDesc }}
              </div>
            </div>
            <!--<div class="flex-wrapper">-->
            <!--<div class="flex-vertical-item">-->
            <!--<span class="flex-vertical-label">商户负责人姓名：</span>-->
            <!--{{ form.contactName }}-->
            <!--</div>-->
            <!--<div class="flex-vertical-item">-->
            <!--<span class="flex-vertical-label">商户负责人手机号：</span>-->
            <!--{{ form.contactPhone }}-->
            <!--</div>-->
            <!--<div class="flex-vertical-item">-->
            <!--<span class="flex-vertical-label">销售：</span>-->
            <!--{{ form.salerName }}-->
            <!--</div>-->
            <!--<div class="flex-vertical-item">-->
            <!--<span class="flex-vertical-label">合伙人：</span>-->
            <!--{{ form.agentName }}<br>{{ form.agentNo }}-->
            <!--</div>-->
            <!--</div>-->
            <div class="flex-wrapper">
              <div class="flex-vertical-item flex-vertical-main">
                <span class="flex-vertical-label">补充信息：</span>
                <div class="clearfix">
                  <span
                    class="flex-vertical-content flex-func-link"
                    v-for="(item, index) in form.supplementFileUrls"
                    :key="index"
                    @click="previewHandle(item)"
                  >点击查看
                  </span>
                </div>
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item flex-vertical-main">
                <span class="flex-vertical-label">公司宣传文件：</span>
                <div>
                  <span
                    class="flex-vertical-content flex-func-link"
                    v-for="(item, index) in form.companyLeafletFileUrls"
                    :key="index"
                    @click="previewHandle(item)"
                  >点击查看
                  </span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane
          label="产品报价单"
          name="second"
          v-if="processType == 'MCH_CREATE'"
        >
          <div class="product-price-list">
            <el-table :data="form.quoteVoList">
              <el-table-column
                label="id"
                prop="mainstayMchNo"
              ></el-table-column>
              <el-table-column
                label="产品编号"
                prop="productNo"
              >
              </el-table-column>
              <el-table-column
                label="产品名称"
                prop="productName"
              >
              </el-table-column>
              <el-table-column
                label="代征主体"
                prop="mainstayMchName"
              ></el-table-column>
              <el-table-column
                label="公式类型"
                prop="formulaType"
              >
                <template v-slot="{row}">
                  {{ $dictCode('FormulaEnum', row.formulaType).desc }}
                </template>
              </el-table-column>
              <el-table-column
                label="固定金额"
                prop="fixedFee"
              >
              </el-table-column>
              <el-table-column
                label="费率（%）"
                prop="rate"
              >
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
        <el-tab-pane
          label="主体信息"
          name="third"
          v-if="processType == 'MAIN_AUTH'"
        >
          <div class="flex-container">
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">企业名称</span>
                {{ form.mchNo}}<br>
                {{ form.mchName }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">统一社会信用代码</span>
                {{ form.taxNo }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">注册地址</span>
                {{ form.registerAddrProvince + ' ' + form.registerAddrCity + ' ' + form.registerAddrTown + ' ' + form.registerAddrDetail }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">注册资本（万）</span>
                {{ form.registerAmount }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">经营范围</span>
                {{ form.managementScope }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">营业期限</span>
                <span v-if="$dictCode('ValidityDateTypeEnum', form.managementValidityDateType).desc == '长期有效'">长期有效<br />{{ form.managementTermBegin }}</span>
                <span v-else>{{ form.managementTermBegin }} 到 {{ form.managementTermEnd }}</span>
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">营业执照</span>
                <template v-if="isPdf(form.businessLicenseFileUrl)">
                  <span
                    class="flex-vertical-content func-content"
                    @click="openFile"
                  >点击查看</span>
                </template>
                <img
                  v-else
                  v-preview
                  class="showImg"
                  :src="fileUrl + form.businessLicenseFileUrl"
                  alt=""
                >
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">证件照</span>
                <div v-if="form.idCardCopyFileUrl">
                  <img
                    v-preview
                    class="showImg"
                    :src="fileUrl + form.idCardCopyFileUrl"
                    alt=""
                  >
                </div>
                <div v-else>
                  <img
                    v-preview
                    class="showImg"
                    :src="fileUrl + form.idCardHeadFileUrl"
                    alt=""
                  >
                  <img
                    v-preview
                    class="showImg"
                    :src="fileUrl + form.idCardEmblemFileUrl"
                    alt=""
                  >
                </div>
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">证件类型</span>
                <span>{{ $dictCode('CertificateTypeEnum', form.certificateType).desc }}</span>
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">银行卡号</span>
                <div>{{ form.accountNo }}</div>
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">联行号</span>
                <div>{{ form.bankChannelNo }}</div>
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">开户银行</span>
                <div>{{ form.bankName }}</div>
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">法定代表人姓名</span>
                {{ form.legalPersonName }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">证件号码</span>
                {{ form.certificateNumber }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">证件有效期</span>

                <span v-if="$dictCode('ValidityDateTypeEnum', form.certificateValidityDateType).desc == '长期有效'">长期有效<br />{{ form.certificateTermBegin }}</span>
                <span v-else>{{ form.certificateTermBegin }} 到 {{ form.certificateTermEnd }}</span>
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">负责人姓名</span>
                {{ form.contactName }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">负责人手机号</span>
                {{ form.contactPhone }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">常用邮箱</span>
                {{ form.contactEmail }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">客服电话</span>
                {{ form.servicePhone }}
              </div>
            </div>

            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">企业简称</span>
                {{ form.shortName }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">实际经营地址</span>
                {{ form.managementAddrProvince + ' ' + form.managementAddrCity + ' ' + form.managementAddrTown + ' ' + form.managementAddrDetail }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">门头照片</span>
                <img
                  v-preview
                  class="showImg"
                  :src="fileUrl + form.doorPhotoFileUrl"
                  alt=""
                >
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">办公内景照片</span>
                <img
                  v-preview
                  class="showImg"
                  :src="fileUrl + form.workIndoorFileUrl"
                  alt=""
                >
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">前台照片</span>
                <img
                  v-preview
                  class="showImg"
                  :src="fileUrl + form.receptionFileUrl"
                  alt=""
                >
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">纳税人类型</span>
                {{ $dictCode('TaxPayerTypeEnum', form.taxPayerType).desc }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">开票地址</span>
                {{ form.invoiceAddress }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">联系电话</span>
                {{ form.invoicePhone }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">开票银行</span>
                {{ form.invoiceBankName }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">开票账户</span>
                {{ form.invoiceAccountNo }}
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!--报价单-->
    <div
      class="box-container"
      v-else-if="processType == 'QUOTE_MODIFY'"
    >
      <el-tabs
        class="tabs-container"
        v-model="activeName"
      >
        <el-tab-pane
          label="基本信息"
          name="first"
        >
          <div class="flex-container">
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">供应商名称</span>
                <div class="flex-vertical-content">
                  {{ form.mainstayMchNo }} <br>
                  {{ form.mainstayMchName }}
                </div>
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">商户名称</span>
                <div class="flex-vertical-content">
                  {{ form.mchNo }} <br>
                  {{ form.mchName }}
                </div>
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">商户资质状态</span>
                <div class="flex-vertical-content">
                  {{ $dictCode('AuthStatusEnum', baseInfo.authStatus).desc }}
                </div>
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">商户状态</span>
                <div class="flex-vertical-content">
                  {{ $dictCode('MchStatusEnum', baseInfo.mchStatus).desc }}
                </div>
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">商户备注</span>
                <div class="flex-vertical-content">
                  {{ baseInfo.remark }}
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane
          label="岗位信息"
          name="second"
        >
          <div class="flex-container">
            <div
              class="flex-wrapper"
              v-for="(item, index) in cooperateInfo.position"
              :key="index"
            >
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">自由职业者的工作场所：</span>
                {{ $dictCode('WorkPlaceEnum', item.workplaceCode).desc }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">自由职业者服务类型：</span>
                {{ item.workCategoryName }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">建议发票类目：</span>
                <template v-for="(cate, key) in item.invoiceCategoryList">
                  <div
                    class="flex-vertical-content"
                    :key="key"
                  >
                    <el-tooltip
                      :content="cate.invoiceCategoryName"
                      :disabled="cate.invoiceCategoryName.length < 9"
                      :key="key"
                    >
                      <el-tag type="info">
                        {{ cate.invoiceCategoryName.length >= 9 ? cate.invoiceCategoryName.slice(0, 9) + '...' : cate.invoiceCategoryName }}
                      </el-tag>
                    </el-tooltip>
                  </div>
                </template>
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">企业从事业务：</span>
                {{ item.businessDesc }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">自由职业者服务描述：</span>
                {{ item.serviceDesc }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">自由职业者服务所得计算规则：</span>
                {{ item.chargeRuleDesc }}
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane
          label="合作信息"
          name="third"
        >
          <div class="flex-container">
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">企业行业类别：</span>
                {{ cooperateInfo.industryTypeName }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">预计用工人数：</span>
                {{ cooperateInfo.workerNum }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">预期可实现C端签约率区间：</span>
                {{ $dictCode('SignRateLevelEnum', cooperateInfo.signRateLevel).desc }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">自由职业者单人月经营所得9.7万以下占比：</span>
                {{ cooperateInfo.workerMonthIncomeRate }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">月流水预估：</span>
                {{ cooperateInfo.monthMoneySlip }}万元
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">是否可提供服务记录及经营所得计算明细：</span>
                {{ $dictCode('ProvideIncomeDetailTypeEnum', cooperateInfo.signRateLevel).desc }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">公司自有业务平台名称：</span>
                {{ cooperateInfo.bizPlatformName }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item flex-vertical-main">
                <span class="flex-vertical-label">公司网站：</span>
                {{ cooperateInfo.companyWebsite }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item flex-vertical-main">
                <span class="flex-vertical-label">补充信息：</span>
                <div class="clearfix">
                  <span
                    class="flex-vertical-content flex-func-link"
                    v-for="(item, index) in cooperateInfo.supplementFileUrls"
                    :key="index"
                    @click="previewHandle(item)"
                  >点击查看
                  </span>
                </div>
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item flex-vertical-main">
                <span class="flex-vertical-label">公司宣传文件：</span>
                <div>
                  <span
                    class="flex-vertical-content flex-func-link"
                    v-for="(item, index) in cooperateInfo.companyLeafletFileUrls"
                    :key="index"
                    @click="previewHandle(item)"
                  >点击查看
                  </span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane
          label="产品报价单"
          name="forth"
        >
          <el-table :data="form.quoteRateList" v-if="form.productNo == 'ZXH'">
            <el-table-column label="产品名称">
              <template v-slot="{row}">
                {{ form.productNo }} <br>
                {{ form.productName }}
              </template>
            </el-table-column>
            <el-table-column
              label="公式类型"
              width="150"
            >
              <template v-slot="{row}">
                {{ $dictCode('FormulaEnum', row.formulaType).desc }}
              </template>
            </el-table-column>
            <el-table-column
              label="固定金额"
              prop="fixedFee"
              width="150"
            ></el-table-column>
            <el-table-column
              label="比例"
              prop="rate"
              width="150"
            ></el-table-column>
            <el-table-column label="特殊规则">
              <template v-slot="{row}">
                <div
                  v-for="(item, index) in row.ruleParam"
                  :key="index"
                >
                  {{ $dictCode('ProductFeeSpecialRuleTypeEnum', item.specialRuleType).desc }}
                  {{ $dictCode('CompareTypeEnum', item.compareType).desc }}
                  {{ item.value }}
                </div>
              </template>
            </el-table-column>
          </el-table>
            <el-table v-else-if="form.productNo == 'CKH'" :data="[form.merchantCkhQuote]">
              <el-table-column label="产品">
                <el-table-column label="产品名称">
                  <template v-slot="{row}">
                    {{ form.productNo }} <br>
                    {{ form.productName }}
                  </template>
                </el-table-column>
                <el-table-column label="供应商">
                  <template v-slot="{row}">
                    {{ form.mainstayMchNo }} <br>
                    {{ form.mainstayMchName }}
                  </template>
                </el-table-column>
                <el-table-column label="岗位类别">
                  <template v-slot="{row}">
                    <el-tooltip
                      v-for="(item, index) in form.positionNameList"
                      :key="index"
                      :content="item"
                    >
                      <el-tag type="info">
                        {{ item }}
                      </el-tag>
                    </el-tooltip>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="个税">
                <el-table-column label="个税类型" prop="taxTypeDesc">
                  <template v-slot="{row, column}">
                    {{ row[column.property] }}
                  </template>
                </el-table-column>
                <el-table-column label="个税承担方" prop="taxPayer">
                  <template v-slot="{row, column}">
                    {{ $dictCode('TaxPayerEnum', row[column.property]).desc }}
                  </template>
                </el-table-column>
                <el-table-column label="增值税税率 %" prop="addedTaxRatePct">
                  <template v-slot="{row, column}">
                    {{ row[column.property] }}
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="服务费比例 %" prop="serviceFeeRatePct">
                <template v-slot="{row, column}">
                  {{ row[column.property] }}
                </template>
              </el-table-column>
              <el-table-column label="服务费结算模式" prop="balancedMode">
                <template v-slot="{row, column}">
                  {{ $dictCode('BalancedEnum', row[column.property]).desc }}
                </template>
              </el-table-column>
            </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!--操作日志-->
    <div class="box-container">
      <h3 style="border-bottom: 1px solid #ddd; padding-bottom: 8px; font-weight: bolder; color: #000;">
        流程列表/操作日志
      </h3>
      <div class="card-container">
        <div class="card-list">
          <div
            class="card-item"
            v-for="(step, index) in nodeList"
            :key="index"
          >
            <div v-show="index < 3 || showAllNode">
              <div class="card-name">
                {{ step.stepName }}
                <div class="card-status-icon"></div>
              </div>
              <div class="card-info">
                <div
                  class="card-detail"
                  v-if="step.commonFlowLogList.length"
                >
                  <div
                    class="card-detail-item"
                    v-for="(item, index) in step.commonFlowLogList"
                    :key="index"
                  >
                    <div class="card-detial-desc">
                      <div class="card-detail-operate">
                        <span>{{ item.handlerName }}</span>
                        -
                        <el-tooltip :disabled="item.taskName !== '编辑信息'">
                          <span>{{ item.taskName }}</span>
                          <template v-slot:content>
                            <div
                              v-for="(value, prop) in item.extInfo"
                              :key="prop"
                            >
                              <div class="diff-content">{{ explainDiff(value, prop) }}</div>
                            </div>
                          </template>
                        </el-tooltip>
                        <el-tag
                          :type="getTagType(item.status)"
                          v-if="item.status"
                          style="margin-left: 8px;"
                        >
                          <span class="color-gray">{{ $dictCode('HandleStatus', item.status).desc }}</span>
                        </el-tag>
                      </div>
                      <div
                        class="card-detail-approval color-gray"
                        v-if="item.approvalOpinion"
                        v-html="item.approvalOpinion"
                        @click="onClickList"
                      ></div>
                    </div>
                  </div>
                  <div
                    class="clearfix color-gray"
                    style="margin-top: 16px;"
                  >
                    <div class="card-detail-time fl">
                      {{ step.commonFlowLogList[0].updateTime }}
                    </div>
                    <div class="card-detail-spend fr">
                      耗时 {{ formatDuration(step.spentTime) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="card-item"
            v-if="!showAllNode"
          >
            <div
              class="card-name func-content"
              @click="() => {showAllNode = true}"
            >
              查看完整流程
              <div class="card-status-icon"></div>
            </div>
          </div>
        </div>
      </div>

    </div>

    <div
      class="form-btn-group"
      v-if="$dictCode('FlowStatus', processInfo.status).desc != '已完成'"
      style="text-align: right;"
    >
      <el-button
        type="text"
        @click="showReply = true"
      >回复</el-button>
      <el-button
        type="text"
        v-if="isChangHandler"
        @click="() => { transferVisible = true; }"
      >
        变更审批人
      </el-button>
      <el-button
        v-if="isHandlerByMe"
        type="primary"
        @click="submitTask('confirm')"
      >确认
      </el-button>
      <el-button
        v-if="isBack"
        type="danger"
        @click="submitTask('disagree')"
      >驳回
      </el-button>
      <el-button
        v-if="isInitUser"
        type="primary"
        @click="showWithdrawDialog"
      >撤回审批
      </el-button>
    </div>

    <opnion-dialog
      :visible.sync="visible"
      @close="closeOpnion"
    ></opnion-dialog>

    <el-dialog
      title="撤回审批"
      :visible.sync="withDrawVisible"
      :close-on-click-modal="false"
    >
      <div class="el-message-box__container">
        <div class="el-message-box__status el-icon-warning"></div>
        <div class="el-message-box__message">
          <p>撤回审批将取消该审批申请，确定要撤回审批吗？</p>
        </div>
      </div>
      <el-form
        :model="withDrawForm"
        label-width="100px"
        style="width: auto; margin-top: 16px;"
      >
        <el-form-item label="审批意见：">
          <el-input
            v-model="withDrawForm.reason"
            type="textarea"
            :rows="5"
          ></el-input>
        </el-form-item>
      </el-form>

      <div slot="footer">
        <el-button
          type="primary"
          @click="withDrawFlow"
          :loading="withdrawLoading"
        >确定</el-button>
        <el-button @click="() => {withDrawVisible = false}">取消</el-button>
      </div>
    </el-dialog>

    <transfer-dialog
      :task-id="taskId"
      :commonFlowId="processId"
      :staff="staffList"
      :visible.sync="transferVisible"
      @confirm="confirmTransferTask"
    ></transfer-dialog>

    <reply-dialog
      v-model="replyContent"
      :visible.sync="showReply"
      @confirm="onReply"
    ></reply-dialog>

  </div>
</template>

<script>
  import parseValue from '@/components/ParseValue'
  import { getCooperateInfo, getBaseInfo } from "@/api/merchant";
  import { getDetail, getInstanceImage, submitTask, getFlowInfo, withDrawFlow, transferTask, reply } from '@/api/flow';
  import { formatDuration } from '@/utils'
  import { listStaffPage } from '@/api/staff'
  import opnionDialog from './opinionDialog'
  import transferDialog from './transferDialog'
  import replyDialog from './replyDialog'

  export default {
    components: {
      opnionDialog,
      transferDialog,
      replyDialog,
      parseValue
    },
    name: 'detailProcess',
    data() {
      return {
        processId: '',
        activeStep: 0,
        activeName: 'first',
        processInfo: {},
        nodeList: [],
        form: {},
        processNodeId: '', // 当前操作人结点的id

        visible: false, // 审核意见输入弹窗控制
        approvalOpinion: '',
        platform: '',
        handleType: '',

        staffList: [],

        processInstanceImg: '',

        withDrawVisible: false, // 撤回窗口显示控制
        withdrawLoading: false,
        withDrawForm: {
          commonFlowId: '',
          reason: '',
        },
        isHandlerByMe: false, // 是否为流程审批人
        isInitUser: false,
        isEdit: false,
        isChangHandler: false,
        isBack: false,

        transferVisible: false,
        showAllNode: false,

        showReply: false,
        replyContent: '',

        baseInfo: {},
        cooperateInfo: {},
      }
    },
    computed: {
      processType() {
        if (this.processInfo && this.processInfo.flowTopicType) {
          return this.$dictCode('FlowPageEnum', this.processInfo.flowTopicType).flag;
        } else {
          return ''
        }
      },
      taskId() {
        return this.$route.query.taskId || '';
      },
    },
    mounted() {
      listStaffPage({
        pageSize: 200,
        pageCurrent: 1
      }).then(({ data }) => this.staffList = data.data);
      this.processId = this.$route.query.processId;
      this.init();
    },
    methods: {
      formatDuration,
      async init() {
        await Promise.all([
          this.getProcessInfo(),
          this.getFlowNode(),
        ]);
        window.requestAnimationFrame(() => {
          if (this.processType == 'MAIN_AUTH') {
            this.activeName = 'third';
          }
        })

        if (this.processType == 'QUOTE_MODIFY') {
          this.getMerchantInfo();
        }
      },
      async getMerchantInfo() {
        if (!this.form.mchNo) return;
        const [{ data: baseInfo }, { data: cooperateInfo }] = await Promise.all([
          getBaseInfo({ mchNo: this.form.mchNo }),
          getCooperateInfo({ mchNo: this.form.mchNo }),
        ])
        this.baseInfo = baseInfo;
        this.cooperateInfo = cooperateInfo;
      },
      async getProcessInfo() {
        const { data } = await getFlowInfo({
          commonFlowId: this.processId,
          taskId: this.taskId,
        })
        this.processInfo = data;
        this.isHandlerByMe = data.isHandleUser;
        this.isInitUser = data.isInitUser;
        this.isEdit = data.isEdit;
        this.isChangHandler = data.isChangHandler;
        this.isBack = data.isBack;
        // 获取创建的信息
        this.form = Object.assign({}, JSON.parse(data.extInfo))
      },
      // 新的获取节点信息
      async getFlowNode() {
        const { data } = await getDetail({
          commonFlowId: this.processId
        });

        this.nodeList = data;

        this.nodeList.forEach(node => {
          let spentTime = 0;
          node.commonFlowLogList.forEach(log => {
            if (!isNaN(log.spentTime)) {
              spentTime += Number(log.spentTime)
            }
            log.extInfo = JSON.parse(log.extInfo);
          });
          node.spentTime = spentTime;
        })
      },
      // 流程追踪图
      async getInstanceImage() {
        if (this.processInfo.processInstanceId) {
          const { data } = await getInstanceImage({
            processInstanceId: this.processInfo.processInstanceId
          });
          this.processInstanceImg = `data:image/png;base64,${data}`;
        }
      },
      // 新版流程处理
      async submitTask(result) {
        if (this.handleType !== result) {
          this.handleType = result;
          this.visible = true;
          return;
        }
        const { data } = await submitTask({
          condition: {},
          participant: {},
          commonFlowId: this.processId,
          opinion: this.approvalOpinion,
          status: result == 'confirm' ? 100 : 101,
          taskId: this.taskId,
        })
        data && this.$message.success(data);
        this.init();
      },
      // 撤回
      showWithdrawDialog() {
        this.withDrawForm.commonFlowId = Number(this.processId);
        this.withDrawVisible = true;
      },
      async withDrawFlow() {
        this.withdrawLoading = true;
        const { data } = await withDrawFlow(this.withDrawForm).finally(() => {
          this.withdrawLoading = true;
        });
        data && this.$message.success(data);
        this.withDrawVisible = false;
        this.init();
      },
      editMainInfo() {
        // 跳去主体认证
        if (this.processType == 'MAIN_AUTH') {
          let form = JSON.parse(JSON.stringify(this.form));
          sessionStorage.setItem('mainInfo', JSON.stringify(form));
          this.$router.push({
            path: '/merchant/auth',
            query: {
              mchNo: this.form.mchNo,
              processId: this.processId,
              taskId: this.taskId
            }
          });
        } else if (this.processType == 'MCH_CREATE') {
          // 跳去创建商户
          let form = JSON.parse(JSON.stringify(this.form));
          form.processId = this.processId
          for (let key in form.positionVoList) {
            // 主动触发选择下拉框
            form.positionVoList[key].workplaceCode += '';
            form.positionVoList[key].workCategoryCode += '';
          }
          sessionStorage.setItem('cooperationInfo', JSON.stringify(form));
          this.$router.push({
            path: '/merchant/merchantManagement/createMerchant',
            query: {
              actionType: 'EDIT',
              platform: this.processInfo.platform,
              taskId: this.taskId
            }
          });
        }
      },
      closeOpnion(data) {
        this.approvalOpinion = data && data.opnion;
        if (data && data.action == 'confirm') {
          this.submitTask(this.handleType);
        } else {
          this.handleType = '';
        }
      },
      selectSaler(id) {
        if (!this.staffList.length) {
          return '';
        }
        for (let item of this.staffList) {
          if (item.id == this.form.salerId) {
            return item.realName
          }
        }
      },
      getTagType(status) {
        switch (Number(status)) {
          case 100:
            return 'success';
          case 101:
            return 'danger';
          case 103:
            return 'info';
          default:
            return ''
        }
      },
      checkFile(file) {
        window.open(this.fileUrl + file);
      },
      explainDiff(data, prop) {
        let result = '';
        if (Array.isArray(data)) {
          result = `${prop} 中`
          data.forEach((item, index) => {
            result += this.explainDiff(item, index + 1)
          })
        } else {
          if (!data) return '';
          if (data.newValue || data['新']) {
            result = `将${typeof prop == 'number' ? '' : prop + '由'} ${data.oldValue || data['旧'] || '空'} 改成 ${data.newValue || data['新'] || '空'}`
          } else if (typeof prop == 'number') {
            result = `第${prop}项`
            let keys = Object.keys(data);
            keys.forEach(key => {
              result += this.explainDiff(data[key], key);
            })
          } else {
            result = `将 ${prop}由 ${data.oldValue || data['旧' || '空']} 改成 ${data.newValue || data['新'] || '空'}`
          }
        }
        return result;
      },
      async openFile() {
        const fileMsg = await this.formatFileMsg(this.form.businessLicenseFileUrl)
        window.open(fileMsg.fileUrl)
      },
      async confirmTransferTask(form) {
        const { data } = await transferTask(form);
        data && this.$message.success(data);
        this.init();
      },
      async onReply() {
        const { data } = await reply({
          commonFlowId: this.processId,
          content: this.replyContent,
        });
        data && this.$message.success(data);
        this.init();
      },
      onClickList(e) {
        const target = e.target;
        const tagName = target.tagName.toLowerCase();
        if (tagName === 'img' && target.src) {
          this.$preview(target.src)
        }
      },
      async previewHandle(url) {
        const fileMsg = await this.formatFileMsg(url)
        if (this.isImg(url)) {
          this.$preview(fileMsg.fileUrl)
        } else {
          const fileName = url.split('/').pop()
          const suffix = url.split('.').pop()
          this.downloadFile(fileMsg.fileUrl, fileName, suffix)
        }
      },
    }
  }
</script>

<style lang="scss" scoped>
  .page-container {
    background: transparent;
    box-shadow: none;
    border: none;
    .box-container {
      box-shadow: none;
      border: none;
      border-radius: 0;
    }

    .flex-container {
      padding: 8px 0 32px 4px;
    }
    .flex-wrapper {
      margin-top: 16px;
    }

    h2 {
      margin-bottom: 0;
      font-size: 24px;
    }

    ::v-deep .el-tabs__nav {
      margin-left: 20px;

      .el-tabs__item {
        padding-top: 10px;
        padding-bottom: 10px;
        box-sizing: content-box;
      }
    }

    .subTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 20px;
      font-size: 16px;
    }
  }

  .process-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 40px 30px;
    .step-container {
      width: 600px;
      margin-left: 10px;
      .step-item {
        display: inline-block;
        position: relative;
        margin-right: 16px;
        padding: 8px 50px;
        border: 1px solid $deepGray;
        border-radius: 25px;
        line-height: 1.3;
        vertical-align: bottom;

        &.active {
          color: #20a0ff;
        }
        .step-handler {
          width: 60px;
          height: 20px;
          line-height: 20px;
          @include text-ellipsis;
        }
        .step-num {
          @include position-tb-center(absolute);
          left: 8px;
          width: 30px;
          height: 30px;
          line-height: 30px;
          margin: 0 auto;
          border: 1px solid #ccc;
          border-radius: 50%;
          text-align: center;
        }
        .step-topic,
        .step-time {
          height: 20px;
          line-height: 20px;
        }
      }
    }
  }

  .el-tabs .flex-container {
    padding-top: 0;
  }

  .tabs-container {
    .el-tag {
      margin: 0 5px 5px 0;
    }
  }

  .tabs-edit-btn {
    position: absolute;
    top: 32px;
    right: 32px;
    z-index: 99;
  }

  .table-container {
    margin-bottom: 20px;
  }

  .el-table {
    width: calc(100% - 40px);
    margin: 0 20px;
  }

  .showImg {
    width: 80px;
    height: 80px;
    & + .showImg {
      margin-left: 20px;
    }
  }

  .btn-group .el-button {
    margin: 8px;
  }

  .flex-vertical-content {
    margin-right: 8px;
    margin-bottom: 8px;
  }

  .card-list {
    .card-name {
      position: relative;
      padding-left: 20px;
      font-weight: bolder;
    }

    .card-status-icon {
      position: absolute;
      left: 0;
      top: 50%;
      bottom: 0;
      transform: translate3d(0, -50%, 0);
      width: 10px;
      height: 10px;
      border-radius: 10px;
      background: #ddd;
    }

    .card-item {
      position: relative;
      &::after {
        content: "";
        display: block;
        position: absolute;
        top: 4px;
        bottom: -5px;
        left: 4px;
        width: 2px;
        background: #ddd;
      }

      &:last-child::after {
        background: transparent;
      }
    }

    .card-info {
      position: relative;
      padding: 16px 0 16px 32px;
    }
    .card-detail {
      width: 70%;
      padding: 0 16px 16px;
      box-shadow: 0 0 5px 3px #ddd;
      overflow: auto;
    }

    .card-detail-item {
      margin-top: 16px;
    }

    .card-detail-approval {
      margin-top: 4px;
      &::v-deep img {
        width: 70px;
        height: 70px;
        cursor: pointer;
      }
    }
  }
</style>
