<template>
  <div class="flex-box">
    <div class="box-container form-container">
      <el-form
        :model="form"
        :rules="rules"
        label-width="150px"
        ref="form"
      >
        <el-form-item
          label="用工企业："
          prop="mchNo"
        >
          <el-select
            v-model="form.mchNo"
            @change="getWorkOrderMerchant"
            filterable
          >
            <el-option
              v-for="item in merchantList"
              :key="item.employerNo"
              :label="item.employerName"
              :value="item.employerNo"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="负责销售：">
          <el-input
            disabled
            v-model="merchant.salerName"
          ></el-input>
        </el-form-item> -->
        <!-- <el-form-item label="合伙人：">
          <el-input
            disabled
            v-model="merchant.agentName"
          ></el-input>
        </el-form-item> -->
        <el-form-item
          label="代征主体："
          v-if="merchant && merchant.mainstayNos"
          prop="mainstayNo"
        >
          <el-select
            v-model="form.mainstayNo"
            disabled
          >
            <el-option
              v-for="(item, index) in merchant.mainstayNos"
              :key="index"
              :label="item.mainstayName"
              :value="item.mainstayNo"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="调单期间："
          prop="endWorkTime"
        >
          <date-picker
            picker="separate"
            type="datetime"
            :start-time.sync="form.startWorkTime"
            :end-time.sync="form.endWorkTime"
            :is-show-fast-time="false"
          ></date-picker>
        </el-form-item>
        <el-form-item
          label="调单范围："
          prop="workRangeUrl"
        >
          <file-upload
            :url.sync="form.workRangeUrl"
            list-type="text"
            :max="1"
            :options="{
              pdf: true,
              word: true,
              addition: ['xls', 'xlsx']
            }"
            @beforeUpload="loading=true"
            @change="loading=false"
          >
            <el-button size="mini">上传文件</el-button>
            <div
              class="color-gray"
              slot="tip"
            >请上传Excel格式的调单人员名单</div>
          </file-upload>
        </el-form-item>
        <el-form-item
          label="截止日期："
          prop="endDate"
        >
          <el-date-picker
            type="datetime"
            v-model="form.endDate"
            default-time="23:59:59"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="工单模板："></el-form-item> -->
        <el-form-item>
          <el-button
            type="primary"
            :loading="loading"
            @click="submit"
          >新建工单</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div
      class="info-container"
      v-if="merchant && merchant.mchNo"
    >
      <div class="info-box">
        <div class="info-title">商户信息</div>
        <div class="info-detail">
          <div class="info-detail-item">
            <span class="info-detail-label">商户名称：</span>
            <span class="info-detail-value">{{ merchant.mchName }}</span>
          </div>
          <!-- <div class="info-detail-item">
            <span class="info-detail-label">负责人：</span>
            <span class="info-detail-value">{{ merchant.contactName }}</span>
          </div>
          <div class="info-detail-item">
            <span class="info-detail-label">负责人手机号：</span>
            <span class="info-detail-value">{{ merchant.contactPhone }}</span>
          </div> -->
          <div class="info-detail-item">
            <span class="info-detail-label">经营地址：</span>
            <span class="info-detail-value">
              {{ merchant.managementAddrProvince + merchant.managementAddrCity + merchant.managementAddrTown + merchant.managementAddrDetail }}
            </span>
          </div>
        </div>
      </div>
      <div class="info-box history-box">
        <div class="info-title">历史工单</div>
        <div
          class="info-detail history-detail"
          v-for="(item, index) in history"
          :key="index"
        >
          <div class="info-detail-item">
            <span class="info-detail-label">工单编号：</span>
            <span
              class="info-detail-value func-content"
              @click="workFormClick(item)"
            >{{ item.workOrderNo}}</span>
          </div>
          <div class="info-detail-item">
            <span class="info-detail-label">工单状态：</span>
            <span class="info-detail-value">{{ $dictCode('FlowStatus', item.status).desc }}</span>
          </div>
          <div class="info-detail-item">
            <span class="info-detail-label">创建人：</span>
            <span class="info-detail-value">{{item.initiatorName}}</span>
          </div>
          <div class="info-detail-item">
            <span class="info-detail-label">发起时间：</span>
            <span class="info-detail-value">{{item.createTime}}</span>
          </div>
          <div class="info-detail-item">
            <span class="info-detail-label">截止时间：</span>
            <span class="info-detail-value">{{item.endDate}}</span>
          </div>
        </div>
        <div
          class="history-list-tip text-center color-gray"
          @click="loadHistory"
        >
          {{ listEnd ? '没有更多了' : (listLoading ? '加载中...' : '加载更多') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getAllFlow } from '@/api/flow'
  import FileUpload from '@/components/FileUpload'
  import { getMchByMainstayNo } from '@/api/merchant'
  import { getWorkOrderMerchant } from '@/api/workform'
  export default {
    components: {
      FileUpload,
    },
    data() {
      return {
        form: {},
        rules: {
          mchNo: [
            { required: true, message: '请选择商户', trigger: 'change' },
          ],
          startWorkTime: [
            { required: true, message: '请选择调单期间', trigger: 'change' },
          ],
          endWorkTime: [
            { required: true, message: '请选择调单期间', trigger: 'change' },
          ],
          endDate: [
            { required: true, message: '请选择截止时间	', trigger: 'change' },
          ],
        },
        loading: false,
        merchantList: [],
        merchant: {},

        history: [],
        listEnd: false,
        listLoading: false,
        page: 1,
      }
    },
    mounted() {
      this.getAllMerchant()
      this.form.mainstayNo = this.$store.state.user.userData.mchNo
    },
    methods: {
      async getAllMerchant() {
        const { data } = await getMchByMainstayNo()
        this.merchantList = data
      },
      async getWorkOrderMerchant(mchNo) {
        this.listEnd = this.listLoading = false
        this.mchNo = mchNo
        const [{ data }, list] = await Promise.all([
          getWorkOrderMerchant({
            mchNo
          }),
          this.loadHistory()
        ])
        this.merchant = data
      },
      async submit() {
        try {
          await this.$refs.form.validate()
          sessionStorage.setItem('workOrder', JSON.stringify(this.form))
        } catch {
          return
        }
        this.$router.push('/audit/workform/generate')
      },
      workFormClick({ workOrderNo }) {
        this.$router.push({
          path: '/audit/workform',
          query: {
            workOrderNo
          }
        })
      },
      async loadHistory() {
        if (this.listLoading || this.listEnd) return
        this.listLoading = true
        const { data: { data: list, totalRecord } } = await getAllFlow({
          employerNo: this.mchNo,
          pageCurrent: this.page++,
          pageSize: 10,
          workType: 101,
        })
        this.listLoading = false
        this.listEnd = list.length + this.history.length >= totalRecord
        this.history = this.history.concat(list)
        return list
      },
    }
  }
</script>

<style lang="scss" scoped>
  .form-container {
    width: 70%;
    padding: 32px 100px 32px 50px;
  }
  .info-container {
    width: 30%;
    margin: 0 0 16px 16px;
    .info-box {
      margin-bottom: 32px;
      padding: 32px 0;
      border-radius: 10px;
      background: #fff;
    }
    .info-detail {
      padding: 0 16px;
    }
    .info-title {
      padding: 0 16px;
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .info-detail-item {
      margin: 8px 0;
    }
    .history-detail {
      padding: 0 16px 16px;
      border-bottom: 1px solid #ddd;
    }
    .history-list-tip {
      margin-top: 16px;
      font-size: 14px;
      cursor: pointer;
    }
    .history-box {
      max-height: 500px;
      overflow: auto;
    }
  }
</style>