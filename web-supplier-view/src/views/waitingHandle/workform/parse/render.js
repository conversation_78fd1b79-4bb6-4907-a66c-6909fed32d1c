const renderLayout = {
  'el-input': function (h, vm) {
    return (
      <el-input value={vm.element.value} onInput={this.onChange} placeholder={vm.element.placeholder}></el-input>
    )
  },
  'el-textarea': function (h, vm) {
    return (
      <el-input value={vm.element.value} onInput={this.onChange} type='textarea' rows={5} placeholder={vm.element.placeholder}></el-input>
    )
  },
  'text': function (h, vm) {
    return (
      <div>{vm.element.value}</div>
    )
  },
  'el-radio-group': function (h, vm) {
    let children = vm.element.options.map(item => {
      return (
        <el-radio label={item.value}>{item.label}</el-radio>
      )
    })
    return (
      <el-radio-group value={vm.element.value} onInput={this.onChange}>
        {children}
      </el-radio-group>
    )
  },
  'el-checkbox-group': function (h, vm) {
    let children = vm.element.options.map(item => {
      return (
        <el-checkbox label={item.value}>{item.label}</el-checkbox>
      )
    })
    return (
      <el-checkbox-group value={vm.element.value} onInput={this.onChange}>
        {children}
      </el-checkbox-group>
    )
  },
  'el-select': function (h, vm) {
    let children = vm.element.options.map(item => {
      return (
        <el-option label={item.label} value={item.value}></el-option>
      )
    })
    return (
      <el-select value={vm.element.value} onInput={this.onChange}>
        {children}
      </el-select>
    )
  },
  'el-table': function (h, vm) {
    let header = vm.element.header
    let columns = header.map((item) => {
      return (
        <el-table-column 
          label={item.title} 
          prop={item.prop} 
          align="center"
          scopedSlots={{
            default: ({$index, row, column}) => {
              return (
                item.prop == 'rowTitle'
                  ? <div>{row[item.prop]}</div>
                  : <el-input value={row[item.prop]} onInput={(val) => this.onTableInput(row, item.prop, val)} clearable></el-input>
              )
            }
          }}
        >
        </el-table-column>)
    })
    return (
      <el-table data={vm.element.value} border={true}>
        {columns}
      </el-table>
    )
  }
}
export default {
  name: 'render',
  props: {
    element: {
      type: Object,
      default: () => ({})
    }
  },
  render(h) {
    let vm = this
    const renderFn = renderLayout[this.element.type];
    if (renderFn) {
      return renderFn.call(this, h, vm)
    } else {
      return (
        <div class="component-no-match">没有对应的组件渲染</div>
      )
    }
  },
  methods: {
    onChange(value) {
      this.$emit('change', value)
    },
    onTableInput(row, prop, val) {
      row[prop] = val
    }
  },
}