<template>
  <div class="box-container">
    <el-button
      type="primary"
      class="create-btn"
      @click="createForm"
    >新建工单</el-button>
    <el-tabs
      v-model="activeTab"
      @tab-click="search(true)"
    >
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="index"
        :label="item.label"
        :name="String(index)"
      ></el-tab-pane>
    </el-tabs>

    <div class="search-container flex-container">
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">工单编号：</span>
          <el-input
            clearable
            v-model="form.workOrderNo"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业：</span>
          <el-input
            clearable placeholder="模糊查询"
            v-model="form.employerName"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">状态：</span>
          <el-select
            clearable
            v-model="form.status"
          >
            <el-option
              v-for="item in $dict('FlowStatus')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">流程节点：</span>
          <el-input
            clearable
            v-model="form.taskNameLike"
          ></el-input>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">发起调单日期：</span>
          <date-picker
            picker="separate"
            ref="datePicker1"
            type="datetime"
            :start-time.sync="form.beginCreateDate"
            :end-time.sync="form.endCreateDate"
          ></date-picker>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">截止日期：</span>
          <date-picker
            picker="separate"
            ref="datePicker2"
            type="datetime"
            :start-time.sync="form.beginEndDate"
            :end-time.sync="form.endEndDate"
            :use-option="false"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search"
        >查询</el-button>
        <el-button @click="exportFlow(1)">
          导出工单列表
        </el-button>
        <el-button @click="exportFlow(2)">
          导出附件
        </el-button>
        <el-button type="text" @click="getExportList">查看已导出列表</el-button>
        <el-button
          type="text"
          @click="reset"
        >重置</el-button>
      </div>
    </div>

    <div class="content-container">
      <el-table :data="list">
        <el-table-column
          label="工单编号"
          width="120"
          prop="workOrderNo"
        ></el-table-column>
        <el-table-column
          label="用工企业"
          width="120"
          prop="employerName"
        ></el-table-column>
        <el-table-column
          label="发起调单日期"
          width="120"
          prop="createTime"
        >
          <template v-slot="{row}">
            <div v-html="renderTime(row.createTime)"></div>
          </template>
        </el-table-column>
        <el-table-column
          label="调单期间"
          width="120"
        >
          <template v-slot="{row}">
            <div>
              {{ row.startWorkTime }} - {{ row.endWorkTime }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="截止时间"
          width="120"
          prop="endDate"
        >
          <template v-slot="{row}">
            <div v-html="renderTime(row.endDate)"></div>
          </template>
        </el-table-column>
        <el-table-column
          label="调单范围"
          width="120"
        >
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="download(row)"
              v-if="row.workRangeUrl"
            >
              下载
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="流程节点"
          width="120"
          prop="taskName"
        ></el-table-column>
        <el-table-column
          label="工单状态"
          width="120"
        >
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.status)">
              {{ $dictCode('FlowStatus', row.status).desc }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="发起人"
          width="120"
          prop="initiatorName"
        ></el-table-column>
        <el-table-column
          label="操作"
          width="120"
        >
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="checkDetail(row)"
            >查看详情</el-button>
            <el-button
              type="text"
              @click="checkHistory(row)"
            >查看操作记录</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="page.totalRecord"
        :current-page.sync="page.pageCurrent"
        :page-sizes="[10, 50]"
        :page-size="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>

    <opinion-dialog
      :visible.sync="visible"
      @close="onConfirm"
    ></opinion-dialog>
    <history-dialog ref="history"></history-dialog>
    <export-record ref="exportRecord"></export-record>
  </div>
</template>

<script>
  import { getTodoList, getAllFlow, submitTask, exportFlow, exportFlowFile } from '@/api/flow'
  import historyDialog from './historyDialog';
  import opinionDialog from '../opinionDialog';
  import ExportRecord from '@/components/ExportRecord'
  export default {
    name: 'workformList',
    components: {
      historyDialog,
      opinionDialog,
      ExportRecord
    },
    data() {
      return {
        list: [],
        sales: [],
        mainstayList: [],
        form: {},
        page: {
          pageCurrent: 1,
          pageSize: 10,
          totalRecord: 0
        },
        activeTab: 0,
        tabs: [
          {
            label: '待处理工单',
            api: getTodoList,
            type: 44,
          },
          {
            label: '我收到的工单',
            api: getAllFlow,
            type: 45,
          },
        ],
        visible: false,
      }
    },

    mounted() {
      this.init()
      this.search()
    },

    methods: {
      async init() {
        for (let p in this.$route.query) {
          this.form[p] = this.$route.query[p]
        }
      },
      reset() {
        this.form = {};
        this.$refs.datePicker1.clearTime()
        this.$refs.datePicker2.clearTime()
      },
      async search(init) {
        if (init) {
          this.page.pageCurrent = 1;
        }
        let api = this.tabs[this.activeTab].api;
        const { data } = await api({
          workType: 101,
          ...this.form,
          ...this.page
        });
        this.page.totalRecord = data.totalRecord
        this.list = data.data
      },
      async download(row) {
        const fileMsg = await this.formatFileMsg(row.workRangeUrl)
        const fileName = row.workRangeUrl.split('/').pop()
        const suffix = row.workRangeUrl.split('.').pop()
        this.downloadFile(fileMsg.fileUrl, fileName, suffix)
      },
      checkDetail({ commonFlowId, taskId }) {
        this.$router.push({
          path: '/audit/workform/detail',
          query: {
            commonFlowId,
            taskId,
            actionType: this.activeTab == 0 ? 'EDIT' : 'VIEW'
          }
        })
      },
      async transferMainstay(row) {
        try {
          await this.$confirm('确认转交代征主体审核吗？', '提示', {
            type: 'warining'
          })
          this.visible = true
          this.editRow = row
        } catch {
          // 取消
        }
      },
      async onConfirm(act) {
        if (!act) return;
        const {
          commonFlowId,
          taskId,
        } = this.editRow
        const { data } = await submitTask({
          commonFlowId,
          taskId,
          opinion: act.opnion,
          status: 100,
          condition: {
            transfer: 1
          }
        })
        data && this.$message.success(data)
        this.search()
      },
      checkHistory(row) {
        this.$refs.history.toggleVisible(row.commonFlowId)
      },
      createForm() {
        this.$router.push({
          path: '/audit/workform/create',
        })
      },
      async exportFlow(flag) {
        if (flag == 1) {
          await exportFlow({ type: this.tabs[this.activeTab]['type'] }, this.form)
        } else {
          await exportFlowFile({ type: this.tabs[this.activeTab]['type'] }, this.form)
        }
        this.$message.success('导出成功，请到导出列表进行导出');
      },
      getExportList() {
        this.$refs.exportRecord.isShow = true;
        this.$refs.exportRecord.getExportRecord(this.tabs[this.activeTab]['type']);
      },
      getTagType(status) {
        switch(Number(status)) {
          case 100:
            return 'success'
          case 102:
            return 'danger'
          default:
            return
        }
      },
    }
  }
</script>

<style>
</style>
