const componentList = [
  {
    name: '上传',
    icon: 'upload',
    element: {
      type: "el-upload",
      title: '上传文件',
      value: []
    }
  },
  {
    name: '单行文本',
    icon: 'edit',
    element: {
      type: 'el-input',
      title: '单行文本',
    }
  },
  {
    name: '多行文本',
    icon: 'edit',
    element: {
      type: 'el-textarea',
      title: '多行文本',
    }
  },
  {
    name: '只读文本',
    icon: 'edit',
    element: {
      type: 'text',
      title: '只读文本',
      value: '只读文本内容'
    }
  },
  {
    name: '单选框组',
    icon: 'help',
    element: {
      type: 'el-radio-group',
      title: '单选框',
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
        { label: '选项3', value: '3' },
      ]
    }
  },
  {
    name: '多选框组',
    icon: 'share',
    element: {
      type: 'el-checkbox-group',
      title: '多选框',
      value: [],
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
        { label: '选项3', value: '3' },
      ]
    }
  },
  {
    name: '下拉选择框',
    icon: 'menu',
    element: {
      type: 'el-select',
      title: '下拉选择框',
      options: [
        { label: '选项1', value: '1' },
        { label: '选项2', value: '2' },
        { label: '选项3', value: '3' },
      ]
    }
  },
  {
    name: '表格',
    icon: 'files',
    element: {
      type: 'el-table',
      title: '表格',
      value: [],
      header: [],
    }
  },
]

const customComponentList = [
  {
    name: '签约组件',
    icon: 'edit',
    element: {
      fieldNum: 2,
      elemType: 'custom',
      title: '签约方式',
      type: 'sign-upload',
      value: '1',
      urls: [],
      tips: "E签宝正在调用贵司数字证书，点击提交后即可自动完成签约",
      uploadText: "提交确认单",
      templateFileName: '模板文件名',
      templateFileUrl: ""
    }
  }
]

export {
  componentList,
  customComponentList,
}