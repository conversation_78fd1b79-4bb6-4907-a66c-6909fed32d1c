<template>
  <div class="setting-panel-box">
    <slot name="head"></slot>
    <el-tabs
      v-model="activeTab"
      stretch
    >
      <el-tab-pane
        label="表单设置"
        name="form"
      >
        <div class="form-setting-panel">
          <el-form
            :form="form"
            label-width="80px"
          >
            <el-form-item label="标题：">
              <el-input
                v-model="form.title"
                clearable
              ></el-input>
            </el-form-item>
            <el-form-item label="描述：">
              <el-input
                v-model="form.desc"
                clearable
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
      <el-tab-pane
        label="组件设置"
        name="component"
      >
        <div class="component-setting-panel">
          <el-form
            :form="activeElement"
            label-width="120px"
            v-if="activeElement"
          >
            <template v-if="!activeElement.elemType">
              <el-form-item label="标题：">
                <el-input
                  v-model="activeElement.title"
                  clearable
                ></el-input>
              </el-form-item>
              <!-- el-upload -->
              <template v-if="activeElement.type === 'el-upload'">
                <el-form-item label="提示文本：">
                  <el-input
                    v-model="activeElement.descText"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="模板文件：">
                  <file-upload
                    :url.sync="activeElement.templateFileUrl"
                    list-type="text"
                    :options="{ pdf: true, word: true }"
                  >
                    <el-button
                      type="primary"
                      size="mini"
                    >
                      点击上传
                    </el-button>
                  </file-upload>
                </el-form-item>
              </template>
              <!-- input / textarea -->
              <template v-if="activeElement.type.match(/(input|textarea|text)$/)">
                <el-form-item
                  label="占位内容："
                  v-if="activeElement.type == 'el-input' || activeElement.type == 'el-textarea'"
                >
                  <el-input
                    v-model="activeElement.placeholder"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="默认文本：">
                  <el-input
                    v-model="activeElement.value"
                    clearable
                    :type="activeElement.type == 'el-input' ? '' : 'textarea'"
                    :rows="4"
                  ></el-input>
                </el-form-item>
              </template>
              <!-- select/radio/checkbox -->
              <template v-if="activeElement.type.match(/(select|checkbox-group|radio-group)$/)">
                <el-divider>选项</el-divider>
                <div
                  class="element-option-list"
                  ref="optionList"
                >
                  <div
                    class="element-option-item flex-box"
                    v-for="(item, index) in activeElement.options"
                    :key="item.key"
                  >
                    <i class="el-icon-s-operation"></i>
                    <el-input
                      v-model="item.label"
                      placeholder="选项文本"
                      clearable
                    ></el-input>
                    <i
                      class="el-icon-delete"
                      @click="deleteOption(index)"
                    ></i>
                  </div>
                </div>
                <div class="el-option-add">
                  <el-button
                    type="primary"
                    class="el-option-addbtn"
                    @click="addOption"
                  >添加选项</el-button>
                </div>
              </template>
              <!-- table -->
              <template v-if="activeElement.type === 'el-table'">
                <el-divider>列标题</el-divider>
                <el-form-item
                  v-for="(item, index) in activeElement.header"
                  :key="'col' + index"
                  :label="'列标题' + (index)"
                >
                  <div
                    class="flex-box"
                    style="align-items: center;"
                  >
                    <el-input
                      v-model="item.title"
                      clearable
                    ></el-input>
                    <i
                      v-if="index > 0"
                      class="el-icon-delete"
                      style="font-size: 20px; margin: 10px;"
                      @click="operateCol(index)"
                    ></i>
                  </div>
                </el-form-item>
                <div class="el-option-add">

                  <el-button
                    type="primary"
                    class="el-option-addbtn"
                    @click="operateCol()"
                  >添加列标题</el-button>
                </div>

                <el-divider>行标题</el-divider>
                <el-form-item
                  v-for="(item, index) in activeElement.value"
                  :key="'row' + index"
                  :label="'行标题' + (index)"
                >
                  <div
                    class="flex-box"
                    style="align-items: center;"
                  >
                    <el-input
                      class="flex-item"
                      v-model="item.rowTitle"
                      clearable
                    ></el-input>
                    <i
                      class="el-icon-delete"
                      style="font-size: 20px; margin: 10px;"
                      @click="operateRow(index)"
                    ></i>
                  </div>
                </el-form-item>
                <div class="el-option-add">
                  <el-button
                    type="primary"
                    class="el-option-addbtn"
                    @click="operateRow()"
                  >添加行标题</el-button>
                </div>

              </template>
            </template>
            <template v-else>
              <!-- sign-upload -->
              <template v-if="activeElement.type == 'sign-upload'">
                <el-form-item label="标题：">
                  <el-input
                    v-model="activeElement.title"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="上传标题：">
                  <el-input
                    v-model="activeElement.uploadText"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="线上签约提示：">
                  <el-input
                    rows="5"
                    type="textarea"
                    v-model="activeElement.tips"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="模板文件：">
                  <file-upload
                    :url.sync="activeElement.templateFileUrl"
                    list-type="text"
                    :options="{ pdf: true, word: true }"
                  >
                    <el-button
                      type="primary"
                      size="mini"
                    >
                      点击上传
                    </el-button>
                  </file-upload>
                </el-form-item>
                <el-form-item label="模板文件名：">
                  <el-input
                    v-model="activeElement.templateFileName"
                    clearable
                  ></el-input>
                </el-form-item>
              </template>
            </template>
          </el-form>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import FileUpload from '@/components/FileUpload'
  import Sortable from 'sortablejs'
  export default {
    name: 'SettingPanel',
    components: {
      FileUpload
    },
    props: ['activeItem', 'form'],
    data() {
      return {
        activeTab: 'form',

        sortInstance: null,
      }
    },
    computed: {
      activeElement() {
        let element = null
        if (this.activeItem) {
          if (Object.keys(this.activeItem).length) {
            element = this.activeItem.element
          }
        }
        return element
      },
    },
    watch: {
      activeElement: {
        immediate: true,
        handler(val) {
          if (!val) return
          this.activeTab = 'component';
          if (val.type.match(/(select|checkbox-group|radio-group)$/)) {
            for (let i = 0; i < val.options.length; i++) {
              val.options[i].key = Date.now() + '-' + i
            }
            this.$nextTick(() => {
              this.initSortable()
            })
          }
        },
      }
    },
    methods: {
      addOption() {
        let len = this.activeElement.options.length
        this.activeElement.options.push({
          label: `选项${len + 1}`,
          value: `${len + 1}`,
          key: Date.now() + '-' + len,
        })
      },
      deleteOption(index) {
        this.activeElement.options.splice(index, 1)
      },
      initSortable() {
        if (this.sortInstance) {
          this.sortInstance.destroy()
        }
        this.sortInstance = new Sortable(this.$refs.optionList, {
          handle: '.el-icon-s-operation',
          animation: 150,
          onEnd: (evt) => {
            let index = evt.oldIndex
            let newIndex = evt.newIndex
            let item = this.activeElement.options.splice(index, 1)[0]
            this.activeElement.options.splice(newIndex, 0, item)
          },
        })
      },
      operateCol(index) {
        let len = index !== undefined ? index : this.activeElement.header.length
        if (len == index) {
          // 删除操作
          let headerData = this.activeElement.header.splice(len, 1)[0]
          this.activeElement.value.forEach((item) => {
            this.$delete(item, headerData.prop)
          })
        } else {
          // 添加操作
          this.activeElement.header.push({
            title: `列${len}`,
            prop: len == 0 ? 'rowTitle' : `${len}`,
          })
          this.activeElement.value.forEach((item) => {
            this.$set(item, len == 0 ? 'rowTitle' : `${len}`, '')
          })
        }
      },
      operateRow(index) {
        let len = index !== undefined ? index : this.activeElement.value.length
        if (len == index) {
          // 删除
          this.activeElement.value.splice(len, 1)
        } else {
          let rowData = {
            rowTitle: `行${len}`,
          }
          for (let i = 0; i < this.activeElement.header.length; i++) {
            rowData[this.activeElement.header[i].prop] = ''
          }
          this.activeElement.value.push(rowData)
        }
      },
      switchPanel(panel) {
        // 外部调用切换面板
        this.activeTab = panel
      },
    }
  }
</script>

<style lang="scss" scoped>
  .form-setting-panel {
    padding-right: 20px;
  }
  .component-setting-panel {
    padding: 0 20px;
  }

  .element-option-item {
    align-items: center;
    margin: 10px 0;
    .el-icon-s-operation {
      margin: 10px;
      font-size: 20px;
      cursor: move;
    }
    .el-icon-delete {
      margin: 10px;
      font-size: 20px;
    }
  }
  .el-option-add {
    margin: 0 10px;
    &btn {
      width: 100%;
    }
  }
</style>