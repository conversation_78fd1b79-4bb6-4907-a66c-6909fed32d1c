<template>
  <div class="workform-generate-box" v-loading="loading">
    <div class="workform-comp-box">
      <el-divider>常用组件</el-divider>
      <div class="workform-comp-list">
        <div
          class="workform-comp-item"
          v-for="(item, index) in componentList"
          :key="index"
        >
          <div class="workform-comp-body flex-box text-center">
            <div class="workform-comp-icon">
              <i :class="'el-icon-' + item.icon"></i>
            </div>
            <div class="workform-comp-name">
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
      <el-divider>定制组件</el-divider>
      <div class="workform-comp-list">
        <div
          class="workform-comp-item"
          v-for="(item, index) in customComponentList"
          :key="index"
        >
          <div class="workform-comp-body flex-box text-center">
            <div class="workform-comp-icon">
              <i :class="'el-icon-' + item.icon"></i>
            </div>
            <div class="workform-comp-name">
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 中间面板 -->
    <el-form
      label-width="150px"
      :model="dragForm"
      class="workform-box"
    >
      <div class="text-center">
        <h1>{{ dragForm.title }}</h1>
        <h6>{{ dragForm.desc }}</h6>
      </div>

      <div class="workform-playground-box">
        <div
          class="workform-element-item"
          :class="{
            active: item.formId == tempActiveId
          }"
          v-for="(item) in workformItemList"
          :key="item.formId"
        >
          <drawing-item
            :item="item"
            :form="dragForm"
            @change="onItemChange"
            @delete="onItemDelete"
            @click.native="(e) => drawItemClick(e, item)"
          ></drawing-item>
        </div>
      </div>
    </el-form>
    <!-- 右侧属性面板 -->
    <div class="workform-comp-setting">
      <panel
        ref="panel"
        :form="dragForm"
        :active-item="tempActive"
      >
        <template #head>
          <div class="panel-head-box text-right">
            <el-button @click="cancel">取消</el-button>
            <el-button
              type="primary"
              style="margin-left: 20px;"
              @click="createWorkOrder"
            >发布</el-button>
          </div>
        </template>
      </panel>
    </div>
  </div>
</template>

<script>
  /**
   * 此程序全靠bug运行，请不要随意修改
   * 我佛保佑你能运行
   */

  import Sortable from 'sortablejs'
  import { componentList, customComponentList } from './setting/generate'
  import { createWorkOrder } from '@/api/workform'
  import drawingItem from './drawing/item'
  import Panel from './setting/panel';


  let globalId = 1;


  // 默认表格规格
  const COLUMN_NUM = 4;
  const ROW_NUM = 3;

  export default {
    name: 'waitingHandleWordlistGenerate',
    components: {
      drawingItem,
      Panel
    },

    data() {
      return {
        workOrder: {}, // session读取的上一页面结果
        componentList,
        customComponentList,

        workformItemList: [],

        componentSort: {},
        playgroundSort: {},

        dragForm: {
          title: '表单标题文本',
          desc: '表单描述文本',
        },

        tempActive: {}, // 当前拖拽板激活的元素
        tempActiveId: 0,

        loading: false,
      }
    },
    mounted() {
      this.initSort()
      if (sessionStorage.getItem('workOrder')) {
        this.workOrder = JSON.parse(sessionStorage.getItem('workOrder'))
      }
    },
    beforeDestroy() {
      sessionStorage.removeItem('workOrder')
    },
    methods: {
      initSort() {
        let list = [...document.getElementsByClassName('workform-comp-list')]
        let playground = document.querySelector('.workform-playground-box')
        let vm = this;
        list.map((dom, index) => {
          this.componentSort[index] = new Sortable(dom, {
            group: {
              name: 'workform',
              pull: 'clone',
              put: false,
            },
            sort: false,
            handler: '.workform-comp-item',
            onStart() {
              vm.drawActiveList = index
            }
          })
        })
        this.playgroundSort = new Sortable(playground, {
          group: {
            name: 'workform',
            pull: false,
          },
          animation: 340,
          onAdd(evt) {
            vm.removeIem(evt.item)
            let list = vm.drawActiveList == 0 ? vm.componentList : vm.customComponentList
            let componentItem = vm.cloneItem(list[evt.oldIndex])
            vm.spliceList(evt.newIndex, 0, componentItem)
          },
          onUpdate(evt) {
            vm.updatePosition(evt.oldIndex, evt.newIndex)
          },
          onEnd() {
          }
        })
      },
      removeIem(item) {
        if (item.parentElement) {
          item.parentElement.removeChild(item)
        }
      },
      cloneItem(item) {
        let tempActive = JSON.parse(JSON.stringify(item))
        if (tempActive.element.fieldNum && tempActive.element.fieldNum > 0) {
          tempActive.formId = globalId
          tempActive.element.field = new Array(tempActive.element.fieldNum).fill(null).map(() => {
            return `field${globalId++}`
          })
        } else {
          tempActive.formId = globalId
          tempActive.element.field = 'field' + globalId++
        }
        tempActive.element.value = tempActive.element.value || ''
        this.tempActive = tempActive
        this.tempActiveId = tempActive.formId

        if (tempActive.element.type === 'el-table') {
          // 手动生成表格 行标题 和 列标题
          tempActive.element.header = new Array(COLUMN_NUM).fill(null).map((item, index) => {
            return {
              title: `列${index}`,
              prop: index == 0 ? 'rowTitle' : `${index}`,
            }
          })
          tempActive.element.value = new Array(ROW_NUM).fill({}).map((item, index) => {
            let rowData = {}
            for (let i = 1; i < COLUMN_NUM; i++) {
              rowData[`${i}`] = ''
            }
            return {
              ...rowData,
              rowTitle: `行${index}`,
            }
          })
        }
        return tempActive
      },
      spliceList() {
        this.workformItemList.splice(...arguments)
      },
      updatePosition(oldIndex, newIndex) {
        this.workformItemList.splice(newIndex, 0, this.workformItemList.splice(oldIndex, 1)[0])
      },
      onItemChange(item, field, value) {
        // todo 考虑是否要对dragForm进行更新
        if (item.element.type !== 'el-table') {
          item.element['value'] = value
        }
      },
      onItemDelete(item) {
        this.spliceList(this.workformItemList.indexOf(item), 1)
        this.tempActive = null
        this.tempActiveId = 0
      },
      drawItemClick(e, item) {
        e.stopPropagation()
        if (e.target.matches('i.el-icon-delete')) {
          this.tempActive = null
          this.tempActiveId = 0
          this.$refs.panel.switchPanel('form')
          return
        }
        this.tempActive = item
        this.tempActiveId = item.formId
      },
      cancel() {
        this.$router.push('/audit/workform')
      },
      async createWorkOrder() {
        this.loading = true;
        let form = {
          ...this.dragForm,
          component: this.workformItemList.map(item => item.element),
        }
        try {
          const { data } = await createWorkOrder({
            ...this.workOrder,
            formJson: JSON.stringify(form),
          })
          data && this.$message.success(data)
          this.cancel()
        } finally {
          this.loading = false;
        }
      },
    }
  }
</script>

<style lang="scss" scoped>
  .workform-comp-box {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 220px;
    overflow: auto;
    background: #fff;
    border-right: 1px solid #e5e5e5;
    .workform-comp {
      &-item {
        margin: 10px 0;
        transition: transform 0ms !important;
        cursor: move;
        background: #f6f7ff;
      }
      &-body {
        justify-content: center;
        padding: 16px 0;
        border: 1px dashed transparent;
        &:hover {
          border: 1px dashed #444;
        }
      }
      &-icon {
        margin-right: 2px;
      }
      &-name {
        margin-left: 2px;
      }
    }
  }
  .workform-comp-setting {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 400px;
    overflow: auto;
    background: #fff;
    border-left: 1px solid #e5e5e5;
  }

  .workform-box {
    margin: 0 400px 0 220px;
    padding: 20px 0;
    background: #fff;
  }
  .workform-playground-box {
    height: calc(100vh - 200px);
    overflow: auto;
    .workform-comp-item.sortable-ghost {
      height: 60px;
    }
    .sortable-ghost {
      opacity: 0.8;
      color: #fff !important;
      background: #42b983 !important;
    }

    // 生成表单组件
    .workform-element-item.active {
      background: rgba(135, 206, 235, 0.5);
    }
  }

  // panel
  .panel-head-box {
    padding: 10px;
  }
</style>