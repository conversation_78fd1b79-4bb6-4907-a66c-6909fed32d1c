<template>
  <el-dialog
    :visible.sync="visible"
    :before-close="beforeClose"
  >
    <div class="card-container">
      <div class="card-list">
        <div
          class="card-item"
          v-for="(step, index) in list"
          :key="index"
        >
          <div v-show="index < 3 || showAllNode">
            <div class="card-name">
              {{ step.stepName }}
              <div class="card-status-icon"></div>
            </div>
            <div class="card-info">
              <div class="card-detail" v-if="step.commonFlowLogList.length">
                <div
                  class="card-detail-item"
                  v-for="(item, index) in step.commonFlowLogList"
                  :key="index"
                >
                  <div class="card-detial-desc">
                    <div class="card-detail-operate">
                      <span>{{ item.handlerName }}</span>
                      -
                      <el-tooltip :disabled="item.taskName !== '编辑信息'">
                        <span>{{ item.taskName }}</span>
                        <template v-slot:content>
                          <div
                            v-for="(value, prop) in item.extInfo"
                            :key="prop"
                          >
                            <div class="diff-content">{{ explainDiff(value, prop) }}</div>
                          </div>
                        </template>
                      </el-tooltip>
                      <el-tag
                        :type="getTagType(item.status)"
                        v-if="item.status"
                        style="margin-left: 8px;"
                      >
                        <span class="color-gray">{{ $dictCode('HandleStatus', item.status).desc }}</span>
                      </el-tag>
                    </div>
                    <div
                      class="card-detail-approval color-gray"
                      v-if="item.approvalOpinion"
                      v-html="item.approvalOpinion"
                    ></div>
                  </div>
                </div>
                <div
                  class="clearfix color-gray"
                  style="margin-top: 16px;"
                >
                  <div class="card-detail-time float-l">
                    {{ step.commonFlowLogList[0].updateTime }}
                  </div>
                  <div class="card-detail-spend float-r">
                    耗时 {{ formatDuration(step.spentTime) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="card-item"
          v-if="!showAllNode && list.length > 3"
        >
          <div
            class="card-name func-content"
            @click="() => {showAllNode = true}"
          >
            查看完整流程
            <div class="card-status-icon"></div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
  import { formatDuration } from '@/utils'
  import { getDetail } from '@/api/flow'
  export default {
    data() {
      return {
        showAllNode: false,
        list: [],
        visible: false,
        commonFlowId: ""
      }
    },
    methods: {
      formatDuration,
      toggleVisible(commonFlowId) {
        this.commonFlowId = commonFlowId
        this.getDetail()
      },
      async getDetail() {
        const { data } = await getDetail({ commonFlowId: this.commonFlowId })
        this.list = data
        this.visible = !this.visible
      },
      explainDiff(data, prop) {
        let result = '';
        if (Array.isArray(data)) {
          result = `${prop} 中`
          data.forEach((item, index) => {
            result += this.explainDiff(item, index + 1)
          })
        } else {
          if (!data) return '';
          if (data.newValue || data['新']) {
            result = `将${typeof prop == 'number' ? '' : prop + '由'} ${data.oldValue || data['旧'] || '空'} 改成 ${data.newValue || data['新'] || '空'}`
          } else if (typeof prop == 'number') {
            result = `第${prop}项`
            let keys = Object.keys(data);
            keys.forEach(key => {
              result += this.explainDiff(data[key], key);
            })
          } else {
            result = `将 ${prop}由 ${data.oldValue || data['旧'] || '空'} 改成 ${data.newValue || data['新'] || '空'}`
          }
        }
        return result;
      },
      getTagType(status) {
        switch (Number(status)) {
          case 100:
            return 'success';
          case 101:
            return 'danger';
          case 103:
            return 'info';
          default:
            return ''
        }
      },
      beforeClose(done) {
        done()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .card-list {
    .card-name {
      position: relative;
      padding-left: 20px;
      font-weight: bolder;
    }

    .card-status-icon {
      position: absolute;
      left: 0;
      top: 50%;
      bottom: 0;
      transform: translate3d(0, -50%, 0);
      width: 10px;
      height: 10px;
      border-radius: 10px;
      background: #ddd;
    }

    .card-item {
      position: relative;
      &::after {
        content: "";
        display: block;
        position: absolute;
        top: 4px;
        bottom: -5px;
        left: 4px;
        width: 2px;
        background: #ddd;
      }

      &:last-child::after {
        background: transparent;
      }
    }

    .card-info {
      position: relative;
      padding: 16px 0 16px 32px;
    }
    .card-detail {
      width: 70%;
      padding: 0 16px 16px;
      box-shadow: 0 0 5px 3px #ddd;
      overflow: auto;
    }

    .card-detail-item {
      margin-top: 16px;
    }

    .card-detail-approval {
      margin-top: 4px;
      &::v-deep img {
        width: 70px;
        height: 70px;
        cursor: pointer;
      }
    }
  }
</style>