<template>
  <div class="page-container">
    <el-form
      :model="form"
      label-width="150px"
      :rules="rules"
      ref="form"
      autocomplete="off"
    >
      <el-form-item label="商户名：">{{ userData.staff.mchName }}</el-form-item>

      <el-form-item
        label="账户类型："
        prop="channelNo"
      >
        <el-select
          clearable
          v-model="payChannelNo"
          @change="handleChannelChange"
        >
          <el-option
            v-for="(item, index) in channelMap"
            :key="index"
            :disabled="item.balance === '未开通'"
            :value="item.payChannelNo"
            :label="item.payChannelName"
          ></el-option>
        </el-select>
        <!-- <el-radio-group v-model="selectChannel" @change="handleChannelChange">
          <template v-for="(item, index) in channelMap">
            <el-radio
              :key="index"
              :label="item"
            >
              {{ item.payChannelName }}
            </el-radio>
          </template>
        </el-radio-group> -->
      </el-form-item>

      <!-- <el-form-item label="账户类型：" prop="channelType">
        {{ $dictCode('ChannelTypeEnum', form.channelType).desc }}
      </el-form-item> -->

      <el-form-item label="可用余额：">
        <span
          class="color-warning"
        >
          {{ balanceAmount }}
        </span> 元
      </el-form-item>

      <el-form-item
        label="提现金额："
        prop="amount"
      >
        <el-input
          style="width: 300px"
          :value="form.amount"
          :readonly="readonly"
          @input="handleRateInput(form, 'amount', $event)"
          @focus="readonly = false"
          @blur="readonly = true"
          type="text"
          clearable
          autocomplete="off"
        >
          <span slot="append">元</span>
        </el-input>
        <el-button
          type="text"
          @click="withdrawAll"
        >全部提现</el-button>
        <p class="color-gray form-tip">本次提现最低限额为 <span class="color-warning">0.1</span> 元</p>
        <p
          class="form-tip"
          v-show="moneyLack"
        >可提现金额不足，本次无法提现</p>
      </el-form-item>

      <div>
        <el-form-item label="到账账户：" v-show="isAli || isWx || isBank">
          <div
            class="account-type-item"
          >
            <div class="account-type-name">
              <template v-if="isBank">
                <svg-icon
                  iconClass="payicon_withdraw"
                  :iocnStyle="{fontSize: '20px'}"
                >
                </svg-icon>
                <div class="account-name-detail account-name">银行卡</div>
                <div class="account-name-detail color-gray">{{ bankAccount.bankName }}</div>
                <div class="account-name-detail color-gray">{{ bankAccount.accountNo }}</div>
              </template>
              <template v-else-if="isAli">
                <svg-icon
                  iconClass="alipay"
                  :iocnStyle="{fontSize: '20px'}"
                >
                </svg-icon>
                <div class="account-name-detail account-name">支付宝</div>
                <div class="account-name-detail color-gray">{{ userData.staff.mchName }}</div>
                <div class="account-name-detail color-gray">****</div>
              </template>
              <template v-else-if="isWx">
                <svg-icon
                  iconClass="payicon_withdraw"
                  :iocnStyle="{fontSize: '20px'}"
                >
                </svg-icon>
                <div class="account-name-detail account-name">银行卡</div>
                <div class="account-name-detail color-gray">{{ userData.staff.mchName }}</div>
                <div class="account-name-detail color-gray">****</div>
              </template>
            </div>
          </div>

        </el-form-item>

        <el-form-item
          label="支付密码："
          prop="payPasswd"
        >
          <el-input
            v-model="form.payPasswd"
            type="password"
            autocomplete="new-password"
            style="width: 300px"
            :readonly="pwdReadOnly"
            @focus="pwdReadOnly = false"
            @blur="pwdReadOnly = true"
          ></el-input>
        </el-form-item>

        <el-form-item label="备注：">
          <el-input
            type="textarea"
            :rows="4"
            v-model="form.remark"
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            @click="withdraw"
            :loading="loading"
          >提现</el-button>
        </el-form-item>
      </div>

    </el-form>
  </div>
</template>

<script>
  import { getMerchantMainstay, getBankAccount } from '@/api/merchant'
  import { encryptParam } from '@/utils/jsencrypt';
  import { getKey } from '@/utils/publicKey'
  import { withdraw } from '@/api/order';

  export default {
    name: 'WithdrawPage',
    data() {
      return {
        form: {
          mainstayNo: '',
          merchantType: 101,
          // 支付宝通道
          channelNo: '',

          amount: '',
          remark: '',
          payPasswd: '',
        },

        rules: {
          mainstayNo: [{ required: true, trigger: 'change', message: '请选择代征主体' }],
          channelNo: [{ required: true, trigger: 'blur', message: '请选择账户类型' }],
          amount: [{ required: true, trigger: 'blur', message: '请输入提现金额' }],
          payPasswd: [{ required: true, trigger: 'blur', message: '请输入支付密码' }],
        },

        mainstayMoneyMap: {},
        channelMap: [], // 代征主体下通道账户信息

        moneyLack: false,
        loading: false,
        readonly: true,
        pwdReadOnly: true,

        bankAccount: {},

        // selectChannel: {},
        payChannelNo: '',
        balanceAmount: '',
      }
    },
    computed: {
      selectChannel() {
        if (!this.channelMap) {
          return {}
        }
        const selectChannel = this.channelMap.find(item => item.payChannelNo === this.payChannelNo)
        if (selectChannel) {
          return selectChannel
        }
        return {}
      },
      isAli() {
        return this.form.channelNo === 'ALIPAY'
      },
      isBank() {
        return ['JOINPAY', 'JOINPAY_JXH'].includes(this.form.channelNo)
      },
      
      isWx() {
        return this.form.channelNo === 'WXPAY'
      }
    },
    async mounted() {
      this.form.mainstayNo = this.userData.mchNo;
      this.getMerchantMainstay(this.form.mainstayNo);
      this.getBankAccount()
    },
    methods: {
      withdrawAll() {
        if (isNaN(this.balanceAmount)) {
          // 通道异常等
        } else {
          this.form.amount = this.balanceAmount;
        }
      },
      // 查询通道下的账户信息
      async getMerchantMainstay(mainstayNo) {
        if (!mainstayNo) {
          // 修改到账账户
          this.form.amount = '';
          return;
        }
        const { data } = await getMerchantMainstay(null, {
          mainstayNo,
        });
        this.channelMap = data
      },
      async withdraw() {
        const valid = await this.$refs.form.validate().catch(_ => false);
        if (!valid) return;
        this.loading = true;
        let pwd = this.form.payPasswd;
        const { data } = await withdraw({
          ...this.form,
          payPasswd: encryptParam({
            pwd,
            publicKey: getKey(),
          })
        }).finally(() => {
          this.loading = false;
        });
        this.$message.success('操作成功');
        this.$router.push({
          path: '/account/withdrawRecord',
          query: {
            withdrawNo: data.withdrawNo
          }
        });
      },
      async getBankAccount() {
        const { data } = await getBankAccount();
        this.bankAccount = data;
      },
      handleChannelChange(channel) {
        this.form.amount = ''
        this.form.channelNo = this.selectChannel.payChannelNo
        this.balanceAmount = this.selectChannel.balance
      }
    },
  }
</script>

<style scoped lang="scss">
  .page-container {
    .account-type-item {
      display: inline-block;
      width: 250px;
      padding: 8px;
      border: 1px solid $borderGray;
    }
    .account-type-name {
      position: relative;
      padding-left: 24px;

      .account-name-detail {
        margin-bottom: 8px;
        line-height: normal;
        font-size: 12px;
      }
      .account-name {
        font-size: 15px;
      }

      .svg-icon {
        position: absolute;
        top: 0;
        left: 0;
      }
    }
  }
</style>
