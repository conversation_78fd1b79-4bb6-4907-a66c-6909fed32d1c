<template>
  <el-main>
    <div class="header-block">
      <div class="header-container header-overview">
        <div class="header-title">资产总览</div>
        <el-divider></el-divider>
        <div class="header-content">
          <div>总可用金额（元）</div>
          <div class="account-balance">{{ totalAmount | moneyFormat }}
            <span
              v-if="checkError"
              style="font-size: 13px; color: #f00"
            >（备注：通道查询异常，请稍后重新查询）</span>
          </div>
        </div>
        <div class="header-btn">
          <el-button
            type="primary"
            @click="goWithdraw"
          >提现</el-button>
        </div>
      </div>
      <div class="header-container header-account">
        <div class="header-title">账户信息</div>
        <el-divider></el-divider>
        <div class="account-content-mch">
          <p>商户名：{{ userData.staff.mchName }}</p>
        </div>
      </div>
    </div>

    <div class="content-container">
      <p class="title-container">账户余额查询</p>
      <el-select
        v-model="channelType"
        clearable
        @change="changeChannelType"
      >
        <el-option
          v-for="(item, index) in searchList"
          :key="index"
          :value="item.code"
          :label="item.desc"
        ></el-option>
      </el-select>
      <el-table :data="list">
        <el-table-column
          label="通道"
          width="200"
        >
          <template v-slot="{row}">
            {{ row.payChannelName }}
          </template>
        </el-table-column>

        <el-table-column
          label="可用余额（元）"
          width="250"
          prop="balance"
        >
          <template v-slot="{row, column}">
            <p
              class="color-warning"
              v-if="!isNaN(row[column['property']])"
            >
              {{ row['balance'] | moneyFormat }}
            </p>
            <p
              v-else-if="row[column['property']] == '异常'"
              style="color: #f00"
            >
              {{row[column['property']]}}
            </p>
            <p
              v-else
              style="color: #ccc"
            >
              <span
                v-if="row.payChannelNo == 'ALIPAY'"
                class="func-content"
                @click="submitChannel(row)"
              >(立即开通)</span>
              {{row[column['property']]}}
            </p>
          </template>
        </el-table-column>

        <el-table-column
          label="不可用余额（元）"
          width="250"
          prop="frozenAmount"
        >
          <template v-slot="{row, column}">
            <p
              class="color-warning"
              v-if="!isNaN(row[column['property']])"
            >
              {{ row['frozenAmount'] | moneyFormat }}
            </p>
            <p
              v-else-if="row[column['property']] == '异常'"
              style="color: #f00"
            >
              {{row[column['property']]}}
            </p>
            <p
              v-else
              style="color: #ccc"
            >
              {{row[column['property']]}}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="操作">
          <template v-slot="{row}">
            <router-link
              v-if="row.channelType == 2"
              to="/account/withdraw"
              class="func-content"
            >提现</router-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-main>
</template>

<script>
  import { getMerchantMainstay } from '@/api/merchant';
  import { submitChannel } from '@/api/levy';

  export default {
    name: 'CheckBalance',
    data() {
      return {
        searchForm: {},

        channelType: '',
        totalAmount: 0,
        checkError: false,

        mainstayList: [],

        channelMap: {},
        list: [],
        originList: [],

        searchList: [],
      }
    },
    async mounted() {
      this.searchForm.mainstayNo = this.userData.mchNo;
      this.getMerchantMainstay(this.searchForm.mainstayNo);
    },
    methods: {
      // 查询通道下的账户信息
      async getMerchantMainstay(mainstayNo) {
        if (!mainstayNo) {
          // 修改到账账户
          return;
        }
        const { data } = await getMerchantMainstay(null, {
          mainstayNo,
        });
        this.list = this.originList = data;

        this.list.forEach((i, key) => {
          this.searchList.push({
            code: key,
            desc: i.payChannelName
          })

          if (i.balance === '异常' || i.frozenAmount === '异常') this.checkError = true

          let balance = isNaN(i.balance) ? 0 : i.balance;
          let frozenAmount = isNaN(i.frozenAmount) ? 0 : i.frozenAmount;

          this.totalAmount = (this.totalAmount * 100 + balance * 100 + frozenAmount * 100) / 100;
        })
      },
      goWithdraw() {
        this.$router.push('/account/withdraw')
      },
      changeChannelType(val) {
        if (val === undefined || val === '') {
          this.list = this.originList;
          return;
        }
        this.list = this.originList.filter((item, index) => {
          return index == val
        })
      },
      async submitChannel() {
        try {
          await this.$confirm(`
            <div style="text-align: left;">
              <p>请确认贵司已开通企业支付宝账户，并在点击「立即开通」后跳转支付宝后台，<strong>使用手机端登录同名企业支付宝账户进行扫码开通。</strong></p>
              <p>本次开通后，汇聚智享平台将基于支付宝资金共管授权的产品（安全发），为贵司提供独立的「专属记账本」，用于承载与本次灵活用工场景下的转账需求。</p>
              <p>请注意，此操作仅开通与灵活用工业务合作相关的记账本，在满足业务场景条件下达到安全隔离资金的效果。不在专属记账本内的企业支付宝资金，与本次授权无关。</p>
            </div>
          `, '专属记账本开通确认', {
            dangerouslyUseHTMLString: true,
            center: true,
            confirmButtonText: '立即开通',
            closeOnClickModal: false,
          })
        } catch {
          return
        }
        const { data } = await submitChannel({
          payChannelNo: 'ALIPAY',
          payChannelName: '支付宝',
          channelType: 2,
        })
        console.log('sign response >>> ', data)
        if (data) {
          if (/^http(s)?/.test(data)) {
            window.open(data)
            try {
              await this.$confirm('请在新页面扫描完成开通之后重新刷新页面', '提示', {
                confirmButtonText: '确定',
                closeOnClickModal: false,
                type: 'info',
                showCancelButton: false,
                showClose: false,
              })
            } finally {
              location.reload()
            }
          } else {
            // 已经签约的情况
            this.list = []
            this.$message.error('已经签约，正在重新查询，请稍候')
            this.getMerchantMainstay(this.searchForm.mainstayNo);
          }
        }
      },
    },
  }
</script>

<style scoped lang="scss">
  .title-container {
    font-size: 25px;
  }
  .header-block {
    display: flex;
  }
  .header-container {
    background: #fff;

    .header-title {
      padding: 24px 24px 0 24px;
      font-size: 20px;
      font-weight: bolder;
    }

    .header-content {
      padding: 0 24px 40px;
    }

    &.header-overview {
      flex: 0.7;
    }
    &.header-account {
      flex: 0.3;
      margin-left: 16px;
    }
  }

  .header-account {
    .account-content-mch {
      padding: 0 24px;
    }

    .account-content {
      display: flex;
    }
    .account-content-item {
      flex: 1;
      width: calc(100% / 3);
      margin-bottom: 8px;
    }
    .account-content-icon {
      display: inline-block;
      background: $mainColor;
      margin-bottom: 4px;
      border-radius: 8px;

      .svg-icon {
        width: 50px;
        height: 45px;
        padding: 5px;
      }
    }
  }
  .header-overview {
    .header-btn {
      padding: 0 24px 24px;
    }
  }
  .content-container {
    margin-top: 32px;

    .el-table {
      margin-top: 16px;
    }
  }
  .account-balance {
    font-size: 30px;
    color: #ffa500;
  }
</style>
