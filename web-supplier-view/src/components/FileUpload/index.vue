<template>
  <el-upload
    v-toggle:[limit]="uploadFileList"
    class="upload-demo"
    :action="remoteAction"
    :headers="uploadHeader"
    :list-type="listType"
    :accept="totalAccept"
    :limit="max"
    :file-list="uploadFileList"
    :before-upload="beforeUpload"
    :on-remove="(file, fileList) => onRemove(fileList)"
    :on-success="(response, file, fileList) => onSuccess(response, file, fileList)"
    :on-preview="handlePreview"
    v-bind="$attrs">
    <template v-slot:file="{file}">
      <div
        class="fileBg"
        v-if="listType === 'picture-card' && !isImageFile(file)">
        <span class="file-name">{{ file.name || name }}</span>
        <span class="el-upload-list__item-actions">
           <span
             v-if="showPreview"
             class="el-upload-list__item-preview"
             @click="handlePreview(file)"
           >
            <i class="el-icon-zoom-in"></i>
          </span>
          <span
            class="el-upload-list__item-delete"
            @click="handleFileRemove(file)"
          >
            <i class="el-icon-delete"></i>
          </span>
        </span>
      </div>
    </template>
    <slot
      v-if="showTip"
      name="tip"
      slot="tip">
      <div>支持扩展名：{{ totalAcceptFormat }}
        <slot name="tip-append"></slot>
      </div>
    </slot>
    <template v-slot:trigger>
      <slot name="trigger">
        <i
          v-if="listType === 'picture-card'"
          class="el-icon-plus avatar-uploader-icon"></i>
        <span v-else>点击上传</span>
      </slot>
    </template>
  </el-upload>
</template>

<script>
export default {
  name: 'FileUpload',
  props: {
    listType: {
      type: String,
      default: 'picture-card'
    },
    max: {
      type: Number,
    },
    options: {
      type: Object,
      default: () => ({})
    },
    url: {
      type: [String, Array],
      default: '',
    },
    urls: {
      type: Array,
      default: () => []
    },
    accept: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '文件'
    },
    showPreview: {
      type: Boolean,
      default: true,
    },
    showTip: {
      type: Boolean,
      default: true,
    },
    action: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      uploadFileList: [],
    }
  },
  computed: {
    validateOption() {
      return Object.assign({}, {
        img: true,
        size: 6,
      }, this.options)
    },
    totalAccept() {
      let accept = ''
      if (this.validateOption.img) {
        accept += ',.jpg,.jpeg,.png'
      }
      if (this.validateOption.pdf) {
        accept += ',.pdf'
      }
      if (this.validateOption.word) {
        accept += ',.doc,.docx'
      }
      if (this.validateOption.excel) {
        accept += ',.xls,.xlsx'
      }
      accept = this.accept + accept
      return accept
    },
    totalAcceptFormat() {
      return this.totalAccept.split(',').filter(i => !!i).join(' ')
    },
    limit() {
      if (this.max === undefined) {
        return 0
      } else {
        return this.max
      }
    },
    remoteAction() {
      return this.baseUrl + (this.action || '/file/upload')
    },
  },
  watch: {
    url: {
      immediate: true,
      handler(val) {
        let fileList = []
        if (val) {
          fileList = [val]
        }
        this.handleFileUrlsChange(fileList)
      },
    },

    urls: {
      immediate: true,
      handler(val) {
        let fileList = []
        if (val && val.length > 0) {
          fileList = val
        } 
        this.handleFileUrlsChange(fileList)
      }
    }

  },
  methods: {
    beforeUpload() {
      this.$emit('beforeUpload')
      return this.validateUploadFile(this.validateOption)
    },
    onSuccess(response, file, fileList) {
      if (this.max > 1 || !this.max) {
        let urls = fileList.map(item => {
          if (item.response) {
            return item.response.data
          } else {
            return item.origin
          }
        })
        this.$emit('update:urls', urls)
        this.$emit('change', urls)
      } else {
        this.$emit('update:url', file.response.data)
        this.$emit('change', file.response.data)
      }
      this.$emit('update:name', file.name)
    },
    onRemove(fileList) {
      if (this.max > 1 || !this.max) {
        let urls = fileList.map(item => {
          if (item.response) {
            return item.response.data
          } else {
            return item.origin
          }
        })
        this.$emit('update:urls', urls)
        this.$emit('change', urls)
      } else {
        this.$emit('update:url', '')
        this.$emit('change', '')
      }
    },
    isImageFile(file) {
      if (file.response && file.response.data) {
        return this.isImg(file.response.data)
      } else {
        return this.isImg(file.origin) || this.isImg(file.name)
      }
    },
    async handlePreview(file) {
      let url = file.response ? file.response.data : file.origin;
      const fileMsg = await this.formatFileMsg(url)
      if (this.isImg(url)) {
        this.$preview(fileMsg.fileUrl)
      } else {
        let suffix = url.split('.').pop();
        this.downloadFile(fileMsg.fileUrl, this.name, suffix);
      }
    },
    handleFileRemove(file) {
      this.uploadFileList = this.uploadFileList.filter(item => {
        return item.uid !== file.uid
      })
      this.onRemove(this.uploadFileList)
    },
    async handleFileUrlsChange(fileList) {
      const formatFileList = []
      for (let i = 0; i < fileList.length; i++) {
        const file = fileList[i];
        const fileMsg = await this.formatFileMsg(file)
        formatFileList.push({
          name: this.name,
          url: fileMsg.fileUrl,
          origin: file
        })
      }
      this.uploadFileList = formatFileList
    }
  },
}
</script>

<style
  scoped
  lang="scss">

</style>
