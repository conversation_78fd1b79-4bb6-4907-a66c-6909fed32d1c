<template>
  <div :class="{fullscreen:fullscreen}" class="tinymce-container" :style="{width:containerWidth}">
    <textarea :id="tinymceId" class="tinymce-textarea"></textarea>
  </div>
</template>

<script>
  /**
   * docs:
   * https://panjiachen.github.io/vue-element-admin-site/feature/component/rich-editor.html#tinymce
   */
  import toolbar from './toolbar'
  import tinymce from 'tinymce'
  import 'tinymce/themes/silver/theme'
  import 'tinymce/skins/ui/oxide/skin.min.css'
  import 'tinymce/icons/default'
  import 'tinymce/plugins/image'
  import 'tinymce/plugins/link'
  import 'tinymce/plugins/media'
  import 'tinymce/plugins/lists'
  import 'tinymce/plugins/wordcount'

  tinymce.baseURL = `${window.location.origin}/tinymce`;

  import { uploadFile } from "@/api/common";

  export default {
    name: 'Tinym<PERSON>',
    props: {
      id: {
        type: String,
        default: function() {
          return 'vue-tinymce-' + +new Date() + ((Math.random() * 1000).toFixed(0) + '')
        }
      },
      value: {
        type: String,
        default: ''
      },
      toolbar: {
        type: [Array, String],
        required: false,
        default() {
          return []
        }
      },
      menubar: {
        type: [String, Boolean],
        default: 'file edit insert view format'
      },
      height: {
        type: [Number, String],
        required: false,
        default: 360
      },
      width: {
        type: [Number, String],
        required: false,
        default: 'auto'
      },
      showImgUpload: {
        type: Boolean,
        default: true,
      }
    },
    data() {
      return {
        hasChange: false,
        hasInit: false,
        tinymceId: this.id,
        fullscreen: false,
        languageTypeList: {
          'en': 'en',
          'zh': 'zh_CN',
        }
      }
    },
    computed: {
      containerWidth() {
        const width = this.width
        if (/^[\d]+(\.[\d]+)?$/.test(width)) { // matches `100`, `'100'`
          return `${width}px`
        }
        return width
      }
    },
    watch: {
      value(val) {
        if (!this.hasChange && this.hasInit) {
          this.$nextTick(() =>
            tinymce.get(this.tinymceId).setContent(val || ''))
        }
      }
    },
    mounted() {
      this.init()
    },
    destroyed() {
      this.destroyTinymce()
    },
    methods: {
      init() {
        // dynamic load tinymce from cdn
        this.initTinymce()
      },
      initTinymce() {
        const _this = this;
        tinymce.init({
          selector: `#${this.tinymceId}`,
          language: this.languageTypeList['zh'],
          language_url: 'tinymce/langs/zh_CN.js',
          skin: 'oxide',
          skin_url: 'tinymce/skins/ui/oxide',
          content_css: 'tinymce/skins/content/default/content.css',
          height: this.height,
          body_class: 'panel-body ',
          object_resizing: false,
          // toolbar: this.toolbar.length > 0 ? this.toolbar : toolbar,
          toolbar: this.toolbar === 'simple' ? ['image link bullist numlist'] : toolbar,
          menubar: this.menubar,
          plugins: ['image', 'link', 'lists', 'wordcount'],
          images_upload_handler: this.uploadFile,
          end_container_on_empty_block: true,
          powerpaste_word_import: 'clean',
          code_dialog_height: 450,
          code_dialog_width: 1000,
          advlist_bullet_styles: 'square',
          advlist_number_styles: 'default',
          imagetools_cors_hosts: [],
          default_link_target: '_blank',
          link_title: false,
          nonbreaking_force_tab: true, // inserting nonbreaking space &nbsp; need Nonbreaking Space Plugin
          init_instance_callback: editor => {
            if (_this.value) {
              editor.setContent(_this.value)
            }
            _this.hasInit = true
            editor.on('NodeChange Change KeyUp SetContent', () => {
              this.hasChange = true
              this.$emit('input', editor.getContent())
            })
          },
          setup(editor) {
            editor.on('FullscreenStateChanged', (e) => {
              _this.fullscreen = e.state
            })
          },
          // it will try to keep these URLs intact
          // https://www.tiny.cloud/docs-3x/reference/configuration/Configuration3x@convert_urls/
          // https://stackoverflow.com/questions/5196205/disable-tinymce-absolute-to-relative-url-conversions
          convert_urls: false
        })
      },
      destroyTinymce() {
        const tinymceItem = tinymce.get(this.tinymceId)
        if (this.fullscreen) {
          tinymceItem.execCommand('mceFullScreen')
        }
        if (tinymceItem) {
          tinymceItem.destroy()
        }
      },
      setContent(value) {
        tinymce.get(this.tinymceId).setContent(value)
      },
      getContent() {
        tinymce.get(this.tinymceId).getContent()
      },
      async uploadFile(blobInfo, successCb, failCb) {
        let formData = new FormData();
        formData.append('file', blobInfo.blob(), blobInfo.filename());
        const { data } = await uploadFile(formData);
        successCb(this.fileUrl + data);
      }
    }
  }
</script>

<style lang="scss" scoped>
  .tinymce-container {
    position: relative;
    line-height: normal;
  }

  .tinymce-container {
    ::v-deep {
      .mce-fullscreen {
        z-index: 10000;
      }
    }
  }

  .tinymce-textarea {
    visibility: hidden;
    z-index: -1;
  }
</style>
