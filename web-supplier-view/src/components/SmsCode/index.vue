<template>
  <el-button type="primary" @click="onSendSms" :disabled="checkPhone(phone )|| disable || timer !== null">{{smsText}}</el-button>
</template>

<script>
  import {sendSmsCode} from "../../api/common";

  export default {
    name: "index",
    props: {
      phone: String,
      disable: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        smsText: '获取短信验证码',
        timer: null
      }
    },
    methods: {
      onSendSms() {
        if(this.timer) {
          return;
        }
        sendSmsCode({phone: this.phone})
          .then(({ data }) => {
            let num = 59;
            this.timer = setInterval(() => {
              if(num == 0) {
                clearInterval(this.timer);
                this.timer = null;
                this.smsText = '获取短信验证码';
              } else {
                this.smsText = num + 's';
                num--;
              }
            }, 1000)
          });
      },
      checkPhone(phone) {
        return !(/^1\d{10}$/.test(phone));
      },
    }
  }
</script>

<style lang="scss" scoped>
  .el-button {
    width: 140px;
    box-sizing: border-box;
    color: $mainColor !important;

    &.is-disabled {
      color: #999 !important;
    }
  }
</style>
