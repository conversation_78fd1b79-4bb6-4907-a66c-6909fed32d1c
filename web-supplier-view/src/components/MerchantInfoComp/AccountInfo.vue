<template>
  <div class="merchant-info-container">
    <el-form
      ref="form"
      label-width="350px"
      class="block-form"
      :disabled="disabled"
    >
      <div class="bankCard_info" v-if="form.bankAccount">
        <p class="subTitle">银行卡信息</p>
        <el-form-item
          label="银行卡号："
          prop="accountNo"
        >
          <el-input v-model="form.bankAccount.accountNo"></el-input>
        </el-form-item>
        <el-form-item
          label="联行号："
          prop="bankChannelNo"
        >
          <el-input
            v-model="form.bankAccount.bankChannelNo"
            prefix-icon="el-icon-search"
            @click.native="searchBankNumberInfo"
          >
          </el-input>
        </el-form-item>
        <el-form-item
            label="开户银行："
            prop="bankName"
          >
            <el-input
              v-model="form.bankAccount.bankName"
              disabled
            ></el-input>
          </el-form-item>
      </div>

    </el-form>

    <!--  表格弹窗  -->
    <bankNumberInfo
      ref="bankNumberInfo"
      @closeDialog="closeBankNumberInfoDialog"
    ></bankNumberInfo>

  </div>
</template>

<script>
  import BankNumberInfo from '@/components/BankNumberInfo';
  import { validateParams } from '@/utils/validate'
  export default {
    components: {
      BankNumberInfo
    },
    props: {
      info: {
        type: Object,
        default: () => ({})
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      channelList: {
        type: Array,
        default: () => [],
      },
      onlyShow: {
        type: Boolean,
        default: false,
      }
    },
    data() {
      let self = this;
      return {
        form: {},
        channelSelect: {},
        rules: {
          accountNo: [
            { required: true, message: "请输入银行卡号", trigger: "blur" },
          ],
          bankChannelNo: [
            { required: true, message: "请输入联行号", trigger: "change" },
          ],
          bankName: [
            { required: true, message: "请输入开户银行", trigger: "blur" },
          ],
          accounts: [
            { required: true, message: '请选择开通的发放方式', trigger: 'blur'},
            { validator: (rule, val, cb) => {
                let result = true;
                for (let mch in self.form.accounts) {
                  let accountList = self.form.accounts[mch];
                  if (accountList.length == 0) {
                    result = false;
                  }
                  for (let i = 0; i < accountList.length; i++) {
                    if (!accountList[i].payChannelNo) {
                      result = false;
                      break;
                    }
                  }
                  if (!result) {
                    break;
                  }
                }
                if (!result) {
                  cb(new Error('请选择通道'))
                } else {
                  cb();
                }
              }, trigger: 'blur'}
          ]
        },
        bankChannel: [],
        alipayChannel: [],
      }
    },
    computed: {
      payChannel() {
        return [
          this.bankChannel,
          this.alipayChannel,
        ]
      },
    },
    watch: {
      info: {
        handler(val) {
          for (let p in val) {
            this.$set(this.form, p, val[p])
          }
          this.preHandle();
        }
      }
    },
    methods: {
      init() {
        for (let p in this.info) {
          this.$set(this.form, p, this.info[p])
        }
        this.preHandle()
      },
      preHandle() {
        this.getPayChannelList();
      },
      closeBankNumberInfoDialog(item) {
        if (item) {
          this.$set(this.form.bankAccount, 'bankChannelNo', item.bankChannelNo)
          this.$set(this.form.bankAccount, 'bankName', item.bankName)
        }
      },
      searchBankNumberInfo() {
        this.$refs.bankNumberInfo.search();
        this.$refs.bankNumberInfo.isShow = true;
      },
      
      async getPayChannelList() {
        this.channelList.forEach(item => {
          item.channelType && item.channelType.forEach(i => {
            if (i == 1) {
              this.bankChannel.push(item)
            } else if (i == 2) {
              this.alipayChannel.push(item)
            }
            // else if (i == 3) {
            //   this.wxChannel.push(item)
            // }
          })
        })
      },
      getFormData() {
        let form = JSON.parse(JSON.stringify(this.form));
        return form;
      },
    },
  }
</script>

<style scoped lang="scss">
  .el-form {
    width: 800px;
  }

</style>
