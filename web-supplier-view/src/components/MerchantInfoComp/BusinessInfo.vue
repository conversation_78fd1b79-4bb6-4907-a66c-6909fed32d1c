<template>
  <div class="merchant-info-container">
    <el-form
      ref="form"
      label-width="350px"
      :model="form"
      :rules="rules"
      class="block-form"
      :disabled="disabled"
    >
      <!--联系人信息-->
      <!--<div class="company_info">-->
        <!--<p class="subTitle">联系人信息</p>-->
        <!--<div>-->
          <!--<el-form-item-->
            <!--label="负责人姓名："-->
            <!--prop="contactName"-->
          <!--&gt;-->
            <!--<el-input-->
              <!--v-model="form.contactName"-->
              <!--disabled-->
              <!--clearable-->
            <!--&gt;</el-input>-->
            <!--<p class="color-gray form-tip">-->
              <!--请填写公司运营负责人-->
            <!--</p>-->
          <!--</el-form-item>-->
          <!--<el-form-item-->
            <!--label="负责人手机号："-->
            <!--prop="contactPhone"-->
          <!--&gt;-->
            <!--<el-input-->
              <!--clearable-->
              <!--v-model="form.contactPhone"-->
              <!--disabled></el-input>-->
          <!--</el-form-item>-->
          <!--<el-form-item-->
            <!--label="客服电话："-->
            <!--prop="servicePhone"-->
          <!--&gt;-->
            <!--<el-input-->
              <!--clearable-->
              <!--v-model="form.servicePhone"></el-input>-->
            <!--<p class="color-gray form-tip">-->
              <!--请注意填写格式，举例（座机：0660-********；手机：***********；400电话：**********-->
            <!--</p>-->
          <!--</el-form-item>-->
        <!--</div>-->
      <!--</div>-->
      <!--经营信息-->
      <div class="bankCard_info">
        <p class="subTitle">经营信息</p>
        <div>
          <el-form-item
            label=" 企业简称："
            prop="shortName"
          >
            <el-input v-model="form.shortName"></el-input>
          </el-form-item>
          <el-form-item
            label="实际经营地址："
            prop="managementAddrDetail"
          >
            <address-select
              :province.sync="form.managementAddrProvince"
              :city.sync="form.managementAddrCity"
              :town.sync="form.managementAddrTown"
              :default-address="defaultAddress"
            ></address-select>
            <el-input
              style="margin-top: 8px" clearable
              v-model="form.managementAddrDetail"></el-input>
          </el-form-item>
          <el-form-item
            label="门头照片"
            prop="doorPhotoFileUrl"
          >
            <el-upload
              v-toggle="doorPhotoFileUrl"
              class="upload-demo"
              data-type="doorPhotoFileUrl"
              :action="baseUrl + '/file/upload'"
              :headers="uploadHeader"
              list-type="picture-card"
              accept=".png,.gif,.jpg,bmp"
              :limit="1"
              :file-list="doorPhotoFileUrl"
              :before-upload="beforeAvatarUpload"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 'doorPhotoFileUrl')"
              :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'doorPhotoFileUrl')"
              :on-preview="previewHandle"
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
              <div
                slot="tip"
                class="el-upload__tip"
              >最多1张，单张照片不超过6M。
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item
            label="办公内景照片"
            prop="workIndoorFileUrl"
          >
            <el-upload
              v-toggle="workIndoorFileUrl"
              data-type="workIndoorFileUrl"
              class="upload-demo"
              :action="baseUrl + '/file/upload'"
              :headers="uploadHeader"
              list-type="picture-card"
              accept=".png,.gif,.jpg,bmp"
              :limit="1"
              :file-list="workIndoorFileUrl"
              :before-upload="beforeAvatarUpload"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 'workIndoorFileUrl')"
              :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'workIndoorFileUrl')"
              :on-preview="previewHandle"
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
              <div
                slot="tip"
                class="el-upload__tip"
              >最多1张，单张照片不超过6M。
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item
            label="前台照片"
            prop="receptionFileUrl"
          >
            <el-upload
              v-toggle="receptionFileUrl"
              data-type="receptionFileUrl"
              class="upload-demo"
              :action="baseUrl + '/file/upload'"
              :headers="uploadHeader"
              list-type="picture-card"
              accept=".png,.gif,.jpg,bmp"
              :limit="1"
              :file-list="receptionFileUrl"
              :before-upload="beforeAvatarUpload"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 'receptionFileUrl')"
              :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'receptionFileUrl')"
              :on-preview="previewHandle"
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
              <div
                slot="tip"
                class="el-upload__tip"
              >最多1张，单张照片不超过6M。
              </div>
            </el-upload>
          </el-form-item>
        </div>
      </div>

      <div class="invoice_info" v-if="form.invoice">
        <p class="subTitle">开票信息</p>
        <el-form-item label="纳税人类型：" prop="taxPayerType">
          <el-radio-group v-model="form.invoice.taxPayerType">
            <el-radio v-for="(item, index) in $dict('TaxPayerTypeEnum')" :key="index" :label="Number(item.code)">
              {{ item.desc }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="开票地址：" prop="invoice.invoiceAddress" :rules="rules.invoiceAddress">
          <el-input
            type="textarea"
            :rows="5"
            clearable
            v-model="form.invoice.invoiceAddress"
          ></el-input>
        </el-form-item>
        <el-form-item label="联系电话：" prop="invoice.invoicePhone" :rules="rules.invoicePhone">
          <el-input
            clearable
            v-model="form.invoice.invoicePhone"></el-input>
        </el-form-item>
        <el-form-item label="开票银行：" prop="invoice.bankName" :rules="rules.invoiceBankName">
          <el-input
            clearable
            v-model="form.invoice.bankName"></el-input>
        </el-form-item>
        <el-form-item label="开票账号：" prop="invoice.accountNo" :rules="rules.invoiceAccountNo">
          <el-input
            clearable
            v-model="form.invoice.accountNo"></el-input>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
  import { validateParams } from '@/utils/validate';

  export default {
    name: 'manageInfo',
    props: {
      info: {
        type: Object,
        default: () => ({})
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      previewHandle: Function,
    },
    watch: {
      info: {
        handler(val) {
          for (let p in val) {
            this.$set(this.form, p, val[p])
          }
          this.preHandle()
        }
      }
    },
    data() {
      return {
        doorPhotoFileUrl: [], // 门头照
        workIndoorFileUrl: [],
        receptionFileUrl: [],

        form: {},
        rules: {
          contactName: [
            {required: true, message: "请输入负责人姓名", trigger: "blur"},
            {validator: validateParams({type: 'Chinese', msg: '请输入正确的姓名'}), trigger: 'blur'},
            {validator: validateParams({type: 'Length', max: '15', msg: '姓名最多可输入15个字'}), trigger: 'blur'},
          ],
          contactPhone: [
            {required: true, message: "请输入负责人手机号码", trigger: "blur"},
            {validator: validateParams({type: 'Phone', msg: '请输入正确的手机号码'}), trigger: 'blur'},
          ],
          servicePhone: [
            {required: true, message: "请输入客服电话", trigger: "blur"},
            {validator: validateParams({type: 'Reg', pattern: /[0-9-]+/, msg: '请输入正确的客服电话'}), trigger: 'blur'},
          ],

          shortName: [
            {required: true, message: "请输入企业简称", trigger: "blur"},
            {validator: validateParams({type: 'Chinese', msg: '请输入正确的企业简称'}), trigger: 'blur'},
            {validator: validateParams({type: 'Length', max: '15', msg: '企业简称最多可输入15个字'}), trigger: 'blur'},
          ],
          managementAddrDetail: [
            {required: true, message: "请选择实际经营地址", trigger: 'blur'},
          ],
          doorPhotoFileUrl: [
            {required: true, message: '请上传公司照片'},
            {validator: validateParams({type: 'Length', min: 1})}
          ],
          workIndoorFileUrl: [
            {required: true, message: '请上传公司照片'},
            {validator: validateParams({type: 'Length', min: 1})}
          ],
          receptionFileUrl: [
            {required: true, message: '请上传公司照片'},
            {validator: validateParams({type: 'Length', min: 1})}
          ],
          taxPayerType: [
            {required: true, message: '请选择纳税人类型', trigger: 'blur'},
          ],
          registerAddrInfo: [
            {required: true, message: '请输入单位注册地址及电话', trigger: 'blur'},
          ],
          invoiceAddress: [
            {required: true, message: '请输入开票地址', trigger: 'blur'},
          ],
          invoiceBankName: [
            {required: true, message: '请输入开票银行', trigger: 'blur'},
          ],
          invoiceAccountNo: [
            {required: true, message: '请输入开票银行账户', trigger: 'blur'},
          ],
          invoicePhone: [
            {required: true, message: '请选择开票联系电话', trigger: 'blur'},
          ],
        },
      }
    },
    computed: {
      defaultAddress() {
        if (this.form.managementAddrProvince) {
          return this.form.managementAddrProvince + '/' + this.form.managementAddrCity + '/' + this.form.managementAddrTown
        } else {
          return ''
        }
      },
      isMerchant() {
        if (this.form.mchNo) {
          return this.form.mchNo.startsWith('M');
        }
        return false
      }
    },
    methods: {
      init() {
        for (let p in this.info) {
          this.$set(this.form, p, this.info[p])
        }
        this.preHandle()
      },
      async preHandle() {
        const doorPhoto =  await this.formatFileMsg(this.form.doorPhotoFileUrl);
        this.doorPhotoFileUrl = this.form.doorPhotoFileUrl ? [{
          // url: this.fileUrl + this.form.doorPhotoFileUrl,
          url: doorPhoto.fileUrl,
          origin: this.form.doorPhotoFileUrl, // 文件源
        }] : [];

        const workIndoorFile =  await this.formatFileMsg(this.form.workIndoorFileUrl);
        this.workIndoorFileUrl = this.form.workIndoorFileUrl ? [{
          // url: this.fileUrl + this.form.workIndoorFileUrl,
          url: workIndoorFile.fileUrl,
          origin: this.form.workIndoorFileUrl, // 文件源
        }] : [];

        const receptionFile =  await this.formatFileMsg(this.form.receptionFileUrl);
        this.receptionFileUrl = this.form.receptionFileUrl ? [{
          // url: this.fileUrl + this.form.receptionFileUrl,
          url: receptionFile.fileUrl,
          origin: this.form.receptionFileUrl, // 文件源
        }] : [];

        this.form.doorPhotoFileUrl = this.doorPhotoFileUrl;
        this.form.workIndoorFileUrl = this.workIndoorFileUrl;
        this.form.receptionFileUrl = this.receptionFileUrl;

        // 开票地址和电话分离
        if (this.form.invoice) {
          this.form.invoice.invoiceAddress = this.form.invoice.registerAddrInfo.split(' ')[0];
          this.form.invoice.invoicePhone = this.form.invoice.registerAddrInfo.split(' ')[1];
        }
      },
      beforeAvatarUpload(file) {
        return this.validateUploadFile({
          img: true,
          size: 6
        })(file)
      },
      handleRemove(file, fileList, param) {
        this.form[param] = fileList;
      },
      handleSuccess(response, file, fileList, param) {
        this.form[param] = fileList;
      },
      handleInvoiceChange(val) {
        for (let i = 0; i < this.form.invoiceCategoryList.length; i++) {
          if (val == this.form.invoiceCategoryList[i].invoiceCategoryCode) {
            this.form.defaultInvoiceCategoryName = this.form.invoiceCategoryList[i].invoiceCategoryName;
            break;
          }
        }
      },
      getFormData() {
        let form = JSON.parse(JSON.stringify(this.form));
        let imgUrl = ['doorPhotoFileUrl', 'workIndoorFileUrl', 'receptionFileUrl'];
        imgUrl.forEach(param => {
          form[param] = this[param][0]
            ? this[param][0].response
              ? this[param][0].response.data
              : this[param][0].origin
            : '';
          // form[param] = form[param].replace(new RegExp(this.fileUrl, 'g'), '')
        });
        return form;
      },
    },
  }
</script>

<style scoped lang="scss">
  .el-form {
    width: 800px;
  }

</style>
