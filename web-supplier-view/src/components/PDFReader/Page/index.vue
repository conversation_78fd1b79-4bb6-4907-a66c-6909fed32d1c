<template>
  <div class="pdf-page-box">
    <div
      class="pdf-page-container"
      :style="pageStyle">
      <canvas
        ref="canvas"
        class="page-canvas"></canvas>
      <div
        class="mark"
        v-if="isInit">
        <slot name="mark"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    pdf: {
      // pdf 解析器
      type: Object,
      default: () => ({})
    },
    page: {
      type: [Number, String],
      default: 0,
    }
  },
  data() {
    return {
      isInit: false,
      canvasWidth: 0,
      width: 0,
      height: 0,
    }
  },
  computed: {
    canvas() {
      return this.$refs.canvas
    },
    pageStyle({width, height}) {
      return {
        width: width + 'px',
        height: height + 'px',
      }
    },
  },
  mounted() {
    this.render();
  },
  methods: {
    async render() {
      if (this.page == 0) {
        setTimeout(this.render, 100);
        return;
      }
      this.canvas.height = 0;
      this.canvas.width = 0;
      const ctx = this.canvas.getContext('2d');

      const pdfPage = await this.pdf.getPage(this.page);
      let vp = pdfPage.getViewport({scale: 1});

      this.width = this.canvas.width = vp.width;
      this.height = this.canvas.height = vp.height;
      const renderContext = {
        canvasContext: ctx,
        viewport: vp
      };
      pdfPage.render(renderContext);

      this.isInit = true;

      this.$emit('vp-change', {vp, width: this.canvas.width, page: this.page});
    },

  },
}
</script>

<style
  scoped
  lang="scss">

.pdf-page-box {
  margin-bottom: 32px;
  text-align: center;

  .pdf-page-container {
    position: relative;
    margin: 0 auto;

    .page-canvas {
      width: 100%;
    }

    .mark {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 8;
    }
  }
}

</style>
