<template>
  <el-dialog
    :visible.sync="isShow"
    title="导出列表"
    width="800px">
    <el-table :data="recordList">
      <el-table-column
        label="创建时间"
        prop="createTime"
        width="100"></el-table-column>
      <el-table-column
        label="标题"
        prop="fileName"></el-table-column>
      <el-table-column
        label="操作人"
        prop="operatorLoginName"></el-table-column>
      <el-table-column
        label="状态"
        prop="exportStatus">
        <template slot-scope="scope">
          {{ $dictCode('ExportStatusEnum', scope.row.exportStatus).desc }}
        </template>
      </el-table-column>
      <el-table-column
        label="描述"
        prop="errDesc"></el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button
            type="text"
            v-if="$dictCode('ExportStatusEnum', scope.row.exportStatus).desc === '成功'"
            @click="exportFile(scope.row)">导出
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
import { getExportRecord } from '@/api/common'

export default {
  name: 'index',
  props: {
    type: {
      type: String,
      default: 'download'
    }
  },
  data() {
    return {
      isShow: false,
      recordList: []
    }
  },
  methods: {
    open(type) {
      this.isShow = true;
      this.getExportRecord(type)
    },
    getExportRecord(reportType) {
      getExportRecord({
        reportType
      }).then(response => {
        this.recordList = response.data;
      })
    },
    async exportFile(data) {
      let urls = data.fileUrl.split(',').filter(Boolean)
      let formatUrls = []
      for (let i = 0; i < urls.length; i++) {
        const fileMsg = await this.formatFileMsg(urls[i])
        formatUrls.push({
          url: fileMsg.fileUrl,
          name: data.fileName ?? "导出文件",
          suffix: urls[i].split('.').pop()
        })
      }
      for (let i = 0; i < formatUrls.length; i++) {
        await this.sleep(i * 200)
        const file = formatUrls[i]
        if (this.type === 'download') {
          this.downloadFile(file.url, file.name, file.suffix)
        } else {
          window.open(file.url)
        }
      }
    }
  }
}
</script>

<style scoped>

</style>
