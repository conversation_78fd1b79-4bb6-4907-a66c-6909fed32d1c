import request from '@/utils/request'


// 自由职业者发单数据
export function analyzeFreelance(data) {
  return request({
    url: '/analyze/freelanceList',
    method: 'post',
    data,
  })
}

// 企业发单数据
export function analyzeMerchant(data) {
  return request({
    url: '/analyze/merchantList',
    method: 'post',
    data,
  })
}

// 企业统计数据
export function countMerchant(data) {
  return request({
    url: '/analyze/countMerchant',
    method: 'post',
    data,
  })
}

// 自由职业者统计数据
export function countFreelance(data) {
  return request({
    url: '/analyze/countFreelance',
    method: 'post',
    data,
  })
}

// 批量下载身份证
export function batchDownloadID(data) {
  return request({
    url: '/analyze/idCard/batchDownload',
    method: 'post',
    data,
  })
}

// 导出自由职业者数据
export function exportPersonData(data) {
  return request({
    url: '/analyze/freelanceExport',
    method: 'post',
    data,
  })
}

// 企业发单数据导出
export function exportCompanyData(data) {
  return request({
    url: '/analyze/merchantInfoExport',
    method: 'post',
    data,
  })
}