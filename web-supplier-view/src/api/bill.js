import request from '@/utils/request.js';


export function listPage(data) {
  return request({
    url: '/feeOrder/listPage',
    method: 'post',
    data
  })
}

export function getStatistics(data) {
  return request({
    url: '/feeOrder/getStatistics',
    method: 'post',
    data
  })
}

export function selectOrderItem(feeBatchNo) {
  return request({
    url: `/feeOrder/selectOrderItem/${feeBatchNo}`,
    method: 'get',
  })
}

export function getOffLineItem(feeBatchNo) {
  return request({
    url: `/feeOrder/getOffLineItem/${feeBatchNo}`,
    method: 'get',
  })
}

export function payFee(feeItemNo) {
  return request({
    url: `/feeOrder/payFee/${feeItemNo}`,
    method: 'post',
  })
}

export function getByItemNo(feeItemNo) {
  return request({
    url: `/feeOrder/getByItemNo/${feeItemNo}`,
    method: 'get',
  })
}

export function complete(data) {
  return request({
    url: `/feeOrder/complete`,
    method: 'post',
    data
  })
}

export function exportOrderItem(feeBatchNo) {
  return request({
    url: `/feeOrder/export/${feeBatchNo}`,
    method: 'post'
  })
}
