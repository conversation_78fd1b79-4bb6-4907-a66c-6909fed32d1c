import request from '@/utils/request.js';

// 提交发放名单
export function uploadOrderList(data) {
  return request({
    url: '/order/batchOrderUpload',
    method: 'post',
    data,
  })
}

// 发放名单列表
export function getOrderList(data) {
  return request({
    url: '/order/listOrderPage',
    method: 'post',
    data
  })
}

// 订单明细列表
export function getOrderDetail(data) {
  return request({
    url: '/order/listOrderItemPage',
    method: 'post',
    data
  })
}

// 挂起订单列表
export function getHangList(data) {
  return request({
    url: '/order/listHangOrderItemPage',
    method: 'post',
    data
  })
}

// 开始发放：发放明细概况
export function getOrderByBatchNo(data) {
  return request({
    url: '/order/getOrderByBatchNo',
    method: 'post',
    data
  })
}

// 开始发放：发放表格
export function getDeliverList(data) {
  return request({
    url: '/order/listOrderItemByBatchNoPage',
    method: 'post',
    data
  })
}

// 取消发放
export function cancelOrderGrant(data) {
  return request({
    url: '/order/cancelBatchOrderGrant',
    method: 'post',
    data
  })
}

// 确认发放
export function confirmOrderGrant(data) {
  return request({
    url: '/order/confirmBatchOrderGrant',
    method: 'post',
    data
  })
}

// 开始发放：发放明细页（根据不同状态）
export function getOrderListByStatus(data) {
  return request({
    url: '/order/listOrderItemByBatchNoAndStatus',
    method: 'post',
    data
  })
}

// 导出发放页面订单明细
export function exportOrderItem(data) {
  return request({
    url: '/order/exportOrderItemByBatchNoAndStatus',
    method: 'post',
    data
  })
}

// 导出订单明细
export function exportOrderList(data) {
  return request({
    url: '/order/exportOrderItem',
    method: 'post',
    data
  })
}

// 导出挂起订单
export function exportHangOrder(data) {
  return request({
    url: '/order/exportHangOrderItem',
    method: 'post',
    data
  })
}

export function getAmount(data) {
  return request({
    url: '/order/getAmount',
    method: 'post',
    data
  })
}

// 发放订单表格总条数
export function countOrder(data) {
  return request({
    url: '/order/countOrder',
    method: 'post',
    data,
  })
}

// 订单明细总条数
export function countOrderItem(data) {
  return request({
    url: '/order/countOrderItem',
    method: 'post',
    data,
  })
}

// 发放订单明细统计
export function sumOrderItem(data) {
  return request({
    url: '/order/sumOrderItem',
    method: 'post',
    data,
  })
}

// 批量下载凭证
export function batchDownloadCertificateFile(data, type) {
  return request({
    url: `/certificate/batchDownloadCertificateFile/${ type }`,
    method: 'post',
    data,
  })
}

// 导出
export function exportExcel(data, type) {
  return request({
    url: `/order/exportExcel/${ type }`,
    method: 'post',
    data
  })
}

// 提现记录
export function getWithdrawRecord(data) {
  return request({
    url: '/order/listWithdrawRecordPage',
    method: 'post',
    data
  })
}

// 提现
export function withdraw(data) {
  return request({
    url: '/order/withdraw',
    method: 'post',
    data
  })
}

// 导出提现记录
export function exportWithdrawRecord(data) {
  return request({
    url: '/order/exportWithRecord',
    method: 'post',
    data,
  })
}

// 外部订单发放列表
export function offlineOrderList(data) {
  return request({
    url: '/offlineOrder/listOrderPage',
    method: 'post',
    data
  })
}

// 外部订单明细
export function offlineOrderItemList(data) {
  return request({
    url: '/offlineOrder/listOrderItemPage',
    method: 'post',
    data
  })
}

// 外部订单明细条数
export function offlineOrderCount(data) {
  return request({
    url: '/offlineOrder/countOrderItem',
    method: 'post',
    data
  })
}

// 外部订单明细统计
export function offlineOrderSum(data) {
  return request({
    url: '/offlineOrder/sumOrderItem',
    method: 'post',
    data
  })
}

// 外部导出订单明细
export function exportOfflineOrderList(data) {
  return request({
    url: '/offlineOrder/exportOrderItem',
    method: 'post',
    data
  })
}
