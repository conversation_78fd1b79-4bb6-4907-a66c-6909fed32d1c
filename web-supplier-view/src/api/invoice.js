import request from '@/utils/request.js';

// 获取申请记录
export function getInvoiceList(data) {
  return request({
    url: '/invoice/listPage',
    method: 'post',
    data,
  })
}

export function exportInvoiceList(data) {
  return request({
    url: '/invoice/exportInvoiceRecord',
    method: 'post',
    data
  })
}

// 更新发票状态
export function updateInvoiceStatus(data) {
  return request({
    url: '/invoice/updateStatus',
    method: 'post',
    data
  })
}

// 根据流水号获取发票申请记录详细信息
export function getByTrxNo(data) {
  return request({
    url: '/invoice/getByTrxNo',
    method: 'get',
    params: data
  })
}

// 查询普通发票批次详情
export function listInvoiceDetail(data) {
  return request({
    url: '/invoice/invoiceRecordDetailPage',
    method: 'post',
    data
  })
}

// 普票更新状态
export function confirmInvoiceRecordDetail(data) {
  return request({
    url: '/invoice/confirmInvoiceRecordDetail',
    method: 'post',
    data,
  })
}

export function exportInvoceInfo(data) {
  return request({
    url: '/invoice/exportInvoceInfo',
    method: 'post',
    data,
  })
}

// 完税证明列表查询
export function getTaxCertList(data) {
  return request({
    url: '/taxCertificate/listPage',
    method: 'post',
    data
  })
}

// 删除完税证明
export function deleteCert(data) {
  return request({
    url: '/taxCertificate/delete',
    method: 'post',
    data
  })
}

// 上传完税证明
export function addTaxCert(data) {
  return request({
    url: '/taxCertificate/add',
    method: 'post',
    data
  })
}

//编辑完税证明
export function editTaxCert(data) {
  return request({
    url: '/taxCertificate/edit',
    method: 'post',
    data
  })
}

// 代开列表
export function listProxyOrder(data) {
  return request({
    url: '/proxyOrder/listPage',
    method: 'post',
    data
  })
}
// 更新订单状态
export function confirmInvoice(data) {
  return request({
    url: '/proxyOrder/confirmInvoice',
    method: 'post',
    data
  })
}
