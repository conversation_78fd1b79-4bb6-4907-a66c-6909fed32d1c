import request from '@/utils/request.js';

// 协议列表
export function getAgreementList(data) {
  return request({
    // url: '/agreement/listAgreementPage',
    url: '/agreement/getAgreementPage',
    method: 'post',
    data
  })
}

// 下载协议
export function downloadArchiveFile(data) {
  return request({
    url: '/agreement/downloadArchiveFile',
    method: 'post',
    data
  })
}

// 根据ID获取协议
export function getAgreementById(id) {
  return request({
    url: `/agreement/getAgreementById/${id}`,
    method: 'get',
  })
}

// 归档
export function finishAgreement(data) {
  return request({
    url: '/agreement/archiveAgreement',
    method: 'post',
    data
  })
}

// 获取签署地址
export function getSignUrl({ id }) {
  return request({
    url: `/agreement/getSignUrl/${id}`,
    method: 'get'
  })
}

// 批量下载归档文件
export function exportFile(data) {
  return request({
    url: '/agreement/exportFile',
    method: 'post',
    data
  })
}



