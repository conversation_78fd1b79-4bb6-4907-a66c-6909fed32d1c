import request from '@/utils/request.js';
import { axiosRequest } from '@/utils/request'

// 获取商户信息
export function getMerchant() {
  return request({
    url: '/merchantMainstay/getMerchant',
    method: 'get'
  })
}

// 主题认证
export function merchantAuth(data) {
  return request({
    url: '/merchantMainstay/mainAuth',
    method: 'post',
    data
  })
}

/* 更新主体认证信息 */
export function updateMerchantMain(data) {
  return request({
    url: '/merchantEmployer/updateMain',
    method: 'post',
    data
  })
}

/* 更新银行卡信息 */
export function updateBankCardInfo(data) {
  return request({
    url: '/merchantEmployer/updateBankAccount',
    method: 'post',
    data
  })
}


// 获取岗位信息
export function getEmployerPosition() {
  return request({
    url: '/employerPosition/listPosition',
    method: 'get'
  })
}

// 获取失败信息
export function getErrorMsg() {
  return request({
    url: '/merchantMainstay/getApprovalFlow',
    method: 'get'
  })
}

// 认证失败回显
export function getBackMainInfo() {
  return request({
    url: '/merchantEmployer/mainEditAuth',
    method: 'get'
  })
}

// 获取商户对应代征主体
export function getMainstayList() {
  return request({
    url: '/order/listOpenMainstayByEmployerNo',
    method: 'get'
  })
}

// 开启状态的发放方式
export function getOpenChannelType() {
  return request({
    url: '/order/listOpenChannelTypeByEmployerNo',
    method: 'get'
  })
}

// 重新提交认证审核
export function merchantAuthAgain(data) {
  return request({
    url: '/merchantMainstay/mainAuthApprovalEdit',
    method: 'post',
    data
  })
}

// 检查是否显示认证查看按钮
export function checkAuthBtn() {
  return axiosRequest({
    url: '/merchantMainstay/getApprovalFlow',
    method: 'get'
  }, { silence: true })
}


// 获取供应商主体下商户列表
export function getMerchantList(data) {
  return request({
    url: '/merchant/listMerchantPage',
    method: 'post',
    data
  })
}

// 获取商户详细信息
// export function getMerchantInfo(params) {
//   return request({
//     url: '/merchant/getMerchantInfoVo',
//     method: 'get',
//     params
//   })
// }
export function getBaseInfo(params) {
  return request({
    url: '/merchantEmployer/getBaseInfo',
    method: 'get',
    params
  })
}

export function getCooperateInfo(params) {
  return request({
    url: '/merchantEmployer/getCooperateInfo',
    method: 'get',
    params
  })
}

export function getAccountInfo(params) {
  return request({
    url: '/merchantEmployer/getAccountInfo',
    method: 'get',
    params
  })
}

export function getMainInfo(params) {
  return request({
    url: '/merchantEmployer/getMainInfo',
    method: 'get',
    params
  })
}

export function getBusinessInfo(params) {
  return request({
    url: '/merchantEmployer/getBusinessInfo',
    method: 'get',
    params
  })
}

export function getAgreementInfo(params) {
  return request({
    url: '/merchantEmployer/getAgreementInfo',
    method: 'get',
    params
  })
}

export function getQuoteInfo(params) {
  return request({
    url: '/merchantEmployer/getQuoteInfo',
    method: 'get',
    params
  })
}

export function getRecordInfo(data) {
  return request({
    url: '/merchant/record',
    method: 'post',
    data
  })
}

// 商户、供应商、合伙人系统信息
export function getSystemRecord(data) {
  return request({
    url: '/record/system',
    method: 'post',
    data
  })
}

// 导出商户信息
export function exportMerchantInfo(data) {
  return request({
    url: '/merchant/exportMerchantInfo',
    method: 'post',
    data
  })
}

// 导出商户列表
export function exportMerchantList(data) {
  return request({
    url: '/merchant/exportMerchantList',
    method: 'post',
    data
  })
}

// 供应商下商户列表(全)
export function getMchByMainstayNo(params) {
  return request({
    url: '/merchant/listMchByMainstayNo',
    method: 'get',
    params
  })
}

// 代征主体下的通道信息
export function getMerchantMainstay(data, params) {
  return request({
    url: '/merchantMainstay/listChannelRelationInfoByMainstayNo',
    method: 'post',
    params,
    data
  })
}

// 获取银行卡信息
export function getBankAccount() {
  return request({
    url: '/merchantMainstay/getBankAccount',
    method: 'get'
  })
}

// 更改logo
export function changeLogo(data) {
  return request({
    url: '/merchantMainstay/changeLogo',
    method: 'post',
    data
  })
}

// 获取岗位信息
export function getAllPosition() {
  return request({
    url: '/workCategory/listAll',
    method: 'get'
  })
}