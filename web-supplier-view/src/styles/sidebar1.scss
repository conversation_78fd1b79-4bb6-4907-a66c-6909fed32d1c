#app {

  .scrollbar-wrapper {
    overflow-x: hidden !important;
  }

  .scrollbar-full-container {
    transition: width .1s linear;
    background: #fff;
  }

  .el-menu-item span {
    box-sizing: content-box;
    @include position-tb-center(absolute);
    left: 12px;
    height: 30px;
    padding: 0;
    padding: 0 24px;
    line-height: 30px;
    border-radius: 4px;
  }
  .submenu-title-noDropdown span {
    left: 10px;
    padding-left: 12px;
  }

  .el-menu-item.is-active span {
    background: $borderColor;
  }

  .scrollbar-temp-container {
    background: #fff;
  }

  .main-container {
    transition: margin-left .1s linear;
  }

  .app-wrapper.hideSidebar {
    .scrollbar-full-container {
      width: 0;
    }

    .main-container {
      margin-left: 92px;
    }
  }

  .app-wrapper.openSidebar {
    .scrollbar-full-container {
      width: 150px
    }

    .main-container {
      margin-left: 242px;
    }
  }
}
