// 专用于商户报备和认证页面(createmerchant.vue, merchantauth.vue)的样式

.merchant-page-container {
  .header-container {
    margin: 16px 0;
    padding: 8px 0;
    background: #fff;
    @include boxShadow;
  }

  .page-container {
    padding-top: 0;
  }
  .content-container {
    padding-top: 16px;
  }
}



// 专用于merchantInfo中各个tab 的样式
.merchant-info-container {
  margin-top: 16px;
  padding: 0 16px 16px;
  background: #fff;

  .form-tip {
    margin-top: 4px;
    margin-bottom: 0;
    font-size: 12px;
  }

}
