@mixin scrollBar {
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}

@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

@mixin boxShadow {
  border: 1px solid rgba(0,0,0,0.2);
  border-radius: 6px;
  box-shadow:0px 2px 10px 0px rgba(0, 0, 0, 0.2);
}

@mixin position-tb-center($pos) {
  position: $pos;
  top: 50%;
  transform: translateY(-50%);
}

@mixin position-lr-center($pos) {
  position: $pos;
  left: 50%;
  transform: translateX(-50%);
}

@mixin position-all-center($pos) {
  position: $pos;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0)
}

@mixin bottom-border {
  border-bottom: 1px solid $borderColor;
}

@mixin text-ellipsis {
  overflow-x: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
