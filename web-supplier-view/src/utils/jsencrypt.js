import { JSEncrypt } from 'jsencrypt';

export const encryptParam = param => {
  const jsencrypt = new JSEncrypt();
  if (!param.publicKey) {
    return
  }
  jsencrypt.setPublicKey(param.publicKey);
  const RSA_encrypt = jsencrypt.encrypt(param.pwd);
  return RSA_encrypt;
};

let LOING_RSA_PUBLIC_KEY = '';
if (process.env.VUE_APP_FLAG === 'production') {
  LOING_RSA_PUBLIC_KEY = 
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCeEUo1Xf2mzp6WbbrcWSo8iNMmZ1ZO53iDeHdQikwrfFt+Oe8mJjiXlShTebrkA0gjbK/fryvXLKLwhj6iZzGwoQpAG79Bd9jS/LNpgxGXGWm1nklegbB7gD0lGedk4BSASm/jDtvOFupWzzfWvn/Ah9Ebr3SvScky4FEaBpbkdQIDAQAB';
} else {
  LOING_RSA_PUBLIC_KEY = 
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCLoHALs6xuUgnmd2PXaqYJS/WNdgvuuzkape3LAZPXHmEArsVETMIOl1Li1CKbpuNQJ8cy43NkEDPGmrIjtCA6OTOYvMxtxv5gDfW3WW/ZcA1My6B55zV8/WhltFklgCslxgmWGCNqrPlp4C71FhUuYhsiVi2DoK+0tzncb80VxQIDAQAB';
}


export const encryptFileParam = param => {
  const jsencrypt = new JSEncrypt();
  jsencrypt.setPublicKey(LOING_RSA_PUBLIC_KEY);
  const RSA_encrypt = jsencrypt.encrypt(param);
  return RSA_encrypt;
};

