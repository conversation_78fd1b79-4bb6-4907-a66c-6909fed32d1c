# 智享汇运营后台
前言：该系统是使用vue-admin-template模板进行开发的，遇到问题可以看看vue-admin-template和vue-element-admin仓库上Issues有没有解决方案，或者vue-element-admin源代码就有解决方案。

## 项目技术栈说明 ##
```bash
vue
vuex
element-ui
vue-admin-template(https://github.com/PanJiaChen/vue-admin-template)
axios
dayjs(时间处理，与moment的用法一致)
```

## 项目需求文档/开发文档/接口文档 ##

```bash
接口文档: YApi文档（http://************:3000/project/26/interface/api）,
```

## 启动
```bash
# install dependency
npm install

# develop
npm run dev
```

This will automatically open http://localhost:9528

## 打包
```bash
# build for dev environment
npm run build:dev

# build for test environment
npm run build:test

# build for production environment
npm run build:production
```

### 项目结构 ###
```bash
build   			打包配置
dist    			打包输出目录
src     			主要目录
--api     			后端请求的api地址
--assets    		图片路径
--components		公共组价路径
--directive         自定义指令
--filter			过滤器
--icons				图标（svg组件读取图片地址）
--layout			整体布局
--router			路由
--store				vuex
--styles			公共样式
--utils				公共函数，工具
--views				访问页面
--main.js 			入口文件
--permission.js  	 通过权限进行路由拦截
static				静态文件（静态文件，路径不会经过编译）
test				单元测试
.env.* 				打包环境配置
vue.config.js 		webpack配置
package.json		项目的配置信息	
```

### 代码理解 ###
```bash
1.首先掌握layout的整体页面布局。
2.查看src/permission.js,弄懂权限控制
3.结合前两者弄懂左边导航栏如何动态渲染，如何将后端权限映射访问哪个页面和哪个组件。
4.愉快的进行代码开发
```


## 常见问题 ##
```bash
Q：如何增加一个新的页面
A：判断是否需要权限
需要：
1.在views适当位置增加你的vue文件
2.使用admin账号在项目菜单管理中增加所增菜单的配置。
3.在目录src/router/index.js中的routersInfo增加映射，注意要与上一步菜单配置的菜单标识一致
不需要：
1.在views适当位置增加你的vue文件
2.在目录src/router/index.js中的constantRoutes增加路由挂载

Q：如何增加新的svg图标
A：1.将图标复制到assets/icons/svg下，然后component文件夹中的svg-icon会根据类名查找同名的图标
   2.修改svg图标的颜色只需要在页面代码修改该容器color的值，假如颜色没有发生变化，可能是UI给的图默认设置了颜色，需要打开图片，将图片里面的fill：{#xxx}代码删除

Q：如何将通过数据字典进行映射?
A：假如需要code -> value, 则$dictCode('字典名'，'需要转化的原始值').desc
   假如要遍历数据字典，则$dict('字典名')
```

## 注意点 && 规范 ##
```
1.当你使用admin账号做了一些操作，接口返回成功，但是页面没有更新的情况下，尝试按流程退出登录再登录清除缓存。
2.基础的公共样式在编写的时候，一定要用一个独特的class类名作为最顶层，防止底下的样式造成样式污染。
3.在难理解的地方请务必加上注释！！！
5.弹框Dailog很多情况下都将代码拉出来弄成个组件，主要是应用在创建和编辑的情况，编辑的时候在父组件通过$子组件名称修改组件的值，从而获取到编辑数据
6.element公共样式修改的问题，请统一在element-ui.scss中进行修改，其中颜色的变量设置是放在variables.scss中，底下的export是用来给js用的，没需要的话可以不设置
7.请求接口的数据不需要保存的情况下不要经过vuex，直接export接口、引入使用  
8.左边导航分为两级导航，第一级导航只放顶级菜单，第二级导航展示选择顶级菜单下的子菜单，二级导航又分为真假两个，假的用于鼠标悬浮在别的顶级菜单时展示的子菜单，失去悬浮时需要隐藏假的展示真的二级导航  
9.后端创建XXX接口的时候，有时需要传个status参数过去是为了默认创建一个可用的(主要是计费那部分的接口)
10.导航路由高亮原理是子路由路径的顶级要跟高亮菜单一致，如顶级菜单为/system，则子菜单跳转地址必须为/system/XXX，这样顶级菜单才会高亮
```

## TODO
- [ ] login页面单独拆分多页面
