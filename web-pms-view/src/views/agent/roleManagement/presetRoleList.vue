<template>
  <div class="box-container">
    <el-button type="primary" class="create-btn" @click="addRolePage">添加角色</el-button>
    <div class="content-container">
      <el-table :data="list">
        <el-table-column type="index" :index="getIndex" />
        <el-table-column label="角色名称" prop="roleName">
          <template v-slot="{row}">
            {{ row.roleName }}
            <el-tag style="margin-left: 16px" type="info" v-if="row.roleType != 2">{{ $dictCode('RoleTypeEnum', row.roleType).desc }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="remark" />
        <el-table-column label="操作" v-slot="{row}">
          <template v-if="row.roleType == 1">
            <el-button
              type="text"
              size="small"
              @click="assignFunctionPage(row)"
              v-permission="'pms:role:assignFunction'"
            >设置权限</el-button>
            <el-button
              type="text"
              size="small"
              @click="editRolePage(row)"
              v-permission="'pms:role:edit'"
            >编辑</el-button>
            <el-button
              type="text"
              size="small"
              @click="deletePmsRole(row)"
              v-permission="'pms:role:delete'"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-footer class="pagination-container">
      <el-pagination
        v-if="list"
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>

    <role-form ref="roleForm" @success="search()"></role-form>
    <assign-function-form ref="assignForm"></assign-function-form>
  </div>
</template>

<script>
  import { getPresetRole, deletePresetRole } from '@/api/agent';
  import RoleForm from './Components/RoleForm'
  import AssignFunctionForm from './Components/AssignFunctionForm'
  export default {
    name: 'AgentPresetRoleList',
    components: {
      RoleForm,
      AssignFunctionForm,
    },
    data() {
      return {
        list: [],

        totalRecord: 0,
        pageSize: 10,
        pageCurrent: 1,
      }
    },
    mounted() {
      this.search()
    },
    methods: {
      async search(init) {
        if (init) {
          this.pageCurrent = 1
        }
        const { data } = await getPresetRole({
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent,
        });
        this.list = data.data;
        this.totalRecord = data.totalRecord;
      },
      assignFunctionPage(row) {
        this.$refs.assignForm.initAndShow(row.id);
      },
      addRolePage() {
        this.$refs.roleForm.actionType = "ADD";
        this.$refs.roleForm.form = {};
        this.$refs.roleForm.show = true;
      },

      editRolePage(row) {
        this.$refs.roleForm.actionType = "EDIT";
        this.$refs.roleForm.form = { ...row };
        this.$refs.roleForm.show = true;
      },
      async deletePmsRole({ id }) {
        const valid = await this.$confirm("确认删除角色?").catch(_ => false)
        if (valid) {
          const { data } = await deletePresetRole({ id });
          this.$message.success(data);
          this.search()
        }
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
    },
  }
</script>

<style scoped lang="scss">

</style>
