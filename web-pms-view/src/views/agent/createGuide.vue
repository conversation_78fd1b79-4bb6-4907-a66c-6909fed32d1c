<template>
  <div class="page-container">
    <h2 class="page-header">选择主体类型，并根据选择的结果，按照页面提示准备所需材料</h2>

    <div class="content-container">
      <el-radio-group v-model="type">
        <el-radio
          v-for="item in $dict('AgentTypeEnum')"
          :key="item.code"
          :label="item.code"
          border
        >
          {{ item.desc}}
        </el-radio>
      </el-radio-group>

      <div class="create-guide-block" v-show="type == 101">
        个人独资企业、合伙企业、其他企业、企业分支机构、个体工商户、政府及事业单位、非营利组织（慈善基金会、大使馆、国外政府机构）、民办非企业、营利组织、社会团体等
      </div>
      <div class="create-guide-tip" v-show="type == 101">
        <h1>
          所需材料
        </h1>
        <ol>
          <li>统一社会信用代码证（多证合一的营业执照或者普通营业执照）</li>
          <li>法人证件照正反面</li>
          <li>结算、发票的主体需与认证主体一致，请如实填写主体认证信息</li>
        </ol>
      </div>
      <div class="create-guide-tip" v-show="type == 100">
        <h1>
          所需材料
        </h1>
        <ol>
          <li>证件照正反面</li>
          <li>结算、发票的主体需与认证主体一致，请如实填写主体认证信息</li>
        </ol>
      </div>
    </div>

    <div class="form-btn-group">
      <el-checkbox v-model="checked">
        我已仔细阅读并同意
        <span class="func-content" @click="downloadProtocal">
          《合伙人协议》
        </span>
      </el-checkbox>
      <el-button :disabled="!checked" type="primary" @click="startCreate">材料准备就绪，开始添加</el-button>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        type: '100',

        checked: false,
      }
    },
    methods: {
      startCreate() {
        let url = '';
        if (this.type == 101) {
          url = '/agent/createCompany'
        } else {
          url = '/agent/createPerson'
        }
        this.$router.push(url);
      },
      downloadProtocal(e) {
        e.preventDefault();
        const doc = require('@/assets/doc/protocal-agent.docx');
        const a = document.createElement('a');
        a.download = '汇聚智享-代理合作协议.docx';
        a.href = doc;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
      }
    }
  }
</script>

<style lang="scss" scoped>
  .page-container {
    text-align: left;
    padding-left: 32px;
    padding-right: 32px;
    .page-header {
      font-weight: bold;
    }

    .create-guide-block {
      width: 80%;
      margin: 16px 0 32px;
      padding: 16px 8px;
      background: #e3ebfa;
    }
    .create-guide-tip {
      margin-top: 32px;
      li {
        margin-top: 12px;
        font-size: 14px;
      }
    }
    .form-btn-group {
      text-align: left;

      .el-checkbox {
        height: 30px;
        line-height: 30px;
      }

      .el-button {
        @include position-tb-center(absolute);
        right: 10px;
      }
    }
  }
</style>
