<template>
  <div class="page-container">
    <div class="header-container">
      <el-steps
        :active="activeStep"
        align-center
      >
        <el-step title="填写主体信息"></el-step>
        <el-step title="填写法定代表人及联系人信息"></el-step>
        <el-step title="审核与公示"></el-step>
      </el-steps>
    </div>

    <div class="content-container">
      <el-form
        v-show="activeStep == 1"
        ref="form_1"
        label-width="400px"
        :model="form_1"
        :rules="rules"
      >
        <div class="hgroup">
          <h2 style="font-size: 30px">填写主体信息</h2>
          <h4>
            <span class="color-gray">填写主体信息，提交平台进行业务审核</span>
          </h4>
        </div>
        <p class="subTitle">合伙人信息</p>
        <el-form-item label="主体类型：">
          非个人
        </el-form-item>
        <el-form-item
          label="销售："
          prop="salerId"
        >
          <el-select
            :disabled="!hadPermission('pms:crm:role')"
            v-model="form_1.salerId"
            @change="handleSalerChange"
          >
            <el-option
              v-for="item in staffList"
              :key="item.id"
              :label="item.realName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="邀请方：" prop="inviterNo">
          <el-select filterable clearable v-model="form_1.inviterNo" :disabled="!form_1.salerId">
            <el-option v-for="(item, index) in agentList" :key="index" :label="item.agentName" :value="item.agentNo"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="企业名称：" prop="agentName">
          <el-input clearable v-model="form_1.agentName" :maxlength="20"></el-input>
          <p class="color-gray form-tip">请输入企业名称，该名称需与在智享汇的银行卡所对应的对公账户名称一致</p>
        </el-form-item>
        <el-form-item label="企业简称：" prop="shortName">
          <el-input clearable v-model="form_1.shortName" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="统一社会信用代码：" prop="taxNo">
          <el-input clearable v-model="form_1.taxNo" :maxlength="18"></el-input>
          <p class="color-gray form-tip">请输入营业执照上18位统一社会信用代码</p>
        </el-form-item>

        <el-form-item label="注册地址" prop="registerAddrProvince">
          <address-select
            :province.sync="form_1.registerAddrProvince"
            :city.sync="form_1.registerAddrCity"
            :town.sync="form_1.registerAddrTown"
            :default-address="defaultAddress"
          ></address-select>
          <el-input style="margin-top: 8px" v-model="form_1.registerAddrDetail" :maxlength="50"></el-input>
          <p class="color-gray form-tip">与营业执照登记住所一致</p>
        </el-form-item>

        <el-form-item label="经营范围" prop="managementScope">
          <el-input type="textarea" v-model="form_1.managementScope" :rows="6" :maxlength="500"></el-input>
          <p class="color-gray form-tip">与企业工商营业执照上一致</p>
        </el-form-item>

        <p class="subTitle">企业证件</p>
        <el-form-item label="营业执照" prop="businessLicenseFileUrl" ref="businessLicenseFileUrl">
          <el-upload
            v-toggle="businessLicenseFileUrl"
            key="businessLicenseFileUrl"
            class="upload-demo"
            list-type="picture-card"
            accept=".png,.gif,.jpg,.bmp"
            :action="baseUrl + '/file/upload'"
            :headers="uploadHeader"
            :limit="1"
            :file-list="businessLicenseFileUrl"
            :before-upload="beforeFileUpload"
            :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'businessLicenseFileUrl', 'businessLicenseFileUrl')"
            :on-remove="(file, fileList) => handleRemove(file, fileList, 'businessLicenseFileUrl')"
          >
            <i class="el-icon-plus avatar-uploader-icon"></i>
            <div
              slot="tip"
              class="el-upload__tip"
            >请上传彩色原件或加盖公司公章的复印件，小于6M，文件格式为bmp、png、jpeg、jpg或gif。
              <el-popover
                trigger="click"
                width="300">
                <el-button type="text" slot="reference">
                  查看示例
                </el-button>
                <div>
                  <img
                    class="example-image"
                    src="@/assets/example-1.png"
                  >
                </div>
              </el-popover>
            </div>
          </el-upload>
        </el-form-item>

        <el-form-item label="营业期限" prop="managementValidityDateType">
          <el-radio-group v-model="form_1.managementValidityDateType">
            <el-radio
              v-for="item in $dict('ValidityDateTypeEnum')"
              :key="item.code"
              :label="Number(item.code)"
            >{{ item.desc }}
            </el-radio>
          </el-radio-group>
          <date-picker
            style="display: block;"
            v-if="form_1.managementValidityDateType == 1"
            picker="separate"
            type="date"
            :is-show-fast-time="false"
            :use-option="false"
            :start-time.sync="form_1.managementTermBegin"
            :end-time.sync="form_1.managementTermEnd"
          ></date-picker>
          <div v-if="form_1.managementValidityDateType == 2">
            <el-date-picker
              type="date"
              placeholder="选择起始日期"
              v-model="form_1.managementTermBegin"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </div>
        </el-form-item>

        <p class="subTitle">银行卡信息</p>
        <el-form-item label="银行卡号：" prop="accountNo">
          <el-input clearable v-model="form_1.accountNo">
          </el-input>
        </el-form-item>
        <el-form-item
          label="联行号："
          prop="bankChannelNo"
          ref="bankChannel"
        >
          <el-input
            v-model="form_1.bankChannelNo"
            prefix-icon="el-icon-search"
            @click.native="searchBankNumberInfo"
          >
          </el-input>
        </el-form-item>
        <el-form-item label="开户银行：">
          <el-input disabled v-model="form_1.bankName"></el-input>
        </el-form-item>
      </el-form>

      <el-form :model="form_2" :rules="rules" v-show="activeStep == 2" label-width="400px" ref="form_2">
        <p class="subTitle">法定代表人证件</p>
        <el-form-item label="证件类型：" prop="certificateType">
          <el-select
            clearable
            v-model="form_2.certificateType"
          >
            <el-option
              v-for="item in $dict('CertificateTypeEnum')"
              :key="item.code"
              :value="Number(item.code)"
              :label="item.desc"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="证件照：" prop="idFileType" ref="idFile">
          <el-radio-group v-model="form_2.idFileType">
            <el-radio label="100">彩色原件</el-radio>
            <el-radio label="101">复印件（加盖公司公章）</el-radio>
          </el-radio-group>

          <div v-show="form_2.idFileType == '100'">
            <p>
              人像面
              <el-popover
                width="300"
                trigger="click"
              >
                <img
                  class="example-image"
                  src="@/assets/example-2.png"
                >
                <el-button
                  slot="reference"
                  type="text"
                >查看示例
                </el-button>
              </el-popover>
            </p>
            <el-upload
              v-toggle="idCardHeadFileUrl"
              key="idCardHeadFileUrl"
              class="upload-demo"
              list-type="picture-card"
              accept=".png,.gif,.jpg,.bmp"
              :action="baseUrl + '/file/upload'"
              :headers="uploadHeader"
              :limit="1"
              :file-list="idCardHeadFileUrl"
              :before-upload="beforeFileUpload"
              :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardHeadFileUrl', 'idFile')"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardHeadFileUrl')"
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            <p>
              国徽面
              <el-popover
                width="300"
                trigger="click"
              >
                <img
                  class="example-image"
                  src="@/assets/example-3.png"
                >
                <el-button
                  slot="reference"
                  type="text"
                >查看示例
                </el-button>
              </el-popover>
            </p>
            <el-upload
              v-toggle="idCardEmblemFileUrl"
              key="idCardEmblemFileUrl"
              class="upload-demo"
              list-type="picture-card"
              accept=".png,.gif,.jpg,.bmp"
              :action="baseUrl + '/file/upload'"
              :headers="uploadHeader"
              :limit="1"
              :file-list="idCardEmblemFileUrl"
              :before-upload="beforeFileUpload"
              :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardEmblemFileUrl', 'idFile')"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardEmblemFileUrl')"
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>

          <div v-show="form_2.idFileType == '101'">
            <p>加盖公司公章的法人身份证复印件
              <el-popover
                width="300"
                trigger="click"
              >
                <img
                  class="example-image"
                  src="@/assets/idCardCopyFileExample.png"
                >
                <el-button
                  slot="reference"
                  type="text"
                >查看示例
                </el-button>
              </el-popover>
            </p>
            <el-upload
              v-toggle="idCardCopyFileUrl"
              key="idCardCopyFileUrl"
              class="upload-demo"
              :action="baseUrl + '/file/upload'"
              :headers="uploadHeader"
              :limit="1"
              list-type="picture-card"
              accept=".png,.gif,.jpg,.bmp"
              :file-list="idCardCopyFileUrl"
              :before-upload="beforeFileUpload"
              :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardCopyFileUrl', 'idFile')"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardCopyFileUrl')"
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </el-form-item>

        <p class="subTitle">法定代表人信息</p>
        <el-form-item label="法定代表人姓名：" prop="name">
          <el-input clearable v-model="form_2.name" :maxlength="20"></el-input>
        </el-form-item>
        <el-form-item label="身份证号码：" prop="certificateNumber" :rules="certificateRules">
          <el-input clearable v-model="form_2.certificateNumber" :maxlength="30"></el-input>
        </el-form-item>


        <el-form-item label="证件有效期：" prop="certificateValidityDateType" ref="timeContainer">
          <el-radio-group
            v-model="form_2.certificateValidityDateType"
            @change="() => { $refs.timeContainer.clearValidate() }">
            <el-radio
              v-for="item in $dict('ValidityDateTypeEnum')"
              :key="item.code"
              :label="Number(item.code)"
            >{{ item.desc }}
            </el-radio>
          </el-radio-group>
          <date-picker
            v-if="form_2.certificateValidityDateType == 1"
            picker="separate"
            type="date"
            :is-show-fast-time="false"
            :use-option="false"
            :start-time.sync="form_2.certificateTermBegin"
            :end-time.sync="form_2.certificateTermEnd"
          ></date-picker>
          <div v-if="form_2.certificateValidityDateType == 2">
            <el-date-picker
              type="date"
              placeholder="选择起始日期"
              v-model="form_2.certificateTermBegin"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </div>
        </el-form-item>

        <p class="subTitle">联系人信息</p>
        <el-form-item label="负责人姓名：" prop="contactName">
          <el-input clearable v-model="form_2.contactName" :maxlength="20"></el-input>
        </el-form-item>

        <el-form-item label="负责人手机号：" prop="contactPhone">
          <el-input clearable v-model="form_2.contactPhone"></el-input>
        </el-form-item>
        <el-form-item label="常用邮箱：" prop="contactEmail">
          <el-input clearable v-model="form_2.contactEmail"></el-input>
        </el-form-item>

        <p class="subTitle">其他</p>
        <el-form-item label="备注：" prop="remark">
          <el-input type="textarea" :rows="5" v-model="form_2.remark" maxlength="200"></el-input>
        </el-form-item>
      </el-form>

      <div v-show="activeStep == 3" class="result-container">
        <img src="@/assets/success.png">
        <p class="result-title">提交成功</p>
        <p class="result-tip">合伙人新增成功</p>
        <div class="footer-container">
          <el-button type="primary" @click="$router.push('/sales/agent/list')">返回合伙人列表</el-button>
        </div>
      </div>

      <div class="form-btn-group" v-show="activeStep <= 2">
        <el-button type="primary" @click="nextStep">
          {{ activeStep == 2 ? '提交' : '下一步'}}
        </el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </div>

    <!--  表格弹窗  -->
    <bankNumberInfo
      ref="bankNumberInfo"
      @closeDialog="closeBankNumberInfoDialog"
    ></bankNumberInfo>

    <!--审批意见-->
    <flow-opinion
      :visible.sync="flowVisible"
      @change="flowConfirm"
    ></flow-opinion>
  </div>
</template>

<script>
  import { getSales } from '@/api/system'
  import { getAllAgentSimple, createAgentCompany, updateCompany } from '@/api/agent'
  import { getProductList, getVendorList } from '@/api/product'
  import { validateParams } from '@/utils/validate'
  import { editBusinessData } from '@/api/flow';
  import BankNumberInfo from '@/components/BankNumberInfo';
  import FlowOpinion from '@/components/Flow/FlowOpinion'
  import { clearVoid } from '@/utils'

  export default {
    components: {
      BankNumberInfo,
      FlowOpinion,
    },
    data() {
      const self = this;

      return {
        activeStep: 1,

        agentList: [],

        defaultAddress: '',

        form_1: {
          inviterNo: '',
          inviterName: '',
          agentName: '',
          shortName: '',
          taxNo: '',
          registerAddrProvince: '',
          registerAddrCity: '',
          registerAddrTown: '',
          registerAddrDetail: '',
          managementScope: '',
          businessLicenseFileUrl: '',
          managementValidityDateType: '',
          managementTermBegin: '',
          managementTermEnd: '',
          accountNo: '',
          bankName: '',
          bankChannelNo: '',
          salerId: '',
          salerName: '',
        },

        form_2: {
          certificateType: '',
          certificateValidityDateType: '',
          certificateNumber: '',
          certificateTermBegin: '',
          certificateTermEnd: '',
          idFileType: '',
          idCardHeadFileUrl: '',
          idCardEmblemFileUrl: '',
          idCardCopyFileUrl: '',
          name: '',
          contactPhone: '',
          contactName: '',
          contactEmail: '',
          remark: '',
        },

        idCardHeadFileUrl: [],
        idCardEmblemFileUrl: [],
        idCardCopyFileUrl: [],

        businessLicenseFileUrl: [],

        result: null,

        staffList: [],
        mainstayList: [],
        productList: [],

        rules: {
          agentName: [
            {required: true, message: '请输入名称', trigger: 'blur'}
          ],
          shortName: [
            {required: true, message: '请输入简称', trigger: 'blur'}
          ],
          taxNo: [
            {required: true, message: '请输入统一社会信用代码', trigger: 'blur'},
          ],
          contactPhone: [
            {required: true, message: '请输入手机号', trigger: 'blur'},
            {validator: validateParams({type: 'Phone', msg: '请输入正确的手机号码'}), trigger: 'blur'},
          ],
          contactName: [
            {required: true, message: '请输入姓名', trigger: 'blur'},
          ],
          contactEmail: [
            {required: true, message: '请输入邮箱', trigger: 'blur'},
            {validator: validateParams({type: 'Email', msg: '请输入正确的邮箱'}), trigger: 'blur'},
          ],
          certificateNumber: [
            {required: true, message: '请输入身份证号码', trigger: 'blur'},
          ],
          certificateType: [
            {required: true, message: '请选择证件类型', trigger: 'change'}
          ],
          accountNo: [
            {required: true, message: '请输入银行卡号', trigger: 'blur'}
          ],
          registerAddrProvince: [
            {required: true, message: '请选择注册地址', trigger: 'blur'}
          ],
          managementValidityDateType: [
            {required: true, message: "请选择营业期限", trigger: "change"},
            {
              validator: function (rule, value, callback) {
                let result = true;
                if (value == 1) {
                  if (!self.form_1.managementTermBegin || !self.form_1.managementTermEnd) {
                    result = false;
                  }
                } else if (value == 1) {
                  if (!self.form_1.managementTermBegin) {
                    result = false;
                  }
                }
                if (result == false) {
                  callback(new Error('请选择营业期限'))
                } else {
                  callback()
                }
              }
              , trigger: 'blur'
            }
          ],
          managementScope: [
            {required: true, message: '请输入经营范围', trigger: 'blur'}
          ],
          name: [
            {required: true, message: '请输入法定代表人姓名', trigger: 'blur'}
          ],
          bankChannelNo: [
            {required: true, message: '请选择联行号', trigger: 'blur'}
          ],
          bankName: [
            {required: true, message: '请选择联行号', trigger: 'blur'}
          ],
          businessLicenseFileUrl: [
            {
              required: true, validator: function (rule, value, callback) {
                if (self.businessLicenseFileUrl.length == 0) {
                  callback(new Error('请上传营业执照'));
                  return;
                }
                callback();
              }
            }
          ],
          salerId: [
            {required: true, message: '请选择销售', trigger: 'change'}
          ],
        },

        flowVisible: false, // 审批意见显示控制
        flowRemark: '',

        feeVisible: false, // 计费弹窗控制
        feeInfo: {},

      }
    },
    computed: {
      certificateRules() {
        if (this.$dictCode('CertificateTypeEnum', this.form_2.certificateType).desc == '身份证') {
          return [
            {required: true, message: "请输入证件号码", trigger: "blur"},
            {validator: validateParams({type: 'IdCard', msg: '请输入正确的证件号码'}), trigger: 'blur'},
          ]
        } else {
          return [
            {required: true, message: "请输入证件号码", trigger: "blur"},
            {validator: validateParams({type: 'Length', msg: '请输入正确的证件号码', min: 1}), trigger: 'blur'},
          ]
        }
      },
      actionType() {
        return this.$route.query.actionType || 'ADD';
      },
      processId() {
        return this.$route.query.processId || '';
      },
      taskId() {
        return this.$route.query.taskId || '';
      },
    },
    async mounted() {
      await this.init();

      if (this.actionType == 'EDIT') {
        let agentInfo = sessionStorage.getItem('agentInfo');
        if (!agentInfo) return;
        this.agentInfo = agentInfo = JSON.parse(agentInfo);
        this.defaultAddress = agentInfo.registerAddrProvince + agentInfo.registerAddrCity + agentInfo.registerAddrTown
        if (agentInfo.idCardCopyFileUrl) {
          agentInfo.idFileType = '101';
          this.idCardCopyFileUrl.push({
            name: '复印件',
            url: this.fileUrl + agentInfo.idCardCopyFileUrl
          })
        } else {
          agentInfo.idFileType = '100';
          if (agentInfo.idCardHeadFileUrl) {
            this.idCardHeadFileUrl.push({
              name: '人像面',
              url: this.fileUrl + agentInfo.idCardHeadFileUrl
            })
          }
          if (agentInfo.idCardEmblemFileUrl) {
            this.idCardEmblemFileUrl.push({
              name: '国徽面',
              url: this.fileUrl + agentInfo.idCardEmblemFileUrl
            })
          }
        }

        if (agentInfo.businessLicenseFileUrl) {
          this.businessLicenseFileUrl.push({
            name: '营业执照',
            url: this.fileUrl + agentInfo.businessLicenseFileUrl
          })
        }
        for (let i = 1; i <= 2; i++) {
          let form = 'form_' + i;
          for (let p in this[form]) {
            this[form][p] = agentInfo[p];
          }
        }

      }
    },
    beforeDestroy() {
      sessionStorage.removeItem('agentInfo');
    },
    methods: {
      async init() {
        const { data: staffList } = await getSales();
        this.staffList = staffList;
        if (this.actionType == 'ADD') {
          this.staffList.forEach(staff => {
            if (staff.id == this.userID) {
              this.form_1.salerId = staff.id;
              this.form_1.salerName = staff.realName;
            }
          });
        }
        const { data: agentList } = await getAllAgentSimple({ salerId: this.form_1.salerId });
        this.agentList = agentList;
      },
      handleSalerChange(val) {
        for (let i = 0; i < this.staffList.length; i++) {
          if (this.staffList[i].id == val) {
            this.form_1.salerName = this.staffList[i].realName;
            break;
          }
        }
      },
      handleSuccess(response, file, fileList, type, ref) {
        this[type] = fileList;
        if (ref) {
          this.$refs[ref].clearValidate();
        }
      },
      handleRemove(file, fileList, type) {
        this[type] = fileList
      },
      async nextStep() {
        let form = this.$refs['form_' + this.activeStep];
        const valid = await form.validate().catch(_ => false);
        if (!valid) {
          return this.$message.error('请填写必要信息')
        }
        if (this.activeStep == 2) {
          if (!this.flowVisible && this.processId) {
            this.flowVisible = true;
            return;
          }
          this.flowVisible = false;
          let formData = {...this.agentInfo, ...this.form_1, ...this.form_2};
          if (this.form_2.idFileType == 100) {
            if (this.idCardHeadFileUrl) {
              for (let item of this.idCardHeadFileUrl) {
                if (item.response) {
                  item.response && (formData.idCardHeadFileUrl = item.response.data)
                } else {
                  item.url && (formData.idCardHeadFileUrl = item.url.replace(this.fileUrl, ''))
                }
              }
            }
            if (this.idCardEmblemFileUrl) {
              for (let item of this.idCardEmblemFileUrl) {
                if (item.response) {
                  item.response && (formData.idCardEmblemFileUrl = item.response.data)
                } else {
                  item.url && (formData.idCardEmblemFileUrl = item.url.replace(this.fileUrl, ''))
                }
              }
            }
            formData.idCardCopyFileUrl = '';
          } else {
            if (this.idCardCopyFileUrl) {
              for (let item of this.idCardCopyFileUrl) {
                if (item.response) {
                  item.response && (formData.idCardCopyFileUrl = item.response.data)
                } else {
                  item.url && (formData.idCardCopyFileUrl = item.url.replace(this.fileUrl, ''))
                }
              }
            }
            formData.idCardHeadFileUrl = formData.idCardEmblemFileUrl = '';
          }

          if (this.businessLicenseFileUrl) {
            for (let item of this.businessLicenseFileUrl) {
              if (item.response) {
                item.response && (formData.businessLicenseFileUrl = item.response.data)
              } else {
                item.url && (formData.businessLicenseFileUrl = item.url.replace(this.fileUrl, ''))
              }
            }
          }

          delete formData.idFileType;

          if (formData.inviterNo) {
            for (let agent of this.agentList) {
              if (agent.agentNo == formData.inviterNo) {
                formData.inviterName = agent.agentName;
                break;
              }
            }
          } else {
            formData.inviterName = '';
          }

          if (formData.managementValidityDateType == 2) {
            delete formData.managementTermEnd
          }
          if (formData.certificateValidityDateType == 2) {
            delete formData.certificateTermEnd
          }

          clearVoid(formData);

          if (this.actionType == 'ADD') {
            const {data} = await createAgentCompany(formData);
            this.$message.success('创建成功');
            this.result = data;
            this.activeStep++;
          } else {
            formData.agentType = this.agentInfo.agentType;
            formData.agentNo = this.agentInfo.agentNo;
            if (this.processId) {
              const {data} = await editBusinessData({
                commonFlowId: this.processId,
                extInfo: JSON.stringify(formData),
                taskId: this.taskId,
                remark: this.flowRemark,
              })
              this.$message.success('修改成功');
              this.$router.push({
                path: '/waitingHandle/detailProcess',
                query: {
                  processId: this.processId,
                  taskId: this.taskId,
                }
              })
            } else {
              formData.agentNo = this.agentInfo.agentNo
              await updateCompany({
                extObj: formData,
                condition: {},
                participant: {},
              });
              this.$message.success('修改成功');
              this.$router.push('/sales/agent/list');
            }
          }
        } else {
          this.activeStep++;
        }
      },
      cancel() {
        if (this.activeStep == 1) {
          this.$router.back();
        } else {
          this.activeStep--;
        }
      },
      goDetailProcess() {
        this.$router.push('/waitingHandle/detailProcess?processId=' + this.result.commonFlowId);
      },
      searchBankNumberInfo() {
        this.$refs.bankNumberInfo.search()
        this.$refs.bankNumberInfo.isShow = true;
      },
      closeBankNumberInfoDialog(item) {
        let form = this['form_' + this.activeStep]
        if (item) {
          this.$refs.bankChannel.clearValidate();
          this.$set(form, 'bankChannelNo', item.bankChannelNo)
          this.$set(form, 'bankName', item.bankName)
        }
      },
      beforeFileUpload(file) {
        return this.validateUploadFile(({
          size: 6,
          img: true,
        }))(file)
      },
      flowConfirm(remark) {
        this.flowRemark = remark;
        this.nextStep();
      },
    }
  }
</script>

<style lang="scss" scoped>
  .page-container {
    .operate-area {
      padding-left: 40px;
      line-height: 40px;
    }
  }
</style>
