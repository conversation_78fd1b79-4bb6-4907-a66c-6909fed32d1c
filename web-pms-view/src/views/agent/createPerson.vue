<template>
  <div class="page-container">
    <div class="header-container">
      <el-steps
        :active="activeStep"
        align-center
      >
        <el-step title="填写主体信息"></el-step>
        <el-step title="审核与公示"></el-step>
      </el-steps>
    </div>

    <div class="content-container">
      <el-form
        v-show="activeStep == 1"
        ref="form_1"
        label-width="400px"
        :model="form_1"
        :rules="rules"
      >
        <div class="hgroup">
          <h2 style="font-size: 30px">填写主体信息</h2>
          <h4><span class="color-gray">填写主体信息，提交平台进行业务审核</span></h4>
        </div>
        <p class="subTitle">合伙人信息</p>
        <el-form-item label="主体类型：">
          个人
        </el-form-item>
        <el-form-item
          label="销售："
          prop="salerId"
        >
          <el-select
            :disabled="!hadPermission('pms:crm:role')"
            v-model="form_1.salerId"
            @change="handleSalerChange"
          >
            <el-option
              v-for="item in staffList"
              :key="item.id"
              :label="item.realName"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="邀请方："
          prop="inviterNo">
          <el-select
            filterable
            clearable
            v-model="form_1.inviterNo"
            :disabled="!form_1.salerId">
            <el-option
              v-for="(item, index) in agentList"
              :key="index"
              :label="item.agentName"
              :value="item.agentNo"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="个人姓名："
          prop="agentName">
          <el-input
            clearable
            v-model="form_1.agentName"
            maxlength="20"></el-input>
          <p class="color-gray form-tip">请输入个人姓名，该名称需与在智享汇的银行卡所对应的账户名称一致</p>
        </el-form-item>
        <el-form-item
          label="手机号："
          prop="contactPhone">
          <el-input
            clearable
            v-model="form_1.contactPhone"></el-input>
          <p class="color-gray form-tip">用于接收日常经营提醒及相关操作验证</p>
        </el-form-item>
        <el-form-item
          label="邮箱："
          prop="contactEmail">
          <el-input
            clearable
            v-model="form_1.contactEmail"></el-input>
        </el-form-item>

        <p class="subTitle">个人证件</p>
        <el-form-item
          label="身份证号码："
          prop="certificateNumber"
          :rules="certificateRules"
          maxlength="30">
          <el-input
            clearable
            v-model="form_1.certificateNumber"></el-input>
        </el-form-item>
        <el-form-item
          label="证件类型："
          prop="certificateType">
          <el-select
            clearable
            v-model="form_1.certificateType"
          >
            <el-option
              v-for="item in $dict('CertificateTypeEnum')"
              :key="item.code"
              :value="Number(item.code)"
              :label="item.desc"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item
          label="证件照："
          prop="idFileType"
          ref="idFile">
          <el-radio-group v-model="form_1.idFileType">
            <el-radio label="100">彩色原件</el-radio>
            <el-radio label="101">复印件</el-radio>
          </el-radio-group>

          <div v-show="form_1.idFileType == '100'">
            <p>
              人像面
              <el-popover
                width="300"
                trigger="click"
              >
                <img
                  class="example-image"
                  src="@/assets/example-2.png"
                >
                <el-button
                  slot="reference"
                  type="text"
                >查看示例
                </el-button>
              </el-popover>
            </p>
            <el-upload
              v-toggle="idCardHeadFileUrl"
              key="idCardHeadFileUrl"
              class="upload-demo"
              list-type="picture-card"
              accept=".png,.gif,.jpg,.bmp"
              :action="baseUrl + '/file/upload'"
              :headers="uploadHeader"
              :limit="1"
              :file-list="idCardHeadFileUrl"
              :before-upload="beforeFileUpload"
              :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardHeadFileUrl', 'idFile')"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardHeadFileUrl')"
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
            <p>
              国徽面
              <el-popover
                width="300"
                trigger="click"
              >
                <img
                  class="example-image"
                  src="@/assets/example-3.png"
                >
                <el-button
                  slot="reference"
                  type="text"
                >查看示例
                </el-button>
              </el-popover>
            </p>
            <el-upload
              v-toggle="idCardEmblemFileUrl"
              key="idCardEmblemFileUrl"
              class="upload-demo"
              list-type="picture-card"
              accept=".png,.gif,.jpg,.bmp"
              :action="baseUrl + '/file/upload'"
              :headers="uploadHeader"
              :limit="1"
              :file-list="idCardEmblemFileUrl"
              :before-upload="beforeFileUpload"
              :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardEmblemFileUrl', 'idFile')"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardEmblemFileUrl')"
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>

          <div v-show="form_1.idFileType == '101'">
            <p>
              <el-popover
                width="300"
                trigger="click"
              >
                <img
                  class="example-image"
                  src="@/assets/idCardCopyFileExample.png"
                >
                <el-button
                  slot="reference"
                  type="text"
                >查看示例
                </el-button>
              </el-popover>
            </p>
            <el-upload
              v-toggle="idCardCopyFileUrl"
              key="idCardCopyFileUrl"
              class="upload-demo"
              :action="baseUrl + '/file/upload'"
              :headers="uploadHeader"
              :limit="1"
              list-type="picture-card"
              accept=".png,.gif,.jpg,.bmp"
              :file-list="idCardCopyFileUrl"
              :before-upload="beforeFileUpload"
              :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardCopyFileUrl', 'idFile')"
              :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardCopyFileUrl')"
            >
              <i class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </div>
        </el-form-item>

        <el-form-item
          label="证件有效期："
          prop="certificateValidityDateType">
          <el-radio-group v-model="form_1.certificateValidityDateType">
            <el-radio
              v-for="item in $dict('ValidityDateTypeEnum')"
              :key="item.code"
              :label="Number(item.code)"
            >{{ item.desc }}
            </el-radio>
          </el-radio-group>
          <date-picker
            v-if="form_1.certificateValidityDateType == 1"
            picker="separate"
            type="date"
            :is-show-fast-time="false"
            :use-option="false"
            :start-time.sync="form_1.certificateTermBegin"
            :end-time.sync="form_1.certificateTermEnd"
          ></date-picker>
          <div v-if="form_1.certificateValidityDateType == 2">
            <el-date-picker
              type="date"
              placeholder="选择起始日期"
              v-model="form_1.certificateTermBegin"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </div>
        </el-form-item>

        <p class="subTitle">银行卡信息</p>
        <el-form-item
          label="银行卡号："
          prop="accountNo">
          <el-input
            v-model="form_1.accountNo"
            @blur="getBankCardBin"
            maxlength="50">
            <el-button
              type="info"
              slot="append"
              @click="getBankCardBin">查询
            </el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="开户银行：">
          <el-input
            disabled
            v-model="form_1.bankName"></el-input>
        </el-form-item>
        <el-form-item label="(备选)支付宝账户：">
          <el-input
            v-model="form_1.alipayAccount"
            clearable></el-input>
        </el-form-item>

        <p class="subTitle">结算代扣规则</p>
        <el-form-item
          label="结算代扣规则："
          prop="selfDeclared">
          <el-radio-group v-model="form_1.selfDeclared">
            <el-radio
              v-for="item in $dict('SelfDeclaredEnum')"
              :key="item.code"
              :label="Number(item.code)"
            >
              {{ item.desc }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <div v-if="form_1.selfDeclared == 1">
          <el-form-item
            label="代扣税比例："
            prop="taxPercent">
            <el-input
              :value="form_1.taxPercent"
              @input="handleRateInput(form_1, 'taxPercent', $event, 0, 100)">
              <span slot="append">%</span>
            </el-input>
          </el-form-item>
        </div>

        <div class="other">
          <p class="subTitle">其他</p>
          <el-form-item
            label="备注："
            prop="remark">
            <el-input
              type="textarea"
              v-model="form_1.remark"
              :rows="5"
            ></el-input>
          </el-form-item>
        </div>

      </el-form>

      <div
        v-show="activeStep == 2"
        class="result-container">
        <img src="@/assets/success.png">
        <p class="result-title">提交成功</p>
        <p class="result-tip">合伙人新增成功</p>
        <div class="footer-container">
          <el-button
            type="primary"
            @click="$router.push('/sales/agent/list')">返回合伙人列表
          </el-button>
        </div>
      </div>

      <div
        class="form-btn-group"
        v-show="activeStep <= 1">
        <el-button
          type="primary"
          @click="nextStep">
          {{ activeStep == 2 ? '提交' : '下一步' }}
        </el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </div>

    <!--审批意见-->
    <flow-opinion
      :visible.sync="flowVisible"
      @change="flowConfirm"
    ></flow-opinion>

    <!--<agent-fee-rule-->
    <!--:visible.sync="feeVisible"-->
    <!--:product-list="productList"-->
    <!--:vendor-list="mainstayList"-->
    <!--:info="feeInfo"-->
    <!--@confirm="handleFeeRule"-->
    <!--&gt;</agent-fee-rule>-->

  </div>
</template>

<script>
import { getSales } from '@/api/system'
import { getAllAgentSimple, createAgentPerson, updatePerson } from '@/api/agent'
import { getProductList, getVendorList } from '@/api/product'
import { validateParams } from '@/utils/validate'
import { editBusinessData } from '@/api/flow';
import { getBankCardBin } from '@/api/common';
import { clearVoid } from '@/utils'
import FlowOpinion from '@/components/Flow/FlowOpinion'

export default {
  name: 'create-person-agent',
  components: {
    FlowOpinion,
  },
  data() {
    return {
      activeStep: 1,

      agentList: [],

      form_1: {
        inviterNo: '',
        inviterName: '',
        agentName: '',
        accountNo: '',
        bankName: '',
        contactPhone: '',
        contactEmail: '',
        certificateType: '',
        certificateNumber: '',
        certificateTermBegin: '',
        certificateTermEnd: '',
        certificateValidityDateType: '',
        idFileType: '',
        idCardHeadFileUrl: '',
        idCardEmblemFileUrl: '',
        idCardCopyFileUrl: '',
        salerId: '',
        salerName: '',
        remark: '',
        selfDeclared: '',
        taxPercent: '',
      },

      idCardHeadFileUrl: [],
      idCardEmblemFileUrl: [],
      idCardCopyFileUrl: [],

      staffList: [],
      mainstayList: [],
      productList: [],

      rules: {
        agentName: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        contactPhone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { validator: validateParams({ type: 'Phone', msg: '请输入正确的手机号码' }), trigger: 'blur' },
        ],
        certificateNumber: [
          { required: true, message: '请输入身份证号码', trigger: 'blur' },
        ],
        contactEmail: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
        ],
        certificateType: [
          { required: true, message: '请选择证件类型', trigger: 'change' }
        ],
        accountNo: [
          { required: true, message: '请输入银行卡号', trigger: 'blur' }
        ],
        salerId: [
          { required: true, message: '请选择销售', trigger: 'change' }
        ],
        taxPercent: [
          { required: true, message: '请输入代扣税比例', trigger: 'blur' }
        ],
        selfDeclared: [
          { required: true, message: '请选择结算代扣规则', trigger: 'change' }
        ]
      },

      result: null,

      flowVisible: false, // 审批意见显示控制
      flowRemark: '',

      feeVisible: false, // 计费弹窗控制
      feeInfo: {},
    }
  },
  computed: {
    certificateRules() {
      if (this.$dictCode('CertificateTypeEnum', this.form_1.certificateType).desc == '身份证') {
        return [
          { required: true, message: "请输入证件号码", trigger: "blur" },
          { validator: validateParams({ type: 'IdCard', msg: '请输入正确的证件号码' }), trigger: 'blur' },
        ]
      } else {
        return [
          { required: true, message: "请输入证件号码", trigger: "blur" },
          { validator: validateParams({ type: 'Length', msg: '请输入正确的证件号码', min: 1 }), trigger: 'blur' },
        ]
      }
    },
    actionType() {
      return this.$route.query.actionType || 'ADD';
    },
    processId() {
      return this.$route.query.processId || '';
    },
    taskId() {
      return this.$route.query.taskId || '';
    },
  },
  async mounted() {
    await this.init();

    if (this.actionType == 'EDIT') {
      let agentInfo = sessionStorage.getItem('agentInfo');
      if (!agentInfo) return;
      this.agentInfo = agentInfo = JSON.parse(agentInfo);
      agentInfo.certificateType = Number(agentInfo.certificateType);
      if (agentInfo.idCardCopyFileUrl) {
        agentInfo.idFileType = '101';
        this.idCardCopyFileUrl.push({
          name: '复印件',
          url: this.fileUrl + agentInfo.idCardCopyFileUrl
        })
      } else {
        agentInfo.idFileType = '100';
        if (agentInfo.idCardHeadFileUrl) {
          this.idCardHeadFileUrl.push({
            name: '人像面',
            url: this.fileUrl + agentInfo.idCardHeadFileUrl
          })
        }
        if (agentInfo.idCardEmblemFileUrl) {
          this.idCardEmblemFileUrl.push({
            name: '国徽面',
            url: this.fileUrl + agentInfo.idCardEmblemFileUrl
          })
        }
      }
      for (let p in this.form_1) {
        this.form_1[p] = agentInfo[p];
      }
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('agentInfo');
  },
  methods: {
    async init() {
      const { data: staffList } = await getSales();
      this.staffList = staffList;
      if (this.actionType == 'ADD') {
        this.staffList.forEach(staff => {
          if (staff.id == this.userID) {
            this.form_1.salerId = staff.id;
            this.form_1.salerName = staff.realName;
          }
        });
      }
      const { data: agentList } = await getAllAgentSimple({ salerId: this.form_1.salerId });
      this.agentList = agentList;
    },
    handleSalerChange(val) {
      for (let i = 0; i < this.staffList.length; i++) {
        if (this.staffList[i].id == val) {
          this.form_1.salerName = this.staffList[i].realName
        }
      }
    },
    handleSuccess(response, file, fileList, type, ref) {
      this[type] = fileList;
      if (ref) {
        this.$refs[ref].clearValidate();
      }
    },
    handleRemove(file, fileList, type) {
      this[type] = fileList
    },
    async nextStep() {
      let form = this.$refs['form_' + this.activeStep];
      const valid = await form.validate().catch(_ => false);
      if (!valid) {
        return this.$message.error('请填写必填信息')
      }
      if (this.activeStep == 1) {
        if (!this.flowVisible && this.processId) {
          this.flowVisible = true;
          return;
        }
        this.flowVisible = false;
        let formData = { ...this.agentInfo, ...this.form_1 };
        if (this.form_1.idFileType == 100) {
          if (this.idCardHeadFileUrl) {
            for (let item of this.idCardHeadFileUrl) {
              if (item.response) {
                item.response && (formData.idCardHeadFileUrl = item.response.data)
              } else {
                item.url && (formData.idCardHeadFileUrl = item.url.replace(this.fileUrl, ''))
              }
            }
          }
          if (this.idCardEmblemFileUrl) {
            for (let item of this.idCardEmblemFileUrl) {
              if (item.response) {
                item.response && (formData.idCardEmblemFileUrl = item.response.data)
              } else {
                item.url && (formData.idCardEmblemFileUrl = item.url.replace(this.fileUrl, ''))
              }
            }
          }
          delete formData.idCardCopyFileUrl
        } else {
          if (this.idCardCopyFileUrl) {
            for (let item of this.idCardCopyFileUrl) {
              if (item.response) {
                item.response && (formData.idCardCopyFileUrl = item.response.data)
              } else {
                item.url && (formData.idCardCopyFileUrl = item.url.replace(this.fileUrl, ''))
              }
            }
          }
          delete formData.idCardHeadFileUrl;
          delete formData.idCardEmblemFileUrl;
        }

        delete formData.idFileType;

        if (formData.inviterNo) {
          for (let agent of this.agentList) {
            if (agent.agentNo == formData.inviterNo) {
              formData.inviterName = agent.agentName;
              break;
            }
          }
        } else {
          formData.inviterName = '';
        }
        clearVoid(formData);
        if (this.actionType == 'ADD') {
          const { data } = await createAgentPerson(formData);
          this.$message.success('创建成功');
          this.result = data;
          this.activeStep++
        } else {
          formData.agentType = this.agentInfo.agentType;
          formData.agentNo = this.agentInfo.agentNo;
          if (this.processId) {
            // 修改审批信息
            const { data } = await editBusinessData({
              extInfo: JSON.stringify(formData),
              commonFlowId: this.processId,
              taskId: this.taskId,
              remark: this.flowRemark,
            })
            this.$message.success('修改成功');
            this.$router.push({
              path: '/waitingHandle/detailProcess',
              query: {
                processId: this.processId,
                taskId: this.taskId,
              }
            })
          } else {
            // 修改主体信息
            await updatePerson({
              extObj: formData,
              condition: {},
              participant: {}
            });
            this.$message.success('修改成功');
            this.$router.push('/sales/agent/list');
          }
        }
      } else {
        this.activeStep++;
      }
    },
    cancel() {
      if (this.activeStep == 1) {
        this.$router.back();
      } else {
        this.activeStep--;
      }
    },
    async getBankCardBin() {
      const { data } = await getBankCardBin({
        cardNo: this.form_1.accountNo
      })
      this.form_1.bankName = (data && data.bankName) || '';
    },
    beforeFileUpload(file) {
      return this.validateUploadFile({
        img: true,
        size: 6
      })(file)
    },
    flowConfirm(remark) {
      this.flowRemark = remark;
      this.nextStep();
    },
  }
}
</script>

<style
  lang="scss"
  scoped>
.page-container {
  .operate-area {
    padding-left: 40px;
    line-height: 40px;
  }
}
</style>
