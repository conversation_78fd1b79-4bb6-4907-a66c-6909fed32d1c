<template>
  <el-dialog
    :visible="visible"
    :before-close="close"
    title="请选择销售"
    width="500px"
    append-to-body
  >
    <el-form
      :model="form"
      label-width="150px"
      ref="form"
    >
      <el-form-item label="选择销售：">
        <el-select v-model="form.salerId" clearable>
          <el-option
            v-for="item in staffList"
            :key="item.id"
            :label="item.realName"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审批备注：">
        <el-input
          type="textarea"
          v-model="form.remark"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button
        type="primary"
        @click="confirm"
      >确认</el-button>
      <el-button @click="close">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    props: {
      staffList: {
        type: Array,
        default: () => ([])
      }
    },
    data() {
      return {
        visible: false,
        form: {
          salerId: '',
          remark: '',
        }
      }
    },
    methods: {
      close() {
        this.$refs.form.resetFields();
        this.visible = false;
      },
      confirm() {
        if (!this.form.salerId) {
          return this.$message.error('请选择销售')
        }
        for (let i = 0; i < this.staffList.length; i++) {
          if (this.staffList[i].id == this.form.salerId) {
            this.form.salerName = this.staffList[i].realName;
            break;
          }
        }
        this.$emit('change', {
          sellerId: this.form.salerId || '',
          sellerName: this.form.salerName || '',
          remark: this.form.remark
        });
        this.close();
      }
    }
  }
</script>

<style>
</style>
