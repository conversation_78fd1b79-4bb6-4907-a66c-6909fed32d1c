<template>
  <el-dialog
    :visible="visible"
    :title="title"
    :before-close="close"
  >
    <template v-if="status == 103">
      <p>该合伙人当前关联 {{agent.merNum}} 个商户，且为 {{agent.invitationNum}} 个合伙人的邀请方</p>
      <p>被清退后无法恢复，请谨慎操作！</p>
    </template>
    <template v-else-if="status == 100">
      <p>激活后，合伙人将正常使用相关功能，请谨慎操作！</p>
    </template>
    <template v-else-if="status == 101">
      <p>冻结期间，合伙人将无法正常使用相关功能，请谨慎操作！</p>
      <p>注意，合伙人下属商户仍可正常使用，但商户交易将不计入合伙人佣金。</p>
    </template>

    <div slot="footer">
      <el-button
        type="primary"
        @click="confirm"
      >确认</el-button>
      <el-button @click="close">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    status: {
      type: [String, Number],
      default: '',
    },
    agent: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: false,
    }
  },
  computed: {
    title() {
      return this.status == 103 ? '清退' : (this.status == 100 ? '激活' : '冻结')
    }
  },
  methods: {
    close() {
      this.visible = false;
    },
    confirm() {
      this.$emit('confirm');
      this.close();
    }
  }
}
</script>

<style>

</style>