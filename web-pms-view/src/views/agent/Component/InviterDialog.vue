<template>
  <el-dialog
    :visible="visible"
    :before-close="close"
    title="请选择邀请方"
    width="500px"
    append-to-body
  >
    <el-form
      :model="form"
      ref="form"
      label-width="150px"
    >
      <el-form-item label="选择邀请方：">
        <el-select v-model="form.inviterNo" clearable>
          <el-option
            v-for="item in agentList"
            :key="item.agentNo"
            :label="item.agentName"
            :value="item.agentNo"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审批备注：">
        <el-input
          type="textarea"
          v-model="form.remark"
        ></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button
        type="primary"
        @click="confirm"
      >确认</el-button>
      <el-button @click="close">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { getAllAgentSimple } from '@/api/agent'
  export default {
    props: {
      salerId: [Number, String]
    },
    data() {
      return {
        visible: false,
        agentList: [],
        form: {
          inviterNo: '',
          inviterName: '',
          remark: '',
        }
      }
    },
    watch: {
      visible(val) {
        if (!val) return;
        getAllAgentSimple({ salerId: this.salerId }).then(res => {
          this.agentList = res.data;
        })
      },
    },
    methods: {
      close() {
        this.$refs.form.resetFields();
        this.visible = false;
      },
      confirm() {
        for (let i = 0; i < this.agentList.length; i++) {
          if (this.agentList[i].agentNo === this.form.inviterNo) {
            this.form.inviterName = this.agentList[i].agentName;
            break
          }
        }
        this.$emit('change', { ...this.form });
        this.close();
      }
    }
  }
</script>

<style>
</style>
