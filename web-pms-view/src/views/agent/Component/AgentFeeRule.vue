<template>
  <el-dialog
    :visible.sync="visible"
    title="编辑计费"
    :before-close="close"
    width="900px"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form label-width="180px" :rules="rules" :model="form" ref="form" style="width: auto;">
      <el-form-item label="所属产品：" prop="productNo">
        <el-select clearable v-model="form.productNo" @change="selectProduct">
          <el-option v-for="item in productList" :key="item.productNo" :value="item.productNo" :label="item.productName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="成本及奖励设置：" prop="firstSecond" class="form-item-main">
        <el-table :data="firstSecond">
          <el-table-column prop="type" label="佣金类型" align="center"></el-table-column>
          <el-table-column prop="formulaType" label="公式类型" align="center">
            <template v-slot="{row}">
              <el-select
                v-model="row.FormulaType"
                size="small"
              >
                <el-option
                  v-for="item in $dict('FormulaEnum')"
                  :key="item.code"
                  :value="Number(item.code)"
                  :label="item.desc"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="fixedFee" label="固定金额" align="center">
            <template v-slot="{row}">
              <el-input
                type="text"
                :value="row.FixedFee"
                @input="handleRateInput(row, 'FixedFee', $event)"
                size="small"
                :disabled="row.FormulaType === 0">
                <span slot="append">元</span>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column prop="feeRate" label="费率" align="center">
            <template v-slot="{row}">
              <el-input
                type="text"
                size="small"
                :value="row.FeeRate"
                :disabled="row.FormulaType === 1"
                @input="handleRateInput(row, 'FeeRate', $event, 0, 100)">
                <span slot="append">%</span>
              </el-input>
            </template>
          </el-table-column>
        </el-table>
        <el-button type="text" class="func-content" @click="() => {rewardVisible = true}">奖励试算&gt;</el-button>
      </el-form-item>

      <el-form-item class="form-item-main" label="特殊计费-规则参数：" v-if="form.ruleType == 1" prop="specialRule">
        <el-button @click="addRuleList">添加</el-button>
        <el-table :data="form.ruleParam">
          <el-table-column width="50">
            <template v-slot="{$index}">
              <span>{{ $index > 0 ? '且' : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column>
            <template v-slot="{row, $index}">
              <el-select clearable v-model="row.specialRuleType" @change="selectRuleType($index)" :disabled="$index == 0">
                <el-option v-for="item in $dict('AgentSpecialRuleTypeEnum')" :key="item.code" :value="Number(item.code)" :label="item.desc"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column>
            <template v-slot="{row, $index}">
              <div v-if="row.specialRuleType">
                <el-select clearable v-model="row.compareType" :disabled="$index == 0 || row.specialRuleType == 1">
                  <el-option :value="1" label="等于"></el-option>
                  <el-option :value="2" label="小于"></el-option>
                  <el-option :value="3" label="大于"></el-option>
                </el-select>
              </div>
            </template>
          </el-table-column>
          <el-table-column>
            <template v-slot="{row, $index}">
              <el-select clearable v-if="row.specialRuleType == 1" v-model="row.value" :disabled="$index == 0">
                <el-option
                  v-for="item in vendorList"
                  :key="item.vendorNo"
                  :value="item.vendorNo"
                  :label="item.vendorName"></el-option>
              </el-select>
              <el-input v-if="row.specialRuleType != '1'" v-model="row.value"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template v-slot="{$index}">
              <el-button v-if="$index > 0" type="text" @click="deleteRule($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-input :value="form.priority" @input="handleNumInput(form, 'priority', $event)"></el-input>
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <el-button type="primary" @click="confirm">确定</el-button>
      <el-button @click="close">取消</el-button>
    </template>

    <RewardCalcDialog :visible.sync="rewardVisible"></RewardCalcDialog>

  </el-dialog>
</template>

<script>
  import RewardCalcDialog from '@/views/agent/Component/RewardCalcDialog.vue';

  export default {
    name: "AgentFeeRule",
    components: {
      RewardCalcDialog,
    },
    props: {
      info: {
        type: Object,
        default: () => ({})
      },
      visible: {
        type: Boolean,
        default: false,
      },
      productList: {
        type: Array,
        default: () => [],
      },
      vendorList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      const self = this;

      return {
        rules: {
          productNo: [{required: true, message: '请选择所属产品', trigger: 'change'}],
          minFee: [{required: true, message: '请输入单笔最低手续费'}],
          maxFee: [{required: true, message: '请输入单笔最高手续费'}],
          ruleType: [{required: true, message: '请选择规则类型', trigger: 'change'}],
          priority: [{required: true, message: '请输入优先级', trigger: 'blur'}],

          firstSecond: [
            {
              required: true, validator: function (rule, value, callback) {
                let result = true;
                for (let i = 0; i < self.firstSecond.length; i++) {
                  if (i === 0 && self.firstSecond[i].FormulaType === '') {
                    result = false;
                    break
                  }
                  if (self.firstSecond[i].FormulaType === 0) {
                    result = (self.firstSecond[i].FeeRate !== '')
                  } else if (self.firstSecond[i].FormulaType === 1) {
                    result = (self.firstSecond[i].FixedFee !== '')
                  } else if (self.firstSecond[i].FormulaType === 2) {
                    result = (self.firstSecond[i].FeeRate !== '' && self.firstSecond[i].fixedFee !== '')
                  }
                  if (!result) {
                    break;
                  }
                }
                if (result) {
                  callback();
                } else {
                  callback(new Error('请设置规则'))
                }
              }, trigger: 'blur'
            }
          ]
        },
        form: {
          mainstayName: '',
          mainstayNo: '',
          productNo: "",
          productName: '',
          minFee: "0.01",
          maxFee: "99999999.00",
          ruleType: "",
          ruleParam: [],
          priority: '',

          formulaType: '',
          fixedFee: '',
          feeRate: '',

          secondFormulaType: '',
          secondFixedFee: '',
          secondFeeRate: '',
        },
        firstSecond: [
          {type: '合伙人成本（1级佣金）', FormulaType: '', FixedFee: '', FeeRate: ''},
          {type: '邀请奖励（2级佣金）', FormulaType: '', FixedFee: '', FeeRate: ''},
        ],
        rewardVisible: false,
      }
    },
    watch: {
      visible(val) {
        if (val) {
          for (let p in this.form) {
            if (this.info.hasOwnProperty(p)) {
              this.form[p] = this.info[p];
            }
          }
          this.firstSecond[0].FormulaType = this.form.formulaType;
          this.firstSecond[0].FixedFee = this.form.fixedFee;
          this.firstSecond[0].FeeRate = this.form.feeRate;
          this.firstSecond[1].FormulaType = this.form.secondFormulaType;
          this.firstSecond[1].FixedFee = this.form.secondFixedFee;
          this.firstSecond[1].FeeRate = this.form.secondFeeRate;
          if (this.form.mainstayNo) {
            this.form.ruleType = 1;
          }
        } else {
          this.$refs.form.resetFields();
          this.form.ruleParam = []
        }
      }
    },
    methods: {
      selectProduct(val) {
        this.productList.forEach(item => {
          if (item.productNo == val) {
            this.form.productName = item.productName;
          }
        })
      },
      async confirm() {
        const valid = await this.$refs.form.validate().catch(_ => false);
        if (!valid) return;
        if (this.form.ruleType == 0) {
          this.form.ruleParam = [];
          this.form.mainstayNo = '';
          this.form.mainstayName = '';
        }
        this.form.secondFormulaType = this.firstSecond[1].FormulaType;
        this.form.secondFixedFee =  this.firstSecond[1].FixedFee;
        this.form.secondFeeRate = this.firstSecond[1].FeeRate;
        this.form.formulaType = this.firstSecond[0].FormulaType;
        this.form.fixedFee =  this.firstSecond[0].FixedFee;
        this.form.feeRate = this.firstSecond[0].FeeRate;

        this.$emit('confirm', { ...this.form });
        this.close();
      },
      close() {
        this.$emit('update:visible', false);
      },
      selectRuleType(index) {
        this.form.ruleParam[index].compareType = 1;
      },
      addRuleList() {
        this.form.ruleParam.push({
          specialRuleType: '',
          compareType: '',
          value: ''
        })
      },
      deleteRule(index) {
        this.form.ruleParam.splice(index, 1)
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
