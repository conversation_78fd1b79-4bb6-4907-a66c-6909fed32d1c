<template>
  <el-dialog
    :visible="visible"
    title="奖励试算"
    :before-close="close"
    width="1000px"
    class="dialog-wrapper"
    append-to-body
  >
    <el-tabs v-model="activeTab" type="card">
      <el-tab-pane label="模式1" name="1"></el-tab-pane>
      <el-tab-pane label="模式2" name="2"></el-tab-pane>
      <el-tab-pane label="模式3" name="3"></el-tab-pane>
    </el-tabs>

    <el-tag type="warning" v-if="activeTab == 1">适用于运营、管理人员检查计费设置合理性</el-tag>
    <el-tag type="warning" v-if="activeTab == 2">适用于销售人员检查计费设置合理性（有合伙人）</el-tag>
    <el-tag type="warning" v-if="activeTab == 3">适用于销售人员检查计费设置合理性（无合伙人）</el-tag>

    <el-form class="calc-form">
      <div class="form-item" v-show="activeTab == 1">
        <p class="item-title">供应商成本</p>
        <el-select
          v-model="form.vendorType"
          size="small"
        >
          <el-option
            v-for="item in $dict('FormulaEnum')"
            :key="item.code"
            :label="item.desc"
            :value="Number(item.code)"
          ></el-option>
        </el-select>
        <el-input
          size="small"
          :value="form.vendorRate"
          @input="handleRateInput(form, 'vendorRate', $event, 0, 100)"
          v-show="form.vendorType !== 1">
          <span slot="append">%</span>
        </el-input>
        <el-input
          size="small"
          :value="form.vendorFixed"
          @input="handleRateInput(form, 'vendorFixed', $event)"
          v-show="form.vendorType !== 0">
          <span slot="append">元</span>
        </el-input>
      </div>
      <div class="form-item">
        <p class="item-title">销售成本</p>
        <el-select
          v-model="form.saleType"
          size="small"
        >
          <el-option
            v-for="item in $dict('FormulaEnum')"
            :key="item.code"
            :label="item.desc"
            :value="Number(item.code)"
          ></el-option>
        </el-select>
        <el-input
          size="small"
          :value="form.saleRate"
          @input="handleRateInput(form, 'saleRate', $event, 0, 100)"
          v-show="form.saleType !== 1">
          <span slot="append">%</span>
        </el-input>
        <el-input
          size="small"
          :value="form.saleFixed"
          @input="handleRateInput(form, 'saleFixed', $event)"
          v-show="form.saleType !== 0">
          <span slot="append">元</span>
        </el-input>
      </div>
      <div class="form-item inline" v-show="activeTab != 3">
        <p class="item-title">合伙人成本</p>
        <el-select
          v-model="form.agentType"
          size="small"
        >
          <el-option
            v-for="item in $dict('FormulaEnum')"
            :key="item.code"
            :label="item.desc"
            :value="Number(item.code)"
          ></el-option>
        </el-select>
        <el-input
          size="small"
          :value="form.agentRate"
          @input="handleRateInput(form, 'agentRate', $event, 0, 100)"
          v-show="form.agentType !== 1">
          <span slot="append">%</span>
        </el-input>
        <el-input
          size="small"
          :value="form.agentFixed"
          @input="handleRateInput(form, 'agentFixed', $event)"
          v-show="form.agentType !== 0">
          <span slot="append">元</span>
        </el-input>
      </div>
      <div class="form-item inline" v-show="activeTab != 3">
        <p class="item-title">合伙人邀请方的邀请奖励</p>
        <el-select
          v-model="form.inviteType"
          size="small"
        >
          <el-option
            v-for="item in $dict('FormulaEnum')"
            :key="item.code"
            :label="item.desc"
            :value="Number(item.code)"
          ></el-option>
        </el-select>
        <el-input
          size="small"
          :value="form.inviteRate"
          @input="handleRateInput(form, 'inviteRate', $event, 0, 100)"
          v-show="form.inviteType !== 1">
          <span slot="append">%</span>
        </el-input>
        <el-input
          size="small"
          :value="form.inviteFixed"
          @input="handleRateInput(form, 'inviteFixed', $event)"
          v-show="form.inviteType !== 0">
          <span slot="append">元</span>
        </el-input>
      </div>
      <div class="form-item">
        <p class="item-title">商户计费</p>
        <el-select
          v-model="form.merchantType"
          size="small"
        >
          <el-option
            v-for="item in $dict('FormulaEnum')"
            :key="item.code"
            :label="item.desc"
            :value="Number(item.code)"
          ></el-option>
        </el-select>
        <el-input
          size="small"
          :value="form.merchantRate"
          @input="handleRateInput(form, 'merchantRate', $event, 0, 100)"
          v-show="form.merchantType !== 1">
          <span slot="append">%</span>
        </el-input>
        <el-input
          size="small"
          :value="form.merchantFixed"
          @input="handleRateInput(form, 'merchantFixed', $event)"
          v-show="form.merchantType !== 0">
          <span slot="append">元</span>
        </el-input>
      </div>
    </el-form>

    <el-divider></el-divider>

    <p class="fee-title">奖励试算</p>
    <p class="fee-title">假设商户交易金额为 {{ money | moneyFormat }}，此时：</p>
    <div class="fee-result">
      <span>商户手续费为 {{ merchantFee | moneyFormat }}</span>
      <span v-show="activeTab != 3">，所属合伙人成本为{{ agentFee | moneyFormat}}</span>
      <span>，销售成本为{{ saleFee | moneyFormat}}</span>
      <span v-show="activeTab == 1">，供应商成本为{{ vendorFee | moneyFormat}}</span>
    </div>
    <div class="fee-result">
      <p v-show="activeTab != 3">该合伙人的交易分润为<span class="profit">{{ agentProfit | moneyFormat }}</span></p>
      <p v-show="activeTab != 3">，合伙人邀请方获得的邀请奖励为<span class="profit">{{ inviterProfit | moneyFormat }}</span>，</p>
      <p>销售毛利为<span class="profit">{{ saleProfit | moneyFormat }}</span></p>
      <p v-show="activeTab == 1">，平台毛利为<span class="profit">{{ platProfit | moneyFormat }}</span></p>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {

        money: 1000, // 交易金额

        activeTab: '1',

        form: {
          vendorType: 0,
          vendorFixed: 0,
          vendorRate: 0,

          saleType: 0,
          saleFixed: 0,
          saleRate: 0,

          agentType: 0,
          agentFixed: 0,
          agentRate: 0,

          inviteType: 0,
          inviteFixed: 0,
          inviteRate: 0,

          merchantType: 0,
          merchantFixed: 0,
          merchantRate: 0,
        }
      }
    },
    computed: {
      merchantFee() {
        let rateMoney = this.money * this.form.merchantRate / 100;
        rateMoney = Math.floor(rateMoney * 100) / 100;
        let fixedMoney = Number(this.form.merchantFixed);
        fixedMoney = Math.floor(fixedMoney * 100) / 100;
        if (this.form.merchantType == 0) {
          return rateMoney;
        } else if (this.form.merchantType == 1) {
          return fixedMoney;
        } else {
          return rateMoney + fixedMoney;
        }
      },
      agentFee() {
        let rateMoney = this.money * this.form.agentRate / 100;
        rateMoney = Math.floor(rateMoney * 100) / 100;
        let fixedMoney = Number(this.form.agentFixed);
        fixedMoney = Math.floor(fixedMoney * 100) / 100;
        if (this.form.agentType == 0) {
          return rateMoney;
        } else if (this.form.agentType == 1) {
          return fixedMoney;
        } else {
          return rateMoney + fixedMoney;
        }
      },
      saleFee() {
        let rateMoney = this.money * this.form.saleRate / 100;
        rateMoney = Math.floor(rateMoney * 100) / 100;
        let fixedMoney = Number(this.form.saleFixed);
        fixedMoney = Math.floor(fixedMoney * 100) / 100;
        if (this.form.saleType == 0) {
          return rateMoney;
        } else if (this.form.saleType == 1) {
          return fixedMoney;
        } else {
          return rateMoney + fixedMoney;
        }
      },
      vendorFee() {
        let rateMoney = this.money * this.form.vendorRate / 100;
        rateMoney = Math.floor(rateMoney * 100) / 100;
        let fixedMoney = Number(this.form.vendorFixed);
        fixedMoney = Math.floor(fixedMoney * 100) / 100;
        if (this.form.vendorType == 0) {
          return rateMoney;
        } else if (this.form.vendorType == 1) {
          return fixedMoney;
        } else {
          return rateMoney + fixedMoney;
        }
      },
      inviterProfit() {
        let rateMoney = this.money * this.form.inviteRate / 100;
        rateMoney = Math.floor(rateMoney * 100) / 100;
        let fixedMoney = Number(this.form.inviteFixed);
        fixedMoney = Math.floor(fixedMoney * 100) / 100;
        if (this.form.inviteType == 0) {
          return rateMoney;
        } else if (this.form.inviteType == 1) {
          return fixedMoney;
        } else {
          return rateMoney + fixedMoney;
        }
      },
      agentProfit() {
        if (this.activateTab == 3) {
          return 0
        } else if (this.activeTab == 2) {
          return this.merchantFee - this.agentFee
        }
        return null
      },
      saleProfit() {
        if (this.activeTab == 3) {
          return this.merchantFee - this.saleFee;
        } else {
          return this.agentFee - this.saleFee;
        }
      },
      platProfit() {
        return this.saleFee - this.vendorFee;
      }
    },
    methods: {
      close() {
        this.$emit('update:visible', false)
      },
    },
  }
</script>

<style scoped lang="scss">
  .dialog-wrapper {
    .calc-form {
    width: 100%;
    .form-item.inline {
      display: inline-block;
      margin-right: 20px;
    }
    .item-title {
      margin-top: 16px;
      margin-bottom: 0;
      font-weight: bold;
    }
    .el-input {
      width: 130px;
    }
    .el-select {
      width: 150px;
    }
  }
    .profit {
      color: #ee9900;
    }
    .fee-title {
      margin: 0;
    }
    .fee-result {
      margin-top: 12px;

      p {
        display: inline-block;
        margin: 0;
      }
    }
  }
</style>
