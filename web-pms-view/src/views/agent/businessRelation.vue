<template>
  <div class="box-container">
    <warning-block>
      用于查询商户与合伙人的绑定关系，具体商户信息请返回
      <span
        @click="goToMerchant"
        class="func-content"
      >
        商户列表
      </span>
      查询。
    </warning-block>

    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">合伙人编号：</span>
          <el-input
            v-model="searchForm.agentNo"
            disabled
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">合伙人名称：</span>
          <el-input
            v-model="searchForm.agentName"
            disabled
          ></el-input>
        </div>
      </div>
    </div>

    <div class="func-container">
      <el-radio-group
        v-model="searchForm.relationType"
        @change="search(true)">
        <el-radio-button
          v-for="item in $dict('RelationTypeEnum')"
          :key="item.code"
          :label="item.code"
        >
          {{ item.desc }}
        </el-radio-button>
      </el-radio-group>
    </div>

    <div class="content-container">
      <el-table :data="list">
        <el-table-column
          label="序号"
          type="index"
          :index="getIndex"
        ></el-table-column>
        <el-table-column
          label="商户编号"
          prop="mchNo"
        ></el-table-column>
        <el-table-column
          label="商户名称"
          prop="mchName"
        ></el-table-column>
        <el-table-column
          label="所属合伙人"
          prop="mchNo"
        >
          <template v-slot="{row}">
            {{ row.agentName }}<br>
            {{ row.agentNo }}
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          prop="createTime"
        >
          <p slot-scope="{row, column}" v-html="renderTime(row[column['property']])"></p>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        background
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </el-footer>

  </div>
</template>

<script>
  import { getBusinessRelation } from '@/api/agent'
  export default {
    data() {
      return {
        searchForm: {
          relationType: '100',
          agentNo: '',
          agentName: '',
        },
        pageSize: 10,
        pageCurrent: 1,

        list: [],
        totalRecord: 0,
      }
    },
    mounted() {
      this.searchForm.agentNo = this.$route.query.agentNo;
      this.searchForm.agentName = this.$route.query.agentName;
      this.search();
    },
    methods: {
      async search(init) {
        if (init) {
          this.pageCurrent = 1
        }
        const { data } = await getBusinessRelation({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent,
        })
        this.list = data.data;
        this.totalRecord = data.totalRecord;
      },
      goToMerchant() {
        this.$router.push("/merchant/merchantManagement/merchant")
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1
      }
    }
  }
</script>

<style>
</style>
