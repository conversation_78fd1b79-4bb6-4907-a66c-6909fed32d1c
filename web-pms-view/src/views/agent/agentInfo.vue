<template>
  <div class="info-box">
    <agent-info-comp
      :loading="loading"
      :base-info="baseInfo"
      :cooperate-info="cooperateInfo"
      :main-info="mainInfo"
      :bank-info="bankInfo"
      :record-info="recordInfo"
      :system-info="systemInfo"
      :staff-list="staffList"
      :disabled="formDisabled"
      :show-arrow="false"
      :del-quote="delQuote"
      :active-tab="processId ? 'mainInfo' : 'baseInfo'"
      @edit-click="onEditClick"
      @export-info="exportInfo"
      @get-export="getExportList"
      @sale-change="onSaleChange"
      @leader-change="onLeaderChange"
      @tab-change="onTabChange"
      ref="merchantInfo"
    >
      <template v-slot:btnMiddle>
        <el-button v-permission="'agent:inviter:edit'" @click="onInviterChange">更换邀请方</el-button>
      </template>
    </agent-info-comp>

    <saler-dialog
      ref="salerDialog"
      :staff-list="staffList"
      @change="batchSetSeller"
    />
    <inviter-dialog
      :saler-id="baseInfo.salerId"
      ref="inviterDialog"
      @change="batchSetInviter"
    ></inviter-dialog>

    <leader-change-dialog
      ref="leaderDialog"
      show-email
      :visible.sync="showLeaderChange"
      @change-leader="changeLeader"
    ></leader-change-dialog>

    <div class="form-btn-group">
      <el-button v-show="!formDisabled" type="primary" @click="onClickSave">保存</el-button>
      <el-button @click="onCancel">{{ formDisabled ? '取消' : '退出编辑'}}</el-button>
    </div>

    <flow-opinion :visible.sync="showOpinion" @change="onFlowConfirm"></flow-opinion>

    <export-select ref="exportSelect" dict="agentListExport" @confirm="submitExportForm"></export-select>
    <export-record ref="exportRecord"></export-record>
  </div>
</template>

<script>
  import FlowOpinion from '@/components/Flow/FlowOpinion'

  import SalerDialog from './Component/SalerDialog.vue'
  import InviterDialog from './Component/InviterDialog.vue'

  import { getSystemRecord } from '@/api/common';
  import { getRecordInfo } from '@/api/merchant';
  import {
    setInviter, setSeller, setPrincipal, exportInfo,
    getBaseInfo, getMainInfo, getBankInfo, getCooperateInfo,
    editBaseInfo, editBankInfo, editMainInfo, delQuote,exportAgent
  } from '@/api/agent'
  import { getSales, getStaffListByDepartment } from '@/api/system'
  import { editBusinessData } from '@/api/flow'

  import LeaderChangeDialog from '@/components/LeaderChangeDialog'
  import AgentInfoComp from '@/components/AgentInfoComp'
  import ExportSelect from '@/components/ExportSelect'
  import ExportRecord from '@/components/ExportRecord'
  // 权限对应查询map
  const permissionMap = {
    'agent:baseInfo:view': {
      api: getBaseInfo,
      result: 'baseInfo'
    },
    'agent:mainInfo:view': {
      api: getMainInfo,
      result: 'mainInfo'
    },
    'agent:bankInfo:view': {
      api: getBankInfo,
      result: 'bankInfo'
    },
    'agent:cooperation:view': {
      api: getCooperateInfo,
      result: 'cooperateInfo'
    },
  };
  // tab修改的map
  const tabMap = {
    baseInfo: {
      needFlow: false,
      api: editBaseInfo,
    },
    mainInfo: {
      needFlow: true,
      api: editMainInfo,
    },
    bankInfo: {
      needFlow: true,
      api: editBankInfo,
    },
    cooperateInfo: {
      needFlow: true,
      api: delQuote,
    }
  };
  export default {
    components: {
      FlowOpinion,
      SalerDialog,
      InviterDialog,
      ExportSelect,
      ExportRecord,
      AgentInfoComp,
      LeaderChangeDialog,
    },
    computed: {
      agentNo({$route}) {
        return $route.params.agentNo || ''
      },
      processType() {
        return this.$route.query.processType || '';
      },
      processId() {
        return this.$route.query.processId || '';
      },
      taskId() {
        return this.$route.query.taskId || '';
      },
    },
    data() {
      return {
        loading: false,
        baseInfo: {},
        mainInfo: {},
        bankInfo: {},
        cooperateInfo: [],
        recordInfo: [],
        systemInfo: {},

        staffList: [],

        formDisabled: true, // 是否禁用表单，一开始均为true，可以切换

        showLeaderChange: false,

        showOpinion: false,
      }
    },
    mounted() {
      if (this.agentNo) {
        this.getAgentDetail();
      }
      this.getSale();
    },
    methods: {
      async getSale() {
        const {data} = await getSales();
        data.forEach(item => {
          this.staffList.push(item)
        })
      },
      async getAgentDetail() {
        let promiseList = [];
        let resultList = [];
        for (let p in permissionMap) {
          if (p === 'void' || this.hadPermission(p)) {
            promiseList.push(permissionMap[p]['api']({
              agentNo: this.agentNo
            }));
            resultList.push(permissionMap[p]['result'])
          }
        }

        // 系统信息单独
        this.loading = true;
        promiseList.push(getRecordInfo({
          mchNo: this.agentNo,
        }));
        resultList.push('recordInfo');

        promiseList.push(getSystemRecord({
          mchNo: this.agentNo,
          type: 103,
        }));
        resultList.push('systemInfo');

        const res = await Promise.all(promiseList).finally(() => {
          this.loading = false;
        });
        res.forEach((item, index) => {
          this[resultList[index]] = item.data || null;
        });

        if (this.processId) {
          const agentInfo = JSON.parse(sessionStorage.getItem('agentInfo'));
          for (let p in agentInfo) {
            this[p] = agentInfo[p]
          }
        }
      },
      async batchSetSeller({sellerId, sellerName, remark}) {
        const formData = {
          agentNo: this.agentNo,
          sellerId,
          sellerName,
        };
        if (!this.processId) {
          await setSeller({
            extObj: formData,
            participant: {},
            condition: {
              referenceName: this.baseInfo && this.baseInfo.agentName,
              referenceNo: this.baseInfo && this.baseInfo.agentNo,
            },
            remark
          });
          this.$message.success('操作成功');
        } else {
          await this.editBusinessData(formData, remark)
        }
        this.getAgentDetail();
      },
      async batchSetInviter({inviterNo, inviterName, remark}) {
        const formData = {
          inviterName,
          inviterNo,
          agentNo: this.agentNo,
          agentName: this.baseInfo.agentName,
        };
        if (!this.processId) {
          await setInviter({
            extObj: formData,
            participant: {},
            condition: {
              referenceName: this.baseInfo && this.baseInfo.agentName,
              referenceNo: this.baseInfo && this.baseInfo.agentNo,
            },
            remark,
          });
          this.$message.success('操作成功');
        } else {
          await this.editBusinessData(formData, remark)
        }
        this.getAgentDetail();
      },
      exportInfo() {
        this.$refs.exportSelect.visible = true
      },
      async submitExportForm(form) {
        // 标准报表，传所有字段，自定义报表，将下标转换成数据
        await exportAgent({
          ...form,
          paramMap: {
            agentNo: this.agentNo
          }
        })
        this.$message.success('导出成功，请到导出列表进行导出');
      },
      getExportList() {
        this.$refs.exportRecord.isShow = true;
        this.$refs.exportRecord.getExportRecord('39');
      },
      onEditClick() {
        this.tempActive = this.$refs.merchantInfo.activeName
        if (this.tempActive in tabMap) {
          this.formDisabled = false;
        }
      },
      onSaleChange() {
        const {salerId = ''} = this.baseInfo;
        this.$refs.salerDialog.form = {
          salerId,
          remark: '',
        };
        this.$refs.salerDialog.visible = true;
      },
      onInviterChange() {
        const {inviterNo = '', inviterName = '',} = this.baseInfo;
        this.$refs.inviterDialog.form = {
          inviterNo,
          inviterName,
          remark: '',
        }
        this.$refs.inviterDialog.visible = true;
      },
      onLeaderChange() {
        const {contactPhone = '', contactName = '', contactEmail = '',} = this.baseInfo;
        this.$refs.leaderDialog.form = {
          contactPhone,
          contactName,
          contactEmail,
          remark: ''
        };
        this.showLeaderChange = true;
      },
      async changeLeader({remark, contactPhone, contactName, contactEmail}) {
        const formData = {
          agentNo: this.agentNo,
          contactPhone,
          contactName,
          contactEmail,
        };
        if (!this.processId) {
          await setPrincipal({
            extObj: formData,
            participant: {},
            condition: {
              referenceName: this.baseInfo && this.baseInfo.agentName,
              referenceNo: this.baseInfo && this.baseInfo.agentNo,
            },
            remark,
          });
          this.$message.success('操作成功');
        } else {
          await this.editBusinessData(formData, remark)
        }
        this.getAgentDetail();
      },
      async editBusinessData(formData, remark) {
        await editBusinessData({
          extInfo: JSON.stringify(formData),
          commonFlowId: this.processId,
          taskId: this.taskId,
          remark,
        });
        this.$message.success('更新成功');
        this.$router.push({
          path: '/waitingHandle/detailProcess',
          query: {
            processId: this.processId,
            taskId: this.taskId,
          }
        })
      },
      async onTabChange({active, old}) {
        this.formDisabled = true;
        this.nextActive = active;
        this.tempActive = old;
        this.onSave();
      },
      async changeInfo(param, {formData, remark = ''}) {
        this.loading = true;
        if (tabMap[param].needFlow) {
          // 需要审批
          if (!this.processId) {
            await this.startChangeFlow(param, formData, remark);
          } else {
            return this.editBusinessData(formData, remark);
          }
        } else {
          // 不需要审批
          await this.editData(formData)
        }
        this.$message.success('操作成功');
        this.loading = false;
        this.formDisabled = true;
        if (this.nextActive) {
          this.$refs.merchantInfo.changeTab(this.nextActive)
        }
        this.getAgentDetail();
      },
      async editData(formData) {
        this.loading = true;
        const activeTab = this.tempActive || this.$refs.merchantInfo.activeName;
        let api = tabMap[activeTab]['api'];
        await api(formData).finally(() => this.loading = false);
        this.tempActive = '';
      },
      async startChangeFlow(param, formData, remark) {
        await tabMap[param]['api']({
          extObj: formData,
          participant: {},
          condition: {
            referenceName: this.baseInfo && this.baseInfo.agentName,
            referenceNo: this.baseInfo && this.baseInfo.agentNo,
          },
          remark,
        }).finally(() => {
          this.loading = false;
        });
        this.tempActive = '';
      },
      async onClickSave() {
        this.tempActive = this.$refs.merchantInfo.activeName;
        let form = this.$refs.merchantInfo.$refs[this.tempActive];
        const valid = await form.$refs['form'].validate().catch(e => e);
        if (!valid) {
          return this.$message.error('请补充必填信息')
        }
        this.onSave()
      },
      async onSave() {
        if (tabMap[this.tempActive].needFlow) { // 需要发起流程
          return this.showOpinion = true;
        } else {
          // 不需要发起流程
          let form = this.$refs.merchantInfo.$refs[this.tempActive];
          let formData = form.getFormData ? form.getFormData() : form['form'];
          this.changeInfo(this.tempActive, {formData});
        }
      },
      onCancel() {
        if (!this.formDisabled) {
          this.formDisabled = true;
        } else {
          this.$router.back();
        }
      },
      async onFlowConfirm(remark) {
        this.confirmVisible = false;
        const form = this.$refs.merchantInfo.$refs[this.tempActive];
        let formData = form.getFormData ? form.getFormData() : form['form'];
        await this.changeInfo(this.tempActive, {formData, remark});
      },
      delQuote() {
        this.tempActive = 'cooperateInfo';
        this.showOpinion = true;
      }
    }
  }
</script>

<style lang="scss" scoped>
  .info-box {
    padding-bottom: 100px;

    .flex-wrapper {
      margin: 0 32px;
      .el-tag {
        max-width: 90%;
        margin-right: 5px;
        margin-bottom: 5px;
        overflow-x: hidden;
        text-overflow: ellipsis;
      }
    }

    .base-info {
      background: #fff;
      padding: 16px;
      border-radius: 8px;
    }
    .base-name {
      font-size: 16px;
    }
    .info-arrow {
      display: inline-block;
      vertical-align: middle;
      padding: 3px 4px;
      margin-left: 8px;
      background: #eee;
      cursor: pointer;
    }

    .info-container {
      margin-top: -15px;
    }
  }
</style>
