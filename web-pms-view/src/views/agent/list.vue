<template>
  <div class="box-container">
    <el-button
      class="create-btn"
      type="primary"
      @click="create"
      v-permission="'agent:create'"
    >
      创建合伙人
    </el-button>
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">合伙人编号：</span>
          <el-input v-model="searchForm.agentNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">合伙人名称：</span>
          <el-input
            v-model="searchForm.agentNameLike"
            placeholder="模糊查询"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">类型：</span>
          <el-select
            clearable
            v-model="searchForm.agentType"
          >
            <el-option
              v-for="item in $dict('AgentTypeEnum')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">邀请方编号：</span>
          <el-input v-model="searchForm.inviterNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">邀请方名称：</span>
          <el-input
            v-model="searchForm.inviterNameLike"
            placeholder="模糊查询"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">所属销售：</span>
          <el-select
            v-model="searchForm.salerId"
            clearable
          >
            <el-option
              v-for="item in staffList"
              :key="item.id"
              :label="item.realName"
              :value="item.id"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">状态：</span>
          <el-select
            v-model="searchForm.agentStatus"
            clearable
          >
            <el-option
              v-for="item in $dict('AgentStatusEnum')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            type="datetimerange"
            :start-time.sync="searchForm.createBeginTime"
            :end-time.sync="searchForm.createEndTime"
            ref="datepicker"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询
        </el-button>
        <el-button @click="exportData">导出</el-button>
        <el-button
          type="text"
          @click="getExportList">查看已导出列表
        </el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件
        </el-button>
      </div>
    </div>

    <div class="func-container">
      <el-checkbox v-model="checkAll">单页全选</el-checkbox>
      |
      <span>已选 {{ selection.length }} 条</span> |
      <span>批量操作</span>
      <el-button
        size="small"
        :disabled="selection.length == 0"
        @click="multiSaler"
      >设置销售
      </el-button>
      <el-button
        size="small"
        :disabled="selection.length == 0"
        @click="multiInviter"
      >设置邀请方
      </el-button>
    </div>

    <div class="content-container">
      <el-table
        ref="table"
        :data="list"
        @selection-change="handleSelection"
      >
        <el-table-column
          type="selection"
          fixed="left"
          :selectable="checkSelectAble"
        ></el-table-column>

        <el-table-column
          label="合伙人编号"
          prop="agentNo"
          width="120"
        >
          <template v-slot="{row}">
            <div
              class="func-content"
              @click="openAgentPop(row)">
              {{ row.agentNo }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="合伙人名称"
          prop="agentName"
          width="120"
        ></el-table-column>
        <el-table-column
          label="类型"
          prop="agentType"
        >
          <template v-slot="{row}">
            {{ $dictCode('AgentTypeEnum', row.agentType).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          prop="agentStatus"
        >
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.agentStatus)">
              {{ $dictCode('AgentStatusEnum', row.agentStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="inviterNo"
          width="150"
        >
          <template slot="header">
            <span>邀请方编号</span><br>
            <span>邀请方名称</span>
          </template>
          <template v-slot="{row}">
            {{ row.inviterNo }} <br>
            {{ row.inviterName }}
          </template>
        </el-table-column>
        <el-table-column
          label="Ta邀请的"
          prop="invitationNum"
        >
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="inviterSearch(row)"
            >
              {{ row.invitationNum || 0 }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="商户关系"
          prop="merNum"
        >
          <template v-slot="{row}">
            <span
              class="func-content"
              @click="goBusiness(row)"
            >
              {{ row.merNum || 0 }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          label="所属销售"
          prop="salerName"
        ></el-table-column>

        <el-table-column
          label="上次推广时间"
          prop="latestPromotionTime"
          width="180">
          <template v-slot="{row, column}">
            <div class="flex-box">
              <div v-html="renderTime(row[column['property']])"></div>
              <el-tooltip
                :content="`距离上次推广商户已超过6个月(${row.promotionDiff}天)`"
                v-if="row.promotionDiff >= 180">
                <i
                  style="margin-left: 8px; align-items: center; color: #FAAC14;"
                  class="el-icon-warning flex-box"
                ></i>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          label="创建时间"
          prop="createTime"
          width="120"
        >
          <p
            slot-scope="{row, column}"
            v-html="renderTime(row[column['property']])"></p>
        </el-table-column>

        <el-table-column
          label="操作"
          width="200"
          fixed="right"
        >
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="goDetail(row)"
            >详情
            </el-button>
            <div v-if="row.agentStatus !== 102">
              <el-button
                type="text"
                @click="goAgentProduct(row)"
              >奖励设置
              </el-button>
              <el-button
                v-if="row.agentStatus !== 103"
                type="text"
                @click="changeStatus(row, (row.agentStatus == 100 || row.agentStatus == 102) ? 101: 100)"
              >{{ (row.agentStatus == 100 || row.agentStatus == 102) ? '冻结' : '激活' }}
              </el-button>
              <el-button
                v-if="row.agentStatus !== 103"
                type="text"
                @click="changeStatus(row, '103')"
              >清退
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        key="agent"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>
    <ExportRecord ref="exportRecord"></ExportRecord>
    <saler-dialog
      ref="salerDialog"
      :staff-list="staffList"
      @change="batchSetSeller"
    />
    <inviter-dialog
      ref="inviterDialog"
      @change="batchSetInviter"
    ></inviter-dialog>

    <status-dialog
      ref="statusDialog"
      :status="changingStatus"
      :agent="changingAgent"
      @confirm="confirmStatus"
    />

    <export-select
      ref="exportSelect"
      dict="agentListExport"
      @confirm="submitExportForm"
    ></export-select>
  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import { getSales } from '@/api/system'
import { getAgentList, batchSetSeller, batchSetInviter, changeStatus, exportAgent } from '@/api/agent'
import SalerDialog from './Component/SalerDialog.vue'
import InviterDialog from './Component/InviterDialog.vue'
import StatusDialog from './Component/StatusDialog.vue'
import ExportSelect from '@/components/ExportSelect'

export default {
  name: 'AgentManage',
  components: {
    SalerDialog,
    InviterDialog,
    StatusDialog,
    ExportRecord,
    ExportSelect
  },
  data() {
    return {
      searchForm: {
        agentNo: '',
        agentNameLike: '',
        agentType: '',
        inviterNo: '',
        inviterNameLike: '',
        salerId: '',
        agentStatus: '',
        createBeginTime: '',
        createEndTime: '',
      },

      pageSize: 10,
      pageCurrent: 1,

      staffList: [],

      list: [],
      totalRecord: 0,
      selection: [],

      checkAll: false,

      changingStatus: '',
      changingAgent: {},

    }
  },
  watch: {
    checkAll(val) {
      if (val) {
        this.list.forEach(row => {
          if (row.agentStatus == 101 || row.agentStatus == 100 || row.agentStatus == 102) {
            this.$refs.table.toggleRowSelection(row, true)
          }
        })
      } else {
        this.$refs.table.clearSelection();
      }
    }
  },
  mounted() {
    getSales().then(res => {
      this.staffList = res.data
    });
    this.search()
  },
  methods: {
    create() {
      this.$router.push('/agent/createGuide')
    },
    resetForm() {
      this.searchForm = {
        agentNo: '',
        agentNameLike: '',
        agentType: '',
        inviterNo: '',
        inviterNameLike: '',
        salerId: '',
        agentStatus: '',
        createBeginTime: '',
        createEndTime: '',
      };
      this.$refs.datepicker.clearTime();
    },
    async search(init) {
      if (init) {
        this.pageCurrent = 1;
      }
      const { data } = await getAgentList({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      })
      this.list = data.data;
      this.totalRecord = data.totalRecord;
      this.checkAll = false;
    },
    getTagType(status) {
      switch (Number(status)) {
        case 100:
          return 'success';
        case 101:
          return 'warning';
        case 103:
          return 'danger';
        default:
          return ''
      }
    },
    handleSelection(val) {
      this.selection = val;
    },
    inviterSearch(row) {
      this.resetForm();
      this.searchForm.inviterNo = row.agentNo;
      this.search(true);
    },
    multiSaler() {
      this.$refs.salerDialog.visible = true;
    },
    multiInviter() {
      this.$refs.inviterDialog.visible = true;
    },
    async batchSetSeller({ sellerId, sellerName, remark }) {
      const agentNoList = this.selection.map(item => {
        return {
          agentNo: item.agentNo,
          agentName: item.agentName,
        }
      });
      await batchSetSeller({
        extObj: {
          agentNoList,
          setNo: sellerId,
          setName: sellerName
        },
        condition: {
          referenceNo: agentNoList.map(i => i.agentNo).join(', '),
          referenceName: agentNoList.map(i => i.agentName).join(', '),
        },
        remark,
      });
      this.$message.success('操作成功');
    },
    async batchSetInviter({ inviterNo, inviterName, remark }) {
      const agentNoList = this.selection.map(item => {
        return {
          agentNo: item.agentNo,
          agentName: item.agentName,
        }
      });
      await batchSetInviter({
        extObj: {
          agentNoList,
          setNo: inviterNo,
          setName: inviterName
        },
        condition: {
          referenceNo: agentNoList.map(i => i.agentNo).join(', '),
          referenceName: agentNoList.map(i => i.agentName).join(', '),
        },
        remark,
      });
      this.$message.success('操作成功');
    },
    goDetail(row) {
      this.$router.push({
        path: `/agent/agentInfo/${ row.agentNo }`,
      })
    },
    changeStatus(agent, status) {
      this.changingStatus = status;
      this.changingAgent = agent;
      this.$refs.statusDialog.visible = true;
    },
    async confirmStatus() {
      const { data } = await changeStatus({
        agentNo: this.changingAgent.agentNo,
        status: this.changingStatus,
      })
      this.$message.success(data);
      this.search();
    },
    goBusiness(row) {
      this.$router.push({
        path: '/agent/businessRelation',
        query: {
          agentNo: row.agentNo,
          agentName: row.agentName,
        }
      })
    },
    checkSelectAble(row) {
      return row.agentStatus == 100 || row.agentStatus == 101 || row.agentStatus == 102;
    },
    goAgentProduct(row) {
      this.$router.push({
        path: '/business/agentProductManagement',
        query: {
          agentNo: row.agentNo,
          agentName: row.agentName,
        }
      })
    },
    openAgentPop(row) {
      this.$store.dispatch('openAgentPop', { agentNo: row.agentNo })
    },
    // async exportData() {
    //   const { data } = await exportAgent(this.searchForm);
    //   this.$message.success(data);
    // },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(39);
    },
    exportData() {
      this.$refs.exportSelect.visible = true;
    },
    async submitExportForm(searchForm) {
      let paramMap = {
        ...this.searchForm
      };

      for (let p in paramMap) {
        if (!paramMap[p]) {
          delete paramMap[p]
        }
      }

      // 标准报表，传所有字段，自定义报表，将下标转换成数据
      await exportAgent({
        fieldListDataName: 'agentListExport',
        fieldInfoList: searchForm.fieldInfoList,
        paramMap,
      });
      this.$message.success('导出成功，请到导出列表进行导出');
    }
  }
}
</script>

<style>
</style>
