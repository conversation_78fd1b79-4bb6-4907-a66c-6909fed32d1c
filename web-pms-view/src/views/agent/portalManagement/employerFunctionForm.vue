<template>
  <el-dialog
    :visible.sync="show"
    :close-on-click-modal="false"
    :before-close="closeForm"
    :title="(actionType==='ADD'&&'新增功能') || (actionType==='EDIT'&&'编辑功能')||''"
    append-to-body
    width="600px"
  >
    <el-form
      ref="form"
      :inline="true"
      :model="employerFunction"
      :rules="rules"
      size="small"
      label-width="100px"
    >
      <el-form-item label="父功能" v-if="actionType==='ADD'">
        <el-input v-model="employerFunction.parentName" :disabled="true" style="width: 450px;" />
      </el-form-item>
      <el-form-item label="功能名称" prop="name">
        <el-input v-model="employerFunction.name" placeholder="名称" style="width: 450px;" />
      </el-form-item>
      <el-form-item label="功能编号" prop="number">
        <el-input v-model="employerFunction.number" placeholder="编号" style="width: 450px;" />
      </el-form-item>
      <el-form-item label="权限标识" prop="permissionFlag">
        <el-input
          v-model="employerFunction.permissionFlag"
          placeholder="权限标识"
          style="width: 450px;"
          :disabled="actionType!=='ADD'"
        />
      </el-form-item>
      <el-form-item label="功能类型" prop="type">
        <el-radio-group
          v-model="employerFunction.type"
          :disabled="actionType!=='ADD'"
          @change="value=>{value===2&&(employerFunction.url=undefined)}"
        >
          <el-radio v-model="employerFunction.type" :label="1">菜单项</el-radio>
          <el-radio v-model="employerFunction.type" :label="2">操作项</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="URL地址" prop="url">
        <el-input
          v-model="employerFunction.url"
          placeholder="URL"
          style="width: 450px;"
          :disabled="employerFunction.type===2"
        />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="closeForm">取消</el-button>
      <el-button :loading="loading" type="primary" @click="doSubmit">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createFunction, editFunction } from "@/api/agent";

export default {
  name: "employerFunctionForm",
  data() {
    return {
      loading: false,
      show: false,
      actionType: undefined,
      employerFunction: {},
      rules: {
        name: [{ required: true, message: "请输入功能名称", trigger: "blur" }],
        number: [
          {
            required: true,
            message: "请输入功能编号,必须为纯数字",
            trigger: "blur",
            pattern: /^[0-9]*$/,
          },
        ],
        permissionFlag: [
          { required: true, message: "请输入权限标识", trigger: "blur" },
        ],
        type: [{ required: true, message: "请选择功能类型" }],
        url: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (this.employerFunction.type === 1 && !value) {
                callback(new Error("请输入URL地址"));
              } else {
                callback();
              }
            },
          },
        ],
      },
    };
  },
  methods: {
    doSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.actionType === "ADD" && this.doAdd();
          this.actionType === "EDIT" && this.doEdit();
        }
      });
    },
    doAdd() {
      createFunction(this.employerFunction).then(({ data }) => {
        this.$message.success(data);
        this.closeForm();
        this.$emit("success");
      })
      .finally(() => {
        this.loading = false;
      });
    },
    doEdit() {
      editFunction(this.employerFunction).then(({ data }) => {
        this.$message.success(data);
        this.closeForm();
        this.$emit("success");
      })
      .finally(() => {
        this.loading = false;
      });
    },
    closeForm() {
      this.show = false;
      this.loading = false;
      this.employerFunction = {};
      this.$refs.form.resetFields();
    },
  },
};
</script>
