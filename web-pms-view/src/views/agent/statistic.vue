<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">合伙人编号：</span>
          <el-input v-model="searchForm.agentNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">合伙人名称：</span>
          <el-input v-model="searchForm.agentNameLike" placeholder="模糊查询"></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">查询时段：</span>
          <el-select
            v-model="searchForm.queryTimeType"
            clearable
          >
            <el-option
              v-for="item in $dict('QueryTimeTypeEnum')"
              :key="item.code"
              :value="Number(item.code)"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">排序维度：</span>
          <el-select
            v-model="searchForm.sortColumns"
            clearable
          >
            <el-option
              v-for="item in $dict('AgentFeeSumSortColumns')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询</el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件</el-button>
      </div>
    </div>

    <div class="content-container">
      <el-table
        ref="table"
        :data="list"
      >
        <el-table-column label="序号" type="index" :index="getIndex"></el-table-column>
        <el-table-column
          label="合伙人编号"
          prop="agentNo"
          width="120"
        ></el-table-column>
        <el-table-column
          label="合伙人名称"
          prop="agentName"
          width="120"
        ></el-table-column>
        <el-table-column
          label="类型"
          prop="agentType"
        >
          <template v-slot="{row}">
            {{ $dictCode('AgentTypeEnum', row.agentType).desc }}
          </template>
        </el-table-column>

        <el-table-column width="150">
          <p slot="header">
            累计成功实发金额
            <el-tooltip content="指该合伙人（直接关联的）下属商户的成功实发金额汇总" placement="top">
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </p>
          <template v-slot="{row}">
            <p class="text-right">
              {{ row.totalNetAmount | moneyFormat }}
            </p>
          </template>
        </el-table-column>
        <el-table-column>
          <p slot="header">
            收益（元）
            <el-tooltip content="指该合伙人的交易分润和邀请奖励汇总" placement="top">
              <i class="el-icon-warning"></i>
            </el-tooltip>
          </p>
          <template v-slot="{row}">
            <p class="text-right">
              {{ row.totalProfit | moneyFormat }}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="交易分润（元）">
          <template v-slot="{row}">
            <p class="text-right">
              {{ row.totalTradeProfit | moneyFormat }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="邀请奖励（元）">
          <template v-slot="{row}">
            <p class="text-right">
              {{ row.totalInviteReward | moneyFormat }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="所属销售">
          <template v-slot="{row}">
            {{ row.salerName }}
          </template>
        </el-table-column>
      </el-table>

      <el-footer class="pagination-container">
        <el-pagination
          ref="pagination"
          key="agent"
          :total="totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10, 50]"
          :page-size.sync="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="search(true)"
          @current-change="search()"
        >
        </el-pagination>
      </el-footer>
    </div>
  </div>
</template>

<script>
  import { getAgentFeeSum } from '@/api/agent';
  export default {
    name: 'AgentStatistic',
    data() {
      return {
        searchForm: {
          agentNo: '',
          agentNameLike: '',
          queryTimeType: 1,
          sortColumns: 'totalNetAmount',
        },
        pageSize: 10,
        pageCurrent: 1,

        list: [],
        totalRecord: 0,
      }
    },
    mounted() {
      this.search();
    },
    methods: {
      async search(init) {
        if (init) {
          this.pageCurrent = 1;
        }
        const { data } = await getAgentFeeSum({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent,
        });
        this.list = data.data;
        this.totalRecord = data.totalRecord;
      },
      resetForm() {
        this.searchForm = {
          agentNo: '',
          agentNameLike: '',
          queryTimeType: 1,
          sortColumns: 'totalNetAmount',
        }
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1
      }
    }
  }
</script>

<style>
</style>
