<template>
  <!--特殊计费窗口-->
  <el-dialog
    :visible.sync="visible"
    :close-on-click-modal="false"
    title="特殊计费"
    width="800px"
    :before-close="close"
  >
    <el-form :model="form" label-width="180px" style="width: auto;" :rules="editRules" ref="form">
      <el-form-item label="所属产品：" prop="productNo">
        <el-select clearable v-model="form.productNo" @change="productChange">
          <el-option v-for="item in productList" :key="item.productNo" :value="item.productNo" :label="item.productName"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="默认公式类型：" prop="formulaType">
        <el-select clearable v-model="form.formulaType">
          <el-option v-for="item in $dict('FormulaEnum')" :key="item.code" :value="Number(item.code)" :label="item.desc"></el-option>
        </el-select>
        <p class="color-gray" v-show="$dictCode('FormulaEnum', form.formulaType).desc === '比例'">
          即每笔订单按“实发流水 * 服务费比例”的公式计费
        </p>
        <p class="color-gray" v-show="$dictCode('FormulaEnum', form.formulaType).desc === '固定金额'">
          即每笔订单按“固定服务费金额”的公式计费，灵活用工场景下请谨慎适用该公式类型
        </p>
        <p class="color-gray" v-show="$dictCode('FormulaEnum', form.formulaType).desc === '固定金额+比例'">
          即每笔订单按“固定服务费金额 + 实发流水 * 服务费比例”的公式计费，通常适用于商户同时承担不同类目手续费的场景
        </p>
      </el-form-item>
      <el-form-item label="固定金额：" prop="fixedFee" v-if="form.formulaType != '0'">
        <el-input :value="form.fixedFee" @input="handleRateInput(form, 'fixedFee', $event)">
          <template slot="append">元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="比例：" prop="rate" v-if="form.formulaType != '1'">
        <el-input :value="form.rate" @input="handleRateInput(form, 'rate', $event, 0, 100)">
          <template slot="append">%</template>
        </el-input>
      </el-form-item>

      <el-form-item class="form-item-main" label="特殊计费-规则参数：" prop="specialRule">
        <el-button @click="addRuleList">添加</el-button>
        <el-table :data="form.ruleParam">
          <el-table-column width="50">
            <template v-slot="{$index}">
              <span>{{ $index > 0 ? '且' : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column>
            <template v-slot="{row, $index}">
              <el-select clearable v-model="row.specialRuleType" @change="selectRuleType($index)" :disabled="$index == 0">
                <el-option v-for="item in $dict('ProductFeeSpecialRuleTypeEnum')" :key="item.code" :value="Number(item.code)" :label="item.desc"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column>
            <template v-slot="{row, $index}">
              <el-select v-if="row.specialRuleType" clearable v-model="row.compareType" :disabled="$index == 0 || row.specialRuleType == 1">
                <el-option :value="1" label="等于"></el-option>
                <el-option :value="2" label="小于"></el-option>
                <el-option :value="3" label="大于"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column>
            <template v-slot="{row, $index}">
              <el-select
                clearable
                v-if="row.specialRuleType == '1'"
                v-model="row.value"
                @change="vendorChange($event, row)"
                :disabled="$index == 0">
                <el-option
                  v-for="item in vendorList"
                  :key="item.vendorNo"
                  :value="item.vendorNo"
                  :label="item.vendorName"></el-option>
              </el-select>
              <el-input v-if="row.specialRuleType != '1'" v-model="row.value"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template v-slot="{$index}">
              <el-button v-if="$index > 0" type="text" @click="deleteRule($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <el-form-item label="描述">
        <el-input type="textarea" v-model="form.description" maxlength="50" rows="5"></el-input>
      </el-form-item>
    </el-form>

    <template slot="footer">
      <el-button type="primary" @click="confirm">保存</el-button>
      <el-button @click="close">取消</el-button>
    </template>
  </el-dialog>
</template>

<script>
  export default {
    props: {
      info: {
        type: Object,
        default: () => ({})
      },
      visible: {
        type: Boolean,
        default: false,
      },
      productList: {
        type: Array,
        default: () => [],
      },
      vendorList: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        editRules: {
          productNo: [{required: true, message: '请选择所属产品', trigger: 'change'}],
          formulaType: [{required: true, message: '请选择默认公式类型', trigger: 'change'}],
          fixedFee: [{required: true, message: '请输入固定金额'}],
          rate: [{required: true, message: '请输入费率'}],
        },
        form: {
          mainstayMchNo: '',
          mainstayMchName: '',
          productNo: "",
          productName: '',
          ruleType: "",
          ruleParam: [],
          formulaType: '',
          fixedFee: '',
          rate: '',
          description: '',
        }
      }
    },
    watch: {
      visible(val) {
        if (val) {
          for (let p in this.form) {
            if (this.info.hasOwnProperty(p)) {
              this.form[p] = this.info[p];
            }
          }
        } else {
          this.$refs.form.resetFields();
          this.form.ruleParam = []
        }
      }
    },
    methods: {
      productChange(val) {
        for (let i = 0; i < this.productList.length; i++) {
          const item = this.productList[i];
          if (item.productNo == val) {
            this.form.productName = item.productName;
            break;
          }
        }
      },
      selectRuleType(index) {
        this.form.ruleParam[index].compareType = 1;
      },
      addRuleList() {
        this.form.ruleParam.push({
          specialRuleType: '',
          compareType: '',
          value: ''
        })
      },
      deleteRule(index) {
        this.form.ruleParam.splice(index, 1)
      },
      close() {
        this.$emit('update:visible', false);
      },
      async confirm() {
        const valid = await this.$refs.form.validate().catch(_ => false);
        if (!valid) return;
        this.$emit('confirm', { ...this.form });
        this.close()
      },
      vendorChange(val, row) {
        for (let i = 0; i < this.vendorList.length; i++) {
          if (val == this.vendorList[i].vendorNo) {
            row['objName'] = this.vendorList[i].vendorName;
            break;
          }
        }
      },
    }
  }
</script>

<style scoped lang="scss">

</style>
