<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户编号：</span>
            <el-input v-model="form.mchNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">操作员姓名：</span>
            <el-input v-model="form.mchNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">操作员账号：</span>
            <el-input v-model="form.mchNameLike"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">创建时间：</span>
            <date-picker ref="datePicker" v-model="form.timeRange" @change="getTimeRange" ></date-picker>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">状态：</span>
            <el-select clearable v-model="form.mchNameLike"></el-select>
          </div>
          <div class="flex-item"></div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="getAccountList(true)">查询</el-button>
            <el-button type="text" @click="resetForm">清空筛选条件</el-button>
          </div>

        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table class="content-main" :data="accountObj.data">
        <el-table-column label="账号" prop="createTime">

        </el-table-column>
        <el-table-column label="操作员姓名" prop="activeTime">

        </el-table-column>
        <el-table-column label="商户编号" prop="mchNo">

        </el-table-column>
        <el-table-column label="用户类型" prop="mchName">

        </el-table-column>
        <el-table-column label="状态" prop="mchStatus">

        </el-table-column>
        <el-table-column label="添加时间" prop="data">

        </el-table-column>
        <el-table-column label="操作" prop="data">

        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="accountObj"
          ref="pagination"
          :total="accountObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>

    </div>
  </div>
</template>

<script>
  import { getAccountList } from '../../../api/merchant'

  export default {
    name: 'account',
    data() {
      return {
        form: {
          mchNo: '',
          mchNameLike: '',
          accountType: '',
          timeRange: []
        },
        activeTab: 'first',
        pageCurrent: 1,
        pageSize: 10,
        accountObj: {
          data: [{}]
        }
      }
    },
    mounted() {
      this.getAccountList();
    },
    methods: {
      getAccountList(initFlag) {
        if (initFlag === true) {
          this.pageCurrent = 1;
        }
        let { mchNo, mchNameLike, accountType } = { ...this.form }
        getAccountList({
          mchNo: this.form.mchNo,
          mchNameLike: this.form.mchNameLike,
          accountType: this.form.accountType,
          createTimeBegin: this.form.timeRange[0],
          createTimeEnd: this.form.timeRange[1],
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        }).then(response => {
          this.accountObj = response.data;
        })
      },
      getTimeRange(val) {
        this.form.timeRange = val;
      },
      resetForm() {
        this.form = {
          mchNo: '',
          mchNameLike: '',
          accountType: '',
          timeRange: []
        }
        this.$refs.datePicker.resetTime();
      },
      changTab() {
        this.pageSize = 10;
        this.pageCurrent = 1;
        this.getAccountList();
      },
      handleCurrentChange(val) {
        this.pageCurrent = val;
        this.getAccountList();
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getAccountList(true);
      },

    }
  }
</script>

<style scoped>
  .create-btn {
    margin-bottom: 20px;
  }
</style>
