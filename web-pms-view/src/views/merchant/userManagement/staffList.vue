<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">操作员帐号：</span>
            <el-input v-model="form.phone"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">操作员姓名：</span>
            <el-input v-model="form.nameLike"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户编号：</span>
            <el-input v-model="form.mchNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">商户名称：</span>
            <el-input v-model="form.mchNameLike"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              ref="datePicker"
              v-model="form.timeRange"
              @change="getTimeRange"

            ></date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="listStaff(true)">查询</el-button>
            <el-button type="text" @click="resetForm">清空筛选条件</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table class="content-main" :data="staffObj.data">
        <el-table-column label="帐号" prop="phone"></el-table-column>
        <el-table-column label="操作员姓名" prop="name"></el-table-column>
        <el-table-column
          label="用户类型"
          prop="type"
          :formatter="row=> $dictCode('PortalStaffTypeEnum',row.type).desc"
        ></el-table-column>
        <el-table-column label="商户编号" prop="mchNo"></el-table-column>
        <el-table-column label="商户名称" prop="mchName"></el-table-column>
        <el-table-column label="添加时间" prop="createTime">
          <p slot-scope="{row}" v-html="renderTime(row.createTime)"></p>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container">
      <el-pagination
        v-if="staffObj"
        ref="pagination"
        :total="staffObj.totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10,50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
  </div>

  </div>
</template>

<script>
import { listEmployerStaffPage } from "../../../api/merchant";

export default {
  name: "EmpStaffList",
  data() {
    return {
      form: {
        phone: "",
        nameLike: "",
        mchNo: "",
        mchNameLike: "",
        timeRange: [],
      },
      pageCurrent: 1,
      pageSize: 10,
      staffObj: {
        data: [{}],
      },
    };
  },
  mounted() {
    this.listStaff();
  },
  methods: {
    listStaff(initFlag) {
      if (this.form.timeRange == null) {
        this.form.timeRange = [];
      }
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      listEmployerStaffPage({
        phone: this.form.phone,
        nameLike: this.form.nameLike,
        mchNo: this.form.mchNo,
        mchNameLike: this.form.mchNameLike,
        createTimeBegin:
          this.form.timeRange.length >= 1 ? this.form.timeRange[0] : null,
        createTimeEnd:
          this.form.timeRange.length >= 2 ? this.form.timeRange[1] : null,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      }).then((response) => {
        this.staffObj = response.data;
      });
    },
    getTimeRange(val) {
      this.form.timeRange = val;
    },
    resetForm() {
      this.form = {
        phone: "",
        nameLike: "",
        mchNo: "",
        mchNameLike: "",
        timeRange: [],
      };
      this.$refs.datePicker.clearTime();
    },
    changTab() {
      this.pageSize = 10;
      this.pageCurrent = 1;
      this.listStaff();
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.listStaff();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.listStaff(true);
    },
  },
};
</script>

<style scoped>
</style>
