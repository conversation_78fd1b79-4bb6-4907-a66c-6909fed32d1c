<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">操作员姓名：</span>
            <el-input v-model="form.nameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">操作员帐号：</span>
            <el-input v-model="form.phone"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">状态：</span>
            <el-select clearable v-model="form.status">
              <el-option
                v-for="{code,desc} in $dict('PortalOperatorStatusEnum')"
                :key="parseInt(code)"
                :label="desc"
                :value="parseInt(code)"
              />
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              ref="datePicker"
              v-model="createTimeRange"
              @change="getTimeRange"
            ></date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="listOperator(true)">查询</el-button>
            <el-button type="text" @click="resetForm">清空筛选条件</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table class="content-main" :data="operatorObj.data">
        <el-table-column label="帐号" prop="phone"></el-table-column>
        <el-table-column label="操作员姓名" prop="name"></el-table-column>
        <el-table-column
          label="状态"
          prop="status"
          :formatter="row=> $dictCode('PortalOperatorStatusEnum',row.status).desc"
        ></el-table-column>
        <el-table-column label="创建时间" prop="createTime">
          <p slot-scope="{row}" v-html="renderTime(row.createTime)"></p>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="operatorObj"
          ref="pagination"
          :total="operatorObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listEmployerOperatorPage } from "../../../api/merchant";

export default {
  name: "EmpOperatorList",
  data() {
    return {
      form: {
        nameLike: "",
        phone: "",
        status: null,
        createTimeBegin: null,
        createTimeEnd: null,
      },
      pageCurrent: 1,
      pageSize: 10,
      operatorObj: {
        data: [{}],
      },
      createTimeRange: []
    };
  },
  mounted() {
    this.listOperator();
  },
  methods: {
    listOperator(initFlag) {
      if (this.timeRange == null) {
        this.timeRange = [];
      }
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      listEmployerOperatorPage({
        ...this.form,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      }).then((response) => {
        this.operatorObj = response.data;
      });
    },
    getTimeRange(val) {
      if (val) {
        this.form.createTimeBegin = val[0] || ''
        this.form.createTimeEnd = val[1] || ''
      } else {
        this.form.createTimeBegin = ''
        this.form.createTimeEnd = ''
      }
    },
    resetForm() {
      this.form = {
        nameLike: "",
        phone: "",
        status: null,
        createTimeBegin: null,
        createTimeEnd: null,
      };
      this.$refs.datePicker.clearTime();
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.listOperator();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.listOperator(true);
    },
  },
};
</script>

<style scoped>
.create-btn {
  margin-bottom: 20px;
}
</style>
