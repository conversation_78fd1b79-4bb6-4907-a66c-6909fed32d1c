<template>
  <div class="bankinfo-container">
    <div class="box-container">
      <div class="search-container">
        <div class="flex-container">
          <div class="flex-wrapper search-wrapper">
            <div class="flex-item">
              <span class="flex-item__label">合伙人编号：</span>
              <el-input v-model="form.agentNo"></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">合伙人名称：</span>
              <el-input v-model="form.agentName" placeholder="模糊查询"></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">联系手机号：</span>
              <el-input v-model="form.contactMobile"></el-input>
            </div>
          </div>
          <div class="search-wrapper">
            <div class="search-btn-group">
              <el-button type="primary" @click="search(true)">查询</el-button>
              <el-button type="text" @click="clearCondition">清空筛选条件</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="content-container">
        <el-table :data="recommendList">
          <el-table-column label="创建时间" prop="createTime"></el-table-column>
          <el-table-column label="联系人姓名" prop="contactName"></el-table-column>
          <el-table-column label="联系人手机号" prop="contactMobile"></el-table-column>
          <el-table-column label="联系人邮箱" prop="contact_email"></el-table-column>
          <el-table-column label="公司名称" prop="companyName"></el-table-column>
          <el-table-column label="联系人职位" prop="contactPosition"></el-table-column>
          <el-table-column label="合伙人编号" prop="agentNo"></el-table-column>
          <el-table-column label="合伙人名称" prop="agentName"></el-table-column>
          <el-table-column label="状态" prop="status">
            <template v-slot="{row}">
              <el-tag :type="getTagType(row)">
                {{ $dictCode('SalesLeadStatusEnums', row.status).desc }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="data">
            <template slot-scope="scope">
              <el-button type="text" @click="showEditPop(scope.row)">变更状态</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-container">
        <el-pagination

          ref="pagination"
          :total="totalNum"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>

    <!--编辑内容-->
    <el-dialog
      :visible.sync="isShowEdit"
      :close-on-click-modal="false"
      :before-close="closeForm"
      title="变更状态"
      append-to-body
      width="350px"
    >
      <el-form
        ref="form"
        :inline="true"
        :model="editStatusForm"
        :rules="rules"
        size="small"
        label-width="100px"
      >
        <el-form-item label="状态" prop="status">
          <el-select v-model="editStatusForm.status" style="width: 150px;">
            <el-option
              v-for="(item, index) in $dict('SalesLeadStatusEnums')"
              :key="index"
              :label="item.desc"
              :value="Number(item.code)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="closeForm">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submitStatusInfo">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  ListRecommendCustoms,
  updateRecommendCustomsStatus
} from "../../../api/merchant";


export default {
  data() {
    return {

      isShowEdit: false,
      operate: "",

      // 分页搜索
      form: {
        agentNo: "",
        agentName: "",
        contactMobile: "",
      },
      pageCurrent: 1,
      pageSize: 10,
      recommendList: [],
      totalNum: 0,
      timeRange: [],

      editStatusForm: {
        id: null,
        status: null
      },
      loading: false,
      rules: {
        status: [
          { required: true, trigger: 'blur', message: '请选择状态'},
        ]
      },

    };
  },
  mounted() {
    this.search();
  },
  methods: {

    search(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      ListRecommendCustoms({
        pageCurrent: this.pageCurrent,
        pageSize: this.pageSize,
        ...this.form,
      }).then((response) => {
        this.recommendList = response.data.records;
        this.totalNum = response.data.total;
      });
    },
    clearCondition() {
      this.form.agentNo = "";
      this.form.agentName = "";
      this.form.contactMobile = "";
      this.search(true);
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    showEditPop(row) {
      this.isShowEdit = true;
      this.editStatusForm.id = row.id;
      this.editStatusForm.status = row.status;
    },
    submitStatusInfo() {
      this.$refs.form.validate(valid => {
        if (valid) {
          updateRecommendCustomsStatus(this.editStatusForm).then(res=>{
            this.search(true)
            this.closeForm()
          });
        }
      })
    },
    closeForm() {
      this.isShowEdit = false;
      this.loading = false;
      this.editStatusForm.id = null;
      this.editStatusForm.status = "";
    },
    getTagType({ status }) {
      console.log(status)
      switch (status) {
        case 100:
          return 'success'
        case 101:
          return 'danger'
        case 102:
          return 'info'
        case 103:
          return
        default:
          return
      }
    }
  },
};
</script>

<style lang="scss" scoped>

.none-department-alert {
  position: relative;
  box-sizing: border-box;
  display: flex;
  width: 100%;
  padding: 8px 16px;
  margin-bottom: 8px;
  border-radius: 4px;
  background-color: #fdf6ec;
  color: #e6a23c;
}

.none-department-content {
  padding: 0 8px;
  display: table-cell;
}

.none-department-return {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 50px;
  font-style: normal;
  font-size: 13px;
  opacity: 1;
  color: #c0c4cc;
  cursor: pointer;
}
</style>
