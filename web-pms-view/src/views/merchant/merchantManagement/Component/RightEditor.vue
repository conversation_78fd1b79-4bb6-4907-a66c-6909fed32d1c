<template>
  <div class="editor-container">
    <div class="editor-toggle text-center" @click="openEditor" v-show="!showEditor">
      <svg-icon icon-class="editor"></svg-icon>
      <p>跟进动态</p>
    </div>
    <div v-show="showEditor">
      <el-form :model="form" :rules="rules" label-position="top" ref="form">
        <el-form-item label="记录内容" prop="eventContent">
          <tinymce
            v-model="form.eventContent"
            :width="300"
            :height="200"
            :id='Date.now() + ""'
            :show-img-upload="false"
            :menubar="false"
            toolbar="simple"
            ref="editor"
          ></tinymce>
        </el-form-item>

        <el-form-item label="跟进类型" prop="eventType" style="margin-top: 32px;">
          <el-select
            clearable
            v-model="form.eventType"
            @change="onEventChange"
          >
            <el-option
              v-for="item in $dict('EventTypeEnum')"
              :key="item.code"
              :label="item.desc"
              :value="Number(item.code)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="text-right" style="margin-top: 16px;">
        <el-button type="primary" @click="addEventFollow" :loading="loading">发布</el-button>
        <el-button @click="close">取消</el-button>
      </div>

      <div class="list-box" style="margin-top: 32px" @click="onClickList">
        <div class="editor-title">全部</div>
        <div
          class="list-item"
          v-for="(item, index) in list"
          :key="index">
          <div class="list-content" v-html="item.eventContent"></div>
          <div class="list-content-type">
            <el-tag type="info">
              {{ $dictCode('EventTypeEnum', item.eventType).desc }}
            </el-tag>
          </div>
          <div class="list-desc">
            <div class="list-content-user">{{ item.updateBy }}</div>
            <div class="list-content-time color-gray">{{ item.updateTime }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { addEventFollow } from "@/api/eventsFollow";
  import Tinymce from '@/components/Tinymce'

  export default {
    name: 'RightEditor',
    components: {
      Tinymce,
    },
    props: {
      mchNo: {
        type: String,
        default: ''
      },
      list: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        loading: false,
        form: {
          eventContent: '',
          eventType: '',
          eventTypeDesc: '',
        },
        rules: {
          eventContent: [{ required: true, trigger: 'blur', message: '请输入内容' }],
          eventType: [{ required: true, trigger: 'change', message: '请选择类型' }]
        },
        showEditor: false,
      }
    },
    methods: {
      openEditor() {
        this.showEditor = true;
        this.$emit("toggle", true)
      },
      close() {
        this.showEditor = false;
        this.$emit("toggle", false)
      },
      async addEventFollow() {
        const result = await this.$refs.form.validate().catch(_=>false);
        if (!result) return;
        this.loading = true;
        const {data} = await addEventFollow({
          ...this.form,
          mchNo: this.mchNo
        }).finally(() => {
          this.loading = false
        });
        data && this.$message.success(data);
        this.$refs.editor.setContent('');
        this.form.eventType = '';
        this.form.eventTypeDesc = '';
        this.$emit('list-change')
      },
      onEventChange(val) {
        for (let i = 0, len = this.$dict('EventTypeEnum').length; i < len; i++) {
          if (this.$dict('EventTypeEnum')[i].code == val) {
            this.form.eventTypeDesc = this.$dict('EventTypeEnum')[i].desc;
            return
          }
        }
      },
      onClickList(e) {
        const target = e.target;
        const tagName = target.tagName.toLowerCase();
        if (tagName === 'img' && target.src) {
          this.$preview(target.src)
        }
      },
    },
  }
</script>

<style scoped lang="scss">
  .editor-container {
    position: fixed;
    top: 60px;
    right: 0;
    bottom: 0;
    z-index: 1000;
    max-width: 350px;
    padding: 16px;
    background: #fff;
    overflow: auto;

    &::-webkit-scrollbar {
      visibility: hidden;
      width: 0;
    }

    .editor-title {
      margin-bottom: 8px;
    }

    .list-box {
      padding-bottom: 16px;
    }
    .list-item {
      position: relative;
      margin-top: 8px;
      padding: 8px;
      border: 1px solid $deepGray;
      border-radius: 8px;

      &::v-deep img {
        width: 70px;
        height: 70px;
        cursor: pointer;
      }
    }
    .list-desc {
      margin-top: 8px;
    }
    .list-content-type {
      position: absolute;
      right: 10px;
      bottom: 10px;
    }
    .list-content-time {
      vertical-align: bottom;
    }
  }
</style>
