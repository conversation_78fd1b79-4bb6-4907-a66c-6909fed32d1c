<template>
  <el-dialog
    :visible="visible"
    title="更改销售"
    :before-close="close"
    append-to-body
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="150px"
    >
      <el-form-item label="销售：" prop="salerId">
        <el-select
          clearable
          v-model="form.salerId"
          @change="onSaleChange"
        >
          <el-option
            v-for="item in staffList"
            :key="item.id"
            :value="item.id"
            :label="item.realName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="合伙人：" v-if="isMerchant">
        <el-select
          clearable
          :disabled="!form.salerId"
          v-model="form.agentNo"
          @change="onAgentChange"
        >
          <el-option
            v-for="item in agentList"
            :key="item.agentNo"
            :value="item.agentNo"
            :label="item.agentName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="审批意见：">
        <el-input
          type="textarea"
          v-model="form.remark"
        ></el-input>
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <el-button type="primary" @click="changeSale" :loading="loading">确定</el-button>
      <el-button @click="close">取消</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { getAllAgentSimple } from '@/api/agent';

  export default {
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      staffList: {
        type: Array,
        default: () => []
      },
      isMerchant: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        loading: false,
        form: {
          salerId: '',
          salerName: '',
          agentNo: '',
          agentName: '',
          remark: '',

        },
        agentList: [],
        rules: {
          salerId: [{required: true, message: '请选择销售', trigger: 'change'}]
        },
      }
    },
    watch: {
      visible: {
        handler(val) {
          if (val && this.form.salerId) {
            this.onSaleChange(this.form.salerId)
          }
        },
        immediate: true,
      },
    },
    methods: {
      async onSaleChange(salerId) {
        if (!salerId) {
          this.form = {
            salerId: '',
            salerName: '',
            agentNo: '',
            agentName: '',
          };
          return;
        }
        for (let i = 0; i < this.staffList.length; i++) {
          if (salerId == this.staffList[i].id) {
            this.form.salerName = this.staffList[i].realName;
            break;
          }
        }
        const { data } = await getAllAgentSimple({
          salerId
        });
        this.agentList = data;
      },
      onAgentChange(val) {
        if (!val) {
          this.form.agentNo = '';
          this.form.agentName = '';
          return
        }
        for (let i = 0; i < this.agentList.length; i++) {
          if (this.agentList[i].agentNo == val) {
            this.form.agentName = this.agentList[i].agentName;
          }
        }
      },
      close(done) {
        this.$emit('update:visible', false)
      },
      async changeSale() {
        const valid = await this.$refs.form.validate().catch(e => e);
        if (!valid) return;
        this.$emit('change-sale', {...this.form})
        this.loading = false;
        this.close();
      }
    }
  }
</script>

<style scoped lang="scss">

</style>
