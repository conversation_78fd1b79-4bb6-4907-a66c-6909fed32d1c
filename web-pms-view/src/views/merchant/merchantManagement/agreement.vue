<template>
  <div class="box-container">
    <el-tabs
      v-model="form.signType"
      @tab-click="search(true)"
    >
      <el-tab-pane
        v-for="(item, key) in $dict('AgreementSignTypeEnum')"
        :key="key"
        :label="item.desc"
        :name="item.code"
      ></el-tab-pane>
    </el-tabs>
    <el-dropdown
      trigger="click"
      class="create-btn"
    >
      <el-button
        class="el-dropdown-link"
        type="primary"
      >
        发起签署<i class="el-icon-arrow-down el-icon--right"></i>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          @click.native="$router.push(`/merchant/startAgreement?signType=${form.signType}`)"
        >直接发起
        </el-dropdown-item>
        <el-dropdown-item
          @click.native="$router.push(`/merchant/chooseAgreementTemplate?signType=${form.signType}`)"
        >使用模板发起
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <warning-block>
      <span class="warning-title">协议说明：</span>
      <p>1、商户协议签署可在商户创建后发起，无需等待主体信息认证流程的完成</p>
      <p>2、协议签署支持两种方式：①直接发起，即通过上传非标协议文件，发起签署流程；②使用模板发起，即使用标准协议完成签署流程</p>
    </warning-block>

    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">签署方：</span>
            <el-select
              clearable
              v-model="form.signerNoList"
              multiple
              filterable
            >
              <el-option
                v-for="item in allMchList"
                :key="item.mchNo"
                :value="item.mchNo"
                :label="item.mchName"
              ></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">协议名称：</span>
            <el-input v-model="form.topicLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">负责销售：</span>
            <el-select
              clearable
              v-model="form.salerId"
            >
              <el-option
                v-for="item in staffList"
                :key="item.id"
                :label="item.realName"
                :value="item.id"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">协议状态：</span>
            <el-select
              clearable
              v-model="form.status"
            >
              <el-option
                v-for="item in $dict('AgreementStatusEnum')"
                :key="item.code"
                :value="item.code"
                :label="item.desc"
              ></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">负责人：</span>
            <el-input
              clearable
              v-model="form.signNameLike"
            >
            </el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">发起时间：</span>
            <date-picker
              ref="datePicker"
              type="datetime"
              picker="separate"
              :start-time.sync="form.beginDate"
              :end-time.sync="form.endDate"
            ></date-picker>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">截止时间：</span>
            <date-picker
              ref="datePicker2"
              type="datetime"
              picker="separate"
              :start-time.sync="form.beginDeadLine"
              :end-time.sync="form.endDeadLine"
              :use-option="false"
            ></date-picker>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">完成时间：</span>
            <date-picker
              ref="datePicker3"
              type="datetime"
              picker="separate"
              :start-time.sync="form.beginFinishTime"
              :end-time.sync="form.endFinishTime"
            ></date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="search(true)"
            >查询
            </el-button>
            <el-button
              type="text"
              @click="getExportList"
            >查看导出列表
            </el-button>
            <el-button
              type="text"
              @click="resetForm"
            >清空筛选条件
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="func-container">
      <el-button
        :disabled="selection.length === 0"
        @click="multiRetreat"
      >批量撤回
      </el-button>
      <el-button
        :disabled="selection.length === 0"
        @click="multiDelete"
      >批量删除
      </el-button>
      <el-button
        :disabled="selection.length === 0"
        @click="exportFile"
      >批量下载
      </el-button>
      <el-button @click="exportExcel">导出明细</el-button>
      <el-button
        v-show="form.signType == 100"
        :disabled="selection.length === 0"
        type="danger"
        @click="turnToOffline"
      >转线下签署
      </el-button>
    </div>

    <div class="content-container">
      <el-table
        class="content-main"
        :data="agreementObj.data"
        v-if="agreementObj.data"
        @selection-change="handleSelect"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column
          label="协议名称"
          min-width="100px"
          prop="topic"
        ></el-table-column>
        <el-table-column
          label="负责销售"
          min-width="80px"
          prop="salerName"
        ></el-table-column>
        <el-table-column
          label="签署方"
          width="150"
        >
          <template v-slot="{row}">
            <div
              class="signer-item"
              v-for="(item, index) in row.signerList"
              :key="index"
            >
              {{ $dictCode('AgreementSignerTypeEnum', item.signerType).desc }}：{{ item.signerMchName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="签署状态">
          <template v-slot="{row}">
            <div
              class="signer-item"
              v-for="(item, index) in row.signerList"
              :key="index"
            >
              {{ $dictCode('AgreementSignerStatusEnum', item.status).desc }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="协议负责人"
          width="130"
        >
          <template v-slot="{row}">
            <div
              class="signer-item"
              v-for="(item, index) in row.signerList"
              :key="index"
            >
              {{ item.signerName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="手机号"
          width="150"
        >
          <template v-slot="{row}">
            <div
              class="signer-item"
              v-for="(item, index) in row.signerList"
              :key="index"
            >
              {{ item.signerPhone }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="签署截止时间"
          width="120"
          prop="deadline"
        >
          <template v-slot="{row}">
            <p v-html="renderTime(row.deadline)"></p>
          </template>
        </el-table-column>
        <el-table-column
          label="协议到期时间"
          width="120"
          prop="expireTime"
        >
          <template v-slot="{row}">
            <p v-html="renderTime(row.expireTime)"></p>
          </template>
        </el-table-column>
        <el-table-column
          label="发起时间"
          width="120"
          prop="createTime"
        >
          <template v-slot="{row}">
            <p v-html="renderTime(row.createTime)"></p>
          </template>
        </el-table-column>
        <el-table-column
          label="完成时间"
          width="120"
          prop="finishTime"
        >
          <template v-slot="{row}">
            <p v-html="renderTime(row.finishTime)"></p>
          </template>
        </el-table-column>

        <el-table-column
          label="协议状态"
          min-width="80px"
          prop="status"
        >
          <template v-slot="{row}">
            {{ $dictCode('AgreementStatusEnum', row.status).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="错误日志"
          min-width="80px"
          prop="errorMsg"
        >
          <template v-slot="{row}">
            <el-tooltip v-if="row.errorMsg" placement="top-end">
              <p slot="content" style="max-width: 500px">{{ row.errorMsg }}</p>
              <el-button type="text">查看日志</el-button>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          min-width="150px"
          fixed="right"
        >
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="openSignFile(row)"
            >查看
            </el-button>
            <el-button
              type="text"
              @click="download(row)"
              v-if="row.status == 100"
            >下载
            </el-button>
            <el-button
              type="text"
              @click="retreatOne(row)"
              v-if="row.status == 103"
            >撤回
            </el-button>
            <el-button
              type="text"
              @click="deleteOne(row)"
              v-if="row.status == 101 || row.stats == 102"
            >删除
            </el-button>
            <el-button
              type="text"
              @click="delay(row)"
              v-if="row.status == 102 && row.signType !== 100"
            >延期
            </el-button>
            <!--<el-button type="text" @click="handleEdit(row)">编辑</el-button>-->
            <el-button
              type="text"
              v-if="row.status == 103 && form.signType == 101"
              @click="handleFinish(row)"
            >归档
            </el-button>
            <el-button
              type="text"
              v-if="form.signType === '101' && row.status === 100"
              @click="openFileChange(row)">替换协议
            </el-button>
            <!--<el-button type="text" @click="cancelAgreement(row)">取消</el-button>-->
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="agreementObj"
          ref="pagination"
          :total="agreementObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
      <ExportRecord ref="exportRecord"></ExportRecord>
    </div>

    <!--延期窗口  -->
    <el-dialog
      title="签署截止时间延长"
      :visible.sync="showDelay"
      :before-close="() => {showDelay = false}"
    >
      <!--      <el-alert show-icon type="warning" title="只可延期一次，且最多延长90天"></el-alert>-->
      <el-form
        ref="delayForm"
        :model="delayForm"
        :rules="delayRules"
      >
        <el-form-item label="原签署截止时间：">{{ editRow.deadline }}</el-form-item>
        <el-form-item
          label="现签署截止时间："
          prop="deadLine"
        >
          <el-date-picker
            type="datetime"
            v-model="delayForm.deadLine"
            placeholder="请选择时间"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button
          type="primary"
          @click="handleDelay"
        >确认
        </el-button>
        <el-button @click="showDelay = false">取消</el-button>
      </template>
    </el-dialog>


    <el-dialog
      title="替换协议"
      :visible.sync="showFileChange"
      :before-close="closeFileChange"
    >
      <file-upload
        v-if="editRow.changeFile"
        :url.sync="editRow.changeFile[0].fileUrl"
        :name.sync="editRow.changeFile[0].fileName"
        :show-preview="false"
        accept=".doc,.docx,.pdf"
        :max="1"
        :options="{
          img: false,
          word: true,
          size: 15
        }"
      >
        <template v-slot:trigger>
          <el-button
            type="primary"
            size="small">上传协议文件
          </el-button>
        </template>
        <template v-slot:tip>
          <div>
            只能上传doc/docx/pdf文件，且不超过15M
          </div>
        </template>
      </file-upload>


      <template v-slot:footer>
        <el-button
          type="primary"
          @click="changeFile">确认
        </el-button>
        <el-button @click="closeFileChange">取消</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import {
  cancelAgreement,
  getAgreementPage,
  retreatAgreement,
  deleteAgreement,
  turnToOffline,
  delayAgreement,
  exportExcel,
  exportFile,
  replaceAgreement
} from '@/api/agreement'
import { getAllMchList } from '@/api/merchant'
import { getSales } from '@/api/system'
import { toPromise } from "@/utils";
import dayjs from "dayjs";
import FileUpload from "@/components/FileUpload";

export default {
  name: 'AgreementManage',
  components: {
    FileUpload,
    ExportRecord
  },
  data(vm) {
    return {
      form: {
        topicLike: '',
        salerId: '',
        signerNoList: [],
        beginDate: '',
        endDate: '',
        status: '',
        signType: vm.$dict('AgreementSignTypeEnum')[0].code,
        signNameLike: ''
      },
      pageCurrent: 1,
      pageSize: 10,
      agreementObj: {},
      allMchList: [],
      staffList: [],
      selection: [],

      editRow: {},
      showDelay: false, // 延期窗口控制
      delayForm: {
        deadLine: '2022-08-21 00:00:00'
      },
      delayRules: {
        deadLine: [{
          required: true,
          message: '请选择时间',
          trigger: 'blur'
        }]
      },
      pickerOptions: {
        disabledDate(date) {
          let timeDisable = 90
          return date > dayjs(vm.editRow.deadline).endOf('day').add(timeDisable, 'day');
        }
      },

      showFileChange: false,
    }
  },
  mounted() {
    this.search();
    this.getAllMchList();
    this.getSales();
  },
  methods: {
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('47');
    },
    getAllMchList() {
      getAllMchList().then(response => {
        this.allMchList = response.data;
      })
    },
    getSales() {
      getSales().then(
        (response) => {
          this.staffList = response.data;
        }
      );
    },
    resetForm() {
      this.form = {
        signType: this.$dict('AgreementSignTypeEnum')[0].code
      }
      this.$refs.datePicker.clearTime();
      this.$refs.datePicker2.clearTime();
      this.$refs.datePicker3.clearTime();
    },
    async search(init) {
      if (init === true) {
        this.pageCurrent = 1;
      }
      const { data } = await getAgreementPage({
        pageCurrent: this.pageCurrent,
        pageSize: this.pageSize,
        ...this.form
      })
      this.agreementObj = data;
    },
    handleEdit(data) {
      this.$router.push('/merchant/startAgreement?agreementId=' + data.id);
    },
    handleFinish(data) {
      this.$router.push('/merchant/finishAgreement?agreementId=' + data.id);
    },
    async download(row) {
      // downloadArchiveFile({id: data.id}).then(({data}) => {
      //   this.$message.success(data);
      // })
      let file = row.fileList.find(item => item.type == 101)
      const fileMsg = await this.formatFileMsg(file.fileUrl)
      const fileName = file.fileName.split('.')
      this.downloadFile(fileMsg.fileUrl, fileName[0], fileName[1]);
    },
    cancelAgreement(data) {
      this.$prompt('确定要取消该协议签署?').then(({ value }) => {
        cancelAgreement({
          id: data.id,
          description: value
        }).then(() => {
          this.$message.success('取消成功');
          this.search();
        })
      }).catch(() => {
      })
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    handleSelect(val) {
      this.selection = val
    },
    async openSignFile(row) {
      let file = row.fileList.find(item => item.type === 100)
      if (!file) {
        this.$message.error('未上传协议文件')
        return;
      }
      const fileMsg = await this.formatFileMsg(file.fileUrl)
      window.open(fileMsg.fileUrl)
    },
    retreatOne(row) {
      this.onRetreat([row.id])
    },
    multiRetreat() {
      if (this.selection.length) {
        const idList = this.selection.map(item => item.id)
        this.onRetreat(idList)
      }
    },
    async onRetreat(idList) {
      const [err] = await toPromise(this.$confirm('该操作不可逆，是否确认撤回协议？', '提示', {
        type: "warning"
      }))
      if (err) return;
      const { data } = await retreatAgreement({ idList })
      data && this.$message.success(data)
      this.search()
    },
    deleteOne(row) {
      this.onDelete([row.id])
    },
    multiDelete() {
      if (this.selection.length) {
        const idList = this.selection.map(item => item.id)
        this.onDelete(idList)
      }
    },
    async onDelete(idList) {
      const [err] = await toPromise(this.$confirm('该操作不可逆，是否确认删除协议？', '提示', {
        type: "warning"
      }))
      if (err) return;
      const { data } = await deleteAgreement({ idList })
      data && this.$message.success(data)
      this.search()
    },
    async delay(row) {
      this.editRow = row
      this.delayForm.deadLine = this.editRow.deadline
      this.delayForm.id = this.editRow.id
      this.showDelay = true
    },
    async handleDelay() {
      const [err] = await toPromise(this.$refs.delayForm.validate())
      if (err) return
      const { data } = await delayAgreement(this.delayForm)
      data && this.$message.success(data)
      this.showDelay = false
      this.search()
    },
    async turnToOffline() {
      if (!this.selection.length) return
      const idList = this.selection.map(item => item.id)
      const { data } = await turnToOffline({ idList })
      data && this.$message.success(data)
      this.form.signType = 101
      this.search(true)
    },
    async exportFile() {
      if (!this.selection.length) return
      const idList = this.selection.map(item => item.id)
      await exportFile({ idList })
      this.$message.success('操作成功，请查看导出列表')
    },
    async exportExcel() {
      await exportExcel({
        pageCurrent: this.pageCurrent,
        pageSize: this.pageSize,
        ...this.form
      })
      this.$message.success('操作成功，请查看导出列表')
    },
    openFileChange(row) {
      this.editRow = JSON.parse(JSON.stringify(row))
      this.showFileChange = true
      this.editRow.changeFile = this.editRow.fileList.filter(f => f.type === 101)
    },
    closeFileChange() {
      this.showFileChange = false
      this.editRow = {}
    },
    async changeFile() {
      if (!this.editRow.changeFile[0].fileUrl) {
        return this.$message.error('请上传文件')
      }
      const { data } = await replaceAgreement({
        id: this.editRow.id,
        fileUrl: this.editRow.changeFile[0].fileUrl,
        fileName: this.editRow.changeFile[0].fileName,
      })
      data && this.$message.success(data)
      this.closeFileChange()
      this.search()
    },
  }
}
</script>

<style
  lang="scss"
  scoped
>

.signer-item {
  &:first-child {
    border-bottom: 1px solid #EBEEF5;
  }
}
</style>
