<template>
  <div
    v-if="agreementForm"
    v-loading="loading"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <pdf-drag-template
      get-drag-api="/agreement/getComponentList"
      :get-drag-query="{singleSignerType: agreementForm.signMode == 101 ? undefined : agreementForm.singleSignerType}"
      :file="agreementFormFileVoUrl"
      ref="drag"
    >
      <template v-slot:footer>
        <el-button
          type="primary"
          @click="onSubmit"
          :loading="loading"
        >确认
        </el-button>
        <el-button @click="$router.back()">返回</el-button>
      </template>
    </pdf-drag-template>
  </div>
</template>

<script>
import PdfDragTemplate from "@/components/PdfDragTemplate";
import { createAgreement } from '@/api/agreement'
import { toPromise } from "@/utils";

export default {
  name: 'DragAgreement',
  components: {
    PdfDragTemplate
  },
  data() {
    return {
      loading: false,
      agreementForm: null,
      agreementFormFileVoUrl: '',
    }
  },
  mounted() {
    this.agreementForm = JSON.parse(sessionStorage.getItem('agreementInfo'))
  },
  watch: {
    agreementForm: {
      async handler(val) {
        if (val && val.fileVoList && val.fileVoList.length > 0) {
          const fileMsg = await this.formatFileMsg(val.fileVoList[0].fileUrl)
          this.agreementFormFileVoUrl = fileMsg.fileUrl
        }
      },
      immediate: true
    }
  },
  methods: {
    async onSubmit() {
      console.log(this.agreementForm)
      let components = this.$refs.drag.getAllDragInst()
      this.loading = true
      const [err, resp] = await toPromise(createAgreement({
        ...this.agreementForm,
        components,
      }))
      this.loading = false
      if (err) return
      const { data } = resp
      data && this.$message.success(data)
      this.$router.push('/merchant/merchantManagement/agreement')
      this.$nextTick(() => {
        sessionStorage.removeItem('agreementInfo')
      })
    },
  }
}
</script>
