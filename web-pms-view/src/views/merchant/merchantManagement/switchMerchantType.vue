<template>
  <div class="box-container">
    <hgroup>
      <h2>选择商户类型</h2>
      <h5>请选择商户类型，选择后不能更改</h5>
    </hgroup>
    <div class="card-container">
      <div class="card-box">
        <img src="@/assets/yonggongqiye.svg" alt="">
        <p class="card-title">用工企业</p>
        <ul>
          <li>平台型灵活用工企业</li>
        </ul>
        <hr>
        <el-button type="primary" @click="createMerchant">立即创建</el-button>
      </div>
      <div class="card-box">
        <img src="@/assets/gongyingshang.svg" alt="">
        <p class="card-title">供应商</p>
        <ul>
          <li>具有委托代征能力的税服/代征机构</li>
        </ul>
        <hr>
        <el-button type="primary" @click="createSupplier">立即创建</el-button>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'switchMerchantType',
    methods: {
      createMerchant() {
        sessionStorage.removeItem('mainInfo')
        sessionStorage.removeItem('bankInfo')
        sessionStorage.removeItem('merchantInfo')
        sessionStorage.removeItem('cooperationInfo')
        this.$router.push('/merchant/merchantManagement/createMerchant')
      },
      createSupplier() {
        sessionStorage.removeItem('mainInfo')
        sessionStorage.removeItem('bankInfo')
        sessionStorage.removeItem('merchantInfo')
        sessionStorage.removeItem('cooperationInfo')
        this.$router.push('/merchant/merchantManagement/createSupplier')
      }
    }
  }
</script>

<style lang="scss" scoped>
  .box-container {
    text-align: center;
    padding: 50px;
    hgroup {
      margin-bottom: 40px;
    }
    .card-container {
      .card-box {
        display: inline-block;
        padding: 20px;
        margin: 0 40px;
        border: 1px solid #ccc;
        img {
          display: block;
          width: 250px;
          height: 300px;
          margin: 0 25px;
        }
        .card-title {
          font-size: 20px;
        }
        li {
          text-align: left;
        }
      }
    }
  }
</style>
