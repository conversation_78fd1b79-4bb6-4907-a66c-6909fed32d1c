<template>
  <div class="box-container">
    <el-form ref="form" :model="form" :rules="rules" size="small" label-width="140px">
      <p class="subTitle">
        基本信息
      </p>
      <el-form-item label="协议名称">
        <el-input v-model="form.topic" :disabled="!!agreementId"/>
      </el-form-item>
      <el-form-item label="负责销售">
        <el-select clearable v-model="salerInfo" :disabled="!!agreementId">
          <el-option v-for="item in staffList" :label="item.realName" :value="[item.id, item.realName]" :key="item.name"></el-option>
        </el-select>
      </el-form-item>
      <p class="subTitle">
        上传文件
      </p>
      <el-form-item label="协议文件">
        <el-upload
          class="upload-demo"
          :action="baseUrl + '/file/upload'"
          :file-list="fileList"
          accept=".doc,.docx"
          list-type="picture-card"
          :disabled="!!agreementId"
          v-toggle="fileList"
          >
          <div slot="file" slot-scope="{file}" class="fileBg">
            <span class="file-name">{{ file.fileName }}</span>
          </div>
          <el-button size="small" type="primary">上传本地文件</el-button>
          <div slot="tip" class="el-upload__tip">只能上传doc/docx文件，且不超过15M</div>
        </el-upload>
      </el-form-item>
      <el-form-item label="归档协议" prop="fileVoList">
        <el-upload
          class="upload-demo"
          :action="baseUrl + '/file/upload'"
          :headers="uploadHeader"
          accept=".doc,.docx,.pdf"
          :file-list="uploadFileList"
          list-type="picture-card"
          :before-upload="beforeAvatarUpload"
          :on-remove="handleRemove"
          :on-success="handSuccess"
        >
          <div slot="file" slot-scope="{file}" class="fileBg">
            <span class="file-name">{{ file.name }}</span>
            <span class="el-upload-list__item-actions">
              <span
                class="el-upload-list__item-delete"
                @click="handleFileRemove(file, 'uploadFileList')"
              >
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
          <el-button size="small" type="primary">上传本地文件</el-button>
          <div slot="tip" class="el-upload__tip">只能上传doc/docx/pdf文件，且不超过15M</div>
        </el-upload>
      </el-form-item>

    </el-form>
    <div class="btn-group">
      <el-button :loading="loading" type="primary" @click="doSubmit">发起</el-button>
      <el-button @click="$router.push('/merchant/merchantManagement/agreement')">取消</el-button>
    </div>
  </div>
</template>

<script>
  import { getStaffListByDepartment } from '../../../api/system'
  import {
    finishAgreement,
    getAgreementById,
  } from '../../../api/agreement'

  export default {
    data() {
      return {
        loading: false,
        show: false,
        actionType: undefined,
        staffList: [],
        activeMchList: [],
        salerInfo: '',
        fileList: [],
        uploadFileList: [],
        form: {
          topic: '',
          salerId: '',
          salerName: '',
          fileVoList: [],
        },
        rules: {
          fileVoList: [{required: true, message: '请上传协议文件', trigger: 'blur'}],
        }
      }
    },

    computed: {
      agreementId() {
        return this.$route.query.agreementId;
      },
    },
    mounted(){
      this.init();
      this.getStaffListByDepartment();
    },
    methods: {
      init() {
        getAgreementById({
          id: this.agreementId
        }).then(response => {
          this.form.topic = response.data.topic;
          this.form.salerId = response.data.salerId;
          this.form.salerName = response.data.salerName;
          this.salerInfo = response.data.salerName;
          this.fileList = response.data.fileList;
        })
      },
      getStaffListByDepartment() {
        getStaffListByDepartment({ departmentId: 0 }).then(
          (response) => {
            this.staffList = response.data;
          }
        );
      },
      beforeAvatarUpload(file) {
        const isTrueType = ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/pdf'].includes(file.type);
        const isLt15M = file.size / 1024 / 1024 < 15;

        this.loading = true;
        if (!isTrueType) {
          this.$message.error('上传文件只能是 doc、docx、pdf 格式!');
        }
        if (!isLt15M) {
          this.$message.error('上传文件大小不能超过 15MB!');
        }
        return isTrueType && isLt15M;
      },
      handleRemove(file, fileList) {
        this.uploadFileList = fileList;
        this.form.fileVoList = fileList;
        this.loading = false;
      },
      handSuccess(response, file, fileList) {
        this.uploadFileList = fileList;
        this.form.fileVoList = fileList;
        this.loading = false;
      },
      doSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.form.fileVoList = [];
            for(let item of this.uploadFileList) {
              this.form.fileVoList.push({
                fileName: item.name,
                fileUrl: item.response.data
              })
            }
            finishAgreement({
              id: this.agreementId,
              fileVoList: this.form.fileVoList
            }).then(response => {
              this.$message.success('归档成功');
              this.$router.push('/merchant/merchantManagement/agreement');
            })
          }
        })
      },
      handleFileRemove(file, list) {
        let fileUrl = file.response ? file.response.data : file.fileUrl;
        this[list] = this[list].filter(item => {
          let itemUrl = item.response ? item.response.data : item.fileUrl;
          return itemUrl !== fileUrl
        })
        this.loading = false;
      }
    }
  }
</script>

<style lang="scss" scoped>
  .box-container {
    .el-form {
      margin-left: 0 auto;

    }
    .subTitle {
      margin-top: 60px;
      margin-left: 0;
    }
    .add-btn {
      width: 100%;
      padding: 20px;
      box-sizing: border-box;
      border: 1px dotted #ccc;
      background: #fff;
      cursor: pointer;
      &:active {
        border: 1px dotted #ccc;
      }
      &:focus {
        border: 1px dotted #ccc;
        outline: none;
      }
    }
    .btn-group {
      margin-top: 60px;
      text-align: center;
      .el-button {
        width: 120px;
        &+.el-button {
          margin-left: 40px;
        }
      }
    }
    ::v-deep .el-upload-list {
      height: 100%;
    }
    :v-deep .el-upload-list__item {
      height: 100%;
    }
  }
</style>
