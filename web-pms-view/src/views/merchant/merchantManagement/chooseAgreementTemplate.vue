<template>
  <div class="box-container">
    <div class="search-wrapper">
      <el-input
        v-model="topicLike"
        placeholder="请输入模板名称"
      ></el-input>
      <el-button
        type="primary"
        @click="getAgreementTemplateList(true)"
      >搜索
      </el-button>
    </div>
    <div class="content-container">
      <el-table :data="templateObj.data">
        <el-table-column
          label="模板名称"
          prop="topic"
        ></el-table-column>
        <el-table-column
          label="流程模板ID"
          prop="flowTemplateId"
        ></el-table-column>
        <el-table-column
          label="文件模板ID"
          prop="fileTemplateId"
        ></el-table-column>
        <el-table-column label="操作">
          <template v-slot="scope">
            <el-button type="text">预览</el-button>
            <el-button
              type="text"
              @click="chooseTemplate(scope.row)"
            >使用模板
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      v-if="templateObj"
      ref="pagination"
      :total="templateObj.totalRecord"
      :current-page.sync="pageCurrent"
      :page-sizes="[10,50]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    >
    </el-pagination>
  </div>
</template>

<script>
import { getAgreementTemplateList } from '@/api/agreement'

export default {
  name: 'chooseAgreementTemplate',
  data() {
    return {
      templateObj: {},
      topicLike: '',
      pageCurrent: 1,
      pageSize: 10,
    }
  },
  computed: {
    signType() {
      return this.$route.query.signType
    }
  },
  mounted() {
    this.getAgreementTemplateList();
  },
  methods: {
    async getAgreementTemplateList(flag) {
      if (flag) {
        this.pageCurrent = 1;
      }
      const {data} = await getAgreementTemplateList({
        topicLike: this.topicLike,
        pageCurrent: this.pageCurrent,
        pageSize: this.pageSize,
      })
      this.templateObj = data;
    },
    chooseTemplate({id}) {
      this.$router.push({
        path: '/merchant/startAgreement',
        query: {
          templateId: id,
          signType: this.signType,
        }
      });
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.getAgreementTemplateList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getAgreementTemplateList();
    },
  }
}
</script>

<style lang="scss" scoped>
.box-container {
  padding: 20px;

  .search-wrapper .el-input {
    width: 200px;
  }
}

.el-pagination {
  text-align: right;
}
</style>
