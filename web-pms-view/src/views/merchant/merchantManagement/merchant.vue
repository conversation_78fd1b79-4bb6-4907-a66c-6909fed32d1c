<template>
  <div class="box-container">
    <el-menu :default-active="defaultActive" mode="horizontal" @select="handleTabChange">
      <el-menu-item v-for="item in $dict('MerchantTypeEnum')" :key="item.code" :index="item.code">
        {{ item.desc }}
      </el-menu-item>
    </el-menu>

    <el-button style="marginTop: 16px;" class="create-btn" type="primary" @click="createMerchant"
               v-show="hasBtnPermission">{{ createBtn }}
    </el-button>
    <warning-block>
      <span class="warning-title">创建商户流程说明：</span>
      <p>1、创建商户：将各种外部渠道搜集的客户信息通过手工的方式录入</p>
      <p>2、主体信息认证：通过「去认证」补充主体信息、法定代表人信息、经营信息等材料，经审批通过后开立支付账户</p>
      <p>3、创建产品报价单：在【商户信息】详情页-【产品报价单】处手工创建产品报价单，经审批通过后自动完成产品计费配置</p>
      <p>4、创建B端协议：在【协议】菜单，发起B端协议签署（线上/线下），协议归档后即自动激活并可进行业务交易</p>
    </warning-block>

    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户编号：</span>
            <el-input v-model="form.mchNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">商户名称：</span>
            <el-input v-model="form.mchNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">资质状态：</span>
            <el-select clearable v-model="form.authStatus">
              <el-option v-for="(item, index) in $dict('AuthStatusEnum')" :key="index" :label="item.desc"
                         :value="item.code"></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper" v-if="form.merchantType != 102">
          <div class="flex-item">
            <span class="flex-item__label">负责销售：</span>
            <el-select clearable v-model="form.salerId">
              <el-option v-for="item in staffList" :key="item.id" :label="item.realName" :value="item.id"></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">所属部门：</span>
            <el-select clearable v-model="form.departmentId">
              <el-option v-for="item in departmentList" :key="item.id" :label="item.departmentName" :value="item.id">
              </el-option>
            </el-select>
          </div>
          <div class="flex-item" v-if="form.merchantType == 100">
            <span class="flex-item__label">合伙人编号：</span>
            <el-input v-model="form.agentNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper" v-if="form.merchantType == 100">
          <div class="flex-item">
            <span class="flex-item__label">品牌名称：</span>
            <el-input v-model="form.branchName"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">备注：</span>
            <el-input v-model="form.remark"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">供应商：</span>
            <el-select clearable v-model="form.quoteMainstayNo">
              <el-option v-for="(item, index) in mainstayList" :key="index" :label="item.mchName" :value="item.mchNo">
              </el-option>
            </el-select>
          </div>
        </div>

        <div class="search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker ref="datePicker" v-model="form.timeRange" @change="getTimeRange" picker="separate"
                         type="datetime"></date-picker>
          </div>
        </div>
        <div class="search-wrapper search-btn-group">
          <el-button type="primary" @click="getMerchantList(true)">查询
          </el-button>
          <el-button @click="exportMchList">导出</el-button>
          <el-button type="text" @click="resetForm">清空筛选条件
          </el-button>
          <el-button type="text" @click="getExportList">查看已导出列表
          </el-button>
        </div>
      </div>
    </div>

    <div class="func-container">
      <el-tabs v-model="form.mchStatus" @tab-click="handleStatusChange" type="card">
        <el-tab-pane key="-1" label="全部" name=""></el-tab-pane>
        <el-tab-pane v-for="(item, index) in $dict('MchStatusEnum')" :key="index" :label="item.desc" :name="item.code">
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="content-container">
      <el-table
        class="content-main"
        :data="merchantObj.data"
        :key="form.merchantType + 'Table'"
      >
        <el-table-column
          label="商户名称"
          prop="mchName"
          min-width="160px"
          fixed="left"
        >
          <template v-slot="{ row }">
            <div
              @click="onClickMch(row.mchNo)"
              class="func-content"
            >
              {{ row.mchNo }}<br />
              {{ row.mchName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="负责销售/所属部门"
          prop="salerName"
          width="100"
          fixed="left"
          v-if="form.merchantType != 102"
        >
          <template v-slot="{ row }">
            {{ row.salerName }}<br>
            {{ row.saleDepartmentName }}
          </template>
        </el-table-column>

        <el-table-column
          label="所属合伙人"
          width="180"
          fixed="left"
          v-if="form.merchantType == 100"
        >
          <template v-slot="{ row }">
            {{ row.agentNo }}<br>
            {{ row.agentName }}
          </template>
        </el-table-column>
        <el-table-column
          label="商户状态"
          prop="mchStatus"
          min-width="80px"
        >
          <template v-slot="{row}">
            <el-tag :type="getTagType(row)">
              {{ $dictCode('MchStatusEnum', row.mchStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="成交状态"
          prop="dealStatus"
          width="120"
        >
          <template v-slot="{row}">
            <div>
              {{ $dictCode('DealStatusEnum', row.dealStatus).desc }}
            </div>
            <div>
              共{{ row.quoteCount }}条
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="资质状态"
          prop="authStatus"
          min-width="80px"
        >
          <template slot-scope="scope">
            {{ $dictCode('AuthStatusEnum', scope.row.authStatus).desc }}
          </template>
        </el-table-column>

        <el-table-column
          label="注册地址"
          width="200">
          <template v-slot="{row}">
            {{ row.registerAddrProvince + row.registerAddrCity + row.registerAddrTown + row.registerAddrDetail }}
          </template>
        </el-table-column>

        <el-table-column
          label="补充说明"
          prop="remark"
          width="150">
          <template v-slot="{row}">
            <el-tooltip
              v-if="row.remark"
              :content="row.remark"
              :disabled="row.remark.length < 15"
            >
              <div>
                {{ row.remark.substring(0, 15) }} {{ row.remark.length > 15 ? '...' : '' }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column
          label="电签账户"
          prop="signAccountStatus"
          v-if="form.merchantType == 101"
        >
          <template v-slot="{ row }">
            <el-tag
              :type="row.signAccountStatus == 100 ? 'success' : 'info'"
              v-if="row.signAccountStatus"
            >
              {{ $dictCode('SignAccountStatusEnum', row.signAccountStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="创建时间/激活时间"
          width="180">
          <template v-slot="{ row }">
            <div>{{ row.createTime }}</div>
            <div>{{ row.activeTime }}</div>
          </template>
        </el-table-column>

        <el-table-column label="操作" prop="data" min-width="140px" fixed="right">
          <template v-slot="{row, $index}">
            <el-button v-if="$dictCode('AuthStatusEnum', row.authStatus).desc == '未认证'" type="text"
                       @click="goAuth(row)">去认证
            </el-button>
            <el-button type="text" v-if="row.mchStatus == 100 || row.mchStatus == 102"
                       @click="goMerchantDetail(row, 'EDIT', $index)">
              编辑
            </el-button>
            <el-button
              v-if="row.merchantType == 101 && row.signAccountStatus !== 100 && row.authStatus == 100"
              type="text" @click="createSignInfo(row)">创建电签账户
            </el-button>
            <el-button type="text" class="red-btn" @click="hardDelete(row)"
                       v-if="$dictCode('AuthStatusDeleteEnum', row.authStatus).code && $store.state.user.userData.type == 1">
              彻底删除
            </el-button>
            <el-button type="text" @click="activateMch(row)"
                       v-if="row.authStatus == 100 && row.mchStatus == 102 && row.dealStatus == 100 && hadPermission('merchant:employer:activate')">
              激活
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-footer class="pagination-container">
      <el-pagination v-if="merchantObj" ref="pagination" :total="merchantObj.totalRecord"
                     :current-page.sync="pageCurrent" :page-sizes="[10, 50]" :page-size="pageSize"
                     layout="total, sizes, prev, pager, next, jumper" background @size-change="handleSizeChange"
                     @current-change="handleCurrentChange">
      </el-pagination>
    </el-footer>

    <!--导出dialog-->
    <export-select ref="exportSelect" dict="ExportEmployerInfo" @confirm="submitExportForm"></export-select>
    <export-record ref="exportRecord"></export-record>
  </div>
</template>

<script>
import {
  getMerchantList,
  createSignInfo,
  exportMchList,
  hardDelete,
  getMainstayList,
  activateMch
} from '@/api/merchant'
import { getSales, getDepartmentList } from '@/api/system'
import ExportSelect from '@/components/ExportSelect'
import ExportRecord from '@/components/ExportRecord/index'
import { toPromise } from '@/utils'

export default {
  name: 'MerchantManage',
  components: {
    ExportSelect,
    ExportRecord,
  },
  data() {
    return {
      form: {
        mchNo: '',
        mchNameLike: '',
        merchantType: '100',
        timeRange: [],
        mchStatus: '',
        salerId: '',
        agentNo: '',
        remark: '',
        branchName: '',
        departmentId: '',
      },
      pageCurrent: 1,
      pageSize: 10,
      merchantObj: {},

      staffList: [],
      mainstayList: [],

      defaultActive: '100',

      departmentList: [],
    }
  },
  computed: {
    createBtn() {
      return this.form.merchantType == 100 ? '创建商户' : '创建代征主体';
    },
    hasBtnPermission() {
      if (this.form.merchantType == 100) {
        return this.hadPermission('merchant:employer:submit')
      } else if (this.form.merchantType == 101) {
        return this.hadPermission('merchantMainstay:create')
      }
      return false
    }
  },
  mounted() {
    this.getMainstayList();
    this.getMerchantList();
    this.getSales();

    getDepartmentList().then(res => this.departmentList = res.data)
  },
  methods: {
    async getMainstayList() {
      const { data } = await getMainstayList();
      this.mainstayList = data;
    },
    createMerchant() {
      sessionStorage.removeItem('mainInfo');
      sessionStorage.removeItem('bankInfo');
      sessionStorage.removeItem('merchantInfo');
      sessionStorage.removeItem('cooperationInfo');
      if (this.form.merchantType == 100) {
        this.$router.push('/merchant/merchantManagement/createMerchant');
      } else if (this.form.merchantType == 101) {
        this.$router.push('/merchant/merchantManagement/createSupplier');
      }
    },
    getMerchantList(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      const form = { ...this.form };
      if (form.mchStatus === '0') {
        form.mchStatus = '';
      }
      getMerchantList({
        ...form,
        createTimeBegin: this.form.timeRange[0],
        createTimeEnd: this.form.timeRange[1],
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent
      }).then(response => {
        this.merchantObj = response.data;
      })
    },
    getTimeRange(val) {
      this.form.timeRange = val;
    },
    resetForm() {
      this.form = {
        merchantType: this.form.merchantType
      }
      this.$refs.datePicker.clearTime();
    },
    changTab() {
      this.pageSize = 10;
      this.pageCurrent = 1;
      this.getMerchantList();
    },
    handleCurrentChange(val) {
      this.form.pageNum = val;
      this.getMerchantList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getMerchantList();
    },
    goMerchantDetail(data, type, index) {
      sessionStorage.setItem('merchantIndex', index);
      sessionStorage.setItem('merchantList', JSON.stringify(this.merchantObj.data));
      this.$router.push({
        path: `/merchant/merchantInfo/${data.mchNo}`,
        query: {
          actionType: type,
        }
      });
    },
    goAuth(data) {
      sessionStorage.setItem('merchantInfo', JSON.stringify(data));
      this.$router.push('/merchant/merchantAuth?mchNo=' + data.mchNo);
    },
    async getSales() {
      const { data } = await getSales();
      this.staffList = data;
    },
    async createSignInfo(row) {
      const { data } = await createSignInfo({
        mainstayNo: row.mchNo
      });
      data && this.$message.success(data);
    },
    handleTabChange(index) {
      this.form.merchantType = index;
      this.getMerchantList(true);
    },
    handleStatusChange() {
      this.getMerchantList(true);
    },
    async submitExportForm(form) {
      let paramMap = {
        ...this.form,
        createTimeBegin: this.form.timeRange[0],
        createTimeEnd: this.form.timeRange[1],
      };

      if (paramMap.mchStatus == 0) {
        paramMap.mchStatus = '';
      }

      for (let p in paramMap) {
        if (!paramMap[p]) {
          delete paramMap[p]
        }
      }

      // 标准报表，传所有字段，自定义报表，将下标转换成数据
      await exportMchList({
        fieldListDataName: 'ExportEmployerInfo',
        fieldInfoList: form.fieldInfoList,
        paramMap,
      });
      this.$message.success('导出成功，请到导出列表进行导出');
    },
    exportMchList() {
      this.$refs.exportSelect.visible = true;
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('29');
    },
    onClickMch(mchNo) {
      this.$store.dispatch('openMerchantPop', {
        mchNo,
        list: this.merchantObj.data
      })
    },
    async hardDelete(row) {
      const [err] = await toPromise(this.$confirm('此操作将删除该商户所有数据，是否继续', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }))
      if (err) return;
      const { data } = await hardDelete(row.mchNo);
      data && this.$message.success(data);
      this.getMerchantList();
    },
    async activateMch(row) {
      const [err] = await toPromise(this.$confirm('是否确认激活商户', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }))
      if (err) return;
      const { data } = await activateMch(row)
      data && this.$message.success(data)
      this.getMerchantList()
    },
    getTagType({ mchStatus }) {
      switch (mchStatus) {
        case 100:
          return 'success'
        case 101:
          return 'info'
        case 102:
        default:
          return
      }
    }
  }
}
</script>

<style
  scoped
  lang="scss"
>
.box-container {
  .el-radio-group {
    margin-left: 16px;
  }
}
</style>
