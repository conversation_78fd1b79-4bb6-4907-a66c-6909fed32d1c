<template>
  <div class="page-container">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="140px"
    >
      <p class="subTitle">
        基本信息
      </p>
      <el-form-item
        label="协议名称"
        prop="topic"
      >
        <el-input
          v-model="form.topic"
          :disabled="isDisabled"
          placeholder="自定义协议文件名称，例如：客户A-代征主体A协议0924"
        />
      </el-form-item>
      <el-form-item
        label="负责销售"
        prop="salerId"
      >
        <el-select
          clearable
          v-model="salerInfo"
          @change="selectSaler"
          :disabled="saleDisable"
        >
          <el-option
            v-for="item in staffList"
            :label="item.realName"
            :value="[item.id, item.realName]"
            :key="item.name"
          ></el-option>
        </el-select>
        <el-button
          type="text"
          v-if="hadPermission('merchant:agreement:changesale')"
          @click="saleDisable = false">更改销售
        </el-button>
      </el-form-item>
      <p class="subTitle">
        上传文件
      </p>
      <el-form-item
        label="协议文件"
        prop="protoList"
      >
        <el-upload
          class="upload-demo"
          :action="uploadApi"
          :headers="uploadHeader"
          accept=".doc,.docx,.pdf"
          :file-list="protoList"
          list-type="picture-card"
          :before-upload="validateUploadFile({
            img: false,
            pdf: true,
            word: true,
            size: 15
          })"
          :on-remove="handleProtoRemove"
          :on-success="handleProtoSuccess"
          :disabled="isDisabled"
          ref="uploadProto"
        >
          <template v-slot:file="{file}">
            <div class="fileBg">
              <span class="file-name">{{ file.name || file.fileName || '协议文件' }}</span>
              <span
                class="el-upload-list__item-actions"
                v-if="!isDisabled"
              >
                <span
                  class="el-upload-list__item-preview"
                  @click="handleFilePreview(file)"
                >
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span
                  v-if="!templateId"
                  class="el-upload-list__item-delete"
                  @click="handleFileRemove('proto', file)"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
          </template>
          <i class="el-icon-plus"></i>
          <div
            v-if="!templateId"
            slot="tip"
            class="el-upload__tip"
          >
            如使用标准模板修改协议，请上传修订模式的协议，以便审核人了解修改点<br>只能上传pdf/doc/docx文件，且不超过15M
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item
        label="归档文件"
        prop="finishList"
        v-show="finishList.length > 0"
      >
        <el-upload
          class="upload-demo"
          :action="baseUrl + '/file/upload'"
          :headers="uploadHeader"
          accept=".doc,.docx,.pdf"
          :file-list="finishList"
          list-type="picture-card"
          :before-upload="validateUploadFile({
            img: false,
            pdf: true,
            word: true,
            size: 15
          })"
          :on-remove="handleFinishRemove"
          :on-success="handleFinishSuccess"
          :disabled="isDisabled"
          ref="uploadFinish"
        >
          <template v-slot="{file}">
            <div
              slot="file"
              class="fileBg"
            >
              <span class="file-name">{{ file.name || file.fileName || '归档文件' }}</span>
              <span
                class="el-upload-list__item-actions"
                v-if="!isDisabled"
              >
                <span
                  class="el-upload-list__item-delete"
                  @click="handleFileRemove('finish', file)"
                >
                  <i class="el-icon-delete"></i>
                </span>
              </span>
            </div>
          </template>
          <i class="el-icon-plus"></i>
          <div
            v-if="!templateId"
            slot="tip"
            class="el-upload__tip"
          >
            如使用标准模板修改协议，请上传修订模式的协议，以便审核人了解修改点<br>只能上传doc/docx文件，且不超过15M
          </div>
        </el-upload>
      </el-form-item>
      <p class="subTitle">
        签署方
      </p>
      <el-form-item
        label-width="0"
        prop="signMode">
        <el-radio-group
          v-model="form.signMode"
          style="margin: 16px">
          <el-radio
            v-for="item in $dict('AgreementSignModeEnum')"
            :key="item.code"
            :label="item.code"
            border>{{ item.desc }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-show="form.signMode == 100"
        label="请选择签署方："
        prop="singleSignerType">
        <el-select
          v-model="form.singleSignerType"
          clearable>
          <el-option
            v-for="item in $dict('AgreementSignerTypeEnum')"
            :key="item.key"
            :value="item.code"
            :label="item.desc"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-table :data="form.signerVoList">
        <el-table-column
          prop="signerType"
          width="100"
        >
          <template v-slot="{row, column}">
            {{ $dictCode('AgreementSignerTypeEnum', row[column.property]).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="签署主体"
          prop="signerName"
        >
          <template v-slot="{row, $index}">
            <el-select
              clearable
              filterable
              v-if="row.signerType == 100"
              v-model="row.signerMchName"
              placeholder="选择商户"
              :disabled="!!agreementId"
              @change="selectSinger($event, $index)"
            >
              <el-option
                v-for="item in activeMchList"
                :key="item.mchNo"
                :value="[item.mchNo, item.mchName, item.contactName, item.contactPhone]"
                :label="item.mchName + '-' + item.mchNo"
              ></el-option>
            </el-select>
            <el-select
              clearable
              filterable
              v-if="row.signerType == 101"
              v-model="row.signerMchName"
              placeholder="选择供应商"
              :disabled="!!agreementId"
              @change="selectSinger($event, $index)"
            >
              <el-option
                v-for="item in mainstayList"
                :key="item.mchNo"
                :value="[item.mchNo, item.mchName, item.contactName, item.contactPhone]"
                :label="item.mchName + '-' + item.mchNo"
              ></el-option>
            </el-select>

            <span
              v-if="$dictCode('AgreementSignerTypeEnum', row.signerType).desc == '固定成员'"
            >{{ row.signerName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="协议负责人"
          prop="signerName"
        >
          <template v-slot="{row}">
            <el-input
              v-model="row.signerName"
              clearable
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column
          label="负责人手机号"
          prop="signerPhone"
        >
          <template v-slot="{row}">
            <el-input
              v-model="row.signerPhone"
              clearable
            ></el-input>
          </template>
        </el-table-column>
        <!--        <el-table-column label="操作" prop="data" width="100px">-->
        <!--          <template v-slot="scope">-->
        <!--            <el-button-->
        <!--              v-if="!agreementId" type="text" @click="deleteRow(scope.row, scope.$index)" :disabled="templateId == 0"-->
        <!--            >删除-->
        <!--            </el-button>-->
        <!--          </template>-->
        <!--        </el-table-column>-->
      </el-table>
      <!--      <el-button v-if="!templateId && !agreementId" class="add-btn" @click="addList">添加签署人</el-button>-->
      <p class="subTitle">
        其他
      </p>
      <el-form-item
        label="签署默认截止日期"
        prop="deadline"
      >
        <el-date-picker
          type="datetime"
          v-model="form.deadline"
          value-format="yyyy-MM-dd HH:mm:ss"
          default-time="23:59:59"
        />
        <p class="color-gray">须在截止日期前完成签署归档</p>
      </el-form-item>
      <el-form-item
        label="协议默认到期日期"
        prop="expireTime"
      >
        <el-date-picker
          type="datetime"
          v-model="form.expireTime"
          value-format="yyyy-MM-dd HH:mm:ss"
          default-time="23:59:59"
        />
        <p class="color-gray">
          协议到期前，系统将提醒
        </p>
      </el-form-item>
    </el-form>
    <div class="form-btn-group">
      <el-button
        :loading="loading"
        type="primary"
        @click="doSubmit"
      >发起
      </el-button>
      <el-button @click="back">取消</el-button>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs';
import { getSales } from '@/api/system'
import { createAgreement, editAgreement, getAgreementById, getAgreementTemplateById } from '@/api/agreement'
import { getAllMerchant, getMainstayList } from '@/api/merchant'
import { toPromise } from "@/utils";

const SIGNER = { // 签署人信息
  signerNo: '',
  signerName: ''
}

/**
 * [beforeunloadHandler 浏览器关闭时进行用户提示]
 * @return {[type]} [description]
 */
 function beforeunloadHandler(e) {
    e = e || window.event;
    if (e) {
      e.preventDefault();
      e.returnValue = "系统可能不会保存您做的的更改！";
    }
    return "系统可能不会保存您做的的更改！";
}

export default {
  data(vm) {
    return {
      loading: false,
      show: false,
      actionType: undefined,
      staffList: [],
      activeMchList: [],
      mainstayList: [],
      fileList: [],
      finishList: [], // 归档文件
      protoList: [], //协议文件
      salerInfo: '',
      signerInfo: '',
      form: {
        topic: '共享经济综合服务合同',
        salerId: '',
        salerName: '',
        expireTime: dayjs().add(1, 'year').endOf('date').format('YYYY-MM-DD hh:mm:ss'),
        deadline: dayjs().add(14, 'day').endOf('date').format('YYYY-MM-DD hh:mm:ss'),
        fileVoList: [],
        signerVoList: [],
        signType: '',
        signMode: '',
        singleSignerType: ''
      },
      rules: {
        topic: [{
          required: true,
          message: '请输入协议名称',
          trigger: 'blur'
        }],
        salerId: [{
          required: true,
          message: '请选择销售',
          trigger: 'change'
        }],
        deadline: [{
          required: true,
          message: "请选择签署默认截止日期",
          trigger: 'blur'
        }],
        expireTime: [{
          required: true,
          message: "请选择协议默认到期日期",
          trigger: 'blur'
        }],
        signMode: [{
          required: true,
          message: "请选择签署模式",
          trigger: 'blur'
        }],
        singleSignerType: [{
          validator(rule, val, cb) {
            if (vm.form.signMode == 100) {
              if (!val) {
                cb(new Error('请选择签署方'))
              } else {
                cb()
              }
            } else {
              cb()
            }
          },
          trigger: 'blur'
        }]
      },
      saleDisable: true,
    }
  },

  computed: {
    agreementId() {
      return this.$route.query.agreementId;
    },
    templateId() {
      return this.$route.query.templateId;
    },
    isDisabled() {
      // return !!(this.agreementId || this.templateId)
      return !!this.agreementId
    },
    signType() {
      return this.$route.query.signType;
    },
    uploadApi() {
      if (this.signType == 100 && !this.templateId) {
        return this.baseUrl + '/agreement/upload'
      } else {
        return this.baseUrl + '/file/upload'
      }
    },
  },
  async mounted() {
    window.addEventListener("beforeunload", beforeunloadHandler)
    await Promise.all([
      this.getMchAndMainstay(),
      this.init(),
      this.getSales(),
    ])
    if (!this.form.salerId) {
      this.selectSaler([
        this.$store.state.user.userData.id,
        this.$store.state.user.userData.realName,
      ])
      this.salerInfo = this.$store.state.user.userData.realName;
    }

    this.handleSessionInfo()
  },
  beforeDestroy() {
    window.removeEventListener("beforeunload", beforeunloadHandler)
  },
  methods: {
    /**
     * [beforeunloadHandler 浏览器关闭时进行用户提示]
     * @return {[type]} [description]
     */
     beforeunloadHandler(e) {
      e = e || window.event;
      if (e) {
        e.returnValue = "系统可能不会保存您做的的更改！";
      }
      return "系统可能不会保存您做的的更改！";
    },
    async init() {
      this.form.signType = this.signType || ''
      if (this.agreementId) {
        let response = await getAgreementById({
          id: this.agreementId
        })
        this.templateObj = response.data;
        this.form.topic = response.data.topic;
        this.form.salerId = response.data.salerId;
        this.form.salerName = response.data.salerName;
        this.salerInfo = response.data.salerName;
        this.form.expireTime = response.data.expireTime;
        this.form.deadline = response.data.deadline;
        this.form.fileVoList = response.data.templateFileUrl;
        this.form.signerVoList = response.data.signerList;
        this.fileList = response.data.fileList;
        this.protoList = this.fileList.filter(item => item.type == 100);
        this.finishList = this.fileList.filter(item => item.type == 101);
      }
      if (this.templateId) {
        let response = await getAgreementTemplateById({
          id: this.templateId
        })
        this.templateObj = response.data;
        this.form.topic = response.data.topic;
        this.form.salerId = response.data.salerId;
        this.form.salerName = response.data.salerName;
        this.form.fileTemplateId = response.data.fileTemplateId;
        if (this.$dictCode('AgreementTempTimeTypeEnum', response.data.expireTimeType).desc == '自发起日期一个月内') {
          this.form.expireTime = dayjs().add(1, 'month').format('YYYY-MM-DD hh:mm:ss');
        } else if (this.$dictCode('AgreementTempTimeTypeEnum', response.data.expireTimeType).desc == '一年') {
          this.form.expireTime = dayjs().add(1, 'year').format('YYYY-MM-DD hh:mm:ss');
        }
        if (this.$dictCode('AgreementTempTimeTypeEnum', response.data.deadlineType).desc == '自发起日期一个月内') {
          this.form.deadline = dayjs().add(1, 'month').format('YYYY-MM-DD hh:mm:ss');
        } else if (this.$dictCode('AgreementTempTimeTypeEnum', response.data.deadlineType).desc == '一年') {
          this.form.deadline = dayjs().add(1, 'year').format('YYYY-MM-DD hh:mm:ss');
        }
        // this.form.fileVoList = response.data.fileVoList;
        // this.fileList = response.data.fileVoList;
        this.protoList = [{
          fileName: this.form.topic,
          fileUrl: response.data.templateFileUrl
        }];
        this.form.signerVoList = [
          { signerType: 100 },
          { signerType: 101 },
        ]
      }

      if (!this.templateId) {
        this.form.signerVoList = [
          { signerType: 100 },
          { signerType: 101 },
        ]
      }

      if (this.agreementId || this.templateId) {
        let node = this.$refs.uploadFinish.$el.querySelector('.el-upload');
        node.parentNode.removeChild(node);
        node = this.$refs.uploadProto.$el.querySelector('.el-upload');
        node.parentNode.removeChild(node);
      }
    },
    getMchAndMainstay() {
      getAllMerchant({ merchantType: 100 }).then(response => {
        this.activeMchList = response.data;
      })
      getMainstayList().then(({ data }) => {
        this.mainstayList = data
      })
    },
    async getSales() {
      const { data } = await getSales()
      this.staffList = data;
    },
    selectSaler(val) {
      this.form.salerId = val[0];
      this.form.salerName = val[1];
    },
    selectSinger(val, index) {
      const attrs = ['signerNo', 'signerMchName', 'signerName', 'signerPhone']
      for (let i = 0; i < val.length; i++) {
        this.$set(this.form.signerVoList[index], attrs[i], val[i])
      }
    },
    handleProtoRemove(file, fileList) {
      this.protoList = fileList;
      this.loading = false;
    },
    handleProtoSuccess(response, file, fileList) {
      this.protoList = fileList;
      this.loading = false;
    },
    handleFinishSuccess(response, file, fileList) {
      this.finishList = fileList;
      this.loading = false;
    },
    handleFinishRemove(file, fileList) {
      this.finishList = fileList;
      this.loading = false;
    },
    // 增加一行签署人
    addList() {
      this.form.signerVoList.push({ ...SIGNER });
    },
    deleteRow(row, index) {
      this.form.signerVoList.splice(index, 1);
    },
    async doSubmit() {
      const [err] = await toPromise(this.$refs.form.validate())
      if (err) return
      if (!this.form.signerVoList || this.form.signerVoList.length === 0) {
        this.$message.error('签署人不能为空！')
      } else {
        this.form.fileVoList = [];
        this.fileList = [...this.protoList, ...this.finishList];
        for (let item of this.fileList) {
          // 判断该文件是重新上传的还是原本有的
          if (item.response) {
            this.form.fileVoList.push({
              fileName: item.name,
              fileUrl: item.response.data
            })
          } else {
            this.form.fileVoList.push({
              fileName: item.fileName,
              fileUrl: item.fileUrl
            })
          }
        }
        this.agreementId ? this.doEdit() : this.doAdd();
      }
    },
    async doAdd() {
      if (this.signType == 101 || this.templateId) {
        await createAgreement(this.form)
        this.$message.success('创建成功');
        this.$router.push('/merchant/merchantManagement/agreement');
      } else if (this.signType == 100) {
        if (!this.form.fileVoList.length) {
          return this.$message.error('请上传文件')
        }
        sessionStorage.setItem('agreementInfo', JSON.stringify(this.form))
        this.$router.push('/merchant/dragAgreement');
      }
    },
    doEdit() {
      editAgreement({
        agreementId: this.agreementId,
        ...this.form
      }).then((response) => {
        this.$message.success('修改成功');
        this.$router.push('/merchant/merchantManagement/agreement');
      })
    },
    handleFileRemove(type, file) {
      if (this.isDisabled) return;
      this[type + 'List'] = this[type + 'List'].filter(item => {
        return item.name !== file.name
      })
      this.loading = false
    },
    async handleFilePreview(file) {
      const url = file.response ? file.response.data : file.origin
      const fileMsg = await this.formatFileMsg(url)
      const suffix = url.split('.').pop()
      this.downloadFile(fileMsg.fileUrl, this.form.topic, suffix);
    },
    handleSessionInfo() {
      let agreementInfo = sessionStorage.getItem('agreementInfo')
      if (agreementInfo) {
        agreementInfo = JSON.parse(agreementInfo)
        // 还原数据
        this.form = agreementInfo
        this.protoList = [{
          fileName: agreementInfo.fileVoList[0].fileName,
          fileUrl: agreementInfo.fileVoList[0].fileUrl,
        }]
      }
    },
    back() {
      this.$router.push('/merchant/merchantManagement/agreement')
      this.$nextTick(() => {
        sessionStorage.removeItem('agreementInfo')
      })
    }
  }
}
</script>

<style
  lang="scss"
  scoped
>
.page-container {

  .el-form {
    margin: 0 auto;
  }

  .add-btn {
    display: block;
    box-sizing: border-box;
    width: 100%;
    padding: 20px;
    border: 1px dotted #ccc;
    background: #fff;
    cursor: pointer;

    &:active {
      border: 1px dotted #ccc;
    }

    &:focus {
      border: 1px dotted #ccc;
      outline: none;
    }
  }
}
</style>
