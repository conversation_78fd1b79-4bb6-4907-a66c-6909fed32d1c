<template>
  <div class="merchant-page-container">
    <div
      class="header-container"
      v-sticky="{ zIndex: 100, stickyTop: 50 }">
      <el-steps
        v-if="!isEdit"
        :active="activeStep"
        align-center
        class="el-steps-container">
        <el-step title="填写合作信息"></el-step>
        <el-step title="审核与公示"></el-step>
      </el-steps>
      <el-steps
        v-else
        :active="activeStep"
        align-center>
        <el-step title="填写合作信息"></el-step>
      </el-steps>
    </div>
    <div class="page-container">
      <div class="content-container">
        <!--step_1-->
        <el-form
          ref="form_1"
          :model="form_1"
          :rules="rules"
          label-width="400px"
          class="step_1"
          v-show="activeStep == 1">
          <!--企业信息-->
          <div class="company_info">
            <p class="subTitle">企业信息</p>
            <div>
              <el-form-item
                label="企业名称："
                prop="mchName">
                <el-input
                  clearable
                  v-model="form_1.mchName"></el-input>
              </el-form-item>
              <el-form-item
                label="销售："
                prop="salerId">
                <el-select
                  v-model="form_1.salerId"
                  @change="handleSalerChange"
                  :disabled="!hadPermission('pms:crm:role')">
                  <el-option
                    v-for="item in staffList"
                    :key="item.id"
                    :label="item.realName"
                    :value="item.id"></el-option>
                </el-select>
                <el-button
                  type="primary"
                  @click="generateMobileUrl">生成创建商户链接
                </el-button>
                <el-button
                  v-if="mobileUrl"
                  ref="copy"
                  type="text"
                  v-clipboard="mobileUrl">点击复制
                </el-button>
              </el-form-item>
              <el-form-item
                prop="agentNo"
                label="合伙人：">
                <el-select
                  clearable
                  filterable
                  v-model="form_1.agentNo"
                  :disabled="!form_1.salerId || platform == 1003"
                  @change="handleAgentChange"
                >
                  <el-option
                    v-for="item in agentList"
                    :key="item.agentNo"
                    :label="item.agentName"
                    :value="item.agentNo"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="商户负责人姓名："
                prop="contactName">
                <el-input
                  clearable
                  v-model="form_1.contactName"
                  :disabled="!editAccess"
                  maxlength="10"></el-input>
                <p class="color-gray form-tip">请填写公司运营负责人</p>
              </el-form-item>
              <el-form-item
                label="商户负责人邮箱："
                prop="contactEmail">
                <el-input
                  clearable
                  v-model="form_1.contactEmail"
                  :disabled="!editAccess"></el-input>
              </el-form-item>
              <el-form-item
                label="商户负责人手机号："
                prop="contactPhone">
                <el-input
                  clearable
                  v-model="form_1.contactPhone"
                  :disabled="!editAccess">
                  <template slot="prepend">中国+86</template>
                </el-input>
                <p class="color-gray form-tip">用于商户的后台登录、接收日常经营提醒及相关操作验证</p>
                <div v-if="actionType !== 'ADD'">
                  <el-button
                    type="text"
                    @click="() => {editAccess = true}">
                    更改
                  </el-button>
                </div>
              </el-form-item>

              <el-form-item
                label="品牌名称："
                prop="branchName">
                <el-input
                  clearable
                  v-model="form_1.branchName"></el-input>
              </el-form-item>
            </div>
          </div>
          <!--岗位信息-->
          <div class="job_info">
            <p class="subTitle">岗位信息
              <el-button
                type="text"
                @click="addJob">增加岗位
              </el-button>
            </p>
            <div
              class="job-box"
              v-for="(item, index) in form_1.positionVoList"
              :key="index">
              <el-divider v-if="index == 1"></el-divider>
              <el-form-item
                label="自由职业者的工作场所："
                :prop="'positionVoList[' + index + '].workplaceCode'"
                :rules="rules.positionVoList['workplaceCode']">
                <el-select
                  clearable
                  v-model="item.workplaceCode">
                  <el-option
                    v-for="item in $dict('WorkPlaceEnum')"
                    :key="item.code"
                    :value="item.code"
                    :label="item.desc"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="自由职业者服务类型："
                :prop="'positionVoList[' + index + '].workCategoryCode'"
                :rules="rules.positionVoList['workCategoryCode']">
                <el-cascader
                  filterable
                  clearable
                  data-index="index"
                  v-model="item.workCategoryCode"
                  :options="workCategoryOptions"
                  :props="{ expandTrigger: 'hover', 'emitPath': false, 'value': 'workCategoryCode', 'label': 'workCategoryName', 'leaf': 'workCategoryCode' }"
                  @change="changeWorkCategory($event, index)">
                  <div slot-scope="{data}">
                    <el-tooltip
                      :content="data.workCategoryName"
                      :disabled="data.workCategoryName.length < 14">
                      <span class="cascader-content-item">
                        {{ data.workCategoryName }}
                      </span>
                    </el-tooltip>
                  </div>
                </el-cascader>
              </el-form-item>
              <el-form-item
                label="发票类目："
                :prop="'positionVoList[' + index + '].invoiceCategoryList'"
                :rules="rules.positionVoList['invoiceCategoryList']">
                <el-select
                  v-model="item.invoiceCategoryList"
                  multiple
                  clearable
                  value-key="invoiceCategoryCode"
                  filterable
                >
                  <el-option
                    v-for="(cate, key) in item._invoiceCateList"
                    :key="key"
                    :label="cate.invoiceCategoryName"
                    :value="cate"
                  ></el-option>
                </el-select>
                <span
                  class="func-content"
                  @click="changeCustomInvoice(item)">自定义选择</span>
                <p class="color-gray form-tip">平台根据贵司业务推荐开票类目，如您有其他选项需求请联系客户经理</p>
              </el-form-item>
              <el-form-item
                label="企业从事业务："
                :prop="'positionVoList[' + index + '].businessDesc'">
                <el-input
                  type="textarea"
                  v-model="item.businessDesc"
                  :autosize="{ minRows: 5, maxRows: 5}"
                  placeholder="请描述用工企业具体从事业务">
                </el-input>
                <p class="form-tip">如有需要请
                  <span
                    class="func-content"
                    @click="usePositionRef(index)">点击使用</span>服务描述模板
                </p>
              </el-form-item>
              <el-form-item
                label="自由职业者服务描述："
                :prop="'positionVoList[' + index + '].serviceDesc'"
                :rules="rules.positionVoList['serviceDesc']">
                <el-input
                  type="textarea"
                  v-model="item.serviceDesc"
                  :autosize="{ minRows: 5, maxRows: 5}"
                  placeholder="为使落地供应商匹配到合适的自由职业者，请按实际描述个人具体需要做什么">
                </el-input>
              </el-form-item>
              <el-form-item
                label="自由职业者服务所得计算规则："
                :prop="'positionVoList[' + index + '].chargeRuleDesc'"
                :rules="rules.positionVoList['chargeRuleDesc']">
                <el-input
                  type="textarea"
                  v-model="item.chargeRuleDesc"
                  :autosize="{ minRows: 5, maxRows: 5}"
                  placeholder="为吸引合适的自由职业者进行匹配，请按实际填写个人完成服务后钱怎么算；如规则的数据范围有差异，请列明确定不同规则的依据"></el-input>
              </el-form-item>
              <div class="job-divider">
                <el-divider v-if="index > 0"></el-divider>
                <el-button
                  v-if="index > 0"
                  type="text"
                  @click="deleteJob(index)">删除
                </el-button>
              </div>
            </div>
          </div>
          <!--合作信息-->
          <div class="cooperation_info">
            <p class="subTitle">合作信息</p>
            <div>
              <el-form-item
                label="企业行业类别："
                prop="industryTypeCode">
                <el-cascader
                  v-model="form_1.industryTypeCode"
                  :options="industryListOptions"
                  :props="{ expandTrigger: 'hover', 'emitPath': false, 'value': 'industryTypeCode', 'label': 'industryTypeName'}"
                  @change="selectIndustry"
                ></el-cascader>
                <div><p class="color-gray form-tip">请正确选择企业行业类别，若类目选择错误可能导致审核驳回</p></div>
              </el-form-item>
              
              <el-form-item label="更多合作信息：">
                <el-switch v-model="showMoreCooperateInfo" active-text="开" inactive-text="关"></el-switch>
                <p class="color-gray form-tip">点击打开才会展开下列菜单</p>
              </el-form-item>
              
              <div v-if="showMoreCooperateInfo">
                <el-form-item
                  label="预计用工人数："
                  prop="workerNum">
                  <el-input
                    clearable
                    v-model="form_1.workerNum"></el-input>
                </el-form-item>
                <el-form-item
                  label="预期可实现C端签署率区间："
                  prop="signRateLevel">
                  <el-select
                    clearable
                    v-model="form_1.signRateLevel">
                    <el-option
                      v-for="item in $dict('SignRateLevelEnum')"
                      :key="item.code"
                      :value="Number(item.code)"
                      :label="item.desc"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="自由职业者单人月经营所得9.7万以下百分比："
                  prop="workerMonthIncomeRate">
                  <el-input
                    clearable
                    @input="handleRateInput(form_1, 'workerMonthIncomeRate', $event, 0, 100)"
                    :value="form_1.workerMonthIncomeRate"></el-input>
                </el-form-item>
              </div>
              
              <el-form-item
                label="月流水预估："
                prop="monthMoneySlip">
                <el-input
                  clearable
                  v-model="form_1.monthMoneySlip">
                  <template slot="append">万元</template>
                </el-input>
                <p class="color-gray form-tip">全月自由职业者经营所得预估</p>
              </el-form-item>
              <el-form-item
                label="是否可提供服务记录及经营所得计算明细："
                prop="provideIncomeDetailType">
                <el-radio-group v-model="form_1.provideIncomeDetailType">
                  <el-radio
                    v-for="item in $dict('ProvideIncomeDetailTypeEnum')"
                    :key="item.code"
                    :label="Number(item.code)">{{ item.desc }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                label="公司网站："
                prop="companyWebsite">
                <el-input
                  clearable
                  v-model="form_1.companyWebsite"></el-input>
              </el-form-item>
              <el-form-item
                label="公司自有业务平台名称："
                prop="bizPlatformName">
                <el-input
                  clearable
                  v-model="form_1.bizPlatformName"></el-input>
                <p class="color-gray form-tip">如微信公众号名、小程序名、APP名等</p>
              </el-form-item>
              <el-form-item label="公司对外宣传资料：">
                <el-upload
                  class="upload-demo"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  accept=".doc,.docx,.pdf,.ppt,.pptx,.txt,.wps,.bpm,.png,.jpg,.jpeg,.gif"
                  :file-list="companyLeafletFileUrls"
                  :before-upload="beforeFileUpload"
                  :on-remove="handleRemove('companyLeafletFileUrls')"
                  :on-success="handleSuccess('companyLeafletFileUrls')"
                >
                  <el-button
                    type="primary"
                    size="small">点击上传文件
                  </el-button>
                  <div
                    slot="tip"
                    class="el-upload__tip">
                    如产品服务宣传册，业务介绍PPT等<br>
                    文件大小需小于10M，格式为word、pdf、ppt、txt、wps、bmp、png、jpeg、jpg或gif
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item label="补充信息：">
                <el-upload
                  v-toggle:8="form_1.supplementFileUrls"
                  class="upload-demo"
                  :action="baseUrl + '/file/upload'"
                  :headers="uploadHeader"
                  list-type="picture-card"
                  accept=".doc,.docx,.pdf,.ppt,.pptx,.txt,.wps,.bpm,.png,.jpg,.jpeg,.gif"
                  :limit="8"
                  :file-list="supplementFileUrls"
                  :before-upload="beforeFileUpload"
                  :on-remove="handleRemove('supplementFileUrls')"
                  :on-success="handleSuccess('supplementFileUrls')"
                  :on-exceed="handleExceed"
                  :on-preview="handlePreview"
                >
                  <template v-slot:file="{file}">
                    <div
                      class="fileBg"
                      v-if="!isImg(file.name)">
                      <span class="file-name">{{ file.name || '补充信息文件' }}</span>
                      <span class="el-upload-list__item-actions">
                         <span
                           class="el-upload-list__item-preview"
                           @click="handlePreview(file)"
                         >
                          <i class="el-icon-zoom-in"></i>
                        </span>
                        <span
                          class="el-upload-list__item-delete"
                          @click="handleFileRemove(file, 'supplementFileUrls')"
                        >
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                  </template>
                  <div
                    slot="tip"
                    class="el-upload__tip">
                    其他可佐证业务场景真实性的资料、照片或截图，包括但不限于
                    <span class="upload-tip__weight">公司自有业务平台（微信公众号关注数/小程序用户数/app下载量等）的截图、自由职业者用工场景照片等。</span>
                    <br>
                    必须为彩色图片（文档请截图后上传），最多8张，单张照片不超过6M。格式为word、pdf、ppt、txt、wps、bmp、png、jpeg、jpg或gif。
                  </div>
                  <i class="el-icon-plus avatar-uploader-icon"></i>
                </el-upload>
              </el-form-item>
            </div>
          </div>

          <div class="other">
            <p class="subTitle">其他</p>
            <el-form-item
              label="备注："
              prop="remark">
              <el-input
                type="textarea"
                v-model="form_1.remark"
                :rows="5"
              ></el-input>
            </el-form-item>
          </div>
        </el-form>

        <el-footer
          class="form-btn-group"
          v-show="activeStep == 1">
          <el-button
            type="primary"
            @click="nextStep"
            :disabled="loading">{{ activeStep == 1 ? '下一步' : '提交' }}
          </el-button>
          <el-button @click="backStep">{{ activeStep == 1 ? '取消' : '上一步' }}</el-button>
        </el-footer>

        <!--step_3-->
        <div
          class="step_3"
          v-show="activeStep == 2">
          <img src="@/assets/success.png">
          <p class="result-title">提交成功</p>
          <p class="result-tip">商户新增成功</p>
          <!--<div class="result-detail">-->
          <!--<p class="result-detail-title">审批流程</p>-->
          <!--<p class="result-wrapper">-->
          <!--<span class="result-item">流程流水号：{{ result.commonFlowId }}</span>-->
          <!--<span class="result-item">发起人：{{ result.submitName }}</span>-->
          <!--<span class="result-item">提交时间：{{ result.createTime }}</span>-->
          <!--</p>-->
          <!--</div>-->
          <div class="footer-container">
            <el-button
              type="primary"
              @click="backStep">返回商户列表
            </el-button>
            <!--<el-button @click="goDetailProcess">查看审批流程</el-button>-->
          </div>
        </div>
      </div>

      <!--<special-fee-rule-->
      <!--:info="editRow"-->
      <!--:vendorList="mainstayList"-->
      <!--:productList="productList"-->
      <!--:visible.sync="editFeeVisible"-->
      <!--@confirm="confirmEdit"-->
      <!--&gt;</special-fee-rule>-->

    </div>
    <!--审批意见-->
    <flow-opinion
      :visible.sync="flowVisible"
      @change="flowConfirm"
    ></flow-opinion>
  </div>
</template>

<script>
import '@/styles/merchant-form.scss'
import VueSticky from 'vue-sticky'
import { createMerchant, updateCooperate, changeLeader, generateCreateURL } from '@/api/merchant'
import { listAllIndustryType, listAllWorkCategory } from '@/api/business'
import { convert, clearVoid } from '@/utils'
import { getSales } from '@/api/system'
import { validateParams } from '@/utils/validate'
import { getAllAgentSimple } from '@/api/agent'
import { editBusinessData } from '@/api/flow';
import { getAllInvoiceCategory } from '@/api/invoice'
import FlowOpinion from '@/components/Flow/FlowOpinion'

/**
 * [beforeunloadHandler 浏览器关闭时进行用户提示]
 * @return {[type]} [description]
 */
  function beforeunloadHandler(e) {
    e = e || window.event;
    if (e) {
      e.preventDefault();
      e.returnValue = "系统可能不会保存您做的的更改！";
    }
    return "系统可能不会保存您做的的更改！";
}

export default {
  name: 'createMerchant',
  components: {
    FlowOpinion,
  },
  directives: {
    'sticky': VueSticky,
  },
  data() {
    const noNumValid = (rule, value, callback) => {
      if (/\d/.test(value)) {
        callback(new Error('公司名称不能包含数字'));
      } else {
        callback();
      }
    }
    return {
      isEdit: false,
      activeStep: 1,
      industryList: [], // 行业类别
      industryListOptions: [],
      workCategoryList: [], // 工作类目
      workCategoryOptions: [], // 工作类目树
      staffList: [],
      companyLeafletFileUrls: [], // 公司宣传文件
      supplementFileUrls: [], // 补充资料
      showMoreCooperateInfo: false, // 控制更多合作信息的显示和隐藏

      form_1: {
        mchName: '',
        branchName: '',
        mchNo: '',
        salerId: '',
        salerName: '',
        contactName: '',
        contactPhone: '',
        contactEmail: '',
        industryTypeCode: '',
        industryTypeName: '',
        remark: '',
        workerNum: '',
        signRateLevel: '',
        workerMonthIncomeRate: '',
        monthMoneySlip: '',
        provideIncomeDetailType: '',
        companyWebsite: '',
        bizPlatformName: '',
        supplementFileUrls: [],
        companyLeafletFileUrls: [],

        positionVoList: [{
          workplaceCode: '101', //默认101，来源数据字典
          workCategoryCode: [],
          workCategoryName: '',
          invoiceCategoryList: [],
          invoiceCategoryCode: '',
          invoiceCategoryName: '',
          serviceDesc: '',
          chargeRuleDesc: '',
          businessDesc: '',
          _invoiceCateList: [], // 下拉框选项列表
        }],

        agentNo: '',
        agentName: '',
      },
      form: {},
      rules: {
        // step 1
        mchName: [
          { required: true, message: '请输入企业名称', trigger: 'blur', },
          { validator: noNumValid, trigger: 'blur' },
        ],
        branchName: [
          { validator: validateParams({ type: 'Length', max: '6', msg: '最多输入6个字符' }), trigger: 'blur' }
        ],
        industryTypeCode: [
          { required: true, message: '请选择企业行业类别', trigger: 'change', }
        ],
        salerId: [{
          required: true, message: '请选择销售', trigger: 'change',
        }],
        contactName: [
          { required: true, message: '请输入商户联系人姓名', trigger: 'blur' },
          { validator: validateParams({ type: 'Chinese', msg: '请输入中文' }), trigger: 'blur' },
          { validator: validateParams({ type: 'Length', max: '10', msg: '最多输入10个中文' }), trigger: 'blur' }
        ],
        contactPhone: [
          { required: true, message: '请输入商户联系人手机号', trigger: 'blur' },
          { validator: validateParams({ type: 'Phone', msg: '请输入正确的手机号' }), trigger: 'blur' }
        ],
        contactEmail: [
          { required: true, message: '请输入商户联系人邮箱', trigger: 'blur' },
        ],
        positionVoList: {
          workplaceCode: [
            { required: true, message: '请选择自由职业者的工作场所', trigger: 'change', }
          ],
          workCategoryCode: [
            { required: true, message: '请选择自由职业者服务类型', trigger: 'change', }
          ],
          serviceDesc: [
            { required: true, message: '请输入自由职业者服务描述', trigger: 'blur', },
            { validator: validateParams({ type: 'Length', max: '500', msg: '最多输入500个字符' }), trigger: 'blur' }
          ],
          chargeRuleDesc: [
            { required: true, message: '请输入自由职业者服务所得计算规则', trigger: 'blur', },
            { validator: validateParams({ type: 'Length', max: '500', msg: '最多输入500个字符' }), trigger: 'blur' }
          ],
          invoiceCategoryList: [
            { required: true, message: '请选择发票类目', trigger: 'change' }
          ]
        },

        workerNum: [
          { validator: validateParams({ type: 'Reg', pattern: /^\d{1,10}$/, msg: '预计用工人数最多可输入10个字' }), trigger: 'blur' }
        ],
        signRateLevel: [],
        workerMonthIncomeRate: [],
        monthMoneySlip: [
          { required: true, message: '请输入月流水预估', trigger: 'blur', },
          {
            validator: validateParams({ type: 'Reg', pattern: /^\d{1,10}$/, msg: '月流水预估最多可输入10个字' }),
            trigger: 'blur'
          }
        ],
        provideIncomeDetailType: [
          { required: true, message: '请选择是否可提供服务记录及经营所得计算明细', trigger: 'change', },
        ],
        companyWebsite: [
          { validator: validateParams({ type: 'Null', msg: '请输入正确的网址' }), trigger: 'blur' },
        ],
        bizPlatformName: [
          { validator: validateParams({ type: 'Null', msg: '请输入中文' }), trigger: 'blur' },
        ],

        companyLeafletFileUrls: [],
      },
      // step 3
      result: null,
      loading: false,

      editAccess: false, // 修改负责人控制

      agentList: [],

      flowVisible: false, // 审批意见显示控制
      flowRemark: '',

      tempList: [], // 服务引用模板
      allInvoiceCate: [], // 全部发票类目

      mobileUrl: '', // h5 链接
    }
  },
  computed: {
    actionType() {
      return this.$route.query.actionType || 'ADD';
    },
    platform() {
      return this.$route.query.platform || '';
    },
    processId() {
      return this.$route.query.processId || '';
    },
    taskId() {
      return this.$route.query.taskId || '';
    },
  },
  async mounted() {
    window.addEventListener("beforeunload", beforeunloadHandler)
    await this.init();
    if (sessionStorage.getItem('cooperationInfo')) {
      let data = JSON.parse(sessionStorage.getItem('cooperationInfo'));
      this.tempInfo = JSON.parse(sessionStorage.getItem('cooperationInfo'));
      this.$set(this, 'form', data);
      this.isEdit = true;
      if (this.form.companyLeafletFileUrls) {
        for (let item of this.form.companyLeafletFileUrls) {
          const companyLeafletFile =  await this.formatFileMsg(item);
          this.companyLeafletFileUrls.push({
            name: '文件',
            // url: item,
            url: companyLeafletFile.fileUrl,
            origin: item, // 文件源
          })
        }
        this.form.companyLeafletFileUrls = this.companyLeafletFileUrls;
      }
      if (this.form.supplementFileUrls) {
        for (let item of this.form.supplementFileUrls) {
          const supplementFile =  await this.formatFileMsg(item);
          let suffix = item.split('.')[1];
          this.supplementFileUrls.push({
            // url: this.fileUrl + item,
            name: `补充信息文件.${ suffix }`,
            url: supplementFile.fileUrl,
            origin: item, // 文件源
          })
        }
        this.form.supplementFileUrls = this.supplementFileUrls;
      }
      for (let p in this.form_1) {
        this.form_1[p] = this.form[p];
      }
      this.preHandleCategory()
    }

    if (this.actionType == 'ADD') {
      this.editAccess = true;
    }
    if (!this.form_1.salerId) {
      this.staffList.forEach(staff => {
        if (staff.id == this.userID) {
          this.form_1.salerId = staff.id;
        }
      })
    }

    this.form_1.salerId && (this.handleSalerChange(this.form_1.salerId || null, true))

  },
  beforeDestroy() {
    sessionStorage.removeItem('cooperationInfo');
    window.removeEventListener("beforeunload", beforeunloadHandler)
  },
  methods: {
    
    async init() {
      // 初始化页面数据
      await Promise.all([
        this.getListAllIndustryType(),
        this.getListAllWorkCategory(),
        this.getAllInvoiceCate()
      ]);
      const response = await getSales();
      this.staffList = response.data;
    },
    // 预处理数据
    preHandleCategory() {
      this.form_1.positionVoList.forEach((item, index) => {
        this.changeWorkCategory(item.workCategoryCode, index, true);
      })
    },
    // 获取企业行业类别
    getListAllIndustryType() {
      listAllIndustryType().then(response => {
        this.industryList = response.data;
        this.industryListOptions = convert(response.data, 0);
      })
    },
    // 获取工作类目
    async getListAllWorkCategory() {
      const response = await listAllWorkCategory()
      this.workCategoryList = response.data;
      this.workCategoryOptions = convert(response.data, 0);
    },
    changeWorkCategory(value, index, prehandle) {
      for (let item of this.workCategoryList) {
        if (item.workCategoryCode == value) {
          if (!prehandle) {
            this.form_1.positionVoList[index].workCategoryName = item.workCategoryName;
            this.form_1.positionVoList[index].serviceDesc = '';
            this.form_1.positionVoList[index].chargeRuleDesc = '';
            this.form_1.positionVoList[index].businessDesc = '';
            const serviceDesc = item.workDesc;
            const chargeRuleDesc = item.chargeRuleDesc;
            const businessDesc = item.businessDesc || '';
            this.tempList.splice(index, 1, {
              serviceDesc,
              chargeRuleDesc,
              businessDesc,
            })
          } else {
            this.form_1.positionVoList[index].workCategoryName = item.workCategoryName;
            this.form_1.positionVoList[index].serviceDesc = item.workDesc;
            this.form_1.positionVoList[index].chargeRuleDesc = item.chargeRuleDesc;
            this.form_1.positionVoList[index].businessDesc = item.businessDesc || '';
          }
          // 拿到对应工作类目的发票类目
          const list = item.jsonEntity.invoiceCategoryList.map(cate => {
            return {
              invoiceCategoryCode: cate.invoiceCategoryCode,
              invoiceCategoryName: cate.invoiceCategoryName,
            }
          });
          if (!prehandle) {
            this.form_1.positionVoList[index].invoiceCategoryList = [];
          }
          if (prehandle) {
            this.$set(this.form_1.positionVoList[index], '_invoiceCateList', this.allInvoiceCate);
          } else {
            this.$set(this.form_1.positionVoList[index], '_invoiceCateList', list);
          }
          break;
        }
      }
    },
    selectIndustry(value, index) {
      for (let item of this.industryList) {
        if (item.industryTypeCode == value) {
          this.form_1.industryTypeName = item.industryTypeName;
        }
      }
    },
    addJob() {
      this.form_1.positionVoList.push({
        workplaceCode: '',
        workCategoryCode: '',
        workCategoryName: '',
        invoiceCategoryList: [],
        serviceDesc: '',
        chargeRuleDesc: '',
      })
    },
    deleteJob(index) {
      if (this.form_1.positionVoList.length == 1) {
        return this.$message.error("必须含有一个岗位信息")
      } else {
        this.form_1.positionVoList.splice(index, 1);
      }
    },
    beforeFileUpload(file) {
      return this.validateUploadFile({
        img: true,
        pdf: true,
        word: true,
        addition: ['ppt', 'txt', 'pptx'],
        size: 10
      })(file)
    },
    handleRemove(param) {
      return (file, fileList) => {
        this[param] = this[param].filter(item => {
          return item.uid !== file.uid;
        });
        let form = this['form_' + this.activeStep];
        form[param] = this[param];
      }
    },
    handleFileRemove(file, param) {
      this[param] = this[param].filter(item => {
        return item.uid !== file.uid;
      });
      let form = this['form_' + this.activeStep];
      form[param] = this[param];
    },
    handleExceed() {
      this.$message.error('最多上传8张!')
    },
    handleSuccess(param) {
      return (response, file, fileList) => {
        let form = this['form_' + this.activeStep];
        form[param] = this[param] = fileList;
      }
    },
    async nextStep() {
      for (let p in this[`form_${ this.activeStep }`]) {
        if (typeof this[`form_${ this.activeStep }`][p] == 'string' && !(/Term/.test(p))) {
          this[`form_${ this.activeStep }`][p] = this[`form_${ this.activeStep }`][p].replace(/\s+/, '')
        }
      }
      let form = this.$refs['form_' + this.activeStep];
      const valid = await form.validate().catch(_ => false);
      if (!valid) {
        this.$message.error('请补充必填信息');
        return;
      }
      if (this.activeStep == 1) {
        if (!this.flowVisible && this.processId) {
          this.flowVisible = true;
          return;
        }
        this.loading = true;
        this.flowVisible = false;
        let data = { ...this.tempInfo, ...this.form_1 };
        
        if (!this.showMoreCooperateInfo) {
          data.workerNum = '';
          data.signRateLevel = '';
          data.workerMonthIncomeRate = '';
        }
        
        data.companyLeafletFileUrls = data.companyLeafletFileUrls.map(item => {
          if (item.response) {
            return item.response.data
          } else {
            return item.origin
          }
        });
        data.supplementFileUrls = data.supplementFileUrls.map(item => {
          if (item.response) {
            return item.response.data
          } else {
            return item.origin
          }
        });
        data.positionVoList.forEach(item => {
          delete item._invoiceCateList
        });
        clearVoid(data);
        try {
          if (this.isEdit) {
            if (this.processId) {
              data.salerName = this.form.salerName
              const response = await editBusinessData({
                commonFlowId: this.processId,
                taskId: this.taskId,
                extInfo: JSON.stringify(data),
                remark: this.flowRemark,
              });
              this.$message.success(response.data);
              this.$router.push({
                path: '/waitingHandle/detailProcess',
                query: {
                  processId: this.processId,
                  taskId: this.taskId,
                }
              });
            } else {
              if (this.editAccess) {
                await this.changeLeader();
              }
              const response = updateCooperate(data);
              this.$message.success(response.data);
              this.$router.push({
                path: `/merchant/merchantInfo/${ this.form_1.mchNo }`
              });
            }
          } else {
            const response = await createMerchant(data);
            this.activeStep++;
            this.result = response.data;
          }
        } finally {
          this.loading = false;
        }
      }
    },
    backStep() {
      if (this.processId) {
        this.$router.back();
      } else {
        this.$router.push('/merchant/merchantManagement/merchant')
      }
    },
    goDetailProcess() {
      this.$router.push('/waitingHandle/detailProcess?processId=' + this.result.commonFlowId);
    },
    async changeLeader() {
      const form = {
        newLeaderPhone: this.form_1.contactPhone,
        newLeaderName: this.form_1.contactName,
        mchNo: this.form_1.mchNo,
      };
      await changeLeader(form);
      this.editAccess = false;
    },
    handleAgentChange(val) {
      if (!val) {
        this.form_1.agentNo = this.form_1.agentName = '';
        return;
      }
      for (let i = 0; i < this.agentList.length; i++) {
        if (this.agentList[i].agentNo == val) {
          this.form_1.agentName = this.agentList[i].agentName;
          break;
        }
      }
    },
    async handleSalerChange(salerId, isKeep) {
      if (this.platform !== 1003 && !isKeep) {
        this.form_1.agentNo = '';
        this.form_1.agentName = '';
      }
      const { data } = await getAllAgentSimple({
        salerId
      });
      this.agentList = data;
    },
    flowConfirm(remark) {
      this.flowRemark = remark;
      this.nextStep();
    },
    usePositionRef(index) {
      if (this.tempList[index]) {
        const { serviceDesc, chargeRuleDesc, businessDesc } = this.tempList[index];
        this.form_1.positionVoList[index].serviceDesc = serviceDesc;
        this.form_1.positionVoList[index].chargeRuleDesc = chargeRuleDesc;
        this.form_1.positionVoList[index].businessDesc = businessDesc;
      }
    },
    async handlePreview(file) {
      let url = file.response ? file.response.data : file.origin;
      const fileMsg = await this.formatFileMsg(url)
      if (this.isImg(url)) {
        this.$preview(fileMsg.fileUrl)
      } else {
        const suffix = url.split('.').pop()
        this.downloadFile(fileMsg.fileUrl, `补充信息文件`, suffix);
      }
    },
    changeCustomInvoice(item) {
      item._invoiceCateList = this.allInvoiceCate
    },
    async getAllInvoiceCate() {
      const { data } = await getAllInvoiceCategory();
      this.allInvoiceCate = data
    },
    async generateMobileUrl() {
      const { data } = await generateCreateURL({
        agentNo: this.form_1.agentNo, salerId: this.form_1.salerId
      })
      this.mobileUrl = data
      this.$nextTick(() => {
        this.$refs.copy.$el.click()
      })
    }
  }
}
</script>

<style
  lang="scss"
  scoped>
.page-container {
  .footer-container {
    text-align: center;
  }

  .step_1 {
    .job-divider {
      position: relative;

      .el-button {
        position: absolute;
        bottom: 0;
        right: 0;
      }
    }
  }

  .step_3 {
    padding: 50px 0;
    text-align: center;

    p {
      margin-bottom: 20px;
    }
  }
}
</style>
