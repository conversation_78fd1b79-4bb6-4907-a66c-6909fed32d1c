<template>
  <div class="merchant-page-container">
    <div
      class="header-container"
      v-sticky="{zIndex: 100, stickyTop: 0}">
      <div class="step-container">
        <el-steps
          :active="activeStep"
          align-center>
          <el-step title="填写合作信息"></el-step>
          <el-step title="审核与公示"></el-step>
        </el-steps>
      </div>
    </div>
    <div class="page-container">
      <div class="content-container">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="260px">
          <!--step_1-->
          <div
            class="step_1"
            v-show="activeStep == 1">
            <!--企业信息-->
            <div class="company_info">
              <p class="subTitle">企业信息</p>
              <div>
                <el-form-item
                  label="企业名称："
                  prop="mchName">
                  <el-input v-model="form.mchName"></el-input>
                </el-form-item>
                <el-form-item
                  label="销售："
                  prop="saleId">
                  <el-select
                    clearable
                    v-model="form.saleId">
                    <el-option
                      v-for="item in staffList"
                      :key="item.id"
                      :label="item.realName"
                      :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="商户联系人姓名："
                  prop="contactName">
                  <el-input v-model="form.contactName"></el-input>
                  <p class="color-gray form-tip">请填写公司运营负责人</p>
                </el-form-item>
                <el-form-item
                  label="商户联系人邮箱："
                  prop="contactEmail">
                  <el-input v-model="form.contactEmail"></el-input>
                </el-form-item>
                <el-form-item
                  label="联系人手机号："
                  prop="contactPhone">
                  <el-input v-model="form.contactPhone">
                    <template slot="prepend">中国+86</template>
                  </el-input>
                  <p class="color-gray form-tip">
                    用于商户的后台登录、接收日常经营提醒及相关操作验证
                  </p>
                </el-form-item>
              </div>
            </div>
            <!--合作信息-->
            <div class="cooperation_info">
              <p class="subTitle">合作信息</p>
              <div>
                <el-form-item label="是否有委托代征协议：">
                  <el-radio-group v-model="form.provideIncomeDetailType">
                    <el-radio label="0">是</el-radio>
                    <el-radio label="1">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="委托代征协议："
                  v-if="form.provideIncomeDetailType == '0'">
                  <el-upload
                    v-toggle="entrustAgreementFileUrlShow"
                    class="upload-demo"
                    :action="baseUrl + '/file/upload'"
                    :headers="uploadHeader"
                    list-type="picture-card"
                    accept=".pdf,.bmp,.png,.jpeg,.jpg,.gif"
                    :limit="1"
                    :file-list="entrustAgreementFileUrlShow"
                    :before-upload="beforeImgUpload"
                    :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'entrustAgreementFileUrl')"
                    :on-remove="(file, fileList) => handleRemove(file, fileList, 'entrustAgreementFileUrl')">
                    <div
                      slot="file"
                      slot-scope="{file}"
                      class="fileBg"
                      v-if="isPdf(file.name)">
                      <span class="file-name">{{ file.name }}</span>
                      <span class="el-upload-list__item-actions">
                        <span
                          class="el-upload-list__item-delete"
                          @click="handleFileRemove(file, 'entrustAgreementFileUrl')"
                        >
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                    <div
                      slot="tip"
                      class="el-upload__tip">不超过2M，格式为pdf、bmp、png、jpeg、jpg或gif
                    </div>
                  </el-upload>
                </el-form-item>
                <el-form-item
                  label="B端协议模板："
                  prop="agreementTemplate2BFileUrl">
                  <el-upload
                    v-toggle="agreementTemplate2BFileUrlShow"
                    class="upload-demo"
                    :action="baseUrl + '/file/upload'"
                    :headers="uploadHeader"
                    list-type="picture-card"
                    accept=".doc,.docx"
                    :limit="1"
                    :file-list="agreementTemplate2BFileUrlShow"
                    :before-upload="beforeWordUpload"
                    :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'agreementTemplate2BFileUrl')">
                    <div
                      slot="file"
                      slot-scope="{file}"
                      class="fileBg">
                      <span class="file-name">{{ file.name }}</span>
                      <span class="el-upload-list__item-actions">
                          <span
                            class="el-upload-list__item-delete"
                            @click="handleFileRemove(file, 'agreementTemplate2BFileUrl')"
                          >
                            <i class="el-icon-delete"></i>
                          </span>
                        </span>
                    </div>
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                    <div
                      slot="tip"
                      class="el-upload__tip">支持扩展名：.doc .docx
                    </div>
                  </el-upload>
                </el-form-item>
                <el-form-item
                  label="C端协议模板："
                  prop="agreementTemplate2CFileUrl">
                  <el-upload
                    v-toggle="agreementTemplate2CFileUrlShow"
                    class="upload-demo"
                    :action="baseUrl + '/file/upload'"
                    :headers="uploadHeader"
                    list-type="picture-card"
                    accept=".doc,.docx"
                    :limit="1"
                    :file-list="agreementTemplate2CFileUrlShow"
                    :before-upload="beforeWordUpload"
                    :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'agreementTemplate2CFileUrl')">
                    <div
                      slot="file"
                      slot-scope="{file}"
                      class="fileBg">
                      <span class="file-name">{{ file.name }}</span>
                      <span class="el-upload-list__item-actions">
                        <span
                          class="el-upload-list__item-delete"
                          @click="handleFileRemove(file, 'agreementTemplate2CFileUrl')"
                        >
                          <i class="el-icon-delete"></i>
                        </span>
                      </span>
                    </div>
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                    <div
                      slot="tip"
                      class="el-upload__tip">支持扩展名：.doc .docx
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
            <div class="form-btn-group">
              <el-button
                type="primary"
                @click="submit">提交
              </el-button>
              <el-button @click="$router.push('/merchant/merchantManagement/merchant')">取消</el-button>
            </div>
          </div>

          <!--step_2-->
          <div
            class="step_2"
            v-show="activeStep == 2">
            <img
              src="@/assets/success.png"
              alt="">
            <p class="success-text">提交成功</p>
            <p>商户新增流程提交成功</p>
            <div class="footer-container">
              <el-button
                type="primary"
                @click="$router.push('/merchant/merchantManagement/merchant')">返回列表
              </el-button>
            </div>
          </div>

        </el-form>
      </div>
      <el-dialog
        :visible.sync="isShowTip"
        :close-on-click-modal="false"
        title="税务合规知悉提示">

        <h5 style="text-align: center">税务合规知悉提示</h5>

        智享汇合作模式下，个人以独立个体经营者的身份与供应商进行项目合作取得经营所得，供应商根据当地合规要求协助个人以经营所得核定征收计算缴纳个人所得税。但根据有关法规，以下个人或以下个人收入类型不适用以经营所得核定征收申报个人所得税，供应商将拒绝与取得下列收入性质的个人合作。如贵司希望匹配的用工个人为以下用工类型，请勿选用众包服务。<br><br>


        1、个人与实际用工企业签订劳动合同，或虽未签订合同但存在事实劳动关系的个人；事实劳动关系包括但不仅限于直接或间接（通过人力资源公司或关联企业）为用工人员就上述岗位的工作提供社会保险、假期工资等雇员待遇；<br><br>


        2、根据财税[2018]164号等有关法规，保险营销员、个人保险代理人、证券经纪人的佣金收入——应适用劳务报酬计算个税；<br><br>


        3、医生到医疗机构出诊的收入（注：医疗机构为卫健委网站登记的医疗机构http://zgcx.nhc.gov.cn:9090/unit/index）——应适用劳务报酬计算个税；<br><br>


        4、律师事务所、会计师事务所、税务师事务所、资产评估和房地产估价等鉴证类中介机构的从业人员——高收入个人不适用经营所得核定计算个税；<br><br>


        5、稿酬、特许权使用费、股权转让、股东分红等不适用，请按个人对应收入类型计算缴纳个税；<br><br>


        6、其他不适用经营所得核定征收申报缴纳个税的收入类型。<br><br>


        <strong>高工伤或特定资质岗位风险提示知悉：</strong>供应商暂不能承接需具备专业资质/行政许可方能提供的服务，包括但不仅限于以下需要特殊资质的工种或者行业：电工、焊接、起重、驾驶、高空作业（高于地面2米或以上）等<br><br>
        <div
          slot="footer"
          class="dialog-footer">
          <el-button @click="$router.push('/merchantManagement/merchant')">取 消</el-button>
          <el-button
            type="primary"
            @click="isShowTip = false">同意并继续
          </el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { createSupplier, updateSupplier } from '@/api/merchant'
import { getSales } from '@/api/system'
import { validateParams } from '@/utils/validate'
import VueSticky from 'vue-sticky'

import '@/styles/merchant-form.scss'

export default {
  name: 'createSupplier',
  directives: {
    'sticky': VueSticky,
  },
  data() {
    const noNumValid = (rule, value, callback) => {
      if (/\d/.test(value)) {
        callback(new Error('公司名称不能包含数字'));
      } else {
        callback();
      }
    }
    return {
      isShowTip: false,
      activeStep: 1,
      staffList: [],
      entrustAgreementFileUrlShow: [],
      agreementTemplate2BFileUrlShow: [],
      agreementTemplate2CFileUrlShow: [],
      form: {
        // step 1
        mchName: '',
        mchType: '',
        saleId: '',
        contactName: '',
        contactPhone: '',
        contactEmail: '',

        entrustAgreementFileUrl: '',
        agreementTemplate2BFileUrl: '',
        agreementTemplate2CFileUrl: '',
        provideIncomeDetailType: '',
      },
      rules: {
        // step 1
        mchName: [
          { required: true, message: '请输入企业名称', trigger: 'blur' },
          { validator: noNumValid, trigger: 'blur' }
        ],
        saleId: [{
          required: true, message: '请选择销售', trigger: 'change',
        }],
        contactName: [
          { required: true, message: '请输入商户联系人姓名', trigger: 'blur' },
          { validator: validateParams({ type: 'Chinese', msg: '请输入中文' }), trigger: 'blur' },
          { validator: validateParams({ type: 'Length', max: '15', msg: '最多输入15个中文' }), trigger: 'blur' }
        ],
        contactPhone: [
          { required: true, message: '请输入商户联系人手机号', trigger: 'blur' },
          { validator: validateParams({ type: 'Phone', msg: '请输入正确的手机号' }), trigger: 'blur' }
        ],
        contactEmail: [
          { required: true, message: '请输入商户联系人邮箱', trigger: 'blur' },
        ],
        agreementTemplate2BFileUrl: [{
          required: true, message: '请上传B端协议模板', trigger: 'change',
        }],
        agreementTemplate2CFileUrl: [{
          required: true, message: '请上传C端协议模板', trigger: 'change',
        }],
      },
    }
  },
  computed: {
    actionType() {
      return this.$route.query.actionType || 'ADD'
    }
  },
  async mounted() {
    getSales().then(
      (response) => {
        this.staffList = response.data;
        this.actionType == 'ADD' && this.staffList.forEach(item => {
          if (item.id == this.userID) {
            this.form.saleId = this.userID
          }
        })
      }
    );
    if (this.actionType == 'EDIT' && sessionStorage.getItem('cooperationInfo')) {
      let form = JSON.parse(sessionStorage.getItem('cooperationInfo'));
      for (let p in form) {
        this.form[p] = form[p]
      }

      this.form.provideIncomeDetailType = '1';
      if (this.form.entrustAgreementFileUrl) {
        if (this.isPdf(this.form.entrustAgreementFileUrl)) {
          const entrustAgreementFile =  await this.formatFileMsg(this.form.entrustAgreementFileUrl);
          this.entrustAgreementFileUrlShow.push({
            // url: this.form.entrustAgreementFileUrl,
            url: entrustAgreementFile.fileUrl,
            name: '委托代征协议.pdf',
            origin: this.form.entrustAgreementFileUrl
          })
        } else {
          const entrustAgreementFile =  await this.formatFileMsg(this.form.entrustAgreementFileUrl);
          this.entrustAgreementFileUrlShow.push({
            // url: this.fileUrl + this.form.entrustAgreementFileUrl,
            url: entrustAgreementFile.fileUrl,
            name: '委托代征协议',
            origin: this.form.entrustAgreementFileUrl
          })
        }
        this.form.provideIncomeDetailType = '0'
      }

      if (this.form.agreementTemplate2BFileUrl) {
        const agreementTemplate2BFile = await this.formatFileMsg(this.form.agreementTemplate2BFileUrl)
        this.agreementTemplate2BFileUrlShow.push({
          // url: this.form.agreementTemplate2BFileUrl,
          url: agreementTemplate2BFile.fileUrl,
          name: 'B端协议模板',
          origin: this.form.agreementTemplate2BFileUrl,
        })
      }

      if (this.form.agreementTemplate2CFileUrl) {
        const agreementTemplate2CFile = await this.formatFileMsg(this.form.agreementTemplate2CFileUrl)
        this.agreementTemplate2CFileUrlShow.push({
          // url: this.form.agreementTemplate2CFileUrl,
          url: agreementTemplate2CFile.fileUrl,
          name: 'C端协议模板',
          origin: this.form.agreementTemplate2CFileUrl,
        })
      }
    }
  },
  methods: {
    beforeImgUpload(file) {
      const fileFormat = {
        'application/pdf': true,
        'image/bmp': true,
        'image/png': true,
        'image/jpeg': true,
        'image/gif': true,
      }
      const isFormat = fileFormat[file.type];
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isFormat) {
        this.$message.error('上传文件格式只能为pdf、bmp、png、jpeg、jpg或gif!');
      }
      if (!isLt2M) {
        this.$message.error('上传文件大小不能超过 2MB!');
      }
      return !!(isFormat && isLt2M);
    },
    beforeWordUpload(file) {
      const isTrueType = ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.type);
      const isLt6M = file.size / 1024 / 1024 < 6;

      if (!isTrueType) {
        this.$message.error('上传文件只能是 doc 和 docx 格式!');
      }
      if (!isLt6M) {
        this.$message.error('上传文件大小不能超过 6MB!');
      }
      return isTrueType && isLt6M;
    },
    handleRemove(file, fileList, param) {
      this[param + 'Show'] = fileList;
    },
    handleSuccess(response, file, fileList, param) {
      this[param + 'Show'] = fileList;
    },
    async submit() {
      for (let p in this.form) {
        if (typeof this.form[p] == 'string') {
          this.form[p] = this.form[p].replace(/\s+/, '');
        }
      }
      this.form.entrustAgreementFileUrl = (this.entrustAgreementFileUrlShow[0])
        ? ((this.entrustAgreementFileUrlShow[0].response)
          ? this.entrustAgreementFileUrlShow[0].response.data
          : this.entrustAgreementFileUrlShow[0].origin)
        : '';
      this.form.agreementTemplate2BFileUrl = (this.agreementTemplate2BFileUrlShow[0])
        ? ((this.agreementTemplate2BFileUrlShow[0].response)
          ? this.agreementTemplate2BFileUrlShow[0].response.data
          : this.agreementTemplate2BFileUrlShow[0].origin)
        : '';
      this.form.agreementTemplate2CFileUrl = (this.agreementTemplate2CFileUrlShow[0])
        ? ((this.agreementTemplate2CFileUrlShow[0].response)
          ? this.agreementTemplate2CFileUrlShow[0].response.data
          : this.agreementTemplate2CFileUrlShow[0].origin)
        : '';
      // this.form.entrustAgreementFileUrl.replace(this.fileUrl, '')
      const valid = await this.$refs.form.validate().catch(_ => false)
      if (valid) {
        let form = { ...this.form };
        if (this.actionType == 'ADD') {
          await createSupplier(form)
          this.activeStep++;
        } else {
          let salerName = '';
          let salerId = form.saleId;
          this.staffList.forEach(item => {
            if (item.id == salerId) {
              salerName = item.realName;
            }
          });
          const { data } = await updateSupplier({
            mchNo: this.form.mchNo,
            mchName: this.form.mchName,
            entrustAgreementFileUrl: form.entrustAgreementFileUrl,
            agreementTemplate2BFileUrl: form.agreementTemplate2BFileUrl,
            agreementTemplate2CFileUrl: form.agreementTemplate2CFileUrl,
            salerId,
            salerName,
          })
          this.$message.success(data);
          this.$router.push({ path: `/merchant/merchantInfo/${this.form.mchNo}` })
        }
      }
    },
    handleFileRemove(file, param) {
      this[param + 'Show'] = this[param + 'Show'].filter(item => {
        return item.url !== file.url
      })
    },
  }
}
</script>

<style
  lang="scss"
  scoped>
.page-container {
  .step-container {
    padding: 20px 0 0;
    background: #fff;
  }

  .content-container {
    margin-top: 0;
    padding: 20px;
    background: #fff;

    .el-form {
      width: 800px;
      margin: 0 auto;
    }

    hgroup {
      text-align: center;
    }

    .footer-container {
      text-align: center;
    }
  }

  .subTitle {
    position: relative;
    margin-left: 30px;
    margin-bottom: 20px;
    padding-left: 30px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;

    &:before {
      position: absolute;
      content: '';
      left: 10px;
      width: 4px;
      height: calc(100% - 10px);
      background: #20a0ff;
    }
  }

  .step_1 {
    .job-box {
      padding-bottom: 50px;
    }
  }

  .step_2 {
    padding: 40px 0;
    text-align: center;

    img {
      width: 100px;
    }

    .success-text {
      font-size: 20px;
    }

    .result-detail {
      margin: 40px 0;
      background: #eee;
    }
  }
}
</style>
