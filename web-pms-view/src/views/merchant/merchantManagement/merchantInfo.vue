<template>
  <div
    class="merchant-info-box"
    :class="{'show-editor' : showEditor}"
  >
    <merchant-info-comp
      :loading="loading"
      :base-info="baseInfo"
      :cooperate-info="cooperateInfo"
      :quote-info="quoteInfo"
      :main-info="mainInfo"
      :business-info="businessInfo"
      :account-info="accountInfo"
      :agreement-info="agreementInfo"
      :system-info="systemInfo"
      :record-info="recordInfo"
      :account-quote-info="accountQuoteInfo"
      :history-info="historyInfo"
      :staffList="staffList"
      :workCategoryList="workCategoryList"
      :workCategoryOptions="workCategoryOptions"
      :industryList="industryList"
      :industryListOptions="industryListOptions"
      :channelList="channelList"
      :disabled="formDisabled"
      :show-edit="!isView"
      :delQuote="onDeleteQuote"
      :active-tab="processId ? 'accountInfo' : 'baseInfo'"
      :all-invoice-cate="allInvoiceCate"
      :load-history="getHandleList"
      @export-info="exportInfo"
      @get-export="getExportList"
      @switch-mch="switchMerchant"
      @leader-change="onChangeLeader"
      @sale-change="onSaleChange"
      @edit-click="onEditClick"
      @tab-change="onTabChange"
      ref="merchantInfo"
    >
      <template v-slot:btnMiddle>
        <el-button
          type="primary"
          v-if="isFrozen"
          @click="onStatusChange"
          v-permission="'pms:merchantFlow:freeze'"
        >解冻
        </el-button>
        <el-button
          type="danger"
          v-if="isActive"
          @click="onStatusChange"
          v-permission="'pms:merchantFlow:freeze'"
        >冻结
        </el-button>
      </template>
    </merchant-info-comp>

    <div class="form-btn-group">
      <el-button
        v-show="!formDisabled"
        type="primary"
        @click="onClickSave"
      >保存
      </el-button>
      <el-button @click="onCancel">{{ formDisabled ? '取消' : '退出编辑' }}</el-button>
    </div>

    <!--导出dialog-->
    <export-select
      ref="exportSelect"
      dict="ExportEmployerInfo"
      @confirm="submitExportForm"
    ></export-select>
    <export-record ref="exportRecord"></export-record>

    <flow-opinion
      :visible.sync="confirmVisible"
      @change="onConfirmEdit"
    ></flow-opinion>

    <right-editor
      :mchNo="mchNo"
      :list="eventsFollow"
      @toggle="toggleEditor"
      @list-change="getEventFollowByMch"
    ></right-editor>

    <leader-change-dialog
      ref="leaderDialog"
      :visible.sync="showLeaderChange"
      @change-leader="changeLeader"
    ></leader-change-dialog>

    <sale-change-dialog
      ref="saleDialog"
      :visible.sync="showSaleChange"
      :staff-list="staffList"
      :is-merchant="isMerchant"
      @change-sale="changeSale"
    ></sale-change-dialog>

    <status-change-dialog
      :visible.sync="showStatusChange"
      :mchStatus="baseInfo.mchStatus"
      @change-status="changeStatus"
    ></status-change-dialog>

  </div>
</template>

<script>
import {
  exportMerchantInfo, getQuote,
  getBaseInfo, getCooperateInfo, getMainInfo, getAccountInfo, getBusinessInfo, getAgreementInfo, getRecordInfo, getQuoteInfo,
  editBaseInfo, editMainstayCooperateInfo, editMerchantCooperateInfo, editMainInfo, editBusinessInfo, deleteQuote
} from '@/api/merchant'
import { getSystemRecord } from '@/api/common';
import { getEventFollowByMch } from "@/api/eventsFollow";
import { listAllIndustryType, listAllWorkCategory } from '@/api/business'
import { getSales } from '@/api/system'
import { editBusinessData, changeAccount, changeLeader, changeSale, changeStatus } from '@/api/flow';
import { getPayChannelList } from "@/api/levy";
import { getAllInvoiceCategory } from "@/api/invoice";
import { getHandleList } from '@/api/flow'

import ExportSelect from '@/components/ExportSelect'
import ExportRecord from '@/components/ExportRecord/index'
import MerchantInfoComp from '@/components/MerchantInfoComp'
import FlowOpinion from '@/components/Flow/FlowOpinion'
import LeaderChangeDialog from '@/components/LeaderChangeDialog'

import RightEditor from './Component/RightEditor'
import SaleChangeDialog from './Component/SaleChangeDialog'
import StatusChangeDialog from "./Component/StatusChangeDialog";
import { convert } from '@/utils'

// 权限对应查询map
const permissionMap = {
  'merchantEmployer:base:view': {
    api: getBaseInfo,
    result: 'baseInfo'
  },
  'merchantEmployer:coop:view': {
    api: getCooperateInfo,
    result: 'cooperateInfo'
  },
  'merchantEmployer:quote:view': {
    api: getQuoteInfo,
    result: 'quoteInfo'
  },
  'merchantEmployer:main:view': {
    api: getMainInfo,
    result: 'mainInfo'
  },
  'merchantEmployer:agreement:view': {
    api: getAgreementInfo,
    result: 'agreementInfo'
  },
  'merchantEmployer:account:view': {
    api: getAccountInfo,
    result: 'accountInfo'
  },
  'merchantEmployer:business:view': {
    api: getBusinessInfo,
    result: 'businessInfo'
  },
  'void': {
    api: getRecordInfo,
    result: 'recordInfo'
  }
};
// tab修改的map
const tabMap = {
  baseInfo: {
    needFlow: false,
    api: editBaseInfo
  },
  cooperateInfo: {
    needFlow: false,
    api: [editMainstayCooperateInfo, editMerchantCooperateInfo]
  },
  mainInfo: {
    needFlow: false,
    api: editMainInfo
  },
  businessInfo: {
    needFlow: false,
    api: editBusinessInfo
  },
  accountInfo: {
    needFlow: true,
    api: changeAccount,
  },
  quoteInfo: {
    needFlow: true,
    api: deleteQuote
  }
};
export default {
  name: 'merchantInfo',
  components: {
    StatusChangeDialog,
    ExportRecord,
    ExportSelect,
    FlowOpinion,
    RightEditor,
    MerchantInfoComp,
    LeaderChangeDialog,
    SaleChangeDialog,
  },
  data() {
    return {
      // 商户信息
      baseInfo: {},
      cooperateInfo: {},
      quoteInfo: [],
      mainInfo: {},
      accountInfo: {},
      businessInfo: {},
      agreementInfo: [],
      systemInfo: {},
      recordInfo: [],
      accountQuoteInfo: [], // 账户信息中报价单信息
      historyInfo: {},

      loading: true,

      staffList: [],
      agentList: [],
      activeTab: "0",
      confirmVisible: false,
      productList: [], // 计费产品列表
      showEditor: false, // 右侧编辑器

      nowIndex: -1, // 现在的商户序号
      merchantList: [], // 缓存的列表节点
      eventsFollow: [], // 企业动态

      workCategoryList: [], // 工作类目
      workCategoryOptions: [], // 工作类目树
      industryList: [], // 行业类别
      industryListOptions: [],
      channelList: [],

      showLeaderChange: false,
      showSaleChange: false,
      showStatusChange: false,

      formDisabled: true, // 是否禁用表单，一开始均为true，可以切换
      allInvoiceCate: [],
    }
  },
  computed: {
    mchNo() {
      return this.$route.params.mchNo || '';
    },
    actionType() {
      return this.$route.query.actionType
    },
    isView() {
      return this.actionType === 'VIEW'
    },
    isEdit() {
      return this.actionType === 'EDIT'
    },
    isMerchant() {
      return this.baseInfo.merchantType == 100;
    },
    processType() {
      return this.$route.query.processType || '';
    },
    processId() {
      return this.$route.query.processId || '';
    },
    taskId() {
      return this.$route.query.taskId || '';
    },
    isFrozen() {
      return this.baseInfo.mchStatus == 101
    },
    isActive() {
      return this.baseInfo.mchStatus == 100
    }
  },
  async mounted() {
    if (sessionStorage.getItem('merchantList')) {
      this.merchantList = JSON.parse(sessionStorage.getItem('merchantList'))
    }
    if (sessionStorage.getItem('merchantIndex')) {
      this.nowIndex = Number(sessionStorage.getItem('merchantIndex'));
    }
    await this.initPage();
    this.init();
  },
  methods: {
    async init() {
      // 获取商户各个模块下的信息
      await this.getMerchantAllInfo();

      if (this.processId) {
        const info = JSON.parse(sessionStorage.getItem('merchantInfo'));
        for (let p in info) {
          this[p] = {...this[p], ...info[p]}
        }
      }
      // 商户动态
      this.getEventFollowByMch();
    },
    async initPage() {
      let list = [
        this.getListAllWorkCategory(),
        this.getListAllIndustryType(),
        this.getSale(),
        this.getAllInvoiceCate(),
      ];
      if (this.hadPermission('merchantEmployer:account:view')) {
        list.push(this.getChannelList())
      }
      await Promise.all(list);
    },
    async getEventFollowByMch() {
      const {data} = await getEventFollowByMch({mchNo: this.mchNo});
      this.eventsFollow = data || [];
    },
    switchMerchant(step) {
      let newIndex = this.nowIndex + step;
      if (newIndex < 0 || newIndex >= this.merchantList.length) {
        return this.$message('已无更多商户')
      }
      this.nowIndex = newIndex;
      sessionStorage.setItem('merchantIndex', newIndex);
      const mchNo = this.merchantList[newIndex].mchNo;
      this.$router.push({
        path: `/merchant/merchantInfo/${mchNo}`,
        query: {
          actionType: this.actionType
        }
      });
    },
    async getSale() {
      const {data} = await getSales();
      data.forEach(item => {
        this.staffList.push(item)
      })
    },
    async getMerchantAllInfo() {
      await this.getQuote();
      let promiseList = [];
      let resultList = [];
      for (let p in permissionMap) {
        if (p === 'void' || this.hadPermission(p)) {
          promiseList.push(permissionMap[p]['api']({
            mchNo: this.mchNo
          }));
          resultList.push(permissionMap[p]['result'])
        }
      }

      // 系统信息
      this.loading = true;
      promiseList.push(getSystemRecord({
        mchNo: this.mchNo,
        type: this.mchNo.startsWith('M') ? 100 : 101,
      }));
      resultList.push('systemInfo');

      // 历史记录
      if (this.hadPermission('merchantEmployer:workOrder:view')) {
        promiseList.push(this.getHandleList({
          current: 1,
          size: 10,
        }));
        resultList.push('historyInfo');
      }

      const res = await Promise.all(promiseList).finally(() => {
        this.loading = false;
      });
      res.forEach((item, index) => {
        this[resultList[index]] = item.data || {}
      })
    },
    // 获取商户报价单信息，防止通道账户未启用导致无法显示
    async getQuote() {
      const {data} = await getQuote({mchNo: this.mchNo})
      const tempMap = {};
      const list = [];
      data.forEach(item => {
        if (!(tempMap[item.mainstayMchNo])) {
          tempMap[item.mainstayMchNo] = item.mainstayMchName;
        }
      });
      for (let p in tempMap) {
        list.push({
          mainstayNo: p,
          mainstayName: tempMap[p]
        })
      }
      this.accountQuoteInfo = list
    },
    exportInfo() {
      this.$refs.exportSelect.visible = true
    },
    async submitExportForm(form) {
      // 标准报表，传所有字段，自定义报表，将下标转换成数据
      await exportMerchantInfo({
        ...form,
        paramMap: {
          mchNo: this.mchNo
        }
      })
      this.$message.success('导出成功，请到导出列表进行导出');
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('1');
    },
    async onConfirmEdit(remark) {
      this.confirmVisible = false;
      const form = this.$refs.merchantInfo.$refs[this.tempActive];
      let formData = form.getFormData ? form.getFormData() : form['form'];
      await this.changeInfo(this.tempActive, {formData, remark});
    },
    async changeLeader({remark, contactPhone, contactName}) {
      const formData = {
        mchNo: this.mchNo,
        mchName: this.baseInfo.mchName,
        merchantType: this.baseInfo.merchantType,
        contactPhone,
        contactName
      };
      if (!this.processId) {
        await changeLeader({
          extObj: formData,
          participant: {},
          condition: {
            referenceName: this.baseInfo && this.baseInfo.mchName,
            referenceNo: this.baseInfo && this.baseInfo.mchNo,
          },
          remark,
        });
        this.$message.success('操作成功');
      } else {
        await this.editBusinessData(formData, remark)
      }
    },
    async changeSale({remark, salerId, salerName, agentNo, agentName}) {
      const formData = {
        mchNo: this.mchNo,
        mchName: this.baseInfo.mchName,
        salerId,
        salerName,
        agentNo,
        agentName,
        merchantType: this.baseInfo.merchantType
      };
      if (!this.processId) {
        await changeSale({
          extObj: formData,
          participant: {},
          condition: {
            referenceName: this.baseInfo && this.baseInfo.mchName,
            referenceNo: this.baseInfo && this.baseInfo.mchNo,
          },
          remark,
        });
        this.$message.success('操作成功');
      } else {
        await this.editBusinessData(formData, remark)
      }
    },
    async changeStatus(remark) {
      const {mchNo, mchName, mchStatus, merchantType} = this.baseInfo;
      this.loading = true;
      await changeStatus({
        extObj: {
          merchantType,
          mchNo,
          mchName,
          handleType: mchStatus == 100 ? 0 : 1,
        },
        participant: {},
        condition: {
          referenceName: this.baseInfo && this.baseInfo.mchName,
          referenceNo: this.baseInfo && this.baseInfo.mchNo,
        },
        remark,
      }).finally(() => {
        this.loading = false;
      });
      this.$message.success('操作成功');
    },
    toggleEditor(status) {
      this.showEditor = status;
      this.$store.dispatch('toggleSideBar', !status)
    },
    async getListAllWorkCategory() {
      const {data} = await listAllWorkCategory();
      data.forEach(item => {
        this.workCategoryList.push(item)
      });
      const list = convert(data, 0);
      list.forEach(item => {
        this.workCategoryOptions.push(item)
      });
    },
    async getListAllIndustryType() {
      const {data} = await listAllIndustryType();
      data.forEach(item => {
        this.industryList.push(item)
      });
      const list = convert(data, 0);
      list.forEach(item => {
        this.industryListOptions.push(item)
      });
    },
    async getChannelList() {
      const {data} = await getPayChannelList({
        pageCurrent: 1,
        pageSize: 100,
      });
      this.channelList = data.data;
    },
    onChangeLeader() {
      const {
        contactPhone = '',
        contactName = '',
        contactEmail = '',
      } = this.businessInfo;
      this.$refs.leaderDialog.form = {
        contactPhone,
        contactName,
        contactEmail,
        remark: ''
      };
      this.showLeaderChange = true;
    },
    onSaleChange() {
      const {
        salerId = '',
        salerName = '',
        agentNo = '',
        agentName = '',
      } = this.baseInfo;
      this.$refs.saleDialog.form = {
        salerId,
        salerName,
        agentNo,
        agentName,
        remark: '',
      };
      this.showSaleChange = true;
    },
    onEditClick() {
      this.tempActive = this.$refs.merchantInfo.activeName
      if (this.tempActive in tabMap) {
        this.formDisabled = false;
      }
    },
    onStatusChange() {
      this.showStatusChange = true;
    },
    onDeleteQuote() {
      this.tempActive = 'quoteInfo';
      this.confirmVisible = true;
    },
    async onTabChange({active = '', old}) {
      this.formDisabled = true;
      this.nextActive = active;
      this.tempActive = old;
      this.onSave();
    },
    async changeInfo(param, {formData, remark = ''}) {
      this.loading = true;
      if (param !== 'quoteInfo') {
        formData.merchantType = this.baseInfo.merchantType;
      }
      if (tabMap[param].needFlow) {
        if (param !== 'quoteInfo') {
          formData.mchName = this.baseInfo.mchName;
        }
        // 需要审批
        if (!this.processId) {
          await this.startChangeFlow(param, formData, remark);
        } else {
          return this.editBusinessData(formData, remark);
        }
        this.$message.success('操作成功');
      } else {
        // 不需要审批
        formData.mchNo = this.baseInfo.mchNo;
        await this.editData(formData)
      }
      this.loading = false;
      this.formDisabled = true;
      if (this.nextActive) {
        this.$refs.merchantInfo.changeTab(this.nextActive)
      }
    },
    async startChangeFlow(param, formData, remark) {
      await tabMap[param]['api']({
        extObj: formData,
        participant: {},
        condition: {
          referenceName: this.baseInfo && this.baseInfo.mchName,
          referenceNo: this.baseInfo && this.baseInfo.mchNo,
        },
        remark,
      }).finally(() => {
        this.loading = false;
      });
      this.tempActive = '';
    },
    async editBusinessData(formData, remark) {
      await editBusinessData({
        extInfo: JSON.stringify(formData),
        commonFlowId: this.processId,
        taskId: this.taskId,
        remark,
      }).finally(() => this.loading = false);
      this.$message.success('更新成功');
      this.$router.push({
        path: '/waitingHandle/detailProcess',
        query: {
          processId: this.processId,
          taskId: this.taskId,
        }
      })
    },
    async editData(formData) {
      this.loading = true;
      const activeTab = this.tempActive || this.$refs.merchantInfo.activeName;
      let api = tabMap[activeTab]['api'];
      if (Array.isArray(api)) {
        api = api[Number(this.isMerchant)]
      }
      const {data} = await api(formData).finally(() => this.loading = false);
      this.tempActive = '';
      data && this.$message.success(data);
      this.getMerchantAllInfo();
    },
    async onSave() {
      if (tabMap[this.tempActive].needFlow) { // 需要发起流程
        return this.confirmVisible = true;
      } else {
        // 不需要发起流程
        let form = this.$refs.merchantInfo.$refs[this.tempActive];
        let formData = form.getFormData ? form.getFormData() : form['form'];
        this.changeInfo(this.tempActive, {formData});
      }
    },
    async onClickSave() {
      this.tempActive = this.$refs.merchantInfo.activeName;
      let form = this.$refs.merchantInfo.$refs[this.tempActive];
      const valid = await form.$refs['form'].validate().catch(e => e);
      if (!valid) return;
      this.onSave()
    },
    onCancel() {
      if (!this.formDisabled) {
        this.formDisabled = true;
      } else {
        this.$router.back();
      }
    },
    async getAllInvoiceCate() {
      const {data} = await getAllInvoiceCategory()
      this.allInvoiceCate = data
    },
    async getHandleList({current = 1, size = 10}) {
      const res = await getHandleList({
        employerNo: this.mchNo,
        pageCurrent: current,
        pageSize: size,
        workType: 101,
      });
      this.historyInfo = res.data;
      return res
    }
  }
}
</script>

<style
  lang="scss"
  scoped
>
.merchant-info-box {
  padding-bottom: 100px;
  padding-right: 100px;

  &.show-editor {
    padding-right: 350px;
  }

  .merchant-base-info {
    background: #fff;
    padding: 16px;
    border-radius: 8px;
  }

  .merchant-base-name {
    font-size: 16px;
  }

  .merchant-arrow {
    display: inline-block;
    vertical-align: middle;
    padding: 3px 4px;
    margin-left: 8px;
    background: #eee;
    cursor: pointer;
  }

  .info-container {
    margin-top: -15px;
  }
}
</style>
