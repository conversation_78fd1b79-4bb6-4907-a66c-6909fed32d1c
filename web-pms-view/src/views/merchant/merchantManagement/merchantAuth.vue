<template>
  <div class="merchant-page-container">
    <div
      class="header-container"
      v-sticky="{ zIndex: 100, stickyTop: 50 }"
    >
      <el-steps
        :active="activeStep"
        align-center
      >
        <el-step title="填写主体信息"></el-step>
        <el-step title="填写经营信息"></el-step>
        <el-step title="填写账户信息"></el-step>
        <el-step title="审核与公示"></el-step>
      </el-steps>
    </div>
    <div class="page-container">
      <div class="content-container">
        <div>
          <!--step_1-->
          <el-form
            ref="form_1"
            label-width="300px"
            :model="form_1"
            :rules="rules"
            class="step_1"
            v-show="activeStep == 1"
          >
            <!--企业信息-->
            <div class="company_info">
              <p class="subTitle">企业信息</p>
              <div>
                <el-form-item
                  label="营业执照"
                  prop="businessLicenseFileUrl"
                >
                  <el-upload
                    v-toggle="form_1.businessLicenseFileUrl"
                    class="upload-demo"
                    data-type="businessLicenseFileUrl"
                    :action="baseUrl + '/file/upload'"
                    :headers="uploadHeader"
                    list-type="picture-card"
                    accept=".bmp,.png,.gif,.jpg,.jpeg,.gif,.pdf"
                    :limit="1"
                    :file-list="businessLicenseFileUrlShow"
                    :before-upload="validateUploadFile({img: true, pdf: true, size: 6})"
                    :on-remove="(file, fileList) => handleRemove(file, fileList, 'businessLicenseFileUrl')"
                    :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'businessLicenseFileUrl')"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                    <template v-slot:file="{file}">
                      <div
                        class="fileBg"
                        v-if="isPdf(file.name)"
                      >
                        <span class="file-name">{{ file.name }}</span>
                        <span class="el-upload-list__item-actions">
                          <span
                            class="el-upload-list__item-delete"
                            @click="handleFileRemove(file, 'businessLicenseFileUrl')"
                          >
                            <i class="el-icon-delete"></i>
                          </span>
                        </span>
                      </div>
                    </template>

                    <div
                      slot="tip"
                      class="el-upload__tip"
                    >请上传彩色原件或加盖公司公章的复印件，小于6M，文件格式为pdf、bmp、png、jpeg、jpg或gif。
                      <el-popover
                        trigger="click"
                        width="300"
                      >
                        <el-button
                          type="text"
                          slot="reference"
                        >
                          查看示例
                        </el-button>
                        <div>
                          <img
                            class="example-image"
                            src="@/assets/example-1.png"
                          >
                        </div>
                      </el-popover>
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
              <div>
                <el-form-item
                  label="企业名称："
                  prop="mchName"
                >
                  <el-input
                    v-model="form_1.mchName"
                    disabled
                  ></el-input>
                </el-form-item>
                <el-form-item
                  label="统一社会信用代码："
                  prop="taxNo"
                >
                  <el-input v-model="form_1.taxNo"></el-input>
                  <p class="color-gray form-tip">请输入营业执照上18位统一社会信用代码</p>
                </el-form-item>
                <el-form-item
                  label="注册地址："
                  prop="registerAddrDetail"
                >
                  <address-select
                    :province.sync="form_1.registerAddrProvince"
                    :city.sync="form_1.registerAddrCity"
                    :town.sync="form_1.registerAddrTown"
                    :default-address="defaultAddress"
                  ></address-select>
                  <el-input
                    v-model="form_1.registerAddrDetail"
                    style="margin-top: 8px;"
                  ></el-input>
                  <p class="color-gray form-tip">与营业执照登记住所一致</p>
                </el-form-item>
                <el-form-item
                  label="注册资本（万）："
                  prop="registerAmount"
                >
                  <el-input v-model="form_1.registerAmount"></el-input>
                </el-form-item>
                <el-form-item
                  label="经营范围："
                  prop="managementScope"
                >
                  <el-input
                    type="textarea"
                    :rows="4"
                    v-model="form_1.managementScope"
                  >
                  </el-input>
                  <p class="color-gray form-tip">
                    与企业工商营业执照上一致
                  </p>
                </el-form-item>
                <el-form-item
                  label="营业期限："
                  prop="managementValidityDateType"
                >
                  <el-radio-group v-model="form_1.managementValidityDateType">
                    <el-radio
                      v-for="item in $dict('ValidityDateTypeEnum')"
                      :key="item.code"
                      :label="Number(item.code)"
                    >{{ item.desc }}
                    </el-radio>
                  </el-radio-group>
                  <div>
                    <date-picker
                      v-if="$dictCode('ValidityDateTypeEnum',form_1.managementValidityDateType).desc == '区间有效'"
                      :timeRange="manageTimeRange"
                      :is-show-fast-time="false"
                      :use-option="false"
                      type="daterange"
                      @change="val => getTimeRange(val, 'managementTerm')"
                    ></date-picker>
                  </div>

                  <div v-if="$dictCode('ValidityDateTypeEnum',form_1.managementValidityDateType).desc == '长期有效'">
                    <el-date-picker
                      type="date"
                      v-model="form_1.managementTermBegin"
                      placeholder="选择起始时间"
                      :value-format="'yyyy-MM-dd'"
                    ></el-date-picker>
                  </div>
                </el-form-item>
              </div>
            </div>
            <!--法定代表人-->
            <div class="company_info">
              <p class="subTitle">法定代表人信息</p>
              <div>
                <el-form-item
                  label="证件类型："
                  prop="certificateType"
                >
                  <el-select
                    clearable
                    v-model="form_1.certificateType"
                  >
                    <el-option
                      v-for="item in $dict('CertificateTypeEnum')"
                      :key="item.code"
                      :value="Number(item.code)"
                      :label="item.desc"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="证件照："
                  prop="idFileType"
                >
                  <el-radio-group v-model="form_1.idFileType">
                    <el-radio :label="100">彩色原件</el-radio>
                    <el-radio :label="101">复印件</el-radio>
                  </el-radio-group>
                  <div v-show="form_1.idFileType == '100'">
                    <p>
                      人像面
                      <el-popover
                        width="300"
                        trigger="click"
                      >
                        <img
                          class="example-image"
                          src="@/assets/example-2.png"
                        >
                        <el-button
                          slot="reference"
                          type="text"
                        >查看示例
                        </el-button>
                      </el-popover>
                    </p>
                    <el-upload
                      v-toggle="form_1.idCardHeadFileUrl"
                      key="idCardHeadFileUrl"
                      class="upload-demo"
                      data-type="idCardHeadFileUrl"
                      :action="baseUrl + '/file/upload'"
                      :headers="uploadHeader"
                      list-type="picture-card"
                      accept=".png,.gif,.jpg,.bmp"
                      :limit="1"
                      :file-list="idCardHeadFileUrlShow"
                      :before-upload="beforeAvatarUpload"
                      :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardHeadFileUrl')"
                      :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardHeadFileUrl')"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                    <p>
                      国徽面
                      <el-popover
                        width="300"
                        trigger="click"
                      >
                        <img
                          class="example-image"
                          src="@/assets/example-3.png"
                        >
                        <el-button
                          slot="reference"
                          type="text"
                        >查看示例
                        </el-button>
                      </el-popover>
                    </p>
                    <el-upload
                      v-toggle="form_1.idCardEmblemFileUrl"
                      key="idCardEmblemFileUrl"
                      class="upload-demo"
                      data-type="idCardEmblemFileUrl"
                      :action="baseUrl + '/file/upload'"
                      :headers="uploadHeader"
                      list-type="picture-card"
                      accept=".png,.gif,.jpg,.bmp"
                      :limit="1"
                      :file-list="idCardEmblemFileUrlShow"
                      :before-upload="beforeAvatarUpload"
                      :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardEmblemFileUrl')"
                      :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardEmblemFileUrl')"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                  </div>
                  <div v-show="form_1.idFileType == '101'">
                    <p>加盖公司公章的法人身份证复印件
                      <el-popover
                        width="300"
                        trigger="click"
                      >
                        <img
                          class="example-image"
                          src="@/assets/idCardCopyFileExample.png"
                        >
                        <el-button
                          slot="reference"
                          type="text"
                        >查看示例
                        </el-button>
                      </el-popover>
                    </p>
                    <el-upload
                      v-toggle="form_1.idCardCopyFileUrl"
                      key="idCardCopyFileUrl"
                      class="upload-demo"
                      data-type="idCardCopyFileUrl"
                      :action="baseUrl + '/file/upload'"
                      :headers="uploadHeader"
                      list-type="picture-card"
                      accept=".png,.gif,.jpg,bmp"
                      :limit="1"
                      :file-list="idCardCopyFileUrlShow"
                      :before-upload="beforeAvatarUpload"
                      :on-remove="(file, fileList) => handleRemove(file, fileList, 'idCardCopyFileUrl')"
                      :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'idCardCopyFileUrl')"
                    >
                      <i class="el-icon-plus avatar-uploader-icon"></i>
                    </el-upload>
                  </div>
                </el-form-item>
              </div>
              <div>
                <el-form-item
                  label="法定代表人姓名："
                  prop="legalPersonName"
                >
                  <el-input v-model="form_1.legalPersonName"></el-input>
                </el-form-item>
                <el-form-item
                  label="证件号码："
                  prop="certificateNumber"
                  :rules="certificateRules"
                >
                  <el-input v-model="form_1.certificateNumber"></el-input>
                </el-form-item>
                <el-form-item
                  label="证件有效期："
                  prop="certificateValidityDateType"
                >
                  <el-radio-group v-model="form_1.certificateValidityDateType">
                    <el-radio
                      v-for="item in $dict('ValidityDateTypeEnum')"
                      :key="item.code"
                      :label="Number(item.code)"
                    >{{ item.desc }}
                    </el-radio>
                  </el-radio-group>
                  <div>
                    <date-picker
                      v-if="$dictCode('ValidityDateTypeEnum',form_1.certificateValidityDateType).desc == '区间有效'"
                      type="daterange"
                      @change="val => getTimeRange(val, 'certificateTerm')"
                      :is-show-fast-time="false"
                      :timeRange="certificateTimeRange"
                      :use-option="false"
                    ></date-picker>
                  </div>

                  <div v-if="$dictCode('ValidityDateTypeEnum',form_1.certificateValidityDateType).desc == '长期有效'">
                    <el-date-picker
                      v-model="form_1.certificateTermBegin"
                      type="date"
                      placeholder="请选择起始时间"
                      :value-format="'yyyy-MM-dd'"
                    ></el-date-picker>
                  </div>
                </el-form-item>
              </div>
            </div>
          </el-form>

          <!--step2-->
          <el-form
            ref="form_2"
            label-width="300px"
            :model="form_2"
            :rules="rules"
            class="step_2"
            v-show="activeStep == 2"
          >
            <!--联系人信息-->
            <div class="company_info">
              <p class="subTitle">联系人信息</p>
              <div>
                <el-form-item
                  label="负责人姓名："
                  prop="contactName"
                >
                  <el-input
                    v-model="form_2.contactName"
                    :disabled="!editAccess"
                  ></el-input>
                  <p class="color-gray form-tip">
                    请填写公司运营负责人
                  </p>
                </el-form-item>
                <el-form-item
                  label="负责人手机号："
                  prop="contactPhone"
                >
                  <el-input
                    v-model="form_2.contactPhone"
                    :disabled="!editAccess"
                  ></el-input>
                  <el-button
                    type="text"
                    @click="() => { editAccess = true }"
                  >更改
                  </el-button>
                </el-form-item>
                <el-form-item
                  label="客服电话："
                  prop="servicePhone"
                >
                  <el-input v-model="form_2.servicePhone"></el-input>
                  <p class="color-gray form-tip">
                    请注意填写格式，举例（座机：0660-********；手机：***********；400电话：**********
                  </p>
                </el-form-item>
              </div>
            </div>
            <!--经营信息-->
            <div class="bankCard_info">
              <p class="subTitle">经营信息</p>
              <div>
                <el-form-item
                  label=" 企业简称："
                  prop="shortName"
                >
                  <el-input v-model="form_2.shortName"></el-input>
                </el-form-item>
                <el-form-item
                  label="实际经营地址："
                  prop="managementAddrDetail"
                >
                  <address-select
                    :province.sync="form_2.managementAddrProvince"
                    :city.sync="form_2.managementAddrCity"
                    :town.sync="form_2.managementAddrTown"
                    :default-address="defaultManageAddress"
                  ></address-select>

                  <el-input v-model="form_2.managementAddrDetail"></el-input>
                </el-form-item>
                <el-form-item
                  label="门头照片"
                  prop="doorPhotoFileUrl"
                >
                  <el-upload
                    v-toggle="form_2.doorPhotoFileUrl"
                    class="upload-demo"
                    data-type="doorPhotoFileUrl"
                    :action="baseUrl + '/file/upload'"
                    :headers="uploadHeader"
                    list-type="picture-card"
                    accept=".png,.gif,.jpg,bmp"
                    :limit="1"
                    :file-list="doorPhotoFileUrlShow"
                    :before-upload="beforeAvatarUpload"
                    :on-remove="(file, fileList) => handleRemove(file, fileList, 'doorPhotoFileUrl')"
                    :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'doorPhotoFileUrl')"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                    <div
                      slot="tip"
                      class="el-upload__tip"
                    >最多1张，单张照片不超过6M。
                    </div>
                  </el-upload>
                </el-form-item>
                <el-form-item
                  label="办公内景照片"
                  prop="workIndoorFileUrl"
                >
                  <el-upload
                    v-toggle="form_2.workIndoorFileUrl"
                    data-type="workIndoorFileUrl"
                    class="upload-demo"
                    :action="baseUrl + '/file/upload'"
                    :headers="uploadHeader"
                    list-type="picture-card"
                    accept=".png,.gif,.jpg,bmp"
                    :limit="1"
                    :file-list="workIndoorFileUrlShow"
                    :before-upload="beforeAvatarUpload"
                    :on-remove="(file, fileList) => handleRemove(file, fileList, 'workIndoorFileUrl')"
                    :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'workIndoorFileUrl')"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                    <div
                      slot="tip"
                      class="el-upload__tip"
                    >最多1张，单张照片不超过6M。
                    </div>
                  </el-upload>
                </el-form-item>
                <el-form-item
                  label="前台照片"
                  prop="receptionFileUrl"
                >
                  <el-upload
                    v-toggle="form_2.receptionFileUrl"
                    data-type="receptionFileUrl"
                    class="upload-demo"
                    :action="baseUrl + '/file/upload'"
                    :headers="uploadHeader"
                    list-type="picture-card"
                    accept=".png,.gif,.jpg,bmp"
                    :limit="1"
                    :file-list="receptionFileUrlShow"
                    :before-upload="beforeAvatarUpload"
                    :on-remove="(file, fileList) => handleRemove(file, fileList, 'receptionFileUrl')"
                    :on-success="(response, file, fileList) => handleSuccess(response, file, fileList, 'receptionFileUrl')"
                  >
                    <i class="el-icon-plus avatar-uploader-icon"></i>
                    <div
                      slot="tip"
                      class="el-upload__tip"
                    >最多1张，单张照片不超过6M。
                    </div>
                  </el-upload>
                </el-form-item>
              </div>
            </div>
            <!--开票信息-->
            <div class="invoice_info">
              <p class="subTitle">开票信息</p>
              <el-form-item
                label="纳税人类型："
                prop="taxPayerType"
              >
                <el-radio-group v-model="form_2.taxPayerType">
                  <el-radio
                    v-for="(item, index) in $dict('TaxPayerTypeEnum')"
                    :key="index"
                    :label="Number(item.code)"
                  >
                    {{ item.desc }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item
                label="开票地址："
                prop="invoiceAddress"
              >
                <el-input
                  v-model="form_2.invoiceAddress"
                  type="textarea"
                  :rows="4"
                ></el-input>
              </el-form-item>
              <el-form-item
                label="联系电话："
                prop="invoicePhone"
              >
                <el-input v-model="form_2.invoicePhone"></el-input>
              </el-form-item>
              <el-form-item
                label="开票银行："
                prop="invoiceBankName"
              >
                <el-input v-model="form_2.invoiceBankName"></el-input>
              </el-form-item>
              <el-form-item
                label="开票账户："
                prop="invoiceAccountNo"
              >
                <el-input v-model="form_2.invoiceAccountNo"></el-input>
              </el-form-item>
            </div>

            <!-- 企业主要人员 -->
            <div class="personnels_info">
              <p class="subTitle">
                企业主要人员
                <el-button
                  type="text"
                  @click="addPeople"
                >增加
                </el-button>
              </p>
              <el-table :data="form_2.personnels">
                <el-table-column
                  label="姓名"
                  prop="name"
                >
                  <template v-slot="{row, $index, column}">
                    <div v-if="$index == 0">
                      {{ row[column.property] }}
                    </div>
                    <div v-else>
                      <el-input
                        v-model="row[column.property]"
                        clearable
                      ></el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  label="身份证号码"
                  prop="idCardNumber"
                >
                  <template v-slot="{row, $index, column}">
                    <div v-if="$index == 0">
                      {{ row[column.property] }}
                    </div>
                    <div v-else>
                      <el-input
                        v-model="row[column.property]"
                        clearable
                      ></el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  label="职务"
                  prop="position"
                >
                  <template v-slot="{row, $index, column}">
                    <div v-if="$index == 0">
                      法人
                    </div>
                    <div v-else>
                      <el-input
                        v-model="row[column.property]"
                        clearable
                      ></el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template v-slot="{$index}">
                    <el-button
                      type="text"
                      @click="deletePeople($index)"
                      style="color: #f00;"
                      v-if="$index > 0"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form>

          <!--step_3-->
          <el-form
            ref="form_3"
            label-width="300px"
            :model="form_3"
            :rules="rules"
            class="step_3"
            v-show="activeStep == 3"
          >
            <!--银行卡信息-->
            <div class="bankCard_info">
              <p class="subTitle">银行卡信息</p>
              <div>
                <el-form-item
                  label="银行卡号："
                  prop="accountNo"
                >
                  <el-input v-model="form_3.accountNo"></el-input>
                </el-form-item>
                <el-form-item
                  label="联行号："
                  prop="bankChannelNo"
                >
                  <el-input
                    v-model="form_3.bankChannelNo"
                    prefix-icon="el-icon-search"
                    @click.native="searchBankNumberInfo"
                  >
                  </el-input>
                </el-form-item>
                <el-form-item
                  label="开户银行："
                  prop="bankName"
                >
                  <el-input
                    v-model="form_3.bankName"
                    disabled
                  ></el-input>
                </el-form-item>
              </div>
            </div>

            <!-- <div
              class="accounts_info"
              v-if="isMerchant"
            >
              <p class="subTitle">账户信息</p>
              <div
                v-for="(item, index) in mainstayList"
                :key="index"
              >
                <el-form-item label="代征主体：">{{ item.mainstayMchName }}</el-form-item>
                <el-form-item
                  label="开通发放方式："
                  prop="accounts"
                  ref="accounts"
                >
                  <template v-for="(i, key) in $dict('ChannelTypeEnum')">
                    <el-checkbox
                      v-model="channelSelect[item.mainstayMchNo][key]"
                      :key="key"
                      :true-label="1"
                      :false-label="0"
                      @change="onChannelTypeChange($event, i, index)"
                    >
                      {{ i.desc }}
                    </el-checkbox>
                  </template>
                </el-form-item>
                <div>
                  <el-form-item
                    v-for="(account, num) in form_3.accounts[item.mainstayMchNo]"
                    :key="num"
                    :label="$dictCode('ChannelTypeEnum', account.channelType).desc + '通道'"
                  >
                    <el-select
                      v-if="account"
                      v-model="account['payChannelNo']"
                      @change="handlePayChannelChange(account, $event)"
                    >
                      <el-option
                        v-for="(i, key) in payChannel[account.channelType - 1]"
                        :key="key"
                        :value="i.payChannelNo"
                        :label="i.payChannelName"
                      ></el-option>
                    </el-select>
                    <p class="color-gray form-tip">请选择提供{{ $dictCode('ChannelTypeEnum', account.channelType).desc }}发放服务的第三方支付机构</p>
                  </el-form-item>
                </div>
              </div>
            </div> -->

            <el-footer class="footer-container">
              <div class="protocal-container">
                <el-checkbox v-model="protocalCheck">
                  <span class="el-checkbox-label">
                    我已仔细阅读并同意
                  </span>
                </el-checkbox>
                <span
                  class="protocal-link"
                  @click="getProtocal(1)"
                >《汇聚支付分账服务协议》</span>
                <span
                  class="protocal-link"
                  @click="getProtocal(2)"
                >《平台商户入驻框架协议》</span>
              </div>
            </el-footer>
          </el-form>

          <el-footer
            class="form-btn-group"
            v-show="activeStep < 4"
          >
            <el-button
              type="primary"
              @click="nextStep"
              :disabled="activeStep == 3 && !protocalCheck"
            >
              {{ activeStep == 3 ? '提交' : '保存并下一步' }}
            </el-button>
            <el-button @click="backStep">{{ activeStep == 1 ? '取消' : '上一步' }}
            </el-button>
          </el-footer>

          <!--step_4-->
          <div
            class="step_4"
            v-show="activeStep == 4"
          >
            <svg-icon icon-class="waiting"></svg-icon>
            <p class="result-title">认证审核中</p>
            <p>资料审核中，审核结果将在2个工作日内通知您</p>
            <div class="result-detail">
              <p class="result-detail-title">主体信息认证</p>
              <p>工作流水号：{{ result.commonFlowId }}</p>
              <p>发起人：{{ result.submitName }}</p>
              <p>创建时间：{{ result.createTime }}</p>
            </div>
            <div class="footer-container">
              <el-button
                type="primary"
                @click="$router.push('/merchant/merchantManagement/merchant')"
              >返回
              </el-button>
            </div>
          </div>

        </div>
      </div>

      <!--  表格弹窗  -->
      <bankNumberInfo
        ref="bankNumberInfo"
        @closeDialog="closeBankNumberInfoDialog"
      >
      </bankNumberInfo>
    </div>

    <!--审批意见-->
    <flow-opinion
      :visible.sync="flowVisible"
      @change="flowConfirm"
    ></flow-opinion>

  </div>
</template>

<script>
import { getPayChannelList } from "@/api/levy";
import {
  merchantAuth,
  updateBankCardInfo,
  updateMerchantMain,
  changeLeader,
  getInvoiceList,
  getQuote
} from '@/api/merchant'
import { getStaffListByDepartment } from '@/api/system'
import { validateParams } from '@/utils/validate'
import { getCityList, getProvinceList, getTownList } from '@/api/common'
import { clearVoid, toPromise } from '@/utils';
import { editBusinessData } from '@/api/flow';
import bankNumberInfo from '@/components/BankNumberInfo';
import FlowOpinion from '@/components/Flow/FlowOpinion'

import VueSticky from 'vue-sticky'
import '@/styles/merchant-form.scss'

/**
 * [beforeunloadHandler 浏览器关闭时进行用户提示]
 * @return {[type]} [description]
 */
 function beforeunloadHandler(e) {
    e = e || window.event;
    if (e) {
      e.preventDefault();
      e.returnValue = "系统可能不会保存您做的的更改！";
    }
    return "系统可能不会保存您做的的更改！";
}

export default {
  name: 'merchantAuth',
  components: {
    bankNumberInfo,
    FlowOpinion,
  },
  directives: {
    'sticky': VueSticky,
  },
  data() {
    const _self = this;
    return {
      isEdit: false,
      isShowTip: false,
      activeStep: 1,
      industryList: [], // 行业类别
      industryListOptions: [],
      workCategoryList: [], // 工作类目
      workCategoryOptions: [], // 工作类目树
      staffList: [],
      mainstayList: [], // 代征主体
      provinceList: [],
      managementCityList: [],
      managementTownList: [],
      registerCityList: [],
      registerTownList: [],

      businessLicenseFileUrlShow: [], // el-upload插件绑定数组xxxShow，作用于回显图片
      receptionFileUrlShow: [],
      workIndoorFileUrlShow: [],
      doorPhotoFileUrlShow: [],
      idCardCopyFileUrlShow: [],
      idCardEmblemFileUrlShow: [],
      idCardHeadFileUrlShow: [],
      form_1: {
        mchNo: this.mchNo,
        mchName: '',
        taxNo: '',
        registerAddrProvince: '',
        registerAddrCity: '',
        registerAddrTown: '',
        registerAddrDetail: '',
        registerAmount: '',
        managementScope: '',
        managementValidityDateType: '',
        managementTermBegin: '',
        managementTermEnd: '',
        businessLicenseFileUrl: '',
        certificateType: '',
        idFileType: '',
        idCardHeadFileUrl: '', // 人像面
        idCardEmblemFileUrl: '', // 国徽面
        idCardCopyFileUrl: '',
        legalPersonName: '',
        certificateNumber: '',
        certificateValidityDateType: '',
        certificateTermBegin: '',
        certificateTermEnd: '',
      },
      form_2: {
        contactName: '',
        contactPhone: '',
        servicePhone: '',
        shortName: '',
        managementAddrProvince: '',
        managementAddrCity: '',
        managementAddrTown: '',
        managementAddrDetail: '',
        doorPhotoFileUrl: '', // 门头照
        workIndoorFileUrl: '',
        receptionFileUrl: '',
        // 开票信息
        taxPayerType: '',
        invoiceAddress: '',
        invoiceBankName: '',
        invoiceAccountNo: '',
        invoicePhone: '',
        // 企业人员
        personnels: [],
      },
      form_3: {
        accountNo: '',
        bankChannelNo: '',
        bankName: '',
        accounts: {}
      },
      invoiceCategoryList: [],
      form: {},
      rules: {
        // step 1
        mchName: [
          { required: true, message: "请输入企业名称", trigger: "blur" },
          // { validator: validateParams({type: 'Chinese', msg: '请输入正确的企业名称'}), trigger: 'blur' },
          {
            validator: validateParams({ type: 'Length', max: '30', msg: '企业名称最多可输入15个字' }),
            trigger: 'blur'
          },
        ],
        taxNo: [
          { required: true, message: '请输入统一社会信用代码或纳税人识别号' },
          {
            validator: validateParams({
              type: 'Reg',
              pattern: /^[a-zA-Z0-9]{1,20}$/,
              msg: '统一社会信用代码最多可输入20个字'
            }), trigger: 'blur'
          }
        ],
        registerAddrDetail: [
          { required: true, message: '请选择注册地址', trigger: 'blur' },
        ],
        registerAmount: [
          { required: true, message: "请输入注册资本", trigger: "blur" },
          { validator: validateParams({ type: 'Number', msg: '请输入正确的金额' }), trigger: 'blur' },
          {
            validator: validateParams({ type: 'Length', max: '20', msg: '注册资本最多可输入20个字' }),
            trigger: 'blur'
          },
        ],
        managementScope: [
          { required: true, message: "请输入经营范围", trigger: "blur" },
          {
            validator: validateParams({ type: 'Length', max: '500', msg: '经营范围最多可输入500个字' }),
            trigger: 'blur'
          },
        ],
        managementValidityDateType: [
          { required: true, message: "请选择营业期限", trigger: "change" },
          {
            validator: (rule, value, cb) => {
              let result = true;
              if (value == 1) {
                if (!_self.form_1.managementTermBegin || !_self.form_1.managementTermEnd) {
                  result = false
                }
              } else if (value == 2) {
                if (!_self.form_1.managementTermBegin) {
                  result = false;
                }
              }
              if (result) {
                cb()
              } else {
                cb(new Error('请选择营业期限'))
              }
            }, trigger: 'blur'
          }
        ],

        businessLicenseFileUrl: [
          { required: true, message: "请上传营业执照", trigger: "change" },
          { validator: validateParams({ type: 'Length', min: 1 }) }
        ],

        accountNo: [
          { required: true, message: "请输入银行卡号", trigger: "blur" },
        ],
        bankChannelNo: [
          { required: true, message: "请输入联行号", trigger: "change" },
        ],
        bankName: [
          { required: true, message: "请输入开户银行", trigger: "blur" },
        ],

        // step 2
        certificateType: [
          { required: true, message: "请选择证件类型", trigger: "change" },
        ],
        idFileType: [
          { required: true, message: "请上传证件照", trigger: "change" },
          {
            validator: function (rule, value, callback) {
              if (value == '100') {
                if (_self.form_1.idCardHeadFileUrl.length == 0 && _self.form_1.idCardEmblemFileUrl.length == 0) {
                  callback(new Error('请上传证件照'))
                } else {
                  callback()
                }
              } else if (value == '101') {
                if (_self.form_1.idCardCopyFileUrl.length == 0) {
                  callback(new Error('请上传证件照'))
                } else {
                  callback()
                }
              }
            }, trigger: 'blur'
          }
        ],

        legalPersonName: [
          { required: true, message: "请输入法定代表人姓名", trigger: "blur" },
          { validator: validateParams({ type: 'Chinese', msg: '请输入正确的姓名' }), trigger: 'blur' },
          { validator: validateParams({ type: 'Length', max: '30', msg: '姓名最多可输入15个字' }), trigger: 'blur' },
        ],
        certificateValidityDateType: [
          { required: true, message: "请选择证件有效期", trigger: "change" },
          {
            validator: (rule, value, cb) => {
              let result = true;
              if (value == 1) {
                if (!_self.form_1.certificateTermBegin || !_self.form_1.certificateTermEnd) {
                  result = false
                }
              } else if (value == 2) {
                if (!_self.form_1.certificateTermBegin) {
                  result = false;
                }
              }
              if (result) {
                cb()
              } else {
                cb(new Error('请选择证件有效期'))
              }
            }, trigger: 'blur'
          }
        ],

        // step 3
        contactName: [
          { required: true, message: "请输入负责人姓名", trigger: "blur" },
          { validator: validateParams({ type: 'Chinese', msg: '请输入正确的姓名' }), trigger: 'blur' },
          { validator: validateParams({ type: 'Length', max: '15', msg: '姓名最多可输入15个字' }), trigger: 'blur' },
        ],
        contactPhone: [
          { required: true, message: "请输入负责人手机号码", trigger: "blur" },
          { validator: validateParams({ type: 'Phone', msg: '请输入正确的手机号码' }), trigger: 'blur' },
        ],
        servicePhone: [
          { required: true, message: "请输入客服电话", trigger: "blur" },
          {
            validator: validateParams({ type: 'Reg', pattern: /[0-9-]+/, msg: '请输入正确的客服电话' }),
            trigger: 'blur'
          },
        ],

        shortName: [
          { required: true, message: "请输入企业简称", trigger: "blur" },
          { validator: validateParams({ type: 'Chinese', msg: '请输入正确的企业简称' }), trigger: 'blur' },
          {
            validator: validateParams({ type: 'Length', max: '15', msg: '企业简称最多可输入15个字' }),
            trigger: 'blur'
          },
        ],
        managementAddrDetail: [
          { required: true, message: "请选择实际经营地址", trigger: 'blur' },
        ],
        doorPhotoFileUrl: [
          { required: true, message: '请上传公司照片' },
          { validator: validateParams({ type: 'Length', min: 1 }) }
        ],
        workIndoorFileUrl: [
          { required: true, message: '请上传公司照片' },
          { validator: validateParams({ type: 'Length', min: 1 }) }
        ],
        receptionFileUrl: [
          { required: true, message: '请上传公司照片' },
          { validator: validateParams({ type: 'Length', min: 1 }) }
        ],
        taxPayerType: [
          { required: true, message: '请选择纳税人类型', trigger: 'blur' },
        ],
        registerAddrInfo: [
          { required: true, message: '请输入单位注册地址及电话', trigger: 'blur' },
        ],
        invoiceAddress: [
          { required: true, message: '请输入开票地址', trigger: 'blur' },
        ],
        invoiceBankName: [
          { required: true, message: '请输入开票银行', trigger: 'blur' },
        ],
        invoiceAccountNo: [
          { required: true, message: '请输入开票银行账户', trigger: 'blur' },
        ],
        invoicePhone: [
          { required: true, message: '请输入开票联系电话', trigger: 'blur' },
        ],
        accounts: [
          { required: true, message: '请选择开通的发放方式', trigger: 'blur' },
          {
            validator: (rule, val, cb) => {
              let result = true;
              if (Object.keys(_self.form_3.accounts).length == 0) {
                result = false;
              }
              for (let mch in _self.form_3.accounts) {
                let accountList = _self.form_3.accounts[mch];
                if (accountList.length == 0) {
                  result = false;
                }
                for (let i = 0; i < accountList.length; i++) {
                  if (!accountList[i].payChannelNo) {
                    result = false;
                    break;
                  }
                }
                if (!result) {
                  break;
                }
              }
              if (!result) {
                cb(new Error('请选择通道'))
              } else {
                cb();
              }
            }, trigger: 'blur'
          }
        ]
      },
      // step 3
      result: '',

      editAccess: false, // 是否可以编辑负责人

      protocalCheck: false, // 是否勾选确认协议

      flowVisible: false, // 审批意见显示控制
      flowRemark: '',

      channelSelect: {},
      bankChannel: [],
      alipayChannel: [],
      wxChannel: [],

      defaultAddress: '',
      defaultManageAddress: '',

      imgList: [
        'businessLicenseFileUrl',
        'receptionFileUrl',
        'workIndoorFileUrl',
        'doorPhotoFileUrl',
        'idCardCopyFileUrl',
        'idCardEmblemFileUrl',
        'idCardHeadFileUrl',
      ]

    }
  },
  computed: {
    mchNo() {
      return this.$route.query.mchNo || ''
    },
    payChannel() {
      return [
        this.bankChannel,
        this.alipayChannel,
        this.wxChannel,
      ]
    },
    manageTimeRange() {
      if (this.form_1.managementTermBegin && this.form_1.managementTermEnd) {
        return [this.form_1.managementTermBegin, this.form_1.managementTermEnd];
      } else {
        return [];
      }
    },
    certificateTimeRange() {
      if (this.form_1.certificateTermBegin && this.form_1.certificateTermEnd) {
        return [this.form_1.certificateTermBegin, this.form_1.certificateTermEnd];
      } else {
        return [];
      }
    },
    certificateRules() {
      if (this.$dictCode('CertificateTypeEnum', this.form_1.certificateType).desc == '身份证') {
        return [
          { required: true, message: "请输入证件号码", trigger: "blur" },
          { validator: validateParams({ type: 'IdCard', msg: '请输入正确的证件号码' }), trigger: 'blur' },
        ]
      } else {
        return [
          { required: true, message: "请输入证件号码", trigger: "blur" },
          { validator: validateParams({ type: 'Length', msg: '请输入正确的证件号码', min: 1 }), trigger: 'blur' },
        ]
      }
    },
    isMerchant() {
      return this.mchNo.startsWith('M')
    },
    taskId() {
      return this.$route.query.taskId || '';
    },
    processId() {
      return this.$route.query.processId || ''
    }
  },
  watch: {
    activeStep: {
      handler(val) {
        if (val == 2) {
          if (!this.form_2.taxNo) {
            this.form_2.taxNo = this.form_1.taxNo;
          }
          if (!this.form_2.invoiceAddress) {
            this.form_2.invoiceAddress = this.form_1.registerAddrProvince + this.form_1.registerAddrCity + this.form_1.registerAddrTown + this.form_1.registerAddrDetail
          }
          if (!this.form_2.invoiceAccountNo) {
            this.form_2.invoiceAccountNo = this.form_1.accountNo
          }
          if (!this.form_2.invoiceBankName) {
            this.form_2.invoiceBankName = this.form_1.bankName
          }
        }
      }
    }
  },
  async mounted() {
    window.addEventListener("beforeunload", beforeunloadHandler)
    this.getPayChannelList();
    getStaffListByDepartment({ departmentId: 0 }).then(
      (response) => {
        this.staffList = response.data;
      }
    );
    getInvoiceList({ mchNo: this.mchNo }).then(res => {
      res.data.forEach(item => {
        this.invoiceCategoryList.push(...item.invoiceCategoryList)
      })
    });
    if (sessionStorage.getItem('merchantInfo')) {
      this.$set(this, 'form', JSON.parse(sessionStorage.getItem('merchantInfo')))
    }
    if (sessionStorage.getItem('mainInfo')) {
      this.form = JSON.parse(sessionStorage.getItem('mainInfo'));
      this.isEdit = true;
      // 处理图片
      const businessLicenseFile = await this.formatFileMsg(this.form.businessLicenseFileUrl);
      this.businessLicenseFileUrlShow = this.form.businessLicenseFileUrl
        ? [{
          url: businessLicenseFile.fileUrl,
          name: this.isPdf(this.form.businessLicenseFileUrl) ? '营业执照.pdf' : '',
          origin: this.form.businessLicenseFileUrl
        }]
        : [];
      
      const idCardHeadFile = await this.formatFileMsg(this.form.idCardHeadFileUrl);
      this.idCardHeadFileUrlShow = this.form.idCardHeadFileUrl ? [{
        url: idCardHeadFile.fileUrl,
        origin: this.form.idCardHeadFileUrl
      }] : [];

      const idCardEmblemFile = await this.formatFileMsg(this.form.idCardEmblemFileUrl);
      this.idCardEmblemFileUrlShow = this.form.idCardEmblemFileUrl ? [{
        url: idCardEmblemFile.fileUrl,
        origin: this.form.idCardEmblemFileUrl
      }] : [];

      const idCardCopyFile = await this.formatFileMsg(this.form.idCardCopyFileUrl);
      this.idCardCopyFileUrlShow = this.form.idCardCopyFileUrl ? [{
        url: idCardCopyFile.fileUrl,
        origin: this.form.idCardCopyFileUrl
      }] : [];

      const doorPhotoFile = await this.formatFileMsg(this.form.doorPhotoFileUrl);
      this.doorPhotoFileUrlShow = this.form.doorPhotoFileUrl ? [{
        url: doorPhotoFile.fileUrl,
        origin: this.form.doorPhotoFileUrl
      }] : [];

      const workIndoorFile = await this.formatFileMsg(this.form.workIndoorFileUrl);
      this.workIndoorFileUrlShow = this.form.workIndoorFileUrl ? [{
        url: workIndoorFile.fileUrl,
        origin: this.form.workIndoorFileUrl
      }] : [];

      const receptionFile = await this.formatFileMsg(this.form.receptionFileUrl);
      this.receptionFileUrlShow = this.form.receptionFileUrl ? [{
        url: receptionFile.fileUrl,
        origin: this.form.receptionFileUrl
      }] : [];

      // 处理字段类型
      this.form.managementValidityDateType = Number(this.form.managementValidityDateType);
      this.form.certificateValidityDateType = Number(this.form.certificateValidityDateType);
      this.form.certificateType = Number(this.form.certificateType);
      this.form.idFileType = this.form.idCardCopyFileUrl ? 101 : 100;

      // 默认地址
      this.defaultAddress = this.form.registerAddrProvince + '/' + this.form.registerAddrCity + '/' + this.form.registerAddrTown;
      this.defaultManageAddress = this.form.managementAddrProvince + '/' + this.form.managementAddrCity + '/' + this.form.managementAddrTown;

      this.form.businessLicenseFileUrl = this.businessLicenseFileUrlShow;
      this.form.idCardHeadFileUrl = this.idCardHeadFileUrlShow;
      this.form.idCardEmblemFileUrl = this.idCardEmblemFileUrlShow;
      this.form.idCardCopyFileUrl = this.idCardCopyFileUrlShow;
      this.form.doorPhotoFileUrl = this.doorPhotoFileUrlShow;
      this.form.workIndoorFileUrl = this.workIndoorFileUrlShow;
      this.form.receptionFileUrl = this.receptionFileUrlShow;

    }
    if (sessionStorage.getItem('bankInfo')) {
      let form = JSON.parse(sessionStorage.getItem('bankInfo'));
      this.isEdit = true;
      this.$set(this.form, 'accountNo', form.accountNo);
      this.$set(this.form, 'bankChannelNo', form.bankChannelNo);
      this.$set(this.form, 'bankName', form.bankName);
    }

    await this.getQuote();

    for (let i = 1; i <= 3; i++) {
      let form = 'form_' + i;
      for (let p in this[form]) {
        if (p in this.form) {
          this[form][p] = this.form[p];
        }
      }
    }

    if (this.processId) {
      this.isEdit = false;
    }

  },
  beforeDestroy() {
    window.removeEventListener("beforeunload", beforeunloadHandler)
  },
  destroyed() {
    sessionStorage.removeItem('mainInfo');
    sessionStorage.removeItem('bankInfo');
  },
  methods: {
    getProvinceList() {
      getProvinceList().then(response => {
        this.provinceList = response.data;
      })
    },
    getCityList(code, resetItems) {
      let form = this['form_' + this.activeStep]
      let val = form[code + 'AddrProvince'];
      form[code + 'AddrProvince'] = val[1];
      getCityList({
        provinceNo: val[0]
      }).then(response => {
        this[code + 'CityList'] = response.data;
        resetItems.forEach(item => {
          this.$set(form, item, '');
        })
      })
    },
    getTownList(code, resetItems) {
      let form = this['form_' + this.activeStep]
      let val = form[code + 'AddrCity'];
      form[code + 'AddrCity'] = val[1];
      getTownList({
        cityNo: val[0]
      }).then(response => {
        this[code + 'TownList'] = response.data;
        resetItems.forEach(item => {
          this.$set(form, item, '');
        })
      })
    },
    searchBankNumberInfo() {
      this.$refs.bankNumberInfo.search()
      this.$refs.bankNumberInfo.isShow = true;
    },
    closeBankNumberInfoDialog(item) {
      let form = this['form_' + this.activeStep]
      if (item) {
        this.$set(form, 'bankChannelNo', item.bankChannelNo)
        this.$set(form, 'bankName', item.bankName)
      }
    },
    getTimeRange(val, param) {
      let form = this['form_' + this.activeStep]
      form[param + 'Begin'] = val[0];
      form[param + 'End'] = val[1];
    },
    beforeAvatarUpload(file) {
      return this.validateUploadFile({
        img: true,
        size: 6,
      })(file);
    },
    handleRemove(file, fileList, param) {
      let form = this['form_' + this.activeStep]
      form[param] = fileList;
      this[param + 'Show'] = fileList
    },
    async handleSuccess(response, file, fileList, param) {
      let form = this['form_' + this.activeStep]

      let formatFileList = []
      for (let i = 0; i < fileList.length; i++) {
        const item = fileList[i];
        const url = item.response ? item.response.data : item.origin
        const fileMsg = await this.formatFileMsg(url);
        formatFileList.push({
          url: fileMsg.fileUrl,
          origin: url,
          uid: item.uid,
          name: item.name,
        });
      }

      form[param] = formatFileList;
      this[param + 'Show'] = formatFileList
    },
    async nextStep() {
      if (this.activeStep == 3) {
        this.$refs.form_3.validate(async (valid, obj) => {
          if (!valid) {
            for (let key in obj) {
              this.$message.error(obj[key][0].message);
              return;
            }
          }
          if (!this.flowVisible) {
            this.flowVisible = true;
            return;
          }
          this.flowVisible = false;

          let data = { ...this.form, ...this.form_1, ...this.form_2, ...this.form_3 };
          // 遍历上传图片的fileUrl，拼装数据
          this.imgList.forEach(img => {
            data[img] = data[img][0] ?
              data[img][0].response ? data[img][0].response.data : data[img][0].origin
              : ''
          })
          for (let p in data) {
            if (typeof data[p] == 'string' && !(/Term/.test(p))) {
              data[p] = data[p].replace(/\s+/, '')
              if (!data[p]) {
                delete data[p]
              }
            }
          }
          delete data.idFileType;

          if (!this.isMerchant) {
            // 代征主体无发票信息
            delete data.defaultInvoiceCategoryCode
            delete data.defaultInvoiceCategoryName
          }
          if (this.isEdit) {
            if (this.editAccess) {
              await this.changeLeader()
            }
            await Promise.all([
              updateMerchantMain(data),
              updateBankCardInfo({
                mchNo: data.mchNo,
                accountNo: data.accountNo,
                bankName: data.bankName,
                bankChannelNo: data.bankChannelNo
              })
            ])
            this.$message.success('更新信息成功');
            this.$router.push({ path: `/merchant/merchantInfo/${this.mchNo}` });
          } else {
            clearVoid(data);
            if (this.processId) {
              editBusinessData({
                commonFlowId: this.processId,
                taskId: this.taskId,
                extInfo: JSON.stringify(data),
                remark: this.flowRemark,
              }).then(response => {
                this.result = response.data;
                this.$router.push({
                  path: '/waitingHandle/detailProcess',
                  query: {
                    processId: this.processId,
                    taskId: this.taskId,
                  }
                })
              }).catch(err => {
              })
            } else {
              merchantAuth({
                extObj: data,
                condition: {},
                participant: {},
                remark: this.flowRemark,
              }).then(response => {
                this.activeStep++;
                this.result = response.data;
              }).catch(err => {
              })
            }
          }
        });
      } else {
        let form = this.$refs['form_' + this.activeStep];
        let [err, _] = await toPromise(form.validate());
        if (err) return;
        this.activeStep++;
        if (this.activeStep == 2) {
          this.form_2.personnels.splice(0, 1, {
            name: this.form_1.legalPersonName,
            idCardNumber: this.form_1.certificateNumber,
            position: '法人',
            isLegal: true,
          })
        }
        this.$nextTick(() => {
          window.scrollTo(0, 0);
        })
      }
    },
    goDetailProcess() {
      this.$router.push('/waitingHandle/detailProcess?processId=' + this.result.id);
    },
    async changeLeader() {
      let activeForm = this['form_' + this.activeStep]
      const form = {
        newLeaderPhone: activeForm.contactPhone,
        newLeaderName: activeForm.contactName,
        mchNo: this.mchNo,
      }
      await changeLeader(form);
      this.editAccess = false;
    },
    getProtocal(index) {
      let doc;
      const a = document.createElement('a');
      if (index == 1) {
        doc = require('@/assets/doc/protocal.docx');
        a.download = '汇聚支付分账服务协议.docx';
      } else {
        doc = require('@/assets/doc/protocal2.docx');
        a.download = '平台商户入驻框架协议.docx';
      }
      a.href = doc;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    },
    backStep() {
      if (this.activeStep == 1) {
        this.$router.push('/merchant/merchantManagement/merchant')
      } else {
        this.activeStep--
      }
    },
    handleInvoiceChange(val) {
      for (let i = 0; i < this.invoiceCategoryList.length; i++) {
        if (val == this.invoiceCategoryList[i].invoiceCategoryCode) {
          this.form_2.defaultInvoiceCategoryName = this.invoiceCategoryList[i].invoiceCategoryName;
          break;
        }
      }
    },
    handleFileRemove(file, param) {
      this['form_' + this.activeStep][param] = this['form_' + this.activeStep][param].filter(item => {
        return item.uid !== file.uid;
      });
      this[param + 'Show'] = this['form_' + this.activeStep][param]
    },
    flowConfirm(remark) {
      this.flowRemark = remark;
      this.nextStep();
    },
    onChannelTypeChange(val, type, i) {
      const channelType = Number(type.code);
      this.$refs.accounts[i].clearValidate();
      this.channelSelect[this.mainstayList[i].mainstayMchNo].splice(channelType - 1, 1, val);
      if (!this.form_3.accounts[this.mainstayList[i].mainstayMchNo]) {
        this.$set(this.form_3.accounts, this.mainstayList[i].mainstayMchNo, []);
      }
      if (val == 1) {
        this.form_3.accounts[this.mainstayList[i].mainstayMchNo].push({
          "channelType": channelType,
          "channelName": type.desc,
          "payChannelNo": "",
          payChannelName: ''
        })
      } else {
        this.form_3.accounts[this.mainstayList[i].mainstayMchNo] = this.form_3.accounts[this.mainstayList[i].mainstayMchNo].filter((item => {
          return item.channelType != channelType;
        }))
      }
    },
    async getPayChannelList() {
      const { data } = await getPayChannelList({
        pageSize: 100,
        pageCurrent: 1,
      });
      let channels = data.data;
      channels.forEach(item => {
        item.channelType && item.channelType.forEach(i => {
          if (i == 1) {
            this.bankChannel.push(item)
          } else if (i == 2) {
            this.alipayChannel.push(item)
          } else if (i == 3) {
            this.wxChannel.push(item)
          }
        })
      })
    },
    async getQuote() {
      const { data } = await getQuote({ mchNo: this.mchNo })
      const tempMap = {};
      data.forEach(item => {
        if (!(tempMap[item.mainstayMchNo])) {
          tempMap[item.mainstayMchNo] = true;
          this.mainstayList.push({ mainstayMchNo: item.mainstayMchNo, mainstayMchName: item.mainstayMchName });
          this.$set(this.channelSelect, item.mainstayMchNo, [0, 0]);
        }
        if (this.processId) {
          let accountInfo = this.form.accounts[item.mainstayMchNo];
          accountInfo.forEach((i, index) => {
            if (i.payChannelNo !== '') {
              this.channelSelect[item.mainstayMchNo].splice(i.channelType - 1, 1, 1);
            }
          })
        }
      })
    },
    handlePayChannelChange(account, val) {
      if (!val) {
        account.payChannelName = '';
      } else {
        let payChannel = this.payChannel[account.channelType - 1];
        for (let i = 0; i < payChannel.length; i++) {
          if (payChannel[i].payChannelNo == val) {
            account.payChannelName = payChannel[i].payChannelName;
          }
        }
      }
    },
    // 增加董监高
    addPeople() {
      this.form_2.personnels.push({
        name: '',
        idCardNumber: '',
        position: '',
        isLegal: false,
      })
    },
    // 删除董监高
    deletePeople(index) {
      this.form_2.personnels.splice(index, 1)
    },
  }
}
</script>

<style
  lang="scss"
  scoped>
.page-container {
  .content-container {
    .example-image {
      float: left;
      width: 148px;
      height: 148px;
      margin-right: 20px;
    }

    .footer-container {
      text-align: center;
    }
  }

  .step_1 {
    .job-box {
      padding-bottom: 50px;
    }
  }

  .step_4 {
    padding: 50px 0;
    text-align: center;

    svg {
      width: 100px;
      height: 100px;
    }

    p {
      margin-bottom: 20px;
    }
  }

  .protocal-container {
    margin: 20px 0;
  }

  .protocal-link {
    cursor: pointer;
    color: $mainColor;
  }

  .el-checkbox-label {
    color: #000;
  }
}
</style>
