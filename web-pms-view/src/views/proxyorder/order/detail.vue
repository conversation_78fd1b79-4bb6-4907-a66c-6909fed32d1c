<template>
  <div>
    <div class="box-container">
      <div class="flex-box func-container">
        <div class="font-bold func-title">订单详情</div>
        <div class="text-right">
          <el-button
            type="primary"
            @click="handleAction(1)"
          >确认开票
          </el-button>
          <!--<el-button-->
          <!--  type="primary"-->
          <!--  @click="handleAction(1, false)">查看发票-->
          <!--</el-button>-->
          <!--<el-button-->
          <!--  type="primary"-->
          <!--  @click="handleAction(1)"-->
          <!--&gt;确认寄出-->
          <!--</el-button>-->
          <el-button
            type="danger"
            @click="handleAction(2)">取消订单
          </el-button>
          <!--<el-button-->
          <!--  type="danger"-->
          <!--  @click="handleAction(3)"-->
          <!--&gt;订单退款-->
          <!--</el-button>-->
        </div>
      </div>
      <!-- 订单详情-->
      <div
        v-if="order.id">
        <div class="subTitle content-title">订单信息</div>
        <div class="content-box flex-box">
          <div class="content-item">代开订单号：{{ order.orderNo }}</div>
          <div class="content-item">订单状态：{{ $dictCode('ProxyOrderStatusEnum', order.orderStatus).desc }}</div>
          <div class="content-item">供应商：{{ order.mainstayName }}</div>
          <div class="content-item">创建时间：{{ order.createTime }}</div>
          <div class="content-item">完成时间：{{ order.completeTime }}</div>
          <div class="content-item">开票金额：{{ order.orderAmount }}</div>
          <div class="content-item">服务费：{{ (order.feeDetail && order.feeDetail.serviceFeeAmount) | moneyFormat }}</div>
          <div class="content-item">个税：{{ (order.feeDetail && order.feeDetail.taxAmount) | moneyFormat }}</div>
          <div class="content-item">附加税：{{ (order.feeDetail && order.feeDetail.vatAmount) | moneyFormat }}</div>
          <div class="content-item">用户备注：{{ order.remark }}</div>
        </div>
        <div class="subTitle content-title">购买方信息</div>
        <div class="content-box flex-box">
          <div class="content-item">购买方名称：{{ order.invoiceTitleCompanyName }}</div>
          <div class="content-item">纳税人识别号：{{ order.invoiceTitleTaxNo }}</div>
          <div class="content-item">单位注册地址及电话：{{ order.companyAddress }}{{ order.addressMobile }}</div>
          <div class="content-item">开户行：{{ order.bankName }}</div>
          <div class="content-item">银行账号：{{ order.bankAccountNo }}</div>
        </div>
        <div class="subTitle content-title">销售方信息</div>
        <div class="content-box flex-box">
          <div class="content-item">姓名：{{ order.userName }}</div>
          <div class="content-item">身份证号：{{ order.userIdcardNo }}</div>
          <div class="content-item">手机号：{{ order.userMobile }}</div>
          <div class="content-item">身份证图片：
            <el-button
              v-if="order.userIdcardImg && order.userIdcardImg.length"
              type="text"
              @click="handlePreviewIdCard">查看
            </el-button>
          </div>
        </div>
        <div class="subTitle content-title">收票地址信息</div>
        <div class="content-box flex-box">
          <div class="content-item">快递单号：{{ order.expressNo }}</div>
          <div class="content-item">收件人姓名：{{ order.invoiceApplicant }}</div>
          <div class="content-item">联系电话：{{ order.addressMobile }}</div>
          <div class="content-item">邮寄地址：{{ order.addressName }}{{ order.addressDetail }}</div>
        </div>

        <div class="subTitle content-title">开票明细</div>
        <div class="content-box">
          <el-table
            :data="order.invoiceDetail"
            show-summary
            :summary-method="getSummary">
            <el-table-column
              type="index"
              label="序号"></el-table-column>
            <el-table-column
              label="开票类目"
              prop="invoiceCategoryName"></el-table-column>
            <el-table-column
              label="规格型号"
              prop="specification"></el-table-column>
            <el-table-column
              label="计量单位"
              prop="unitType"></el-table-column>
            <el-table-column
              label="数量"
              prop="quantity"></el-table-column>
            <el-table-column
              label="单价"
              prop="univalent"></el-table-column>
            <el-table-column
              label="开票金额"
              prop="invoiceAmount"></el-table-column>
            <el-table-column
              label="税率"
              prop="compositeTaxRatio">
              <template v-slot="{row}">
                {{ row.compositeTaxRatio }}%
              </template>
            </el-table-column>
            <el-table-column
              label="税额"
              prop="compositeTax">
            </el-table-column>
          </el-table>
        </div>
        <div class="subTitle content-title">凭证文件</div>
        <div class="content-box">
          <div class="content-file">资金流水：
            <el-button
              type="text"
              v-preview="order.fundFlowFile">资金流水截图
            </el-button>
          </div>
          <div class="content-file">业务合同：
            <el-button
              type="text"
              v-preview="order.businessContractFile">业务合同截图
            </el-button>
          </div>
          <div class="content-file">委托协议：
            <el-button
              type="text"
              v-preview="order.entrustAgreementFile">已签署的委托代征协议
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <!--  弹窗-->
    <el-dialog
      :visible.sync="visible"
      :title="actionTitle"
      :close-on-click-modal="false"
      :before-close="close"
    >
      <el-form
        ref="form"
        :model="form"
        label-position="top"
        :rules="rules">
        <el-form-item
          label="快递单号"
          prop="expressNo"
          v-if="action === 1 && order.orderStatus === 400">
          <el-input
            v-model="form.expressNo"
            clearable
            placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item
          v-if="action === 1"
          label="已开具发票影像"
          prop="invoiceUrl">
          <file-upload
            :urls.sync="form.invoiceUrl"
          ></file-upload>
        </el-form-item>
        <template v-if="action === 3">
          <el-form-item label="退款金额">
            {{ order.orderAmount | moneyFormat }}
          </el-form-item>
          <el-form-item
            label="退款原因"
            prop="refundReason">
            <el-select v-model="form.refundReason"></el-select>
          </el-form-item>
          <el-form-item prop="errorRemark">
            <el-input
              v-model="form.errorRemark"
              type="textarea"
              placeholder="请输入退款原因"
              show-word-limit
              maxlength="50"
              :autosize="{ minRows: 4, maxRows: 4}"></el-input>
          </el-form-item>
        </template>
      </el-form>

      <template
        v-slot:footer
        v-if="canEdit">
        <el-button @click="close">取消</el-button>
        <el-button
          type="primary"
          @click="onSubmit">{{ action === 3 ? '退款' : '确认' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { toPromise } from "@/utils";
import { confirmInvoice } from "@/api/proxyorder";
import FileUpload from "@/components/FileUpload/index.vue";

export default {
  name: "ProxyOrderDetail",
  components: { FileUpload },
  data() {
    return {
      order: {},
      action: 0,
      visible: false,
      form: {},
      rules: {
        invoiceUrl: [{ required: true, message: '请上传发票影像', trigger: 'change' }],
        expressNo: [{ required: true, message: '请输入快递单号', trigger: 'blur' }],
        refundReason: [{ required: true, message: '请选择原因', trigger: 'blur' }],
      },
      canEdit: true,
    }
  },
  computed: {
    actionTitle() {
      if (!this.canEdit) return '查看发票'
      switch (this.action) {
        case 1:
          return this.order.orderStatus === 300 ? '确认开票' : '确认寄出'
        case 3:
          return '订单退款'
        default:
          return ''
      }
    }
  },
  mounted() {
    this.getOrderInfo()
  },
  beforeDestroy() {
    // sessionStorage.removeItem('proxyOrder')
  },
  methods: {
    getOrderInfo() {
      let info = sessionStorage.getItem('proxyOrder')
      if (info) {
        this.order = JSON.parse(info)
        this.order.invoiceDetail && this.order.invoiceDetail.forEach(row => {
          row.compositeTax = (row.invoiceAmount * row.compositeTaxRatio / 100).toFixed(2)
        })
        this.form.invoiceUrl = this.order.invoiceUrl
        this.form.expressNo = this.order.expressNo
      }
    },
    handlePreviewIdCard() {
      this.$preview(this.order.userIdcardImg)
    },
    getSummary(param) {
      const { columns, data } = param
      const sums = []
      if (data) {
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '总价';
            return;
          }
          if (column.property === 'invoiceAmount' || column.property === 'compositeTax') {
            const values = data.map(item => Number(item[column.property]))
            sums[index] = values.reduce((prev, next) => {
              return prev + next
            }, 0)
          } else {
            sums[index] = '-'
          }
        })
      }
      return sums
    },
    async handleAction(action, canEdit = true) {
      this.action = action
      this.canEdit = canEdit
      // 取消订单
      if (action === 2) {
        const [err] = await toPromise(this.$confirm('确认取消订单？订单取消后无法恢复', '提示', {
          type: 'warning',
          cancelButtonText: '暂不取消',
          confirmButtonText: '取消订单',
        }))
        if (err) return
        this.confirmAction(action)
      } else if (action === 1 || action === 3) {
        this.visible = true
      }
    },
    async onSubmit() {
      const [err] = await toPromise(this.$refs.form.validate())
      if (err) return
      this.confirmAction()
    },
    async confirmAction() {
      const form = {
        id: this.order.id,
        action: this.action
      }
      if (this.action === 1) {
        // 确认开票
        form.status = 400
        form.invoiceUrl = this.order.invoiceUrl
        form.expressNo = this.order.invoiceUrl
      } else if (this.action === 3) {
        // 退款
        form.refundReason = this.order.refundReason
        form.errorRemark = this.order.errorRemark
      }
      await confirmInvoice(form)
      this.$message.success('操作成功')
      this.close()
      this.$router.push('/proxyorder/order/list')
    },
    close() {
      this.action = 0
      this.visible = false
    },
  },
}
</script>

<style
  scoped
  lang="scss">
.func-title {
  flex: 1 0 auto;
  font-size: 25px;
}

.content-box {
  margin-left: 10px;
  flex-wrap: wrap;
}

.content-item {
  min-width: calc(100% / 5);
  margin: 10px 10px 10px 0;
}

.content-file {
  margin: 16px 0;
}

.content-title {
  margin-top: 32px;
}
</style>
