<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <div class="flex-item__label">代开订单号：</div>
          <el-input v-model="searchForm.orderNo"></el-input>
        </div>
        <div class="flex-item">
          <div class="flex-item__label">订单状态：</div>
          <el-select
            v-model="searchForm.orderStatus"
            clearable>
            <el-option
              v-for="item in $dict('ProxyOrderStatusEnum')"
              :key="item.code"
              :label="item.desc"
              :value="item.code"></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <div class="flex-item__label">发票抬头：</div>
          <el-input v-model="searchForm.invoiceTitleCompanyNameLike"></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <div class="flex-item__label">代开人姓名：</div>
          <el-input
            v-model="searchForm.invoiceApplicant"
            clearable></el-input>
        </div>
        <div class="flex-item">
          <div class="flex-item__label">供应商：</div>
          <el-select
            v-model="searchForm.mainstayNo"
            filterable
            clearable>
            <el-option
              v-for="(item, index) in mainstayList"
              :key="index"
              :label="item.mchName"
              :value="item.mchNo"></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <div class="flex-item__label">创建时间：</div>
          <date-picker
            picker="separate"
            ref="datepicker"
            type="datetime"
            :start-time.sync="searchForm.createTimeBegin"
            :end-time.sync="searchForm.createTimeEnd"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)">查询
        </el-button>
        <el-button @click="resetForm">重置</el-button>
      </div>
    </div>

    <div class="content-container">
      <el-table :data="list">
        <el-table-column
          label="代开订单号"
          width="150"
          prop="orderNo"></el-table-column>
        <el-table-column
          label="发票抬头"
          width="180"
          prop="invoiceTitleCompanyName"></el-table-column>
        <el-table-column
          label="代开人姓名"
          width="120"
          prop="invoiceApplicant"></el-table-column>
        <el-table-column
          label="开票金额"
          prop="orderAmount">
          <template v-slot="{row}">
            {{ row.orderAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column label="个税">
          <template v-slot="{row}">
            {{ (row.feeDetail && row.feeDetail.taxAmount) | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column label="附加税">
          <template v-slot="{row}">
            {{ (row.feeDetail && row.feeDetail.vatAmount) | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column label="服务费">
          <template v-slot="{row}">
            {{ (row.feeDetail && row.feeDetail.serviceFeeAmount) | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column label="供应商">
          <template v-slot="{row}">
            {{ row.mainstayNo }}<br>
            {{ row.mainstayName }}
          </template>
        </el-table-column>
        <el-table-column label="订单状态">
          <template v-slot="{row}">
            <el-tag :type="getTagStatus(row.orderStatus)">
              {{ $dictCode('ProxyOrderStatusEnum', row.orderStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          width="180"
          prop="createTime"
        ></el-table-column>
        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button
              @click="goDetail(row)"
              type="text">查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="page.total"
        :current-page.sync="page.current"
        :page-sizes="[10, 50]"
        :page-size.sync="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>
  </div>
</template>

<script>
import { listProxyOrder } from "@/api/proxyorder";
import { getMainstayList } from "@/api/merchant";

export default {
  name: 'ProxyOrderList',
  data() {
    return {
      searchForm: {},
      mainstayList: [],
      list: [],
      page: {
        size: 10,
        current: 1,
        total: 0
      }
    }
  },
  mounted() {
    this.getMainstayList()
    this.search(true)
  },
  methods: {
    async getMainstayList() {
      const { data } = await getMainstayList()
      this.mainstayList = data
    },
    async search(init) {
      if (init === true) {
        this.page.current = 1
      }
      const { data: { records, total } } = await listProxyOrder({
        ...this.searchForm,
        ...this.page
      })
      this.list = records
      this.page.total = total
      this.page.current++
    },
    resetForm() {
      this.searchForm = {}
      this.$refs.datepicker.clearTime()
    },
    getTagStatus(status) {
      switch (Number(status)) {
        case 100:
          return 'success'
        case 200:
          return 'info'
        case 300:
        case 400:
          return 'primary'
        case 500:
          return 'danger'
      }
    },
    goDetail(row) {
      sessionStorage.setItem('proxyOrder', JSON.stringify(row))
      this.$router.push('/proxyorder/order/detail')
    }
  }
}
</script>

<style scoped>

</style>
