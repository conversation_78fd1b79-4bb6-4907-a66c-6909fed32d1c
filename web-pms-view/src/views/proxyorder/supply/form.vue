<template>
  <div class="box-container page-container">
    <el-form
      ref="form"
      id="form"
      :model="supplyInfo"
      :rules="rules"
      label-width="150px">
      <el-form-item
        label="供应商："
        prop="mainstayNo">
        <el-select
          v-model="supplyInfo.mainstayNo"
          @change="onMainstayChange">
          <el-option
            v-for="item in mainstayList"
            :key="item.mchNo"
            :value="item.mchNo"
            :label="item.mchName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="服务费比例："
        prop="serviceFeeRatio">
        <el-input
          :value="supplyInfo.serviceFeeRatio"
          @input="handleRateInput(supplyInfo, 'serviceFeeRatio', $event, 0, 100)">
          <span slot="append">%</span>
        </el-input>
      </el-form-item>
      <el-form-item label="服务费公式：">
        服务费 = 开票金额 * 服务费比例
      </el-form-item>
      <el-form-item
        label="单笔开票金额："
        prop="singleLimitMax">
        <el-input
          :value="supplyInfo.singleLimitMin"
          @input="handleRateInput(supplyInfo, 'singleLimitMin', $event, 0)"></el-input>
        -
        <el-input
          :value="supplyInfo.singleLimitMax"
          @input="handleRateInput(supplyInfo, 'singleLimitMax', $event, 0)"></el-input>
      </el-form-item>
      <el-form-item
        label="累计开票金额："
        prop="accumulativeLimitMax">
        <el-input
          :value="supplyInfo.accumulativeLimitMin"
          @input="handleRateInput(supplyInfo, 'accumulativeLimitMin', $event, 0)"></el-input>
        -
        <el-input
          :value="supplyInfo.accumulativeLimitMax"
          @input="handleRateInput(supplyInfo, 'accumulativeLimitMax', $event, 0)"></el-input>
      </el-form-item>
      <el-form-item
        label="年龄限制："
        prop="acitiveAgeLimit">
        <el-switch
          v-model="supplyInfo.acitiveAgeLimit"
          style="margin-right: 16px"></el-switch>
        <template v-if="supplyInfo.acitiveAgeLimit">
          <el-input
            style="width: 150px"
            :value="supplyInfo.ageLimitMin"
            @input="handleNumInput(supplyInfo, 'ageLimitMin', $event)"></el-input>
          -
          <el-input
            style="width: 150px"
            :value="supplyInfo.ageLimitMax"
            @input="handleNumInput(supplyInfo, 'ageLimitMax', $event)"></el-input>
        </template>
      </el-form-item>
      <el-form-item
        label="10万以下免征附加："
        prop="levyLessThan10w">
        <el-switch v-model="supplyInfo.levyLessThan10w"></el-switch>
      </el-form-item>
      <el-form-item
        label="业务凭证要求："
        prop="requiedBizVouchers">
        <el-checkbox-group
          v-model="supplyInfo.requiedBizVouchers">
          <el-checkbox
            v-for="item in $dict('RequiedBizVoucherEnum')"
            :key="item.code"
            :label="item.code">
            {{ item.desc }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item
        label="地域限制："
        prop="areaLimitStatus">
        <el-switch
          v-model="supplyInfo.areaLimitStatus"
          style="margin-right: 16px;"></el-switch>
        <address-select
          v-if="supplyInfo.areaLimitStatus"
          ref="address"
          style="display: inline-block"
          :end-level="1"
          multiple
          clearable
          :callback-fn="selectCb"
          @multi-change="onAddressChange"
        ></address-select>
      </el-form-item>
      <el-form-item
        label="特殊说明："
        prop="specialRemark">
        <el-input
          v-model="supplyInfo.specialRemark"
          type="textarea"
          placeholder="请输入备注"
          :rows="5"></el-input>
      </el-form-item>
    </el-form>
    <div class="text-center">
      <el-button>
        <router-link to="/proxyorder/supply">
          取消
        </router-link>
      </el-button>
      <el-button
        type="primary"
        @click="onSubmit">确认
      </el-button>
    </div>
  </div>
</template>

<script>
import { getMainstayList } from "@/api/merchant";
import { toPromise } from "@/utils";
import { addSupply, editSupply } from "@/api/proxyorder";

export default {
  name: 'ProxyOrderSupplyForm',
  data(vm) {
    return {
      supplyInfo: {
        requiedBizVouchers: [],
        acitiveAgeLimit: false,
        levyLessThan10w: false,
        areaLimitStatus: false,
      },
      mainstayList: [],
      rules: {
        mainstayNo: [{ required: true, message: '请选择供应商', trigger: 'blur' }],
        serviceFeeRatio: [{ required: true, message: '请输入服务费比例', trigger: 'blur' }],
        singleLimitMax: [
          { required: true, message: '请输入单笔开票金额限制', trigger: 'blur' },
          {
            validator(rule, val, cb) {
              if (vm.supplyInfo.singleLimitMax !== undefined && vm.supplyInfo.singleLimitMin !== undefined) {
                cb()
              } else {
                cb(new Error('请输入单笔开票金额限制'))
              }
            }, trigger: 'blur'
          }
        ],
        accumulativeLimitMax: [
          { required: true, message: '请输入累计开票金额限制', trigger: 'blur' },
          {
            validator(rule, val, cb) {
              if (vm.supplyInfo.accumulativeLimitMax !== undefined && vm.supplyInfo.accumulativeLimitMin !== undefined) {
                cb()
              } else {
                cb(new Error('请输入累计开票金额限制'))
              }
            }, trigger: 'blur'
          }
        ],
        acitiveAgeLimit: [
          { required: true, message: '请选择是否限制', trigger: 'blur' },
          {
            validator(rule, val, cb) {
              if (!val) {
                cb()
              } else {
                if (vm.supplyInfo.ageLimitMin !== undefined && vm.supplyInfo.ageLimitMax !== undefined) {
                  cb()
                } else {
                  cb(new Error('请输入年龄限制'))
                }
              }
            }, trigger: 'blur'
          }
        ],
        levyLessThan10w: [{ required: true, message: '请选择是否限制', trigger: 'blur' }],
        requiedBizVouchers: [{ required: true, message: '请选择业务凭证要求', trigger: 'blur' }],
        areaLimitStatus: [{ required: true, message: '请选择是否限制', trigger: 'blur' }, {
          validator(rule, val, cb) {
            if (!val) {
              cb()
            } else {
              if (!vm.supplyInfo.areaSurport || !vm.supplyInfo.areaSurport.length) {
                cb(new Error('请选择地域限制'))
              } else {
                cb()
              }
            }
          }, trigger: ['blur', 'change']
        }],
      }
    }
  },
  computed: {
    action() {
      return this.$route.query.action || 'add'
    }
  },
  mounted() {
    this.initData()
  },
  beforeDestroy() {
    sessionStorage.removeItem('invoiceSupply')
  },
  methods: {
    initData() {
      const supplyInfo = sessionStorage.getItem('invoiceSupply')
      if (supplyInfo) {
        this.supplyInfo = supplyInfo
      }
      this.getMainstayList()

    },
    async getMainstayList() {
      const { data } = await getMainstayList()
      this.mainstayList = data
    },
    onMainstayChange(val) {
      const item = this.mainstayList.find(tmp => {
        return tmp.mchNo === val
      })
      this.supplyInfo.mainstayName = item.mchName
    },
    onAddressChange(data) {
      this.supplyInfo.areaSurport = data
    },
    async onSubmit() {
      const [err] = await toPromise(this.$refs.form.validate())
      if (err) return
      const api = this.action === 'add' ? addSupply : editSupply
      await api(this.supplyInfo)
      this.$message.success('操作成功')
      this.$router.push('/proxyorder/supply')
    },
    selectCb(cascader) {
      cascader.$refs.panel.store.nodes.forEach(node => node.loading = false)
    }
  },
}
</script>

<style
  lang="scss"
  scoped>
#form {
  width: 800px;
  margin: 0 auto;
}
</style>
