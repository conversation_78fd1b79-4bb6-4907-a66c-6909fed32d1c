<template>
  <div class="box-container">
    <div class="create-btn">
      <el-button type="primary">
        <router-link to="/proxyorder/supply/form?action=add">
          新增供应商
        </router-link>
      </el-button>
    </div>
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">供应商编号：</span>
          <el-input
            v-model="searchForm.mainstayNo"
            clearable
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">供应商名称：</span>
          <el-input
            v-model="searchForm.mainstayName"
            clearable
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">状态：</span>
          <el-select
            v-model="searchForm.status"
            clearable
          >
            <el-option
              v-for="item in $dict('TrueFalseEnum')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"></el-option>
          </el-select>
        </div>

      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建日期：</span>
          <date-picker
            :start-time.sync="searchForm.createTimeBegin"
            :end-time.sync="searchForm.createTimeEnd"
            ref="datePicker"
            picker="separate"
            type="datetime"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)">查询
        </el-button>
        <el-button>重置</el-button>
      </div>
    </div>

    <div class="content-container">
      <el-table :data="list">
        <el-table-column type="expand">
          <template v-slot="{row}">
            <el-form
              label-position="left"
              class="expand-form-column">
              <el-form-item label="开票金额限制：">
                单笔开票金额：{{ row.singleLimitMin }} - {{ row.singleLimitMax }}
                累计开票金额：{{ row.accumulativeLimitMin }} - {{ row.accumulativeLimitMax }}
              </el-form-item>
              <el-form-item label="年龄限制：">
                <template v-if="row.acitiveAgeLimit">
                  {{ row.ageLimitMin }} 至 {{ row.ageLimitMax }}
                </template>
              </el-form-item>
              <el-form-item label="10万以下免征附加：">
                {{ row.levyLessThan10w ? '是' : '否' }}
              </el-form-item>
              <el-form-item label="业务凭证要求：">
                <span
                  v-for="(item, key) in row.requiedBizVouchers"
                  :key="key">
                  {{ $dictCode('RequiedBizVoucherEnum', item).desc }}
                </span>
              </el-form-item>
              <el-form-item label="地域限制：">
                <template v-if="row.areaLimitStatus">
                  <span
                    v-for="(item, key) in row.areaSurport"
                    :key="key">
                    {{ item.provinceName }}{{ item.cityName }}、
                  </span>
                </template>
              </el-form-item>
              <el-form-item label="特殊说明：">
                {{ row.specialRemark }}
              </el-form-item>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column
          label="供应商编号"
          prop="mainstayNo"></el-table-column>
        <el-table-column
          label="供应商名称"
          prop="mainstayName"></el-table-column>
        <el-table-column
          label="服务费比例%"
          prop="serviceFeeRatio"></el-table-column>
        <el-table-column
          label="可开类目数"
          prop="mainstayName"></el-table-column>
        <el-table-column
          label="状态"
          prop="status">
          <template v-slot="{row}">
            <el-switch
              v-model="row.status"
              active-text="开"
              inactive-text="关"
              @change="updateSupplyStatus(row)">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          prop="createTime"></el-table-column>
        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="handleEdit(row)">编辑
            </el-button>
            <el-button
              type="text"
              @click="handlePriceEdit(row)">报价管理
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="page.total"
        :current-page.sync="page.total"
        :page-sizes="[10,50]"
        :page-size.sync="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </el-footer>
  </div>
</template>

<script>
import { listProxyOrderSupply, updateSupplyStatus } from "@/api/proxyorder";

export default {
  name: 'ProxyOrderSupplyList',
  data() {
    return {
      searchForm: {},
      page: {
        total: 0,
        current: 1,
        size: 10
      },
      list: []
    }
  },
  methods: {
    async search(init) {
      if (init === true) {
        this.page.current = 1
      }
      const { data: { records, total } } = await listProxyOrderSupply({
        ...this.searchForm,
        ...this.page
      })
      this.list = records
      this.page.total = total
    },
    resetForm() {
      this.searchForm = {}
      this.$refs.datePicker.clearTime()
    },
    handleEdit(row) {
      sessionStorage.setItem('invoiceSupply', JSON.stringify(row))
      this.$router.push('/proxyorder/supply/form')
    },
    handlePriceEdit(row) {
      this.$router.push(`/proxyorder/supply/taxquote/${ row.mainstayNo }`)
    },
    async updateSupplyStatus({ id, status }) {
      await updateSupplyStatus({
        id,
        status: !status
      })
      this.$message.success('更改状态成功')
      this.search()
    }
  },
}
</script>

<style scoped>

</style>
