<template>
  <div class="box-container">
    <div>
      <el-button
        type="primary"
        class="create-btn"
        @click="addTopCate">新增一级类目
      </el-button>
      <el-button @click="openUpload">导入</el-button>
      <el-button
        @click="getRecord(-1)"
        type="text">查看导入
      </el-button>
    </div>
    <el-table
      :data="list"
      row-key="invoiceCategoryCode"
      default-expand-all
      :tree-props="{children: 'subcategorys', hasChildren: 'hasChildren'}">
      <el-table-column
        label="类目名称"
        prop="invoiceCategoryName"></el-table-column>
      <el-table-column
        label="个税%"
        prop="taxRatio"></el-table-column>
      <el-table-column
        label="增值税%"
        prop="vatRatio"></el-table-column>
      <el-table-column
        label="地方教育附加税%"
        prop="localEduAddTaxRatio"></el-table-column>
      <el-table-column
        label="教育附加税%"
        prop="eduAddTaxRatio"></el-table-column>
      <el-table-column
        label="城建税%"
        prop="buildingTaxRatio"></el-table-column>
      <el-table-column
        label="印花税%"
        prop="stampDutyRatio"></el-table-column>
      <el-table-column label="操作">
        <template v-slot="{row}">
          <el-button
            type="text"
            @click="editCate(row)">编辑
          </el-button>
          <el-button
            type="text"
            class="red-btn"
            @click="deleteCate(row)">删除
          </el-button>
          <el-button
            type="text"
            v-if="row.subcategorys"
            @click="addSubCate(row)">新增子类目
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :visible.sync="visible"
      :title="title">
      <el-form
        :model="form"
        :rules="rules"
        id="form"
        ref="form"
        label-width="150px">
        <template v-if="action === 'add' && editRow">
          <!--新增子类-->
          <el-form-item
            label="一级类目名称：">
            <el-input
              v-model="editRow.invoiceCategoryName"
              disabled></el-input>
          </el-form-item>
          <el-form-item
            label="子类目名称："
            prop="invoiceCategoryName">
            <el-input v-model="form.invoiceCategoryName"></el-input>
          </el-form-item>
        </template>
        <el-form-item
          v-else
          label="类目名称："
          prop="invoiceCategoryName">
          <el-input v-model="form.invoiceCategoryName"></el-input>
        </el-form-item>
        <el-form-item
          label="个税："
          prop="taxRatio">
          <el-input
            :value="form.taxRatio"
            @input="handleRateInput(form, 'taxRatio', $event, 0, 100)">
            <template v-slot:append>%</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="增值税："
          prop="vatRatio">
          <el-input
            :value="form.vatRatio"
            @input="handleRateInput(form, 'vatRatio', $event, 0, 100)">
            <template v-slot:append>%</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="地方教育附加税："
          prop="localEduAddTaxRatio">
          <el-input
            :value="form.localEduAddTaxRatio"
            @input="handleRateInput(form, 'localEduAddTaxRatio', $event, 0, 100)">
            <template v-slot:append>%</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="教育附加税："
          prop="eduAddTaxRatio">
          <el-input
            :value="form.eduAddTaxRatio"
            @input="handleRateInput(form, 'eduAddTaxRatio', $event, 0, 100)">
            <template v-slot:append>%</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="城建税："
          prop="buildingTaxRatio">
          <el-input
            :value="form.buildingTaxRatio"
            @input="handleRateInput(form, 'buildingTaxRatio', $event, 0, 100)">
            <template v-slot:append>%</template>
          </el-input>
        </el-form-item>
        <el-form-item
          label="印花税："
          prop="stampDutyRatio">
          <el-input
            :value="form.stampDutyRatio"
            @input="handleRateInput(form, 'stampDutyRatio', $event, 0, 100)">
            <template v-slot:append>%</template>
          </el-input>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <el-button @click="close">取消</el-button>
        <el-button
          type="primary"
          @click="onSubmit">确认
        </el-button>
      </template>
    </el-dialog>

    <!--  upload-->
    <el-dialog
      :visible.sync="uploadVisible"
      title="上传"
      :before-close="closeUpload">
      <file-upload
        action="/individualproxy/importProxyQuote"
        :data="{mainstayNo: supplyInfo.mainstayNo}"
        :options="{img: false, excel: true}"
        drag
        ref="upload"
        @success="closeUpload"
      ></file-upload>
    </el-dialog>

    <export-record ref="exportRecord"></export-record>
  </div>
</template>

<script>
import { toPromise } from "@/utils";
import { addQuote, deleteQuote, editQuote, listProxyOrderSupply } from "@/api/proxyorder";
import FileUpload from "@/components/FileUpload";
import ExportRecord from '@/components/ExportRecord';


export default {
  name: "TaxQuote",
  components: {
    FileUpload,
    ExportRecord,
  },
  data() {
    return {
      list: [],
      supplyInfo: {},
      visible: false, // 类目修改弹窗控制
      editRow: null,
      action: '',
      form: {},
      rules: {
        invoiceCategoryName: [{ required: true, message: '请输入类目名称', trigger: 'blur' }],
        taxRatio: [{ required: true, message: '请输入个税', trigger: 'blur' }],
      },

      uploadVisible: false,
    }
  },
  computed: {
    title() {
      switch (this.action) {
        case 'add':
          return !this.editRow ? '新增一级类目' : '新增子类目'
        default:
          return '编辑'
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    async getData() {
      const { data } = await listProxyOrderSupply({
        mainstayNo: this.$route.params.id,
        current: 1,
        size: 10,
      })
      this.supplyInfo = data[0]
    },
    addTopCate() {
      this.action = 'add'
      this.editRow = null
      this.open()
    },
    addSubCate(row) {
      this.action = 'add'
      this.editRow = JSON.parse(JSON.stringify(row))
      this.open()
    },
    editCate(row) {
      this.action = 'edit'
      this.form = JSON.parse(JSON.stringify(row))
      this.visible = true
    },
    open() {
      this.form = {}
      this.visible = true
    },
    close() {
      this.$refs.form.clearValidate()
      this.visible = false
      this.$nextTick(() => {
        this.editRow = null
      })
    },
    async deleteCate({ invoiceCategoryCode }) {
      const [err] = await toPromise(this.$confirm('是否确认删除该条记录？', '提示', {
        type: 'warning'
      }))
      if (err) return
      await deleteQuote({
        id: this.supplyInfo.id,
        invoiceCategoryCode
      })
      this.getData()
    },
    async onSubmit() {
      const [err] = await toPromise(this.$refs.form.validate())
      if (err) return
      const form = {
        id: this.supplyInfo.id,
        individualProxyQuote: this.form
      }
      if (this.action === 'add') {
        this.handleAdd(form)
      } else {
        this.handleEdit(form)
      }
    },
    async handleAdd(form) {
      form.parentCateCode = this.editRow ? this.editRow.invoiceCategoryCode : undefined
      await addQuote(form)
      this.$message.success('操作成功')
      this.close()
      this.getData()
    },
    async handleEdit(form) {
      await editQuote(form)
      this.$message.success('操作成功')
      this.close()
      this.getData()
    },
    openUpload() {
      this.uploadVisible = true
      this.$nextTick(() => {
        this.$refs.upload.clearFiles()
      })
    },
    closeUpload() {
      this.uploadVisible = false
    },
    getRecord(type) {
      if (type === -1) {
        this.$refs.exportRecord.open(type, { direction: 1 })
      }
    }
  },
}
</script>

<style
  scoped
  lang="scss">
#form {
  .el-input {
    width: 300px;
  }
}
</style>
