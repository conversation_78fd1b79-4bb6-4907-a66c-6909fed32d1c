<template>
  <div class="merchant-page-container">
    <div class="header-container">
      <el-steps
        :active="activeStep"
        align-center
        class="el-steps-container"
      >
        <el-step
          title="编辑产品报价单"
          v-if="actionType === 'EDIT'"
        ></el-step>
        <el-step
          title="新建产品报价单"
          v-else
        ></el-step>
        <el-step title="选择发放产品"></el-step>
        <el-step title="审核与公示"></el-step>
      </el-steps>
    </div>

    <div class="page-container">
      <div
        class="content-container"
        v-if="activeStep === 1"
      >
        <el-form
          :model="form"
          :rules="rules"
          label-width="400px"
          class="block-form"
          ref="form"
        >
          <el-form-item
            label="所属产品："
            prop="productNo"
          >
            <el-select
              v-model="form.productNo"
              :disabled="actionType === 'EDIT'"
              @change="onProductChange"
            >
              <el-option
                v-for="(item, key) in productList"
                :key="key"
                :label="item.productName"
                :value="item.productNo"
              ></el-option>
            </el-select>
          </el-form-item>

          <template v-if="form.productNo !== 'ZFT'">
            <el-form-item label="个税报税类型：">
              <div>{{ form.merchantCkhQuote._taxType }}</div>
            </el-form-item>
            <el-form-item
              label="供应商："
              prop="mainstayMchNo"
            >
              <el-select
                v-model="form.mainstayMchNo"
                :disabled="actionType === 'EDIT'"
                @change="onMainstayChange"
              >
                <el-option
                  v-for="(item, key) in mainstayList"
                  :key="key"
                  :label="item.vendorName"
                  :value="item.vendorNo"
                ></el-option>
              </el-select>
              <div v-if="mainstayTips" class="warning-tips">{{ mainstayTips }}</div>
            </el-form-item>

            <el-form-item
              label="岗位信息绑定："
              prop="positionList"
            >
              <el-select
                clearable
                multiple
                v-model="form.positionList"
              >
                <el-option
                  v-for="item in positionList"
                  :key="item.id"
                  :label="item.workCategoryName"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="发票类目：">
              <el-tag
                v-for="(item, index) in bindInvoiceCategoryList"
                :key="index"
                type="info">
                {{ item.invoiceCategoryName }}
              </el-tag>
            </el-form-item>
          </template>

          <template v-if="form.quoteRateList && (form.productNo === 'ZXH' || form.productNo === 'CEP')">
            <div
              v-for="(item, index) in form.quoteRateList"
              class="list-item"
              :key="index"
            >
              <p class="subTitle">
                报价规则{{ index + 1 }}
                <el-button
                  type="text"
                  v-if="index == form.quoteRateList.length - 1"
                  @click="addQuoteRate"
                >添加一行
                </el-button>
                <el-button
                  type="text"
                  v-if="index > 0"
                  @click="deleteQuoteRate(index)"
                  style="margin-left: 8px; color: #f00;"
                >删除
                </el-button>
              </p>
              <el-form-item
                label="默认公式类型："
                :prop="'quoteRateList[' + index + '].formulaType'"
                :rules="rules.quoteRateList.formulaType"
              >
                <el-select
                  clearable
                  v-model="item.formulaType"
                >
                  <el-option
                    v-for="rule in $dict('FormulaEnum')"
                    :key="rule.code"
                    :label="rule.desc"
                    :value="Number(rule.code)"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="比例："
                v-if="item.formulaType == 0 || item.formulaType == 2"
                :prop="'quoteRateList[' + index + '].rate'"
              >
                <el-input
                  clearable
                  :value="item.rate"
                  @input="handleRateInput(item, 'rate', $event, 0, 100)"
                >
                  <span slot="append">%</span>
                </el-input>
              </el-form-item>
              <el-form-item
                label="固定金额："
                v-if="item.formulaType == 1 || item.formulaType == 2"
                :prop="'quoteRateList[' + index + '].fixedFee'"
              >
                <el-input
                  clearable
                  :value="item.fixedFee"
                  @input="handleRateInput(item, 'fixedFee', $event)"
                >
                  <span slot="append">元</span>
                </el-input>
              </el-form-item>

              <el-form-item
                label="特殊计费-规则参数："
                label-width="200px"
              >
                <el-button
                  type="text"
                  @click="addRuleList(item)"
                >添加一行
                </el-button>
                <el-table
                  :data="item.ruleParam"
                  class="special-rule-table"
                >
                  <el-table-column width="50">
                    <template v-slot="{$index}">
                      <span>{{ $index > 0 ? '且' : '' }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column>
                    <template v-slot="{row, $index}">
                      <el-select
                        clearable
                        v-model="row.specialRuleType"
                        @change="selectRuleType(item, $index)"
                        :disabled="$index == 0"
                      >
                        <el-option
                          v-for="rule in $dict('ProductFeeSpecialRuleTypeEnum')"
                          :key="rule.code"
                          :value="Number(rule.code)"
                          :label="rule.desc"
                          :disabled="rule.code == 1"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column>
                    <template v-slot="{row, $index}">
                      <div v-if="row.specialRuleType">
                        <el-select
                          clearable
                          v-model="row.compareType"
                          :disabled="$index == 0"
                        >
                          <el-option
                            v-for="rule in $dict('CompareTypeEnum')"
                            :key="rule.code"
                            :value="Number(rule.code)"
                            :label="rule.desc"
                          >
                          </el-option>
                        </el-select>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column>
                    <template v-slot="{row, $index}">
                      <el-input
                        v-model="row.value"
                        :disabled="$index == 0"
                      ></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column>
                    <template v-slot="{$index}">
                      <el-button
                        v-if="$index > 0"
                        type="text"
                        @click="deleteRule(item, $index)"
                      >删除
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </div>
          </template>
          <template v-if="form.productNo === 'JKH'">
            <el-form-item
              label="个税承担方："
              prop="merchantCkhQuote.taxPayer"
            >
              <el-select v-model="form.merchantCkhQuote.taxPayer">
                <el-option
                  v-for="(item, index) in $dict('TaxPayerEnum')"
                  :key="index"
                  :label="item.desc"
                  :value="Number(item.code)"
                ></el-option>
              </el-select>
              <el-input
                v-model="form.merchantCkhQuote.taxRatePct"
                disabled
                style="margin-left: 8px;"
              >
                <span slot="suffix">%</span>
              </el-input>
            </el-form-item>

            <el-form-item label="个税计算公式：">
              <el-table
                :data="taxList"
                style="display: inline-block; width: auto;"
              >
                <el-table-column
                  label="级别"
                  prop="level"
                  width="50"
                ></el-table-column>
                <el-table-column
                  label="应纳税所得额"
                  prop="description"
                  width="220"
                ></el-table-column>
                <el-table-column
                  label="税率（%）"
                  prop="taxRatePct"
                  width="100"
                ></el-table-column>
                <el-table-column
                  label="速算扣除数"
                  prop="deduction"
                  width="100"
                ></el-table-column>
              </el-table>
            </el-form-item>

            <el-form-item
              label="增值税税率："
              prop="merchantCkhQuote.addedTaxRatePct"
            >
              <el-input v-model="form.merchantCkhQuote.addedTaxRatePct">
                <span slot="suffix">%</span>
              </el-input>
            </el-form-item>
            <el-form-item
              label="个税公式："
              prop="merchantCkhQuote.taxFormula"
            >
              <el-radio-group v-model="form.merchantCkhQuote.taxFormula">
                <el-radio
                  v-for="(item, index) in $dict('TaxFormulaEnum')"
                  :key="index"
                  :label="Number(item.code)"
                >{{ item.desc }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
          <template v-if="form.productNo === 'CKH'">
            <el-form-item
              label="服务费比例："
              prop="merchantCkhQuote.serviceFeeRatePct"
            >
              <el-input v-model="form.merchantCkhQuote.serviceFeeRatePct">
                <span slot="suffix">%</span>
              </el-input>
            </el-form-item>
            <el-form-item label="服务费公式：">
              服务费 = 任务金额 * 服务费比例
            </el-form-item>
            <el-form-item
              label="服务费结算模式："
              prop="merchantCkhQuote.balancedMode"
            >
              <el-radio-group v-model="form.merchantCkhQuote.balancedMode">
                <el-radio
                  v-for="(item, index) in $dict('BalancedEnum')"
                  :key="index"
                  :label="Number(item.code)"
                >{{ item.desc }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
          <template v-if="form.productNo === 'ZFT'">
            <div
              class="list-item"
              v-for="(quote, index) in form.merchantZftQuote"
              :key="index">
              <p class="subTitle">
                报价规则{{ index + 1 }}
                <el-button
                  type="text"
                  v-if="index == form.merchantZftQuote.length - 1"
                  @click="addZft"
                >添加一行
                </el-button>
                <el-button
                  type="text"
                  v-if="index > 0"
                  @click="deleteZft(index)"
                  style="margin-left: 8px; color: #f00;"
                >删除
                </el-button>
              </p>
              <el-form-item
                label="默认公式类型："
                :prop="'merchantZftQuote[' + index + '].formulaType'"
                :rules="rules.merchantZftQuote.formulaType"
              >
                <el-select
                  clearable
                  v-model="quote.formulaType"
                >
                  <el-option
                    v-for="rule in $dict('FormulaEnum')"
                    :key="rule.code"
                    :label="rule.desc"
                    :value="Number(rule.code)"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                label="固定金额："
                v-if="quote.formulaType == 1 || quote.formulaType == 2"
                :prop="'quoteRateList[' + index + '].fixedFee'"
              >
                <el-input
                  clearable
                  :value="quote.fixedFee"
                  @input="handleRateInput(quote, 'fixedFee', $event)"
                >
                  <span slot="append">元</span>
                </el-input>
              </el-form-item>
              <el-form-item
                label="比例："
                v-if="quote.formulaType == 0 || quote.formulaType == 2"
                :prop="'merchantZftQuote[' + index + '].rate'"
              >
                <el-input
                  clearable
                  :value="quote.rate"
                  @input="handleRateInput(quote, 'rate', $event, 0, 100)"
                >
                  <span slot="append">%</span>
                </el-input>
              </el-form-item>
              <el-form-item
                label="退款是否退服务费："
                :prop="'merchantZftQuote['+index+'].canRefund'"
                :rules="rules.merchantZftQuote.canRefund">
                <el-radio-group v-model="quote.canRefund">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </div>
          </template>
        </el-form>
      </div>

      <div
        class="content-container"
        v-if="activeStep === 2"
      >
        <el-form
          v-if="form.productNo !== 'ZFT'"
          label-width="200px"
          :model="form2"
          :rules="rules"
          ref="form2"
        >
          <div class="accounts_info">
            <p class="subTitle">账户信息</p>
            <el-form-item label="代征主体：">{{ form.mainstayMchName }}</el-form-item>
            <el-form-item
              label="开通发放方式："
              prop="accounts"
              ref="accounts"
            >
              <template v-for="(i, key) in $dict('ChannelTypeEnum')">
                <el-checkbox
                  v-model="channelSelect[key]"
                  :key="key"
                  :true-label="1"
                  :false-label="0"
                  @change="onChannelTypeChange($event, i)"
                >
                  {{ i.desc }}
                </el-checkbox>
              </template>
            </el-form-item>
            <template
              v-for="(account, num) in form2.accounts[form.mainstayMchNo]"
            >
              <el-form-item
                :key="num"
                :label="$dictCode('ChannelTypeEnum', account.channelType).desc + '通道'"
              >
                <el-select
                  v-if="account"
                  v-model="account['payChannelNo']"
                  @change="handlePayChannelChange(account, $event)"
                >
                  <el-option
                    v-for="(i, key) in payChannel[account.channelType - 1]"
                    :key="key"
                    :value="i.payChannelNo"
                    :label="i.payChannelName"
                  ></el-option>
                </el-select>
                <p
                  class="color-gray form-tip"
                >请选择提供{{ $dictCode('ChannelTypeEnum', account.channelType).desc }}发放服务的第三方支付机构</p>
              </el-form-item>
            </template>
          </div>
        </el-form>
        <p v-else>代付产品无需选择发放方式，请直接提交报价单</p>
      </div>

      <div
        class="form-btn-group"
        v-if="activeStep < 3"
      >
        <el-button
          type="primary"
          :loading="loading"
          @click="onSubmit"
        >{{ activeStep == 1 ? '下一步' : '提交' }}
        </el-button>
        <el-button @click="backStep">取消</el-button>
      </div>

      <div
        class="content-container text-center"
        v-if="activeStep == 3"
      >
        <img src="@/assets/success.png">
        <p class="result-title">提交成功</p>
        <p class="result-tip">报价单新增流程成功</p>
        <div class="footer-container">
          <el-button
            type="primary"
            @click="backStep"
          >返回商户列表
          </el-button>
          <el-button @click="goDetailProcess">查看审批流程</el-button>
        </div>
      </div>

      <flow-opinion
        :visible.sync="showOpinion"
        @change="changeQuote"
      ></flow-opinion>

    </div>
  </div>
</template>

<script>
import '@/styles/merchant-form.scss';
import FlowOpinion from '@/components/Flow/FlowOpinion'
import { getPayChannelList } from "@/api/levy";
import { getVendorList, getProductList } from '@/api/product'
import { editBusinessData } from '@/api/flow';
import { addQuote, listPosition } from '@/api/merchant';
import { checkCooperation } from '@/api/agent';
import { getTaxList } from '@/api/business'
import { toPromise } from '@/utils';
import { validateParams } from '@/utils/validate'

export default {
  name: 'mch-quote-form',
  components: {
    FlowOpinion,
  },
  data() {
    let _self = this
    return {
      loading: false,
      showOpinion: false,
      activeStep: 1,
      mainstayList: [],
      productList: [],
      form: {
        mchNo: '',
        mchName: '',
        productNo: '',
        productName: '',
        mainstayMchNo: '',
        mainstayMchName: '',
        quoteRateList: [{
          ruleParam: [],
          formulaType: '',
          fixedFee: '',
          rate: '',
        }],
        merchantCkhQuote: {
          taxRatePct: 100,
        },
        positionList: [],
        merchantZftQuote: [{
          canRefund: 1,
          formulaType: '',
          fixedFee: '',
          rate: '',
        }],
      },
      form2: {
        accounts: {}
      }, // { [mchNo]: []}
      rules: {
        productNo: [{
          required: true,
          message: '请选择所属产品',
          trigger: 'change'
        }],
        mainstayMchNo: [{
          required: true,
          message: '请选择所属供应商',
          trigger: 'change'
        }],
        quoteRateList: {
          formulaType: [{
            required: true,
            message: '请选择公式类型',
            trigger: 'change'
          }],
        },
        positionList: [{
          required: true,
          message: '请选择岗位',
          trigger: 'change'
        }],
        merchantCkhQuote: {
          taxPayer: [
            {
              required: true,
              message: '请选择个税承担方',
              trigger: 'change'
            }
          ],
          addedTaxRatePct: [
            {
              required: true,
              message: '请填写增值税税率',
              trigger: 'blur'
            },
            {
              validator: validateParams({
                type: 'Number',
                msg: '请填写正确格式的增值税税率'
              }),
              trigger: 'blur'
            }
          ],
          taxFormula: [
            {
              required: true,
              message: '请选择个税公式',
              trigger: 'change'
            }
          ],
          serviceFeeRatePct: [
            {
              required: true,
              message: '请填写服务费比例',
              trigger: 'blur'
            }
            // {
            //   validator: validateParams({
            //     type: 'NumberWithZero',
            //     msg: '请填写正确格式的服务费比例'
            //   }),
            //   trigger: 'blur'
            // }
          ],
          balancedMode: [
            {
              required: true,
              message: '请选择服务费结算模式',
              trigger: 'change'
            }
          ],
        },
        accounts: [
          {
            required: true,
            message: '请选择开通的发放方式',
            trigger: 'blur'
          },
          {
            validator: (rule, val, cb) => {
              let result = true;
              if (Object.keys(_self.form2.accounts).length == 0) {
                result = false;
              }
              for (let mch in _self.form2.accounts) {
                let accountList = _self.form2.accounts[mch];
                if (accountList.length == 0) {
                  result = false;
                }
                for (let i = 0; i < accountList.length; i++) {
                  if (!accountList[i].payChannelNo) {
                    result = false;
                    break;
                  }
                }
                if (!result) {
                  break;
                }
              }
              if (!result) {
                cb(new Error('请选择通道'))
              } else {
                cb();
              }
            },
            trigger: 'blur'
          }
        ],
        merchantZftQuote: {
          formulaType: [{
            required: true,
            message: '请选择公式类型',
            trigger: 'change'
          }],
          canRefund: [{
            required: true,
            message: '请选择退款选项',
            trigger: 'change'
          }]
        }
      },
      result: null,

      positionList: [], // 岗位列表

      taxList: [],

      bankChannel: [],
      alipayChannel: [],
      wxChannel: [],
      channelSelect: {},

      agentNoFromBaseInfo: '',
      mainstayTips: ''
    }
  },
  computed: {
    mchNo() {
      return this.$route.query.mchNo;
    },
    mchName() {
      return this.$route.query.mchName;
    },
    actionType() {
      return this.$route.query.actionType;
    },
    processId() {
      return this.$route.query.processId || '';
    },
    taskId() {
      return this.$route.query.taskId || '';
    },
    positionMap() {
      let map = {}
      this.positionList.forEach(item => {
        map[item.id] = item
      })
      return map
    },
    payChannel() {
      return [
        this.bankChannel,
        this.alipayChannel,
        this.wxChannel,
      ]
    },
    bindInvoiceCategoryList() {
      let ret = []
      this.form.positionList.forEach(pos => {
        if (this.positionMap[pos]) {
          ret.push(...this.positionMap[pos].invoiceCategoryList)
        }
      })
      return ret
    }
  },
  mounted() {
    this.init();
    this.listPosition();
    this.getPayChannelList();

    // 编辑
    if (this.actionType === 'EDIT') {
      const form = JSON.parse(sessionStorage.getItem('quoteInfo'));
      for (let p in form) {
        if (p === 'employerAccountInfo') {
          let info = form[p]
          if (typeof info === 'string') {
            info = JSON.parse(info)
          }
          info.forEach((i, index) => {
            if (typeof i === 'string') {
              info[index] = JSON.parse(i)
            }
          })
          this.form2.accounts = {
            [form.mainstayMchNo]: []
          }
          if (form[p].length) {
            this.form2.accounts = {
              [form.mainstayMchNo]: form[p][0][p]
            }
            form[p][0][p].forEach(item => {
              this.channelSelect[item.channelType - 1] = 1
            })
          }
        } else {
          this.$set(this.form, p, form[p])
        }
      }
      this.form.quoteRateList.forEach(item => {
        if (item.ruleParam === null) {
          item.ruleParam = [{
            "compareType": 1,
            "specialRuleType": 1,
            "value": this.form.mainstayMchNo
          }]
        }
      })
      this.getMainstayList(this.form.productNo)
    } else {
      const agentNo = JSON.parse(sessionStorage.getItem('agentNoForQuote'))
      if (agentNo) {
        this.agentNoFromBaseInfo = agentNo
      }
    }

    this.getTaxList()
  },
  beforeDestroy() {
    sessionStorage.removeItem('quoteInfo')
    sessionStorage.removeItem('agentNoForQuote')
  },
  methods: {
    async init() {
      await this.getProductList()
      if (this.form.productNo) {
        this.onProductChange(this.form.productNo)
      }
    },
    async getMainstayList(productNo) {
      // 供应商列表
      const { data: { data: list } } = await getVendorList({
        pageSize: 200,
        pageCurrent: 1,
        productNo
      });
      this.mainstayList = list;
    },
    async getProductList() {
      // 计费产品列表
      const { data } = await getProductList({
        pageCurrent: 1,
        pageSize: 300,
        productType: "0"
      });
      this.productList = data.data || []
    },
    backStep() {
      if (this.activeStep == 2) {
        this.activeStep--
      } else {
        this.$router.back()
      }
    },
    goDetailProcess() {
      this.$router.push({
        path: '/waitingHandle/detailProcess',
        query: {
          processId: this.result.commonFlowId
        }
      });
    },
    onProductChange(val) {
      for (let i = 0; i < this.productList.length; i++) {
        if (val === this.productList[i].productNo) {
          this.form.productName = this.productList[i].productName;
          this.$set(this.form.merchantCkhQuote, '_taxType', this.productList[i].description)
          break;
        }
      }
      this.getMainstayList(val)
    },
    onMainstayChange(val) {
      for (let i = 0; i < this.mainstayList.length; i++) {
        if (val === this.mainstayList[i].vendorNo) {
          this.form.mainstayMchName = this.mainstayList[i].vendorName;

          const paramItem = {
            specialRuleType: 1,
            compareType: 1,
            value: val
          };
          this.form.quoteRateList.forEach(item => {
            item.ruleParam.splice(0, 1, paramItem)
          });
          break;
        }
      }

      // 初始化form2
      this.form2.accounts = { [val]: [] }
      this.mainstayTips = ''
      if (this.agentNoFromBaseInfo && this.form.mainstayMchNo) {
        this.checkAgentCooperation()
      }
    },
    async checkAgentCooperation () {
      const { data } = await checkCooperation({
        agentNo: this.agentNoFromBaseInfo,
        mainstayNo: this.form.mainstayMchNo
      })
      if (data) {
        this.mainstayTips = data
      }
    },
    addQuoteRate() {
      const quoteItem = {
        ruleParam: [],
        formulaType: '',
        fixedFee: '',
        rate: '',
      };
      this.form.quoteRateList.push(quoteItem);
      if (this.form.mainstayMchNo) {
        this.onMainstayChange(this.form.mainstayMchNo)
      }
    },
    deleteQuoteRate(index) {
      this.form.quoteRateList.splice(index, 1);
    },
    addZft() {
      const quoteItem = {
        formulaType: '',
        fixedFee: '',
        rate: '',
        canRefund: 1,
      };
      this.form.merchantZftQuote.push(quoteItem);
    },
    deleteZft(index) {
      this.form.merchantZftQuote.splice(index, 1);
    },
    selectRuleType(rule, index) {
      rule.ruleParam[index].compareType = 1;
    },
    addRuleList(rule) {
      if (rule.ruleParam.length == 0) {
        return this.$message.error('请先选择供应商')
      }
      if (!rule.ruleParam) {
        rule.ruleParam = [];
      }
      rule.ruleParam.push({
        specialRuleType: '',
        compareType: '',
        value: ''
      })
    },
    deleteRule(rule, index) {
      rule.ruleParam.splice(index, 1)
    },
    async onSubmit() {
      if (this.activeStep == 1) {
        const [err] = await toPromise(this.$refs.form?.validate());
        if (err) {
          this.$message.error('请补充必填信息');
          return;
        }
        this.activeStep++
      } else {
        const [err] = await toPromise(this.$refs.form2?.validate());
        if (err) {
          this.$message.error('请补充必填信息');
          return;
        }
        this.showOpinion = true;
      }
    },
    async changeQuote(remark) {
      const form = JSON.parse(JSON.stringify(Object.assign({}, this.form, this.form2)));
      form.positionNameList = form.positionList?.map(item => this.positionMap[item].workCategoryName);
      form.mchNo = this.mchNo;
      form.mchName = this.mchName;
      // 增加发票
      form.invoiceCategoryList = this.bindInvoiceCategoryList
      if (this.processId) {
        await editBusinessData({
          extInfo: JSON.stringify(form),
          commonFlowId: this.processId,
          taskId: this.taskId,
          remark,
        }).finally(() => this.loading = false);
        this.$router.replace({
          path: '/waitingHandle/detailProcess',
          query: {
            processId: this.processId,
            taskId: this.taskId,
          }
        })
      } else {
        this.loading = true;
        const { data } = await addQuote({
          extObj: form,
          participant: {},
          condition: {
            referenceName: this.mchName,
            referenceNo: this.mchNo,
          },
          remark,
        }).finally(() => {
          this.loading = false;
        });
        this.result = data;
        this.activeStep++;
      }
    },
    async listPosition() {
      const { data } = await listPosition({
        mchNo: this.mchNo,
      });
      this.positionList = data || []
    },
    async getTaxList() {
      const { data } = await getTaxList()
      this.taxList = data;
    },
    onChannelTypeChange(val, type) {
      this.$refs.accounts.clearValidate()
      let channelType = type.code
      let accounts = this.form2.accounts[this.form.mainstayMchNo]
      if (val == 1) {
        accounts.push({
          "channelType": channelType,
          "channelName": type.desc,
          "payChannelNo": "",
          payChannelName: ''
        })
      } else {
        let index = accounts.findIndex(item => item.channelType == channelType)
        if (index > -1) {
          accounts.splice(index, 1)
        }
      }
    },
    handlePayChannelChange(account, val) {
      if (!val) {
        account.payChannelName = '';
      } else {
        let payChannel = this.payChannel[account.channelType - 1];
        for (let i = 0; i < payChannel.length; i++) {
          if (payChannel[i].payChannelNo == val) {
            account.payChannelName = payChannel[i].payChannelName;
          }
        }
      }
    },
    async getPayChannelList() {
      const { data } = await getPayChannelList({
        pageSize: 100,
        pageCurrent: 1,
      });
      let channels = data.data;
      channels.forEach(item => {
        item.channelType && item.channelType.forEach(i => {
          if (i == 1) {
            this.bankChannel.push(item)
          } else if (i == 2) {
            this.alipayChannel.push(item)
          } else if (i == 3) {
            this.wxChannel.push(item)
          }
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.warning-tips {
  color: #ff0000;
  font-size: 12px;
  margin-top: 10px;
}

</style>
