<template>
  <div class="merchant-page-container">
    <div class="header-container">
      <el-steps
        :active="activeStep"
        align-center
        class="el-steps-container">
        <el-step
          title="编辑产品报价单"
          v-if="actionType == 'EDIT'"></el-step>
        <el-step
          title="填写产品报价单"
          v-else></el-step>
        <el-step title="审核与公示"></el-step>
      </el-steps>
    </div>

    <div
      class="page-container"
      v-show="activeStep == 1">
      <div class="content-container">
        <el-form
          label-width="400px"
          class="block-form"
          :model="form"
          :rules="rules"
          ref="form">
          <el-form-item
            label="所属产品："
            prop="productNo">
            <el-select
              v-model="form.productNo"
              @change="onProductChange"
              :disabled="actionType == 'EDIT'"
            >
              <el-option
                v-for="(item, key) in productList"
                :key="key"
                :label="item.productName"
                :value="item.productNo"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="规则类型："
            prop="ruleType"
          >
            <el-radio-group
              v-model="form.ruleType"
              @change="onRuleTypeChange">
              <el-radio
                v-for="item in $dict('RuleTypeEnum')"
                :key="item.code"
                :label="Number(item.code)">{{ item.desc }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="供应商："
            prop="mainstayNo"
            v-show="!isNormalType">
            <el-select
              v-model="form.mainstayNo"
              clearable
              @change="onMainstayChange"
            >
              <el-option
                v-for="(item, key) in mainstayList"
                :key="key"
                :label="item.vendorName"
                :value="item.vendorNo"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            prop="realProfitRatio"
            class="form-item-main">
            <span slot="label">
              真实分润比例
              <el-tooltip content="用户控制合伙人佣金（包括二级佣金）实际分润金额">
                <i class="el-icon-question"></i>：
              </el-tooltip>
            </span>
            <el-input
              type="number"
              :value="form.realProfitRatio"
              @input="handleNumInput(form, 'realProfitRatio', $event)">
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
          <el-form-item
            label="邀请奖励计算模式："
            prop="calculateMode">
            <el-radio-group
              v-model="form.calculateMode">
              <el-radio
                v-for="item in $dict('CalculateModeEnum')"
                :key="item.code"
                :label="Number(item.code)">{{ item.desc }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <div class="form-list">
            <div
              v-for="(quote, index) in form.quoteList"
              :key="index"
              class="list-item"
            >
              <height-wrapper
                :min="300"
              >
                <p class="subTitle">
                  报价规则{{ index + 1 }}
                  <el-button
                    type="text"
                    v-if="!isNormalType && index == form.quoteList.length - 1"
                    @click="addQuote">增加报价规则
                  </el-button>
                  <el-button
                    type="text"
                    v-if="index > 0"
                    @click="deleteQuote(index)">删除
                  </el-button>
                </p>
                <!--<el-form-item-->
                <!--  label="邀请奖励计算模式："-->
                <!--  :prop="'quoteList.' + index + '.calculateMode'"-->
                <!--&gt;-->
                <!--  <el-radio-group-->
                <!--    v-model="quote.calculateMode">-->
                <!--    <el-radio-->
                <!--      v-for="item in $dict('CalculateModeEnum')"-->
                <!--      :key="item.code"-->
                <!--      :label="Number(item.code)">{{ item.desc }}-->
                <!--    </el-radio>-->
                <!--  </el-radio-group>-->
                <!--</el-form-item>-->
                <div class="list-item-detail">
                  <span class="list-item-title">合伙人成本（1级佣金）</span>
                  <el-form-item
                    label="公式类型："
                    :prop="'quoteList.' + index + '.formulaType'"
                    :rules="rules.formulaType">
                    <el-select
                      clearable
                      v-model="quote.formulaType"
                    >
                      <el-option
                        v-for="item in $dict('FormulaEnum')"
                        :key="item.code"
                        :label="item.desc"
                        :value="Number(item.code)"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    label="比例："
                    :prop="'quoteList.' + index + '.feeRate'"
                    :rules="rules.feeRate"
                    v-if="quote.formulaType == 0 || quote.formulaType == 2">
                    <el-input
                      clearable
                      :value="quote.feeRate"
                      @input="handleRateInput(quote, 'feeRate', $event, 0, 100)"
                    >
                      <span slot="append">%</span>
                    </el-input>
                  </el-form-item>
                  <el-form-item
                    label="固定金额："
                    :prop="'quoteList.' + index + '.fixedFee'"
                    :rules="rules.fixedFee"
                    v-if="quote.formulaType == 1 || quote.formulaType == 2">
                    <el-input
                      clearable
                      :value="quote.fixedFee"
                      @input="handleRateInput(quote, 'fixedFee', $event)"
                    >
                      <span slot="append">元</span>
                    </el-input>
                  </el-form-item>
                </div>
                <div
                  class="list-item-detail"
                  v-if="quote.calculateMode == 100">
                  <span class="list-item-title">邀请奖励（2级佣金）</span>
                  <el-form-item
                    label="公式类型："
                    :prop="'quoteList.' + index + '.secondFormulaType'"
                    :rules="rules.secondFormulaType"
                  >
                    <el-select
                      clearable
                      v-model="quote.secondFormulaType"
                    >
                      <el-option
                        v-for="item in $dict('FormulaEnum')"
                        :key="item.code"
                        :label="item.desc"
                        :value="Number(item.code)"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    label="比例："
                    :prop="'quoteList.' + index + '.secondFeeRate'"
                    :rules="rules.secondFeeRate"
                    v-if="quote.secondFormulaType == 0 || quote.secondFormulaType == 2">
                    <el-input
                      clearable
                      :value="quote.secondFeeRate"
                      @input="handleRateInput(quote, 'secondFeeRate', $event, 0, 100)"
                    >
                      <span slot="append">%</span>
                    </el-input>
                  </el-form-item>
                  <el-form-item
                    label="固定金额："
                    :prop="'quoteList.' + index + '.secondFixedFee'"
                    :rules="rules.secondFixedFee"
                    v-if="quote.secondFormulaType == 1 || quote.secondFormulaType == 2">
                    <el-input
                      clearable
                      :value="quote.secondFixedFee"
                      @input="handleRateInput(quote, 'secondFixedFee', $event)"
                    >
                      <span slot="append">元</span>
                    </el-input>
                  </el-form-item>
                </div>
                <el-form-item
                  label="特殊计费-规则参数："
                  label-width="200px"
                  v-show="quote.ruleType == 1">
                  <el-button
                    type="text"
                    @click="addRuleList(quote)"
                  >添加一行
                  </el-button>
                  <el-table
                    :data="quote.ruleParam"
                    class="special-rule-table">
                    <el-table-column width="50">
                      <template v-slot="{$index}">
                        <span>{{ $index > 0 ? '且' : '' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column>
                      <template v-slot="{row, $index}">
                        <el-select
                          clearable
                          v-model="row.specialRuleType"
                          @change="selectRuleType(quote, $index)"
                          :disabled="$index == 0"
                        >
                          <el-option
                            v-for="item in $dict('AgentSpecialRuleTypeEnum')"
                            :key="item.code"
                            :value="Number(item.code)"
                            :label="item.desc"
                            :disabled="item.code == 1"
                          ></el-option>
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column>
                      <template v-slot="{row, $index}">
                        <div v-if="row.specialRuleType">
                          <el-select
                            clearable
                            v-model="row.compareType"
                            :disabled="$index == 0"
                          >
                            <el-option
                              v-for="item in $dict('CompareTypeEnum')"
                              :key="item.code"
                              :value="Number(item.code)"
                              :label="item.desc">
                            </el-option>
                          </el-select>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column>
                      <template v-slot="{row, $index}">
                        <el-select
                          v-if="row.specialRuleType == 1"
                          v-model="row.value"
                          clearable
                          :disabled="$index == 0"
                        >
                          <el-option
                            v-for="(item, key) in mainstayList"
                            :key="key"
                            :label="item.vendorName"
                            :value="item.vendorNo"
                          ></el-option>
                        </el-select>
                        <el-select
                          v-else-if="row.specialRuleType == 3"
                          v-model="row.value"
                        >
                          <el-option
                            v-for="m in agentMerchant"
                            :key="m.mchNo"
                            :value="m.mchNo"
                            :label="m.mchName"></el-option>
                        </el-select>
                        <el-input
                          v-else
                          clearable
                          v-model="row.value"
                        ></el-input>
                      </template>
                    </el-table-column>
                    <el-table-column>
                      <template v-slot="{$index}">
                        <el-button
                          type="text"
                          @click="deleteRule(quote,$index)"
                          v-if="$index > 0"
                        >删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
                <el-form-item
                  label="优先级："
                  :prop="'quoteList.' + index + '.priority'"
                  :rules="rules.priority"
                >
                  <el-input
                    clearable
                    :value="quote.priority"
                    @input="handleNumInput(quote, 'priority', $event)"
                  ></el-input>
                </el-form-item>
              </height-wrapper>
            </div>
          </div>

          <div class="form-btn-group">
            <el-button
              type="primary"
              @click="onSubmit"
              :loading="loading">提交
            </el-button>
            <el-button @click="backStep">取消</el-button>
          </div>
        </el-form>
      </div>
    </div>
    <div
      class="page-container"
      v-show="activeStep == 2">
      <div class="content-container text-center">
        <img src="@/assets/success.png">
        <p class="result-title">提交成功</p>
        <p class="result-tip">报价单新增流程成功</p>
        <div class="footer-container">
          <el-button
            type="primary"
            @click="backStep">返回商户列表
          </el-button>
          <el-button @click="goDetailProcess">查看审批流程</el-button>
        </div>
      </div>
    </div>

    <flow-opinion
      :visible.sync="showOpinion"
      @change="changeQuote"
    ></flow-opinion>
  </div>
</template>

<script>
import '@/styles/merchant-form.scss';
import FlowOpinion from '@/components/Flow/FlowOpinion'
import HeightWrapper from '@/components/HeightWrapper'
import { getVendorList, getProductList } from '@/api/product'
import { editBusinessData } from '@/api/flow';
import { editQuote, getBusinessRelation } from '@/api/agent'
import { toPromise } from '@/utils'

export default {
  name: 'agent-quote-form',
  components: {
    FlowOpinion,
    HeightWrapper,
  },
  data() {
    const self = this;
    return {
      loading: false,
      showOpinion: false,
      activeStep: 1,
      mainstayList: [],
      productList: [],
      form: {
        "productNo": "",
        "productName": "",
        "ruleType": 0,
        "mainstayNo": '',
        "realProfitRatio": null,
        "calculateMode": 100,
        quoteList: [{
          "calculateMode": 100,
          "formulaType": '',
          "fixedFee": '',
          "feeRate": '',
          "ruleType": 0,
          "secondFormulaType": '',
          "secondFixedFee": '',
          "secondFeeRate": '',
          "maxFee": 99999999,
          "minFee": 0.01,
          "priority": '',
          "ruleParam": [],
          "flowId": ""
        }],
      },
      rules: {
        productNo: [{ required: true, message: '请选择所属产品', trigger: 'change' }],
        ruleType: [{ required: true, message: '请选择规则类型', trigger: 'change' }],
        priority: [{ required: true, message: '请输入优先级', trigger: 'blur' }],
        formulaType: [{ required: true, message: '请选择公式类型', trigger: 'change' }],
        calculateMode: [{ required: true, message: '请选择邀请奖励计费模式', trigger: 'change' }],
        realProfitRatio: [
          { required: true, message: '请输入真实分润比例' }
        ],
        mainstayNo: [{
          validator: (rule, val, cb) => {
            if (self.form.ruleType == 0) {
              cb()
            } else {
              if (val) {
                cb()
              } else {
                cb(new Error('请选择供应商'))
              }
            }
          }, trigger: 'change'
        }],
        fixedFee: [
          {
            required: true, validator: (rule, val, cb) => {
              let index = rule.field.split('.')[1];
              let valid = true;
              if (self.form.quoteList[index].formulaType > 0) {
                valid = (val !== '')
              }
              if (valid) {
                cb()
              } else {
                cb(new Error('请输入固定金额'))
              }
            }, trigger: 'blur'
          },
        ],
        feeRate: [{
          required: true, validator: (rule, val, cb) => {
            let index = rule.field.split('.')[1];
            let valid = true;
            if (self.form.quoteList[index].formulaType === 0 || self.form.quoteList[index].formulaType == 2) {
              valid = (val !== '')
            }
            if (valid) {
              cb()
            } else {
              cb(new Error('请输入比例'))
            }
          }, trigger: 'blur'
        }],
        secondFormulaType: [{ required: true, message: '请选择公式类型', trigger: 'change' }],
        secondFixedFee: [
          {
            required: true,
            validator: (rule, val, cb) => {
              let index = rule.field.split('.')[1];
              let valid = true;
              if (self.form.quoteList[index].secondFormulaType > 0) {
                valid = (val !== '')
              }
              if (valid) {
                cb()
              } else {
                cb(new Error('请输入固定金额'))
              }
            }, trigger: 'blur'
          }
        ],
        secondFeeRate: [
          {
            required: true,
            validator: (rule, val, cb) => {
              let index = rule.field.split('.')[1];
              let valid = true;
              if (self.form.quoteList[index].secondFormulaType === 0 || self.form.quoteList[index].secondFormulaType === 2) {
                valid = (val !== '')
              }
              if (valid) {
                cb()
              } else {
                cb(new Error('请输入比例'))
              }
            }, trigger: 'blur'
          }
        ]
      },
      result: null,
      agentMerchant: [],
    }
  },
  computed: {
    agentNo() {
      return this.$route.query.agentNo;
    },
    agentName() {
      return this.$route.query.agentName;
    },
    actionType() {
      return this.$route.query.actionType;
    },
    processId() {
      return this.$route.query.processId || '';
    },
    taskId() {
      return this.$route.query.taskId || '';
    },
    isNormalType() {
      // 是否为通用规则
      return this.form.ruleType == 0;
    }
  },
  watch: {
    'form.calculateMode': {
      handler(val) {
        this.form.quoteList.forEach(item => {
          item.calculateMode = val
        })
      }
    }
  },
  mounted() {
    // 编辑
    if (this.actionType === 'EDIT') {
      const form = JSON.parse(sessionStorage.getItem('quoteInfo'));
      const { productNo, productName, ruleType, realProfitRatio, calculateMode } = form[0];
      this.form.productNo = productNo;
      this.form.productName = productName;
      this.form.ruleType = ruleType;
      this.form.realProfitRatio = realProfitRatio
      this.form.calculateMode = calculateMode

      if (ruleType == 1) {
        this.form.mainstayNo = form[0]['ruleParam'][0]['value']
      }
      if (form[0].quoteRateList) {
        this.form.quoteList = form[0].quoteRateList;
      } else {
        this.form.quoteList = form;
      }
    }
    this.init();
    this.getAgentMerchant()
  },
  beforeDestroy() {
    sessionStorage.removeItem('quoteInfo')
  },
  methods: {
    async init() {
      await Promise.all([
        this.getMainstayList(),
        this.getProductList(),
      ])
      if (this.form.ruleType == 1) {
        this.onMainstayChange(this.form.mainstayNo)
      }
    },
    async getMainstayList() {
      // 供应商列表
      const { data: { data: list } } = await getVendorList({
        pageSize: 200,
        pageCurrent: 1,
      });
      this.mainstayList = list;
    },
    async getProductList() {
      // 计费产品列表
      const { data } = await getProductList({
        pageCurrent: 1,
        pageSize: 300
      });
      this.productList = data.data || []
    },
    backStep() {
      this.$router.back();
    },
    goDetailProcess() {
      this.$router.push({
        path: '/waitingHandle/detailProcess',
        query: {
          processId: this.result.commonFlowId
        }
      });
    },
    selectRuleType(quote, index) {
      quote.ruleParam[index].compareType = 1;
      quote.ruleParam[index].value = '';
    },
    addRuleList(quote) {
      if (!this.form.mainstayNo) {
        return this.$message.error('请先选择供应商')
      }
      if (!quote.ruleParam) {
        quote.ruleParam = [];
      }
      quote.ruleParam.push({
        specialRuleType: '',
        compareType: '',
        value: ''
      })
    },
    deleteRule(quote, index) {
      quote.ruleParam.splice(index, 1)
    },
    onProductChange(val) {
      for (let i = 0; i < this.productList.length; i++) {
        if (val === this.productList[i].productNo) {
          this.form.productName = this.productList[i].productName;
          break;
        }
      }
    },
    async onSubmit() {
      const [err] = await toPromise(this.$refs.form.validate());
      if (err) return this.$message.error('请补充必填信息');
      if (this.form.realProfitRatio > 100 || this.form.realProfitRatio <= 0) {
        this.$message.error("真实分润比例只能为(0，100]的整数");
        return;
      }
      this.showOpinion = true;
    },
    async changeQuote(remark) {
      const quoteList = JSON.parse(JSON.stringify(this.form.quoteList));
      quoteList.forEach(quote => {
        quote.agentNo = this.agentNo;
        quote.agentName = this.agentName;
        quote.productNo = this.form.productNo;
        quote.productName = this.form.productName;
        quote.realProfitRatio = this.form.realProfitRatio
        if (quote.ruleType == 1) {
          quote.mainstayNo = quote.ruleParam[0].value;
          quote.mainstayName = this.form.mainstayName
        } else {
          quote.ruleParam = [];
          quote.mainstayNo = null;
          quote.mainstayName = null;
        }
      });
      if (this.processId) {
        await editBusinessData({
          extInfo: JSON.stringify(quoteList),
          commonFlowId: this.processId,
          taskId: this.taskId,
          remark,
        }).finally(() => this.loading = false);
        this.$router.push({
          path: '/waitingHandle/detailProcess',
          query: {
            processId: this.processId,
            taskId: this.taskId,
          }
        })
      } else {
        this.loading = true;
        const { data } = await editQuote({
          extObj: quoteList,
          remark,
          condition: {
            referenceName: this.agentName,
            referenceNo: this.agentNo,
          },
        }).finally(() => {
          this.loading = false;
        });
        this.result = data;
        this.activeStep++;
      }
    },
    onRuleTypeChange(val) {
      this.form.quoteList.forEach(item => {
        item.ruleType = val;
      })
    },
    // onCalculateModeChange(val){
    //   this.form.quoteList.forEach(item =>{
    //     item.calculateMode = val;
    //   })
    // },
    addQuote() {
      this.form.quoteList.push({
        "calculateMode": this.form.calculateMode,
        "formulaType": '',
        "fixedFee": '',
        "feeRate": '',
        "ruleType": this.form.ruleType,
        "secondFormulaType": '',
        "secondFixedFee": '',
        "secondFeeRate": '',
        "maxFee": 99999999,
        "minFee": 0.01,
        "priority": '',
        "ruleParam": [],
        "flowId": ""
      });
      this.onMainstayChange(this.form.mainstayNo)
    },
    deleteQuote(index) {
      this.form.quoteList.splice(index, 1)
    },
    onMainstayChange(val) {
      this.form.quoteList.forEach(item => {
        if (item.ruleParam.length == 0) {
          item.ruleParam.push({
            specialRuleType: 1,
            compareType: 1,
            value: ''
          })
        }
        item.ruleParam[0].value = val
      });
      for (let i = 0; i < this.mainstayList.length; i++) {
        if (this.mainstayList[i].vendorNo == val) {
          this.form.mainstayName = this.mainstayList[i].vendorName;
          break
        }
      }
    },
    async getAgentMerchant() {
      const { data: { data } } = await getBusinessRelation({
        relationType: '101',
        agentNo: this.agentNo,
        pageSize: 200,
        pageCurrent: 1,
      })
      this.agentMerchant = data
    }
  },
}
</script>

<style
  scoped
  lang="scss">
.merchant-page-container {

  .content-container {
    background: #fff;
  }

  .special-rule-table ::v-deep {
    .el-input, .el-textarea {
      width: 100%;
    }
  }

  .list-item-detail {
    position: relative;
    margin: 32px;
    padding: 16px;
    border: 1px dashed #ddd;
  }
}
</style>
