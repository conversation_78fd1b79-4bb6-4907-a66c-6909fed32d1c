<template>
  <el-dialog
    :visible.sync="visible"
    :title="title"
    :before-close="close">
    <el-form
      :model="form"
      :rules="rules"
      label-width="150px"
      ref="form">
      <el-form-item
        label="用工企业："
        prop="employerNo">
        <el-select
          v-model="form.employerNo"
          :disabled="!!form.id"
          filterable>
          <el-option
            v-for="item in mchList"
            :key="item.id"
            :label="item.mchName"
            :value="item.mchNo">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="供应商："
        prop="mainstayNo">
        <el-select
          v-model="form.mainstayNo"
          :disabled="!!form.id"
          filterable>
          <el-option
            v-for="item in mainstayList"
            :key="item.id"
            :label="item.mchName"
            :value="item.mchNo">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="税款归属时期："
        prop="dateEnd">
        <date-picker
          :start-time.sync="form.dateBegin"
          :end-time.sync="form.dateEnd"
          type="date"
          picker="separate"
          :is-show-fast-time="false"
        ></date-picker>
      </el-form-item>
      <el-form-item label="备注：">
        <el-input
          type="textarea"
          :rows="5"
          v-model="form.remark"></el-input>
      </el-form-item>
      <el-form-item
        label="完税证明："
        prop="certificateFilePath">
        <file-upload
          :urls.sync="form.certificateFilePath"
          :options="{pdf: true, word: true}"
          multi-upload
          accept=".rar,.zip"
          name="完税证明">
        </file-upload>
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="confirm">确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { getAllMerchant, getMainstayList } from "@/api/merchant";
import FileUpload from "@/components/FileUpload/index.vue";
import { toPromise } from "@/utils";
import { addTaxCert, editTaxCert } from "@/api/invoice";

export default {
  name: 'TaxCertDialog',
  components: { FileUpload },
  data(vm) {
    return {
      visible: false,
      mainstayList: [],
      mchList: [],
      rules: {
        employerNo: [{ required: true, message: '请选择用工企业', trigger: 'blur' }],
        mainstayNo: [{ required: true, message: '请选择供应商', trigger: 'blur' }],
        dateEnd: [
          { required: true, message: '请选择时间', trigger: 'blur' },
          {
            validator(rule, val, cb) {
              if (!vm.form.dateBegin || !vm.form.dateEnd) {
                cb(new Error('请选择时间'))
              } else {
                cb()
              }
            }, trigger: 'blur'
          }
        ],
        certificateFilePath: [{ required: true, message: '请上传完税证明', trigger: 'change' }],
      },
      form: {},
    }
  },
  computed: {
    title() {
      return this.form.id ? '编辑完税证明' : '上传完税证明'
    },
  },
  watch: {
    'form.employerNo': {
      handler(value) {
        if (!value) return this.form.employerName = ''
        const item = this.mchList.find(mch => mch.mchNo === value)
        if (item) {
          this.form.employerName = item.mchName
        }
      }
    },
    'form.mainstayNo': {
      handler(value) {
        if (!value) return this.form.mainstayName = ''
        const item = this.mainstayList.find(mch => mch.mchNo === value)
        if (item) {
          this.form.mainstayName = item.mchName
        }
      }
    },
  },
  methods: {
    open(row) {
      this.visible = true
      this.getMainstayList()
      this.getMchList()
      this.$nextTick(this.copyInfo(row))
    },
    copyInfo(data) {
      if (data) {
        this.form = JSON.parse(JSON.stringify(data))
      }
    },
    close() {
      this.form = {}
      this.visible = false
    },
    async getMainstayList() {
      const { data } = await getMainstayList()
      this.mainstayList = data
    },
    async getMchList() {
      const { data } = await getAllMerchant({ merchantType: 100 })
      this.mchList = data
    },
    async confirm() {
      const [err] = await toPromise(this.$refs.form.validate())
      if (err) return
      const api = this.form.id ? editTaxCert : addTaxCert
      await api(this.form)
      this.$message.success('操作成功')
      this.close()
      this.$emit('change')
    }

  }
}
</script>
