<template>
  <el-dialog
    class="dialog-container"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :title="'编辑开票记录'"
    @close="close"
    append-to-body
    width="700px"
  >
    <div class="form-container">
      <el-form :model="form" ref="form" :rules="rules" size="mini" label-width="200px">
        <el-form-item label="发票流水：" prop="trxNo">
          {{ form.trxNo }}
        </el-form-item>
        <el-form-item label="邮寄信息：">
          <p style="width: 400px">{{ form.expressConsignee }}    {{ form.expressTelephone }}</p>
          <p style="width: 400px">{{ form.county }} {{ form.province }} {{ form.city }} {{ form.address }}</p>
        </el-form-item>
        <el-form-item label="发票类型：" prop="invoiceType">
          <el-radio-group v-model="form.invoiceType">
            <el-radio v-for="(item, index) in $dict('InvoiceTypeEnum')" :key="index" :label="parseInt(item.code)">
              {{ item.desc }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="开票类目：" prop="invoiceCategoryCode">
          <el-select v-model="form.invoiceCategoryCode" @change="invoiceCategorySelect">
            <el-option v-for="(item, index) in invoiceInfo.invoiceCategoryVoList"
                       :key="index" :label="item.invoiceCategoryName" :value="item.invoiceCategoryCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开票金额：" class="issue-amount">
          <p style="color: red;width: 400px">{{ form.invoiceAmount | moneyFormat }}</p>
        </el-form-item>
        <el-form-item label="企业名称：">
          <p style="width: 400px">
            {{ form.employerMchName }}
          </p>
        </el-form-item>
        <el-form-item label="纳税人识别号：">
          <p style="width: 400px">
            {{ form.taxNo }}
          </p>
        </el-form-item>
        <el-form-item label="单位注册地址及电话：">
          <p style="width: 400px">
            {{ form.registerAddrInfo }}
          </p>
        </el-form-item>
        <el-form-item label="开户行及帐号：">
          <p style="width: 400px">
            {{ form.bankName }}
          </p>
          <p style="width: 400px">
            {{ form.accountNo }}
          </p>
        </el-form-item>
        <el-form-item label="开票说明：">
          <el-input type="textarea" rows="3" v-model="form.remark" maxlength="200" show-word-limit
                    style="width: 400px"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="confirm">保存</el-button>
      <el-button @click="close">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {getByTrxNo, getInvoiceInfoWithMainstayNo, updateInvoiceRecord} from '@/api/invoice';

  export default {
    data() {
      return {
        visible: false,
        loading: false,
        form: {
          trxNo: '',
          expressConsignee: null,
          expressTelephone: null,
          province: null,
          city: null,
          county: null,
          address: null,

          invoiceType: null,
          invoiceCategoryCode: null,
          invoiceCategoryName: null,
          invoiceAmount: null,
          employerMchNo: null,
          employerMchName: null,
          taxNo: null,
          registerAddrInfo: null,
          bankName: '',
          accountNo: null,
          remark: null,
        },
        invoiceInfo: {},

        rules: {
          invoiceType: [
            { required: true, message: '请选择发票类型', trigger: 'blur' }
          ],
          invoiceCategoryCode: [
            { required: true, message: '请选择开票类目', trigger: 'blur' }
          ],
        }
      }
    },
    methods: {
      async initAndShow(record) {
        const [{ data }, { data: invoiceInfo }] = await Promise.all([
          getByTrxNo({
            trxNo: record.trxNo
          }),
          getInvoiceInfoWithMainstayNo({
            mchNo: record.employerMchNo,
            mainstayNo: record.mainstayMchNo,
            workCategoryCode: record.workCategoryCode
          })
        ]);
        this.form = data
        this.invoiceInfo = invoiceInfo
        this.visible = true
      },
      invoiceCategorySelect(val) {
        if (!this.invoiceInfo || !this.invoiceInfo.invoiceCategoryVoList) {
          return
        }
        for (let idx in this.invoiceInfo.invoiceCategoryVoList) {
          const item = this.invoiceInfo.invoiceCategoryVoList[idx]
          if (item.invoiceCategoryCode === val) {
            this.form.invoiceCategoryName = item.invoiceCategoryName
            return;
          }
        }
      },
      async confirm() {
        this.$refs.form.validate(valid => {
          if (!valid) {
            return
          }
          updateInvoiceRecord(this.form).then(resp => {
            if (resp.code === 20000) {
              this.$message.success(resp.data);
              this.$emit("refresh");
              this.close();
            } else {
              this.$message.error(resp.message);
            }
          })
        })
      },
      close() {
        this.form = {
          trxNo: '',
          expressConsignee: null,
          expressTelephone: null,
          province: null,
          city: null,
          county: null,
          address: null,

          invoiceType: null,
          invoiceCategoryCode: null,
          invoiceCategoryName: null,
          invoiceAmount: null,
          employerMchNo: null,
          employerMchName: null,
          taxNo: null,
          registerAddrInfo: null,
          bankName: '',
          accountNo: null,
          remark: null,
        }
        this.visible = false
      }
    }
  }
</script>

<style lang="scss" scoped>
.dialog-container {

  //.form-container {
  //  width: 800px;
  //}
}
</style>
