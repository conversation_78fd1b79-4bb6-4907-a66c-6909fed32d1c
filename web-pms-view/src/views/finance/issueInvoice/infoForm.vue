<template>
  <div class="page-container">
    <div class="content-container">
      <el-form label-width="200px" :disabled="shouldEdit">
        <el-form-item label="企业名称：">
          <el-input v-model="invoiceInfo.mchName"></el-input>
        </el-form-item>

        <el-form-item label="纳税人类型：">
          <el-radio-group v-model="invoiceInfo.taxPayerType">
            <el-radio v-for="(item, index) in $dict('TaxPayerTypeEnum')" :key="index" :label="Number(item.code)">
              {{ item.desc }}
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="纳税人识别号：">
          <el-input v-model="invoiceInfo.taxNo"></el-input>
        </el-form-item>

        <el-form-item label="单位注册地址及电话：">
          <el-input v-model="invoiceInfo.registerAddrInfo"></el-input>
        </el-form-item>

        <el-form-item label="开户行及账号：">
          <el-input v-model="invoiceInfo.bankName"></el-input>
          <el-input v-model="invoiceInfo.accountNo"></el-input>
        </el-form-item>

        <el-form-item label="发票类目：">
          <el-tag type="info" v-for="(item, index) in invoiceInfo.invoiceCategoryVoList" :key="index">
            {{ item.invoiceCategoryName }}
          </el-tag>
        </el-form-item>

        <el-form-item label="默认开票类目：">
          <el-select
            v-model="invoiceInfo.defaultInvoiceCategoryCode"
            @change="handleChange">
            <el-option
              v-for="(item, key) in invoiceInfo.invoiceCategoryVoList"
              :key="key"
              :label="item.invoiceCategoryName"
              :value="item.invoiceCategoryCode"
            ></el-option>
          </el-select>
        </el-form-item>

      </el-form>
      <div class="form-btn text-center">
        <el-button @click="updateInvoiceInfo" :type="btnType">{{btnWord}}</el-button>
        <el-button @click="cancel" v-if="!shouldEdit">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { updateInvoiceInfo, getEmployerInfo } from '@/api/merchant'
export default {
  data() {
    return {
      invoiceInfo: {},
      categoryMap: {},
      shouldEdit: true,
    }
  },
  computed: {
    btnType() {
      return this.shouldEdit ? '' : 'primary';
    },
    btnWord() {
      return this.shouldEdit ? '编辑' : '提交';
    },
    mchNo() {
      return this.$route.query.mchNo || '';
    }
  },
  mounted() {
    let invoiceInfo = sessionStorage.getItem('invoiceInfo');
    if (invoiceInfo) {
      this.$set(this, 'invoiceInfo', JSON.parse(invoiceInfo));
      this.invoiceInfo.invoiceCategoryVoList && this.invoiceInfo.invoiceCategoryVoList.forEach(item => {
        this.$set(this.categoryMap, item.invoiceCategoryCode, item.invoiceCategoryName)
      })
    }
  },
  destroyed() {
    sessionStorage.removeItem('invoiceInfo');
  },
  methods: {
    handleChange(val) {
      this.invoiceInfo.defaultInvoiceCategoryName = this.categoryMap[val];
    },
    async updateInvoiceInfo() {
      if (this.shouldEdit) {
        return this.shouldEdit = false;
      }
      const { data } = await updateInvoiceInfo({...this.invoiceInfo, mchNo: this.mchNo});
      this.$message.success(data);
      this.shouldEdit = true;
      this.$router.push({
        path: `/merchant/merchantInfo/${this.mchNo}`
      });
    },
    async cancel() {
      this.shouldEdit = true;
      const { data } = await getEmployerInfo({
        mchNo: this.mchNo
      })
      this.$set(this, 'invoiceInfo', data.invoiceInfo);
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
