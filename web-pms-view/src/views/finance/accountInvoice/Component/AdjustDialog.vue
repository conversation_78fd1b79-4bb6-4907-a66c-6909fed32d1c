<template>
  <el-dialog
    :visible.sync="visible"
    title="调账"
    :before-close="close">
    <el-form ref="form" :model="form" :rules="rules" label-position="right" label-width="100px">
      <el-form-item label="账号" prop="accountNo">
        <el-input v-model="form.accountNo" disabled></el-input>
      </el-form-item>
      <el-form-item label="金额" prop="amount">
        <el-input v-model="form.amount"></el-input>
      </el-form-item>
      <el-form-item label="调账方式" prop="accountProcessType">
        <el-select clearable v-model="form.accountProcessType">
          <el-option
            v-for="(item, index) in types"
            :key="index"
            :label="item.text"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="form.remark" type="textarea"></el-input>
      </el-form-item>
    </el-form>
    <div class="footer-func-btn">
      <el-button type="primary" @click="adjustAccount">确认</el-button>
      <el-button @click="close">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { adjustAccount } from '@/api/accountInvoice'
export default {
  props: {
    visible: Boolean,
    data: Object,
  },
  watch: {
    data(val) {
      for(let p in this.form) {
        if (p in val) {
          this.form[p] = val[p];
        }
      }
    }
  },
  data() {
    return {
      form: {
        accountNo: '',
        amount: '',
        accountProcessType: '',
        remark: ''
      },
      rules: {
        accountNo: [{ required: true, message: "请输入账号", trigger: "blur" }],
        amount: [{ required: true, message: "请输入金额", trigger: "blur" }],
        accountProcessType: [{ required: true, message: "请选择调账方式", trigger: "change" }],
        remark: [{ required: true, message: "请输入备注", trigger: "blur" }],
      },
      types: [
        { value: 4, text: '调增' },
        { value: 5, text: '调减' },
      ]
    }
  },
  methods: {
    close() {
      this.$refs.form.resetFields();
      this.$emit('update:visible', false);
    },
    adjustAccount() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const { data } = await adjustAccount(this.form);
          this.$message.success(data);
          this.$emit('refresh');
          this.close();
        }
      })
    },
  }
}
</script>

<style lang="scss" scoped>
  .footer-func-btn {
    text-align: center;
  }
</style>