<template>
  <div class="box-container" v-loading="loading">
    <div class="search-container">
      <div class="flex-container">

        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">处理流水号：</span>
            <el-input v-model="searchParam.accountProcessNo"/>
          </div>

          <div class="flex-item">
            <el-tooltip content="用工企业编号" placement="top">
              <span class="flex-item__label">企业编号：</span>
            </el-tooltip>
            <el-input v-model="searchParam.employerMchNo" placeholder="须与创建时间一起使用"/>
          </div>

          <div class="flex-item">
            <el-tooltip placement="top" content="代征主体编号">
              <span class="flex-item__label">代征编号：</span>
            </el-tooltip>
            <el-input v-model="searchParam.mainstayMchNo" placeholder="须与创建时间一起使用"/>
          </div>
        </div>

        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">平台流水号：</span>
            <el-input v-model="searchParam.trxNo" placeholder="须与创建时间一起使用"/>
          </div>

          <div class="flex-item">
            <span class="flex-item__label">发送阶段：</span>
            <el-select clearable v-model="searchParam.callbackStage">
              <el-option
                v-for="{code,desc} in $dict('AccountProcessResultCallbackStageEnum')"
                :label="desc"
                :key="parseInt(code)"
                :value="parseInt(code)"/>
            </el-select>
          </div>

          <div class="flex-item">
            <span class="flex-item__label">审核阶段：</span>
            <el-select clearable v-model="searchParam.auditStage">
              <el-option
                v-for="{code,desc} in $dict('AccountProcessResultAuditStageEnum')"
                :label="desc"
                :key="parseInt(code)"
                :value="parseInt(code)"/>
            </el-select>
          </div>
        </div>

        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">处理结果：</span>
            <el-select clearable v-model="searchParam.processResult">
              <el-option key="1" :value="1" label="成功"/>
              <el-option key="2" :value="-1" label="失败"/>
            </el-select>
          </div>

          <div class="flex-item">
            <span class="flex-item__label">是否异步：</span>
            <el-select clearable v-model="searchParam.isFromAsync">
              <el-option key="1" :value="1" label="是"/>
              <el-option key="2" :value="-1" label="否"/>
            </el-select>
          </div>
        </div>

        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              ref="datepicker"
              v-model="createTimeRange"
              type="datetimerange"
              @change="getTimeRange"/>
          </div>
        </div>

        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-switch @change="onHistoryChange" v-model="inHistory" active-text="查询历史表" inactive-text="查询实时表"/>
            <el-button type="primary" @click="refreshList(true)"><i class="el-icon-search"/>查询</el-button>
            <el-button @click="auditProcessResult({rows:selectedRow,auditType:parseInt($dictFlag('AccountResultAuditTypeEnum','AUDIT_TO_REPROCESS').code)})">
              批量审核为重新处理
            </el-button>
            <el-button @click="resetField" type="text">清空筛选条件</el-button>
          </div>
        </div>

      </div>
    </div>

    <div class="content-container">
      <el-table :data="pageResult.data" row-key="id" @selection-change="val=>selectedRow=val">
        <el-table-column type="selection"/>
        <el-table-column type="index" :index="getIndex" label="序号"/>

        <el-table-column label="创建时间" prop="createTime" width="150px">
          <p slot-scope="{row, column}" v-html="renderTime(row[column['property']])"></p>
        </el-table-column>

        <el-table-column label="账务处理流水号" prop="accountProcessNo" width="200px"/>

        <el-table-column label="账务处理结果" prop="processResult" v-slot="{row}" width="100px">
          <span v-if="row.processResult===100" style="color: green;">成功</span>
          <span v-else-if="row.processResult===101" style="color: red;">失败</span>
        </el-table-column>

        <el-table-column label="错误编码" prop="errorCode" width="80px"/>

        <el-table-column label="需要回调" width="80px"
                         :formatter="row=>row.requestDto&&JSON.parse(row.requestDto).callbackDestination?'是':'否'"/>

        <el-table-column label="异步账务" width="80px"
                         :formatter="row=>row.isFromAsync===1?'是':'否'"/>

        <el-table-column label="审核阶段" width="80px" v-slot="{row}">
          <span v-if="row.auditStage===parseInt($dictFlag('AccountProcessResultAuditStageEnum','AUDIT_NONE').code)" style="color: green;">不审核</span>
          <span v-else-if="row.auditStage===parseInt($dictFlag('AccountProcessResultAuditStageEnum','AUDIT_WAITING').code)" style="color: red;">待审核</span>
          <span v-else-if="row.auditStage===parseInt($dictFlag('AccountProcessResultAuditStageEnum','AUDIT_FINISHED').code)" style="color: gray">已审核</span>
        </el-table-column>


        <el-table-column label="发送阶段" width="80px" v-slot="{row}">
          <span v-if="row.callbackStage===parseInt($dictFlag('AccountProcessResultCallbackStageEnum','PENDING').code)" style="color: red;">待发送</span>
          <span v-else-if="row.callbackStage===parseInt($dictFlag('AccountProcessResultCallbackStageEnum','SENT').code)" style="color: green;">已发送</span>
          <span v-else-if="row.callbackStage===parseInt($dictFlag('AccountProcessResultCallbackStageEnum','NONE_SEND').code)" style="color: gray">不发送</span>
        </el-table-column>


        <el-table-column prop="remark" label="备注" show-overflow-tooltip/>

        <el-table-column fixed="right" label="操作" v-slot="{row}" width="190px">
          <el-button type="text" size="small" @click="viewAccountProcessResultPage(row)"
                     v-permission="'account:invoice:result:view'">查看详情
          </el-button>

          <el-button type="text" size="small" @click="resendProcessResultCallback(row)"
                     v-if="(row.callbackStage===parseInt($dictFlag('AccountProcessResultCallbackStageEnum','PENDING').code)
                     ||row.callbackStage===parseInt($dictFlag('AccountProcessResultCallbackStageEnum','SENT').code))
                      && row.auditStage!==parseInt($dictFlag('AccountProcessResultAuditStageEnum','AUDIT_WAITING').code)"
                     v-permission="'account:invoice:result:callback'">发送通知
          </el-button>

          <el-dropdown size="small" placement="right" trigger="click" @command="auditProcessResult" v-permission="'account:invoice:result:audit'"
                       v-if="row.auditStage===parseInt($dictFlag('AccountProcessResultAuditStageEnum','AUDIT_WAITING').code)"
                       style="margin-left: 10px;color: red;">
            <span class="el-dropdown-link" style="cursor: pointer;">
              审核<i class="el-icon-arrow-down el-icon--right"/>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="{code,desc} in $dict('AccountResultAuditTypeEnum')" :key="code" :command="{rows:[row],auditType:parseInt(code)}">{{desc}}</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>

        </el-table-column>
      </el-table>
    </div>

    <el-footer class="main-page-footer">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[15,30,50,100]"
        :total="pageResult.totalRecord"
        :page-size.sync="pageParam.pageSize"
        :current-page.sync="pageParam.pageCurrent"
        @size-change="refreshList"
        @current-change="refreshList"/>
    </el-footer>

    <account-Invoice-process-result-view ref="processResultView"/>
  </div>
</template>

<script>
  import AccountInvoiceProcessResultView from './AccountInvoiceProcessResultView'
  import {formatMoney} from "@/utils/commonUtil"
  import dayjs from "dayjs"
  import { getAccountInvoiceResult, viewProcessResult, resendProcessResultCallback, auditProcessResult } from '@/api/accountInvoice'

  export default {
    name: "PmsAccountInvoiceProcessResultList",
    components: {
      AccountInvoiceProcessResultView
    },
    data() {
      return {
        searchParam: {},
        pageParam: {pageCurrent: 1, pageSize: 30},
        inHistory: false,
        createTimeRange: [],
        pageResult: {},
        selectedRow: [],
        loading: false,
      };
    },
    methods: {
      formatMoney,
      refreshList(initFlag) {
        if (initFlag === true) {
          this.pageParam.pageCurrent = 1;
        }
        this.loading = true
        getAccountInvoiceResult(
          Object.assign({}, this.searchParam, this.createTimeRange && {createTimeBegin: this.createTimeRange[0], createTimeEnd: this.createTimeRange[1]}),
          {
            inHistory: this.inHistory,
            ...this.pageParam
          }
        )
          .then(({data}) => {
            this.pageResult = data;
          }).finally(() => {
          this.loading = false
        });
      },
      viewAccountProcessResultPage(row) {
        viewProcessResult({
          id: row.id,
        }, {
          inHistory: this.inHistory,
        })
          .then(({data}) => {
            this.$refs.processResultView.processResult = data;
            this.$refs.processResultView.show = true;
          })
      },
      resendProcessResultCallback(row) {
        this.$confirm('确认发送账务处理结果通知吗?')
          .then(() => resendProcessResultCallback({id: row.id}, {inHistory: row.inHistory}))
          .then(({data}) => {
            this.$message.success(data);
          });
      },
      auditProcessResult({rows, auditType}) {
        if (!rows || rows.length === 0 || rows.find(row => row.auditStage !== parseInt(this.$dictFlag('AccountProcessResultAuditStageEnum', 'AUDIT_WAITING').code))) {
          this.$message.error("请选择需要审核的账务处理结果")
          return
        }
        this.$confirm(`确定将${rows.length}条账务处理结果审核为:" <span style="color: red;">${this.$dictCode('AccountResultAuditTypeEnum', auditType).desc}</span>" 吗?`, {dangerouslyUseHTMLString: true})
          .then(() => auditProcessResult({auditType, ids: +rows.map(row => row.id)}))
          .then(({data}) => {
            this.$message.success(data)
            this.refreshList()
          })
      },
      onHistoryChange(history) {
        if (history) {
          this.createTimeRange = [dayjs(new Date()).add(-1, 'M').format("YYYY-MM-DD HH:mm:ss"), dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")];
        } else {
          this.createTimeRange = [];
        }
        this.pageResult = {}
      },
      getIndex(index) {
        return (this.pageParam.pageCurrent - 1) * this.pageParam.pageSize + index + 1;
      },
      getTimeRange(data) {
        this.createTimeRange = data;
      },
      resetField() {
        this.searchParam = {};
        this.$refs.datepicker.clearTime();
      }
    },
    mounted() {
      this.refreshList();
    }
  };
</script>

