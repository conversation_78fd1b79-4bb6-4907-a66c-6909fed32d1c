<template>
  <div class="box-container" v-loading="loading">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">处理流水号：</span>
            <el-input v-model="searchParam.accountProcessNo"/>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">处理阶段：</span>
            <el-select clearable v-model="searchParam.processStage">
              <el-option v-for="{code,desc} in $dict('AccountProcessPendingStageEnum')" :label="desc" :key="parseInt(code)" :value="parseInt(code)"/>
            </el-select>
          </div>

        </div>

        <!-- <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">交易流水号</span>
            <el-input v-model="searchParam.trxNo" placeholder="与创建时间搭配使用"/>
          </div>

          <div class="flex-item">
            <span class="flex-item__label">用工企业编号</span>
            <el-input v-model="searchParam.employerMchNo" placeholder="与创建时间搭配使用"/>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">代征主体编号</span>
            <el-input v-model="searchParam.mainstayMchNo" placeholder="与创建时间搭配使用"/>
          </div>

        </div> -->

        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              ref="datepicker"
              v-model="createTimeRange"
              type="datetimerange"
              @change="getTimeRange"/>
          </div>
        </div>

        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-switch @change="onHistoryChange" v-model="inHistory" active-text="查询历史表" inactive-text="查询实时表"/>
            <el-button class="el-button--primary" @click="refreshList(true)"><i class="el-icon-search"/>查询</el-button>
            <el-button type="text" @click="resetField">清空筛选条件</el-button>
          </div>
        </div>

      </div>
    </div>

    <el-main class="content-container">
      <el-table :data="pageResult.data" row-key="id">

        <el-table-column type="index" :index="getIndex" label="序号"/>

        <el-table-column prop="createTime" label="创建时间">
          <p slot-scope="{row, column}" v-html="renderTime(row[column['property']])"></p>
        </el-table-column>

        <el-table-column prop="modifyTime" label="修改时间">
          <p slot-scope="{row, column}" v-html="renderTime(row[column['property']])"></p>
        </el-table-column>

        <el-table-column prop="accountProcessNo" label="账务处理流水号"/>

        <el-table-column label="处理阶段" v-slot="{row}">
          <span v-if="row.processStage===1" style="color: #ff7f11;">待处理</span>
          <span v-else-if="row.processStage===2" style="color:red;">处理中</span>
          <span v-else-if="row.processStage===3" style="color:green;">已处理</span>
        </el-table-column>

        <el-table-column prop="remark" label="备注"/>

        <el-table-column label="操作" v-slot="{row}">
          <el-button type="text" size="small" @click="viewAccountProcessPendingPage(row)">查看详情</el-button>
        </el-table-column>
      </el-table>
    </el-main>

    <el-footer class="main-page-footer">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[15,30,50,100]"
        :total="pageResult.totalRecord"
        :page-size.sync="pageParam.pageSize"
        :current-page.sync="pageParam.pageCurrent"
        @size-change="refreshList"
        @current-change="refreshList"/>
    </el-footer>

    <account-invoice-process-pending-view ref="processPendingView"/>
  </div>
</template>

<script>
  import AccountInvoiceProcessPendingView from "./AccountInvoiceProcessPendingView";
  import {formatMoney} from "@/utils/commonUtil"
  import dayjs from "dayjs"
  import { getPendingAccountInvoiceList, viewProcessPending } from '@/api/accountInvoice';

  export default {
    name: "PmsAccountInvoiceProcessPendingList",
    components: {
      AccountInvoiceProcessPendingView,
    },
    data() {
      return {
        searchParam: {},
        pageParam: {pageCurrent: 1, pageSize: 30},
        inHistory: false,
        createTimeRange: [],
        pageResult: {},
        loading: false,
      };
    },
    methods: {
      formatMoney,
      refreshList(initFlag) {
        if (initFlag === true) {
          this.pageParam.pageCurrent = 1;
        }
        this.loading = true
        const reqData = Object.assign({}, this.searchParam, this.createTimeRange && {createTimeBegin: this.createTimeRange[0], createTimeEnd: this.createTimeRange[1]});
        getPendingAccountInvoiceList(reqData, {
          inHistory: this.inHistory,
          ...this.pageParam
        })
          .then(({data}) => {
            this.pageResult = data;
          }).finally(() => {
          this.loading = false
        });
      },
      viewAccountProcessPendingPage(row) {
        viewProcessPending({
          id: row.id
        }, {
          inHistory: this.inHistory,
        })
          .then(({data}) => {
            this.$refs.processPendingView.processPending = data;
            this.$refs.processPendingView.show = true;
          })
      },
      onHistoryChange(history) {
        if (history) {
          this.createTimeRange = [dayjs(new Date()).add(-1, 'M').format("YYYY-MM-DD HH:mm:ss"), dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")];
        } else {
          this.createTimeRange = [];
        }
        this.pageResult = {}
      },
      getIndex(index) {
        return (this.pageParam.pageCurrent - 1) * this.pageParam.pageSize + index + 1;
      },
      getTimeRange(data) {
        this.createTimeRange = data;
      },
      resetField() {
        this.searchParam = {};
        this.$refs.datepicker.clearTime();
      }
    },
    mounted() {
      this.refreshList();
    }
  };
</script>

