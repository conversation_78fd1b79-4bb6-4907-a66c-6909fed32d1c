<template>
  <div class="box-container" v-loading="loading">
    <div class="search-container">
      <div class="flex-container">

        <div class="search-wrapper flex-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">账户编号：</span>
            <el-input v-model="searchParam.accountNo"/>
          </div>

          <div class="flex-item">
            <el-tooltip content="用工企业编号" placement="top">
              <span class="flex-item__label">企业编号：</span>
            </el-tooltip>
            <el-input v-model="searchParam.employerMchNo"/>
          </div>

          <div class="flex-item">
            <el-tooltip placement="top" content="代征主体编号">
              <span class="flex-item__label">代征编号：</span>
            </el-tooltip>
            <el-input v-model="searchParam.mainstayMchNo"/>
          </div>

        </div>

        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">处理流水号：</span>
            <el-input v-model="searchParam.accountProcessNo"/>
          </div>

          <div class="flex-item">
            <span class="flex-item__label">平台流水号：</span>
            <el-input v-model="searchParam.trxNo"/>
          </div>

          <div class="flex-item">
            <span class="flex-item__label">处理类型：</span>
            <el-select clearable v-model="searchParam.processType">
              <el-option v-for="{code,desc} in $dict('AccountProcessTypeEnum')" :key="code" :value="parseInt(code)" :label="desc"/>
            </el-select>
          </div>
        </div>

        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">记账时间：</span>
            <date-picker
              ref="datepicker"
              v-model="createTimeRange"
              type="datetimerange"
              @change="getTimeRange"/>
          </div>
        </div>

        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-switch @change="onHistoryChange" v-model="inHistory" active-text="查询历史表" inactive-text="查询实时表"/>
            <el-button class="el-button--primary" @click="refreshList(true)"><i class="el-icon-search"/>查询</el-button>
            <el-button type="text" @click="resetField">清空筛选条件</el-button>
          </div>
        </div>
      </div>
    </div>

    <el-main class="content-container">
      <el-table :data="pageResult.data" row-key="id">

        <el-table-column type="index" :index="getIndex" label="序号"/>

        <el-table-column width="120">
          <template v-slot:header="{}">记账时间<br/>业务时间</template>
          <template v-slot="{row}">
            <p v-html="renderTime(row.createTime)" ></p>
            <p v-html="renderTime(row.trxTime)" ></p>
          </template>
        </el-table-column>

        <el-table-column>
          <template v-slot:header="{}">账户编号</template>
          <template v-slot="{row}">{{row.accountNo}}</template>
        </el-table-column>

        <el-table-column>
          <template v-slot:header="{}">用工企业编号<br/>代征主体编号</template>
          <template v-slot="{row}">{{row.employerMchNo}}<br/>{{row.mainstayMchNo}}</template>
        </el-table-column>

        <el-table-column>
          <template v-slot:header="{}">平台流水号<br/>商户订单号</template>
          <template v-slot="{row}">{{row.trxNo}}<br/>{{row.mchTrxNo}}</template>
        </el-table-column>

        <el-table-column>
          <template v-slot:header="{}">账务处理流水号<br/>业务类型</template>
          <template v-slot="{row}">{{row.accountProcessNo}}<br/>{{$dictCode('BusinessTypeEnum',row.bizType).desc}}</template>
        </el-table-column>

        <el-table-column>
          <template v-slot:header="{}">交易金额</template>
          <template v-slot="{row}">
            <p class="text-right">
              {{row.alterAmount | moneyFormat }}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="账务处理类型">
          <template v-slot="{row}">
            {{$dictCode('AccountProcessTypeEnum',row.processType).desc}}
          </template>
        </el-table-column>

        <el-table-column>
          <template v-slot:header="{}">发票余额变动金额<br/>发票金额</template>
          <template v-slot="{row}">
            <div class="text-right">
              {{ row.alterInvoiceAmount | moneyFormat}}<br/>
              {{row.invoiceAmount | moneyFormat}}
            </div>
          </template>
        </el-table-column>

        <el-table-column>
          <template v-slot:header="{}">冻结余额变动金额<br/>冻结余额</template>
          <template v-slot="{row}">
            <div class="text-right">
              {{ row.alterFrozenAmount | moneyFormat}}<br/>
              {{row.frozenAmount | moneyFormat}}
            </div>
          </template>
        </el-table-column>


        <el-table-column>
          <template v-slot:header="{}">备注</template>
          <template v-slot="{row}">{{row.remark}}</template>
        </el-table-column>

      </el-table>
    </el-main>

    <el-footer class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10,15,30,50]"
        :total="pageResult.totalRecord"
        :page-size.sync="pageParam.pageSize"
        :current-page.sync="pageParam.pageCurrent"
        @size-change="refreshList"
        @current-change="refreshList"/>
    </el-footer>
  </div>
</template>

<script>
  import {formatMoney} from "@/utils/commonUtil"
  import dayjs from 'dayjs'
  import { getAccountInvoiceDetail } from '@/api/accountInvoice'


  export default {
    name: "PmsAccountInvoiceProcessDetailList",
    data() {
      return {
        searchParam: {},
        pageParam: {pageCurrent: 1, pageSize: 10},
        inHistory: false,
        createTimeRange: [],
        pageResult: {},
        loading: false,
      };
    },
    methods: {
      formatMoney,
      refreshList(initFlag) {
        if (initFlag === true) {
          this.pageParam.pageCurrent = 1;
        }
        this.loading = true
        getAccountInvoiceDetail(
          Object.assign({}, this.searchParam, this.createTimeRange && {createTimeBegin: this.createTimeRange[0], createTimeEnd: this.createTimeRange[1]}),
          {
            inHistory: this.inHistory,
            ...this.pageParam
          }
        )
          .then(({data}) => {
            this.pageResult = data;
          }).finally(() => {
          this.loading = false
        });
      },
      onHistoryChange(history) {
        if (history) {
          this.createTimeRange = [dayjs(new Date()).add(-1, 'M').format("YYYY-MM-DD HH:mm:ss"), dayjs(new Date()).format("YYYY-MM-DD HH:mm:ss")];
        } else {
          this.createTimeRange = [];
        }
        this.pageResult = {}
      },
      getIndex(index) {
        return (this.pageParam.pageCurrent - 1) * this.pageParam.pageSize + index + 1;
      },
      getTimeRange(data) {
        this.createTimeRange = data;
      },
      resetField() {
        this.searchParam = {};
        this.$refs.datepicker.clearTime();
      }
    },
    mounted() {
      this.refreshList();
    }
  };
</script>

