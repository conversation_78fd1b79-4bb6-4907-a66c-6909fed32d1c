<template>
  <div class="box-container" v-loading="loading">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">账户编号：</span>
            <el-input v-model="searchParam.accountNo"/>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">用工企业编号：</span>
            <el-input v-model="searchParam.employerMchNo"/>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">供应商编号：</span>
            <el-select v-model="searchParam.mainstayMchNo" clearable>
              <el-option
                v-for="(item, index) in mainstayList"
                :key="index"
                :label="item.mchName"
                :value="item.mchNo"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">账户状态：</span>
            <el-select clearable v-model="searchParam.status">
              <el-option v-for="{code,desc} in $dict('AccountStatusEnum')" :label="desc" :key="parseInt(code)" :value="parseInt(code)"/>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">用工企业名称：</span>
            <el-input v-model="searchParam.employerMchNameLike" placeholder="模糊查询"></el-input>
          </div>
        </div>
      </div>

      <div class="search-wrapper">
        <div class="search-btn-group">
          <el-button type="primary" @click="refreshList(true)"><i class="el-icon-search"/>查询</el-button>
          <el-button @click="exportAccountInvoiceList">导出</el-button>
          <el-button type="text" @click="getExportList">查看已导出列表</el-button>
          <el-button type="text" @click="resetField">清空筛选条件</el-button>
        </div>
      </div>
    </div>

    <div class="func-container" v-permission="'order:listOrderItemPage:sum'">
      <el-button type="text" @click="() => {showResult = !showResult}">
        统计查询结果
        <i class="el-icon-instance" :class="{'el-icon-arrow-up' : showResult, 'el-icon-arrow-down': !showResult}"></i>
      </el-button>
      <div v-if="showResult">
        <el-tag type="info">
          待开发票金额 {{ totalResult.total_amount }} 元，冻结余额 {{ totalResult.total_frozen_amount }} 元。
        </el-tag>
      </div>
    </div>

    <div class="content-container">
      <el-table :data="pageResult.data" row-key="id" @sort-change="sortChange" ref="contentTable">

        <el-table-column type="index" :index="getIndex" label="序号"/>

        <el-table-column width="150" prop="createTime" label="创建时间">
          <p slot-scope="{row, column}" v-html="renderTime(row[column['property']])"></p>
        </el-table-column>

        <el-table-column prop="accountNo" label="账户编号"/>
        <el-table-column label="用工企业编号/名称">
          <template v-slot="{row}">
            <span>{{ row.employerMchNo }}</span><br>
            <span>{{ row.employerMchName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="供应商编号/名称">
          <template v-slot="{row}">
            <span>{{ row.mainstayMchNo }}</span><br>
            <span>{{ row.mainstayMchName }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" :formatter="row=>$dictCode('AccountStatusEnum',row.status).desc"/>

        <el-table-column label="发票余额" prop="invoiceAmount" sortable="custom">
          <template v-slot="{row}">
            <p class="text-right">
              {{ row.invoiceAmount | moneyFormat}}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="冻结余额" prop="frozenAmount" sortable="custom">
          <template v-slot="{row}">
            <p class="text-right">
              {{ row.frozenAmount | moneyFormat}}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="预开票金额" prop="invoicePreAmount" sortable="custom">
          <template v-slot="{row}">
            <p v-if="row.invoicePreAmount" class="text-right">
              {{ row.invoicePreAmount | moneyFormat}}
            </p>
            <p v-else class="text-right">-</p>
          </template>
        </el-table-column>

        <el-table-column label="操作" prop="frozenAmount">
          <template v-slot="{row}">
            <el-button type="text" @click="adjustAccount(row)">调账</el-button>
          </template>
        </el-table-column>

      </el-table>
    </div>
    <ExportRecord ref="exportRecord"></ExportRecord>
    <el-footer class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[15,30,50,100]"
        :total="pageResult.totalRecord"
        :page-size.sync="pageParam.pageSize"
        :current-page.sync="pageParam.pageCurrent"
        @size-change="refreshList(true)"
        @current-change="refreshList"/>
    </el-footer>
    <adjust-dialog :data="rowData" :visible.sync="dialogVisible" @refresh="refreshList"/>
  </div>
</template>

<script>
  import ExportRecord from '@/components/ExportRecord'
  import AdjustDialog from './Component/AdjustDialog'
  import {formatMoney} from "@/utils/commonUtil"
  import { getMainstayList } from '@/api/merchant';
  import { getAccountInvoiceList, statisticAccountInvoice,exportAccountInvoiceList } from '@/api/accountInvoice';

  export default {
    name: "PmsAccountInvoiceList",
    components: {
      AdjustDialog,
      ExportRecord
    },
    data() {
      return {
        searchParam: {},
        createTimeRange: [],
        pageParam: {pageCurrent: 1, pageSize: 30},
        pageResult: {},
        loading: false,

        dialogVisible: false,
        rowData: {},

        mainstayList: [],

        showResult: false,
        totalResult: {},

      };
    },
    methods: {
      formatMoney,
      async refreshList(initFlag) {
        if (initFlag === true) {
          this.pageParam.pageCurrent = 1;
        }
        this.loading = true
        const { data } = await getAccountInvoiceList({
          ...this.searchParam,
        }, this.pageParam).finally(() => {
          this.loading = false
        });
        this.pageResult = data;

        this.statisticAccountInvoice();
      },
      sortChange(event){
        console.log(event)
        let order = event.order;
        var sqlOrder = null
        if(order == 'ascending'){
          sqlOrder = 'asc'
        }else if (order == 'descending'){
          sqlOrder = 'desc'
        }

        var col=null
        if(event.prop=='invoiceAmount'){
          col = 'INVOICE_AMOUNT'
        }
        if(event.prop=='frozenAmount'){
          col = 'FROZEN_AMOUNT'
        }

        if (sqlOrder!=null){
          this.searchParam.sortColumns=col+' '+ sqlOrder
        }

        this.refreshList()
      },
      getIndex(index) {
        return (this.pageParam.pageCurrent - 1) * this.pageParam.pageSize + index + 1;
      },
      adjustAccount(data) {
        this.rowData = data;
        this.dialogVisible = true
      },
      getTimeRange(data) {
        this.createTimeRange = data;
      },
      resetField() {
        this.searchParam = {};
        this.$refs.contentTable.clearSort()
      },
      async statisticAccountInvoice() {
        const { data } = await statisticAccountInvoice({
          ...this.searchParam
        })
        this.totalResult = data;
      },
      async exportAccountInvoiceList() {
        const { data } = await exportAccountInvoiceList(this.searchParam);
        this.$message.success(data);
      },
      getExportList() {
        this.$refs.exportRecord.isShow = true;
        this.$refs.exportRecord.getExportRecord(37);
      }
    },
    mounted() {
      this.refreshList();
      getMainstayList().then(res => { this.mainstayList = res.data })
    }
  };
</script>
