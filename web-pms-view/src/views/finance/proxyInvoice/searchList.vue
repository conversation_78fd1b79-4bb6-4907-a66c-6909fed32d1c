<template>
  <div class="box-container">
    <div class="search-page">
      <el-form
        class="search-page-form"
        :model="queryForm"
        :rules="rules"
        label-position="top">
        <el-form-item label="请选择商户：">
          <el-select
            v-model="queryForm.mchNo"
            v-width="250"
            clearable
            filterable
            @change="getProd">
            <el-option
              v-for="item in mchList"
              :key="item.mchNo"
              :label="item.mchName"
              :value="item.mchNo"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属产品：">
          <el-select
            v-width="250"
            clearable
            filterable
            v-model="queryForm.productNo">
            <el-option
              v-for="item in prodList"
              :key="item.productNo"
              :label="item.productName"
              :value="item.productNo"></el-option>
          </el-select>
        </el-form-item>
        <el-button
          type="primary"
          :disabled="btnDisabled"
          @click="query">查询未开票订单
        </el-button>
      </el-form>
    </div>
  </div>
</template>

<script>
import { getProductByMch } from '@/api/product'
import { getAllMerchant } from '@/api/merchant'

export default {
  name: "ProxyInvoiceList",
  data() {
    return {
      mchList: [],
      rules: {},
      prodList: [],
      queryForm: {},
    }
  },
  computed: {
    btnDisabled() {
      return !(this.queryForm.mchNo && this.queryForm.productNo)
    }
  },
  mounted() {
    this.getMch()
  },
  methods: {
    async getMch() {
      const { data } = await getAllMerchant({ merchantType: 100 })
      this.mchList = data
    },
    async getProd() {
      const { data } = await getProductByMch({ mchNo: this.queryForm.mchNo })
      this.prodList = data
    },
    query() {
      this.$router.push({
        path: `/finance/proxyInvoice/${ this.queryForm.productNo.toLowerCase() }`,
        query: {
          mchNo: this.queryForm.mchNo
        }
      })
    },
  }
}
</script>

<style
  lang="scss"
  scoped>
.search-page {
  text-align: center;

  &-form {
    display: inline-block;
    margin: 0 auto;
    text-align: left;
  }
}
</style>
