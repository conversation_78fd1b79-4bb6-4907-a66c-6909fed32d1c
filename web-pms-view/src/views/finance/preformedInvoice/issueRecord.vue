<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">用工企业编号：</span>
          <el-input v-model="searchForm.employerMchNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业名称：</span>
          <el-input v-model="searchForm.employerMchName"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">代征主体：</span>
          <el-select
            v-model="searchForm.mainstayMchNo"
            clearable>
            <el-option
              v-for="item in mainstayList"
              :key="item.id"
              :label="item.mchName"
              :value="item.mchNo"></el-option>
          </el-select>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            :start-time.sync="searchForm.createTimeBegin"
            :end-time.sync="searchForm.createTimeEnd"
            type="datetime"
            picker="separate"
            ref="datepicker"
          />
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">完成时间：</span>
          <date-picker
            :start-time.sync="searchForm.completeTimeBegin"
            :end-time.sync="searchForm.completeTimeEnd"
            type="datetime"
            picker="separate"
            ref="datepicker"
          />
        </div>
      </div>
      <div class="search-wrapper">
        <div class="search-btn-group">
          <el-button
            type="primary"
            @click="search(true)"><i class="el-icon-search" />查询
          </el-button>
          <el-button @click="exportInvoiceList">导出</el-button>
          <el-button
            type="text"
            @click="getExportList">查看已导出列表
          </el-button>
          <el-button
            type="text"
            @click="resetField">清空筛选条件
          </el-button>
        </div>
      </div>
    </div>
    <div class="func-container">
      <el-button
        type="primary"
        v-permission="'invoicePre:create'"
        @click="openIssueEditDialog()">预开票申请
      </el-button>
    </div>
    <div class="content-container">
      <el-table :data="list">
        <el-table-column
          label="创建时间/完成时间"
          width="240">
          <template v-slot="{row}">
            {{ row.createTime }} <br> {{ row.completeTime || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="用工企业"
          width="150">
          <template v-slot="{row}">
            {{ row.employerMchNo }} <br> {{ row.employerMchName }}
          </template>
        </el-table-column>
        <el-table-column
          label="开票方"
          prop="mainstayMchName"></el-table-column>
        <el-table-column
          label="所属产品"
          prop="productName"></el-table-column>
        <el-table-column
          label="开票类目"
          prop="invoiceCategoryName" show-overflow-tooltip></el-table-column>
        <el-table-column
          label="预开票金额"
          prop="invoiceAmount"
          width="150">
          <template v-slot="{row}">
            <p>
              {{ row.invoiceAmount | moneyFormat}}
            </p>
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          prop="remark" show-overflow-tooltip></el-table-column>
        <el-table-column
          label="状态"
          width="100"
          prop="invoiceStatus">
          <template v-slot="{row}">
            <el-tag :type="row.invoiceStatus === 1 ? 'info' : 'warning'">{{ row.invoiceStatus === 1 ? '未完成' : '已完成' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button
              type="text"
              v-permission="'invoicePre:update'"
              @click="openIssueEditDialog(row)">编辑
            </el-button>
            <el-button
              v-if="row.invoiceStatus === 1"
              type="text"
              class="red-btn"
              v-permission="'invoicePre:delete'"
              @click="deleteRecord(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-footer class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :page-sizes="[10, 30, 50]"
        :total="totalRecord"
        :page-size.sync="page.pageSize"
        :current-page.sync="page.pageCurrent"
        @size-change="search(true)"
        @current-change="search()" />
    </el-footer>

    <issue-edit-dialog
      ref="issueEditDialog"
      @change="search"
    ></issue-edit-dialog>

    <export-record ref="exportRecord"></export-record>

  </div>
</template>


<script>
import { getMainstayList } from "@/api/merchant";
import { getPreformedInvoiceList, exportPreformedInvoiceList, deletePreformedInvoice } from "@/api/invoice";
import { toPromise } from "@/utils";
import ExportRecord from "@/components/ExportRecord/index.vue";
import IssueEditDialog from "./Component/IssueEditDialog.vue";

export default {
  name: 'PreformedInvoiceRecordList',
  components: { ExportRecord, IssueEditDialog },
  data() {
    return {
      searchForm: {},
      mainstayList: [],
      list: [],
      page: {
        pageCurrent: 1,
        pageSize: 10,
      },
      totalRecord: 0,
    }
  },
  mounted() {
    this.getMainstayList()
    this.search()
  },
  methods: {
    async getMainstayList() {
      const { data } = await getMainstayList()
      this.mainstayList = data
    },
    async search(init) {
      if (init === true) {
        this.page.pageCurrent = 1
      }
      const { data } = await getPreformedInvoiceList({
        ...this.searchForm,
        ...this.page,
      })
      this.list = data.data || []
      this.totalRecord = data.totalRecord || 0
    },
    resetField() {
      this.searchForm = {}
      this.$refs.datepicker.clearTime()
      this.$refs.monthpicker.clearTime()
    },
    async deleteRecord({ id }) {
      const [err, _] = await toPromise(
        this.$confirm('此操作将删除该条数据，是否继续？', '提示', {
          type: 'warning'
        })
      )
      if (err) return
      await deletePreformedInvoice({ id })
      this.search()
    },
    openIssueEditDialog(row) {
      this.$refs.issueEditDialog.open(row)
    },
    async exportInvoiceList() {
      const { data } = await exportPreformedInvoiceList(this.searchForm);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(68);
    },
  }
}
</script>
