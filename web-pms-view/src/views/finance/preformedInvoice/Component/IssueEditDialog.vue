<template>
  <el-dialog
    :visible.sync="visible"
    :title="title"
    :before-close="close">
    <el-form
      :model="form"
      :rules="rules"
      label-width="150px"
      ref="form">
      <el-form-item
        label="预开票商户："
        prop="employerMchNo">
        <el-select
          v-if="!form.id"
          v-model="form.employerMchNo"
          @change="changeMch"
          filterable>
          <el-option
            v-for="item in mchList"
            :key="item.mchNo"
            :label="item.mchName"
            :value="item.mchNo">
          </el-option>
        </el-select>
        <el-input v-else v-model="form.employerMchName" disabled></el-input>
      </el-form-item>
      <el-form-item
        label="所属产品："
        prop="productNo">
        <el-select
          v-if="!form.id"
          v-model="form.productNo"
          :disabled="!form.employerMchNo"
          @change="changeProduct"
          filterable>
          <el-option
            v-for="item in productList"
            :key="item.productNo"
            :label="item.productName"
            :value="item.productNo">
          </el-option>
        </el-select>
        <el-input v-else v-model="form.productName" disabled></el-input>
      </el-form-item>
      <el-form-item
        label="开票方："
        prop="mainstayMchNo">
        <el-select
          v-if="!form.id"
          v-model="form.mainstayMchNo"
          :disabled="!form.employerMchNo || !form.productNo"
          @change="changePosition"
          filterable>
          <el-option
            v-for="item in mainstayList"
            :key="item.mainstayNo"
            :label="item.mainstayName"
            :value="item.mainstayNo">
          </el-option>
        </el-select>
        <el-input v-else v-model="form.mainstayMchName" disabled></el-input>
      </el-form-item>
      <el-form-item
        label="岗位类目"
        prop="workCategoryCode">
        <el-select
          v-if="!form.id"
          v-model="form.workCategoryCode"
          :disabled="!form.employerMchNo"
          @change="changePosition"
          filterable>
          <el-option
            v-for="item in positionList"
            :key="item.workCategoryCode"
            :label="item.workCategoryName"
            :value="item.workCategoryCode">
          </el-option>
        </el-select>
        <el-input v-else v-model="form.workCategoryName" disabled></el-input>
      </el-form-item>
      <el-form-item
        label="开票类目："
        prop="invoiceCategoryCode">
        <el-select
          v-model="form.invoiceCategoryCode"
          :disabled="!form.employerMchNo || !form.mainstayMchNo || !form.workCategoryCode"
          filterable>
          <el-option
            v-for="item in invoiceList.invoiceCategoryVoList"
            :key="item.invoiceCategoryCode"
            :label="item.invoiceCategoryName"
            :value="item.invoiceCategoryCode">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="预开票金额："
        prop="invoiceAmount">
        <el-input-number :disabled="Boolean(form.id)" v-model="form.invoiceAmount" :precision="2" :min="0" style="width: 200px;"></el-input-number>
      </el-form-item>
      <el-form-item label="备注：">
        <el-input
          type="textarea"
          :rows="5"
          v-model="form.remark"></el-input>
      </el-form-item>
      <el-form-item
        label="发票文件："
        prop="invoiceFileUrl">
        <file-upload
          :urls.sync="form.invoiceFileUrl"
          :options="{picture: true}"
          :max="10"
          multi-upload
          accept=".pdf,.rar,.zip"
          name="发票文件">
        </file-upload>
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="confirm">确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import {
  getAllMerchant, // 预开票商户
  getEmployerPosition // 岗位类目
} from "@/api/merchant";
import { getProductByMch } from "@/api/product"; // 所属产品
import FileUpload from "@/components/FileUpload/index.vue";
import { toPromise } from "@/utils";
import { 
  createPreformedInvoice,
  updatePreformedInvoice,
  getMainstayListByProduct, // 开票方
  getInvoiceInfoWithMainstayNo // 开票类目
} from "@/api/invoice";

export default {
  name: 'TaxCertDialog',
  components: { FileUpload },
  data(vm) {
    return {
      visible: false,
      mchList: [], // 预开票商户
      productList: [], // 所属产品
      mainstayList: [], // 开票方
      positionList: [], // 岗位类目
      invoiceList: [], // 开票类目
      rules: {
        employerMchNo: [{ required: true, message: '请选择预开票商户', trigger: 'blur' }],
        productNo: [{ required: true, message: '请选择所属产品', trigger: 'blur' }],
        mainstayMchNo: [{ required: true, message: '请选择开票方', trigger: 'blur' }],
        workCategoryCode: [{ required: true, message: '请选择岗位类目', trigger: 'blur' }],
        invoiceCategoryCode: [{ required: true, message: '请选择开票类目', trigger: 'blur' }],
        invoiceAmount: [{ required: true, message: '请选择开票类目', trigger: 'blur' }],
      },
      form: {
        employerMchNo: "",
        employerMchName: "",
        productNo: "",
        productName: "",
        mainstayMchNo: "",
        mainstayMchName: "",
        workCategoryCode: "",
        workCategoryName: "",
        invoiceCategoryCode: "",
        invoiceCategoryName: "",
        invoiceAmount: "",
        remark: "",
        invoiceFileUrl: []
      },
    }
  },
  computed: {
    title() {
      return this.form.id ? '编辑预开票申请' : '新增预开票申请'
    },
  },
  methods: {
    open(row) {
      this.visible = true
      this.getAllMerchant()
      
      this.$nextTick(this.copyInfo(row))
    },
    copyInfo(data) {
      if (data) {
        let dataReset = JSON.parse(JSON.stringify(data))
        this.form = {
          id: dataReset.id,
          employerMchNo: dataReset.employerMchNo,
          employerMchName: dataReset.employerMchName,
          productNo: dataReset.productNo,
          productName: dataReset.productName,
          mainstayMchNo: dataReset.mainstayMchNo,
          mainstayMchName: dataReset.mainstayMchName,
          workCategoryCode: dataReset.workCategoryCode,
          workCategoryName: dataReset.workCategoryName,
          invoiceCategoryCode: dataReset.invoiceCategoryCode,
          invoiceCategoryName: dataReset.invoiceCategoryName,
          invoiceAmount: dataReset.invoiceAmount,
          remark: dataReset.remark,
          invoiceFileUrl: JSON.parse(dataReset.invoiceFileUrl)
        }
        this.changePosition()
      } else {
        this.form = {
          employerMchNo: "",
          employerMchName: "",
          productNo: "",
          productName: "",
          mainstayMchNo: "",
          mainstayMchName: "",
          workCategoryCode: "",
          workCategoryName: "",
          invoiceCategoryCode: "",
          invoiceCategoryName: "",
          invoiceAmount: "",
          remark: "",
          invoiceFileUrl: []
        }
      }
      console.log("this.form", this.form);
      
    },
    close() {
      this.form = {}
      this.visible = false
    },
    async getAllMerchant () {
      const { data } = await getAllMerchant({ merchantType: 100 })
      this.mchList = data
    },
    changeMch (val) {
      this.form.productNo = ''
      this.form.mainstayMchNo = ''
      this.form.workCategoryCode = ''
      this.form.invoiceCategoryCode = ''

      this.productList = []
      this.mainstayList = []
      this.positionList = []
      this.invoiceList = []

      this.getProductByMch(val)
      this.getEmployerPosition(val)
    },
    async getProductByMch (mchNo) {
      const { data } = await getProductByMch({mchNo: mchNo ?? ""})
      this.productList = data
    },
    changeProduct (val) {
      this.form.mainstayMchNo = ''
      this.form.invoiceCategoryCode = ''

      this.mainstayList = []
      this.invoiceList = []

      this.getMainstayListByProduct({
        status: 100,
        productNo: val,
        employerNo: this.form.employerMchNo,
      })
    },
    async getMainstayListByProduct (params) {
      const { data } = await getMainstayListByProduct(params)
      this.mainstayList = data
    },
    async getEmployerPosition (mchNo) {
      const { data } = await getEmployerPosition({mchNo: mchNo ?? ""})
      this.positionList = data
    },
    changePosition () {
      console.log("this.form", this.form.workCategoryCode, this.form);
      
      if (!this.form.employerMchNo || !this.form.mainstayMchNo || !this.form.workCategoryCode) return
      this.getInvoiceInfoWithMainstayNo({
        mainstayNo: this.form.mainstayMchNo,
        mchNo: this.form.employerMchNo,
        workCategoryCode: this.form.workCategoryCode
      })
    },
    async getInvoiceInfoWithMainstayNo (params) {
      const { data } = await getInvoiceInfoWithMainstayNo(params)
      this.invoiceList = data
    },
    async confirm() {
      const [err] = await toPromise(this.$refs.form.validate())
      if (err) return
      if (!this.form.id) {
        this.form.employerMchName = this.mchList.find(item => item.mchNo === this.form.employerMchNo).mchName
        this.form.productName = this.productList.find(item => item.productNo === this.form.productNo).productName
        this.form.mainstayMchName = this.mainstayList.find(item => item.mainstayNo === this.form.mainstayMchNo).mainstayName
        this.form.workCategoryName = this.positionList.find(item => item.workCategoryCode === this.form.workCategoryCode).workCategoryName
      }
      this.form.invoiceCategoryName = this.invoiceList.invoiceCategoryVoList.find(item => item.invoiceCategoryCode === this.form.invoiceCategoryCode).invoiceCategoryName
      const api = this.form.id ? updatePreformedInvoice : createPreformedInvoice
      await api(this.form)
      this.$message.success('操作成功')
      this.close()
      this.$emit('change')
    }

  }
}
</script>
