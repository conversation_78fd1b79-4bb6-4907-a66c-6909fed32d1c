<template>
  <div class="box-container">
    <el-menu class="menu-tab-container" :default-active="defaultActive" mode="horizontal" @select="handleTypeChange">
      <el-menu-item
        v-for="item in $dict('AgentTypeEnum')"
        :key="item.code"
        :index="item.code"
      >
        {{ item.desc }}
      </el-menu-item>
    </el-menu>

    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">合伙人编号：</span>
          <el-input v-model="searchForm.agentNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">合伙人名称：</span>
          <el-input v-model="searchForm.agentName" placeholder="模糊查询"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">所属销售：</span>
          <el-select
            clearable
            v-model="searchForm.salerId"
          >
            <el-option
              v-for="item in staffList"
              :key="item.id"
              :value="item.id"
              :label="item.realName"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">账单编号：</span>
          <el-input v-model="searchForm.billNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">结算状态：</span>
          <el-select v-model="searchForm.settlementStatus" clearable>
            <el-option
              v-for="item in $dict('AgentMonthBillTypeEnum')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">账单月份：</span>
          <el-date-picker
            ref="datepicker"
            type="month"
            v-model="searchForm.billDate"
            value-format="yyyy-MM"
            :picker-options="pickerOption"
          ></el-date-picker>
        </div>
      </div>

      <div class="search-wrapper search-btn-group">
        <el-button type="primary" @click="search(true)">查询</el-button>
        <el-button @click="exportList">导出</el-button>
        <el-button type="text" @click="getExportList">查看已导出列表</el-button>
        <el-button type="text" @click="resetForm">清空筛选条件</el-button>
      </div>
    </div>

    <div class="func-container">
      <el-checkbox v-model="checkAll" @change="changeAllChange">单页全选</el-checkbox> |
      <span>已选 {{selection.length}} 条 | </span>
      <span>批量操作</span>
      <el-button
        size="small"
        @click="personSettle"
        :disabled="selection.length == 0"
      >标记为人工结算</el-button>
    </div>

    <div class="content-container">
      <el-table
        ref="table"
        :data="list"
        @selection-change="handleSelection"
        :key="'table-' + searchForm.agentType"
      >
        <el-table-column type="selection" fixed="left" :selectable="checkSelect"></el-table-column>
        <el-table-column label="账单编号" prop="billNo" width="180"></el-table-column>

        <el-table-column label="合伙人编号/名称" width="200">
          <template v-slot="{row}">
            {{ row.agentNo }} <br>
            {{ row.agentName }}
          </template>
        </el-table-column>
        <el-table-column label="身份证号" prop="idCardNo"></el-table-column>
        <el-table-column label="银行卡号" prop="bankCardNo"></el-table-column>
        <el-table-column label="手机号" prop="mobile"></el-table-column>
        <el-table-column label="账单月份" prop="billDate"></el-table-column>

        <el-table-column width="120">
          <template v-slot:header>
            收益（元）<el-tooltip content="收益包含个税" placement="top" v-if="searchForm.agentType == 100"><i class="el-icon-warning"></i></el-tooltip>
          </template>
          <template v-slot="{row}">
            <p class="text-right">
              {{ row.agentMonthProfit | moneyFormat }}
            </p>
          </template>
        </el-table-column>

        <el-table-column width="100" label="结算代扣规则" prop="selfDeclared">
          <template v-slot="{row}">
            {{ $dictCode('SelfDeclaredEnum', row.selfDeclared).desc }}
          </template>
        </el-table-column>

        <el-table-column width="220" label="个税（元）/代扣税比例（%）" v-if="searchForm.agentType == '100'">
          <template v-slot="{row}">
            <div class="text-right" v-if="row.selfDeclared == 1">
              {{ row.tax | moneyFormat }}
            </div>
            <div class="text-right color-gray" v-if="row.selfDeclared == 1">
              {{ row.taxPercent }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="结算金额（元）" width="180">
          <template v-slot="{row}">
            <p class="text-right">
              {{ row.settlementAmount | moneyFormat }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="所属销售" prop="salerName"></el-table-column>
        <el-table-column label="备注" prop="note" width="150" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column label="结算状态" width="150">
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.settlementStatus)">
              {{ $dictCode('AgentMonthBillTypeEnum', row.settlementStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right">
          <template v-slot="{row}">
            <el-button type="text" @click="goDetail(row)">查看明细</el-button>
            <el-button type="text" @click="edit(row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        v-if="list"
        ref="pagination"
        :total="totalRecord"
        :page-sizes="[10,50]"
        :current-page.sync="pageCurrent"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      />
    </el-footer>

    <PersonSettle :visible.sync="visible" @confirm="confirmHandle" @close="closeHandle" :operate-row="operateRow"></PersonSettle>
    <ExportRecord ref="exportRecord"></ExportRecord>
  </div>
</template>

<script>
  import dayjs from 'dayjs'
  import { getStaffListByDepartment } from '@/api/system'

  import ExportRecord from '@/components/ExportRecord'
  import PersonSettle from './Component/PersonSettle'
  import { getMonthBill, updateMonthBill, exportBillList } from '@/api/agent'

  const nowMonth = dayjs().month()
  const nowYear = dayjs().year()

  export default {
    name: 'PmsAgentSettle',
    components: {
      ExportRecord,
      PersonSettle
    },
    data() {
      return {
        defaultActive: '100',
        searchForm: {
          agentType: 100,
          agentNo: '',
          agentName: '',
          salerId: '',
          billNo: '',
          settlementStatus: '',
          billDate: '',
        },

        pageCurrent: 1,
        pageSize: 10,
        totalRecord: 0,

        list: [],

        staffList: [],

        checkAll: false,
        selection: [],
        operateRow: {},

        pickerOption: {
          disabledDate(time) {
            let date = new Date(time);
            if (date.getFullYear() > nowYear) {
              return true
            } else if (date.getFullYear() < nowYear) {
              return false
            } else {
              return date.getMonth() >= nowMonth
            }
          }
        },

        visible: false,
      }
    },
    mounted() {
      getStaffListByDepartment({departmentId: 0}).then(
        (response) => {
          this.staffList = response.data;
        }
      );
      this.search()
    },
    methods: {
      async search(init) {
        if (init) {
          this.pageCurrent = 1
        }
        const { data } = await getMonthBill({
          ...this.searchForm,
          pageCurrent: this.pageCurrent,
          pageSize: this.pageSize,
        })

        this.list = data.data;
        this.totalRecord = data.totalRecord;

        this.checkAll = false;
      },
      async exportList() {
        await exportBillList(this.searchForm)
        this.$message.success('导出成功')
      },
      getExportList() {
        this.$refs.exportRecord.isShow = true;
        this.$refs.exportRecord.getExportRecord('17');
      },
      resetForm() {
        this.searchForm = {
          agentType: this.searchForm.agentType,
          agentNo: '',
          agentName: '',
          salerId: '',
          billNo: '',
          settlementStatus: '',
          date: '',
        }
      },
      changeAllChange(check) {
        if (check) {
          this.list.forEach(row => {
            if (row.settlementStatus == 101) {
              this.$refs.table.toggleRowSelection(row, true)
            }
          })
        } else {
          this.$refs.table.clearSelection()
        }
      },
      personSettle() {
        this.visible = true;
      },
      async confirmHandle({ status, note,settlementAmount }) {
        await this.updateMonthBill(status, note, settlementAmount);
      },
      closeHandle() {
        this.operateRow = {};
      },
      async updateMonthBill(status, note,settlementAmount) {
        let idList = this.operateRow.id ? [this.operateRow.id] : this.selection.map(item => item.id);
        const { data } = await updateMonthBill({
          idList,
          settlementStatus: status,
          settlementAmount: settlementAmount,
          note,
        });
        data && this.$message.success(data);
        this.search()
      },
      handleSelection(val) {
        this.selection = val;
      },
      handleTypeChange(index) {
        this.searchForm.agentType = Number(index);
        this.resetForm();
        this.search(true);
      },
      getTagType(status) {
        switch (Number(status)) {
          case 100:
          case 104:
            return 'success';
          case 103:
            return 'danger';
          case 102:
            return 'warning';
          case 105:
            return 'info';
          default:
            return 'info'
        }
      },
      checkSelect(row) {
        return row.settlementStatus == 101
      },
      edit(row) {
        this.operateRow = row;
        this.operateRow.atRow = true
        this.visible = true;
      },
      goDetail(row) {
        this.$router.push({
          path: '/trade/fee/agentFeeOrder',
          query: {
            date: row.billDate,
            agentNo: row.agentNo,
            agentName: row.agentName,
          }
        })
      }
    },
  }
</script>

<style scoped lang="scss">

</style>
