<template>
  <el-dialog
    :visible="visible"
    :before-close="close"
    title="请选择状态"
    width="500px"
  >

    <el-form label-width="100px" :model="form" :rules="rules" ref="form">
      <el-form-item label="选择状态" prop="status">
        <el-select v-model="form.status">
          <el-option
            v-for="item in $dict('AgentMonthBillTypeEnum')"
            :key="item.code"
            :value="Number(item.code)"
            :label="item.desc"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="this.operateRow.atRow" label="结算金额">
        <el-input
          type="text" style="width: 60%"
          v-model="form.settlementAmount"
          maxlength="10"
        ></el-input>
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          type="textarea"
          rows="5"
          v-model="form.note"
          maxlength="50"
        ></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer">
      <el-button
        type="primary"
        @click="confirm"
      >确认</el-button>
      <el-button @click="close">取消</el-button>
    </div>

  </el-dialog>
</template>

<script>
  export default {
    name: 'PersonSettleDialog',
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      operateRow: {
        type: Object,
        default: () => ({}),
      }
    },

    data() {
      return {
        form: {
          status: '',
          note: '',
          settlementAmount:''
        },
        rules: {
          status: [
            { required: true, trigger: 'blur', message: '请选择状态' }
          ]
        }
      }
    },

    watch: {
      visible(val) {
        if (val) {
          this.form.note = this.operateRow.note || '';
          this.form.status = this.operateRow.settlementStatus;
          this.form.settlementAmount = this.operateRow.settlementAmount;
        }
      }
    },

    methods: {
      async confirm() {
        const valid = await this.$refs.form.validate().catch(_ => false)
        if (!valid) return;
        this.$emit('confirm', this.form)
        this.close();
      },
      close() {
        this.$emit('update:visible', false,)
        this.$emit('close')
      }
    },

  }
</script>

<style scoped lang="scss">

</style>
