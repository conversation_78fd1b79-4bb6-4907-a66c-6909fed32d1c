<template>
  <div class="box-container">
    <el-button class="create-btn" type="primary" @click="$router.push('/sale/saleCostForm')">新建</el-button>
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">产品名称：</span>
            <el-input v-model="form.productNameLike"  placeholder="模糊查询"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">所属部门：</span>
            <el-select clearable v-model="form.departmentId">
              <el-option v-for="item in departmentList" :key="item.id" :value="item.id" :label="item.departmentName"></el-option>
            </el-select>
          </div>
          <div class="flex-item"></div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="getSaleCostList(true)">查询</el-button>
            <el-button type="text" @click="resetForm">清空筛选条件</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table class="content-main" :data="saleCostObj.data">
        <el-table-column label="序号" type="index" :index="getIndex"></el-table-column>
        <el-table-column label="所属部门" prop="departmentName">
        </el-table-column>
        <el-table-column label="产品名称" prop="productName">

        </el-table-column>
        <el-table-column label="规则类型" prop="ruleType">
          <template slot-scope="scope">
            {{ $dictCode('RuleTypeEnum', scope.row.ruleType).desc }}
          </template>
        </el-table-column>

        <el-table-column label="规则参数" prop="ruleParam">
          <template v-slot="{row}">
            <div v-html="explainRuleParam(row.ruleParam)"></div>
          </template>
        </el-table-column>

        <el-table-column label="公式类型" prop="methodType">
          <template slot-scope="scope">
            {{ $dictCode('FormulaEnum', scope.row.formulaType).desc }}
          </template>
        </el-table-column>
        <el-table-column label="固定金额手续费" prop="fixedFee">

        </el-table-column>
        <el-table-column label="比例手续费" prop="feeRate">
        </el-table-column>
        <el-table-column label="优先级" prop="priority">
        </el-table-column>

        <el-table-column label="操作" prop="data">
          <template slot-scope="scope">
            <el-button type="text" @click="handleView(scope.row)">查看</el-button>
            <el-button type="text" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="text" @click="handleConfirm('删除', scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="saleCostObj"
          ref="pagination"
          :total="saleCostObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
  import { deleteSaleCostRule, getSaleCostRuleList } from '../../../api/sale'
  import { getDepartmentList } from '../../../api/system'

  export default {
    name: 'PmsSaleCostManagement',
    data() {
      return {
        form: {
          productNameLike: '',
          departmentId: '',
        },
        departmentList: [],
        pageCurrent: 1,
        pageSize: 10,
        saleCostObj: {}
      }
    },
    mounted() {
      this.getDepartmentList();
      this.getSaleCostList();
    },
    methods: {
      getDepartmentList() {
        getDepartmentList().then(response => {
          this.departmentList = response.data;
        })
      },

      getSaleCostList(initFlag) {
        if (initFlag === true) {
          this.pageCurrent = 1;
        }
        getSaleCostRuleList({
          ...this.form,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        }).then(response => {
          this.saleCostObj = response.data;
        })
      },
      resetForm() {
        this.form = {
          productName: '',
          departmentId: '',
        }
      },
      handleCurrentChange(val) {
        this.pageCurrent = val;
        this.getSaleCostList();
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getSaleCostList(true);
      },
      handleEdit(data) {
        sessionStorage.setItem('saleCost', JSON.stringify(data));
        this.$router.push({
          path: '/sale/saleCostForm',
          query: {
            actionType: 'EDIT'
          }
        });
      },
      handleView(data) {
        sessionStorage.setItem('saleCost', JSON.stringify(data));
        this.$router.push({
          path: '/sale/saleCostForm',
          query: {
            actionType: 'VIEW'
          }
        });

      },
      handleConfirm(text, data) {
        this.$confirm(`确定要${text}该条数据吗`, {
          type: 'warning'
        }).then(res => {
          deleteSaleCostRule({
            id: data.id
          }).then(resposne => {
            this.$message.success('删除成功');
            this.getSaleCostList();
          })
        }).catch(() => {})
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
      explainRuleParam(param) {
        param = JSON.parse(param);
        let result = ''
        param.forEach(item => {
          result += this.$dictCode('SalesSpecialRuleTypeEnum', item.specialRuleType).desc + ' ' + this.$dictCode('CompareTypeEnum', item.compareType).desc + ' ' +  item.value + '；<br/>'
        })
        return result
      }
    }
  }
</script>

<style scoped>

</style>
