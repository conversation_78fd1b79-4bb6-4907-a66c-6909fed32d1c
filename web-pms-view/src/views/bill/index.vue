<template>
  <div>
    <div class="box-container">
      <div class="search-container">
        <div class="flex-container">
          <div class="flex-wrapper search-wrapper">
            <div class="flex-item">
              <span class="flex-item__label">账单编号：</span>
              <el-input
                v-model="searchForm.feeBatchNo"
                placeholder="请输入账单编号"
                clearable
              ></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">服务商：</span>
              <el-input
                v-model="searchForm.mainstayName"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">用工企业：</span>
              <el-input
                v-model="searchForm.employerName"
                placeholder="模糊查询"
                clearable
              ></el-input>
            </div>
          </div>
          <div class="flex-wrapper search-wrapper">
            <div class="flex-item">
              <span class="flex-item__label">账单状态：</span>
              <el-select
                v-model="searchForm.status"
                filterable
                clearable>
                <el-option
                  v-for="(item, index) in $dict('FeeOrderStatus')"
                  :key="index"
                  :label="item.desc"
                  :value="item.code"
                ></el-option>
              </el-select>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">付款模式：</span>
              <el-select
                v-model="searchForm.balancedMode"
                filterable
                clearable>
                <el-option
                  v-for="(item, index) in $dict('BalancedEnum')"
                  :key="index"
                  :label="item.desc"
                  :value="item.code"
                ></el-option>
              </el-select>
            </div>
          </div>
          <div class="search-wrapper flex-wrapper">
            <div class="flex-item-main">
              <span class="flex-item__label">账单日期：</span>
              <date-picker
                ref="datepicker"
                type="date"
                picker="separate"
                :start-time.sync="searchForm.startDate"
                :end-time.sync="searchForm.endDate"
              ></date-picker>
            </div>
          </div>
          <div class="flex-wrapper search-wrapper">
            <div class="search-btn-group">
              <el-button
                type="primary"
                size="small"
                @click="handleQuery"
              >查询
              </el-button>
              <el-button
                type="primary"
                size="small"
                @click="clearField"
              >重置
              </el-button
              >
            </div>
          </div>
        </div>
      </div>

      <el-tabs
        v-model="searchForm.feeSource"
        @tab-click="handleQuery">
        <el-tab-pane
          v-for="item in $dict('FeeSourceEnum')"
          :key="item.code"
          :label="item.desc"
          :name="item.code"></el-tab-pane>
      </el-tabs>

      <div class="content-container">
        <div>
          共生成{{ statistics.total }}条账单，已付款
          {{ statistics.successCount }} 条，未付款
          {{ statistics.unPayCount }} 条
        </div>
        <div class="statistics-container">
          <el-card class="box-card">
            <div>已发放金额合计</div>
            <div class="statistics-money">
              {{ "￥" + $format.toCurrency(statistics.orderNetAmount) }}
            </div>
          </el-card>
          <el-card class="box-card">
            <div>服务费金额合计</div>
            <div class="statistics-money">
              {{ "￥" + $format.toCurrency(statistics.feeAmount) }}
            </div>
          </el-card>
          <el-card class="box-card">
            <div>个税代缴金额合计</div>
            <div class="statistics-money">
              {{ "￥" + $format.toCurrency(statistics.taxAmount) }}
            </div>
          </el-card>
          <el-card class="box-card">
            <div>未付款金额合计</div>
            <div class="statistics-money">
              {{ "￥" + $format.toCurrency(statistics.unPayAmount) }}
            </div>
          </el-card>
        </div>
        <el-table :data="dataList">
          <el-table-column
            label="账单编号"
            prop="feeBatchNo"
            width="180"
          ></el-table-column>
          <el-table-column
            label="账单日期"
            prop="createDate"
            width="100"
          ></el-table-column>
          <el-table-column
            label="用工企业"
            prop="employerName"
            width="150"
          ></el-table-column>
          <el-table-column
            label="服务商"
            prop="mainstayName"
            width="150"
          ></el-table-column>
          <el-table-column
            label="发放批次数"
            prop="orderBatchCount"
            width="100"
          ></el-table-column>
          <el-table-column
            label="订单明细数"
            prop="orderItemCount"
            width="100"
          ></el-table-column>
          <el-table-column
            label="发放成功订单数"
            prop="successCount"
            width="120"
          ></el-table-column>
          <el-table-column
            label="发放失败订单数"
            prop="failCount"
            width="120"
          ></el-table-column>
          <el-table-column
            label="已发放金额合计"
            prop="orderNetAmount"
            width="120"
          ></el-table-column>
          <el-table-column
            label="个税合计"
            prop="taxAmount"
            width="100"
          ></el-table-column>
          <el-table-column
            label="服务费合计"
            prop="feeAmount"
            width="100"
          ></el-table-column>
          <el-table-column
            label="付款模式"
            prop="balancedMode">
            <template v-slot="{ row }">
              {{ $dictCode("BalancedEnum", row.balancedMode).desc || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="账单状态"
            prop="status">
            <template v-slot="{ row }">
              {{ $dictCode("FeeOrderStatus", row.status).desc || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="receiptUrl"
            width="150">
            <template slot="header">
              <div class="table-header">
                付款凭证
                <el-tooltip
                  content="线上结算账单在付款完成15-20分钟后生成回单"
                  placement="top"
                >
                  <i class="el-icon-info table-icon"></i>
                </el-tooltip>
              </div>
            </template>
            <template v-slot="{ row }">
              <el-button
                v-if="
                  row.status == 400 &&
                  !(row.balancedMode == 100 && row.receiptUrl == null)
                "
                type="text"
                @click="download(row)"
              >下载回单
              </el-button
              >
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column
            label="付款完成时间"
            prop="completeTime"
            width="180"
          ></el-table-column>
        </el-table>
        <el-footer class="pagination-container">
          <el-pagination
            v-if="dataList.length > 0"
            :total="totalRecord"
            :current-page="pageCurrent"
            :page-size="pageSize"
            :page-sizes="[10, 50]"
            layout="total, sizes, prev, pager, next, slot, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          >
            <!-- <span @click="forceSearch" class="force-next-btn">下一页</span> -->
          </el-pagination>
        </el-footer>
      </div>
    </div>
  </div>
</template>

<script>
import { listPage, getStatistics } from "@/api/bill";
import { getToken } from "@/utils/loginToken";

export default {
  data(vm) {
    return {
      searchForm: {
        feeSource: '100'
      },
      totalRecord: 0,
      pageSize: 10,
      pageCurrent: 1,
      dataList: [],
      statistics: {
        total: 0,
        successCount: 0,
        unPayCount: 0,
        feeAmount: 0,
        taxAmount: 0,
        orderNetAmount: 0,
        unPayAmount: 0,
      },
      formLabelWidth: "100px",

      confirmShow: false,
      feeItem: {},
      isLoad: false,
      offLoad: false,
      servicePaying: false,
      taxPaying: false,
      offLineShow: false,
      ruleForm: {},
      rules: {
        serviceUrls: [
          { required: true, message: "请上传服务费凭证", trigger: "change" },
        ],
        taxUrls: [
          { required: true, message: "请上传个税凭证", trigger: "change" },
        ],
      },
      serviceUrls: [],
      taxUrls: [],
      offLineItem: {},
      sumLoadding: false,
    };
  },
  created() {
    this.getList();
    this.getData();
  },
  methods: {
    async getData() {
      const { data } = await getStatistics(this.searchForm);
      this.statistics = data;
    },
    forceSearch() {
      this.pageCurrent++;
      this.getList();
    },
    handleQuery() {
      this.pageCurrent = 1;
      this.getList();
      this.getData();
      // console.log(this.searchForm);
    },
    async getList() {
      const { data } = await listPage({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      });
      this.dataList = data.data;
      this.totalRecord = data.totalRecord;
    },
    clearField() {
      this.searchForm = {
        feeSource: '100'
      };
      this.$refs.datepicker.clearTime();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.handleQuery();
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.getList();
    },
    download(row) {
      window.open(this.baseUrl + `/download/feeOrder/certificateFile?feeBatchNo=${ row.feeBatchNo }&x-token=${ getToken() }`);
    },
  },
};
</script>

<style scoped>
.statistics-container {
  display: flex;
}

.box-card {
  flex: 1;
  margin: 1rem;
  justify-content: space-between;
}

.statistics-money {
  font-size: 30px;
}

.comfirm-box {
  display: flex;
  justify-content: space-between;
}

.line ::v-deep .el-divider {
  background: #999;
}

.line ::v-deep .el-divider--vertical {
  width: 1.5px;
  height: 100%;
}

.comfirm-item {
  /* display: flex;
  justify-content: center;
  align-items: center; */
  padding: 30px;
  flex: 1;
}

.comfirm-text {
  margin-top: 2rem;
}

.icon {
  font-size: 100px;
  line-height: 0;
}

.off-line-dialog ::v-deep .el-dialog__header {
  border-bottom: 1px solid #ebebeb;
}

.off-line-dialog ::v-deep .el-dialog {
  margin-bottom: 15vh;
}

.offline-form-item {
  padding: 10px 0;
  display: flex;
  /* align-items: center; */
}

.offline-form {
  padding: 10px;
}

.offline-form-label {
  width: 100px;
  text-align: right;
}

.offline-form-value {
  margin-left: 1rem;
  width: 70%;
}

.offline-box {
  margin-top: 1rem;
  background: #f2f2f2;
}

/* .offline-batch-box{
  margin-top: 1rem;
} */
.form-box {
  display: flex;
}

.form-context {
  flex: 1;
}

/* .form-price{} */
</style>
