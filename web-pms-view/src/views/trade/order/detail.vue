<template>
  <div class="box-container">
    <warning-block>统计月度数据请以「完成时间」为准，并可适当延长创建时间以查询完整数据</warning-block>
    <div class="search-container">
      <div class="flex-container">
        <height-wrapper :collapsed="true">
          <div class="flex-wrapper search-wrapper">
            <div class="flex-item">
              <span class="flex-item__label">订单状态：</span>
              <el-select
                v-model="searchForm.orderItemStatusList"
                clearable
                multiple
                collapse-tags
              >
                <template v-if="!isOuter">
                  <el-option
                    v-for="(item, index) in $dict('OrderItemStatusEnum')"
                    :key="index"
                    :label="searchForm.orderItemStatusList.length == 1 ? item.desc : item.desc.slice(0, 6)"
                    :value="item.code"
                  >
                    <span>{{ item.desc }}</span>
                  </el-option>
                </template>
                <template v-else>
                  <el-option
                    v-for="(item, index) in $dict('OfflineOrderItemStatusEnum')"
                    :key="index"
                    :label="searchForm.orderItemStatusList.length == 1 ? item.desc : item.desc.slice(0, 6)"
                    :value="item.code"
                  >
                    <span>{{ item.desc }}</span>
                  </el-option>
                </template>
              </el-select>
            </div>
            <div class="flex-item">
              <span class="flex-item__label ">代征主体：</span>
              <el-select
                clearable
                v-model="searchForm.mainstayNo"
              >
                <el-option
                  v-for="(item, index) in mainstayList"
                  :key="index"
                  :label="item.mchName"
                  :value="item.mchNo"
                >
                </el-option>
              </el-select>
            </div>
            <div
              class="flex-item"
              v-if="!isOuter">
              <span class="flex-item__label">发放方式：</span>
              <el-select
                clearable
                v-model="searchForm.channelType"
              >
                <el-option
                  v-for="(item, index) in $dict('ChannelTypeEnum')"
                  :key="index"
                  :label="item.desc"
                  :value="item.code"
                >
                </el-option>
              </el-select>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">商户名称：</span>
              <el-input
                clearable
                v-model="searchForm.employerNameLike"
                placeholder="模糊搜索商户名称"
              ></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">支付通道：</span>
              <el-select
                v-model="searchForm.payChannelNo"
                clearable>
                <el-option
                  v-for="(item, index) in payChannelList"
                  :key="index"
                  :label="item.payChannelName"
                  :value="item.payChannelNo"></el-option>
              </el-select>
            </div>
          </div>
          <div class="flex-wrapper search-wrapper">
            <div class="flex-item">
              <span class="flex-item__label">手机号：</span>
              <el-input v-model="searchForm.receivePhoneNo"></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">用工企业编号：</span>
              <el-input v-model="searchForm.employerNo"></el-input>
            </div>
            <div
              class="flex-item"
              style="max-width: none;"
            >
              <span class="flex-item__label">用工企业品牌：</span>
              <el-select
                v-model="searchForm._branch"
                clearable
                placeholder="请选择品牌"
                @change="onBranchChange"
              >
                <el-option
                  v-for="(item, index) in branchList"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
              <el-select
                v-model="searchForm.employerList"
                clearable
                multiple
                filterable
                collapse-tags
                placeholder="请选择商户"
              >
                <el-option
                  v-for="(item, index) in selectMerchant"
                  :key="index"
                  :label="item.mchName"
                  :value="item.mchNo"
                ></el-option>
              </el-select>
            </div>
          </div>
          <div class="flex-wrapper search-wrapper">
            <div class="flex-item">
              <span class="flex-item__label">平台流水号：</span>
              <el-input v-model="searchForm.platTrxNo"></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">商户批次号：</span>
              <el-input v-model="searchForm.mchBatchNo"></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">商户订单号：</span>
              <el-input v-model="searchForm.mchOrderNo"></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">金额：</span>
              <el-input v-model="searchForm.orderItemNetAmountMin"></el-input>
              -
              <el-input v-model="searchForm.orderItemNetAmountMax"></el-input>
            </div>
          </div>
          <div class="flex-wrapper search-wrapper">
            <div class="flex-item">
              <span class="flex-item__label">姓名：</span>
              <el-input v-model="searchForm.receiveName"></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">身份证：</span>
              <el-input v-model="searchForm.receiveIdCardNo"></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">收款账号：</span>
              <el-input v-model="searchForm.receiveAccountNo"></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">所属产品：</span>
              <el-select
                clearable
                v-model="searchForm.productNo"
              >
                <el-option
                  v-for="(item, index) in productList"
                  :key="index"
                  :label="item.productName"
                  :value="item.productNo"
                >
                </el-option>
              </el-select>
            </div>
          </div>
          <div class="flex-wrapper search-wrapper">
            <div class="flex-item-main">
              <span class="flex-item__label">创建时间：</span>
              <date-picker
                type="datetimerange"
                ref="startPicker"
                :start-time.sync="searchForm.createBeginDate"
                :end-time.sync="searchForm.createEndDate"
                :fast-time="fastTime"
                :fast-time-arr="['today', 'yesterday', 'sevenDay', 'thirtyDay', 'lastMonth', 'month', 'lastThreeMonth']"
                @change="getTimeRange('create', $event)"
              >
              </date-picker>
            </div>
          </div>
          <div class="flex-wrapper search-wrapper">
            <div class="flex-item-main">
              <span class="flex-item__label">完成时间：</span>
              <date-picker
                type="datetimerange"
                ref="endPicker"
                :start-time.sync="searchForm.completeBeginDate"
                :end-time.sync="searchForm.completeEndDate"
                :fast-time-arr="['today', 'yesterday', 'sevenDay', 'thirtyDay', 'lastMonth', 'month', 'lastThreeMonth']"
                @change="getTimeRange('complete', $event)"
              >
              </date-picker>
            </div>
          </div>
        </height-wrapper>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="search(true)"
            >查询
            </el-button>
            <el-button @click="exportOrderItem">导出</el-button>
            <el-button
              type="text"
              @click="getExportList"
            >查看已导出列表
            </el-button>
            <el-button
              type="text"
              @click="clearField"
            >清空搜索条件
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div
      class="func-container"
    >
      <el-dropdown trigger="click">
        <el-button
          type="primary">
          点击筛选表格列
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-checkbox-group v-model="checkedColumns">
            <el-checkbox
              style="display: block; margin-left: 8px"
              v-for="(item, key) in tableColumns"
              :key="key"
              :label="key">
              {{ item.label }}
            </el-checkbox>
          </el-checkbox-group>
          <div
            style="margin: 5px 5px 0"
            class="text-right">
            <el-button
              size="mini"
              @click="clearCheckedCol">
              清空
            </el-button>
            <el-button
              type="primary"
              size="mini"
              @click="checkAllCol">全选
            </el-button>
          </div>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button
        type="text"
        @click="() => {showResult = !showResult}"
        v-permission="'order:listOrderItemPage:sum'"
      >
        统计查询结果
        <i
          class="el-icon-instance el-icon-arrow-down"
          :class="{'icon-up' : showResult}"
        ></i>
      </el-button>
      <div v-if="showResult">
        <el-tag type="info">
          合计 {{ totalResult.totalNum || 0 }} 笔，实发金额汇总 {{ totalResult.totalNetAmount || 0 }} 元，代征主体服务费 {{ totalResult.totalFee || 0 }} 元，共计 {{ totalResult.totalOrderAmount || 0 }} 元。
        </el-tag>
      </div>
    </div>
    <div class="table-column-filter">

    </div>
    <div class="content-container">
      <el-table
        fit
        :data="response.data"
        @row-dblclick="goRecord">
        <template
          v-for="(col, key) in tableColumns">
          <filter-table-column
            v-if="checkedColumns.includes(key)"
            :key="key"
            :prop="col.prop"
            :width="col.width"
            :label="col.label"
            :render-fn="col.renderFn"
            :fixed="col.fixed"
            v-bind="col"></filter-table-column>
        </template>
        <!--<el-table-column-->
        <!--  label="商户批次号/平台批次号"-->
        <!--  width="170"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <p>{{ row.mchBatchNo }}<br>{{ row.platBatchNo }}</p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="创建时间/完成时间"-->
        <!--  width="180"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    {{ row.createTime }}<br>-->
        <!--    {{ row.completeTime }}-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="商户订单号/平台流水号"-->
        <!--  width="170"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <p>{{ row.mchOrderNo }}<br>{{ row.platTrxNo }}</p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="所属产品"-->
        <!--  width="100"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    {{ row.productNo }}<br>-->
        <!--    {{ row.productName }}-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="用工企业"-->
        <!--  width="100"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    {{ row.employerNo }}<br>-->
        <!--    {{ row.employerName }}-->
        <!--  </template>-->
        <!--</el-table-column>-->
        <!--<el-table-column-->
        <!--  label="代征主体"-->
        <!--  prop="mainstayName"-->
        <!--&gt;</el-table-column>-->

        <!--<el-table-column-->
        <!--  v-if="!isOuter"-->
        <!--  label="发放方式"-->
        <!--  prop="channelType"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <p>{{ $dictCode('ChannelTypeEnum', row.channelType).desc }}</p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  width="120"-->
        <!--  v-if="!isOuter"-->
        <!--  label="支付通道"-->
        <!--  prop="channelNo"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    {{ row.channelNo }}<br>-->
        <!--    {{ row.channelName }}-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="收款账户"-->
        <!--  prop="receiveAccountNo"-->
        <!--  width="150"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <p>{{ row.receiveAccountNo }}<br />{{ row.bankName }}</p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column width="130">-->
        <!--  <template v-slot:header>-->
        <!--    姓名<br>身份证-->
        <!--  </template>-->
        <!--  <template v-slot="{row}">-->
        <!--    {{ row.receiveName }} <br>-->
        <!--    {{ row.receiveIdCardNo }}-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="手机号"-->
        <!--  prop="receivePhoneNo"-->
        <!--  width="130"-->
        <!--&gt;</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="订单明细（总）金额"-->
        <!--  prop="orderItemAmount"-->
        <!--  width="150"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <p class="text-right">-->
        <!--      {{ row.orderItemAmount | moneyFormat }}-->
        <!--    </p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="任务金额"-->
        <!--  prop="orderItemTaskAmount"-->
        <!--  width="100"-->
        <!--  align="right"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <p class="text-right">-->
        <!--      {{ row.orderItemTaskAmount | moneyFormat }}-->
        <!--    </p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="实发金额"-->
        <!--  prop="orderItemNetAmount"-->
        <!--  width="100"-->
        <!--  align="right"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <p class="text-right">-->
        <!--      {{ row.orderItemNetAmount | moneyFormat }}-->
        <!--    </p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="服务费"-->
        <!--  prop="orderItemFee"-->
        <!--  width="100"-->
        <!--  align="right"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <p class="text-right">-->
        <!--      {{ row.orderItemFee | moneyFormat }}-->
        <!--    </p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="个税"-->
        <!--  prop="orderItemTaxAmount"-->
        <!--  width="100"-->
        <!--  align="right"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <p class="text-right">-->
        <!--      {{ row.orderItemTaxAmount | moneyFormat }}-->
        <!--    </p>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="备注"-->
        <!--  prop="remark"-->
        <!--&gt;</el-table-column>-->
        <!--<el-table-column-->
        <!--  label="失败原因"-->
        <!--  prop="errorDesc"-->
        <!--  width="100"-->
        <!--&gt;</el-table-column>-->

        <!--<el-table-column-->
        <!--  label="商户备忘录"-->
        <!--  prop="memo"-->
        <!--&gt;</el-table-column>-->
        <!--<el-table-column-->
        <!--  label="订单状态"-->
        <!--  prop="orderItemStatus"-->
        <!--  width="120"-->
        <!--  fixed="right"-->
        <!--&gt;-->
        <!--  <template v-slot="{row}">-->
        <!--    <el-tooltip-->
        <!--      :content="$dictCode('OrderItemStatusEnum', row.orderItemStatus).desc"-->
        <!--      :disabled="$dictCode('OrderItemStatusEnum', row.orderItemStatus).desc.length < 5"-->
        <!--    >-->
        <!--      <el-tag :type="getTagType(row.orderItemStatus)">-->
        <!--        {{ $dictCode('OrderItemStatusEnum', row.orderItemStatus).desc.slice(0, 5) }}-->
        <!--      </el-tag>-->
        <!--    </el-tooltip>-->
        <!--  </template>-->
        <!--</el-table-column>-->

        <el-table-column
          v-if="isOuter && checkedColumns.length > 0"
          label="操作"
          width="150">
          <template v-slot="{row}">
            <el-button
              v-if="row.workerBillFilePath"
              type="text"
              @click="downloadBillFile(row)">
              下载支付回单
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="response"
          :total="totalRecord"
          :current-page="pageCurrent"
          :page-size="pageSize"
          :page-sizes="[10, 50]"
          layout="total, sizes, prev, pager, next, slot, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <span
            @click="forceSearch"
            class="force-next-btn"
          >下一页</span>
        </el-pagination>
      </div>
    </div>
    <ExportRecord ref="exportRecord"></ExportRecord>
  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import { getMainstayList, listBranchMerchant } from '@/api/merchant'
import {
  getOrderItem,
  exportOrderItem,
  countOrderItem,
  sumOrderItem,
  offlineOrderItemList,
  offlineSumOrder, offlineCountOrderItem, exportOfflineOrderList
} from '@/api/order'
import { getProductList } from '@/api/product'
import HeightWrapper from '@/components/HeightWrapper'
import dayjs from 'dayjs'
import { getPayChannelList } from "@/api/levy";
import FilterTableColumn from "@/components/FilterTableColumn/index.vue";
import { formatMoney } from "@/filter/filter";


export default {
  name: 'PmsTradeDetail',
  components: {
    FilterTableColumn,
    ExportRecord,
    HeightWrapper,
  },
  data(vm) {
    const tableColumns = [
      {
        label: '商户批次号/平台批次号',
        width: 170,
        prop: 'mchBatchNo',
        renderFn(h, row, col) {
          return h('div', [h('div', row.mchBatchNo), h('div', row.platBatchNo)])
        }
      }, {
        label: '创建时间/完成时间',
        width: 180,
        prop: 'createTime',
        renderFn(h, row, col) {
          return h('div', [h('div', row.createTime), h('div', row.completeTime)])
        }
      }, {
        label: '商户订单号/平台流水号',
        width: 180,
        prop: 'mchOrderNo',
        renderFn(h, row, col) {
          return h('div', [h('div', row.mchOrderNo), h('div', row.platTrxNo)])
        }
      }, {
        label: '所属产品',
        width: 100,
        prop: 'productNo',
        renderFn(h, row, col) {
          return h('div', [h('div', row.productNo), h('div', row.productName)])
        }
      }, {
        label: '用工企业',
        width: 100,
        prop: 'employerNo',
        renderFn(h, row, col) {
          return h('div', [h('div', row.employerNo), h('div', row.employerName)])
        }
      }, {
        label: '代征主体',
        width: 100,
        prop: 'mainstayName',
      }, {
        label: '发放方式',
        prop: 'channelType',
        renderFn(h, row, col) {
          return h('div', vm.$dictCode('ChannelTypeEnum', row.channelType).desc)
        }
      }, {
        label: '支付通道',
        width: 120,
        prop: 'channelNo',
        renderFn(h, row, col) {
          if (!vm.isOuter) {
            return h('div', [h('div', row.channelNo), h('div', row.channelName)])
          } else {
            return '-'
          }
        }
      }, {
        label: '收款账户',
        width: 150,
        prop: 'receiveAccountNo',
        renderFn(h, row, col) {
          return h('div', [h('div', row.receiveAccountNo), h('div', row.bankName)])
        }
      }, {
        label: '姓名/身份证',
        width: 150,
        prop: 'receiveName',
        renderFn(h, row, col) {
          return h('div', [h('div', row.receiveName), h('div', row.receiveIdCardNo)])
        }
      }, {
        label: '手机号',
        width: 150,
        prop: 'receivePhoneNo',
      }, {
        label: '订单明细（总）金额',
        width: 150,
        prop: 'orderItemAmount',
        renderFn(h, row) {
          return h('div', { class: 'text-right' }, [formatMoney(row.orderItemAmount)])
        }
      }, {
        label: '任务金额',
        width: 150,
        prop: 'orderItemTaskAmount',
        align: 'right',
        renderFn(h, row) {
          return h('div', { class: 'text-right' }, [formatMoney(row.orderItemTaskAmount)])
        }
      }, {
        label: '实发金额',
        width: 150,
        prop: 'orderItemNetAmount', align: 'right',
        renderFn(h, row) {
          return h('div', { class: 'text-right' }, [formatMoney(row.orderItemNetAmount)])
        }
      }, {
        label: '服务费',
        width: 150,
        prop: 'orderItemFee', align: 'right',
        renderFn(h, row) {
          return h('div', { class: 'text-right' }, [formatMoney(row.orderItemFee)])
        }
      }, {
        label: '个税',
        width: 150,
        prop: 'orderItemTaxAmount', align: 'right',
        renderFn(h, row) {
          return h('div', { class: 'text-right' }, [formatMoney(row.orderItemTaxAmount)])
        }
      }, {
        label: '备注',
        prop: 'remark',
      }, {
        label: '失败原因',
        prop: 'errorDesc',
      }, {
        label: '商户备忘录',
        prop: 'memo',
      }, {
        label: '订单状态',
        prop: 'orderItemStatus',
        width: 120,
        fixed: 'right',
        renderFn(h, row, col) {
          return h('el-tooltip', {
            props: {
              content: vm.$dictCode('OrderItemStatusEnum', row.orderItemStatus).desc,
              disabled: vm.$dictCode('OrderItemStatusEnum', row.orderItemStatus).desc.length < 5,
            },
          }, [h('el-tag', {
            attrs: { slot: 'default' },
            props: { type: vm.getTagType(row.orderItemStatus) }
          }, [vm.$dictCode('OrderItemStatusEnum', row.orderItemStatus).desc.slice(0, 5)])])
        }
      }
    ]
    return {
      searchForm: { // 搜索表单
        orderItemStatusList: [],
        mainstayNo: '',
        channelType: '',
        mchBatchNo: '',
        mchOrderNo: '',
        orderItemNetAmountMin: '',
        orderItemNetAmountMax: '',
        receiveName: '',
        receiveIdCardNo: '',
        receiveAccountNo: '',
        createBeginDate: '',
        createEndDate: '',
        completeBeginDate: '',
        completeEndDate: '',
        employerNo: '',
        receivePhoneNo: '',
        platTrxNo: '',
        employerNameLike: '',
        productNo: '',
        productName: '',
        _branch: '',
      },
      response: { // 查询结果
        data: [],
      },
      totalRecord: 0,
      pageSize: 10,
      pageCurrent: 1,

      mainstayList: [],
      productList: [],

      showResult: false, // 是否显示统计结果
      totalResult: {},

      fastTime: 'today',

      branchList: [], // 品牌列表
      branchMap: [],

      payChannelList: [],

      tableColumns,
      checkedColumns: []
    }
  },
  computed: {
    selectMerchant() {
      // 提供选择的商户
      let merchant = []
      if (this.branchMap[this.searchForm._branch]) {
        merchant = this.branchMap[this.searchForm._branch].merchant
      }
      return merchant
    },
    isOuter() {
      return this.$route.path === '/trade/outer/detail'
    },

  },
  created() {
    if (this.$route.query) {
      for (let p in this.$route.query) {
        this.searchForm[p] = this.$route.query[p];
      }
      if (this.searchForm.createBeginDate) {
        this.fastTime = '';
      }
      this.listBranchMerchant()
    }
  },
  mounted() {
    this.getProductList();
    this.getMainstayList();
    if (sessionStorage.getItem('createTime')) {
      this.$refs.startPicker.changeTime([sessionStorage.getItem('createTime'), '']);
    }
    if (this.searchForm.receiveIdCardNo) {
      this.$refs.startPicker.changeTime([
        dayjs().startOf('month').startOf('hour').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().endOf('month').endOf('hour').format('YYYY-MM-DD HH:mm:ss')
      ]);
    }
    this.getPayChannelList()
    this.checkAllCol() // 默认全选
    this.$nextTick(() => {
      this.search()
    })
  },
  beforeDestroy() {
    sessionStorage.removeItem('createTime');
    sessionStorage.removeItem('completeTime');
  },
  methods: {
    async search(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      if ((this.searchForm._branch !== '' && this.searchForm._branch !== undefined) && this.searchForm.employerList.length == 0) {
        // 如果选择了品牌，但是没有选择商户，则全选
        this.searchForm.employerList = this.branchMap[this.searchForm._branch].merchant.map(item => item.mchNo)
      }
      const listApi = this.isOuter ? offlineOrderItemList : getOrderItem
      const countApi = this.isOuter ? offlineCountOrderItem : countOrderItem
      const [{ data }, { data: { totalRecord } }] = await Promise.all([
        listApi({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        }),
        countApi(this.searchForm)
      ])
      this.response = data;
      this.totalRecord = totalRecord;

      this.hadPermission('order:listOrderItemPage:sum') && this.checkResult();
    },
    clearField() {
      this.searchForm = {
        orderItemStatusList: [],
        mainstayNo: '',
        channelType: '',
        mchBatchNo: '',
        mchOrderNo: '',
        orderItemNetAmountMin: '',
        orderItemNetAmountMax: '',
        receiveName: '',
        receiveIdCardNo: '',
        receiveAccountNo: '',
        createBeginDate: '',
        createEndDate: '',
        completeBeginDate: '',
        completeEndDate: '',
        employerNo: '',
        receivePhoneNo: '',
        platTrxNo: '',
        employerList: [],
        employerNameLike: '',
        productNo: '',
        productName: '',
      };
      this.$refs.startPicker.clearTime();
      this.$refs.endPicker.clearTime();
    },
    getTimeRange(type, val) {
      this.searchForm[type + 'BeginDate'] = val && val[0];
      this.searchForm[type + 'EndDate'] = val && val[1];
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    async getProductList() {
      const { data } = await getProductList({ productType: 0, pageSize: 100 });
      this.productList = data.data;
    },
    async getMainstayList() {
      const { data } = await getMainstayList();
      this.mainstayList = data;
    },
    async exportOrderItem() {
      const api = this.isOuter ? exportOfflineOrderList : exportOrderItem
      const { data } = await api(this.searchForm);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      if (this.isOuter) {
        this.$refs.exportRecord.getExportRecord(53)
      } else {
        this.$refs.exportRecord.getExportRecord(this.$dictFlag('ReportTypeEnum', 'TRADE_ORDER_ITEM_PMS').code);
      }
    },
    getTagType(status) {
      switch (Number(status)) {
        case 100:
          return 'success';
        case 200:
        case 600:
        case 700:
          return 'danger';
        case 400:
        case 800:
          return 'info';
        default:
          return '';
      }
    },
    forceSearch() {
      this.pageCurrent++;
      this.search();
    },
    async checkResult() {
      const sumApi = this.isOuter ? offlineSumOrder : sumOrderItem
      const { data } = await sumApi(this.searchForm);
      this.$set(this, 'totalResult', data);
    },
    async listBranchMerchant() {
      const { data } = await listBranchMerchant()
      data.forEach(item => {
        this.branchList.push(item.branchName)
      })
      this.branchMap = data
    },
    onBranchChange() {
      this.searchForm.employerList = []
    },
    async downloadBillFile(row) {
      let targetPath = row.workerBillFilePath;
      if (Array.isArray(row.workerBillFilePath)) {
        targetPath = row.workerBillFilePath[0];
      }
      const fileMsg = await this.formatFileMsg(targetPath)
      const suffix = targetPath.split('.').pop()
      this.downloadFile(fileMsg.fileUrl, '支付回单', suffix)
    },
    getPayChannelList() {
      getPayChannelList({
        pageSize: 1000,
        pageCurrent: 1
      }).then(response => {
        this.payChannelList = response.data.data;
      })
    },
    clearCheckedCol() {
      this.checkedColumns = []
    },
    checkAllCol() {
      this.checkedColumns = this.tableColumns.map((_, index) => index)
    },
    goRecord(row,column,event) {
      if (row.createTime) {
        sessionStorage.setItem('recordCreateTime', row.createTime)
      }
      if (row.completeTime) {
        sessionStorage.setItem('recordCompleteTime', row.completeTime)
      }
      const path = this.isOuter ? null : '/trade/order/record'
      if(path == null){
        return
      }
      this.openNewBrowserTab({
        path,
        query: {
          platTrxNo: row.platTrxNo,
        }
      })
    },
  },
}
</script>

<style
  lang="scss"
  scoped>
.box-container {
  .func-container {
    padding-top: 16px;
  }

  .el-icon-instance {
    transition: transform 0.2s linear;
  }

  .icon-up {
    transform: rotate(180deg);
  }
}
</style>
