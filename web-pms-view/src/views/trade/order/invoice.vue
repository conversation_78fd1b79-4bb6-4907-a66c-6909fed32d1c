<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">批次状态：</span>
            <el-radio-group v-model="searchForm.batchStatus">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button
                v-for="(item, index) in $dict('OrderStatusEnum')"
                :key="index"
                :label="item.code">
                {{ item.desc }}
              </el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户批次号：</span>
            <el-input v-model="searchForm.mchBatchNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">商户编号：</span>
            <el-input v-model="searchForm.employerNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">商户名称：</span>
            <el-input v-model="searchForm.employerNameLike"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">发放模式：</span>
            <el-select clearable v-model="searchForm.launchWay">
              <el-option
                v-for="(item, index) in $dict('LaunchWayEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code">
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">代征主体：</span>
            <el-select clearable v-model="searchForm.mainstayNo">
              <el-option
                v-for="(item, index) in mainstayList"
                :key="index"
                :label="item.mchName"
                :value="item.mchNo">
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">发放方式：</span>
            <el-select clearable v-model="searchForm.channelType">
              <el-option
                v-for="(item, index) in $dict('ChannelTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code">
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              type="datetimerange"
              ref="startPicker"
              v-model="createTimeRange"
              fast-time="sevenDay"
              @change="getTimeRange('create', $event)">
            </date-picker>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">完成时间：</span>
            <date-picker
              type="datetimerange"
              ref="endPicker"
              v-model="completeTimeRange"
              fast-time="sevenDay"
              @change="getTimeRange('complete', $event)">
            </date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="flex-item search-btn-group">
            <el-button type="primary" @click="search(true)">查询</el-button>
            <el-button type="text" @click="clearField">清空搜索条件</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table :data="response.data">
        <el-table-column label="序号" type="index" :index="getIndex" width="50"></el-table-column>
        <el-table-column label="创建时间" prop="createTime"></el-table-column>
        <el-table-column label="确认发放时间" prop="confirmTime"></el-table-column>
        <el-table-column label="完成时间" prop="completeTime"></el-table-column>
        <el-table-column label="商户批次号" prop="mchBatchNo"></el-table-column>
        <el-table-column label="平台批次号" prop="platBatchNo"></el-table-column>
        <el-table-column label="商户编号" prop="employerNo"></el-table-column>
        <el-table-column label="商户名称" prop="employerName"></el-table-column>
        <el-table-column label="代征主体" prop="mainstayName"></el-table-column>

        <el-table-column label="发放方式" prop="channelType">
          <template v-slot="{row}">
            {{ $dictCode('ChannelTypeEnum', row.channelTyp).desc }}
          </template>
        </el-table-column>

        <el-table-column label="发放模式" prop="launchWay">
          <template v-slot="{row}">
            {{ $dictCode('LaunchWayEnum', row.launchWay).desc }}
          </template>
        </el-table-column>

        <el-table-column label="已创建总数" prop="requestCount"></el-table-column>
        <el-table-column label="已受理总数" prop="acceptedCount"></el-table-column>

        <el-table-column label="已受理代征主体服务费" prop="acceptedFee">
          <template v-slot="{row}">
            {{ row.acceptedFee | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column label="已受理(总)订单金额" prop="acceptedOrderAmou">
          <template v-slot="{row}">
            {{ row.acceptedOrderAmou | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column label="批次状态" prop="batchStatus">
          <template v-slot="{row}">
            {{ $dictCode('OrderStatusEnum', row.batchStatus).desc }}
          </template>
        </el-table-column>

        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button type="text" @click="goDetail(row)">查看明细</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 50]"
        :total="response.totalRecord"
        @size-change="handleSizeChange"
        @current-change="handleCurrnetChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { getMainstayList } from '@/api/merchant'
import { getOrderList } from '@/api/order'

export default {
  name: 'PmsTradeInvoice',
  data() {
    return {
      searchForm: { // 搜索表单
        batchStatus: '',
        mchBatchNo: '',
        employerNo: '',
        employerNameLike: '',
        launchWay: '',
        mainstayNo: '',
        channelType: '',
        createBeginDate: '',
        createEndDate: '',
        completeBeginDate: '',
        completeEndDate: '',
      },
      response: { // 查询结果
        data: [],
        totalRecord: 0,
      },
      pageSize: 10,
      pageCurrent: 1,

      mainstayList: [],

      createTimeRange: [],
      completeTimeRange: [],

    }
  },
  mounted() {
    this.getMainstayList();
    this.search();
  },
  methods: {
    clearField() {
      this.searchForm = {
        batchStatus: '',
        mchBatchNo: '',
        employerNo: '',
        employerNameLike: '',
        launchWay: '',
        mainstayNo: '',
        channelType: '',
        createBeginDate: '',
        createEndDate: '',
        completeBeginDate: '',
        completeEndDate: '',
      }
      this.$refs.startPicker.resetTime();
      this.$refs.endPicker.resetTime();
    },
    getTimeRange(type, val) {
      this.searchForm[type + 'BeginDate'] = val && val[0];
      this.searchForm[type + 'EndDate'] = val && val[1];
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    handleCurrnetChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    async getMainstayList() {
      const { data } = await getMainstayList();
      this.mainstayList = data;
    },
    async search(initFlag) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      const { data } = await getOrderList({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      });
      this.response = data;
    },
    goDetail(data) {
      this.$router.push({
        path: '/trade/order/detail',
        query: {
          mchBatchNo: data.mchBatchNo,
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  .box-container {
    padding: 20px 20px;
  }
</style>
