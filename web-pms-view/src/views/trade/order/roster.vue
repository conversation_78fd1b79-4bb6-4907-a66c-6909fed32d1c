<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">批次状态：</span>
            <el-radio-group v-model="searchForm.batchStatus">
              <el-radio-button label="">全部</el-radio-button>
              <template v-if="isOuter">
                <el-radio-button
                  v-for="(item, index) in $dict('OfflineOrderStatusEnum')"
                  :key="index"
                  :label="item.code"
                >
                  {{ item.desc }}
                </el-radio-button>
              </template>
              <template v-else>
                <el-radio-button
                  v-for="(item, index) in $dict('OrderStatusEnum')"
                  :key="index"
                  :label="item.code"
                >
                  {{ item.desc }}
                </el-radio-button>
              </template>
            </el-radio-group>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户批次号：</span>
            <el-input v-model="searchForm.mchBatchNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">用工企业编号：</span>
            <el-input v-model="searchForm.employerNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">用工企业名称：</span>
            <el-input v-model="searchForm.employerNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">岗位类别：</span>
            <el-cascader
              filterable
              clearable
              data-index="index"
              v-model="searchForm.workCategoryCode"
              :options="workCategoryOptions"
              :props="{ expandTrigger: 'hover', 'emitPath': false, 'value': 'workCategoryCode', 'label': 'workCategoryName', 'leaf': 'workCategoryCode' }"
            >
              <template v-slot="{data}">
                <el-tooltip
                  :content="data.workCategoryName"
                  :disabled="data.workCategoryName.length < 14"
                >
                  <span class="cascader-content-item">
                    {{ data.workCategoryName }}
                  </span>
                </el-tooltip>
              </template>
            </el-cascader>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">发放模式：</span>
            <el-select
              clearable
              v-model="searchForm.launchWay"
            >
              <el-option
                v-for="(item, index) in $dict('LaunchWayEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">代征主体：</span>
            <el-select
              clearable
              v-model="searchForm.mainstayNo"
            >
              <el-option
                v-for="(item, index) in mainstayList"
                :key="index"
                :label="item.mchName"
                :value="item.mchNo"
              >
              </el-option>
            </el-select>
          </div>
          <div
            class="flex-item"
            v-if="!isOuter">
            <span class="flex-item__label">发放方式：</span>
            <el-select
              clearable
              v-model="searchForm.channelType"
            >
              <el-option
                v-for="(item, index) in $dict('ChannelTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">所属产品：</span>
            <el-select
              clearable
              v-model="searchForm.productNo"
            >
              <el-option
                v-for="(item, index) in productList"
                :key="index"
                :label="item.productName"
                :value="item.productNo"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              type="datetimerange"
              ref="startPicker"
              v-model="createTimeRange"
              fast-time="today"
              @change="getTimeRange('create', $event)"
            >
            </date-picker>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">完成时间：</span>
            <date-picker
              type="datetimerange"
              ref="endPicker"
              v-model="completeTimeRange"
              @change="getTimeRange('complete', $event)"
            >
            </date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="search(true)"
            >查询
            </el-button>
            <el-button
              type="text"
              @click="clearField"
            >清空搜索条件
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table :data="list">
        <el-table-column
          label="序号"
          type="index"
          :index="getIndex"
          width="50"
        ></el-table-column>

        <el-table-column
          label="创建时间"
          prop="createTime"
          width="120"
        >
          <p
            slot-scope="{row, column}"
            v-html="renderTime(row[column['property']])"
          ></p>
        </el-table-column>
        <el-table-column
          label="确认发放时间"
          prop="confirmTime"
          width="120"
        >
          <p
            slot-scope="{row, column}"
            v-html="renderTime(row[column['property']])"
          ></p>
        </el-table-column>
        <el-table-column
          label="完成时间"
          prop="completeTime"
          width="120"
        >
          <p
            slot-scope="{row, column}"
            v-html="renderTime(row[column['property']])"
          ></p>
        </el-table-column>

        <el-table-column
          label="商户批次号/平台批次号"
          prop="mchBatchNo"
          width="180"
        >
          <template v-slot="{row}">
            {{ row.mchBatchNo }}<br>{{ row.platBatchNo }}
          </template>
        </el-table-column>

        <el-table-column
          label="所属产品"
          prop="productNo"
          width="120">
          <template v-slot="{row}">
            {{ row.productNo }}<br>{{ row.productName }}
          </template>
        </el-table-column>

        <el-table-column
          label="商户编号"
          prop="employerNo"
          width="120"
        >
          <template v-slot="{row}">
            {{ row.employerNo }}<br>
            {{ row.employerName }}
          </template>
        </el-table-column>
        <el-table-column
          label="代征主体"
          prop="mainstayName"
        ></el-table-column>
        <el-table-column
          label="岗位类别"
          prop="workCategoryName"
          width="100"></el-table-column>
        <el-table-column
          label="发放方式"
          prop="channelType"
        >
          <template
            v-slot="{row}"
            v-if="!isOuter">
            {{ $dictCode('ChannelTypeEnum', row.channelType).desc }}
          </template>
          <div v-else>线下发放</div>
        </el-table-column>

        <el-table-column
          label="发放模式"
          prop="launchWay"
        >
          <template v-slot="{row}">
            {{ $dictCode('LaunchWayEnum', row.launchWay).desc }}
          </template>
        </el-table-column>

        <el-table-column
          label="已创建总数"
          prop="requestCount"
          width="150"
        >
          <template v-slot="{row}">
            笔数：{{ row.requestCount }}<br />
            金额：{{ row.requestTaskAmount | moneyFormat }}
          </template>
        </el-table-column>
        <el-table-column
          label="已受理总数"
          prop="acceptedCount"
          width="150"
        >
          <template v-slot="{row}">
            笔数：{{ row.acceptedCount }}<br />
            金额：{{ row.acceptedTaskAmount | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column
          label="代征主体服务费"
          prop="acceptedFee"
          width="160"
        >
          <template v-slot="{row}">
            <p class="text-right">
              {{ row.acceptedFee | moneyFormat }}
            </p>
          </template>
        </el-table-column>
        <el-table-column
          label="订单金额"
          prop="acceptedOrderAmou"
          width="150"
        >
          <template v-slot="{row}">
            <p class="text-right">
              {{ row.acceptedOrderAmount | moneyFormat }}
            </p>
          </template>
        </el-table-column>

        <el-table-column
          label="批次状态"
          prop="batchStatus"
          width="120"
          fixed="right"
        >
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.batchStatus)">
              {{ $dictCode('OrderStatusEnum', row.batchStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          fixed="right"
        >
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="goDetail(row)"
            >查看明细
            </el-button>
            <el-button
              v-if="row.batchStatus == 104"
              type="text"
              @click="grantAgain(row)"
              v-permission="'order:grantAgain'"
            >
              重新发放
            </el-button>
            <el-button
              v-if="row.batchStatus == 102"
              type="text"
              @click="acceptAgain(row)"
              v-permission="'order:acceptAgain'"
            >
              重新受理
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, slot, jumper"
        :page-sizes="[10, 50]"
        :total="totalRecord"
        :current-page="pageCurrent"
        @size-change="handleSizeChange"
        @current-change="handleCurrnetChange"
      >
        <span
          @click="forceSearch"
          class="force-next-btn"
        >下一页</span>
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { getMainstayList } from '@/api/merchant'
import { getOrderList, acceptAgain, grantAgain, countOrder, offlineOrderList, acceptOfflineAgain } from '@/api/order'
import { listAllWorkCategory } from '@/api/business'
import { convert } from '@/utils'
import { getProductList } from '@/api/product'

export default {
  name: 'PmsTradeRoster',
  data() {
    return {
      searchForm: { // 搜索表单
        batchStatus: '',
        mchBatchNo: '',
        employerNo: '',
        employerNameLike: '',
        launchWay: '',
        mainstayNo: '',
        channelType: '',
        createBeginDate: '',
        createEndDate: '',
        completeBeginDate: '',
        completeEndDate: '',
        workCategoryCode: '',
      },
      list: [],
      totalRecord: 0,
      pageSize: 10,
      pageCurrent: 1,

      mainstayList: [],
      productList: [],

      createTimeRange: [],
      completeTimeRange: [],

      workCategoryList: [],
      workCategoryOptions: [],
    }
  },
  computed: {
    isOuter() {
      // 外部订单标识
      return this.$route.path === '/trade/outer/roster'
    }
  },
  mounted() {
    this.getMainstayList();
    this.search();
    this.getListAllWorkCategory();
    this.getProductList();
  },
  methods: {
    clearField() {
      this.searchForm = {
        batchStatus: '',
      }
      this.$refs.startPicker.clearTime();
      this.$refs.endPicker.clearTime();
    },
    getTimeRange(type, val) {
      this.searchForm[type + 'BeginDate'] = val && val[0];
      this.searchForm[type + 'EndDate'] = val && val[1];
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    handleCurrnetChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    async getProductList() {
      const { data } = await getProductList({ productType: 0, pageSize: 100 });
      this.productList = data.data;
    },
    async getMainstayList() {
      const { data } = await getMainstayList();
      this.mainstayList = data;
    },
    async search(initFlag) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      if (this.isOuter) {
        const { data: { data, totalRecord } } = await offlineOrderList({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent,
        })
        this.list = data
        this.totalRecord = totalRecord
      } else {
        const [{ data: { data } }, { data: { totalRecord } }] = await Promise.all([
          getOrderList({
            ...this.searchForm,
            pageSize: this.pageSize,
            pageCurrent: this.pageCurrent,
          }),
          countOrder(this.searchForm)
        ])
        this.list = data;
        this.totalRecord = totalRecord;
      }
    },
    goDetail(data) {
      if (data.createTime) {
        sessionStorage.setItem('createTime', data.createTime)
      }
      if (data.completeTime) {
        sessionStorage.setItem('completeTime', data.completeTime)
      }
      const path = this.isOuter ? '/trade/outer/detail' : '/trade/order/detail'
      this.$router.push({
        path,
        query: {
          mchBatchNo: data.mchBatchNo,
        }
      })
    },
    async acceptAgain(row) {
      const api = this.isOuter ? acceptOfflineAgain : acceptAgain
      const { data } = await api({
        platBatchNo: row.platBatchNo
      })
      this.$message.success(data);
      this.search();
    },

    async grantAgain(row) {
      const { data } = await grantAgain({
        platBatchNo: row.platBatchNo
      })
      this.$message.success(data);
      this.search();
    },
    getTagType(status) {
      switch (Number(status)) {
        case 100:
          return 'success';
        case 101:
        case 105:
          return 'danger';
        case 103:
          return 'info';
        default:
          return '';
      }
    },
    forceSearch() {
      this.pageCurrent++;
      this.search();
    },
    // 获取工作类目
    async getListAllWorkCategory() {
      const { data } = await listAllWorkCategory()
      this.workCategoryList = data;
      this.workCategoryOptions = convert(data, 0);
    },
  }
}
</script>

<style
  lang="scss"
  scoped>
.box-container {
  padding: 20px 20px;
}
</style>
