<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">订单状态：</span>
            <el-select
              clearable
              v-model="searchForm.processStatus"
            >
              <el-option
                v-for="(item, index) in $dict('RecordItemStatusEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">代征主体：</span>
            <el-select
              clearable
              v-model="searchForm.mainstayNo"
            >
              <el-option
                v-for="(item, index) in mainstayList"
                :key="index"
                :label="item.mchName"
                :value="item.mchNo"
              >
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">发放方式：</span>
            <el-select
              clearable
              v-model="searchForm.channelType"
            >
              <el-option
                v-for="(item, index) in $dict('ChannelTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">支付通道：</span>
            <el-select
              v-model="searchForm.payChannelNo"
              clearable>
              <el-option
                v-for="(item, index) in payChannelList"
                :key="index"
                :label="item.payChannelName"
                :value="item.payChannelNo"></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">打款流水号：</span>
            <el-input v-model="searchForm.remitPlatTrxNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">商户订单号：</span>
            <el-input v-model="searchForm.mchOrderNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">平台批次号：</span>
            <el-input v-model="searchForm.platBatchNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">用工企业编号：</span>
            <el-input v-model="searchForm.employerNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">姓名：</span>
            <el-input v-model="searchForm.receiveName"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">收款账户：</span>
            <el-input v-model="searchForm.receiveAccountNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">用工企业名称：</span>
            <el-input
              v-model="searchForm.employerNameLike"
              placeholder="模糊查询"
            ></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">平台流水号：</span>
            <el-input v-model="searchForm.platTrxNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">所属产品：</span>
            <el-select
              clearable
              v-model="searchForm.productNo"
            >
              <el-option
                v-for="(item, index) in productList"
                :key="index"
                :label="item.productName"
                :value="item.productNo"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              type="datetimerange"
              ref="startPicker"
              :start-time.sync="searchForm.createBeginDate"
              :end-time.sync="searchForm.createEndDate"
              v-model="createTimeRange"
              :fast-time="fastTime"
              @change="getTimeRange('create', $event)"
            >
            </date-picker>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">完成时间：</span>
            <date-picker
              type="datetimerange"
              ref="endPicker"
              v-model="completeTimeRange"
              @change="getTimeRange('complete', $event)"
            >
            </date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="search(true)"
            >查询
            </el-button>
            <el-button @click="exportRecordItem">导出</el-button>
            <el-button
              type="text"
              @click="getExportList"
            >查看已导出列表
            </el-button>
            <el-button
              type="text"
              @click="clearField"
            >清空搜索条件
            </el-button>
            <el-button type="danger" plain
                       v-permission="'order:reexchangeBatch:update'"
                       @click="dialogVisible = true"
                       v-if="$store.state.user.userData.type === 1">
              批量退汇
            </el-button>
          </div>
          <el-dialog
            title="导入退汇名单"
            :visible.sync="dialogVisible"
            width="30%"
            @close="handleClose">
            <div>
              <el-form inline>
                <el-form-item>
                  <el-select v-model="selectedType" placeholder="请选择识别模式" style="width: 100%;">
                    <el-option label="按商户订单号" value="1"></el-option>
                    <el-option label="按平台流水号" value="2"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="text"
                    size="mini"
                    style="color: #e1c930;"
                    @click="downloadOfflineTemplate">
                    <i class="el-icon-download"></i> 退汇模板下载
                  </el-button>
                </el-form-item>

                <el-upload
                  class="text-center"
                  ref="upload"
                  :file-list="fileList"
                  :on-preview="handlePreview"
                  :on-remove="handleRemove"
                  :before-upload="beforeUpload"
                  :on-success="handleSuccess"
                  :auto-upload="false"
                  :on-change="handleFileChange"
                  :action="baseUrl + '/orderReexchange/uploadReexchangeExcel'"
                  :headers="uploadHeaders"
                  :data="{ type: selectedType }"
                >
                  <el-button size="small" plain>
                    <i class="el-icon-upload2"></i> 上传文件
                  </el-button>
                </el-upload>
                <div class="text-center" style="margin-top: 10px;color: #a6a5a5;" v-show="fileList.length === 0">
                  请上传退汇模板excel文件
                </div>
              </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleUpload">退汇</el-button>
    </span>
          </el-dialog>

        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table :data="response.data">
        <el-table-column
          label="序号"
          type="index"
          :index="getIndex"
          width="50"
        ></el-table-column>

        <el-table-column
          prop="remitPlatTrxNo"
          width="250"
        >
          <template v-slot:header>
            <span>打款流水号/</span><br>
            <span>商户订单号/</span>
            <span>平台流水号</span>
          </template>
          <template v-slot="{row}">
            {{ row.remitPlatTrxNo }}<br>{{ row.mchOrderNo }}<br>{{ row.platTrxNo }}
          </template>
        </el-table-column>

        <el-table-column
          label="创建时间/完成时间"
          width="180"
        >
          <template v-slot="{row}">
            {{ row.createTime }}<br>
            {{ row.completeTime }}
          </template>
        </el-table-column>
        <el-table-column
          label="商户批次号/平台批次号"
          width="170"
        >
          <template v-slot="{row}">
            <p>{{ row.mchBatchNo }}<br>{{ row.platBatchNo }}</p>
          </template>
        </el-table-column>
        <el-table-column label="代征主体">
          <template v-slot="{row}">
            <p>{{ row.mainstayNo }}<br>{{ row.mainstayName }}</p>
          </template>
        </el-table-column>
        <el-table-column
          label="用工企业"
          width="100"
        >
          <template v-slot="{row}">
            <p>{{ row.employerNo }}<br>{{ row.employerName }}</p>
          </template>
        </el-table-column>

        <el-table-column
          label="发放方式"
          prop="channelType"
        >
          <template v-slot="{row}">
            <p>{{ $dictCode('ChannelTypeEnum', row.channelType).desc }}</p>
          </template>
        </el-table-column>

        <el-table-column
          label="收款账户"
          prop="receiveAccountNo"
          width="150"
        >
          <template v-slot="{row}">
            {{ row.receiveAccountNo }} <br> {{ row.receiveName }}
          </template>
        </el-table-column>

        <el-table-column
          label="实发金额 / 代征主体服务费"
          prop="orderNetAmount"
          width="150"
        >
          <template v-slot="{row}">
            <p class="text-right">
              {{ row.orderNetAmount | moneyFormat }}
            </p>
            <p class="text-right">
              {{ row.orderFee | moneyFormat }}
            </p>
          </template>
        </el-table-column>

        <el-table-column
          label="备注"
          prop="remark"
        ></el-table-column>
        <el-table-column
          label="支付通道"
          prop="channelName"
        ></el-table-column>

        <el-table-column
          label="失败原因"
          prop="errorDesc"
        ></el-table-column>
        <el-table-column
          label="订单状态"
          prop="processStatus"
          width="120"
        >
          <template v-slot="{row}">
            <el-tooltip
              :content="$dictCode('RecordItemStatusEnum', row.processStatus).desc"
              :disabled="$dictCode('RecordItemStatusEnum', row.processStatus).desc.length < 5"
            >
              <el-tag :type="getTagType(row.processStatus)">
                {{ $dictCode('RecordItemStatusEnum', row.processStatus).desc.slice(0, 5) }}
              </el-tag>
            </el-tooltip>
          </template>
        </el-table-column>

        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button
              v-if="row.processStatus === 300"
              type="text"
              @click="reverseQuery(row)"
              v-permission="'order:recordItem:reverseQuery'"
            >反查
            </el-button>
            <el-button
              type="text"
              class="red-btn"
              @click="reexchange(row)"
              v-if="row.processStatus === 100 && $store.state.user.userData.type == 1"
            >退汇
            </el-button>
            <el-button
              type="text"
              class="red-btn"
              @click="refundFrozenAmount(row)"
              v-if="row.processStatus === 200 && $store.state.user.userData.type == 1"
            >解冻
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="response"
          :total="totalRecord"
          :current-page="pageCurrent"
          :page-size="pageSize"
          :page-sizes="[10, 50]"
          layout="total, sizes, prev, pager, next, slot, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
          <span
            @click="forceSearch"
            class="force-next-btn"
          >下一页</span>
        </el-pagination>
      </div>
    </div>
    <ExportRecord ref="exportRecord"></ExportRecord>
  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import {getMainstayList} from '@/api/merchant'
import {
  countRecordItem,
  exportRecordItem,
  getRecordList,
  reexchange,
  refundFrozenAmount,
  reverseQuery
} from '@/api/order'
import {getProductList} from '@/api/product'
import {getPayChannelList} from "@/api/levy";
import {getToken} from "@/utils/loginToken";

export default {
  name: 'PmsTradeRecord',
  components: {
    ExportRecord
  },
  data() {
    return {
      searchForm: { // 搜索表单
        processStatus: '',
        mainstayNo: '',
        channelType: '',
        remitPlatTrxNo: '',
        mchOrderNo: '',
        platBatchNo: '',
        employerNo: '',
        employerNameLike: '',
        receiveName: '',
        receiveAccountNo: '',
        createBeginDate: '',
        createEndDate: '',
        completeBeginDate: '',
        completeEndDate: '',
        productNo: '',
        productName: ''
      },
      response: { // 查询结果
        data: [],
      },
      totalRecord: 0,
      pageSize: 10,
      pageCurrent: 1,
      fastTime: 'today',
      mainstayList: [],
      productList: [],
      createTimeRange: [],
      completeTimeRange: [],
      payChannelList: [],
      showOffline: false,
      uploadHeaders: {
        'X-Token': getToken()
      },
      dialogVisible: false,
      selectedType: '',
      fileList: []
    }
  },
  created() {
    if (this.$route.query) {
      for (let p in this.$route.query) {
        this.searchForm[p] = this.$route.query[p];
      }
      if (this.searchForm.createBeginDate) {
        this.fastTime = '';
      }
    }
  },
  mounted() {
    this.getProductList();
    this.getMainstayList();
    if (this.$route.query.mchBatchNo) {
      this.searchForm.mchBatchNo = this.$route.query.mchBatchNo;
    }
    if (sessionStorage.getItem('recordCreateTime')) {
      this.$refs.startPicker.changeTime([sessionStorage.getItem('recordCreateTime'), '']);
    }
    this.search()
    this.getPayChannelList()
  },
  beforeDestroy() {
    sessionStorage.removeItem('recordCreateTime');
    sessionStorage.removeItem('recordCompleteTime');
  },
  methods: {
    async search(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      const [{data}, {data: {totalRecord}}] = await Promise.all([
        getRecordList({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        }),
        countRecordItem(this.searchForm)
      ])
      this.response = data;
      this.totalRecord = totalRecord;
    },
    clearField() {
      this.searchForm = { // 搜索表单
        processStatus: '',
        mainstayNo: '',
        channelType: '',
        remitPlatTrxNo: '',
        mchOrderNo: '',
        platBatchNo: '',
        employerNo: '',
        employerNameLike: '',
        receiveName: '',
        receiveAccountNo: '',
        createBeginDate: '',
        createEndDate: '',
        completeBeginDate: '',
        completeEndDate: '',
        productNo: '',
        productName: '',
      }
      this.$refs.startPicker.clearTime();
      this.$refs.endPicker.clearTime();
    },
    getTimeRange(type, val) {
      this.searchForm[type + 'BeginDate'] = val && val[0];
      this.searchForm[type + 'EndDate'] = val && val[1];
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    async getProductList() {
      const {data} = await getProductList({productType: 0, pageSize: 100});
      this.productList = data.data;
    },
    async getMainstayList() {
      const {data} = await getMainstayList();
      this.mainstayList = data;
    },
    async exportRecordItem() {
      const {data} = await exportRecordItem(this.searchForm);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(this.$dictFlag('ReportTypeEnum', 'TRADE_RECORD_ITEM_PMS').code);
    },
    getTagType(status) {
      switch (Number(status)) {
        case 100:
          return 'success';
        case 200:
        case 700:
          return 'danger';
        case 300:
          return 'info';
        default:
          return '';
      }
    },
    async reverseQuery(row) {
      let form = {
        remitPlatTrxNo: row.remitPlatTrxNo
      }
      const {data} = await reverseQuery(form);
      this.$message.success(data);
    },
    forceSearch() {
      this.pageCurrent++;
      this.search();
    },
    async reexchange(row) {
      try {
        await this.$confirm('此操作将执行退汇操作，是否继续', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch {
        return
      }
      const {data} = await reexchange(row.platTrxNo);
      data && this.$message.success(data);
      this.search();
    },
    refundFrozenAmount(row) {
      this.$confirm('此操作将退回本订单在账户的冻结余额，请务必确认通道侧无此订单，是否继续', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const platTrxNo = row.platTrxNo
        refundFrozenAmount(platTrxNo).then(resp => {
          if (resp.code === 20000) {
            this.$message.success(resp.data);
          } else {
            this.$message.error(resp.message);
          }
        })
      })
    },
    getPayChannelList() {
      getPayChannelList({
        pageSize: 1000,
        pageCurrent: 1
      }).then(response => {
        this.payChannelList = response.data.data;
      })
    },
    downloadOfflineTemplate() {
      this.createDownloadLink(require('@/assets/template/platTrxNoTemplate.xlsx'), '退汇-导入模板')
    },
    handleClose() {
      this.dialogVisible = false;
      this.fileList = [];
      this.selectedType = '';
    },
    handlePreview(file) {
      console.log(file);
    },
    handleRemove(file, fileList) {
      this.fileList = fileList;
    },
    beforeUpload(file) {
      if (this.fileList.length > 1) {
        this.$message.error('只能上传一个文件');
        return false;
      }
      const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      if (!isExcel) {
        this.$message.error('上传文件格式不正确，请上传 .xls 或 .xlsx 文件');
        return false;
      }
      return true;
    },
    handleFileChange(file, fileList) {
      if (file.status === 'ready') {
        this.fileList = [...fileList];
        this.fileFlag = true;
      } else if (file.status === 'removed') {
        this.fileList = fileList.filter(f => f.uid !== file.uid);
      }
      if (this.fileList.length > 1) {
        this.$message.warning('最多只能上传 1 个文件');
        this.fileList = this.fileList.slice(0, 1);
      }
    },
    handleSuccess(response, file, fileList) {
      if (response && response.data) {
        this.$message.success(response.data);
      } else {
        this.$message.error(response.message);
      }
      this.showOffline = false
      this.handleClose();
      this.search()
    },
    handleUpload() {
      if (this.fileList.length === 0) {
        this.$message.warning('请先选择文件！');
        return;
      }
      //selectedType判空逻辑
      if (!this.selectedType) {
        this.$message.warning('请选择识别模式！');
        return;
      }
      // 手动触发上传
      this.$refs.upload.submit();

    }
  }
}
</script>

<style
  lang="scss"
  scoped>
.box-container {
  padding: 20px 20px;
}
</style>
