<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">订单号：</span>
          <el-input
            clearable
            v-model="searchForm.withdrawNo"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">代征主体：</span>
          <el-select
            clearable
            v-model="searchForm.mainstayNo"
          >
            <el-option
              v-for="(item, index) in mainstayList"
              :key="index"
              :label="item.mchName"
              :value="item.mchNo"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">订单状态：</span>
          <el-select
            v-model="searchForm.withdrawStatus"
            clearable
          >
            <el-option
              v-for="item in $dict('WithdrawStatusEnum')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">商户类型：</span>
          <el-select
            v-model="searchForm.merchantType"
            clearable
          >
            <el-option
              v-for="item in $dict('MerchantTypeEnum')"
              :key="item.code"
              :label="item.desc"
              :value="item.code"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业名称：</span>
          <el-input
            v-model="searchForm.employerNameLike"
            clearable
            placeholder="模糊查询"
          >
          </el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">支付通道：</span>
          <el-select
            clearable
            v-model="searchForm.channelNo"
          >
            <el-option
              v-for="(item, index) in $dict('ChannelNoEnum')"
              :key="index"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            ref="datepicker"
            type="datetime"
            picker="separate"
            :start-time.sync="searchForm.createBeginDate"
            :end-time.sync="searchForm.createEndDate"
            fast-time="today"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)">查询
        </el-button>
        <el-button @click="exportList">导出</el-button>
        <el-button
          type="text"
          @click="getExportList">查看已导出列表
        </el-button>
        <el-button
          type="text"
          @click="resetForm">清空筛选条件
        </el-button>

        <el-tag type="info" style="margin-left: 60px">
          合计: {{ sumInfo.totalNum || 0 }} 笔， {{ sumInfo.sumAmt || 0 }} 元。
        </el-tag>
      </div>
    </div>

    <div class="content-container">
      <el-table :data="list">
        <el-table-column
          label="订单号"
          prop="withdrawNo"
          width="200"></el-table-column>

        <el-table-column
          label="创建时间"
          prop="createTime"
          width="120">
          <template v-slot="{row, column}">
            <p v-html="renderTime(row[column['property']])"></p>
          </template>
        </el-table-column>
        <el-table-column
          label="完成时间"
          prop="updateTime"
          width="120">
          <template v-slot="{row, column}">
            <p v-html="renderTime(row[column['property']])"></p>
          </template>
        </el-table-column>

        <el-table-column
          label="账户类型"
          prop="channelType">
          <template v-slot="{row}">
            {{ $dictCode('ChannelTypeEnum', row.channelType).desc }}({{ row.channelName }})
          </template>
        </el-table-column>

        <el-table-column
          label="商户类型"
          prop="merchantType">
          <template v-slot="{row}">
            {{ $dictCode('MerchantTypeEnum', row.merchantType).desc }}
          </template>
        </el-table-column>

        <el-table-column
          label="用工企业"
          prop="employerNo"
          width="150">
          <template v-slot="{row}">
            {{ row.employerNo }}<br>{{ row.employerName }}
          </template>
        </el-table-column>

        <el-table-column
          label="代征主体"
          prop="mainstayNo"
          width="150">
          <template v-slot="{row}">
            {{ row.mainstayNo }}<br>{{ row.mainstayName }}
          </template>
        </el-table-column>

        <el-table-column
          label="提现金额"
          prop="amount"
          width="200">
          <template v-slot="{row}">
            <p class="float-r">{{ row.amount | moneyFormat }}</p>
          </template>
        </el-table-column>

        <el-table-column
          label="备注"
          prop="remark"></el-table-column>

        <el-table-column
          label="错误信息"
          prop="errorMsg"
          width="180"></el-table-column>

        <el-table-column
          label="订单状态"
          prop="withdrawStatus"
          fixed="right">
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.withdrawStatus)">
              {{ $dictCode('WithdrawStatusEnum', row.withdrawStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        v-if="list"
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>

    <export-record ref="exportRecord"></export-record>

  </div>
</template>

<script>
import { getMainstayList } from '@/api/merchant';
import { getWithdrawRecord, exportWithdrawRecord,sumWithdrawRecord } from '@/api/order';
import ExportRecord from '@/components/ExportRecord';

export default {
  name: 'PmsWithdrawRecord',
  components: {
    ExportRecord
  },
  data() {
    return {
      searchForm: {
        withdrawNo: '',
        withdrawStatus: '',
        mainstayNo: '',
        createBeginDate: '',
        createEndDate: '',
        merchantType: '',
        employerNameLike: '',
      },

      list: [],
      sumInfo:{},
      mainstayList: [],

      totalRecord: 0,
      pageSize: 10,
      pageCurrent: 1,
    }
  },
  mounted() {
    getMainstayList().then(res => {
      this.mainstayList = res.data
    });
    this.search();
  },
  methods: {
    async search(init) {
      if (init) {
        this.pageCurrent = 1;
      }
      const { data } = await getWithdrawRecord({
        ...this.searchForm,
        pageCurrent: this.pageCurrent,
        pageSize: this.pageSize
      });
      this.list = data.data;
      this.totalRecord = data.totalRecord;

      sumWithdrawRecord({
        ...this.searchForm,
        pageCurrent: this.pageCurrent,
        pageSize: this.pageSize
      }).then(res=>{
        this.sumInfo = res.data;
        console.log(this.sumInfo)
      })
    },
    resetForm() {
      this.searchForm = {
        withdrawNo: '',
        withdrawStatus: '',
        mainstayNo: '',
        createBeginDate: '',
        createEndDate: '',
      };
      this.$refs.datepicker.clearTime()
    },
    async exportList() {
      const { data } = await exportWithdrawRecord(this.searchForm);
      data && this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('24')
    },
    getTagType(status) {
      switch (Number(status)) {
        case 0:
          return '';
        case 1:
          return 'success';
        case 2:
          return 'danger';
        default:
          return ''
      }
    }
  },
}
</script>

<style
  scoped
  lang="scss">

</style>
