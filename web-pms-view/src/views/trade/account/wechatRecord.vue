<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">微信单号：</span>
          <el-input
            clearable
            v-model="form.incomeRecordId"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业编号：</span>
          <el-input
            clearable
            v-model="form.mchNo"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业名称：</span>
          <el-input
            clearable
            v-model="form.mchNameLike"
            placeholder="模糊查询"
          ></el-input>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">特约商户ID：</span>
          <el-input
            clearable
            v-model="form.subMchId"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">代征主体：</span>
          <el-select
            v-model="form.mainstayNo"
            clearable
          >
            <el-option
              v-for="item in mainstayList"
              :key="item.id"
              :label="item.mchName"
              :value="item.mchNo"
            >
            </el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">订单状态：</span>
          <el-select
            v-model="form.status"
            clearable
          >
            <el-option
              v-for="item in $dict('WxIncomeRecordEnum')"
              :key="item.code"
              :label="item.desc"
              :value="Number(item.code)"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">完成时间：</span>
          <date-picker
            ref="datepicker"
            type="datetime"
            picker="separate"
            :start-time.sync="form.beginDate"
            :end-time.sync="form.endDate"
          ></date-picker>
        </div>
      </div>

      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询
        </el-button>
        <el-button @click="exportList">导出</el-button>
        <el-button
          type="text"
          @click="getExportList"
        >查看已导出列表
        </el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件
        </el-button>
      </div>
    </div>

    <el-table :data="list">
      <el-table-column
        label="微信单号"
        prop="incomeRecordId"
        width="120"
      ></el-table-column>
      <el-table-column
        label="完成时间"
        prop="successTime"
        width="150"
      >
        <template v-slot="{row, column}">
          <div v-html="renderTime(row[column.property])">
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="商户"
        prop="mchName"
        width="150"
      >
        <template v-slot="{row}">
          {{ row.mchNo }}<br />{{ row.mchName }}
        </template>
      </el-table-column>
      <el-table-column
        label="特约商户ID"
        prop="subMchId"
        width="150"
      ></el-table-column>
      <el-table-column
        label="代征主体"
        prop="mainstayName"
        width="150"
      ></el-table-column>
      <el-table-column
        label="充值金额"
        prop="amount"
        width="180"
      >
        <template v-slot="{row}">
          <div class="text-right">
            {{ ((row.amount) / 100) | moneyFormat }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="充值类型"
        prop="incomeType"
      >
        <template v-slot="{row}">
          {{ $dictCode('WxIncomeTypeEnum', row.incomeType).desc }}
        </template>
      </el-table-column>
      <el-table-column
        label="付款银行名称"
        prop="bankName"
        width="150"
      ></el-table-column>
      <el-table-column
        label="付款银行账户名"
        prop="bankAccountName"
        width="150"
      ></el-table-column>
      <el-table-column
        label="付款银行卡号"
        prop="bankAccountNumber"
        width="150"
      ></el-table-column>
      <el-table-column
        label="订单状态"
        prop="status"
      >
        <template v-slot="{row}">
          <el-tag :type="getTagType(row.status)">
            {{ $dictCode('WxIncomeRecordEnum', row.status).desc }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        prop="rechargeRemark"
      ></el-table-column>
      <el-table-column label="操作">
        <template v-slot="{row}">
          <el-button
            type="text"
            @click="openAudit(row)"
            v-if="row.status == 101"
          >
            审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-footer class="pagination-container">
      <el-pagination
        v-if="list"
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>

    <export-record ref="exportRecord"></export-record>

    <!-- audit dialog -->
    <el-dialog
      :visible="showAudit"
      :before-close="closeAudit"
      title="审核"
    >
      <warning-block>
        <span style="color: #000">
          审核通过将为该商户入账，请谨慎处理！
        </span>
      </warning-block>
      <el-form
        :model="editRow"
        :rules="rules"
        label-width="200px"
        ref="form"
      >
        <el-form-item label="充值金额：">{{ (editRow.amount / 100) | moneyFormat }}</el-form-item>
        <el-form-item
          label="用工企业："
          prop="mchNo"
        >
          <el-select
            v-width="{default: 200, max: '100%'}"
            v-model="editRow.mchNo"
            clearable
            filterable
          >
            <el-option
              v-for="item in merchantList"
              :key="item.mchNo"
              :label="item.mchName + '(' + item.mchNo + ')' "
              :value="item.mchNo"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="银行出款回单：">
          <file-upload :url.sync="editRow.payImgUrl"></file-upload>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button
          type="primary"
          @click="confirm"
        >审核通过
        </el-button>
        <el-button @click="closeAudit">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ExportRecord from "@/components/ExportRecord";
import FileUpload from "@/components/FileUpload"
import { getMainstayList, getAllMerchant } from "@/api/merchant";
import { getWxIncomeRecord, confirmWxIncome, expotWxRecord } from '@/api/wechat'

export default {
  name: "wechat-record",
  components: {
    ExportRecord,
    FileUpload,
  },
  data() {
    return {
      form: {},
      mainstayList: [],
      merchantList: [],

      list: [],
      pageCurrent: 1,
      pageSize: 10,
      totalRecord: 0,

      showAudit: false,
      rules: {
        mchNo: [
          { required: true, message: '请选择商户', trigger: 'change' },
        ]
      },
      editRow: {
        id: '',
        mchNo: '',
        mchName: '',
        payImgUrl: '',
        amount: '',
      }
    };
  },
  mounted() {
    this.init();
    this.search();
  },
  methods: {
    async search(init) {
      if (init === true) this.pageCurrent = 1;
      const { data: { data, totalRecord } } = await getWxIncomeRecord({
        ...this.form,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      });
      this.list = data;
      this.totalRecord = totalRecord;
    },
    async init() {
      const [
        { data: mainstayList },
        { data: merchantList }
      ] = await Promise.all([
        getMainstayList(),
        getAllMerchant({ merchantType: 100 })
      ])
      this.mainstayList = mainstayList;
      this.merchantList = merchantList;
    },
    resetForm() {
      this.form = {}
      this.$refs.datepicker.clearTime()
    },
    async exportList() {
      const { data } = await expotWxRecord(this.form)
      data && this.$message.success(data)
    },
    getExportList() {
      this.$refs.exportRecord.open('36')
    },
    openAudit(row) {
      for (let key in this.editRow) {
        this.editRow[key] = row[key]
      }
      this.showAudit = true;
    },
    async confirm() {
      try {
        await this.$refs.form.validate()
        for (let i = 0, l = this.merchantList.length; i < l; i++) {
          if (this.merchantList[i].mchNo === this.editRow.mchNo) {
            this.editRow.mchName = this.merchantList[i].mchName;
            break;
          }
        }
        const { data } = await confirmWxIncome(this.editRow)
        data && this.$message.success(data)
        this.closeAudit()
      } catch {
        return
      }
    },
    closeAudit() {
      this.showAudit = false;
      this.$refs.form.resetFields();
    },
    getTagType(type) {
      switch (Number(type)) {
        case 100:
          return 'success';
        case 102:
          return 'danger';
        default:
          return ''
      }
    },
  },
};
</script>

<style>
</style>
