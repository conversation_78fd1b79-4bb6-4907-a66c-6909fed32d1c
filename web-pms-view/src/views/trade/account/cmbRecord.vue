<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">交易流水号：</span>
          <el-input
            clearable
            v-model="form.channelTrxNo"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业编号：</span>
          <el-input
            clearable
            v-model="form.mchNo"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业名称：</span>
          <el-input
            clearable
            v-model="form.mchName"
            placeholder="模糊查询"
          ></el-input>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">通道商户号：</span>
          <el-input
            clearable
            v-model="form.payeeAccountNo"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">代征主体：</span>
          <el-select
            v-model="form.mainstayNo"
            clearable
          >
            <el-option
              v-for="item in mainstayList"
              :key="item.id"
              :label="item.mchName"
              :value="item.mchNo"
            >
            </el-option>
          </el-select>
        </div>
        <div class="flex-item">
            <span class="flex-item__label">订单状态：</span>
          <el-select
            v-model="form.state"
            clearable
          >
            <el-option
              v-for="item in $dict('CmbIncomeStateEnum')"
              :key="item.code"
              :label="item.desc"
              :value="Number(item.code)"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">完成时间：</span>
          <date-picker
            ref="datepicker"
            type="datetime"
            picker="separate"
            :start-time.sync="form.finishStartTime"
            :end-time.sync="form.finishEndTime"
          ></date-picker>
        </div>
      </div>

      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询
        </el-button>
        <el-button @click="exportList">导出</el-button>
        <el-button
          type="text"
          @click="getExportList"
        >查看已导出列表
        </el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件
        </el-button>
      </div>
    </div>

    <el-table :data="list">
      <el-table-column
        label="交易流水号"
        prop="channelTrxNo"
        width="120"
      ></el-table-column>
      <el-table-column
        label="完成时间"
        prop="successTime"
        width="150"
      >
        <template v-slot="{row, column}">
          <div v-html="renderTime(row[column.property])">
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="入账商户"
        prop="mchName"
        width="150"
      >
        <template v-slot="{row}">
          {{ row.mchNo }}<br />{{ row.mchName }}
        </template>
      </el-table-column>
      <el-table-column
        label="收款账号"
        prop="payeeAccountNo"
        width="150"
      ></el-table-column>
      <el-table-column
        label="代征主体"
        prop="mainstayName"
        width="150"
      ></el-table-column>
      <el-table-column
        label="充值金额"
        prop="amount"
        width="180"
      >
        <template v-slot="{row}">
          <div class="text-right">
            {{ row.amount | moneyFormat }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="付款银行名称"
        prop="bankName"
        width="150"
      ></el-table-column>
      <el-table-column
        label="付款账户名"
        prop="bankAccountName"
        width="150"
      ></el-table-column>
      <el-table-column
        label="付款银行卡号"
        prop="bankAccountNumber"
        width="150"
      ></el-table-column>
      <el-table-column
        label="订单状态"
        prop="status"
      >
        <template v-slot="{row}">
          <el-tag :type="getTagType(row.state)">
            {{ $dictCode('CmbIncomeStateEnum', row.state).desc }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        prop="rechargeRemark"
      ></el-table-column>
      <el-table-column label="操作">
        <template v-slot="{row}">
          <el-button
            type="text"
            @click="openAudit(row)"
            v-if="row.state == 100"
          >
            审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-footer class="pagination-container">
      <el-pagination
        v-if="list"
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>

    <export-record ref="exportRecord"></export-record>

    <!-- audit dialog -->
    <el-dialog
      :visible="showAudit"
      :before-close="closeAudit"
      title="审核"
    >
      <warning-block>
        <span style="color: #000">
          审核通过将为该商户入账，请谨慎处理！
        </span>
      </warning-block>
      <el-form
        :model="editRow"
        :rules="rules"
        label-width="200px"
        ref="form"
      >
        <el-form-item label="充值金额：">{{ editRow.amount | moneyFormat }}</el-form-item>
        <el-form-item
          label="入账商户："
          prop="mchId"
        >
          <el-select
            v-width="{default: 200, max: '100%'}"
            v-model="editRow.mchId"
            clearable
            filterable
          >
            <el-option
              v-for="item in merchantList"
              :key="item.id"
              :label="item.mchName + '(' + item.mchNo + ')' "
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button
          type="primary"
          @click="confirm"
        >审核通过
        </el-button>
        <el-button @click="closeAudit">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ExportRecord from "@/components/ExportRecord";
import { getMainstayList, getAllMerchantById } from "@/api/merchant";
import { getCmbIncomeRecord, confirmCmbIncome, exportCmbRecord } from '@/api/wechat'

export default {
  name: "cmb-record",
  components: {
    ExportRecord,
  },
  data() {
    return {
      form: {},
      mainstayList: [],
      merchantList: [],

      list: [],
      pageCurrent: 1,
      pageSize: 10,
      totalRecord: 0,

      showAudit: false,
      rules: {
        mchId: [
          { required: true, message: '请选择商户', trigger: 'change' },
        ]
      },
      editRow: {
        id: '',
        mchId: '',
        amount: '',
      }
    };
  },
  mounted() {
    this.init();
    this.search();
  },
  methods: {
    async search(init) {
      if (init === true) this.pageCurrent = 1;
      const { data: { records, total } } = await getCmbIncomeRecord({
        ...this.form,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      });
      this.list = records;
      this.totalRecord = total;
    },
    async init() {
      const { data: mainstayList } = await getMainstayList()
      this.mainstayList = mainstayList;
    },
    resetForm() {
      this.form = {}
      this.$refs.datepicker.clearTime()
    },
    async exportList() {
      const { data } = await exportCmbRecord(this.form)
      data && this.$message.success(data)
    },
    getExportList() {
      this.$refs.exportRecord.open('65');
    },
    async openAudit(row) {
      const { data: merchantList } = await getAllMerchantById({ id: row.id });
      this.merchantList = merchantList;
      this.editRow.id = row.id;
      this.editRow.amount = row.amount;
      this.showAudit = true;
    },
    async confirm() {
      try {
        await this.$refs.form.validate()
        const { data } = await confirmCmbIncome(this.editRow)
        data && this.$message.success(data)
        this.closeAudit()
      } catch {
        return
      }
    },
    closeAudit() {
      this.showAudit = false;
      this.$refs.form.resetFields();
    },
    getTagType(type) {
      switch (Number(type)) {
        case 100:
          return 'success';
        case 102:
          return 'danger';
        default:
          return ''
      }
    },
  },
};
</script>

<style>
</style>
