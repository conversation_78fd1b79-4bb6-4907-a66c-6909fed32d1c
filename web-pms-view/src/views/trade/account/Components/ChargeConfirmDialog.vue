<template>
  <el-dialog
    :visible="visible"
    title="充值记录"
    :before-close="close"
    :close-on-click-modal="false"
  >
    <el-form ref="form" :model="submitForm" label-width="150px" :rules="rules">
      <el-form-item label="充值金额：" prop="rechargeAmount">
        <el-input
          v-model="submitForm.rechargeAmount"
          clearable
          style="width: 200px;"
        ></el-input>
      </el-form-item>
      <el-form-item label="订单状态：" prop="rechargeStatus">
        <el-select
          v-model="submitForm.rechargeStatus"
        >
          <el-option
            v-for="item in $dict('RechargeStatusEnum')"
            :key="item.code"
            :value="Number(item.code)"
            :label="item.desc"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="邮件通知商户：" prop="emailNotify" v-show="submitForm.rechargeStatus == 1">
        <el-switch
          v-model="submitForm.emailNotify"
        ></el-switch>
      </el-form-item>
      <el-form-item label="银行出款回单：">
        <el-upload
          v-toggle="fileList"
          class="upload-demo"
          :action="baseUrl + '/file/upload'"
          :headers="uploadHeader"
          list-type="picture-card"
          accept=".pdf,.bmp,.png,.jpeg,.jpg,.gif"
          :limit="1"
          :file-list="fileList"
          :before-upload="validateUploadFile({img: true, pdf: true, size: 6})"
          :on-success="(response, file, fileList) => handleSuccess(response, file, fileList)"
          :on-remove="(file, fileList) => handleRemove(fileList, file)"
        >
          <i class="el-icon-plus avatar-uploader-icon"></i>
          <div slot="file" slot-scope="{file}" class="fileBg" v-if="isPdf(file.name)">
            <span class="file-name">{{ file.name }}</span>
            <span class="el-upload-list__item-actions">
              <span
                class="el-upload-list__item-delete"
                @click="handleFileRemove(file)"
              >
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item label="备注：">
        <el-input
          type="textarea"
          v-model="submitForm.remark"
          :rows="4"
        ></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer">
      <el-button type="primary" @click="submit">确定</el-button>
      <el-button @click="close">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { editRecord } from '@/api/recharge';
  export default {
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      form: {
        type: Object,
        default: () =>({})
      }
    },
    data() {
      return {
        submitForm: {
          "rechargeOrderId": "",
          "rechargeAmount": "",
          "receiptUrl": "",
          "remark": "",
          "rechargeStatus": 0,
          "emailNotify": false,
        },

        fileList: [],

        rules: {
          rechargeAmount: [{required: true, trigger: 'blur', message: '请输入充值金额'}],
          rechargeStatus: [{required: true, trigger: 'blur', message: '请选择订单状态'}],
        }
      }
    },
    watch: {
      visible: {
        handler(val) {
          if (val) {
            for (let p in this.submitForm) {
              if (this.form[p] !== undefined) {
                this.submitForm[p] = this.form[p]
              }
            }
            if (this.form.receiptUrl) {
              this.fileList.push({
                url: this.fileUrl + this.form.receiptUrl,
                name: this.isPdf(this.form.receiptUrl) ? '银行出款回单.pdf' : ''
              });
              this.submitForm.receiptUrl = this.fileList;
            }
          } else {
            this.fileList = [];
            this.$refs.form.resetFields();
          }
        }
      }
    },
    methods: {
      close() {
        this.$emit('update:visible', false)
      },
      async submit() {
        const valid = await this.$refs.form.validate().catch(_ => false);
        if (!valid) return;
        if (this.fileList[0]) {
          this.submitForm.receiptUrl = this.fileList[0].response ? this.fileList[0].response.data : this.fileList[0].origin
        }
        await editRecord(this.submitForm);
        this.$emit('change')
        this.close();
      },
      async handleSuccess(response, file, fileList) {
        let formatFileList = []
        for (let i = 0; i < fileList.length; i++) {
          const item = fileList[i];
          const url = item.response ? item.response.data : item.origin
          const fileMsg = await this.formatFileMsg(url);
          formatFileList.push({
            url: fileMsg.fileUrl,
            origin: url,
            uid: item.uid,
            name: item.name,
          });
        }
        this.submitForm.receiptUrl = this.fileList = formatFileList;
      },
      handleRemove (fileList, file) {
        this.submitForm.receiptUrl = this.fileList = fileList;
      },
      handleFileRemove(file) {
        this.submitForm.receiptUrl = this.fileList = this.fileList.filter(item => {
          return item.uid != file.uid
        })
      }
    },
  }
</script>

<style scoped lang="scss">

</style>
