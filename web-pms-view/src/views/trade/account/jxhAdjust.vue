<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">唯一键：</span>
          <el-input
            clearable
            v-model="form.logKey"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">变动类型：</span>
          <el-select
            clearable
            v-model="form.amountChangeType"
          >
            <el-option
              v-for="(item) in $dict('WxAmountChangeLogTypeEnum')"
              :key="item.code"
              :label="item.desc"
              :value="item.code"
            ></el-option>
          </el-select>
        </div>

        <div class="flex-item">
          <span class="flex-item__label">流水号：</span>
          <el-input
            clearable
            v-model="form.platTrxNo"
          ></el-input>
        </div>
      </div>

      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">供应商名称：</span>
          <el-input
            clearable
            v-model="form.mainstayName"
            placeholder="模糊搜索"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">供应商编号：</span>
          <el-input
            clearable
            v-model="form.mainstayNo"
          ></el-input>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">商户名称：</span>
          <el-input
            clearable
            v-model="form.mchName"
            placeholder="模糊搜索"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">商户编号：</span>
          <el-input
            clearable
            v-model="form.mchNo"
          ></el-input>
        </div>
      </div>

      <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            ref="datepicker"
            type="datetime"
            picker="separate"
            :start-time.sync="form.createTimeBegin"
            :end-time.sync="form.createTimeEnd"
          ></date-picker>
        </div>
      </div>

      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询</el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件</el-button>
      </div>
    </div>

    <el-table :data="list">
      <el-table-column
        label="唯一键"
        prop="logKey"
        width="120"
      ></el-table-column>
      <el-table-column
        label="流水号"
        prop="platTrxNo"
        width="120"
      ></el-table-column>
      <el-table-column
        label="创建时间"
        prop="createTime"
        width="150"
      >
        <template v-slot="{row, column}">
          <div v-html="renderTime(row[column.property])">
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="商户"
        prop="mchName"
        width="150"
      >
        <template v-slot="{row}">
          {{row.mchNo}}<br />{{row.mchName}}
        </template>
      </el-table-column>
      <el-table-column
        label="供应商"
        prop="mainstayName"
        width="150"
      >
        <template v-slot="{row}">
          {{ row.mainstayNo}} <br/> {{ row.mainstayName }}
        </template>
      </el-table-column>
      <el-table-column
        label="变动金额	"
        prop="amount"
        width="180"
      >
        <template v-slot="{row}">
          <div class="text-right">
            {{ row.amount | moneyFormat}}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="冻结金额"
        prop="frozenAmount"
        width="180"
      >
        <template v-slot="{row}">
          <div class="text-right">
            {{ row.frozenAmount | moneyFormat}}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="变动类型	"
        prop="amountChangeType"
        fixed="right"
      >
        <template v-slot="{row}">
          {{ $dictCode('WxAmountChangeLogTypeEnum', row.amountChangeType).desc }}
        </template>
      </el-table-column>
    </el-table>

    <el-footer class="pagination-container">
      <el-pagination
        v-if="list"
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>

  </div>
</template>

<script>

  import { jxhChangeList } from '@/api/wechat'
  export default {
    name: 'jxh-adjust',
    data() {
      return {
        form: {},
        list: [],
        pageCurrent: 1,
        pageSize: 10,
        totalRecord: 0,
      }
    },
    mounted() {
      this.init()
      this.search()
    },
    methods: {
      init() {
        for (let p in this.$route.query) {
          this.form[p] = this.$route.query[p]
        }
      },
      async search(init) {
        if (init === true) {
          this.pageCurrent = 1
        }
        const { data: { data, totalRecord } } = await jxhChangeList({
          ...this.form,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent,
        })
        this.list = data;
        this.totalRecord = totalRecord;
      },
      resetForm() {
        this.form = {}
        this.$refs.datepicker.clearTime()
      },

    }
  }
</script>

<style>
</style>