<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">订单号：</span>
          <el-input
            clearable
            v-model="searchForm.rechargeOrderId"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业编号：</span>
          <el-input
            clearable
            v-model="searchForm.employerNo"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业名称：</span>
          <el-input
            clearable
            v-model="searchForm.employerNameLike"
            placeholder="模糊查询"
          ></el-input>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">账户类型：</span>
          <el-select
            clearable
            v-model="searchForm.channelType"
          >
            <el-option
              v-for="(item, index) in $dict('ChannelTypeEnum')"
              :key="index"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">代征主体：</span>
          <el-select
            clearable
            v-model="searchForm.mainstayNo"
          >
            <el-option
              v-for="(item, index) in mainstayList"
              :key="index"
              :label="item.mchName"
              :value="item.mchNo"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">订单状态：</span>
          <el-select
            clearable
            v-model="searchForm.rechargeStatus"
          >
            <el-option
              v-for="(item, index) in $dict('RechargeStatusEnum')"
              :key="index"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">支付通道：</span>
          <el-select
            clearable
            v-model="searchForm.channelCode"
          >
            <el-option
              v-for="(item, index) in $dict('ChannelNoEnum')"
              :key="index"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            ref="datepicker"
            type="datetime"
            picker="separate"
            :start-time.sync="searchForm.createBeginDate"
            :end-time.sync="searchForm.createEndDate"
            fast-time="today"
          ></date-picker>
        </div>
      </div>

      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)">查询
        </el-button>
        <el-button @click="exportMultiList">批量下载</el-button>
        <el-button @click="exportList">导出</el-button>
        <el-button
          type="text"
          @click="getExportList">查看已导出列表
        </el-button>
        <el-button
          type="text"
          @click="resetForm">清空筛选条件
        </el-button>

        <el-tag type="info" style="margin-left: 60px">
          合计： {{ sumInfo.totalNum || 0 }} 笔， {{ sumInfo.sumAmt || 0 }} 元。
        </el-tag>
      </div>
    </div>

    <div class="content-container">
      <el-table
        :data="list"
        :key="tableId">
        <el-table-column
          label="订单号"
          prop="rechargeOrderId"
          width="120"></el-table-column>

        <el-table-column
          label="创建时间/支付时间"
          prop="createTime"
          width="180">
          <template v-slot="{row}">
            {{ row.createTime }}<br>
            {{ row.transPayTime }}
          </template>
        </el-table-column>

        <el-table-column
          label="用工企业"
          width="120">
          <template v-slot="{row}">
            {{ row.employerNo }}<br>{{ row.employerName }}
          </template>
        </el-table-column>

        <el-table-column
          label="账户类型(支付通道)"
          prop="channelType"
          width="160">
          <template v-slot="{row}">
            {{ $dictCode('ChannelTypeEnum', row.channelType).desc }}
            ({{ row.channelName }})
          </template>
        </el-table-column>

        <el-table-column
          label="收款方名称"
          width="120">
          <template v-slot="{row}">
            {{ row.payeeName }}
          </template>
        </el-table-column>
        <el-table-column
          label="收款方账号"
          width="120">
          <template v-slot="{row}">
            {{ row.payeeIdentity }}<br>
          </template>
        </el-table-column>

        <el-table-column
          label="付款方名称"
          width="120">
          <template v-slot="{row}">
            {{ row.payerName }}
          </template>
        </el-table-column>

        <el-table-column
          label="付款方账号"
          width="120">
          <template v-slot="{row}">
            {{ row.payerBankName }}<br>
            {{ row.payerIdentity }}
          </template>
        </el-table-column>


        <el-table-column
          label="代征主体"
          width="120">
          <template v-slot="{row}">
            {{ row.mainstayNo }}<br>{{ row.mainstayName }}
          </template>
        </el-table-column>

        <el-table-column
          label="充值金额"
          prop="rechargeAmount"
          width="200">
          <template v-slot="{row}">
            <p class="text-right">{{ row.rechargeAmount | moneyFormat }}</p>
          </template>
        </el-table-column>

        <el-table-column
          label="充值类型"
          prop="rechargeType">
          <template v-slot="{row}">
            {{ $dictCode('RechargeTypeEnum', row.rechargeType).desc }}
          </template>
        </el-table-column>

        <el-table-column
          label="订单状态"
          prop="rechargeStatus">
          <template v-slot="{row}">
            <el-tag :type="getStatusType(row.rechargeStatus)">
              {{ $dictCode('RechargeStatusEnum', row.rechargeStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="当前余额"
          prop="currentBalance"
          width="120">
          <template v-slot="{row}">
            <p class="text-right">{{ row.currentBalance | moneyFormat }}</p>
          </template>
        </el-table-column>
        <el-table-column
          label="银行出款回单"
          width="110"
          prop="receiptUrl">
          <template v-slot="{row}">
            <!--<img v-if="row.receiptUrl" v-preview="fileUrl + row.receiptUrl" />-->
            <span
              class="func-content"
              v-if="row.receiptUrl"
              @click="checkReceiptUrl(row.receiptUrl)">查看回单</span>
          </template>
        </el-table-column>

        <el-table-column
          label="备注"
          prop="remark"
          width="120"></el-table-column>

        <el-table-column
          label="操作"
          fixed="right">
          <template v-slot="{row}">
            <el-button
              v-if="row.channelType == 1"
              type="text"
              @click="handleConfirm(row)">编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        v-if="list"
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>

    <export-record ref="exportRecord"></export-record>

    <charge-confirm-dialog
      :form="rowData"
      :visible.sync="visible"
      @change="search"></charge-confirm-dialog>

  </div>
</template>

<script>
import { getMainstayList } from '@/api/merchant';
import { getMultiRecordList, getRechargeRecord, getRecordList,sumRechargeRecord } from '@/api/recharge';
import ExportRecord from '@/components/ExportRecord';
import ChargeConfirmDialog from './Components/ChargeConfirmDialog'

export default {
  name: "PmsChargeRecord",
  components: {
    ExportRecord,
    ChargeConfirmDialog,
  },
  data() {
    return {
      searchForm: {
        rechargeOrderId: '',
        employerNo: '',
        employerNameLike: '',
        mainstayNo: '',
        channelType: '',
        rechargeStatus: '',
        createBeginDate: '',
        createEndDate: '',
      },

      mainstayList: [],

      list: [],
      sumInfo:{},

      totalRecord: 0,
      pageSize: 10,
      pageCurrent: 1,

      rowData: {},
      visible: false,

      tableId: Date.now()
    }
  },
  mounted() {
    getMainstayList().then(res => {
      this.mainstayList = res.data
    });
    this.search()
  },
  methods: {
    async search(init) {
      if (init) {
        this.pageCurrent = 1;
      }
      const { data } = await getRechargeRecord({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent
      });
      this.list = data.data;
      this.totalRecord = data.totalRecord;
      this.tableId = Date.now();

      sumRechargeRecord({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent
      }).then(res=>{
        this.sumInfo = res.data;
        console.log(this.sumInfo)
      })
    },
    resetForm() {
      this.searchForm = {
        rechargeOrderId: '',
        employerNo: '',
        employerNameLike: '',
        mainstayNo: '',
        channelType: '',
        rechargeStatus: '',
      };
      this.$refs.datepicker.clearTime()
    },
    getStatusType(status) {
      switch (Number(status)) {
        case 0:
          return 'primary';
        case 1:
          return 'success';
        case 2:
          return 'danger';
        default:
          return ''
      }
    },
    async exportList() {
      await getRecordList(this.searchForm);
      this.$message.success('操作成功')
    },
    async exportMultiList() {
      await getMultiRecordList(this.searchForm);
      this.$message.success('操作成功')
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('23')
    },
    handleConfirm(row) {
      this.rowData = row;
      this.visible = true;
    },
    async checkReceiptUrl(url) {
      const fileMsg = await this.formatFileMsg(url)
      if (this.isPdf(url)) {
        window.open(fileMsg.fileUrl)
      } else {
        this.$preview(fileMsg.fileUrl)
      }
    },
  },
}
</script>

<style
  scoped
  lang="scss">

</style>
