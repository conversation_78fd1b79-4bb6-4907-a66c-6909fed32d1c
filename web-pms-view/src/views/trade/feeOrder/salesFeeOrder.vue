<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">用工企业编号：</span>
            <el-input v-model="searchForm.mchNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">用工企业名称：</span>
            <el-input
              v-model="searchForm.mchNameLike"
              placeholder="模糊查询"
            ></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">供应商编号：</span>
            <el-input v-model="searchForm.vendorNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">产品编号：</span>
            <el-select
              clearable
              v-model="searchForm.productNo"
            >
              <el-option
                v-for="(item, index) in $dict('FeeOrderProductNoEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">交易流水号：</span>
            <el-input v-model="searchForm.platTrxNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">销售员：</span>
            <el-select
              clearable
              v-model="searchForm.salerId"
            >
              <el-option
                v-for="item in staffList"
                :key="item.id"
                :label="item.realName"
                :value="item.id"
              ></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">是否有合伙人：</span>
            <el-select
              v-model="searchForm.existAgent"
              clearable
            >
              <el-option
                :value="true"
                label="是"
              ></el-option>
              <el-option
                :value="false"
                label="否"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              ref="endPicker"
              type="datetimerange"
              v-model="createTimeRange"
              @change="getTimeRange('createTime', $event)">
            </date-picker>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">完成时间：</span>
            <date-picker
              type="datetimerange"
              ref="startPicker"
              v-model="tradeTimeRange"
              fast-time="today"
              @change="getTimeRange('tradeTime', $event)"
            >
            </date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="search(true)"
            >查询
            </el-button>
            <el-button @click="exportOrderItem">导出</el-button>
            <el-button
              type="text"
              @click="getExportList"
            >查看导出列表
            </el-button>
            <el-button
              type="text"
              @click="clearField"
            >清空搜索条件
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-tabs
        v-model="active"
        @tab-click="search(true)"
      >
        <el-tab-pane
          label="订单明细"
          name="1"
        >
          <div
            class="func-container"
            v-permission="'fee:salesFeeOrder:sum'"
          >
            <el-button
              type="text"
              @click="() => {showResult = !showResult}"
            >
              统计查询结果
              <i
                class="el-icon-instance el-icon-arrow-down"
                :class="{'icon-up' : showResult}"
              ></i>
            </el-button>
            <div v-if="showResult">
              <el-tag type="info">
                合计 {{ totalResult.totalNum || 0 }} 笔，交易金额共计 {{ totalResult.totalOrderAmount || 0 }}，销售毛利合计 {{ totalResult.totalSalesProfit || 0 }} 元，代理商分润合计 {{ totalResult.totalAgentProfit || 0 }} 元。
              </el-tag>
            </div>
          </div>
          <el-table :data="orderList">
            <el-table-column
              label="序号"
              type="index"
              width="50"
              :index="getIndex"
            ></el-table-column>
            <el-table-column
              label="创建时间/交易完成时间"
              width="170"
            >
              <template v-slot:header>
                <span>创建时间/<br />交易完成时间</span>
              </template>
              <template v-slot="{row}">
                {{ row.createTime }}<br>
                {{ row.tradeTime }}
              </template>
            </el-table-column>
            <el-table-column
              label="商户订单号/交易流水号"
              width="180"
            >
              <template v-slot="{row}">
                <span>{{ row.mchOrderNo }}</span><br>
                <span>{{ row.platTrxNo }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="商户编号/名称"
              width="130"
            >
              <template v-slot="{row}">
                <span>{{ row.mchNo }}</span><br>
                <span>{{ row.mchName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="产品供应商"
              width="150"
            >
              <template v-slot="{row}">
                <span>{{ row.vendorNo }}</span><br>
                <span>{{ row.vendorName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="产品编号/名称"
              width="130"
            >
              <template v-slot="{row}">
                <span>{{ row.productNo }}</span><br>
                <span>{{ row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="部门名称/销售员">
              <template v-slot="{row}">
                <span>{{ row.departmentName }}</span><br>
                <span>{{ row.salerName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="费率基数（毛利）"
              prop="calculateFormula"
              width="200"
            ></el-table-column>
            <el-table-column
              label="交易金额"
              prop="orderAmount"
              width="150"
            >
              <template v-slot="{row}">
                <p class="">
                  {{ row.orderAmount | moneyFormat }}
                </p>
              </template>
            </el-table-column>
            <el-table-column
              label="销售成本"
              prop="salesFee"
              width="150"
            >
              <template v-slot="{row}">
                <p class="">
                  {{ row.salesFee | moneyFormat }}
                </p>
              </template>
            </el-table-column>
            <el-table-column
              label="销售毛利"
              prop="salesProfit"
              width="150"
            >
              <template v-slot="{row}">
                <p class="">
                  {{ row.salesProfit | moneyFormat }}
                </p>
              </template>
            </el-table-column>
            <el-table-column
              label="手续费"
              prop="orderFee"
              width="150"
            >
              <template v-slot="{row}">
                <p class="">
                  {{ row.orderFee | moneyFormat }}
                </p>
              </template>
            </el-table-column>
            <el-table-column
              label="是否有合伙人"
              prop="hasAgent"
              width="120"
            >
              <template v-slot="{row}">
                {{ row.existAgent ? '是' : (row.existAgent === null ? '' : '否') }}
              </template>
            </el-table-column>
            <el-table-column
              label="一级合伙人分润"
              prop="agentProfit"
              width="150"
            >
              <template v-slot="{row}">
                <p class="">
                  {{ row.agentProfit | moneyFormat }}
                </p>
              </template>
            </el-table-column>
            <el-table-column
              label="二级合伙人分润"
              prop="agentSecondProfit"
              width="150"
            >
              <template v-slot="{row}">
                <p class="">
                  {{ row.agentSecondProfit | moneyFormat }}
                </p>
              </template>
            </el-table-column>
            <el-table-column
              label="订单类型"
              prop="orderType"
            >
              <template v-slot="{row}">
                {{ $dictCode('OrderTypeEnum', row.orderType).desc }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane
          label="合并汇总"
          name="2"
        >
          <div
            class="func-container"
            v-permission="'fee:salesFeeOrder:sum'"
          >
            <el-button
              type="text"
              @click="() => {showResult = !showResult}"
            >
              统计查询结果
              <i
                class="el-icon-instance el-icon-arrow-down"
                :class="{'icon-up' : showResult}"
              ></i>
            </el-button>
            <div v-if="showResult">
              <el-tag type="info">
                合计 {{ totalResult.totalNum || 0 }} 笔，交易金额共计 {{ totalResult.totalOrderAmount || 0 }}，销售毛利合计 {{ totalResult.totalSalesProfit || 0 }} 元，代理商分润合计 {{ totalResult.totalAgentProfit || 0 }} 元。
              </el-tag>
            </div>
          </div>
          <el-table
            :data="summaryList"
            @sort-change="onSummarySortChange"
          >
            <el-table-column
              label="序号"
              type="index"
              width="50"
              :index="getIndex"
            ></el-table-column>
            <el-table-column
              label="商户编号/名称"
              width="150"
            >
              <template v-slot="{row}">
                {{ row.employerNo }}<br>{{ row.employerName }}
              </template>
            </el-table-column>
            <el-table-column
              label="产品供应商"
              width="150"
              prop="mainstayName"
            >
              <template v-slot="{row}">
                {{ row.mainstayNo }}<br>{{ row.mainstayName }}
              </template>
            </el-table-column>
            <el-table-column
              label="产品编号/名称"
              width="150"
            >
              <template v-slot="{row}">
                {{ row.productNo }}<br>{{ row.productName }}
              </template>
            </el-table-column>
            <el-table-column
              label="部门名称/销售员"
              width="150"
              prop="salerName"
            >
              <template v-slot="{row}">
                <div>
                  {{ row.departmentName }}<br>{{ row.salerName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="交易金额合计"
              sortable="custom"
              width="150"
              prop="totalNetAmount"
            >
              <template v-slot="{row}">
                <div class="">
                  {{ row.totalNetAmount | moneyFormat }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="销售成本合计"
              width="150"
              prop="totalSalesFee"
            >
              <template v-slot="{row}">
                <div class="">
                  {{ row.totalSalesFee | moneyFormat }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="销售毛利合计"
              sortable="custom"
              width="150"
              prop="salesProfit"
            >
              <template v-slot="{row}">
                <div class="">
                  {{ row.salesProfit | moneyFormat }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="手续费合计"
              width="150"
              prop="totalMerchantFee"
            >
              <template v-slot="{row}">
                <div class="">
                  {{ row.totalMerchantFee | moneyFormat }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <div class="pagination-container">
        <el-pagination
          :total="pageInfo.totalRecord"
          :current-page="pageInfo.pageCurrent"
          :page-size="pageInfo.pageSize"
          :page-sizes="[10, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
    <ExportRecord ref="exportRecord"></ExportRecord>
  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import { getSalesFeeOrderList, exportSalesFeeOrder, sumSalesFeeOrder, exportSalerStatistics } from '@/api/feeOrder'
import { getSales } from '@/api/system'
import { salerstatistics } from '@/api/data'

export default {
  name: 'PmsSalesFeeOrder',
  components: {
    ExportRecord
  },
  data() {
    return {
      searchForm: { // 搜索表单
        mchNo: '',
        vendor: '',
        platTrxNo: '',
        productNo: '',
        tradeTimeBegin: '',
        tradeTimeEnd: '',
        createTimeBegin: '',
        createTimeEnd: '',
        mchNameLike: '',
        existAgent: '',
      },
      orderList: [],
      orderPage: {
        totalRecord: 0,
        pageSize: 10,
        pageCurrent: 1,
      },
      summaryList: [],
      summaryPage: {
        totalRecord: 0,
        pageSize: 10,
        pageCurrent: 1,
      },
      mainstayList: [],
      staffList: [],
      tradeTimeRange: [],
      createTimeRange: [],

      showResult: false,
      totalResult: {},

      // pane
      active: '1',
    }
  },
  computed: {
    pageInfo() {
      return this.active == 1 ? this.orderPage : this.summaryPage
    },
  },
  mounted() {
    if (sessionStorage.getItem('tradeTime')) {
      this.tradeTimeRange[0] = sessionStorage.getItem('tradeTime')
    }
    getSales().then(
      (response) => {
        this.staffList = response.data;
      }
    );
    this.search();
  },
  methods: {
    async search(initFlag) {
      let page = this.active == 1 ? this.orderPage : this.summaryPage;
      if (initFlag === true) {
        page.pageCurrent = 1;
      }
      await this.$nextTick();
      if (this.active == 1) {
        const { data } = await getSalesFeeOrderList({
          ...this.searchForm,
          pageSize: this.pageInfo.pageSize,
          pageCurrent: this.pageInfo.pageCurrent
        })
        page.totalRecord = data.totalRecord;
        this.orderList = data.data;
      } else {
        const { data } = await salerstatistics({
          "salerIds": this.searchForm.salerId ? [
            this.searchForm.salerId
          ] : [],
          "mchNos": this.searchForm.mchNo ? [
            this.searchForm.mchNo
          ] : [],
          "employerName": this.searchForm.mchNameLike,
          "mainstayNo": this.searchForm.vendorNo,
          "agentExists": this.searchForm.existAgent,
          "tradeTimeBegin": this.searchForm.tradeTimeBegin,
          "tradeTimeEnd": this.searchForm.tradeTimeEnd,
          "sortColumns": this.sortColumns,
          size: this.pageInfo.pageSize,
          current: this.pageInfo.pageCurrent,
        })
        page.totalRecord = data.totalRecord;
        this.summaryList = data.data;
      }
      this.hadPermission('fee:salesFeeOrder:sum') && this.checkResult();
    },
    clearField() {
      this.searchForm = {
        mchNo: '',
        vendor: '',
        platTrxNo: '',
        productNo: '',
        tradeTimeBegin: '',
        tradeTimeEnd: '',
        createTimeBegin: '',
        createTimeEnd: '',
        mchNameLike: '',
        existAgent: '',
      }
      this.$refs.startPicker.resetTime();
      this.$refs.endPicker.clearTime();
    },
    getTimeRange(type, val) {
      this.searchForm[type + 'Begin'] = val && val[0];
      this.searchForm[type + 'End'] = val && val[1];
    },
    getIndex(index) {
      return (this.pageInfo.pageCurrent - 1) * this.pageInfo.pageSize + index + 1;
    },
    handleSizeChange(val) {
      let page = this.active == 1 ? this.orderPage : this.summaryPage;
      page.pageSize = val;
      this.search(true);
    },
    handleCurrentChange(val) {
      let page = this.active == 1 ? this.orderPage : this.summaryPage;
      page.pageCurrent = val;
      this.search();
    },
    async exportOrderItem() {
      const api = this.active == 1 ? exportSalesFeeOrder : exportSalerStatistics
      const { data } = await api(this.searchForm);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(
        this.active == 1 ? this.$dictFlag('ReportTypeEnum', 'SALES_FEE_ORDER').code : '61');
    },
    async checkResult() {
      const { data } = await sumSalesFeeOrder(this.searchForm);
      this.$set(this, 'totalResult', data);
    },
    onSummarySortChange({ column, prop, order }) {
      this.sortColumns = prop + ' ' + (order.replace('ending', ''));
      this.search(true);
    }
  },
}
</script>

<style
  lang="scss"
  scoped>
.box-container {
  .el-icon-instance {
    transition: transform 0.2s linear;
  }

  .icon-up {
    transform: rotate(180deg);
  }

  .func-container {
    margin: 0;
  }
}
</style>
