<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">用工企业编号：</span>
            <el-input v-model="searchForm.mchNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">用工企业名称：</span>
            <el-input
              v-model="searchForm.mchNameLike"
              placeholder="模糊查询"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">供应商编号：</span>
            <el-input v-model="searchForm.vendorNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">供应商名称：</span>
            <el-input v-model="searchForm.vendorNameLike"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">产品编号：</span>
            <el-select
              clearable
              v-model="searchForm.productNo">
              <el-option
                v-for="(item, index) in $dict('FeeOrderProductNoEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code">
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">交易流水号：</span>
            <el-input v-model="searchForm.platTrxNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">是否有合伙人：</span>
            <el-select
              v-model="searchForm.existAgent"
              clearable>
              <el-option
                :value="true"
                label="是"></el-option>
              <el-option
                :value="false"
                label="否"></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              ref="endPicker"
              type="datetimerange"
              v-model="createTimeRange"
              @change="getTimeRange('createTime', $event)">
            </date-picker>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">完成时间：</span>
            <date-picker
              v-model="tradeTimeRange"
              type="datetimerange"
              ref="startPicker"
              fast-time="today"
              @change="getTimeRange('tradeTime', $event)">
            </date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="search(true)">查询
            </el-button>
            <el-button @click="exportOrderItem">导出</el-button>
            <el-button
              type="text"
              @click="getExportList">查看导出列表
            </el-button>
            <el-button
              type="text"
              @click="clearField">清空搜索条件
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <el-tabs
      v-model="activeType"
      @tab-click="onTabChange">
      <el-tab-pane
        v-for="(item, index) in tabList"
        :key="index"
        :name="index + ''"
        :label="item"
      ></el-tab-pane>
    </el-tabs>

    <div
      class="func-container"
      v-permission="'fee:vendorFeeOrder:sum'">
      <el-button
        type="text"
        @click="() => {showResult = !showResult}">
        统计查询结果
        <i
          class="el-icon-instance el-icon-arrow-down"
          :class="{'icon-up' : showResult}"></i>
      </el-button>
      <div v-if="showResult">
        <el-tag type="info">
          合计 {{ totalResult.totalNum || 0 }} 笔，
          商户计费 {{ totalResult.totalOrderFee }} 元，
          手续费 {{ totalResult.totalFee || 0 }} 元，
          交易金额共计 {{ totalResult.totalOrderAmount || 0 }} 元，
          供应商返佣 {{ totalResult.totalRebateFee || 0 }} 元。
        </el-tag>
      </div>
    </div>

    <div class="content-container">
      <el-table
        :data="response.data"
        :key="activeType">
        <el-table-column
          label="序号"
          type="index"
          width="50"
          :index="getIndex"></el-table-column>
        <el-table-column
          width="180"
          v-if="activeType == 0">
          <template v-slot:header>
            <span>创建时间/<br />交易完成时间</span>
          </template>
          <template v-slot="{row}">
            {{ row.createTime }}<br>
            {{ row.tradeTime }}
          </template>
        </el-table-column>
        <el-table-column
          label="商户订单号/交易流水号"
          v-if="activeType == 0"
          width="180">
          <template v-slot="{row}">
            <span>{{ row.mchOrderNo }}</span><br>
            <span>{{ row.platTrxNo }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="activeType == 0"
          label="渠道订单号/渠道流水号"
          width="180">
          <template v-slot="{row}">
            <span>{{ row.channelOrderNo }}</span><br>
            <span>{{ row.channelTrxNo }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="商户编号/名称"
          width="130">
          <template v-slot="{row}">
            <span>{{ row.mchNo }}</span><br>
            <span>{{ row.mchName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          :label="activeType == 0 ? '产品供应商' : '代征主体编号/名称'"
          width="150">
          <template v-slot="{row}">
            <span>{{ row.vendorNo }}</span><br>
            <span>{{ row.vendorName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="产品编号/名称"
          width="130">
          <template v-slot="{row}">
            <span>{{ row.productNo }}</span><br>
            <span>{{ row.productName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          v-if="activeType == 0"
          label="费率基数"
          prop="calculateFormula"
          width="130"></el-table-column>
        <el-table-column
          label="交易金额"
          prop="orderAmount"
          width="180">
          <template v-slot="{row}">
            <p class="">
              {{ (row.orderAmount || row.totalNetAmount) | moneyFormat }}
            </p>
          </template>
        </el-table-column>
        <el-table-column
          label="商户计费"
          prop="orderFee"
          width="180">
          <template v-slot="{row}">
            <p class="">
              {{ row.orderFee | moneyFormat }}
            </p>
          </template>
        </el-table-column>
        <el-table-column
          label="供应商成本"
          width="180">
          <template v-slot="{row}">
            <p class="">
              {{ (row.vendorFee || row.totalVendorFee) | moneyFormat }}
            </p>
          </template>
        </el-table-column>
        <el-table-column
          label="供应商返佣"
          width="150">
          <template v-slot="{row}">
            <div class="">
              {{ (row.orderFee * 100 - (row.vendorFee || row.totalVendorFee) * 100) / 100 | moneyFormat }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="activeType == 0"
          label="是否有合伙人"
          prop="existAgent"
          width="120">
          <template v-slot="{row}">
            {{ row.existAgent ? '是' : '否' }}
          </template>
        </el-table-column>

        <el-table-column
          label="订单类型"
          v-if="activeType == 0"
          prop="orderType">
          <template v-slot="{row}">
            {{ $dictCode('OrderTypeEnum', row.orderType).desc }}
          </template>
        </el-table-column>
      </el-table>


      <div class="pagination-container">
        <el-pagination
          v-if="response"
          :total="response.totalRecord"
          :current-page.sync="pageCurrent"
          :page-size.sync="pageSize"
          :page-sizes="[10, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="search(true)"
          @current-change="search()"></el-pagination>
      </div>
    </div>
    <ExportRecord ref="exportRecord"></ExportRecord>
  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import {
  getVendorFeeOrderList,
  exportVendorFeeOrder,
  sumVendorFeeOrder,
  vendorStatistics,
  exportVendorStatistics
} from '@/api/feeOrder'

export default {
  name: 'PmsVendorFeeOrder',
  components: {
    ExportRecord
  },
  data() {
    return {
      searchForm: { // 搜索表单
        mchNo: '',
        vendor: '',
        platTrxNo: '',
        productNo: '',
        tradeTimeBegin: '',
        tradeTimeEnd: '',
        createTimeBegin: '',
        createTimeEnd: '',
        mchNameLike: '',
        existAgent: null,
      },
      response: { // 查询结果
        data: [],
        totalRecord: 0,
      },
      pageSize: 10,
      pageCurrent: 1,

      mainstayList: [],

      tradeTimeRange: [],
      createTimeRange: [],
      showResult: false, // 是否显示统计结果
      totalResult: {},

      activeType: 0,
      tabList: ['订单明细', '合并汇总']
    }
  },
  mounted() {
    if (sessionStorage.getItem('tradeTime')) {
      this.tradeTimeRange[0] = sessionStorage.getItem('tradeTime')
    }
    this.search()
  },
  methods: {
    async search(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      const api = this.activeType == 0 ? getVendorFeeOrderList : vendorStatistics
      const form = this.activeType == 0 ? {
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent
      } : { ...this.searchForm, pageCurrent: this.pageCurrent, pageSize: this.pageSize }
      const [{ data }] = await Promise.all([
        api(form),
        this.checkResult(),
      ])
      if (this.activeType == 1) {
        // data.data = data.data || data.records
        // data.totalRecord = data.total

        // delete data.records
        // delete data.total
      }
      this.response = data;
    },
    clearField() {
      this.searchForm = {
        mchNo: '',
        vendor: '',
        platTrxNo: '',
        productNo: '',
        tradeTimeBegin: '',
        tradeTimeEnd: '',
        createTimeBegin: '',
        createTimeEnd: '',
        mchNameLike: '',
        existAgent: null,
      }
      this.$refs.startPicker.resetTime();
      this.$refs.endPicker.clearTime();
    },
    getTimeRange(type, val) {
      this.searchForm[type + 'Begin'] = val && val[0];
      this.searchForm[type + 'End'] = val && val[1];
      // this.search(true)
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    async exportOrderItem() {
      const api = this.activeType == 0 ? exportVendorFeeOrder : exportVendorStatistics
      const { data } = await api(this.searchForm);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(
        this.activeType == 0 ? this.$dictFlag('ReportTypeEnum', 'VENDOR_FEE_ORDER').code : '63');
    },
    async checkResult() {
      if (this.hadPermission('fee:vendorFeeOrder:sum')) {
        const { data } = await sumVendorFeeOrder(this.searchForm);
        this.$set(this, 'totalResult', data);
      }
    },
    onTabChange() {
      this.response.data = []
      this.search(true)
      this.showResult = false
    }
  },
}
</script>

<style
  lang="scss"
  scoped>
.box-container {
  .func-container {
    margin: 0;
  }

  .el-icon-instance {
    transition: transform .2s linear;
  }

  .icon-up {
    transform: rotate(180deg);
  }
}
</style>
