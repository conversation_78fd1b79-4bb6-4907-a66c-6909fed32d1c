<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">平台流水号：</span>
          <el-input v-model="searchForm.platTrxNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业编号：</span>
          <el-input v-model="searchForm.mchNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业名称：</span>
          <el-input
            v-model="searchForm.mchNameLike"
            placeholder="模糊查询"></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">合伙人名称：</span>
          <el-input v-model="searchForm.agentNameLike"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">合伙人编号：</span>
          <el-input v-model="searchForm.agentNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">产品：</span>
          <el-select
            v-model="searchForm.productNo"
            clearable
          >
            <el-option
              v-for="item in productList"
              :key="item.id"
              :label="item.productName"
              :value="item.productNo"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">奖励类型：</span>
          <el-select
            v-model="searchForm.rewardType"
            clearable
          >
            <el-option
              v-for="item in $dict('RewardTypeEnum')"
              :key="item.code"
              :label="item.desc"
              :value="item.code"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            ref="createDatePicker"
            type="datetimerange"
            :start-time.sync="searchForm.createTimeBegin"
            :end-time.sync="searchForm.createTimeEnd"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">交易时间：</span>
          <date-picker
            ref="datepicker"
            type="datetimerange"
            fast-time="today"
            :start-time.sync="searchForm.tradeTimeBegin"
            :end-time.sync="searchForm.tradeTimeEnd"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询
        </el-button>
        <el-button @click="exportList">
          导出
        </el-button>
        <el-button
          type="text"
          @click="getExportList">
          查看已导出列表
        </el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件
        </el-button>
      </div>
    </div>

    <el-tabs
      v-model="activeType"
      @tab-click="onTabChange">
      <el-tab-pane
        v-for="(item, index) in tabList"
        :key="index"
        :name="index + ''"
        :label="item"
      ></el-tab-pane>
    </el-tabs>

    <div
      class="func-container"
      v-permission="'fee:agentFeeOrder:sum'">
      <el-button
        type="text"
        @click="() => {showResult = !showResult}">
        统计查询结果
        <i
          class="el-icon-instance el-icon-arrow-down"
          :class="{'icon-up' : showResult}"></i>
      </el-button>
      <div v-if="showResult">
        <el-tag type="info">
          合计 {{ totalResult.totalCount || 0 }} 笔， 合伙人收益 {{ totalResult.totalProfit || 0 }} 元，交易金额共计 {{ totalResult.totalNetAmount || 0 }} 元， 合伙人成本 {{ totalResult.totalAgentCost || 0 }} 元。
        </el-tag>
      </div>
    </div>
    <div class="content-container">
      <el-table
        :data="list"
        :key="activeType">
        <el-table-column
          type="index"
          :index="getIndex"
          label="序号"></el-table-column>
        <el-table-column
          v-if="activeType == 0"
          label="创建时间"
          prop="createTime"
          width="120">
          <p
            slot-scope="{row, column}"
            v-html="renderTime(row[column['property']])"></p>
        </el-table-column>
        <el-table-column
          v-if="activeType == 0"
          label="交易时间"
          prop="tradeTime"
          width="120">
          <p
            slot-scope="{row, column}"
            v-html="renderTime(row[column['property']])"></p>
        </el-table-column>
        <el-table-column
          v-if="activeType == 0"
          label="交易流水号"
          prop="platTrxNo"
          width="180"></el-table-column>

        <el-table-column
          label="商户编号/名称"
          prop="mchNo"
          width="150">
          <template v-slot="{row}">
            {{ row.mchNo }}<br>
            {{ row.mchName }}
          </template>
        </el-table-column>
        <el-table-column
          label="代征主体编号/名称"
          prop="vendorNo"
          width="150">
          <template v-slot="{row}">
            {{ row.vendorNo }}<br>
            {{ row.vendorName }}
          </template>
        </el-table-column>

        <el-table-column
          label="合伙人编号/名称"
          prop="agentNo"
          width="150">
          <template v-slot="{row}">
            {{ row.agentNo }}<br>
            {{ row.agentName }}
          </template>
        </el-table-column>
        <el-table-column
          label="所属产品"
          prop="productNo"
          width="80">
          <template v-slot="{row}">
            {{ row.productNo }}<br>
            {{ row.productName }}
          </template>
        </el-table-column>
        <el-table-column label="分润比例">
          <template v-slot="{row}">
            {{ row.realProfitRatio }}%
          </template>
        </el-table-column>

        <el-table-column
          width="150"
          label="奖励类型"
          prop="rewardType">
          <template v-slot="{row}">
            {{ $dictCode('RewardTypeEnum', row.rewardType).desc }}
            <span v-if="row.agentFeeRate"> / {{ row.agentFeeRate }}</span>
          </template>
        </el-table-column>

        <el-table-column
          width="180"
          label="交易金额"
          prop="orderAmount">
          <template v-slot="{row}">
            <p class="">
              {{ (row.orderAmount || row.totalNetAmount) | moneyFormat }}
            </p>
          </template>
        </el-table-column>

        <el-table-column
          label="商户手续费"
          prop="orderFee"
          width="120">
          <template v-slot="{row}">
            <p class="">
              {{ (row.orderFee || row.totalOrderFee) | moneyFormat }}
            </p>
          </template>
        </el-table-column>
        <el-table-column
          label="合伙人成本"
          prop="agentCost"
          width="120">
          <template v-slot="{row}">
            <p class="">
              {{ row.agentCost | moneyFormat }}
            </p>
          </template>
        </el-table-column>
        <el-table-column
          label="合伙人收益"
          prop="agentProfit"
          width="150">
          <template v-slot="{row}">
            <p class="">
              {{ row.agentProfit | moneyFormat }}
            </p>
          </template>
        </el-table-column>

        <el-table-column
          v-if="activeType == 0"
          label="费率基数"
          width="240"
          prop="calculateFormula">
        </el-table-column>

        <el-table-column
          label="订单类型"
          v-if="activeType == 0">
          <template v-slot="{row}">
            <el-tag :type="row.orderType != 1 ? 'danger' : 'info'">
              {{ $dictCode('OrderTypeEnum', row.orderType).desc }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </el-footer>
    <ExportRecord ref="exportRecord"></ExportRecord>
  </div>
</template>

<script>
import {
  agentFeeOrderList,
  agentStatistics,
  exportAgentStatistics,
  exportList,
  getAgentOrderFeeSum
} from '@/api/agent';
import { getProductList } from '@/api/product';
import ExportRecord from '@/components/ExportRecord'
import dayjs from 'dayjs'

export default {
  components: {
    ExportRecord,
  },
  name: 'PmsAgentOrder',
  data() {
    return {
      showResult: false,
      searchForm: {
        platTrxNo: '',
        mchNo: '',
        mchNameLike: '',
        agentNameLike: '',
        agentNo: '',
        productNo: '',
        rewardType: '',
        tradeTimeBegin: '',
        tradeTimeEnd: '',
        createTimeBegin: '',
        createTimeEnd: '',
      },

      pageSize: 10,
      pageCurrent: 1,
      totalRecord: 0,

      list: [],

      productList: [],

      totalResult: {},
      activeType: 0,
      tabList: ['订单明细', '合并汇总']
    }
  },
  mounted() {
    getProductList({ pageCurrent: 1, pageSize: 200 }).then(({ data }) => {
      this.productList = data.data
    });
    if (this.$route.query.agentNo) {
      this.searchForm.agentNo = this.$route.query.agentNo
    }
    if (this.$route.query.agentName) {
      this.searchForm.agentNameLike = this.$route.query.agentName
    }
    if (this.$route.query.date) {
      let start = dayjs(this.$route.query.date).startOf('month').hour(0).minute(0).second(0).format('YYYY-MM-DD HH:mm:ss');
      let end = dayjs(this.$route.query.date).endOf('month').hour(23).minute(59).second(59).format('YYYY-MM-DD HH:mm:ss');
      this.$refs.datepicker.changeTime([start, end])
    }
    this.search();
  },
  methods: {
    async search(init) {
      if (init) {
        this.pageCurrent = 1;
      }
      const api = this.activeType == 0 ? agentFeeOrderList : agentStatistics
      const form = this.activeType == 0 ? {
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      } : { ...this.searchForm, pageSize: this.pageSize, pageCurrent: this.pageCurrent }
      const { data } = await api(form)
      this.list = data.data
      this.totalRecord = data.totalRecord
      this.hadPermission('fee:agentFeeOrder:sum') && this.checkResult();
    },
    resetForm() {
      this.searchForm = {
        platTrxNo: '',
        mchNo: '',
        mchNameLike: '',
        agentNameLike: '',
        agentNo: '',
        productNo: '',
        rewardType: '',
        tradeTimeBegin: '',
        tradeTimeEnd: '',
        createTimeBegin: '',
        createTimeEnd: '',
      };
      this.$refs.datepicker.resetTime();
      this.$refs.createDatePicker.clearTime();
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    async exportList() {
      const api = this.activeType == 0 ? exportList : exportAgentStatistics
      const { data } = await api(this.searchForm);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(this.activeType == 0 ? '16' : '62');
    },
    async checkResult() {
      const { data } = await getAgentOrderFeeSum(this.searchForm);
      this.$set(this, 'totalResult', data);
    },
    onTabChange() {
      this.search(true)
      this.showResult = false
    }
  },
}
</script>

<style
  scoped
  lang="scss">
.box-container {
  .func-container {
    margin: 0;
  }

  .icon-up {
    transform: rotate(180deg);
  }
}
</style>
