<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">用工企业编号：</span>
            <el-input v-model="searchForm.mchNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">用工企业名称：</span>
            <el-input
              v-model="searchForm.mchNameLike"
              placeholder="模糊查询"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">供应商编号：</span>
            <el-input v-model="searchForm.vendorNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">供应商名称：</span>
            <el-input v-model="searchForm.vendorNameLike"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">产品编号：</span>
            <el-select
              clearable
              v-model="searchForm.productNo">
              <el-option
                v-for="(item, index) in $dict('FeeOrderProductNoEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code">
              </el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">交易流水号：</span>
            <el-input v-model="searchForm.platTrxNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">是否有合伙人：</span>
            <el-select
              v-model="searchForm.existAgent"
              clearable>
              <el-option
                :value="true"
                label="是"></el-option>
              <el-option
                :value="false"
                label="否"></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              ref="endPicker"
              type="datetimerange"
              v-model="createTimeRange"
              @change="getTimeRange('createTime', $event)">
            </date-picker>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">完成时间：</span>
            <date-picker
              type="datetimerange"
              ref="startPicker"
              v-model="tradeTimeRange"
              fast-time="today"
              @change="getTimeRange('tradeTime', $event)">
            </date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="search(true)">查询
            </el-button>
            <el-button @click="exportOrderItem">导出</el-button>
            <el-button
              type="text"
              @click="getExportList">查看导出列表
            </el-button>
            <el-button
              type="text"
              @click="clearField">清空搜索条件
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="content-container">
      <el-tabs v-model="activeTab" @tab-click="search(true)">
        <el-tab-pane label="订单明细" name="detail">
          <div class="func-container" v-permission="'fee:merchantFeeOrder:sum'">
            <el-button type="text" @click="() => {showResult = !showResult}">统计查询结果
              <i class="el-icon-instance el-icon-arrow-down" :class="{'icon-up' : showResult}"></i>
            </el-button>
            <div v-if="showResult">
              <el-tag type="info">
                合计 {{ totalResult.totalNum || 0 }} 笔， 手续费 {{ totalResult.totalFee || 0 }} 元，交易金额共计 {{ totalResult.totalOrderAmount || 0 }} 元。
              </el-tag>
            </div>
          </div>
          <el-table :data="response.data">
            <el-table-column
              label="序号"
              type="index"
              width="50"
              :index="getIndex"></el-table-column>
            <el-table-column
              label="创建时间/交易完成时间"
              width="180">
              <template v-slot="{row}">
                {{ row.createTime }}<br>
                {{ row.tradeTime }}
              </template>
            </el-table-column>
            <el-table-column
              label="商户订单号/交易流水号"
              width="180">
              <template v-slot="{row}">
                <span>{{ row.mchOrderNo }}</span><br>
                <span>{{ row.platTrxNo }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="商户编号/名称"
              width="130">
              <template v-slot="{row}">
                <span>{{ row.mchNo }}</span><br>
                <span>{{ row.mchName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="所属部门/销售">
              <template v-slot="{row}">
                <span>{{ row.departmentName }}</span><br><span>{{ row.salerName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品供应商">
              <template v-slot="{row}">
                <span>{{ row.vendorNo }}</span><br>
                <span>{{ row.vendorName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品编号/名称">
              <template v-slot="{row}">
                <span>{{ row.productNo }}</span><br>
                <span>{{ row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="费率基数"
              prop="calculateFormula"
              width="170"></el-table-column>
            <el-table-column
              label="交易金额"
              prop="orderAmount"
              width="150">
              <template v-slot="{row}">
                <p class="">
                  {{ row.orderAmount | moneyFormat }}
                </p>
              </template>
            </el-table-column>
            <el-table-column
              label="手续费"
              prop="orderFee"
              width="150">
              <template v-slot="{row}">
                <p class="">
                  {{ row.orderFee | moneyFormat }}
                </p>
              </template>
            </el-table-column>

            <el-table-column
              label="是否有合伙人"
              prop="existAgent"
              width="100">
              <template v-slot="{row}">
                {{ row.existAgent ? '是' : '否' }}
              </template>
            </el-table-column>

            <el-table-column
              label="订单类型"
              prop="orderType">
              <template v-slot="{row}">
                {{ $dictCode('OrderTypeEnum', row.orderType).desc }}
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="合并汇总" name="statistic">
          <div class="func-container" v-permission="'fee:merchantFeeOrder:sum'">
            <el-button type="text" @click="() => {showResult = !showResult}">统计查询结果
              <i class="el-icon-instance el-icon-arrow-down" :class="{'icon-up' : showResult}"></i>
            </el-button>
            <div v-if="showResult">
              <el-tag type="info">
                合计 {{ totalResult.totalNum || 0 }} 笔， 手续费 {{ totalResult.totalFee || 0 }} 元，交易金额共计 {{ totalResult.totalOrderAmount || 0 }} 元。
              </el-tag>
            </div>
          </div>
          <el-table :data="response.data">
            <el-table-column
              label="序号"
              type="index"
              width="50"
              :index="getIndex">
            </el-table-column>
            <el-table-column
              label="商户编号/名称"
              width="130">
              <template v-slot="{row}">
                <span>{{ row.mchNo }}</span><br>
                <span>{{ row.mchName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="供应商编号/名称">
              <template v-slot="{row}">
                <span>{{ row.vendorNo }}</span><br>
                <span>{{ row.vendorName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="产品编号/名称">
              <template v-slot="{row}">
                <span>{{ row.productNo }}</span><br>
                <span>{{ row.productName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="交易金额"
              prop="totalNetAmount"
              width="130">
              <template v-slot="{row}">
                <span>{{ row.totalNetAmount | moneyFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="商户计费"
              prop="totalOrderFee"
              width="130">
              <template v-slot="{row}">
                <span>{{ row.totalOrderFee | moneyFormat }}</span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <div class="pagination-container">
        <el-pagination
          v-if="response"
          :total="response.totalRecord"
          :current-page="pageCurrent"
          :page-size="pageSize"
          :page-sizes="[10, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"></el-pagination>
      </div>
    </div>
    <ExportRecord ref="exportRecord"></ExportRecord>
  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import {
  getMerchantFeeOrderList,
  exportMerchantFeeOrder,
  sumMerchantFeeOrder,
  merchantStatistics,
  exportMerchantStatistics,
} from '@/api/feeOrder'

export default {
  name: 'PmsMerchantFeeOrder',
  components: {
    ExportRecord
  },
  data() {
    return {
      searchForm: { // 搜索表单
        mchNo: '',
        mchNameLike: '',
        vendor: '',
        platTrxNo: '',
        productNo: '',
        tradeTimeBegin: '',
        tradeTimeEnd: '',
        createTimeBegin: '',
        createTimeEnd: '',
        existAgent: null,
      },
      response: { // 查询结果
        data: [],
        totalRecord: 0,
      },
      pageSize: 10,
      pageCurrent: 1,
      activeTab: 'detail',
      mainstayList: [],

      tradeTimeRange: [],
      createTimeRange: [],
      showResult: false, // 是否显示统计结果
      totalResult: {},

    }
  },
  mounted() {
    if (sessionStorage.getItem('tradeTime')) {
      this.tradeTimeRange[0] = sessionStorage.getItem('tradeTime')
    }
    this.search();
  },
  methods: {
    async search(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }

      const apiMethod = this.activeTab === 'detail' ? getMerchantFeeOrderList : merchantStatistics;
      const { data } = await apiMethod({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent
      })
      this.response = data;
      this.hadPermission('fee:merchantFeeOrder:sum') && this.checkResult();
    },
    clearField() {
      this.createTimeRange = [];
      this.searchForm = {
        mchNo: '',
        vendor: '',
        platTrxNo: '',
        productNo: '',
        tradeTimeBegin: '',
        tradeTimeEnd: '',
        createTimeBegin: '',
        createTimeEnd: '',
        mchNameLike: '',
        existAgent: null,
      }
      this.$refs.startPicker.resetTime();
      this.$refs.endPicker.clearTime();
    },
    getTimeRange(type, val) {
      this.searchForm[type + 'Begin'] = val && val[0];
      this.searchForm[type + 'End'] = val && val[1];
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    async exportOrderItem() {
      const apiMethod = this.activeTab === 'detail'? exportMerchantFeeOrder : exportMerchantStatistics;
      const { data } = await apiMethod(this.searchForm)
      this.$message.success(data);
    },
    getExportList() {
      const enumName = this.activeTab === 'detail' ? 'MERCHANT_FEE_ORDER' : 'PMS_MERCHANT_MERGE_FEE'
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(this.$dictFlag('ReportTypeEnum', enumName).code);
    },
    async checkResult() {
      const { data } = await sumMerchantFeeOrder(this.searchForm);
      this.$set(this, 'totalResult', data);
    }
  },
}
</script>

<style lang="scss" scoped>
.box-container {
  .func-container {
    //padding-top: 16px;
    //padding-left: 16px;
    margin: 0;
  }

  .el-icon-instance {
    transition: transform .2s linear;
  }

  .icon-up {
    transform: rotate(180deg);
  }
}
</style>
