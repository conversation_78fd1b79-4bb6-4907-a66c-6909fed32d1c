<template>
  <div class="bpmn-container">
    <vue-bpmn-designer
      v-model="xml"
      @init-finish="initModeler"
      @element-click="elementClick"
      @element-hover="elementHover"
      @element-out="elementOut"
    ></vue-bpmn-designer>

    <vue-bpmn-panel
      :bpmn-modeler="bpmnModeler"
    ></vue-bpmn-panel>

    <div id="popper" ref="popper" v-show="popperShow">
      {{ popperContent }}
    </div>
  </div>
</template>

<script>
  import BpmnDesigner from '@/components/Bpmn/designer'
  import BpmnPanel from '@/components/Bpmn/panel'
  import { createPopper } from '@popperjs/core';

  export default {
    name: 'BpmnPage',
    components: {
      'vue-bpmn-designer': BpmnDesigner,
      'vue-bpmn-panel': BpmnPanel,
    },
    data() {
      return {
        xml: '',

        bpmnModeler: null,
        element: null, //点击选中元素

        popperShow: false,
        popperContent: '',
      }
    },
    mounted() {
    },
    methods: {
      initModeler(modeler) {
        this.bpmnModeler = modeler;
        this.elementRegistry = this.bpmnModeler.get("elementRegistry");
      },
      elementClick(element) {
        this.element = element;
      },
      elementHover(element, event) {
        if (element.type === 'bpmn:UserTask') {
          this.element = element;
          const assignee = element.businessObject.get('assignee');
          if (assignee && assignee.length > 0) {
            this.popperContent = assignee + ''
          } else {
            this.popperContent = '该任务暂无指定审批人'
          }
          const svgElement = event.gfx;
          this.popperShow = true;
          this.popperInstance = createPopper(svgElement, this.$refs.popper, {
            placement: 'top'
          })
        }
      },
      elementOut(element) {
        if (element.type == 'bpmn:UserTask') {
          this.popperShow = false;
        }
      }
    },
  }
</script>

<style scoped lang="scss">

  .bpmn-container {
    display: inline-grid;
    grid-template-columns: auto max-content;
    width: 100%;
    height: 100vh;
    overflow: hidden;

    #popper {
      padding: 8px;
      border-radius: 8px;
      background: #333;
      color: #fff;
      font-weight: bold;
      font-size: 13px;
    }
  }
</style>
