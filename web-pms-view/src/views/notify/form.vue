<template>
  <div class="page-container">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      label-width="200px"
      :disabled="isView"
    >
      <el-form-item
        label="通知类型："
        prop="notificationType"
      >
        <el-select
          clearable
          v-model="form.notificationType"
        >
          <el-option
            v-for="(item, index) in $dict('NotificationTypeEnum')"
            :key="index"
            :label="item.desc"
            :value="Number(item.code)"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="通知标题："
        prop="notificationTitle"
      >
        <el-input
          v-model="form.notificationTitle"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item
        label="通知内容："
        prop="notificationContent"
      >
        <div
          v-html="form.notificationContent"
          v-if="isView"
          style="width: 600px; max-height: 400px; overflow: auto;"
        ></div>
        <tinymce
          v-else
          v-model="form.notificationContent"
          :width="600"
          :height="400"
          :menubar="false"
        ></tinymce>
      </el-form-item>
      <el-form-item
        label="接收人："
        prop="notificationReceiverType"
      >
        <el-radio-group
          clearable
          v-model="form.notificationReceiverType"
        >
          <el-radio
            v-for="(item, index) in $dict('NotificationReceiverTypeEnum')"
            :key="index"
            :label="Number(item.code)"
          >{{ item.desc }}</el-radio>
        </el-radio-group>
        <div v-if="form.notificationReceiverType == 400">
          <el-select
            v-model="form.notificationReceivers"
            clearable
            filterable
            multiple
          >
            <el-option
              v-for="(item, index) in receivers"
              :key="index"
              :label="item.mchName"
              :value="item.mchNo"
            ></el-option>
          </el-select>
        </div>
        <div v-if="form.notificationReceiverType == 600">
          <el-select
            v-model="form.notificationReceivers"
            clearable
            filterable
            multiple
          >
            <el-option
              v-for="(item, index) in receiverAgents"
              :key="index"
              :label="item.agentName"
              :value="item.agentNo"
            ></el-option>
          </el-select>
        </div>
      </el-form-item>
      <el-form-item
        label="推送规则："
        prop="publishType"
      >
        <el-radio-group
          clearable
          v-model="form.publishType"
        >
          <el-radio
            v-for="(item, index) in $dict('PublishTypeEnum')"
            :key="index"
            :label="Number(item.code)"
          >{{ item.desc }}</el-radio>
        </el-radio-group>
        <div v-if="form.publishType == 200">
          <el-date-picker
            type="datetime"
            v-model="form.pushTime"
            default-time="00:00:00"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </div>
      </el-form-item>
      <el-form-item
        label="是否弹窗："
        prop="pop"
      >
        <el-radio-group
          clearable
          v-model="form.pop"
        >
          <el-radio
            v-for="(item, index) in $dict('TrueFalseEnum')"
            :key="index"
            :label="item.code"
          >{{ item.desc }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div
      class="footer-btn-box"
      v-if="!isView"
    >
      <el-button
        type="primary"
        @click="submit"
      >发布</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script>
  import { addNotify, getNotify } from '@/api/notify'
  import {getActiveAgents} from '@/api/agent'
  import { getAllMerchant } from '@/api/merchant';
  import Tinymce from '@/components/Tinymce'

  export default {
    name: "notifyForm",
    components: {
      Tinymce,
    },
    data(vm) {
      return {
        form: {},
        rules: {
          notificationType: [
            { required: true, message: '请选择通知类型', trigger: 'change' },
          ],
          notificationTitle: [
            { required: true, message: '请输入通知标题', trigger: 'blur' },
          ],
          notificationContent: [
            { required: true, message: '请输入通知内容', trigger: 'blur' },
          ],
          notificationReceiverType: [
            { required: true, message: '请选择接收人', trigger: 'blur' },
            {
              validator(rule, val, cb) {
                if (val == 400 && !vm.form.notificationReceivers.length) {
                  cb(new Error('请指定接收人'));
                } else {
                  cb();
                }
              }, trigger: 'blur'
            },
          ],
          pop:[{ required: true, message: '请选择是否弹窗', trigger: 'blur' }],
          publishType: [
            { required: true, message: '请选择推送规则', trigger: 'change' },
            {
              validator(rule, val, cb) {
                if (val == 200 && !vm.form.pushTime) {
                  cb(new Error('请选择推送时间'));
                } else {
                  cb();
                }
              }, trigger: 'blur'
            },
          ],
        },
        receivers: [],
        receiverAgents:[],
        loading: true,
      }
    },
    computed: {
      isView() {
        return this.$route.query.type == 'view'
      },
      id() {
        return this.$route.query.id || null
      }
    },
    mounted() {
      this.getMchAndSupplier()
      this.getAgents()
      if (this.id) {
        this.getNotify()
      }
    },
    methods: {
      async getMchAndSupplier() {
        const [{ data: mch }, { data: supplier }] = await Promise.all([
          getAllMerchant({ merchantType: '100' }),
          getAllMerchant({ merchantType: '101' }),
        ])
        this.receivers = [...mch, ...supplier]
      },
      async getAgents() {
        const {data} = await getActiveAgents()
        this.receiverAgents=data
      },
      cancel() {
        this.$router.push('/notify/list')
      },
      async submit() {
        this.loading = true
        try {
          await this.$refs.form.validate()

          const { data } = await addNotify({
            ...this.form,
            notificationReceivers: JSON.stringify(this.form.notificationReceivers),
          })
          data && this.$message.success(data)
          this.cancel()
        } finally {
          this.loading = false
        }
      },
      async getNotify() {
        const { data } = await getNotify({ id: this.id })
        this.form = data
        this.form.pop = data.pop + "";
        this.form.notificationReceivers = JSON.parse(data.notificationReceivers);
      },
    }
  }
</script>

<style lang="scss" scoped>
  .page-container {
    margin-bottom: 100px;
  }
  .footer-btn-box {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #fff;
    padding: 20px;
    text-align: right;

    .el-button {
      margin-left: 20px;
    }
  }
</style>
