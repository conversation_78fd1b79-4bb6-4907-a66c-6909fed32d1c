<template>
  <div class="box-container">
    <el-button
      class="create-btn"
      type="primary"
      @click="create"
    >新建</el-button>
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">通知类型：</span>
          <el-select
            clearable
            v-model="form.notificationType"
          >
            <el-option
              v-for="(item, index) in $dict('NotificationTypeEnum')"
              :key="index"
              :label="item.desc"
              :value="item.code"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">接收人：</span>
          <el-select
            v-model="form.notificationReceivers"
            clearable
            filterable
          >
            <el-option
              v-for="(item, index) in receivers"
              :key="index"
              :label="item.mchName"
              :value="item.mchNo"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">推送规则：</span>
          <el-select
            v-model="form.publishType"
            clearable
            filterable
          >
            <el-option
              v-for="(item, index) in $dict('PublishTypeEnum')"
              :key="index"
              :label="item.desc"
              :value="item.code"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">通知标题：</span>
          <el-input
            v-model="form.notificationTitle"
            clearable
            placeholder="支持模糊搜索"
          ></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">推送时间：</span>
          <date-picker
            ref="datepicker"
            type="datetime"
            picker="separate"
            :start-time.sync="form.pushBeginDate"
            :end-time.sync="form.pushEndDate"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search"
        >查询</el-button>
        <el-button
          type="text"
          @click="reset"
        >重置</el-button>
      </div>
    </div>
    <div class="content-container">
      <el-table :data="list">
        <el-table-column
          label="序号"
          type="index"
          :index="getIndex"
        ></el-table-column>
        <el-table-column
          label="通知类型"
          prop="notificationType"
        >
          <template v-slot="{row}">
            {{ $dictCode('NotificationTypeEnum', row.notificationType).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="通知标题"
          prop="notificationTitle"
        >
        </el-table-column>
        <el-table-column
          label="推送规则"
          prop="publishType"
        >
          <template v-slot="{row}">
            {{ $dictCode('PublishTypeEnum', row.publishType).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="接收人"
          prop="notificationReceiverType"
        >
          <template v-slot="{row}">
            {{ $dictCode('NotificationReceiverTypeEnum', row.notificationReceiverType).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="推送时间"
          prop="pushTime"
        >
          <template v-slot="{row}">
            <p v-html="renderTime(row.pushTime)"></p>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="handleEdit(row)"
            >详情</el-button>
            <el-button
              type="text"
              style="color: red;"
              @click="handleDelete(row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="page.total"
        :current-page.sync="page.current"
        :page-size.sync="page.size"
        :page-sizes="[10, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>

  </div>
</template>

<script>
  import { getAllMerchant } from '@/api/merchant';

  import { getNotifyList, delNotify } from '@/api/notify'
  export default {
    name: 'notifyList',
    data() {
      return {
        list: [],
        page: {
          current: 1,
          size: 10,
          total: 0,
        },
        form: {},
        receivers: [],
      }
    },
    mounted() {
      this.getMchAndSupplier()
      this.search()
    },
    methods: {
      async search(init) {
        if (init) {
          this.page.current = 1
        }
        const { data: { records, total } } = await getNotifyList({
          ...this.page,
          ...this.form,
        })
        this.page.total = total
        this.list = records
      },
      reset() {
        this.form = {}
        this.$refs.datepicker.clearTime()
      },
      async getMchAndSupplier() {
        const [{ data: mch }, { data: supplier }] = await Promise.all([
          getAllMerchant({ merchantType: '100' }),
          getAllMerchant({ merchantType: '101' }),
        ])
        this.receivers = [...mch, ...supplier]
      },
      getIndex(index) {
        return index + 1 + (this.page.current - 1) * this.page.size
      },
      handleEdit({ id }) {
        this.$router.push({
          path: '/notify/form',
          query: { id, type: 'view' },
        })
      },
      async handleDelete(row) {
        try {
          await this.$confirm('确定删除该通知吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
        } catch (e) {
          return
        }
        const { data } = await delNotify({ ids: row.id })
        data && this.$message.success(data)
        this.search()
      },
      create() {
        this.$router.push({
          path: '/notify/form',
        })
      }
    }
  }
</script>

<style>
</style>