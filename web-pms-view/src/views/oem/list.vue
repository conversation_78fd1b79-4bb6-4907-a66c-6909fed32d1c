<template>
  <div class="box-container">
    <el-button
      type="primary"
      class="create-btn"
      @click="onEdit()">新建配置
    </el-button>
    <div class="flex-container search-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">租户名称：</span>
          <el-input
            v-model="searchForm.tenantName"
            clearable></el-input>
        </div>
        <div class="flex-item">
          <el-button
            type="primary"
            @click="search(true)">查询
          </el-button>
          <el-button @click="resetField">重置</el-button>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table
        :data="list">
        <el-table-column
          label="租户名称"
          width="180">
          <template v-slot="{row}">
            {{ row.tenantNo }}<br>{{ row.tenantName }}
          </template>
        </el-table-column>
        <el-table-column
          label="网站名称："
          width="180"
          prop="webSiteName"></el-table-column>
        <el-table-column
          label="平台生成域名"
          width="250">
          <template v-slot="{row}">
            <el-link
              :href="'https://' + row.link[0].website"
              target="_blank">
              {{ row.link[0].website }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column
          label="租户私有域名"
          width="250">
          <template v-slot="{row}">
            <el-link
              v-for="(link, index) in row.link.slice(1)"
              :key="index"
              :href="'https://' + link.website"
              target="_blank">
              {{ link.website }}
            </el-link>
          </template>
        </el-table-column>
        <el-table-column label="域名状态">
          <template v-slot="{row}">
            <el-switch
              v-model="row.status"
              :active-value="100"
              :inactive-value="101"
              @change="changeStatus($event, row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column
          width="180"
          label="更新时间"
          prop="updateTime"></el-table-column>
        <el-table-column
          label="合作开始时间"
          width="180"
          prop="startTime"></el-table-column>
        <el-table-column
          label="合作结束时间"
          width="180"
          prop="endTime"></el-table-column>
        <el-table-column
          label="操作"
          width="150"
          fixed="right">
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="onEdit(row)">编辑
            </el-button>
            <el-button
              type="text"
              class="red-btn"
              @click="onDelete(row)">删除配置
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="page.totalRecord"
        :current-page.sync="page.pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="page.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </el-footer>

    <oem-config-dialog
      ref="dialog"
      @change="search()"></oem-config-dialog>
  </div>
</template>

<script>
import OemConfigDialog from "@/views/oem/Component/OemConfigDialog.vue";
import { toPromise } from "@/utils";
import { changeOemStatus, deleteOem, listOem } from "@/api/oem";

export default {
  name: 'OemList',
  components: { OemConfigDialog },
  data() {
    return {
      searchForm: {},
      list: [],
      page: {
        totalRecord: 0,
        pageCurrent: 1,
        pageSize: 10
      },
      editRow: {}
    }
  },
  mounted() {
    this.search()
  },
  methods: {
    async search(init) {
      if (init) {
        this.page.pageCurrent = 1
      }
      const { data: { data, totalRecord } } = await listOem({
        ...this.searchForm,
        ...this.page
      })
      this.list = data
      this.page.totalRecord = totalRecord
    },
    resetField() {
      this.searchForm = {}
    },
    async onStatusChange() {
    },
    onEdit(row) {
      this.$refs.dialog.open(row ? { ...row } : {})
    },
    async onDelete({ id }) {
      const [err] = await toPromise(this.$confirm('该操作将删除该条数据，是否继续？', '提示', {
        type: 'warning'
      }))
      if (err) return
      const { data } = await deleteOem(id)
      data && this.$message.success(data)
      this.search()
    },
    async changeStatus(status, { id }) {
      const { data } = await changeOemStatus({ id, status })
      data && this.$message.success(data)
      this.search()
    }
  }
}
</script>

<style
  lang="scss"
  scoped>

.content-container {
  ::v-deep .el-link {
    display: block;
    margin-left: 0;
  }
}

</style>
