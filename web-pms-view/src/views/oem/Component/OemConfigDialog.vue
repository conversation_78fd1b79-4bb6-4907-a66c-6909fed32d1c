<template>
  <el-dialog
    :visible.sync="visible"
    :title="(form.id ? '修改' : '新建') + '配置'"
    :before-close="close"
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="150px">
      <el-form-item
        label="请选择用户："
        prop="tenantNo">
        <el-select
          v-model="form.tenantNo"
          :disabled="!!form.id"
          filterable
          clearable
          @change="onMainstayChange">
          <el-option
            v-for="item in mchList"
            :key="item.mchNo"
            :label="item.mchName"
            :value="item.mchNo"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="合作开始时间："
        prop="startTime">
        <el-date-picker
          v-model="form.startTime"
          type="datetime"
          placeholder="选择日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          default-time="00:00:00"
        ></el-date-picker>
      </el-form-item>
      <el-form-item
        label="合作截止时间："
        prop="endTime">
        <el-date-picker
          v-model="form.endTime"
          type="datetime"
          placeholder="选择日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          default-time="23:59:59"
        ></el-date-picker>
      </el-form-item>
      <el-form-item
        label="网站名称："
        prop="webSiteName">
        <el-input
          v-model="form.webSiteName"
          clearable></el-input>
      </el-form-item>
      <el-form-item
        v-if="form.link"
        label="平台生成域名："
        prop="link">
        <el-input
          clearable
          v-model="form.link[0].website">
          <template v-slot:prepend>{{ oemUrl }}</template>
        </el-input>
      </el-form-item>
      <el-button
        class="add-btn"
        @click="addWebsite">+ 新增租户私有域名
      </el-button>
      <template v-if="form.privateLink">
        <div
          v-for="(item, index) in form.privateLink"
          :key="item.id"
        >
          <el-form-item
            :label="'租户私有域名'+ (index+1) + '：'">
            <div class="input-item">
              <el-input
                v-model="item.website"
                clearable>
                <template v-slot:prepend>https://</template>
              </el-input>
              <el-button
                type="info"
                @click="() => deleteItem(index)">删除
              </el-button>
            </div>
          </el-form-item>
          <el-form-item
            label="备案信息："
            prop="privateLink">
            <el-input
              v-model="item.icpNo"
              :rows="4"
              type="textarea"></el-input>
          </el-form-item>
        </div>
      </template>
    </el-form>

    <template v-slot:footer>
      <el-button @click="close">取消</el-button>
      <el-button
        type="primary"
        @click="confirm">确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { getAllMerchant } from "@/api/merchant";
import { toPromise } from "@/utils";
import { addOem, updateOem } from "@/api/oem";

let linkId = 0 // 前端标识
export default {
  name: 'OemConfigDialog',
  data() {
    return {
      visible: false,
      form: {},
      rules: {
        tenantNo: [{ required: true, message: '请选择租户', trigger: 'blur' }],
        webSiteName: [{ required: true, message: '请填写网站名称', trigger: 'true' }],
        link: [
          { required: true, message: '请填写域名', trigger: 'blur' },
          {
            validator(rule, val, cb) {
              if (val[0].website) {
                cb()
              } else {
                cb(new Error('请填写域名'))
              }
            }, trigger: 'blur'
          }
        ],
        privateLink: [
          {
            validator(rule, val, cb) {
              for (let i = 0; i < val.length; i++) {
                if (!val[i].icpNo) {
                  return cb(new Error('请填写备案信息'))
                }
              }
              cb()
            }, trigger: 'blur'
          }
        ],
        startTime: [{ required: true, message: '请选择时间', trigger: 'blur' }],
        endTime: [{ required: true, message: '请选择时间', trigger: 'blur' }],
      },
      mchList: [],
    }
  },
  methods: {
    async getMchList() {
      if (this.mchList.length) return
      const { data } = await getAllMerchant({ merchantType: '101' })
      this.mchList = data
    },
    open(data) {
      this.getMchList()
      const form = JSON.parse(JSON.stringify(data))
      this.form = form || {}
      this.init()
      this.toggle(true)
    },
    init() {
      if (!this.form.id) {
        this.$set(this.form, 'link', [{ website: '', siteType: 100 }])
        this.$set(this.form, 'privateLink', [])
      } else {
        let host = this.oemUrl.replace(/^https?:\/\//, '')
        this.$set(this.form, 'privateLink', this.form.link.slice(1))
        this.form.link = this.form.link.slice(0, 1)
        this.form.link[0].website = this.form.link[0].website.replace(host, '')
      }
    },
    close() {
      this.$refs.form.resetFields()
      this.toggle()
    },
    toggle(status = false) {
      this.visible = status
    },
    handleForm(form) {
      // 平台域名带回平台前缀
      form.link[0].website = (this.oemUrl + form.link[0].website).replace(/^https?:\/\//, '')
      form.link = [form.link[0], ...form.privateLink]
      form.link.forEach(item => {
        delete item.id
        if (!item.website.endsWith('/')) {
          // 手动补上斜杠
          item.website += '/'
        }
      })
      delete form.privateLink
    },
    async confirm() {
      const [err] = await toPromise(this.$refs.form.validate())
      if (err) return
      const form = JSON.parse(JSON.stringify(this.form))
      this.handleForm(form)
      await (this.form.id ? updateOem(form) : addOem(form))
      this.$emit('change')
      this.close()
    },
    deleteItem(index) {
      this.form.privateLink.splice(index, 1)
    },
    onMainstayChange(val) {
      const res = this.mchList.find(item => item.mchNo === val)
      if (res) {
        this.form.tenantName = res.mchName
      }
    },
    addWebsite() {
      this.form.privateLink.push({ website: '', siteType: 101, icpNo: '' })
    }
  }
}
</script>

<style
  lang="scss"
  scoped>
.add-btn {
  display: block;
  margin: 0 auto 10px;
}

.input-item {
  display: flex;
}
</style>
