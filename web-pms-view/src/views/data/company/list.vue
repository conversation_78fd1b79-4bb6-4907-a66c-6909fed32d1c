<template>
  <div class="box-container">
    <warning-block>数据次日生成，当前数据时间截至 {{ lastTime }}</warning-block>
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">商户编号：</span>
          <el-input
            clearable
            v-model="form.employerNo"
          ></el-input>
        </div>
        <div
          class="flex-item"
          style="max-width: none;"
        >
          <span class="flex-item__label">用工企业品牌：</span>
          <el-select
            v-model="form._branch"
            clearable
            placeholder="请选择品牌"
            @change="onBranchChange"
          >
            <el-option
              v-for="(item, index) in branchList"
              :key="index"
              :label="item"
              :value="index"
            ></el-option>
          </el-select>
          <el-select
            v-model="form.employerList"
            clearable
            multiple
            filterable
            collapse-tags
            placeholder="请选择商户"
          >
            <el-option
              v-for="(item, index) in selectMerchant"
              :key="index"
              :label="item.mchName"
              :value="item.mchNo"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">代征主体：</span>
          <el-input
            clearable
            v-model="form.mainstayName"
          ></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">销售：</span>
          <el-select
            clearable
            v-model="form.saleId"
          >
            <el-option
              v-for="item in sale"
              :key="item.id"
              :label="item.realName"
              :value="item.id"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">商户名称：</span>
          <el-input
            clearable
            v-model="form.employerName"
            placeholder="模糊搜索商户名称"
          ></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">账单月份：</span>
          <date-picker
            :start-time.sync="form.beginDate"
            :end-time.sync="form.endDate"
            :is-show-fast-time="false"
            :auto-fix="false"
            ref="datePicker"
            picker="separate"
            type="month"
          >
          </date-picker>
        </div>
      </div>

      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询
        </el-button>
        <el-button @click="exportList">导出</el-button>
        <el-button
          type="text"
          @click="getExportList"
        >查看已导出列表
        </el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件
        </el-button>
      </div>
    </div>

    <div class="func-container">
      <el-button
        type="text"
        @click="() => {showResult = !showResult}"
      >
        统计查询结果
        <i
          class="el-icon-instance"
          :class="{'el-icon-arrow-up' : showResult, 'el-icon-arrow-down': !showResult}"
        ></i>
      </el-button>
      <div v-if="showResult">
        <div>
          商户
          <span style="color: #409EFF;">{{ countData.employerCount || 0 }}</span>
          个，发单数量
          <span style="color: #409EFF;">{{ countData.orderCount || 0 }}</span>
          个，自由职业者
          <span style="color: #409EFF;">{{ countData.freelanceCount || 0 }}</span>
          个；
        </div>
        <div>
          实发金额汇总
          <span style="color: #FBB84E">{{ countData.orderItemNetAmountSum || 0 }}</span>
          元，订单金额
          <span style="color: #FBB84E">{{ countData.orderAmountSum || 0 }}</span>
          元。
        </div>
      </div>

    </div>

    <div class="content-container">
      <el-table
        :data="list"
        @sort-change="onSortChange"
      >
        <el-table-column label="商户名称">
          <template v-slot="{row}">
            <div
              class="func-content"
              @click="goOrderDetail(row)"
            >
              {{ row.employerName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="商用时间"
          prop="firstOrderTime"
        >
          <template v-slot="{row, column}">
            <p v-html="renderTime(row[column['property']])"></p>
          </template>
        </el-table-column>
        <el-table-column
          label="销售"
          prop="saleName"
        ></el-table-column>
        <el-table-column
          label="代征主体"
          prop="mainstayName"
        ></el-table-column>
        <el-table-column
          label="月份"
          prop="createDate"
        ></el-table-column>
        <el-table-column
          label="订单金额"
          prop="orderItemAmount"
          sortable="custom"
        >
          <template v-slot="{row}">
            <div class="text-right">
              ￥{{ row.orderItemAmount }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="实发金额"
          prop="orderItemNetAmount"
          sortable="custom"
        >
          <template v-slot="{row}">
            <div class="text-right">
              ￥{{ row.orderItemNetAmount }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="发单数量"
          align="center"
          prop="orderAmount"
          sortable="custom"
        >
          <template v-slot="{row}">
            <div
              class="func-content"
              @click="goOrderDetail(row)"
            >
              {{ row.orderAmount }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="发单人数"
          align="center"
          prop="receiverNumber"
          sortable="custom"
        >
          <template v-slot="{row}">
            <div
              class="func-content"
              @click="goOrderDetail(row)"
            >
              {{ row.receiverNumber }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
        >
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="checkHistoryData(row)"
              v-if="hadPermission('data:history:view')"
            >历史数据分析
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </div>

    <!--导出dialog-->
    <export-record ref="exportRecord"></export-record>

  </div>
</template>

<script>
import dayjs from 'dayjs'
import ExportRecord from '@/components/ExportRecord/index'
import { getSales } from '@/api/system';
import { analyzeMerchant, countMerchant, merchantInfoExport } from '@/api/data';
import { listBranchMerchant } from '@/api/merchant'

export default {
  name: 'company-data',
  components: {
    ExportRecord,
  },
  data() {
    return {
      form: {
        employerList: [],
      },

      totalRecord: 0,
      pageCurrent: 1,
      pageSize: 10,

      list: [],
      countData: {},
      showResult: false,
      sortObj: {
        order: '',
        sortColumn: '',
      },

      sale: [],

      lastTime: dayjs().subtract(1, 'day').endOf('day').endOf('hour').format('YYYY-MM-DD HH:mm:ss'),
      branchList: [], // 品牌列表
      branchMap: [],

    }
  },
  computed: {
    selectMerchant() {
      // 提供选择的商户
      let merchant = []
      if (this.branchMap[this.form._branch]) {
        merchant = this.branchMap[this.form._branch].merchant
      }
      return merchant
    }
  },
  async mounted() {
    const beginDate = dayjs().format('YYYY-MM');
    const endDate = dayjs().format('YYYY-MM');
    this.$refs.datePicker.changeTime([beginDate, endDate]);
    this.getSales();
    this.listBranchMerchant();
    this.search(true);
  },
  methods: {
    async search(init, opts) {
      if (init === true) {
        this.pageCurrent = 1;
      }
      if ((this.form._branch !== '' && this.form._branch !== undefined) && this.form.employerList.length == 0) {
        // 如果选择了品牌，但是没有选择商户，则全选
        this.form.employerList = this.branchMap[this.form._branch].merchant.map(item => item.mchNo)
      }
      const [{ data: { data: list, totalRecord } }, { data: countData }] = await Promise.all([
        analyzeMerchant({
          ...this.form,
          ...this.sortObj,
          pageCurrent: this.pageCurrent,
          pageSize: this.pageSize,
        }),
        countMerchant({
          ...this.form,
        })
      ])

      this.list = list;
      this.totalRecord = totalRecord;
      this.countData = countData;
    },
    async getSales() {
      const { data } = await getSales()
      this.sale = data
    },
    resetForm() {
      this.form = {};
      this.$refs.datePicker.clearTime();
    },
    async exportList() {
      await merchantInfoExport({
        ...this.form
      });
      this.$message.success('导出成功，请到导出列表查看')
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('33');
    },
    goOrderDetail(row) {
      this.$router.push({
        path: '/trade/order/detail',
        query: {
          employerName: row.employerName,
          createBeginDate: dayjs(row.createDate).subtract(1, 'month').startOf('month').format('YYYY-MM-DD') + ' 00:00:00',
          createEndDate: dayjs(row.createDate).endOf('month').format('YYYY-MM-DD') + ' 23:59:59',
          completeBeginDate: dayjs(row.createDate).startOf('month').format('YYYY-MM-DD') + ' 00:00:00',
          completeEndDate: dayjs(row.createDate).endOf('month').format('YYYY-MM-DD') + ' 23:59:59',
          mainstayNo: row.mainstayNo,
          employerNo: row.employerNo,
          orderItemStatusList: ['100'],
        }
      })
    },
    onSortChange({ prop, order }) {
      this.sortObj = {
        order: order.replace('ending', ''),
        sortColumn: prop
      };
      this.search()
    },
    checkHistoryData(data) {
      this.$router.push({
        path: '/data/previewSelf/month',
        query: {
          dimensionType: 1,
          dimensionMessages: data.employerNo
        }
      })
    },
    async listBranchMerchant() {
      const { data } = await listBranchMerchant()
      data.forEach(item => {
        this.branchList.push(item.branchName)
      })
      this.branchMap = data
    },
    onBranchChange() {
      this.form.employerList = []
    },
  }
}
</script>

<style
  scoped
  lang="scss">
</style>
