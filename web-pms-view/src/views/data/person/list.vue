<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">姓名：</span>
          <el-input
            clearable
            v-model="form.receiveName"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">证件号码：</span>
          <el-input
            clearable
            v-model="form.idCard"
          ></el-input>
        </div>
        <div
          class="flex-item"
          style="max-width: none;"
        >
          <span class="flex-item__label">用工企业品牌：</span>
          <el-select
            v-model="form._branch"
            clearable
            placeholder="请选择品牌"
            @change="onBranchChange"
          >
            <el-option
              v-for="(item, index) in branchList"
              :key="index"
              :label="item"
              :value="index"
            ></el-option>
          </el-select>
          <el-select
            v-model="form.employerList"
            clearable
            multiple
            filterable
            collapse-tags
            placeholder="请选择商户"
          >
            <el-option
              v-for="(item, index) in selectMerchant"
              :key="index"
              :label="item.mchName"
              :value="item.mchNo"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">代征主体：</span>
          <el-input
            clearable
            v-model="form.mainstayName"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">商户名称：</span>
          <el-input
            clearable
            v-model="form.employerName"
            placeholder="模糊搜索商户名称"
          ></el-input>
        </div>
        <div class="flex-item-main">
          <span class="flex-item__label">账单月份：</span>
          <date-picker
            :start-time.sync="form.beginDate"
            :end-time.sync="form.endDate"
            :is-show-fast-time="false"
            :auto-fix="false"
            ref="datePicker"
            picker="separate"
            type="month"
          >
          </date-picker>
        </div>
      </div>

      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询
        </el-button>
        <el-button @click="exportList">导出</el-button>
        <el-button
          type="text"
          @click="getExportList"
        >查看已导出列表
        </el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件
        </el-button>
      </div>
    </div>

    <div class="func-container">
      <el-button
        type="primary"
        size="small"
        :disabled="selection.length == 0"
        @click="multiSign"
      >批量签约
      </el-button>
      <el-button
        type="text"
        @click="showResult = !showResult"
      >
        统计查询结果
        <i
          class="el-icon-instance"
          :class="{
            'el-icon-arrow-up': showResult,
            'el-icon-arrow-down': !showResult,
          }"
        ></i>
      </el-button>
      <div v-if="showResult">
        <div>
          自由职业者
          <span style="color: #409eff">{{ countData.freelanceCount || 0 }}</span>
          个，接单数量
          <span style="color: #409eff">{{ countData.orderCount || 0 }}</span>
          个；
        </div>
        <div>
          实发金额汇总
          <span style="color: #fbb84e">{{ countData.orderItemNetAmountSum || 0 }}</span>
          元。
        </div>
      </div>
    </div>

    <div class="content-container">
      <el-table
        :data="list"
        @sort-change="onSortChange"
        @filter-change="onFilterChange"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          :selectable="checkSelectAble"
        >
        </el-table-column>
        <el-table-column
          label="姓名"
          prop="receiveName"
        ></el-table-column>
        <el-table-column
          label="证件号码"
          prop="receiveIdCardNo"
          width="180"
        >
        </el-table-column>
        <el-table-column
          label="手机号"
          prop="receivePhoneNo"
          width="180"></el-table-column>
        <el-table-column
          label="月份"
          prop="createDate"
        ></el-table-column>
        <el-table-column
          label="用工企业"
          prop="employer"
          width="180"
        >
          <template v-slot="{ row }">
            <div
              class="func-content"
              @click="goOrderDetail(row, { employerNameLike: row.employerName })"
            >
              {{ row.employerName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="代征主体">
          <template v-slot="{ row }">
            <div
              class="func-content"
              @click="goOrderDetail(row, { mainstayNo: row.mainstayNo })"
            >
              {{ row.mainstayName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="条件接单数量 / 平台总接单数量"
          width="250"
        >
          <template v-slot="{ row }">
            <span
              class="func-content"
              @click="goOrderDetail(row, {
                  employerNameLike: row.employerName,
                  mainstayNo: row.mainstayNo,
                  receiveIdCardNo: row.receiveIdCardNo,
                })"
            >
              {{ row.conditionOrder }} /
            </span>
            <span
              class="func-content"
              @click="
                goOrderDetail(row, { receiveIdCardNo: row.receiveIdCardNo })
              "
            >
              {{ row.receiverOrder }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="实发金额"
          sortable="custom"
          width="120"
          prop="orderItemNetAmount"
        >
          <template v-slot="{ row }">
            <div class="text-right">￥{{ row.orderItemNetAmount }}</div>
          </template>
        </el-table-column>
        <el-table-column
          width="100"
          label="电子签约"
          prop="signRecord"
          :filters="[
            { text: '是', value: 1 },
            { text: '否', value: 0 },
          ]"
          column-key="hasSign"
        >
          <!-- :filter-method="filterHandler" -->
          <template v-slot="{ row }">
            <el-tag :type="row.signRecord == 1 ? 'success' : 'danger'">
              {{ row.signRecord == 1 ? "是" : "否" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          width="150"
          label="身份证复印件"
          :filters="[
            { text: '已上传', value: 1 },
            { text: '未上传', value: 0 },
          ]"
          prop="idCard"
          column-key="hasUploadIdCard"
        >
          <!-- :filter-method="filterHandler" -->
          <template v-slot="{ row }">
            <el-tag :type="row.idCard == 1 ? 'success' : 'danger'">
              {{ row.idCard == 1 ? "已上传" : "未上传" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
        >
          <template v-slot="{ row }">
            <el-button
              type="text"
              @click="startSign(row)"
              v-if="row.signRecord !== 1"
            >发起签约
            </el-button>
            <el-button
              type="text"
              @click="uploadId(row)"
            >上传身份证
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </div>

    <!--导出dialog-->
    <export-record ref="exportRecord"></export-record>

    <upload-img
      ref="uploadDialog"
      :signItem="editRow"
      :use-data-api="true"
      :need-phone="true"
      @change="search()"
    ></upload-img>

    <sign-dialog
      ref="signDialog"
      @change="onSign"
    ></sign-dialog>
  </div>
</template>

<script>
import dayjs from "dayjs";
import ExportRecord from "@/components/ExportRecord/index";

import {
  analyzeFreelance,
  countFreelance,
  sign,
  signList,
  freelanceExport,
} from "@/api/data";
import SignDialog from "../component/SignDialog";
import UploadImg from "@/views/sign/Component/UploadImg";
import { listBranchMerchant } from '@/api/merchant'
import { toPromise } from '@/utils'

export default {
  name: "freelance-data",
  components: {
    SignDialog,
    ExportRecord,
    UploadImg,
  },
  data() {
    return {
      form: {
        employerList: [],
      },

      totalRecord: 0,
      pageCurrent: 1,
      pageSize: 10,

      list: [],
      countData: {},
      showResult: false,

      editRow: {},

      sortObj: {
        order: "",
        sortColumn: "",
      },
      branchList: [], // 品牌列表
      branchMap: [],
      selection: [],
    };
  },
  computed: {
    selectMerchant() {
      // 提供选择的商户
      let merchant = []
      if (this.branchMap[this.form._branch]) {
        merchant = this.branchMap[this.form._branch].merchant
      }
      return merchant
    }
  },
  mounted() {
    const beginDate = dayjs().format("YYYY-MM");
    const endDate = dayjs().format("YYYY-MM");
    this.$refs.datePicker.changeTime([beginDate, endDate]);
    this.listBranchMerchant()
    this.search(true);
  },
  methods: {
    async search(init, opts) {
      if (init === true) {
        this.pageCurrent = 1;
      }
      if ((this.form._branch !== undefined && this.form._branch !== '') && this.form.employerList.length == 0) {
        // 如果选择了品牌，但是没有选择商户，则全选
        this.form.employerList = this.branchMap[this.form._branch].merchant.map(item => item.mchNo)
      }

      const [
        {
          data: { data: list, totalRecord },
        },
        { data: countData },
      ] = await Promise.all([
        analyzeFreelance({
          ...this.form,
          ...this.sortObj,
          pageCurrent: this.pageCurrent,
          pageSize: this.pageSize,
        }),
        countFreelance({
          ...this.form,
        }),
      ]);
      this.list = list;
      this.totalRecord = totalRecord;
      this.countData = countData;
    },
    resetForm() {
      this.form = {};
      this.$refs.datePicker.clearTime();
    },
    async exportList() {
      await freelanceExport({
        ...this.form,
      });
      this.$message.success("导出成功，请到导出列表查看");
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord("32");
    },
    uploadId(row) {
      this.editRow = row;
      this.$refs.uploadDialog.open();
    },
    startSign(row) {
      this.editRow = row;
      this.$refs.signDialog.form = {
        receiveName: row.receiveName,
        idCard: row.receiveIdCardNo,
        phone: row.receivePhoneNo,
      }
      this.$refs.signDialog.toggle(1);

      // if (row.phone == 0) {
      //   this.$refs.signDialog.form.receiveName = row.receiveName;
      //   this.$refs.signDialog.form.idCard = row.receiveIdCardNo;
      //   this.$refs.signDialog.visible = true;
      // } else {
      //   const form = {
      //     receiveName: row.receiveName,
      //     idCard: row.receiveIdCardNo,
      //   };
      //   this.onSign(form);
      // }
    },
    async onSign(form) {
      const { type } = form
      delete form.type
      const formData = {
        ...form,
        mainstayNo: this.editRow.mainstayNo,
        employerNo: this.editRow.employerNo,
      };
      let loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      let err = null, res = null
      if (type == 1) {
        [err, res] = await toPromise(sign(formData))
        loading.close();
        if (err) return;
      } else if (type == 2) {
        [err, res] = await toPromise(signList({
          signType: form.signType,
          dataAnalyzeVos: this.selection
        }))
        loading.close();
        if (err) return;
      }
      res && this.$message.success(res.data);
      this.$refs.signDialog.form = {}
      this.$refs.signDialog.toggle()
      this.search();
    },
    goOrderDetail(row, param) {
      this.$router.push({
        path: "/trade/order/detail",
        query: {
          createBeginDate:
            dayjs(row.createDate)
              .subtract(1, "month")
              .startOf("month")
              .format("YYYY-MM-DD") + " 00:00:00",
          createEndDate:
            dayjs(row.createDate).endOf("month").format("YYYY-MM-DD") + " 23:59:59",
          completeBeginDate:
            dayjs(row.createDate).startOf("month").format("YYYY-MM-DD") + " 00:00:00",
          completeEndDate:
            dayjs(row.createDate).endOf("month").format("YYYY-MM-DD") + " 23:59:59",
          orderItemStatusList: ["100"],
          ...param,
        },
      });
    },
    onSortChange({ prop, order }) {
      this.sortObj = {
        order: order.replace("ending", ""),
        sortColumn: prop,
      };
      this.search();
    },
    onFilterChange(filters) {
      let keys = Object.keys(filters)
      keys.forEach(key => {
        this.form[key] = filters[key][0]
      })
      this.search()
    },
    async listBranchMerchant() {
      const { data } = await listBranchMerchant()
      data.forEach(item => {
        this.branchList.push(item.branchName)
      })
      this.branchMap = data
    },
    onBranchChange() {
      this.form.employerList = []
    },
    handleSelectionChange(val) {
      this.selection = val;
    },
    checkSelectAble(row, index) {
      return row.signRecord !== 1
    },
    multiSign() {
      this.$refs.signDialog.toggle(2);
    }
  },
};
</script>

<style
  scoped
  lang="scss">
</style>
