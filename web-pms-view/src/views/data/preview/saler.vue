<template>
  <div class="box-container">
    <div class="data-box-title flex-box">
      <div class="data-title flex-item">关联商户数据汇总</div>
      <div>
        选择日期：
        <date-picker
          picker="separate"
          type="datetime"
          :is-show-fast-time="false"
          :start-time.sync="form.tradeTimeBegin"
          :end-time.sync="form.tradeTimeEnd"
        ></date-picker>
        <el-button
          type="primary"
          @click="reloadData"
        >查询</el-button>
      </div>
    </div>
    <div class="data-map-list flex-box">
      <div
        class="data-item flex-item"
        v-for="(item, index) in dataMap"
        :key="index"
        @click="onItemClick(index)"
      >
        <div class="flex-box">
          <div class="data-item-title flex-item">
            {{ item.title }}
          </div>
          <div class="data-item-check">
            <el-checkbox
              v-model="item.check"
              @change="onItemClick(index)"
            ></el-checkbox>
          </div>
        </div>
        <div>
          <span class="data-item-legend">{{item.legend}}</span>
          <span class="data-item-detail">{{ item.data }}</span>
        </div>
      </div>
    </div>
    <div class="data-type-list">
      <span>统计方式：</span>
      <el-radio-group v-model="activeCountType" @change="reloadData">
        <el-radio label="month">按月统计</el-radio>
        <el-radio label="day">按日统计</el-radio>
      </el-radio-group>
    </div>
    <div class="data-echart-box">
      <div class="echart-box">
        <div
          id="echart"
          ref="echart"
        ></div>
      </div>
    </div>
    <div class="data-table-box">
      <el-table
        ref="table"
        :data="list"
        @selection-change="onSelectChange"
        @sort-change="onSortChange"
        height="350"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column
          label="商户名"
          width="170"
          prop="employerName"
        ></el-table-column>

        <el-table-column
          label="支付金额（元）"
          sortable="custom"
          prop="totalNetAmount"
        >
          <template v-slot="{row}">
            {{ row.totalNetAmount | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column
          label="订单笔数"
          prop="totalCount"
          sortable="custom"
        ></el-table-column>
        <el-table-column
          label="发放人数"
          prop="userCount"
          sortable="custom"
        ></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
  import { listSalerOrdersGroupedByMch, listSalerOrdersDaily, listSalerOrdersMonthly, salerOrdersStatistics } from '@/api/data'
  import dayjs from 'dayjs'
  import * as echarts from 'echarts';
  export default {
    name: 'salerDataPreview',
    data(vm) {
      let tradeTimeBegin = dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss')
      let tradeTimeEnd = dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss')
      if (vm.$route.query.startTime) {
        tradeTimeBegin = dayjs(vm.$route.query.startTime).startOf('day').format('YYYY-MM-DD HH:mm:ss')
      }
      if (vm.$route.query.endTime) {
        tradeTimeEnd = dayjs(vm.$route.query.endTime).endOf('day').format('YYYY-MM-DD HH:mm:ss')
      }
      return {
        active: 0,
        dataMap: [
          { check: false, title: '支付金额（元）', legend: '累计', prop: 'totalNetAmount', data: '', },
          { check: false, title: '客单价（元）', legend: '平均', prop: 'avgNetAmount', data: '', },
          { check: false, title: '订单数（笔）', legend: '累计', prop: 'totalCount', data: '', },
          { check: false, title: '下单人数（个）', legend: '累计', prop: 'userCount', data: '', },
        ],
        echart: null,
        list: [],
        activeCountType: 'month',
        selection: [],
        form: {
          tradeTimeBegin,
          tradeTimeEnd,
          sortColumns: '',
        },
        echartData: [],


        firstRender: true,
      };
    },
    computed: {
      salerId() {
        return this.$route.query.salerId;
      },
      merchantMap() {
        let map = {}
        this.selection.forEach(item => {
          map[item.employerNo] = item.employerName
        })
        return map
      },
      activeData() {
        return this.dataMap[this.active]
      },
    },
    mounted() {
      this.init()
    },
    beforeDestroy() {
      this.echart && this.echart.dispose();
    },
    methods: {
      async init() {
        this.dataMap[this.active].check = true;
        if (this.salerId) {
          await this.getData(true)
          this.initEchart()
          this.refreshEchart()

          this.firstRender = false;
        }
      },
      async getData() {
        this.salerOrdersStatistics()
        await this.listSalerOrdersGroupedByMch()
        await this.getEchartData(this.list)
      },
      async salerOrdersStatistics() {
        let { data } = await salerOrdersStatistics({
          "salerIds": [this.salerId],
          "tradeTimeBegin": this.form.tradeTimeBegin,
          "tradeTimeEnd": this.form.tradeTimeEnd,
        })
        this.dataMap.forEach(item => {
          item.data = data[item.prop]
        })
      },
      async listSalerOrdersGroupedByMch() {
        const { data } = await listSalerOrdersGroupedByMch({
          ...this.form,
          salerIds: [Number(this.salerId)],
        })
        this.list = data

        // 初始化选中前三项
        // await this.$nextTick()
        // let len = Math.min(this.list.length, 3)
        // for (let i = 0; i < len; i++) {
        //   this.$refs.table.toggleRowSelection(this.list[i], true)
        // }
      },
      async getEchartData(list) {
        let api = (this.activeCountType === 'month' ? listSalerOrdersMonthly : listSalerOrdersDaily)
        const { data } = await api({
          tradeTimeBegin: this.form.tradeTimeBegin,
          tradeTimeEnd: this.form.tradeTimeEnd,
          salerIds: [Number(this.salerId)],
          mchNos: list.map(item => item && item.employerNo).filter(item => item),
        })
        this.echartData = data
      },
      onItemClick(index) {
        this.active = index;
        this.dataMap.forEach((item, key) => {
          if (key != index) {
            item.check = false
          } else {
            item.check = true
          }
        });
        this.$nextTick(() => {
          this.refreshEchart()
        })
      },
      initEchart() {
        if (!this.echart) {
          this.echart = echarts.init(this.$refs.echart);
        }
        this.echart.setOption({
          tooltip: {
            trigger: 'axis'
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: [],
          },
          yAxis: {
            type: 'value'
          },
          series: []
        })
      },
      refreshEchart() {
        // 生成时间坐标轴
        let xAxisData = []
        let startTime = this.form.tradeTimeBegin
        let endTime = this.form.tradeTimeEnd
        let format = this.activeCountType === 'month' ? 'YYYY-MM' : 'YYYY-MM-DD'
        while (startTime <= endTime) {
          xAxisData.push(dayjs(startTime).format(format))
          startTime = dayjs(startTime).add(1, this.activeCountType).format(format)
        }
        // 构造图表数据
        let series = []
        Object.keys(this.merchantMap).map(item => {
          let merchantData = {
            name: this.merchantMap[item],
            type: 'line',
            data: new Array(xAxisData.length).fill(0)
          }
          this.echartData[item].map(data => {
            let { date } = data
            let index = xAxisData.indexOf(date)
            if (index > -1) {
              merchantData.data[index] = data[this.activeData.prop]
            }
          })
          series.push(merchantData)
        })
        this.echart.setOption({
          title: {
            text: this.dataMap[this.active].title,
          },
          legend: {
            data: this.selection.map(item => item.employerName),
            top: 0,
            right: 0,
            orient: 'vertical',
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxisData
          },
          series
        }, {
          replaceMerge: 'series'
        })
      },
      onSelectChange(val) {
        this.selection = val

        if (!this.firstRender) {
          this.$nextTick(() => {
            this.refreshEchart()
          })
        }
      },
      async onSortChange({ column, prop, order}) {
        this.form.sortColumns = prop + ' ' + ((order || '').replace('ending', ''))
        this.reloadData()
      },
      reloadData() {
        this.firstRender = true;
        this.selection = []
        this.echart.clear();
        this.init()
      },
    }
  }
</script>

<style lang="scss" scoped>
  .data-box-title {
    align-items: center;
    justify-content: space-between;
    .data-title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
  }
  .data-map-list {
    margin-top: 32px;
  }
  .data-item {
    position: relative;
    margin-right: 16px;
    padding: 16px;
    border-radius: 10px;
    max-width: 300px;
    border: 1px solid #ddd;
    cursor: pointer;
    &-title {
      margin-bottom: 10px;
      font-size: 14px;
    }
    &-detail {
      margin-left: 10px;
      font-size: 25px;
      font-weight: bolder;
    }
  }

  .data-type-list {
    margin: 16px 0;
  }

  #echart {
    height: 500px;
  }
</style>