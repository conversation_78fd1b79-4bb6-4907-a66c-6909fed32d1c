<template>
  <div class="data-chart-container">
    <div class="box-container">
      <div class="data-block">
        <p class="subTitle">月度概览</p>
        <p>统计时间跨度：{{ startTime }} - {{ endTime }}</p>
        <div class="data-list flex-box" v-if="monthData">
          <div class="data-item flex-item">
            <div class="data-item-title">本月订单数（笔）</div>
            <div class="data-item-detail">
              <span class="data-item-detail-data">
                {{ monthData.order.num }}
              </span>
              <div class="data-item-detail-diff" :class="{down: monthData.order.subtractNum < 0}">
                <span class="data-item-diff-icon" :class="{down: monthData.order.subtractNum < 0}">
                  <svg-icon icon-class="double-arrow-up"></svg-icon>
                </span>
                {{ monthData.order.subtractNum }}
              </div>
            </div>
            <div class="data-item-diff">上个自然月同期 <span>{{ monthData.order.lastNum }}</span></div>
          </div>
          <div class="data-item flex-item">
            <div class="data-item-title">本月成单商户数（个）</div>
            <div class="data-item-detail">
              <span class="data-item-detail-data">
                {{ monthData.merchant.num }}
              </span>
              <div class="data-item-detail-diff" :class="{down: monthData.merchant.subtractNum < 0}">
                <span class="data-item-diff-icon" :class="{down: monthData.merchant.subtractNum < 0}">
                  <svg-icon icon-class="double-arrow-up"></svg-icon>
                </span>
                {{ monthData.merchant.subtractNum }}
              </div>
            </div>
            <div class="data-item-diff">上个自然月同期 <span>{{ monthData.merchant.lastNum }}</span></div>
          </div>
          <div class="data-item flex-item">
            <div class="data-item-title">本月下单人数（个）</div>
            <div class="data-item-detail">
              <span class="data-item-detail-data">
                {{ monthData.user.num }}
              </span>
              <div class="data-item-detail-diff" :class="{down: monthData.user.subtractNum < 0}">
                <span class="data-item-diff-icon" :class="{down: monthData.user.subtractNum < 0}">
                  <svg-icon icon-class="double-arrow-up"></svg-icon>
                </span>
                {{ monthData.user.subtractNum }}
              </div>
            </div>
            <div class="data-item-diff">上个自然月同期 <span>{{ monthData.user.lastNum }}</span></div>
          </div>
        </div>
      </div>
      <div class="echart-block">
        <div class="echart-head-block" v-if="monthData">
          <div class="data-item no-border" style="background: transparent;">
            <div class="data-item-title">本月支付金额（元）</div>
            <div class="data-item-detail">
              <span class="data-item-detail-data">
                {{ monthData.money.money | moneyFormat('') }}
              </span>
              <div class="data-item-detail-diff" :class="{down: monthData.money.subtractMoney < 0}">
                <span class="data-item-diff-icon" :class="{down: monthData.money.subtractMoney < 0}">
                  <svg-icon icon-class="double-arrow-up"></svg-icon>
                </span>
                {{ monthData.money.subtractMoney | moneyFormat('') }}
              </div>
              <div class="data-item-diff" style="display: inline-block; margin-top: 0; vertical-align: middle;">上个自然月同期 <span>{{ monthData.money.lastMoney | moneyFormat('') }}</span></div>
            </div>
          </div>
        </div>
        <!--month-echart-->
        <div id="echart-month" class="echarts"></div>
      </div>
    </div>

    <!-- tabs -->
    <div class="box-container" style="position: sticky; padding-bottom: 0; top: 50px; z-index: 1000;">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="核心指标" name="1"></el-tab-pane>
        <el-tab-pane label="运营视图" name="2"></el-tab-pane>
      </el-tabs>
    </div>

    <!-- core and main -->
    <core v-if="activeTab == 1"></core>
    <Main v-else-if="activeTab == 2"></Main>

  </div>
</template>

<script>
  import { getMonthlyOverview } from '@/api/data';
  import * as echarts from 'echarts';
  import dayjs from 'dayjs'
  import Core from '../operate/core'
  import Main from '../operate/main'
  export default {
    name: 'data-preivew-month',
    components: {
      Core,
      Main,
    },
    data() {
      let format = 'YYYY 年 MM 月 DD 日'
      let startTime = dayjs().startOf('month');
      let endTime = dayjs().subtract(1, 'day');
      if (dayjs().date() == 1) {
        // 当月 1号，查询上个月
        startTime = startTime.subtract(1, 'month')
      }
      return {
        startTime: startTime.format(format),
        endTime: endTime.format(format),

        monthData: null,
        echart: null,

        activeTab: '1',
      }
    },
    mounted() {
      if (this.$route.path === '/data/previewSelf/month' 
          && this.hadPermission('data:history:view') 
          && !this.$route.query.dimensionMessages) {
        return this.$router.replace('/404')
      }
      this.getData()
    },
    methods: {
      async getData() {
        const { data } = await getMonthlyOverview()
        this.monthData = data
        this.setEcharts()
      },
      setEcharts() {
        if (this.echart !== null) {
          this.echart.clear()
        }
        this.echart = echarts.init(document.getElementById('echart-month'))
        this.echart.setOption({
          xAxis: {
            type: 'category',
            name: '日',
            nameLocation: 'middle',
            nameGap: 20,
            boundaryGap: false,
            data: this.monthData.xaxis,
          },
          yAxis: {
            type: 'value',
            name: '支付金额(元)',
            nameLocation: 'end',
          },
          tooltip: {
            show: true,
            trigger: 'axis',
          },
          legend: {
            bottom: 0,
            data: ['上个月', '本月']
          },
          series: [
            {
              data: this.monthData.lastMoneys,
              type: 'line',
              smooth: true,
              name: '上个月',
            },
            {
              data: this.monthData.moneys,
              type: 'line',
              smooth: true,
              name: '本月',
            },

          ]
        })
      },
    },
  }
</script>

<style scoped lang="scss">
  .data-item {
    margin-right: 16px;
    padding: 16px;
    border-radius: 10px;
    max-width: 300px;
    border: 1px solid #ddd;

    &.no-border {
      border: none;
    }

    &-title {
      font-size: 14px;
    }
    &-detail {
      margin: 16px 0 0;
    }
    &-detail-diff {
      display: inline-block;
      vertical-align: middle;
      color: #0f0;
      &.down {
        color: #f00;
      }
    }
    &-detail-data {
      margin-right: 24px;
      vertical-align: middle;
      font-size: 24px;
      font-weight: bolder;
    }

    &-diff {
      margin: 16px 0 0;
      font-size: 14px;
      color: #aaa69d;
      span {
        margin-left: 12px;
      }
    }
    &-diff-icon {
      display: inline-block;
      color: #0f0;
      &.down {
        color: #f00;
        transform: rotate(180deg);
      }
    }
  }

  .echart-block {
    margin-top: 32px;
    border: 1px solid #ddd;
  }
  .echarts {
    height: 400px;
  }
</style>
