<template>
  <div class="box-container">
    <div class="data-block">
      <p class="subTitle">核心指标</p>
      <div>
        选择时间：
        <date-picker
          :is-show-fast-time="false"
          :start-time.sync="timeRange.startDate"
          :end-time.sync="timeRange.endDate"
          type="date"
          picker="separate"
        ></date-picker>

      </div>
      <div style="margin-top: 20px;">
        <span class="flex-item__label">次级维度：</span>
        <el-select
          v-model="secondDimension.dimensionType"
          clearable
          collapse-tags
          @change="changeDimension"
        >
          <el-option
            v-for="(item, index) in $dict('DimensionTypeEnum')"
            :key="index"
            :label="item.desc"
            :value="item.code"
          >
            <span>{{ item.desc }}</span>
          </el-option>
        </el-select>
        <el-select
          key="1"
          v-if="secondDimension.dimensionType == 1"
          style="margin-left: 20px;"
          v-model="secondDimension.dimensionMessages"
          clearable
          multiple
          collapse-tags
          filterable
        >
          <el-option
            v-for="(item, index) in merchants"
            :key="index"
            :label="item.mchName"
            :value="item.mchNo"
          >
            <span>{{ item.mchName }}</span>
          </el-option>
        </el-select>
        <el-select
          key="2"
          v-else-if="secondDimension.dimensionType == 2"
          style="margin-left: 20px;"
          v-model="secondDimension.dimensionMessages"
          clearable
          filterable
        >
          <el-option
            v-for="(item, index) in branchList"
            :key="index"
            :label="item.branchName"
            :value="item.branchName"
          >
            <span>{{ item.branchName }}</span>
          </el-option>
        </el-select>

        <el-button
          type="primary"
          @click="getData"
        >查询</el-button>
      </div>
      <div
        class="data-list flex-box"
        v-if="coreData"
      >
        <div
          class="data-item flex-item"
          v-for="(item, index) in dataMap"
          :key="index"
          @click="clearCheck(index)"
        >
          <div class="data-item-title">
            {{ item.title }}
          </div>
          <div class="data-item-legend">
            {{ item.legend }}
            <span
              class="data-item-legend-data"
              v-if="index === 0"
            >
              {{ coreData[item.param] | moneyFormat('')}}
            </span>
            <span
              class="data-item-legend-data"
              v-else
            >
              {{ coreData[item.param] }}
            </span>
          </div>
          <div class="data-item-check">
            <el-checkbox
              v-model="item.check"
              @change="clearCheck(index)"
            ></el-checkbox>
          </div>
        </div>
      </div>
      <div class="func-block">
        统计方式：
        <el-radio-group
          v-model="chartType"
          @change="setChart()"
        >
          <el-radio label="month">按月统计</el-radio>
          <el-radio label="day">按日统计</el-radio>
        </el-radio-group>
      </div>
    </div>

    <div class="echart-block">
      <div id="echart"></div>
    </div>
  </div>
</template>

<script>
  import * as echarts from 'echarts';
  import { getCoreIndex } from '@/api/data'
  import dayjs from 'dayjs'
  import { listActiveMerchant, listBranchMerchant } from "@/api/merchant"
  export default {
    name: 'core-data',
    data() {
      return {
        dataMap: [
          { check: false, title: '支付金额（元）', legend: '累计', param: 'totalPayAmount' },
          { check: false, title: '客单价（元）', legend: '平均', param: 'avgPayAmount' },
          { check: false, title: '订单数（笔）', legend: '累计', param: 'orderCount' },
          { check: false, title: '下单人数（个）', legend: '累计', param: 'receivedUserCount' },
        ],
        timeRange: {
          startDate: dayjs().subtract(3, 'month').startOf('month').format('YYYY-MM-DD'),
          endDate: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
        },
        secondDimension: {
        },
        coreDataParam: '',
        coreData: null,
        chartType: 'month',
        echart: null,
        merchants: [],
        branchList: [],
      }
    },
    mounted() {
      this.dataMap[0].check = true;
      this.coreDataParam = this.dataMap[0].param;
      // 路由跳转参数
      if (this.$route.query.dimensionType) {
        this.secondDimension.dimensionType = this.$route.query.dimensionType + '';
        this.secondDimension.dimensionMessages = [this.$route.query.dimensionMessages];
        this.changeDimension(this.secondDimension.dimensionType);
      }
      this.getData()
    },
    methods: {
      async changeDimension(dimensionType) {
        this.$set(this.secondDimension, 'dimensionMessages', '')
        if (dimensionType == 1 && !this.merchants.length) {
          const { data } = await listActiveMerchant();
          this.merchants = data
        } else if (dimensionType == 2 && !this.branchList.length) {
          const { data } = await listBranchMerchant();
          this.branchList = data
        }
      },
      async getData() {
        let param = {
          startDate: this.timeRange.startDate,
          endDate: this.timeRange.endDate
        }
        if (this.secondDimension.dimensionType) {
          param.secondDimension = {...this.secondDimension}
        }
        if (this.secondDimension.dimensionType == 2) {
          param.secondDimension.dimensionMessages = [param.secondDimension.dimensionMessages]
        }
        const { data } = await getCoreIndex(param);
        this.coreData = data;
        this.setChart()
      },
      clearCheck(index) {
        this.dataMap.forEach((item, key) => {
          if (key != index) {
            item.check = false
          } else {
            item.check = true
          }
        });
        this.coreDataParam = this.dataMap[index]['param'];
        this.setChart(index);
      },
      setChart(index) {
        if (index === undefined) {
          for (let i = 0; i < this.dataMap.length; i++) {
            if (this.dataMap[i].check) {
              index = i;
              break;
            }
          }
          if (index === undefined) index = 0
        }
        if (this.echart !== null) {
          this.echart.clear()
        }
        let chartDataList = this.chartType === 'month'
          ? this.coreData['payAmountMonthly']
          : this.coreData['payAmountDaily']
        let xAisData = chartDataList.map(item => item.date);
        let chartData = chartDataList.map(item => {
          return item[this.coreDataParam]
        })
        if (!this.echart) {
          this.echart = echarts.init(document.getElementById('echart'))
        }
        this.echart.setOption({
          title: {
            text: `已选择指标：${this.dataMap[index]['title']}`
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAisData,
            axisLine: {
              show: true,
              onZero: false,
            },
          },
          yAxis: {
            type: 'value',
            name: `${this.dataMap[index]['title']}`,
            nameLocation: 'end',
            minInterval: this.coreDataParam === 'receivedUserCount' ? 2 : '',
          },
          legend: {
            bottom: 0,
            data: [`${this.dataMap[index]['title']}`]
          },
          tooltip: {
            trigger: 'axis'
          },
          series: [
            {
              data: chartData,
              type: 'line',
              smooth: true,
              name: `${this.dataMap[index]['title']}`
            }
          ],
        })
      },
    }
  }
</script>

<style scoped lang="scss">
  .data-list {
    margin: 16px 0;
  }
  .data-item {
    position: relative;
    margin-right: 16px;
    padding: 16px;
    border-radius: 10px;
    border: 1px solid #ddd;

    &-title {
      font-size: 14px;
    }
    &-detail {
      margin: 16px 0;
    }
    &-check {
      position: absolute;
      top: 16px;
      right: 16px;
    }

    &-legend {
      margin-top: 16px;
    }

    &-legend-data {
      font-size: 25px;
      font-weight: bolder;
    }
  }

  .echart-block {
    margin-top: 32px;
    padding: 16px;
    border: 1px solid #ddd;
  }
  #echart {
    height: 400px;
  }
</style>
