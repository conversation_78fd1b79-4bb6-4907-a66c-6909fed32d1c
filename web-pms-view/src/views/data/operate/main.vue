<template>
  <div class="main-data-container">
    <div class="box-container">
      <p>统计时间跨度：{{ startTime }} 至 {{ endTime }}
        <el-date-picker
          style="margin-left: 16px;"
          v-model="time"
          type="date"
          placeholder="请选择日期"
          value-format="yyyy-MM-dd"
          :picker-options="{
            disabledDate(date) {
              return date > today
            }
          }"
          @change="timeRefresh"
        ></el-date-picker>
        <el-button
          type="primary"
          @click="refreshData"
        >查询
        </el-button>
      </p>
    </div>
    <div
      class="box-container"
      v-for="(content, prop) in dataMap"
      :key="prop"
    >
      <div class="data-chart-block">
        <div
          class="data-block"
          v-if="mainData && mainData[prop]"
        >
          <p class="subTitle">{{ content.title }}
            <el-button
              type="text"
              v-if="prop == 'merchant'"
              @click="exportMerchantData"
            >导出数据
            </el-button>
          </p>
          <div class="data-list flex-box">
            <div
              class="data-item flex-item"
              v-for="(item, index) in content.map"
              :key="index"
              @click="clearCheck(prop, index)"
            >
              <div class="data-item-title">
                {{ item.title }}
              </div>
              <div class="data-item-detail">
                <span class="data-item-detail-data">
                  {{ mainData[prop]['this' + monthDiffField[index]] }}
                </span>
                <div
                  class="data-item-detail-diff"
                  :class="{down: mainData[prop][diffFieldList[index]] < 0}"
                >
                  <span
                    class="data-item-diff-icon"
                    :class="{down:  mainData[prop][diffFieldList[index]] < 0 }"
                  >
                    <svg-icon icon-class="double-arrow-up"></svg-icon>
                  </span>
                  {{ mainData[prop][diffFieldList[index]] }}
                </div>
                <div class="data-item-diff">{{ item.legend }}
                  <span>
                    {{ mainData[prop]['last' + monthDiffField[index]] }}
                  </span>
                </div>
                <div class="data-item-check">
                  <el-checkbox
                    v-model="item.check"
                    @change="clearCheck(prop, index)"
                  ></el-checkbox>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="echart-block">
          <div style="margin: 16px 0;">
            <el-radio-group
              v-model="content.type"
              @change="refreshEchart(prop)"
            >
              <el-radio label="bar">数据</el-radio>
              <el-radio label="pie">占比</el-radio>
            </el-radio-group>
          </div>
          <div
            :id="prop + '-echart'"
            class="echart"
          ></div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';
import dayjs from 'dayjs'
import { salerData, supplyData, merchantData, agentData } from '@/api/data'

export default {
  name: 'main-data',
  data() {
    return {
      startTime: null,
      endTime: null,
      time: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
      today: dayjs().endOf('day').add(-1, 'day'),
      mainData: null,

      echartMap: {
        saler: null,
        supply: null,
        agent: null,
        merchant: null,
      },
      salerMerchant: [],
      dataMap: {
        saler: {
          title: '销售数据',
          index: 0,
          type: 'bar',
          map: [
            { check: false, title: '月支付金额（万元）', legend: '上个自然月同期', },
            { check: false, title: '月签约商户数（个）', legend: '上个自然月同期', },
          ],
        },
        supply: {
          title: '供应商数据',
          index: 0,
          type: 'bar',
          map: [
            { check: false, title: '月支付金额（万元）', legend: '上个自然月同期' },
            { check: false, title: '月成单商户数（个）', legend: '上个自然月同期' },
          ],
        },
        agent: {
          title: '合伙人数据',
          index: 0,
          type: 'bar',
          map: [
            { check: false, title: '月支付金额（万元）', legend: '上个自然月同期' },
            { check: false, title: '月签约商户数（个）', legend: '上个自然月同期' },
          ],
        },
        merchant: {
          title: '商户数据',
          index: 0,
          type: 'bar',
          map: [
            { check: false, title: '月支付金额（万元）', legend: '上个自然月同期' },
            { check: false, title: '月订单量（笔）', legend: '上个自然月同期' },
            { check: false, title: '月毛利（万元）', legend: '上个自然月同期' },
          ],
        },
      },
      diffFieldList: ['moneyDiff', 'numDiff', 'profitDiff'],
      monthDiffField: ['MonthMoney', 'MonthNum', 'MonthProfit'],
    }
  },
  mounted() {
    this.initPage();
    this.refreshData();
    // this.listActiveMerchant()
  },
  methods: {
    // async listActiveMerchant(){
    //   const {data} = await listActiveMerchant()
    //   this.salerMerchant = data
    //   console.log(this.salerMerchant)
    // },
    initPage() {
      for (let p in this.dataMap) {
        this.dataMap[p]['map'][0].check = true;
      }
      this.timeRefresh()
    },
    timeRefresh() {
      this.startTime = dayjs(this.time).startOf('month').format('YYYY-MM-DD');
      this.endTime = this.time;
    },
    clearCheck(type, index) {
      this.dataMap[type].map.forEach((item, key) => {
        if (key != index) {
          item.check = false
        } else {
          item.check = true
        }
      });
      this.dataMap[type].index = index;
      this.refreshEchart(type)
    },
    setEcharts() {
      let task = []
      for (let prop in this.echartMap) {
        task.push(this.refreshEchart(prop))
      }
      Promise.all(task)
    },
    async refreshData() {
      const [
        { data: saler },
        { data: supply },
        { data: merchant },
        { data: agent },
      ] = await Promise.all([
        salerData({ endDate: this.endTime }),
        supplyData({ endDate: this.endTime }),
        merchantData({ endDate: this.endTime }),
        agentData({ endDate: this.endTime }),
      ])
      this.mainData = {
        saler,
        supply,
        merchant,
        agent,
      }
      let list = Object.keys(this.mainData)
      list.forEach(prop => {
        let item = this.mainData[prop];
        item.moneyDiff = ((item.thisMonthMoney * 100 - item.lastMonthMoney * 100) / (100 * 10 * 10 * 10 * 10)).toFixed(2);
        item.numDiff = item.thisMonthNum - item.lastMonthNum;
        item.profitDiff = ((item.thisMonthProfit * 100 - item.lastMonthProfit * 100) / (100 * 10 * 10 * 10 * 10)).toFixed(2);
        item.thisMonthMoney = (item.thisMonthMoney / 10000).toFixed(2);
        item.lastMonthMoney = (item.lastMonthMoney / 10000).toFixed(2);
        item.thisMonthProfit = (item.thisMonthProfit / 10000).toFixed(2);
        item.lastMonthProfit = (item.lastMonthProfit / 10000).toFixed(2);
        item.moneyData.forEach(money => {
          money.lastMonth = (money.lastMonth / 10000).toFixed(2);
          money.thisMonth = (money.thisMonth / 10000).toFixed(2);
        })
        if (item.profitData) {
          item.profitData.forEach(money => {
            money.lastMonth = (money.lastMonth / 10000).toFixed(2);
            money.thisMonth = (money.thisMonth / 10000).toFixed(2);
          })
        }
      })
      await this.$nextTick()
      this.setEcharts();
    },
    async refreshEchart(type) {
      let vm = this
      let echart = this.echartMap[type]
      if (echart !== null) {
        echart.clear()
      } else {
        const elm = document.querySelector(`#${ type }-echart`);
        this.echartMap[type] = echarts.init(elm)
      }

      const echartItem = this.echartMap[type];
      echartItem.off('click')

      const isMonthProfit = type === 'merchant' && this.dataMap[type].index === 2

      let param = this.dataMap[type].index === 0 ? 'moneyData' : (isMonthProfit ? 'profitData' : 'numData')

      if (this.dataMap[type].type === 'bar') {
        // 柱状图数据
        let yAxisData = this.mainData[type][param].map(item => item.name)
        let lastMonthData = this.mainData[type][param].map(item => item.lastMonth)
        let thisMonthData = this.mainData[type][param].map(item => item.thisMonth)
        echartItem.setOption({
          title: {
            text: `已选择指标：${ this.dataMap[type]['map'][this.dataMap[type]['index']]['title'] }`,
            subtext: type === 'saler' ? '' : '备注：只显示前15条记录'
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            },
            formatter: (param) => {
              let result = param[0].name + '<br />'
              result = result + param.map(item => {
                return item.seriesName + '&nbsp;&nbsp;' + item.value + '<br/>'
              }).join('')

              let diff = ''
              if (this.dataMap[type].index === 0) {
                diff = (param[1].value - param[0].value).toFixed(2)
              } else {
                diff = param[1].value - param[0].value
              }
              result += diff == 0 ? '' : ((diff > 0 ? '增加' : '减少') + '&nbsp;&nbsp;' + Math.abs(diff))
              return result
            }
          },
          legend: {
            top: 0,
            right: '10%',
          },
          grid: {
            left: '3%',
            right: '10%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'value',
            boundaryGap: [0, '10%'],
          },
          yAxis: {
            type: 'category',
            data: yAxisData
          },
          dataZoom: [
            {
              type: 'slider',
              show: true,
              yAxisIndex: [0],
              left: '90%',
              start: 0,
              end: 100,
              showDetail: false,
            },
          ],
          series: [
            {
              name: '上月',
              type: 'bar',
              data: lastMonthData,
              label: {
                show: true,
                position: 'right',
                formatter: (param) => {
                  return this.dataMap[type].index === 0 || this.dataMap[type].index === 2
                    ? (Number(param.value)).toFixed(2) + '万'
                    : (param.value) + (type === 'merchant' ? '笔' : '个')
                }
              },
            },
            {
              name: '本月',
              type: 'bar',
              data: thisMonthData,
              label: {
                show: true,
                position: 'right',
                formatter: (param) => {
                  return this.dataMap[type].index === 0 || this.dataMap[type].index === 2
                    ? (Number(param.value)).toFixed(2) + '万'
                    : (param.value) + (type === 'merchant' ? '笔' : '个')
                }
              }
            }
          ]
        });
      } else if (this.dataMap[type].type === 'pie') {
        // 环形图数据
        let thisMonth = this.mainData[type][param].map(item => {
          return {
            name: item.name,
            value: item.thisMonth,
          }
        })
        echartItem.setOption({
          title: {
            text: `已选择指标：${ this.dataMap[type]['map'][this.dataMap[type]['index']]['title'] }`,
            subtext: type === 'saler' ? '' : '备注：只显示前15条记录'
          },
          tooltip: {
            trigger: 'item'
          },
          legend: {
            bottom: 0,
            left: 'center',
          },
          series: [
            {
              type: 'pie',
              radius: ['40%', '60%'],
              avoidLabelOverlap: false,
              emphasis: {
                label: {
                  show: true,
                  fontWeight: 'bold'
                }
              },
              label: {
                show: true,
                fontSize: 16,
                formatter: (param) => {
                  return `${ param.data.name }\n${ param.percent }%`
                }
              },
              data: thisMonth.reverse(),
            }
          ]
        });
      }
      if (type === 'saler') {
        echartItem.on('click', 'series', function (params) {
          let salerId = vm.mainData.saler.moneyData[params.dataIndex].key
          vm.$router.push({
            path: '/data/preview/saler',
            query: {
              salerId,
              startTime: vm.startTime,
              endTime: vm.endTime,
            }
          })
        })
      }
      return true
    },
    exportMerchantData() {
      window.open(`${ this.baseUrl }/download/exportData?reportType=400&endDate=${ this.time }&x-token=${ this.uploadHeader['X-Token'] }`)
    }
  }
}
</script>

<style
  scoped
  lang="scss">
.data-item {
  position: relative;
  margin-right: 16px;
  padding: 16px;
  border-radius: 10px;
  max-width: 300px;
  border: 1px solid #ddd;

  &-title {
    font-size: 14px;
  }

  &-detail {
    margin: 16px 0 0;
  }

  &-detail-diff {
    display: inline-block;
    vertical-align: middle;
    color: #008000;

    &.down {
      color: #f00;
    }
  }

  &-detail-data {
    margin-right: 24px;
    vertical-align: middle;
    font-size: 24px;
    font-weight: bolder;
  }

  &-check {
    position: absolute;
    top: 16px;
    right: 16px;
  }

  &-diff {
    margin: 14px 0 0;
    font-size: 16px;
    color: #aaa69d;
  }

  &-diff-icon {
    display: inline-block;
    color: #008000;

    &.down {
      color: #f00;
      transform: rotate(180deg);
    }
  }
}

.echart-block {
  padding: 16px;
  margin-top: 32px;
  border: 1px solid #ddd;
  font-size: 0;

  .echart {
    height: 400px;
  }
}

.data-chart-block {
  margin: 0 0 64px;

  .subTitle {
    font-size: 20px;
    font-weight: 500;
  }
}
</style>
