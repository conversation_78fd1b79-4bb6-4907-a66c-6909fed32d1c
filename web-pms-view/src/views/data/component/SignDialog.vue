<template>
  <el-dialog
    :visible="visible"
    :before-close="close"
    title="发起签约"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="signRules"
      label-width="150px"
    >
      <warning-block>将向该用户发送电子签约短信，请确认用户手机号</warning-block>
      <template v-if="type == 1">
        <el-form-item label="姓名：">
          <el-input
            disabled
            v-model="form.receiveName"
          ></el-input>
        </el-form-item>
        <el-form-item label="证件号：">
          <el-input
            disabled
            v-model="form.idCard"
          ></el-input>
        </el-form-item>
        <el-form-item label="手机号：" prop="phone">
          <el-input
            clearable
            v-model="form.phone"
          ></el-input>
        </el-form-item>
      </template>
      <el-form-item label="签约模式：" prop="signType">
        <el-select
          clearable
          v-model="form.signType"
        >
          <el-option
            v-for="item in $dict('ChannelSignTypeEnum')"
            :key="item.code"
            :value="item.code"
            :label="item.desc"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <el-button type="primary" @click="confirm">确认</el-button>
      <el-button @click="close">取消</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { toPromise } from '@/utils'
  import WarningBlock from '@/components/WarningBlock'
  import { validateParams } from '@/utils/validate'
  export default {
    name: 'sign-dialog',
    components: {
      WarningBlock
    },
    data() {
      return {
        visible: false,
        form: {},
        signRules: {
          phone: [
            {required: true, trigger: 'blur', message: '请输入手机号'},
            { validator: validateParams({ type: 'Phone', msg: '请输入正确的手机号' }), trigger: 'blur' }
          ],
          signType: [
            {required: true, trigger: 'blur', message: '选择签约方式'},
          ],
        },
        type: 0,
      }
    },
    methods: {
      close() {
        this.form = {}
        this.toggle()
      },
      async confirm() {
        const [err] = await toPromise(this.$refs.form.validate())
        if (err) return;
        this.$emit('change', { ...this.form, type: this.type });
      },
      /**
       * type
       * 1. 单人
       * 2. 多人
       */
      toggle(type = 0) {
        this.visible = !this.visible
        this.type = type
      }
    },
  }
</script>

<style scoped lang="scss">

</style>
