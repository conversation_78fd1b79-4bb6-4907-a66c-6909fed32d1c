<template>
  <div class="page-container">
    <el-steps class="header-container" :active="active" align-center>
      <el-step title="设置基础信息"></el-step>
      <el-step title="设置流程"></el-step>
    </el-steps>

    <el-form label-width="150px" :model="form" v-show="active == 1" ref="form_1" :rules="rules">
      <div class="subTitle">基本配置</div>
      <el-form-item label="审批流名称：" prop="name">
        <el-input clearable style="width: 400px;" v-model="form.name" ></el-input>
      </el-form-item>
      <el-form-item label="审批流描述：">
        <el-input type="textarea" :rows="5" v-model="form.description"></el-input>
      </el-form-item>

      <el-form-item label="所属平台：" prop="platform">
        <el-select
          v-model="form.platform"
          clearable
        >
          <el-option
            v-for="item in $dict('PlatformSource')"
            :key="item.code"
            :label="item.desc"
            :value="item.code"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="当：" required>
        <el-form-item prop="dataObj" style="display: inline-block;">
          <el-select
            v-model="form.dataObj"
            clearable
          >
            <el-option
              v-for="item in $dict('DataObjectEnum')"
              :key="item.code"
              :label="item.desc"
              :value="item.code"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item prop="triggerAct" style="display: inline-block;">
          <el-select
            v-model="form.triggerAct"
            clearable
          >
            <el-option
              v-for="item in triggerActDict"
              :key="item.code"
              :label="item.desc"
              :value="item.code"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form-item>

    </el-form>

    <el-form label-width="150px" :model="form" v-show="active == 2" ref="form_2" :rules="rules" :disabled="actionType == 'EDIT'">
      <el-form-item label="上传流程文件：" prop="file">
        <el-upload
          drag
          action="file"
          :auto-upload="false"
          accept=".xml"
          :headers="uploadHeader"
          :limit="1"
          :file-list="fileList"
          :on-change="handleFileChange"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip" slot="tip">只能上传xml文件</div>
        </el-upload>
      </el-form-item>
    </el-form>

    <div class="form-btn-group">
      <el-button type="primary" v-if="active == 1" @click="nextStep">保存并继续</el-button>
      <el-button type="primary" v-if="active == 2" @click="nextStep">保存</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>

  </div>
</template>

<script>
  import { deployByUpload, updateProcDef, getProcById } from '@/api/procDef';
  export default {
    name: "processForm",

    data() {
      return {
        active: 1,

        form: {
          name: '',
          platform: '',
          description: '',
          dataObj: '',
          triggerAct: '',
          file: null,
        },

        fileList: [],

        rules: {
          name: [{required: true, trigger: 'blur', message: '请输入流程名称'}],
          platform: [{required: true, trigger: 'change', message: '请选择所属平台'}],
          file: [{required: true, trigger: 'blur', message: '请上传流程图文件'}],
          dataObj: [{required: true, trigger: 'change', message: '请选择关联对象'}],
          triggerAct: [{required: true, trigger: 'change', message: '请选择触发动作'}],
        }
      }
    },
    computed: {
      actionType() {
        return this.$route.query.actionType || 'ADD'
      },
      triggerActDict() {
        let flag = this.$dictCode('DataObjectEnum', this.form.dataObj).flag;
        return this.$dict(flag) || []
      }
    },
    mounted() {
      if (this.actionType == 'EDIT' && this.$route.query.id) {
        this.getProcById(this.$route.query.id)
      }
    },
    methods: {
      handleFileChange(file) {
        this.form.file = file.raw
      },
      async nextStep() {
        const valid = this.$refs[`form_${this.active}`].validate().catch(_ => false)
        if (!valid) return;
        if (this.active == 1) return this.active++;
        if (this.actionType == 'ADD') {
          this.doAdd();
        } else {
          this.doEdit();
        }
      },
      async doAdd() {
        const formData = new FormData();
        for (let p in this.form) {
          formData.append(p, this.form[p]);
        }
        const { data } = await deployByUpload(formData);
        data && this.$message.success(data);
        this.$router.push('/system/process/manage');
      },
      async doEdit() {
        let form = { ...this.form, id: this.$route.query.id };
        delete form.file;
        const { data } = await updateProcDef(form);
        data && this.$message.success(data);
        this.$router.push('/system/process/manage');
      },
      cancel() {
        if (this.active == 1) {
          this.$router.push('/system/process/manage')
        } else {
          this.active--;
        }
      },
      async getProcById(id) {
        const { data } = await getProcById({id});
        for (let p in this.form) {
          if (data.hasOwnProperty(p)) {
            this.form[p] = data[p]
          }
        }
      }
    },
  }
</script>

<style scoped>

</style>
