<template>
  <div class="box-container">
    <el-button class="create-btn" type="primary" @click="createProc">新建流程</el-button>

    <div class="content-container">
      <el-table :data="list">
        <el-table-column label="审批流名称" prop="name"></el-table-column>
        <el-table-column label="所属平台" prop="platform">
          <template v-slot="{row}">
            {{ $dictCode('PlatformSource', row.platform).desc }}
          </template>
        </el-table-column>
        <el-table-column label="关联对象" prop="dataObj">
          <template v-slot="{row}">
            {{ $dictCode('DataObjectEnum', row.dataObj).desc }}
          </template>
        </el-table-column>
        <el-table-column label="触发动作" prop="triggerAct">
          <template v-slot="{row}">
            {{ $dictCode($dictCode('DataObjectEnum', row.dataObj).flag, row.triggerAct).desc }}
          </template>
        </el-table-column>
        <el-table-column label="创建人" prop="createUser"></el-table-column>

        <el-table-column label="创建时间" prop="createTime">
          <template v-slot="{row, column}">
            <div v-html="renderTime(row[column['property']])"></div>
          </template>
        </el-table-column>

        <el-table-column label="最后修改人" prop="updateUser"></el-table-column>

        <el-table-column label="最后修改时间	" prop="updateTime">
          <template v-slot="{row, column}">
            <div v-html="renderTime(row[column['property']])"></div>
          </template>
        </el-table-column>

        <el-table-column label="状态" prop="suspensionState">
          <template v-slot="{row}">
            {{ row.suspensionState == 1 ? '激活' : '挂起' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template v-slot="{row}">
            <div v-if="row.suspensionState == 1">
              <el-button type="text" @click="toggleStatus(row)">停用</el-button>
            </div>
            <div v-else-if="row.suspensionState == 2">
              <el-button type="text" @click="editProcDef(row)">编辑</el-button>
              <el-button type="text" @click="deleteProcDef(row)">删除</el-button>
              <el-button type="text" @click="toggleStatus(row)">启用</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      >
      </el-pagination>
    </el-footer>
  </div>
</template>

<script>
  import { getProcDefList, toggleProcDefStatus, deleteProcDef } from '@/api/procDef';
  export default {
    name: 'PmsProcessManage',

    data() {
      return {
        list: [],

        pageSize: 10,
        pageCurrent: 1,
        totalRecord: 0,
      }
    },
    mounted() {
      this.search()
    },

    methods: {
      async search(init) {
        if (init) {
          this.pageCurrent = 1;
        }

        const { data } = await getProcDefList({
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent,
        });

        this.list = data.data;
        this.totalRecord = data.totalRecord;
      },
      async toggleStatus(row) {
        const valid = await this.$confirm(`是否确认${row.suspensionState == 1 ? '停用' : '启用'}该流程？`).catch(_ => false)
        if (valid) {
          const { data } = await toggleProcDefStatus({
            id: row.id,
            state: row.suspensionState == 1 ? 2 : 1,
          });
          data && this.$message.success(data);
          this.search();
        }
      },
      async deleteProcDef(row) {
        const valid = await this.$confirm(`是否确认删除该流程？`).catch(_ => false)
        if (valid) {
          await deleteProcDef({
            id: row.id
          });
          this.search();
        }
      },
      editProcDef(row) {
        this.$router.push({
          path: '/system/process/processForm',
          query: {
            id: row.id,
            actionType: 'EDIT',
          }
        })
      },
      createProc() {
        this.$router.push({
          path: '/system/process/processForm'
        })
      }
    },
  }
</script>

<style scoped lang="scss">

</style>
