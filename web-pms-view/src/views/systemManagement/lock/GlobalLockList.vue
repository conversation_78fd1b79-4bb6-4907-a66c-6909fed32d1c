<template>
  <div class="main-page">
    <el-header class="main-page-header">
      <div class="search-item">
        <span>资源ID</span>
        <el-input v-model="searchParam.resourceId"/>
      </div>
      <div class="search-item">
        <span>客户端ID</span>
        <el-input v-model="searchParam.clientId"/>
      </div>

      <div class="search-item">
        <span>资源状态</span>
        <el-select clearable v-model="searchParam.resourceStatus">
          <el-option v-for="({code,desc}) in $dict('ResourceStatusEnum')" :key="parseInt(code)" :label="desc" :value="parseInt(code)"/>
        </el-select>
      </div>

      <div class="function-bar">
        <el-button class="el-button--primary" @click="refreshList">
          <i class="el-icon-search"/>查询
        </el-button>
      </div>
    </el-header>

    <el-main class="main-page-content">
      <el-table :data="pageResult.data" max-height="300" row-key="id">
        <el-table-column type="index" :index="getIndex"/>
        <el-table-column label="创建时间" prop="createTime"/>
        <el-table-column label="资源ID" prop="resourceId"/>
        <el-table-column label="资源状态" prop="resourceStatus" :formatter="(row)=>$dictCode('ResourceStatusEnum',row.resourceStatus).desc"/>
        <el-table-column label="描述" prop="remark"/>
        <el-table-column>
          <template v-slot:header="{}">客户端Id<br/>客户端标识</template>
          <template v-slot="{row}">{{row.clientId}}<br/>{{row.clientFlag}}</template>
        </el-table-column>
        <el-table-column>
          <template v-slot:header="{}">上锁时间<br/>过期时间</template>
          <template v-slot="{row}">{{row.lockTime}}<br/>{{row.expireTime}}</template>
        </el-table-column>

        <el-table-column label="操作">
          <template v-slot:default="{row}">
            <el-button type="text" size="small" @click="unlockForce(row)">解锁</el-button>
            <el-button type="text" size="small" @click="unlockAndDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-main>

    <el-footer class="main-page-footer">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10,50,100]"
        :total="pageResult.totalRecord"
        :page-size.sync="pageParam.pageSize"
        :current-page.sync="pageParam.pageCurrent"
        @size-change="refreshList"
        @current-change="refreshList"/>
    </el-footer>
    <pForm ref="form" v-bind="{isAdd}" v-on:success="refreshList"/>
  </div>
</template>

<script>
  import { listGlobalLock, unlockForce, unlockAndDelete } from '@/api/globalLock'

  export default {
    name: "GlobalLockList",
    data() {
      return {
        searchParam: {},
        pageParam: {pageCurrent: 1, pageSize: 10},
        pageResult: {},
        isAdd: false,
      };
    },
    methods: {
      refreshList() {
        listGlobalLock(this.searchParam, this.pageParam)
          .then(({data}) => {
            this.pageResult = data;
          });
      },
      unlockForce(row) {
        this.$confirm("确定强制释放锁吗?")
          .then(() => unlockForce({resourceId: row.resourceId}))
          .then(({data}) => {
            this.refreshList()
            this.$message.success(data)
          });
      },
      unlockAndDelete(row) {
        this.$confirm("确定删除吗?")
          .then(() => unlockAndDelete({resourceId: row.resourceId}))
          .then(({data}) => {
            this.refreshList()
            this.$message.success(data)
          });
      },
      getIndex(index) {
        return (this.pageParam.pageCurrent - 1) * this.pageParam.pageSize + index + 1;
      },
    },
    mounted() {
      this.refreshList();
    }
  };
</script>
