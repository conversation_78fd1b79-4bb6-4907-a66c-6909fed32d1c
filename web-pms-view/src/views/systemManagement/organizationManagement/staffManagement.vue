<template>
  <div class="staff-container">
    <div class="none-department-alert" v-if="noneDepartmentStaffList.length > 0">
      <i class="el-icon-warning"></i>
      <div class="none-department-content">
        <span @click="showNoneDepartment()">{{noneDepartmentStaffList.length}}个员工未分配主属部门，请点击查看</span>
        <i class="el-icon-closebtn none-department-return" @click="noneDepartmentReturn">返回</i>
      </div>
    </div>
    <div class="box-container">
      <el-button class="create-btn" type="primary" @click="toStaffForm('create')">添加员工</el-button>
      <div class="search-container" v-show="!isshowNoneDepartment">
        <div class="flex-container">
          <div class="flex-wrapper search-wrapper">
            <div class="flex-item">
              <span class="flex-item__label">账号：</span>
              <el-input v-model="form.loginName"></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">员工姓名：</span>
              <el-input v-model="form.realNameLike"></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">角色：</span>
              <el-select
                clearable
                v-model="form.roleId"
              >
                <el-option
                  v-for="(item, index) in roleList"
                  :key="index"
                  :value="item.id"
                  :label="item.roleName"
                ></el-option>
              </el-select>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">状态：</span>
              <el-select clearable v-model="form.status">
                <el-option
                  v-for="{code,desc} in $dict('PmsOperatorStatusEnum')"
                  :key="parseInt(code)"
                  :label="desc"
                  :value="parseInt(code)"
                />
              </el-select>
            </div>
          </div>
          <div class="search-wrapper">
            <div class="search-btn-group">
              <el-button type="primary" @click="search(true)">查询</el-button>
              <el-button type="text" @click="clearCondition">清空筛选条件</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="content-container">
        <el-table :data="staffList">
          <el-table-column label="账号" prop="loginName"></el-table-column>
          <el-table-column label="员工姓名" prop="realName"></el-table-column>
          <el-table-column label="联系方式" prop="mobileNo"></el-table-column>
          <el-table-column
            label="用户类型"
            prop="type"
            :formatter="row => (row.type===1&&'超级管理员')||(row.type===2&&'普通操作员')"
          ></el-table-column>

          <el-table-column label="角色" prop="pmsRolesList">
            <template v-slot="{row}">
              <div v-if="row.pmsRolesList">
                {{ row.pmsRolesList.reduce((prev, curr) => {
                    return prev += `、${curr.roleName}`
                }, '').slice(1)}}
              </div>
            </template>
          </el-table-column>

          <el-table-column
            label="状态"
            prop="status"
            :formatter="row=> $dictCode('PmsOperatorStatusEnum',row.status).desc"
          ></el-table-column>
          <el-table-column label="添加时间" prop="createTime">
            <template v-slot="{row}">
              <p v-html="renderTime(row.createTime)"></p>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="data">
            <template slot-scope="scope">
              <el-button type="text" @click="toStaffForm('edit', scope.row)">编辑</el-button>
              <el-button type="text" @click="showChangePwd(scope.row)">修改密码</el-button>
              <el-button
                type="text"
                v-if="scope.row.status===1"
                @click="changeStatus(scope.row, '冻结')"
              >冻结</el-button>
              <el-button
                type="text"
                v-if="scope.row.status===2"
                @click="changeStatus(scope.row, '审核')"
              >审核</el-button>
              <el-button
                type="text"
                v-if="scope.row.status===-1"
                @click="changeStatus(scope.row, '激活')"
              >激活</el-button>
              <el-button type="text" @click="deleteStaff(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div v-show="!isshowNoneDepartment" class="pagination-container">
        <el-pagination
          v-if="totalNum"
          ref="pagination"
          :total="totalNum"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>

    <!--修改密码-->
    <el-dialog
      :visible.sync="isShowChangePwd"
      :close-on-click-modal="false"
      :before-close="closeForm"
      title="修改密码"
      append-to-body
      width="600px"
    >
      <el-form
        ref="form"
        :inline="true"
        :model="changePwdForm"
        :rules="rules"
        size="small"
        label-width="100px"
      >
        <el-form-item label="账号" prop="loginName">
          <el-input v-model="changePwdForm.loginName" style="width: 450px;" disabled />
        </el-form-item>
        <el-form-item label="登录新密码" prop="newPwd">
          <el-input
            v-model="changePwdForm.newPwd"
            type="password"
            placeholder="登录新密码"
            style="width: 450px;"
          />
        </el-form-item>
        <el-form-item label="确认新密码" prop="newPwd2">
          <el-input
            v-model="changePwdForm.newPwd2"
            type="password"
            placeholder="确认登录新密码"
            style="width: 450px;"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="closeForm">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submitChangePwd">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getStaffList,
  changeStaffStatus,
  resetStaffPwd,
  deleteStaff,
  getNoDepartmentStaff,
  getRoleList
} from "../../../api/system";

export default {
  name: "PmsStaffManagement",
  data() {
    return {
      // 未分配部门员工
      noneDepartmentStaffList: [],
      isshowNoneDepartment: false,

      // 分页搜索
      form: {
        loginName: "",
        realNameLike: "",
        status: "",
        roleId: '',
      },
      pageCurrent: 1,
      pageSize: 10,
      staffList: [],
      totalNum: 0,
      timeRange: [],

      // 修改密码
      changePwdForm: {
        id: null,
        loginName: "",
        newPwd: "",
        newPwd2: "",
      },
      loading: false,
      isShowChangePwd: false,
      rules: {
        newPwd: [
          {
            required: true,
            message: "密码为8-16位，包含字母和数字，区分大小写",
            // eslint-disable-next-line
            pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,16}$/,
            trigger: "blur",
          },
        ],
        newPwd2: [
          {
            required: true,
            trigger: "blur",
            validator: (rule, value, callback) =>
              value !== this.changePwdForm.newPwd
                ? callback(new Error("两次输入密码不一致!"))
                : callback(),
          },
        ],
      },

      roleList: []
    };
  },
  mounted() {
    if (this.$route.query.roleId) {
      this.form.roleId = Number(this.$route.query.roleId)
    }
    this.getNoDepartmentStaff();
    this.search();

    getRoleList({
      pageCurrent: 1,
      pageSize: 100
    }).then(res => { this.roleList = res.data.data })

  },
  methods: {
    toStaffForm(type, row) {
      var redirect = "/system/organizationManagement/staffForm?type=" + type;
      if (type === "edit") {
        redirect += "&id=" + row.id;
      }
      this.$router.push(redirect);
    },
    getNoDepartmentStaff() {
      getNoDepartmentStaff()
        .then((response) => {
          this.noneDepartmentStaffList = response.data;
        })
        .catch((err) => {});
    },
    showNoneDepartment() {
      this.isshowNoneDepartment = true;
      this.staffList = this.noneDepartmentStaffList;
    },
    noneDepartmentReturn() {
      this.isshowNoneDepartment = false;
      this.search();
    },
    search(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      getStaffList({
        pageCurrent: this.pageCurrent,
        pageSize: this.pageSize,
        ...this.form,
      }).then((response) => {
        this.staffList = response.data.data;
        this.totalNum = response.data.totalRecord;
      });
    },
    clearCondition() {
      this.form.loginName = "";
      this.form.realNameLike = "";
      this.form.status = "";
      this.search();
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    getTimeRange(val) {
      this.timeRange = val;
    },
    showChangePwd(row) {
      this.isShowChangePwd = true;
      this.changePwdForm.id = row.id;
      this.changePwdForm.loginName = row.loginName;
    },
    submitChangePwd() {
      this.$refs.form.validate(valid => {
        resetStaffPwd({
          operatorId: this.changePwdForm.id,
          newPwd: this.changePwdForm.newPwd,
        })
          .then((response) => {
            this.$message.success(response.data);
            this.isShowChangePwd = false;
          })
          .catch((err) => {});
      })
    },
    closeForm() {
      this.isShowChangePwd = false;
      this.loading = false;
      this.changePwdForm.id = null;
      this.changePwdForm.loginName = "";
      this.changePwdForm.newPwd = "";
      this.changePwdForm.newPwd2 = "";
    },
    changeStatus(row, change) {
      this.$confirm(`确定要${change}账号?`, {
        type: "warning",
      }).then((res) => {
        changeStaffStatus({ id: row.id }).then(({ data }) => {
          this.$message.success(data);
          this.search();
        });
      });
    },
    deleteStaff(id) {
      this.$confirm("确定要删除账号？", { type: "warning" }).then((res) => {
        deleteStaff({ id: id })
          .then((response) => {
            this.$message.success(response.data);
            this.search();
          })
          .catch((err) => {});
      });
    },
  },
};
</script>

<style lang="scss" scoped>

.none-department-alert {
  position: relative;
  box-sizing: border-box;
  display: flex;
  width: 100%;
  padding: 8px 16px;
  margin-bottom: 8px;
  border-radius: 4px;
  background-color: #fdf6ec;
  color: #e6a23c;
}

.none-department-content {
  padding: 0 8px;
  display: table-cell;
}

.none-department-return {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 50px;
  font-style: normal;
  font-size: 13px;
  opacity: 1;
  color: #c0c4cc;
  cursor: pointer;
}
</style>
