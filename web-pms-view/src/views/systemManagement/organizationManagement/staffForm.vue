<template>
  <div class="box-container">
    <el-form label-width="120px" :rules="rules" :model="form" ref="form" v-loading="load">
      <!--账号信息-->
      <div class="account_info">
        <p class="subTitle">账号信息</p>
        <div>
          <el-form-item label="账号：" prop="loginName">
            <el-input v-model="form.loginName" :disabled="type !== 'create'"></el-input>账号是员工在企业中的唯一标识，请勿重复
          </el-form-item>
          <el-form-item label="员工姓名：" prop="realName">
            <el-input v-model="form.realName"></el-input>
          </el-form-item>
          <el-form-item label="联系方式：" prop="mobileNo">
            <el-input v-model="form.mobileNo"></el-input>
          </el-form-item>
          <el-form-item label="初始密码：" prop="loginPwd" v-if="type == 'create'">
            <el-input type="password" v-model="form.loginPwd" autocomplete="new-password"></el-input>8-16位密码，包含字母和数字，区分大小写
          </el-form-item>
        </div>
      </div>
      <!--部门信息-->
      <div class="department_info">
        <p class="subTitle">部门信息</p>
        <div>
          <el-form-item label="主属部门：">
            <el-popover placement="bottom-start" trigger="click">
              <departmentTree
                ref="menuTree"
                :canChooseAll="true"
                @selectDepartment="selectDepartment"
              ></departmentTree>
              <el-input slot="reference" v-model="form.departmentName" placeholder="点击选择部门"></el-input>
            </el-popover>
          </el-form-item>
        </div>
      </div>
      <!--合作信息-->
      <div class="cooperation_info">
        <p class="subTitle">角色权限关系</p>
        <div>
          <el-form-item label="所属角色：" prop="roleIds">
            <el-select clearable v-model="form.roleIds" multiple>
              <el-option
                v-for="item in roleList"
                :key="item.roleName"
                :label="item.roleName"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
      </div>
      <div class="footer-container">
        <el-button type="primary" @click="submit">确定</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>
  </div>
</template>

<script>
import departmentTree from "@/components/DepartmentTree";
import {
  createStaff,
  editStaff,
  getSingleStaff,
  getDepartmentList,
  getAllRoleList,
} from "../../../api/system";
export default {
  name: "staffForm",
  components: {
    departmentTree,
  },
  data() {
    return {
      type: "create",
      load: false,
      rules: {
        loginName: [
          {
            required: true,
            message: "请输入正确的邮箱",
            // eslint-disable-next-line
            pattern: /^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/,
          },
        ],
        realName: [
          {
            required: true,
            message: "请输入正确的姓名",
          },
        ],
        mobileNo: [
          {
            required: true,
            message: "请输入正确的电话号码",
            pattern: /^1[0-9]{10}$/,
          },
        ],
        loginPwd: [
          {
            required: true,
            message: "请输入正确的密码",
            pattern: /^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{8,16}$/,
          },
        ],
        roleIds: [
          {
            type: "array",
            required: true,
            message: "请选择角色权限",
            trigger: "change",
          },
        ],
      },
      form: {
        id: null,
        createTime: "",
        loginName: "",
        loginPwd: "",
        realName: "",
        mobileNo: "",
        remark: "",
        status: null,
        type: null,
        departmentId: null,
        departmentName: "",
        roleIds: [],
      },
      departmentList: [],
      roleList: [],
    };
  },
  mounted() {
    this.type = this.$route.query.type;
    if (this.type == "edit") {
      this.load = true;
      getSingleStaff({ operatorId: this.$route.query.id }).then((response) => {
        this.form = response.data;
        this.load = false;
      });
    }
    getDepartmentList().then((response) => {
      this.departmentList = response.data;
    });
    getAllRoleList().then((response) => {
      this.roleList = response.data;
    });
  },
  methods: {
    selectDepartment(name, id) {
      this.form.departmentId = id;
      this.form.departmentName = name;
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.type == "create") {
            createStaff(this.form).then((response) => {
              this.$message.success("创建成功");
              this.$router.push("/system/staffManagement");
            });
          } else {
            editStaff(this.form).then((response) => {
              this.$message.success("编辑成功");
              this.$router.push("/system/staffManagement");
            });
          }
        }
      });
    },
    cancel() {
      this.$router.push("/system/staffManagement");
    },
  },
};
</script>

<style lang="scss" scoped>
.box-container {
  padding: 20px;
  background: #fff;
  display: flex; // 调试用
  .el-form {
    width: 400px;
    margin-left: 30px;

    ::v-deep .el-form-item__label {
      font-weight: normal;
    }
  }

  .footer-container {
    padding-left: 120px;
  }
}
</style>
