<template>
  <div class="department-container">
    <div class="department-tree">
      <el-tree :data="departmentTree" node-key="id" default-expand-all :props="defaultProps">
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <el-button type="text" @click="showDepartmentInfo(data)">{{ node.label }}</el-button>
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              <i class="el-icon-setting"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="createDepartment(data)">新建下级部门</el-dropdown-item>
              <el-dropdown-item v-if="data.id != 0" @click.native="editDepartment(data)">编辑部门</el-dropdown-item>
              <el-dropdown-item v-if="data.id != 0" @click.native="deleteDepartment(data)">删除部门</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </span>
      </el-tree>
    </div>

    <div class="box-container">
      <el-alert
        v-if="noneLeaderDepartmentList.length > 0"
        :title="noneLeaderDepartmentList.length + '个部门未设置负责人，请点击查看'"
        type="warning"
        show-icon
        @click.native="isShowNoneLeaderTable = true"
      ></el-alert>
      <!-- 查看部门员工页面 -->
      <div class="content-container" v-show="!isShowNoneLeaderTable">
        <div class="department-info">
          <span>部门：</span>
          <span>{{ curDepartmentName }}</span>
          <span>负责人：</span>
          <span>{{ curDepartmentLeader }}</span>
        </div>
        <el-table :data="curStaffList">
          <el-table-column label="账号" prop="loginName"></el-table-column>
          <el-table-column label="员工姓名" prop="realName"></el-table-column>
          <el-table-column label="联系方式" prop="mobileNo"></el-table-column>
          <el-table-column label="主属部门" prop="departmentName"></el-table-column>
        </el-table>
      </div>
      <!-- 查看未设置部门负责人页面 -->
      <div class="content-container" v-show="isShowNoneLeaderTable">
        <el-table :data="noneLeaderDepartmentList">
          <el-table-column label="部门名称" prop="departmentName"></el-table-column>
          <el-table-column label="上级部门" prop="parentName"></el-table-column>
          <el-table-column label="创建时间" prop="createTime"></el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="setDepartmentLeader(scope.row)">选择部门负责人</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!--部门dialog-->
    <el-dialog
      :visible.sync="isShowDepartmentDialog"
      :close-on-click-modal="false"
      :before-close="closeDepartmentForm"
      :title="departmentDialogTitle"
      width="600px"
    >
      <el-form
        ref="departmentForm"
        :model="departmentForm"
        :rules="departmentRules"
        label-width="100px"
      >
        <el-form-item label="部门名称" prop="departmentName">
          <el-input v-model="departmentForm.departmentName" />
        </el-form-item>
        <el-form-item label="上级部门" prop="parentName">
          <el-input v-model="departmentForm.parentName" disabled />
        </el-form-item>
        <el-form-item label="编号" prop="number">
          <el-input v-model="departmentForm.number" />
        </el-form-item>
        <el-form-item label="部门负责人">
          <el-select clearable v-model="departmentForm.leaderId">
            <el-option
              v-for="leader in leaderList"
              :key="leader.id"
              :label="leader.realName"
              :value="leader.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="closeDepartmentForm">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submitDepartmentForm">确认</el-button>
      </div>
    </el-dialog>

    <!--设置部门负责人-->
    <el-dialog
      :visible.sync="isShowSetLeaderDialog"
      :loading="setLeaderDialogLoading"
      :before-close="closeSetLeaderForm"
      title="选择部门负责人"
      width="600px"
    >
      <el-form
        ref="departmentForm"
        :model="setLeaderForm"
        :rules="setLeaderRules"
        label-width="100px"
      >
        <el-form-item label="部门负责人" prop="leaderId">
          <el-select clearable v-model="setLeaderForm.leaderId">
            <el-option
              v-for="leader in leaderList"
              :key="leader.id"
              :label="leader.realName"
              :value="leader.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button :loading="loading" type="primary" @click="submitSetLeaderForm">确认</el-button>
        <el-button type="text" @click="closeSetLeaderForm">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDepartmentList } from "@/api/system";
import { convert } from "../../../utils";
import {
  createDepartment,
  deleteDepartment,
  getNoneLeaderDepartmentList,
  getStaffListByDepartment,
  getStaffListByOnlyDepartment,
  editDepartment,
  setDepartmentLeader,
  getNoDepartmentStaff,
} from "../../../api/system";

let id = 1000;
export default {
  name: "PmsDepartmentManagement",
  data() {
    return {
      loading: false,
      leaderList: [],

      // 未设置负责人部门表格
      isShowNoneLeaderTable: false,
      noneLeaderDepartmentList: [],
      // 设置部门负责人弹框
      isShowSetLeaderDialog: false,
      setLeaderDialogLoading: false,
      setLeaderForm: {
        departmentId: null,
        leaderId: null,
        leaderName: "",
      },
      setLeaderRules: {
        leaderId: [
          { required: true, message: "请选择部门负责人", trigger: "blur" },
        ],
      },

      // 部门树形结构
      departmentTree: [],
      defaultProps: {
        children: "children",
        label: "departmentName",
      },
      // 当前选中的部门及其员工
      curDepartmentName: "",
      curDepartmentLeader: "",
      curStaffList: [],
      // 部门dialog
      isShowDepartmentDialog: false,
      departmentDialogType: "create",
      departmentDialogTitle: "新建下级部门",
      departmentForm: {
        id: null,
        departmentName: "",
        parentId: null,
        parentName: "",
        number: "",
        leaderId: null,
        leaderName: "",
      },
      departmentRules: {
        departmentName: [
          { required: true, message: "请输入部门名称", trigger: "blur" },
        ],
        parentName: [
          { required: true, message: "请选择上级部门", trigger: "change" },
        ],
        number: [
          { required: true, message: "请填写部门编号", trigger: "blur" },
        ],
      },
    };
  },
  mounted() {
    this.getNoneLeaderDepartment();
    this.getDepartmentList();
  },
  methods: {
    getNoneLeaderDepartment() {
      getNoneLeaderDepartmentList().then((response) => {
        this.noneLeaderDepartmentList = response.data;

        if (this.noneLeaderDepartmentList.length == 0) {
          this.isShowNoneLeaderTable = false;
        }
      });
    },
    getDepartmentList() {
      getDepartmentList().then((response) => {
        let departmentTree = convert(response.data, null);
        this.departmentTree = departmentTree;

        if (!this.isShowNoneLeaderTable) {
          this.showDepartmentInfo(this.departmentTree[0]);
        }
      });
    },
    showDepartmentInfo(node) {
      this.isShowNoneLeaderTable = false;
      // 显示部门信息
      this.curDepartmentName = node.departmentName;
      this.curDepartmentLeader = node.leaderName;
      getStaffListByDepartment({ departmentId: node.id || 0 }).then(
        (response) => {
          this.curStaffList = response.data;
        }
      );
    },
    createDepartment(data) {
      this.isShowDepartmentDialog = true;
      this.departmentDialogType = "create";
      this.departmentDialogTitle = "新建下级部门";
      getNoDepartmentStaff().then((response) => {
        this.leaderList = response.data;
      });

      this.departmentForm.id = null;
      this.departmentForm.departmentName = "";
      this.departmentForm.parentId = data.id;
      this.departmentForm.parentName = data.departmentName;
      this.departmentForm.number = "";
      this.departmentForm.leaderId = null;
      this.departmentForm.leaderName = "";
    },
    editDepartment(data) {
      this.isShowDepartmentDialog = true;
      this.departmentDialogType = "edit";
      this.departmentDialogTitle = "编辑部门";
      getStaffListByOnlyDepartment({ departmentId: data.id }).then(
        (response) => {
          this.leaderList = response.data;
        }
      );

      this.departmentForm.id = data.id;
      this.departmentForm.departmentName = data.departmentName;
      this.departmentForm.parentId = data.parentId;
      this.departmentForm.parentName = data.parentName;
      this.departmentForm.number = data.number;
      this.departmentForm.leaderId =
        data.leaderId === -1 ? null : data.leaderId;
      this.departmentForm.leaderName = data.leaderName;
    },
    closeDepartmentForm() {
      this.isShowDepartmentDialog = false;
      this.$refs.departmentForm.resetFields();
    },
    submitDepartmentForm() {
      this.$refs.departmentForm.validate((valid) => {
        if (valid) {
          if (this.departmentDialogType == "create") {
            createDepartment(this.departmentForm).then((response) => {
              this.$message.success("创建部门成功");
              this.isShowDepartmentDialog = false;
              this.getNoneLeaderDepartment();
              this.getDepartmentList();
            });
          } else if (this.departmentDialogType == "edit") {
            editDepartment(this.departmentForm).then((response) => {
              this.$message.success("修改部门成功");
              this.isShowDepartmentDialog = false;
              this.getNoneLeaderDepartment();
              this.getDepartmentList();
            });
          }
        }
      });
    },
    deleteDepartment(data) {
      this.$confirm(
        "部门停用后将不能再接收或发出工作信息内容，原部门员工将自动归入未分配部门中，是否继续停用该部门",
        "停用部门",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
      }).then(() => {
        deleteDepartment({
          departmentId: data.id,
        })
          .then((response) => {
            this.$message.success("删除成功");
            this.getNoneLeaderDepartment();
            this.getDepartmentList();
          })
          .catch((err) => {});
      });
    },
    setDepartmentLeader(row) {
      this.isShowSetLeaderDialog = true;
      this.setLeaderDialogLoading = true;
      getStaffListByOnlyDepartment({
        departmentId: row.id,
      })
        .then((response) => {
          this.leaderList = response.data;
          this.setLeaderForm.departmentId = row.id;
          this.setLeaderForm.leaderId =
            row.leaderId === -1 ? null : row.leaderId;
          this.setLeaderForm.leaderName = row.leaderName;
          this.setLeaderDialogLoading = false;
        })
        .catch((err) => {
          this.isShowSetLeaderDialog = false;
          this.setLeaderDialogLoading = false;
        });
    },
    submitSetLeaderForm(row) {
      this.$refs.departmentForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          setDepartmentLeader(this.setLeaderForm)
            .then((response) => {
              this.$message.success(response.data);
              this.isShowSetLeaderDialog = false;
              this.loading = false;
              this.getNoneLeaderDepartment();
              this.getDepartmentList();
            })
            .catch((err) => {
              this.loading = false;
            });
        }
      });
    },
    closeSetLeaderForm(row) {
      this.isShowSetLeaderDialog = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.department-container {
  display: flex;
  .department-tree {
    width: 200px;
  }
  .search-page-container {
    padding: 0 10px;
    .el-alert {
      margin-bottom: 20px;
    }
    .content-container {
      margin-top: 0;
      .department-info {
        margin-bottom: 20px;
      }
    }
  }
}
</style>
