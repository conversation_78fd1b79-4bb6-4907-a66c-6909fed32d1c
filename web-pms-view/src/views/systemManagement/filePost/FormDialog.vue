<template>
  <el-dialog
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="cancel"
    top="30px"
    title="推送任务">
    <el-form
      style="margin: 0 auto; width: 500px;"
      ref="form"
      :model="form"
      :rules="rules"
      label-width="150px">
      <el-form-item
        label="关联商户："
        prop="mchNo">
        <el-select
          v-model="form.mchNo"
          clearable
          filterable>
          <el-option
            v-for="item in mchList"
            :key="item.mchNo"
            :label="item.mchName"
            :value="item.mchNo"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="推送内容："
        prop="pushType">
        <el-select
          v-model="form.pushType"
          clearable
          filterable>
          <el-option
            v-for="item in $dict('PushTypeEnum')"
            :key="item.code"
            :label="item.desc"
            :value="Number(item.code)"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="服务器类型："
        prop="serverType">
        <el-select
          v-model="form.serverType"
          clearable>
          <el-option
            v-for="item in $dict('ServerTypeEnum')"
            :key="item.code"
            :label="item.desc"
            :value="Number(item.code)"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="SFTP服务器地址："
        prop="sftpIp">
        <el-input
          v-model="form.sftpIp"
          clearable></el-input>
      </el-form-item>
      <el-form-item
        label="服务器端口："
        prop="sftpPort">
        <el-input
          v-model="form.sftpPort"
          clearable></el-input>
      </el-form-item>
      <el-form-item
        label="文件上传目录："
        prop="sftpPath">
        <el-input
          v-model="form.sftpPath"
          clearable></el-input>
      </el-form-item>
      <el-form-item
        label="用户名："
        prop="username">
        <el-input
          v-model="form.username"
          clearable></el-input>
      </el-form-item>
      <el-form-item
        label="密码："
        prop="password">
        <el-input
          v-model="form.password"
          clearable></el-input>
      </el-form-item>
      <el-form-item
        label="通知地址："
        prop="callbackUrl">
        <el-input
          clearable
          v-model="form.callbackUrl"></el-input>
      </el-form-item>
    </el-form>

    <template v-slot:footer>
      <el-button
        type="primary"
        @click="onSubmit">确定
      </el-button>
      <el-button @click="cancel">取消</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { toPromise } from "@/utils";
import { getAllMerchant } from "@/api/merchant";
import { addFilePush, updateFilePush } from "@/api/file";


export default {
  name: 'FilePostDialog',
  data() {
    return {
      mchList: [],
      visible: false,
      form: {},
      rules: {
        mchNo: [{ required: true, message: "请选择商户", trigger: 'change' }],
        pushType: [{ required: true, message: "请选择推送内容", trigger: 'change' }],
        serverType: [{ required: true, message: "请选择推送服务器类型", trigger: 'change' }],
        sftpIp: [{ required: true, message: "请填写服务器地址", trigger: 'change' }],
        sftpPort: [{ required: true, message: "请填写服务器端口", trigger: 'change' }],
        sftpPath: [{ required: true, message: "请填写文件上传目录", trigger: 'change' }],
        username: [{ required: true, message: "请填写服务器用户名", trigger: 'change' }],
        password: [{ required: true, message: "请填写服务器密码", trigger: 'change' }],
      }
    }
  },
  computed: {
    isAdd() {
      return !this.form.id
    }
  },
  methods: {
    toggle(status = false) {
      this.visible = status
      if (status && this.mchList.length === 0) {
        this.getMchList()
      }
    },
    async onSubmit() {
      const [err] = await toPromise(this.$refs.form.validate())
      if (err) return
      const api = this.isAdd ? addFilePush : updateFilePush
      const { data } = await api(this.form)
      data && this.$message.success(data)
      await this.$nextTick()
      this.$emit('update')
      this.cancel()
    },
    open() {
      this.toggle(true)
    },
    cancel() {
      this.form = {}
      this.$refs.form.resetFields()
      this.toggle(false)
    },
    setData(row) {
      this.form = row
    },
    async getMchList() {
      const { data } = await getAllMerchant({ merchantType: 100 })
      this.mchList = data
    },
  },
}
</script>

<style
  lang="scss"
  scoped>

</style>
