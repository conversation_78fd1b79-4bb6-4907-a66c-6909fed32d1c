<template>
  <div class="box-container">
    <el-button
      type="primary"
      @click="add">新增
    </el-button>
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">商户名称：</span>
          <el-select
            v-model="searchForm.mchNo"
            clearable
            filterable>
            <el-option
              v-for="item in mchList"
              :key="item.mchNo"
              :label="item.mchName"
              :value="item.mchNo"></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">推送内容：</span>
          <el-select
            v-model="searchForm.pushType"
            clearable>
            <el-option
              v-for="item in $dict('PushTypeEnum')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)">查询
        </el-button>
        <el-button @click="resetForm">重置</el-button>
      </div>
    </div>

    <div class="content-container">
      <el-table :data="list">
        <el-table-column label="商户名称">
          <template v-slot="{row}">
            {{ row.mchNo }}<br>{{ row.mchName }}
          </template>
        </el-table-column>
        <el-table-column
          label="推送内容"
          prop="pushType">
          <template v-slot="{row, column}">
            {{ $dictCode('PushTypeEnum', row[column.property]).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="服务器地址"
          prop="sftpIp">
          <template v-slot="{row}">
            {{ row.sftpIp }}:{{ row.sftpPort }}
          </template>
        </el-table-column>
        <el-table-column
          label="用户名"
          prop="username"></el-table-column>
        <el-table-column
          label="通知地址"
          prop="callbackUrl"></el-table-column>
        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="editRow(row)">编辑
            </el-button>
            <el-button
              type="text"
              class="red-btn"
              @click="deleteRow(row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :page-sizes="[10,15,30]"
        :total="page.totalRecord"
        :page-size.sync="page.pageSize"
        :current-page.sync="page.pageCurrent"
        @size-change="search(true)"
        @current-change="search()"
      />
    </el-footer>

    <form-dialog
      ref="form"
      @update="search()"></form-dialog>
  </div>
</template>

<script>
import FormDialog from "./FormDialog.vue";
import { getAllMerchant } from "@/api/merchant";
import { toPromise } from "@/utils";
import { deleteFilePush, filePushList } from "@/api/file";

export default {
  components: {
    FormDialog,
  },
  data() {
    return {
      mchList: [],
      searchForm: {},
      list: [],
      page: {
        pageCurrent: 1,
        pageSize: 10,
        totalRecord: 0,
      }
    }
  },
  mounted() {
    this.getMchList()
    this.search()
  },
  methods: {
    async search(init) {
      if (init) {
        this.page.pageCurrent = 1
      }
      const { data: { data, totalRecord } } = await filePushList({
        ...this.page,
        ...this.searchForm,
      })
      this.list = data
      this.page.totalRecord = totalRecord
    },
    add() {
      this.$refs.form.open()
    },
    resetForm() {
      this.searchForm = {}
    },
    editRow(row) {
      let data = JSON.parse(JSON.stringify(row))
      this.$refs.form.setData(data)
      this.$refs.form.open()
    },
    async deleteRow({ id }) {
      const [err] = await toPromise(this.$confirm('此操作将删除该条数据，是否继续？', '提示', {
        type: 'warning'
      }))
      if (err) return
      const { data } = await deleteFilePush({ id })
      data && this.$message.success(data)
      this.search()
    },
    async getMchList() {
      const { data } = await getAllMerchant({ merchantType: 100 })
      this.mchList = data
    },
  },
}
</script>

<style
  lang="scss"
  scoped>

</style>
