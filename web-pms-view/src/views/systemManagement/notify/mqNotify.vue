<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">消息topic名称：</span>
            <el-input v-model="searchForm.topic"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">mq返回id：</span>
            <el-input v-model="searchForm.msgId"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">消息tags属性：</span>
            <el-input v-model="searchForm.tags"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">消息keys属性：</span>
            <el-input v-model="searchForm.keys"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">商户编号：</span>
            <el-input v-model="searchForm.merchantNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">流水号：</span>
            <el-input v-model="searchForm.trxNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">通知类型：</span>
            <el-select
              clearable
              v-model="searchForm.notifyType"
            >
              <el-option
                v-for="(item, index) in $dict('NotifyTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">选择时间：</span>
            <date-picker
              type="datetimerange"
              ref="datePicker"
              v-model="timeRange"
              fast-time="today"
              @change="getTimeRange($event)"
            >
            </date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="flex-item search-btn-group">
            <el-button
              type="primary"
              @click="search(true)"
            >查询</el-button>
            <el-button
              type="text"
              @click="clearField"
            >清空搜索条件</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="function-bar">
      <el-button
        v-permission="'notify:msg:batchReissueMessage'"
        type="primary"
        @click="multiBatchReissue"
      >批量补发</el-button>
      <el-button
        style="margin-left: 20px;"
        v-permission="'notify:msg:batchReissueMessageNew'"
        type="primary"
        @click="multiBatchReissueNew"
      >后台批量补发</el-button>
    </div>
    <div class="content-container">
      <el-table
        :data="response.data"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column
          label="序号"
          type="index"
          :index="getIndex"
          width="50"
        ></el-table-column>
        <el-table-column
          label="创建时间"
          prop="createTime"
          width="130"
        >
          <template v-slot="{row, column}">
            <p v-html="renderTime(row[column['property']])"></p>
          </template>
        </el-table-column>

        <el-table-column
          label="创建日期"
          prop="createDate"
        >
          <template v-slot="{row}">
            {{ row.createDate | dateFilter}}
          </template>
        </el-table-column>

        <el-table-column
          label="消息topic名称"
          prop="topic"
        ></el-table-column>
        <el-table-column
          label="mq返回id"
          prop="msgId"
        ></el-table-column>
        <el-table-column
          label="消息的tags属性"
          prop="tags"
        ></el-table-column>
        <el-table-column
          label="消息的keys属性"
          prop="keys"
        ></el-table-column>
        <el-table-column
          label="消息体"
          prop="message"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="商户编号"
          prop="merchantNo"
        ></el-table-column>
        <el-table-column
          label="流水号"
          prop="trxNo"
        ></el-table-column>
        <el-table-column
          label="通知类型"
          prop="notifyType"
        >
          <template v-slot="{row}">
            <span>{{ $dictCode('NotifyTypeEnum', row.notifyType).desc }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 50, 100, 200, 500]"
        :total="response.totalRecord"
        @size-change="handleSizeChange"
        @current-change="handleCurrnetChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
  import { getMQMessage, batchReissue, batchReissueNew } from '@/api/system';

  export default {
    name: 'PmsMqNotify',
    data() {
      return {
        searchForm: { // 搜索表单
          beginDate: '',
          endDate: '',
          topic: '',
          msgId: '',
          tags: '',
          keys: '',
          merchantNo: '',
          trxNo: '',
          notifyType: '',
        },
        response: { // 查询结果
          data: [],
          totalRecord: 0,
        },
        pageSize: 10,
        pageCurrent: 1,

        timeRange: [],

        selection: [],
      }
    },
    filters: {
      dateFilter(val) {
        return val.split(' ')[0]
      }
    },
    mounted() {
      this.search();
    },
    methods: {
      clearField() {
        this.searchForm = {
          beginDate: '',
          endDate: '',
          topic: '',
          msgId: '',
          tags: '',
          keys: '',
          merchantNo: '',
          trxNo: '',
          notifyType: '',
        }
        this.$refs.datePicker.resetTime();
      },
      getTimeRange(val) {
        this.searchForm['beginDate'] = val && val[0];
        this.searchForm['endDate'] = val && val[1];
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.search(true);
      },
      handleCurrnetChange(val) {
        this.pageCurrent = val;
        this.search();
      },
      async search(initFlag) {
        if (initFlag) {
          this.pageCurrent = 1;
        }
        const { data } = await getMQMessage({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent,
        });
        this.response = data;
      },
      async multiBatchReissue() {
        if (this.selection.length == 0) {
          return this.$message.error('请选择补发的MQ消息');
        }
        let idList = this.selection.map(item => item.id);
        const { data } = await batchReissue({ idList });
        this.$message.success(data);

      },
      handleSelectionChange(val) {
        this.selection = val;
      },
      async multiBatchReissueNew() {
        const { data } = await batchReissueNew(this.searchForm);
        data && this.$message.success(data);
      }
    }
  }
</script>

<style lang="scss" scoped>
  .box-container {
    .flex-item__label {
      width: 120px;
    }

    .function-bar {
      margin-top: 16px;
    }
  }
</style>
