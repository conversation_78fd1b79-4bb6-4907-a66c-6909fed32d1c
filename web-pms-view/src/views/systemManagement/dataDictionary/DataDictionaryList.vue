<template>
  <div class="box-container">
    <el-button
      type="primary"
      class="create-btn"
      @click="addDataDictionary">
      <i class="el-icon-plus" />创建数据字典
    </el-button>
    <div class="search-container">

      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">数据名称：</span>
            <el-input
              placeholder="模糊查询"
              clearable
              v-model="searchParam.dataNameLike"
              @keyup.enter.native="refreshDataDictionaryList"
            />
          </div>
          <div class="flex-item-main">
            <span class="flex-item__label">描述：</span>
            <el-input
              placeholder="模糊查询"
              clearable
              v-model="searchParam.remark"
            />
          </div>
          <div class="flex-item-main">
            <span class="flex-item__label">系统标识：</span>
            <el-select
              clearable
              v-model="searchParam.systemType">
              <el-option
                v-for="({code,desc}) in $dict('SystemTypeEnum')"
                :key="parseInt(code)"
                :label="desc"
                :value="parseInt(code)" />
            </el-select>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              class="el-button--primary"
              @click="refreshDataDictionaryList">
              <i class="el-icon-search" />查询
            </el-button>
            <el-button @click="exportDataDictionary">导出已选字典</el-button>
            <el-button
              :loading="loading"
              type="text"
              @click="getExportList">查看已导出列表
            </el-button>
            <el-button type="text">
              <el-upload
                :action="''"
                :auto-upload="false"
                :file-list="fileList"
                :show-file-list="false"
                accept=".xls,.xlsx"
                :on-change="importDataDictionary"
              >
                <el-button type="text">导入字典</el-button>
              </el-upload>
            </el-button>

          </div>
        </div>
      </div>

    </div>

    <el-main class="main-page-content">
      <el-table
        :data="pageResult.data"
        row-key="id"
        @selection-change="handleSelect">
        <el-table-column type="selection"></el-table-column>
        <el-table-column
          label="数据名称"
          prop="dataName" />
        <el-table-column
          label="字典信息"
          prop="dataInfo"
          :show-overflow-tooltip="true" />
        <el-table-column
          label="系统标识"
          prop="systemType"
          :formatter="(row)=>$dictCode('SystemTypeEnum',row.systemType).desc" />
        <el-table-column
          label="描述"
          prop="remark" />
        <el-table-column
          label="操作"
          v-slot="{row}">
          <el-button
            type="text"
            size="small"
            @click="editDataDictionary(row)">编辑
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteDataDictionary(row)">删除
          </el-button>
        </el-table-column>
      </el-table>
    </el-main>

    <el-footer class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :page-sizes="[10,50,100]"
        :total="pageResult.totalRecord"
        :page-size.sync="pageParam.pageSize"
        :current-page.sync="pageParam.pageCurrent"
        @size-change="refreshDataDictionaryList"
        @current-change="refreshDataDictionaryList" />
    </el-footer>
    <data-dictionary-form
      ref="form"
      v-on:success="refreshDataDictionaryList" />

    <export-record ref="exportRecord"></export-record>
  </div>
</template>

<script>
import DataDictionaryForm from "./DataDictionaryForm";
import {
  listDataDictionary,
  getDataDictionaryVOById,
  deleteDataDictionary,
  exportDataDictionary,
  importDataDictionary
} from '@/api/dataDictionary'
import ExportRecord from '@/components/ExportRecord'

export default {
  name: "DataDictionaryList",
  components: {
    DataDictionaryForm,
    ExportRecord,
  },
  data() {
    return {
      searchParam: {},
      pageParam: { pageCurrent: 1, pageSize: 10 },
      pageResult: {},

      selection: [],

      fileList: [],
      loading: false,
    };
  },
  methods: {
    refreshDataDictionaryList(initFlag) {
      if (initFlag === true) {
        this.pageParam.pageCurrent = 1
      }
      listDataDictionary(
        this.searchParam,
        this.pageParam
      )
        .then(({ data }) => {
          this.pageResult = data;
        });
    },
    addDataDictionary() {
      this.$refs.form.actionType = "ADD"
      this.$refs.form.form = { id: "", dataInfo: [] };
      this.$refs.form.show = true;
    },
    editDataDictionary(row) {
      this.$refs.form.actionType = "EDIT"
      getDataDictionaryVOById({
        id: row.id
      })
        .then(({ data }) => {
          data.dataInfo.forEach((item, index) => {
            item.index = index
          })
          this.$refs.form.form = {
            id: data.id,
            dataName: data.dataName,
            dataInfo: data.dataInfo,
            remark: data.remark,
            systemType: data.systemType
          };
          this.$refs.form.show = true;
        })
    },
    deleteDataDictionary(row) {
      this.$confirm("确定删除吗?")
        .then(() => deleteDataDictionary({
          id: row.id
        }))
        .then(() => this.refreshDataDictionaryList());
    },
    handleSelect(select) {
      this.selection = select
    },
    async exportDataDictionary() {
      if (!this.selection.length) {
        return this.$message.error('请选择字典项')
      }
      const idList = this.selection.map(item => item.id);
      await exportDataDictionary({ idList });
      this.$message.success('导出成功')
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('28');
    },
    async importDataDictionary(file) {
      let fileRaw = file.raw;
      this.loading = true;
      let form = new FormData();
      form.append('file', fileRaw);
      await importDataDictionary(form).finally(() => {
        this.loading = false;
      })
      this.$message.success('操作成功');
      this.refreshDataDictionaryList();
    }
  },
  mounted() {
    this.refreshDataDictionaryList();
  }
};
</script>
