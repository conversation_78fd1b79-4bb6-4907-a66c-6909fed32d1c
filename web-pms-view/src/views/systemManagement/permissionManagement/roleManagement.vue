<template>
  <div class="box-container" v-loading="loading">
    <el-button type="primary" class="create-btn" @click="addRolePage" v-permission="'pms:role:add'">新增角色</el-button>
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">角色：</span>
            <el-input v-model="searchParam.roleName" />
          </div>
        </div>
      </div>
      <div class="search-wrapper">
        <div class="search-btn-group">
          <el-button type="primary" @click="refreshList">搜索</el-button>
        </div>
      </div>
    </div>
    <el-main class="main-page-content">
      <el-table :data="pageResult.data">
        <el-table-column type="index" :index="getIndex" />
        <el-table-column label="角色名称" prop="roleName">
          <template v-slot="{row}">
            {{ row.roleName }}
            <el-tag style="margin-left: 16px" type="info" v-if="row.roleType != 2">{{ $dictCode('RoleTypeEnum', row.roleType).desc }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="remark" />
        <el-table-column label="员工数量" prop="employerCount">
          <template v-slot="{row}">
            <router-link :to="'/system/staffManagement?roleId=' + row.id" class="func-content">
              {{ row.employerCount || 0}}
            </router-link>
          </template>
        </el-table-column>
        <el-table-column label="操作" v-slot="{row}">
          <template v-if="row.roleType != 0">
            <el-button
              type="text"
              size="small"
              @click="assignFunctionPage(row)"
              v-permission="'pms:role:assignFunction'"
            >设置权限</el-button>
            <el-button
              type="text"
              size="small"
              @click="editRolePage(row)"
              v-permission="'pms:role:edit'"
              v-if="row.roleType == 2"
            >编辑</el-button>
            <el-button
              type="text"
              size="small"
              @click="deletePmsRole(row)"
              v-permission="'pms:role:delete'"
              v-if="row.roleType == 2"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-main>

    <el-footer class="main-page-footer">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10,50,100]"
        :total="pageResult.totalRecord"
        :page-size.sync="pageParam.pageSize"
        :current-page.sync="pageParam.pageCurrent"
        @size-change="refreshList"
        @current-change="refreshList"
      />
    </el-footer>

    <role-form @success="refreshList" ref="roleForm" />
    <assign-function-form ref="assignForm" />
  </div>
</template>

<script>
import roleForm from "./roleForm";
import assignFunctionForm from "./assignFunctionForm";
import { getRoleList, deleteRole } from "../../../api/system";

export default {
  name: "PmsRoleManagement",
  components: {
    roleForm,
    assignFunctionForm,
  },
  data() {
    return {
      loading: false,
      searchParam: {},
      pageParam: { pageCurrent: 1, pageSize: 10 },
      pageResult: {},
    };
  },
  methods: {
    refreshList() {
      this.loading = true;
      getRoleList({
        ...this.pageParam,
        ...this.searchParam,
      })
        .then(({ data }) => {
          this.pageResult = data;
        })
        .finally(() => {
          this.loading = false;
        });
    },

    addRolePage() {
      this.$refs.roleForm.actionType = "ADD";
      this.$refs.roleForm.form = {};
      this.$refs.roleForm.show = true;
    },

    editRolePage(row) {
      this.$refs.roleForm.actionType = "EDIT";
      this.$refs.roleForm.form = { ...row };
      this.$refs.roleForm.show = true;
    },

    deletePmsRole(role) {
      this.$confirm("确认删除角色?")
        .then(() => deleteRole({id: role.id}))
        .then(({ data }) => {
          this.$message.success(data);
          this.refreshList();
        });
    },

    assignFunctionPage(row) {
      this.$refs.assignForm.initAndShow(row.id);
    },

    getIndex(index) {
      return (this.pageParam.pageCurrent - 1) * this.pageParam.pageSize + index + 1;
    },
  },

  mounted() {
    this.refreshList();
  },
};
</script>
