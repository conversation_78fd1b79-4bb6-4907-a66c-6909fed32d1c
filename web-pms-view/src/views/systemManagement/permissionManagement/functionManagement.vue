<template>
  <div class="box-container">
    <div class="search-container">
      <div class="function-bar">
        <el-button
          type="primary"
          size="small"
          @click="refreshFunctionList">
          <i class="el-icon-search" />刷新
        </el-button>
        <el-button
          size="small"
          @click="addFunctionPage"
          v-permission="'pms:function:add'">
          <i class="el-icon-plus" />添加顶级菜单
        </el-button>
        <el-input
          size="small"
          v-model="search"
          placeholder="输入功能名称搜索">
          <el-button
            slot="append"
            @click="searchFunc"
          >搜索
          </el-button>
        </el-input>
      </div>
      <div class="func-bar">
        <el-button
          type="primary"
          size="small"
          @click="exportFunc"
          :loading="loading">
          导出功能列表
        </el-button>
        <el-upload
          style="display: inline-block;"
          :action="''"
          :auto-upload="false"
          :file-list="fileList"
          :show-file-list="false"
          accept=".xls,.xlsx"
          :on-change="importFunc"
        >
          <el-button
            size="small"
            :loading="loading">导入功能列表
          </el-button>
        </el-upload>
        <el-button
          type="text"
          @click="getExportList">查看已导出列表
        </el-button>
      </div>
    </div>

    <el-main class="content-container">
      <el-table
        ref="table"
        :data="treeData"
        row-key="id"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      >
        <el-table-column
          label="功能名称"
          prop="name" />
        <el-table-column
          label="功能编号"
          prop="number" />
        <el-table-column
          label="URL"
          prop="url">
          <template v-slot="{row}">
            <div v-clipboard="row.url">{{ row.url }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="权限标识"
          prop="permissionFlag">
          <template v-slot="{row}">
            <div v-clipboard="row.permissionFlag">{{ row.permissionFlag }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="功能类型"
          prop="functionType"
          :formatter="row => (row.functionType===1&&'菜单项')||(row.functionType===2&&'操作项')"
        />
        <el-table-column
          label="操作"
          v-slot="{row}">
          <el-button
            type="text"
            size="small"
            @click="editFunctionPage(row)"
            v-permission="'pms:function:edit'"
          >编辑
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteFunction(row)"
            v-permission="'pms:function:delete'"
          >删除
          </el-button>
          <el-button
            type="text"
            size="small"
            @click="addFunctionPage(row)"
            v-if="row.functionType===1"
            v-permission="'pms:function:add'"
          >添加子功能
          </el-button>
        </el-table-column>
      </el-table>
    </el-main>
    <function-form
      :edit-row="editRow"
      ref="form"
      v-on:success="refreshFunctionList" />
    <export-record ref="exportRecord"></export-record>
  </div>
</template>

<script>
import functionForm from "./functionForm";
import ExportRecord from '@/components/ExportRecord'
import { autoNext } from '@/utils'
import { importFunc, exportFunc, deleteFunc, getFuncList } from '@/api/system';

function sortByNum(arr) {
  if (arr.length == 0) return;
  arr.sort((a, b) => {
    if (a.number < b.number) {
      return -1
    } else {
      return 1
    }
  })
  arr.forEach(item => {
    if (item.children) {
      sortByNum(item.children);
    }
  })
}

const buildTreeData = function (pid, pidGroup, resultArr, isCircle, parent) {
  if (!pidGroup[pid]) {
    return;
  }
  pidGroup[pid].forEach((f) => {
    if (isCircle) {
      parent && (f.parent = parent)
    }
    resultArr.push(f);
    if (pidGroup[f.id]) {
      f.children = [];
      buildTreeData(f.id, pidGroup, f.children, isCircle, f);
    }
  });
};

function findMatchedRow(key, list, prop) {
  let match = [];
  list.forEach(item => {
    if (item[prop].includes(key)) {
      match.push(item);
      findParent(item, match);
    }
    if (item.children) {
      match = match.concat(findMatchedRow(key, item.children, prop))
    }
  })
  return match;
}

function findParent(child, list) {
  if (!child.parent) return;
  const parent = child.parent
  if (list.indexOf(parent) == -1) {
    list.push(parent);
  }
  findParent(parent, list)
}

export default {
  name: "PmsFunctionManagement",
  components: {
    functionForm,
    ExportRecord,
  },
  data() {
    return {
      functions: [],

      search: '',
      match: [],

      fileList: [],
      loading: false,

      editRow: {},
    };
  },
  methods: {
    async refreshFunctionList() {
      const { data } = await getFuncList()
      this.functions = data;
    },
    addFunctionPage(row) {
      this.$refs.form.actionType = "ADD";
      let newNumber = autoNext(row.number, (row.children && row.children[row.children.length - 1].number));
      const editRow = {
        parentId: row && row.id,
        parentName: row && row.name,
        number: newNumber
      }
      this.editRow = editRow;
      this.$refs.form.show = true;
    },
    editFunctionPage(row) {
      this.$refs.form.actionType = "EDIT";
      let tempRow = { ...row };
      delete tempRow.parent;
      this.editRow = tempRow;
      this.$refs.form.show = true;
    },
    async deleteFunction(row) {
      const valid = await this.$confirm("确定删除吗?")
      if (valid) {
        await deleteFunc({
          id: row.id
        })
        this.refreshFunctionList()
      }
    },
    searchFunc() {
      if (!this.search) return;
      this.match.forEach(item => {
        this.$refs.table.toggleRowExpansion(item, false)
      })
      this.match = findMatchedRow(this.search, this.circleTreeData, 'name');
      this.match.forEach(item => {
        this.$refs.table.toggleRowExpansion(item, true)
      })
    },
    async exportFunc() {
      const { data } = await exportFunc();
      data && this.$message.success(data)
    },
    async importFunc(file) {
      let fileRaw = file.raw;
      this.loading = true;
      let form = new FormData();
      form.append('file', fileRaw);
      await importFunc(form).finally(() => {
        this.loading = false;
      })
      this.$message.success('操作成功');
      this.refreshFunctionList();
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('19')
    }
  },
  computed: {
    treeData() {
      let pidGroup = [];
      const functions = JSON.parse(JSON.stringify(this.functions));
      functions.forEach((f) => {
        if (!pidGroup[f.parentId]) {
          pidGroup[f.parentId] = [f];
        } else {
          pidGroup[f.parentId].push(f);
        }
      });
      let resultArr = [];
      buildTreeData(0, pidGroup, resultArr, false);
      sortByNum(resultArr);
      return resultArr;
    },
    circleTreeData() {
      let pidGroup = [];
      const functions = JSON.parse(JSON.stringify(this.functions));
      functions.forEach((f) => {
        if (!pidGroup[f.parentId]) {
          pidGroup[f.parentId] = [f];
        } else {
          pidGroup[f.parentId].push(f);
        }
      });
      let resultArr = [];
      buildTreeData(0, pidGroup, resultArr, true);
      sortByNum(resultArr);
      return resultArr;
    }
  },
  mounted() {
    this.refreshFunctionList();
  },
};
</script>

<style
  lang="scss"
  scoped>
.search-container {
  padding-left: 16px;

  .func-bar {
    margin-top: 16px
  }

  .el-input {
    width: 500px;
  }
}
</style>
