<template>
  <div class="box-container" v-loading="loading">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">

          <div class="flex-item">
            <span class="flex-item__label">操作人：</span>
            <el-input v-model="searchParam.operatorLoginNameLike" />
          </div>

          <div class="flex-item">
            <span class="flex-item__label">操作类型：</span>
            <el-select clearable v-model="searchParam.operateSource">
              <el-option
                v-for="{code,desc} in $dict('OperateLogSourceEnum')"
                :key="parseInt(code)"
                :label="desc"
                :value="parseInt(code)"
              />
            </el-select>
          </div>
        </div>

        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              ref="datePicker"
              type="datetimerange"
              v-model="timeRange"
              @change="getTimeRange"
            ></date-picker>
          </div>
        </div>

        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button @click="refreshList(true)" type="primary">
              <i class="el-icon-search" />查询
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="content-container">
      <el-table :data="pageResult.data">
        <el-table-column type="index" :index="getIndex" />
        <el-table-column prop="createTime" label="创建时间" sortable />
        <el-table-column prop="operatorLoginName" label="操作人" />

        <el-table-column
          prop="operateSource"
          label="操作类型"
        >
          <template v-slot="{row}">
            {{ $dictCode('OperateLogSourceEnum', row.operateSource).desc }}
          </template>
        </el-table-column>

        <el-table-column prop="ip" label="IP">
          <template v-slot="{row}">
            {{ row.ip }}<br>{{ row.address }}
          </template>
        </el-table-column>
        <el-table-column prop="content" label="操作内容" />
        <el-table-column prop="status" label="操作状态" width="100">
          <template v-slot="{row}">
            <el-tag type="success" v-if="row.status == 1">成功</el-tag>
            <el-tag type="danger" v-else>失败</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="detail" show-overflow-tooltip label="详情"/>

      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :page-sizes="[10,15,30]"
        :total="pageResult.totalRecord"
        :page-size.sync="pageParam.pageSize"
        :current-page.sync="pageParam.pageCurrent"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-footer>
  </div>
</template>
<script>
import { getOperateLog } from '@/api/system'

export default {
  name: "PmsOperateLogList",
  data() {
    return {
      loading: false,
      searchParam: {
        operatorLoginNameLike: '',
        operateSource: '',
        createTimeEnd: '',
        createTimeBegin: '',
      },
      pageParam: { pageCurrent: 1, pageSize: 15 },
      pageResult: {},

      timeRange: []
    };
  },
  methods: {
    refreshList(initFlag) {
      if (initFlag) {
        this.pageParam.pageCurrent = 1;
      }
      this.loading = true;
      getOperateLog({
        ...this.pageParam,
        ...this.searchParam,
      })
        .then(({ data }) => {
          this.pageResult = data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getIndex(index) {
      return (this.pageParam.pageCurrent - 1) * this.pageParam.pageSize + index + 1;
    },
    getTimeRange(data) {
      this.searchParam.createTimeBegin = data && data[0];
      this.searchParam.createTimeEnd = data && data[1];
    },
    handleSizeChange(val) {
      this.refreshList(true);
    },
    handleCurrentChange(val) {
      this.pageParam.pageCurrent = val;
      this.refreshList();
    }
  },
  mounted() {
    this.refreshList();
  },
};
</script>
