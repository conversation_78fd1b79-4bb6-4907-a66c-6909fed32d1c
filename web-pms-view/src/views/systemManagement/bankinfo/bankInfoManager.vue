<template>
  <div class="bankinfo-container">
    <div class="box-container">
      <el-button class="create-btn" type="primary" @click="addBankInfo('create')">添加银行信息</el-button>
      <div class="search-container">
        <div class="flex-container">
          <div class="flex-wrapper search-wrapper">
            <div class="flex-item">
              <span class="flex-item__label">联行号：</span>
              <el-input v-model="form.bankChannelNo"></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">银行名称：</span>
              <el-input v-model="form.bankNameLike" placeholder="模糊查询"></el-input>
            </div>
          </div>
          <div class="search-wrapper">
            <div class="search-btn-group">
              <el-button type="primary" @click="search(true)">查询</el-button>
              <el-button type="text" @click="clearCondition">清空筛选条件</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="content-container">
        <el-table :data="bankList">
          <el-table-column label="银行编号" prop="bankCode"></el-table-column>
          <el-table-column label="联行号" prop="bankChannelNo"></el-table-column>
          <el-table-column label="银行名称" prop="bankName"></el-table-column>
          <el-table-column label="银行开户行号" prop="openingBankNo"></el-table-column>
          <el-table-column label="省份" prop="province"></el-table-column>
          <el-table-column label="城市" prop="city"></el-table-column>

          <el-table-column label="操作" prop="data">
            <template slot-scope="scope">
              <el-button type="text" @click="addBankInfo('edit', scope.row)">编辑</el-button>
              <el-button type="text" @click="deleteBankInfo(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-container">
        <el-pagination
          v-if="totalNum"
          ref="pagination"
          :total="totalNum"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>

    <!--编辑内容-->
    <el-dialog
      :visible.sync="isShowEdit"
      :close-on-click-modal="false"
      :before-close="closeForm"
      title="添加/编辑银行"
      append-to-body
      width="680px"
    >
      <el-form
        ref="form"
        :inline="true"
        :model="editBankInfoForm"
        :rules="rules"
        size="small"
        label-width="120px"
      >
        <el-form-item label="银行编号" prop="bankCode">
          <el-input v-model="editBankInfoForm.bankCode" style="width: 450px;"  />
        </el-form-item>
        <el-form-item label="联行号" prop="bankChannelNo">
          <el-input v-model="editBankInfoForm.bankChannelNo" style="width: 450px;"  />
        </el-form-item>
        <el-form-item label="银行名称" prop="bankName">
          <el-input v-model="editBankInfoForm.bankName" style="width: 450px;"  />
        </el-form-item>
        <el-form-item label="银行开户行号" prop="openingBankNo">
          <el-input v-model="editBankInfoForm.openingBankNo" style="width: 450px;"  />
        </el-form-item>
        <el-form-item label="省份" prop="province">
          <el-input v-model="editBankInfoForm.province" style="width: 450px;"  />
        </el-form-item>
        <el-form-item label="城市" prop="city">
          <el-input v-model="editBankInfoForm.city" style="width: 450px;"  />
        </el-form-item>
        <el-form-item label="地区编号" prop="cityCode">
          <el-input v-model="editBankInfoForm.cityCode" style="width: 450px;"  />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="text" @click="closeForm">取消</el-button>
        <el-button :loading="loading" type="primary" @click="submitBankInfo">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getBankList,addBankInfo,editBankIno,delBankInfo
} from "../../../api/common";


export default {
  name: "BankInfoManagement",
  data() {
    return {

      isShowEdit: false,
      operate: "",

      // 分页搜索
      form: {
        bankChannelNo: "",
        bankNameLike: "",
      },
      pageCurrent: 1,
      pageSize: 10,
      bankList: [],
      totalNum: 0,
      timeRange: [],

      editBankInfoForm: {
        id: null,
        bankCode: "",
        bankChannelNo: "",
        bankName: "",
        openingBankNo: "",
        province: "",
        city: "",
        cityCode: ""
      },
      loading: false,
      rules: {
        bankCode: [
          { required: true, trigger: 'blur', message: '请输入银行编号'},
        ],
        bankChannelNo: [
          { required: true, trigger: 'blur', message: '请输入银行联行号'},
        ],
        bankName: [
          { required: true, trigger: 'blur', message: '请输入银行名称'},
        ],
        province: [
          { required: true, trigger: 'blur', message: '请输入省份'},
        ],
        city: [
          { required: true, trigger: 'blur', message: '请输入城市名'},
        ],
        cityCode: [
          { required: true, trigger: 'blur', message: '请输入城市编号'},
        ],
      },

    };
  },
  mounted() {
    this.search();
  },
  methods: {
    addBankInfo(type, row) {
      this.operate=type
      if (type == "edit") {
        this.showEditPop(row)
      }else {
        this.closeForm()
        this.isShowEdit = true;
      }
    },

    search(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      getBankList({
        pageCurrent: this.pageCurrent,
        pageSize: this.pageSize,
        ...this.form,
      }).then((response) => {
        this.bankList = response.data.data;
        this.totalNum = response.data.totalRecord;
      });
    },
    clearCondition() {
      this.form.bankChannelNo = "";
      this.form.bankNameLike = "";
      this.search(true);
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    getTimeRange(val) {
      this.timeRange = val;
    },
    showEditPop(row) {
      this.isShowEdit = true;
      this.editBankInfoForm.id = row.id;
      this.editBankInfoForm.bankChannelNo = row.bankChannelNo;
      this.editBankInfoForm.bankCode = row.bankCode;
      this.editBankInfoForm.bankName = row.bankName;
      this.editBankInfoForm.openingBankNo = row.openingBankNo;
      this.editBankInfoForm.province = row.province;
      this.editBankInfoForm.city = row.city;
      this.editBankInfoForm.cityCode = row.cityCode
    },
    submitBankInfo() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (isNaN(this.editBankInfoForm.cityCode)) {
            this.$message.error("城市编码必须为数字组成")
            return;
          }
          if (this.editBankInfoForm.cityCode.length > 4) {
            this.$message.error("城市编码过长");
            return;
          }
          if (this.operate == "edit") {
            editBankIno(this.editBankInfoForm).then(res=>{
              this.clearCondition()
              this.closeForm()
            });
          }else {
            addBankInfo(this.editBankInfoForm).then(res=>{
              this.search(true)
              this.closeForm()
            });
          }
        }
      })
    },
    closeForm() {
      this.isShowEdit = false;
      this.loading = false;
      this.editBankInfoForm.id = null;
      this.editBankInfoForm.bankChannelNo = "";
      this.editBankInfoForm.bankCode = "";
      this.editBankInfoForm.bankName = "";
      this.editBankInfoForm.openingBankNo = "";
      this.editBankInfoForm.province = "";
      this.editBankInfoForm.city = "";
      this.editBankInfoForm.cityCode = "";
    },
    deleteBankInfo(id) {
      this.$confirm("确定要删除银行信息？", { type: "warning" }).then((res) => {
        delBankInfo({id:id}).then(res=>{
          this.clearCondition()
          this.search(true)
        })
      });
    },
  },
};
</script>

<style lang="scss" scoped>

.none-department-alert {
  position: relative;
  box-sizing: border-box;
  display: flex;
  width: 100%;
  padding: 8px 16px;
  margin-bottom: 8px;
  border-radius: 4px;
  background-color: #fdf6ec;
  color: #e6a23c;
}

.none-department-content {
  padding: 0 8px;
  display: table-cell;
}

.none-department-return {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 50px;
  font-style: normal;
  font-size: 13px;
  opacity: 1;
  color: #c0c4cc;
  cursor: pointer;
}
</style>
