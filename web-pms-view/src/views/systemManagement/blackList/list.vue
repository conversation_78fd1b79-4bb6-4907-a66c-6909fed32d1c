<template>
  <div class="box-container">
    <div>
      <el-button
        type="primary"
        @click="operateOne()">新建规则
      </el-button>
    </div>
    <el-table
      class="content-container"
      :data="list">
      <el-table-column label="功能标签">
        <template v-slot="{row}">{{ $dictCode('BlackTagEnum', row.tag).desc }}</template>
      </el-table-column>
      <el-table-column
        label="功能描述"
        prop="tagDesc"
        width="150"></el-table-column>
      <el-table-column label="适用商户">
        <template v-slot="{row}">
          {{ row.subjectNo }}<br>{{ row.subjectName }}
        </template>
      </el-table-column>
      <el-table-column label="创建人/创建时间">
        <template v-slot="{row}">
          {{ row.createBy }}<br>{{ row.createTime }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template v-slot="{row}">
          <el-button
            type="text"
            @click="operateOne(row)">编辑
          </el-button>
          <el-button
            class="red-btn"
            type="text"
            @click="deleteItem(row)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-footer class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        background
        :page-sizes="[10,50,100]"
        :total="page.totalRecord"
        :page-size.sync="page.pageSize"
        :current-page.sync="page.pageCurrent"
        @size-change="search(true)"
        @current-change="search()" />
    </el-footer>

    <el-dialog
      :before-close="cancel"
      :visible.sync="visible"
      title="黑名单规则"
      @open="listAllMerchant">
      <el-form
        ref="form"
        style="width: 500px;"
        label-width="120px"
        :model="editRow"
        :rules="rules">
        <el-form-item
          label="功能标签："
          prop="tag">
          <el-select
            clearable
            v-model="editRow.tag">
            <el-option
              v-for="item in $dict('BlackTagEnum')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="功能描述："
          prop="tagDesc">
          <el-input
            v-model="editRow.tagDesc"
            clearable
            type="textarea"
            rows="5"></el-input>
        </el-form-item>
        <el-form-item
          label="适用商户："
          prop="subjectNo">
          <el-select
            v-model="editRow.subjectNo"
            clearable
            filterable
            @change="changeSubjectNo">
            <el-option
              v-for="(item,key) in mchList"
              :key="key"
              :value="item.mchNo"
              :label="item.mchName"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <el-button
          type="primary"
          @click="save">保存
        </el-button>
        <el-button @click="cancel">取消</el-button>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import { toPromise } from "@/utils";
import { getAllMerchant } from "@/api/merchant";
import { addBlack, deleteBlack, listBlackList, updateBlack } from "@/api/system";


export default {
  name: 'SystemBlackList',
  data() {
    return {
      list: [],
      page: {
        pageCurrent: 1,
        pageSize: 10,
        totalRecord: 0,
      },
      editRow: {},
      visible: false,
      mchList: [],
      rules: {
        tag: [{ required: true, message: '请选择标签', trigger: 'blur' }],
        tagDesc: [{ required: true, message: '请填写标签描述', trigger: 'blur' }],
        subjectNo: [{ required: true, message: '请选择商户', trigger: 'blur' }],
      }
    }
  },
  computed: {
    isAdd() {
      return !this.editRow.id
    }
  },
  mounted() {
    this.search()
  },
  methods: {
    async search(init) {
      if (init) {
        this.page.pageCurrent = 1
      }
      const { data: { data, totalRecord } } = await listBlackList(this.page)
      this.list = data
      this.page.totalRecord = totalRecord
    },
    operateOne(row) {
      if (row) {
        this.editRow = JSON.parse(JSON.stringify(row))
      }
      this.visible = true
    },
    async deleteItem({ id }) {
      const [err] = await toPromise(this.$confirm('此操作将删除该条数据，是否继续？', '提示', {
        type: 'warning'
      }))
      if (err) return
      await deleteBlack(id)
      this.$message.success('操作成功')
      this.search()
    },
    async listAllMerchant() {
      const { data } = await getAllMerchant({ merchantType: '100' })
      this.mchList = data
    },
    async save() {
      const [err] = await toPromise(this.$refs.form.validate())
      if (err) return
      const api = this.isAdd ? addBlack : updateBlack
      await api(this.editRow)
      this.$message.success('操作成功')
      this.cancel()
      this.search()
    },
    cancel() {
      (this.editRow = {}) && this.$refs.form.resetFields()
      this.visible = false
    },
    changeSubjectNo(value) {
      for (let i = 0; i < this.mchList.length; i++) {
        if (this.mchList[i].mchNo === value) {
          this.editRow.subjectName = this.mchList[i].mchName
          break
        }
      }
    }
  },
}
</script>

<style
  lang="scss"
  scoped>

</style>
