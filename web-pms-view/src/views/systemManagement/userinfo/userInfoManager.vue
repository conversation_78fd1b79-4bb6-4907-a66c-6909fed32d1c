<template>
  <div class="userinfo-container">
    <div class="box-container">
      <div class="search-container">
        <div class="flex-container">
          <div class="flex-wrapper search-wrapper">
            <div class="flex-item">
              <span class="flex-item__label">姓名：</span>
              <el-input v-model="form.receiveName"></el-input>
            </div>
            <div class="flex-item">
              <span class="flex-item__label">身份证号码：</span>
              <el-input v-model="form.receiveIdCardNo"></el-input>
            </div>
          </div>
          <div class="search-wrapper">
            <div class="search-btn-group">
              <el-button type="primary" @click="search(true)">查询</el-button>
              <el-button type="text" @click="clearCondition">清空筛选条件</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="content-container">
        <el-table height="500" :data="userList" v-on:row-dblclick="clickShowuserinfo">
          <el-table-column label="创建时间" prop="createTime"></el-table-column>
          <el-table-column label="更新时间" prop="updateTime"></el-table-column>
          <el-table-column label="姓名" prop="receiveNameDecrypt"></el-table-column>
          <el-table-column label="身份证号码" prop="receiveIdCardNoDecrypt"></el-table-column>

          <el-table-column label="操作" prop="data">
            <template slot-scope="scope">
              <el-button type="text" @click="deleteuserinfo(scope.row.id)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination-container">
        <el-pagination
          v-if="totalNum"
          ref="pagination"
          :total="totalNum"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>

    <el-dialog
      :visible.sync="isShowEdit"
      :close-on-click-modal="true"
      :before-close="closeForm"
      title="查看认证用户详情"
      append-to-body
      width="760px"
    >
      <div style="text-align: center">
        <p style="font-size: 16px;font-weight: bold">姓名：{{this.edituserinfoForm.receiveName}}</p>
        <p style="font-size: 16px;font-weight: bold">身份证号码：{{this.edituserinfoForm.receiveIdCardNo}}</p>
      </div>
      <div style="margin-top: 2px;text-align: left">
        <div>
          <p style="font-size: 16px;font-weight: bold">身份证正面：</p>
          <el-image v-if="this.edituserinfoForm.idCardFrontUrl!=null" :src="this.fileUrl+this.edituserinfoForm.idCardFrontUrl"></el-image>
        </div>
        <div>
          <p style="font-size: 16px;font-weight: bold">身份证反面：</p>
          <el-image v-if="this.edituserinfoForm.idCardBackUrl!=null" :src="this.fileUrl+this.edituserinfoForm.idCardBackUrl"></el-image>
        </div>
        <div>
          <p style="font-size: 16px;font-weight: bold">身份证复印件：</p>
          <el-image v-if="this.edituserinfoForm.idCardCopyUrl!=null" :src="this.fileUrl+this.edituserinfoForm.idCardCopyUrl"></el-image>
        </div>
        <div>
          <p style="font-size: 16px;font-weight: bold">个人签章：</p>
          <el-image v-if="this.edituserinfoForm.personalSignature!=null" :src="this.fileUrl+this.edituserinfoForm.personalSignature"></el-image>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listPage,deleteById
} from "../../../api/userinfo";


export default {
  name: "UserInfoManagement",
  data() {
    return {

      isShowEdit: false,
      operate: "",

      // 分页搜索
      form: {
        receiveName: "",
        receiveIdCardNo: "",
      },
      pageCurrent: 1,
      pageSize: 10,
      userList: [],
      totalNum: 0,
      timeRange: [],

      edituserinfoForm: {
        receiveName: "",
        receiveIdCardNo: "",
        idCardFrontUrl: "",
        idCardBackUrl: "",
        idCardCopyUrl: "",
        personalSignature: ""
      }
    };
  },
  mounted() {
    this.search();
  },
  methods: {

    search(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      listPage({
        current: this.pageCurrent,
        size: this.pageSize,
        ...this.form,
      }).then((response) => {
        console.log(response)
        this.userList = response.data.records;
        this.totalNum = response.data.total;
      });
    },
    clickShowuserinfo(row, column) {
      this.showuserinfo(row)
    },
    clearCondition() {
      this.form.receiveName = "";
      this.form.receiveIdCardNo = "";
      this.search(true);
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    getTimeRange(val) {
      this.timeRange = val;
    },
    showuserinfo(row) {
      console.log(row)
      this.isShowEdit = true;
      this.edituserinfoForm.receiveName       = row.receiveNameDecrypt;
      this.edituserinfoForm.receiveIdCardNo   = row.receiveIdCardNoDecrypt;
      this.edituserinfoForm.idCardFrontUrl    = row.idCardFrontUrl;
      this.edituserinfoForm.idCardBackUrl     = row.idCardBackUrl;
      this.edituserinfoForm.idCardCopyUrl     = row.idCardCopyUrl;
      this.edituserinfoForm.personalSignature = row.personalSignature;
    },
    closeForm() {
      this.isShowEdit = false;
      // this.edituserinfoForm.receiveName       = "";
      // this.edituserinfoForm.receiveIdCardNo   = "";
      // this.edituserinfoForm.idCardFrontUrl    = "";
      // this.edituserinfoForm.idCardBackUrl     = "";
      // this.edituserinfoForm.idCardCopyUrl     = "";
      // this.edituserinfoForm.personalSignature = "";
    },
    deleteuserinfo(id) {
      this.$confirm("确定要删除认证用户信息？", { type: "warning" }).then((res) => {
        deleteById({id:id}).then(res=>{
          this.clearCondition()
          this.search(true)
          this.$message.success(res.data)
        })
      });
    },
  },
};
</script>

<style lang="scss" scoped>

.none-department-alert {
  position: relative;
  box-sizing: border-box;
  display: flex;
  width: 100%;
  padding: 8px 16px;
  margin-bottom: 8px;
  border-radius: 4px;
  background-color: #fdf6ec;
  color: #e6a23c;
}

.none-department-content {
  padding: 0 8px;
  display: table-cell;
}

.none-department-return {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 50px;
  font-style: normal;
  font-size: 13px;
  opacity: 1;
  color: #c0c4cc;
  cursor: pointer;
}
</style>
