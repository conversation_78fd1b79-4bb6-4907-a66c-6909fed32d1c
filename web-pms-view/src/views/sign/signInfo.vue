<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main flex-item-spec_main">
          <span class="flex-item__label">签约状态：</span>
          <el-radio-group v-model="searchForm.signStatus">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button
              v-for="(item, index) in $dict('SignStatusEnum')"
              :key="index"
              :label="item.code"
            >
              {{ item.desc }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">代征主体：</span>
          <el-select
            v-model="searchForm.mainstayNo"
            clearable
          >
            <el-option
              v-for="(item, index) in mainstayList"
              :key="index"
              :label="item.mchName"
              :value="item.mchNo"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业编号：</span>
          <el-input v-model="searchForm.employerNo" />
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业名称：</span>
          <el-input
            v-model="searchForm.employerNameLike"
            placeholder="模糊查询"
          />
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">姓名：</span>
          <el-input v-model="searchForm.receiveName"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">证件号码：</span>
          <el-input v-model="searchForm.receiveIdCardNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">手机号：</span>
          <el-input v-model="searchForm.receivePhoneNo"></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            v-model="createTimeRange"
            ref="timepicker"
            type="datetimerange"
            @change="getTimeRange"
          >
          </date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询
        </el-button>
        <el-button
          @click="exportSignRecord"
          v-permission="'sign:signRecord:view'"
        >导出
        </el-button>
        <el-button
          type="text"
          @click="getExportList"
        >查看已导出列表
        </el-button>
        <el-button
          type="text"
          @click="resetField"
        >清空筛选条件
        </el-button>
        <el-button
          type="primary"
          @click="testAuth"
        >人工鉴权
        </el-button>
        <el-button
          type="primary"
          @click="importOfflineSign">导入线下签约人员
        </el-button>
      </div>
    </div>

    <el-main class="content-container">
      <el-table :data="response.data">
        <el-table-column
          type="index"
          :index="getIndex"
          label="序号"
        ></el-table-column>
        <el-table-column
          label="创建时间"
          width="150"
          prop="createTime"
        >
          <p
            slot-scope="{row, column}"
            v-html="renderTime(row[column['property']])"
          ></p>
        </el-table-column>
        <el-table-column
          label="更新时间"
          width="150"
          prop="updateTime"
        >
          <p
            slot-scope="{row, column}"
            v-html="renderTime(row[column['property']])"
          ></p>
        </el-table-column>
        <el-table-column
          label="姓名"
          prop="receiveName"
        ></el-table-column>
        <el-table-column
          label="证件号码"
          width="180"
          prop="receiveIdCardNo"
        >
          <template v-slot="{row}">
            {{ row.receiveIdCardNo }}
            <div
              v-if="row.idCardBackUrl"
              @click="openIdCard(row)"
              class="func-content"
            >查看证件照片
            </div>
            <div
              v-if="row.idCardCopyUrl"
              @click="openIdCopy(row)"
              class="func-content"
            >查看证件复印件
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="预签约手机号"
          width="150"
          prop="receivePhoneNo"
        ></el-table-column>

        <el-table-column
          label="信息校验状态"
          prop="infoStatus"
          width="120"
        >
          <template v-slot="{row}">
            【{{ $dictCode('SuccessFailCodeEnum', row.infoStatus).desc }}】{{ row.infoStatus == '101' ? row.errMsg : '' }}
          </template>
        </el-table-column>

        <el-table-column
          label="签约状态"
          width="100"
        >
          <template v-slot="{row}">
            <el-tag :type="getTagType(row.signStatus)">
              {{ $dictCode('SignStatusEnum', row.signStatus).desc }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="签约类型"
          width="100"
        >
          <template v-slot="{row}">
            {{ $dictCode('ChannelSignTypeEnum', row.signType).desc }}
          </template>
        </el-table-column>

        <el-table-column
          label="签约模式"
          prop="signerType">
          <template v-slot="{row}">
            {{ $dictCode('SignerTypeEnum', row.signerType).desc }}
          </template>
        </el-table-column>

        <el-table-column
          label="商户编号/商户名称"
          width="140"
        >
          <template v-slot="{row}">
            {{ row.employerNo }} <br /> {{ row.employerName }}
          </template>
        </el-table-column>

        <el-table-column
          label="代征主体ID/名称"
          width="140"
        >
          <template v-slot="{row}">
            {{ row.mainstayNo }} <br /> {{ row.mainstayName }}
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          width="180">
          <template v-slot="{row}">
            <el-button
              v-if="row.fileUrl"
              type="text"
              @click="openFile(row.fileUrl)"
            >
              查看文件
            </el-button>
            <el-button
              v-if="row.signerType===101 && !row.hasFile"
              type="text"
              @click="uploadSignFile(row)">
              上传协议文件
            </el-button>
            <el-button
              v-if="row.signStatus === 100 || row.signStatus === 400"
              type="text"
              @click="openUploadDialog(row)"
            >上传身份证文件
            </el-button>
            <el-button
              v-if="row.id"
              type="text"
              @click="deleteById(row.id)"
              v-permission="'sign:signRecord:del'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>

      </el-table>
    </el-main>

    <el-footer class="pagination-container">
      <el-pagination
        :total="response.totalRecord"
        :current-page.sync="pageCurrent"
        :page-size.sync="pageSize"
        :page-sizes="[10, 50]"
        @size-change="search(true)"
        @current-change="search()"
        background
        layout="total, sizes, prev, pager, next, jumper"
      ></el-pagination>
    </el-footer>

    <export-record ref="exportRecord" />

    <upload-img
      ref="uploadDialog"
      :sign-item="editRow"
      @change="search()"
    ></upload-img>

    <el-dialog
      :visible.sync="isShowPop"
      :close-on-click-modal="false"
      :before-close="closeForm"
      title="鉴权"
      append-to-body
      width="600px"
    >
      <el-form
        ref="authform"
        :inline="true"
        :model="authForm"
        :rules="authRules"
        size="small"
        label-width="100px"
      >
        <el-form-item
          label="姓名"
          prop="name">
          <el-input
            v-model="authForm.name"
            style="width: 450px;" />
        </el-form-item>
        <el-form-item
          label="身份证号码"
          prop="idCardNo">
          <el-input
            v-model="authForm.idCardNo"
            style="width: 450px;" />
        </el-form-item>
        <el-form-item
          label="手机号"
          prop="phoneNo">
          <el-input
            v-model="authForm.phoneNo"
            style="width: 450px;" />
        </el-form-item>
        <el-form-item
          label="银行卡号"
          prop="bankAccountNo">
          <el-input
            v-model="authForm.bankAccountNo"
            style="width: 450px;" />
        </el-form-item>
        <el-form-item
          label="鉴权通道"
          prop="authChannel">
          <el-radio-group v-model="authForm.authChannel">
            <el-radio
              v-for="item in $dict('AuthChannelEnum')"
              :key="item.code"
              :label="item.code">{{ item.desc }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          label="鉴权方式："
          prop="authType">
          <el-select
            clearable
            v-model="authForm.authType"
          >
            <el-option
              v-for="item in $dict('AuthTypeEnum')"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer">
        <el-button
          type="text"
          @click="closeForm">取消
        </el-button>
        <el-button
          type="primary"
          @click="submitAuth">确认
        </el-button>
      </div>
    </el-dialog>

    <!--  线下导入-->
    <el-dialog
      :visible.sync="showOffline"
      :before-close="() => closeOfflineImport()"
      title="批量导入人员"
    >
      <div class="text-center">请下载批量导入人员模板，按规定格式上传
        <el-button
          type="primary"
          size="mini"
          @click="downloadOfflineTemplate">下载模板
        </el-button>
      </div>
      <file-upload
        action="/sign/uploadOfflineSign"
        :options="{img: false, excel: true}"
        :list-type="null"
        class="text-center"
        drag
        ref="uploader"
        @success="closeOfflineImport('操作成功')">
        <div slot="tip">支持扩展名：.xls,.xlsx格式文件</div>
      </file-upload>
    </el-dialog>

    <!--  线下导入签约文件-->
    <el-dialog
      :visible.sync="showUploadSign"
      title="导入签约文件"
      :before-close="closeUploadSign">
      <file-upload
        drag
        :options="{img: false, pdf: true, word: true}"
        :url.sync="editRow.fileUrl"
        :max="1"></file-upload>
      <template v-slot:footer>
        <el-button @click="closeUploadSign">取消</el-button>
        <el-button
          type="primary"
          @click="onSubmitSign">提交
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getMainstayList } from '@/api/merchant'
import { getSignList, exportSignRecord, deleteById, uploadSignFileUrl } from '@/api/sign'
import ExportRecord from '@/components/ExportRecord';
import UploadImg from './Component/UploadImg'
import { authTest } from '@/api/auth'
import FileUpload from "@/components/FileUpload/index.vue";

export default {
  name: 'PmsSignInfo',
  components: {
    FileUpload,
    ExportRecord,
    UploadImg,
  },
  data() {
    return {
      mainstayList: [],
      authRules: {
        name: [
          { required: true, trigger: 'blur', message: '请输入姓名' },
        ],
        idCardNo: [
          { required: true, trigger: 'blur', message: '请输入身份证号码' },
        ],
        authChannel: [
          { required: true, trigger: 'blur', message: '请选择鉴权通道' },
        ],
        authType: [
          { required: true, trigger: 'blur', message: '请选择鉴权方式' },
        ]
      },
      searchForm: {
        signStatus: '',
        mainstayNo: '',
        receiveName: '',
        receiveIdCardNo: '',
        receivePhoneNo: '',
        employerNo: '',
        employerNameLike: '',
        createBeginDate: '',
        createEndDate: '',
      },
      createTimeRange: [],

      response: {
        data: [],
        totalRecord: 0,
      },

      pageSize: 10,
      pageCurrent: 1,

      editRow: {},
      isShowPop: false,
      authForm: {},
      loading: false,

      showOffline: false, // 线下导入控制
      showUploadSign: false,
    }
  },
  mounted() {
    getMainstayList().then(res => this.mainstayList = res.data)
    this.search();
  },
  methods: {
    async search(initFlag) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      const { data } = await getSignList({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
      });
      this.response.data = data.data;
      this.response.totalRecord = data.totalRecord;
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    testAuth() {
      this.isShowPop = true
    },
    closeForm() {
      this.isShowPop = false
      this.loading = false
      this.authForm = {}
    },
    submitAuth() {
      this.$refs.authform.validate(valid => {
        if (valid) {
          authTest(this.authForm).then(res => {
            console.log(res.data)
            if (res.data.authStatus == 100) {
              this.$message.success("鉴权通过");
            } else {
              this.$message.error("鉴权失败：" + res.data.bizDesc)
            }
          })
        }
      })
    },
    resetField() {
      this.searchForm = {
        signStatus: '',
        mainstayNo: '',
        receiveName: '',
        receiveIdCardNo: '',
        receivePhoneNo: '',
        employerNo: '',
        employerNameLike: '',
        createBeginDate: '',
        createEndDate: '',
      };
      this.$refs.timepicker.clearTime();
    },
    async exportSignRecord() {
      const { data } = await exportSignRecord(this.searchForm);
      data && this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('11');
    },
    getTimeRange(val) {
      this.searchForm.createBeginDate = val[0];
      this.searchForm.createEndDate = val[1];
    },
    getTagType(status) {
      switch (Number(status)) {
        case 100:
          return 'success';
        case 200:
          return 'danger';
        case 400:
          return 'info';
        default:
          return ''
      }
    },
    async openFile(url) {
      const fileMsg = await this.formatFileMsg(url)
      window.open(fileMsg.fileUrl)
    },
    deleteById(id) {
      this.$confirm('删除数据后不可恢复, 是否继续?', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const { data } = await deleteById(id);

        data && this.$message({
          type: 'success',
          message: '删除成功!'
        });

        this.search(1);
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        });
      });
    },
    async openIdCard(row) {
      let urls = []
      if (row.idCardBackUrl && row.idCardFrontUrl) {
        const idCardBackFileMsg = await this.formatFileMsg(row.idCardBackUrl)
        const idCardFrontFileMsg = await this.formatFileMsg(row.idCardFrontUrl)
        urls = [idCardFrontFileMsg.fileUrl, idCardBackFileMsg.fileUrl]
      }
      if (row.cerFaceUrl) {
        const cerFaceFileMsg = await this.formatFileMsg(row.cerFaceUrl)
        urls.push(cerFaceFileMsg.fileUrl)
      }
      this.$preview(urls)
    },
    async openIdCopy(row) {
      let urls = []
      if (row.idCardCopyUrl) {
        const idCardCopyFileMsg = await this.formatFileMsg(row.idCardCopyUrl)
        urls = [idCardCopyFileMsg.fileUrl]
      }
      this.$preview(urls)
    },
    openUploadDialog(row) {
      this.editRow = row
      this.$refs.uploadDialog.open()
    },
    importOfflineSign() {
      this.showOffline = true
      this.$nextTick(() => {
        this.$refs.uploader.clearFiles()
      })
    },
    closeOfflineImport(toast) {
      if (toast && typeof toast === 'string') {
        this.$message.success(toast)
      }
      this.showOffline = false
      this.search()
    },
    downloadOfflineTemplate() {
      this.createDownloadLink(require('@/assets/template/signOfflineTemplate.xlsx'), '导入模板')
    },
    uploadSignFile(row) {
      this.editRow = JSON.parse(JSON.stringify(row))
      this.showUploadSign = true
    },
    closeUploadSign() {
      this.showUploadSign = false
      setTimeout(() => {
        this.editRow = {}
      }, 0)
    },
    async onSubmitSign() {
      if (!this.editRow.fileUrl) return
      await uploadSignFileUrl({
        id: this.editRow.id,
        fileUrl: this.editRow.fileUrl
      })
      this.$message.success('操作成功')
      this.closeUploadSign()
      this.search()
    }
  }
}
</script>

<style>
</style>
