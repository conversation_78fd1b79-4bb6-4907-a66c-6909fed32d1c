<template>
  <div class="box-container">
    <div class="search-container">
      <div class="function-bar">
        <el-button type="primary" size="small" @click="refreshFunctionList">
          <i class="el-icon-search" />刷新
        </el-button>
        <el-button size="small" @click="addFunctionPage">
          <i class="el-icon-plus"></i>添加顶级菜单
        </el-button>
        <el-button type="primary" size="small" @click="exportFunc" :loading="loading">
          导出功能列表
        </el-button>
        <el-upload
          style="display: inline-block;"
          :action="''"
          :auto-upload="false"
          :file-list="fileList"
          :show-file-list="false"
          accept=".xls,.xlsx"
          :on-change="importFunc"
        >
          <el-button size="small" :loading="loading">导入功能列表</el-button>
        </el-upload>
        <el-button type="text" @click="getExportList">查看已导出列表</el-button>
      </div>
    </div>
    <el-main>
      <el-table
        :data="treeData"
        row-key="id"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      >
        <el-table-column label="功能名称" prop="name" />
        <el-table-column label="功能编号" prop="number" />
        <el-table-column label="URL" prop="url" />
        <el-table-column label="权限标识" prop="permissionFlag" />
        <el-table-column
          label="功能类型"
          prop="type"
          :formatter="row => (row.type===1&&'菜单项')||(row.type===2&&'操作项')"
        />
        <el-table-column label="操作" v-slot="{row}">
          <el-button
            type="text"
            size="small"
            @click="editFunctionPage(row)"
            v-permission="'employer:function:edit'"
          >编辑</el-button>
          <el-button
            type="text"
            size="small"
            @click="deleteFunction(row)"
            v-permission="'employer:function:delete'"
          >删除</el-button>
          <el-button
            type="text"
            size="small"
            @click="addFunctionPage(row)"
            v-if="row.type===1"
            v-permission="'employer:function:add'"
          >添加子功能</el-button>
        </el-table-column>
      </el-table>
    </el-main>
    <employer-function-form ref="employerFunctionForm" v-on:success="refreshFunctionList" />
    <export-record ref="exportRecord"></export-record>
  </div>
</template>

<script>
import employerFunctionForm from "./employerFunctionForm";
import ExportRecord from '@/components/ExportRecord'
import { importFunc, exportFunc, listFunction, deleteSupplierFunction } from "@/api/supplier";
import { autoNext } from '@/utils'
function sortByNum(arr) {
  if (arr.length == 0) return;
  arr.sort((a, b) => {
    if (a.number < b.number) {
      return -1
    } else {
      return 1
    }
  })
  arr.forEach(item => {
    if (item.children) {
      sortByNum(item.children);
    }
  })
}

const buildTreeData = function (pid, pidGroup, resultArr) {
  if (!pidGroup[pid]) {
    return;
  }
  pidGroup[pid].forEach((f) => {
    resultArr.push(f);
    if (pidGroup[f.id]) {
      f.children = [];
      buildTreeData(f.id, pidGroup, f.children);
    }
  });
};

export default {
  name: "SupFunctionList",
  components: {
    employerFunctionForm,
    ExportRecord,
  },
  data() {
    return {
      functions: [],

      loading: false,
      fileList: [],
    };
  },
  methods: {
    refreshFunctionList() {
      listFunction().then(({ data }) => {
        this.functions = data;
      });
    },
    addFunctionPage(row) {
      this.$refs.employerFunctionForm.actionType = "ADD";
      let newNumber = autoNext(row.number, (row.children && row.children[row.children.length - 1].number));
      this.$refs.employerFunctionForm.employerFunction = {
        parentId: row && row.id,
        parentName: row && row.name,
        number: newNumber,
      };
      this.$refs.employerFunctionForm.show = true;
    },
    editFunctionPage(row) {
      this.$refs.employerFunctionForm.actionType = "EDIT";
      this.$refs.employerFunctionForm.employerFunction = { ...row };
      this.$refs.employerFunctionForm.show = true;
    },
    deleteFunction(row) {
      this.$confirm("确定删除吗?")
        .then(() => deleteSupplierFunction({ id: row.id }))
        .then(() => this.refreshFunctionList());
    },
    async exportFunc() {
      const { data } = await exportFunc();
      data && this.$message.success(data)
    },
    async importFunc(file) {
      let fileRaw = file.raw;

      this.loading = true;
      let form = new FormData();
      form.append('file', fileRaw);
      await importFunc(form).finally(() => {
        this.loading = false;
      })
      this.$message.success('操作成功');
      this.refreshFunctionList();
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord('21')
    }
  },
  computed: {
    treeData() {
      let pidGroup = [];
      this.functions.forEach((f) => {
        if (!pidGroup[f.parentId]) {
          pidGroup[f.parentId] = [f];
        } else {
          pidGroup[f.parentId].push(f);
        }
      });
      let resultArr = [];
      buildTreeData(0, pidGroup, resultArr);
      sortByNum(resultArr);
      return resultArr;
    },
  },
  mounted() {
    this.refreshFunctionList();
  },
};
</script>

