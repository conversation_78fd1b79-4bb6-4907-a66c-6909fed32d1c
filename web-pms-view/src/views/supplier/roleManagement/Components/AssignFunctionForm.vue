<template>
  <el-dialog title="分配功能" :visible.sync="show" :close-on-click-modal="false" @close="closeForm" width="800px">
    <el-input placeholder="输入关键字进行过滤" v-model="filterText"/>
    <el-tree
      ref="assignTree"
      :data="assignTreeData"
      border
      row-key="id"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      :default-expand-all="true"
      :filter-node-method="filterNode"
      v-slot="{data:menu}">

      <div class="treeNode">
        <el-checkbox
          class="menuNode"
          @change="checked=>selectChange(menu,checked)"
          v-model="assignedInfo[menu.id]"
          :key="menu.id"
          :label="menu.id">
          {{menu.name}}
        </el-checkbox>

        <div class="actionNode" v-if="menu.actions">
          <el-checkbox
            @change="checked=>selectChange(action,checked)"
            v-model="assignedInfo[action.id]"
            v-for="action in menu.actions"
            :key="action.id"
            :label="action.id">{{action.name}}
          </el-checkbox>
        </div>
      </div>
    </el-tree>

    <div slot="footer" class="dialog-footer">
      <el-button @click="closeForm">取 消</el-button>
      <el-button type="primary" @click="doAssignFunction">确 定</el-button>
    </div>
  </el-dialog>

</template>

<script>
  import { getRoleFunction, listFunction, assignPermission } from '@/api/supplier';

  const buildAssignTree = function (pFunction, pidGroup, subMenuArr, subActionArr) {
    let pid = (pFunction && pFunction.id) || 0
    if (!pidGroup[pid]) {
      return;
    }
    pidGroup[pid].forEach(f => {
      f.children = []
      f.actions = []
      if (f.type === 1) {
        subMenuArr.push(f)
        if (pidGroup[f.id]) {
          buildAssignTree(f, pidGroup, f.children, f.actions)
        }
      } else {
        subActionArr.push(f)
      }
      f.parent = pFunction;
    })

  }

  export default {
    name: "AssignFunctionForm",
    data() {
      return {
        filterText: '',
        functions: [],
        assignedInfo: [],
        roleId: undefined,
        show: false,
      }
    },
    watch: {
      filterText(val) {
        this.$refs.assignTree.filter(val);
      }
    },

    methods: {
      async initAndShow(roleId) {
        this.roleId = roleId;
        const [{data: allPortalFunction}, {data: assignedFunctions}] = await Promise.all([
          listFunction(),
          getRoleFunction({ roleId })
        ])
        this.functions = allPortalFunction;
        assignedFunctions.forEach(f => this.assignedInfo[f.id] = true)
        this.show = true;
      },

      filterNode(value, menu) {
        if (!value) {
          return true;
        }
        return menu.name.indexOf(value) !== -1 || menu.actions.some(p => p.name.indexOf(value) !== -1)
      },

      recursiveUp(currentFunction, checked) {
        if (!currentFunction) {
          return;
        }
        if (checked) {
          this.assignedInfo[currentFunction.id] = true;
          this.recursiveUp(currentFunction.parent, checked)
        }
      },
      recursiveDown(currentFunction, checked) {
        currentFunction.children.forEach(c => {
          this.assignedInfo[c.id] = checked;
          this.recursiveDown(c, checked);
        })
        currentFunction.actions.forEach(c => {
          this.assignedInfo[c.id] = checked;
        })
      },

      selectChange(func, checked) {
        this.recursiveUp(func, checked)
        this.recursiveDown(func, checked)
      },

      doAssignFunction() {
        let assignedIds = []
        this.assignedInfo.forEach((v, i) => {
          if (v === true) {
            assignedIds.push(i)
          }
        });
        assignPermission({
          roleId: this.roleId,
          functionIds: assignedIds
        }).then(({data}) => {
          this.$message.success(data);
          this.show = false;
        })
      },

      closeForm() {
        this.functions = [];
        this.roleId = undefined;
        this.assignedInfo = [];
        this.show = false;
      }
    },

    computed: {
      assignTreeData() {
        let pidGroup = []
        this.functions.forEach(f => {
          if (!pidGroup[f.parentId]) {
            pidGroup[f.parentId] = [f]
          } else {
            pidGroup[f.parentId].push(f)
          }
        })
        let subMenuArr = []
        buildAssignTree(null, pidGroup, subMenuArr, [])
        return subMenuArr
      }
    },
  }
</script>

<style lang="scss" scoped>

  ::v-deep .el-dialog {

    .el-input {
      margin-bottom: 10px;
    }

    .el-dialog__body {
      max-height: 600px;
      overflow-y: auto;

      .el-tree {
        border-bottom: 1px solid #ccc;

        .el-tree-node {
          .el-tree-node__content {
            height: auto;
            align-items: flex-start;
            border: 1px solid #ccc;
            border-bottom: 0px;

            .el-tree-node__expand-icon {
              padding: 6px;
              padding-left: 0;
              margin-top: 2px;
            }

            .el-checkbox {
              margin-bottom: 5px;
              margin-top: 5px;
            }

            .treeNode {
              display: flex;

              .menuNode {
                display: inline-block;
                width: 150px;
                margin-right: 50px;
                white-space: normal;
                margin-top: 5px;

                .el-checkbox__label {
                  font-size: 13px;
                }
              }

              .actionNode {
                width: 500px;
                white-space: normal;

                .el-checkbox {
                  margin-right: 15px;
                  margin-bottom: 5px;
                  margin-top: 5px;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
