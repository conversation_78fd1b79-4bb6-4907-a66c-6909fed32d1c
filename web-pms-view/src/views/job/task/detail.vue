<template>
  <div class="box-container page-container">
    <div class="info-box">
      <div class="info-title">任务信息</div>
      <div
        class="info-wrapper"
        v-if="task">
        <div class="info-item">
          <span class="info-label">任务编号：</span>
          <span class="info-value">{{ task.jobId }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">任务名称：</span>
          <span class="info-value">{{ task.jobName }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">行业/岗位：</span>
          <span
            class="info-value"
          >{{ task.industryName }}/{{ task.workCategoryName }}</span
          >
        </div>
        <div class="info-item">
          <span class="info-label">任务标签：</span>
          <span class="info-value">
            <el-tag
              class="info-tag"
              v-for="(item, index) in task.tags"
              :key="index"
            >{{ item }}</el-tag
            >
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">任务日期：</span>
          <span class="info-value">
            {{
              task.jobAvalDateType == 100
                ? $dictCode("JobAvalDateTypeEnum", task.jobAvalDateType).desc
                : `${task.jobStartDate} - ${task.jobFinishDate} `
            }}
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">工作时段：</span>
          <span class="info-value">
            {{ `${task.jobTimeStart} - ${task.jobTimeEnd}` }}
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">任务地点：</span>
          <span class="info-value">
            {{ `${task.jobProvinceName} ${task.jobCityName} ${task.jobAreaName}` }}
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">详细地址：</span>
          <span class="info-value">
            {{ task.jobAddress }}
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">任务报酬：</span>
          <span
            class="info-value"
            v-if="task.rewardType == 100">
            {{ task.rewardAmount }} /
            {{ $dictCode("PayTypeEnum", task.payType).desc }}
          </span>
          <span
            class="info-value"
            v-if="task.rewardType == 200"> 面议 </span>
        </div>
        <div class="info-item">
          <span class="info-label">接单模式：</span>
          <span class="info-value">
            {{ $dictCode("AcceptModeEnum", task.acceptMode).desc }}
          </span>
        </div>
        <div class="info-item">
          <span class="info-label">任务可见性：</span>
          <span class="info-value">
            {{ $dictCode("JobVisibilityEnum", task.scope).desc }}
          </span>
        </div>
      </div>
    </div>
    <div class="info-box">
      <div class="info-title">任务要求</div>
      <div
        class="info-wrapper"
        v-if="task">
        <div class="info-item">
          <span class="info-label">性别要求：</span>
          <span class="info-value">
            {{ $dictCode("WorkerGenderEnum", task.workerGender).desc }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">年龄要求：</span>
          <span class="info-value">{{
              task.workerAgeLimitType == 100
                ? `${task.workerAgeLimitMin} - ${task.workerAgeLimitMax}`
                : "不限" }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">学历要求：</span>
          <span class="info-value">{{ $dictCode("EduBackgroundEnum", task.eduBackground).desc }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">技能要求：</span>
          <span class="info-value">{{ task.professionalSkill }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">任务描述：</span>
          <span class="info-value">{{ task.jobDescribe }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">交付标准：</span>
          <span class="info-value">{{ task.deliveryStandard }}</span>
        </div>
      </div>
    </div>
    <div class="info-box">
      <div class="info-title">人员管理</div>
      <div class="btn-box">
        <el-button
          size="small"
          plain
          @click="batchdownload('resFile')"
        >批量下载成果验收单
        </el-button
        >
        <el-button
          style="margin-left: 5px"
          size="small"
          plain
          @click="batchdownload('devFile')"
        >批量下载交付明细表
        </el-button
        >
      </div>
      <div
        class="info-wrapper"
        v-if="task">
        <el-table
          :data="task.jobWorkerRecords"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            :selectable="selected"
            width="55" />
          <el-table-column
            label="姓名"
            prop="workerName"></el-table-column>
          <el-table-column
            width="150"
            label="手机号"
            prop="workerPhone"></el-table-column>
          <el-table-column
            label="身份证"
            prop="workerIdcard"
            width="180">
            <template v-slot="{ row }">
              <div>{{ row.workerIdcard || "-" }}</div>
              <el-button
                v-if="row.workerIdcard && row.authStatus == 100"
                type="text"
                @click="workerInfo(row)"
              >查看身份证
              </el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            label="认证状态"
            prop="authStatus">
            <template v-slot="{ row }">
              {{ $dictCode("WorkerAuthStatus", row.authStatus).desc || "-" }}
            </template>
          </el-table-column>
          <el-table-column label="接单模式">
            {{ $dictCode("AcceptModeEnum", task.acceptMode).desc }}
          </el-table-column>
          <el-table-column
            label="任务状态"
            prop="jobStatus">
            <template v-slot="{ row, column }">
              {{ $dictCode("JobWorkerStatusEnum", row[column.property]).desc }}
            </template>
          </el-table-column>
          <el-table-column
            label="交付状态"
            prop="deliveryStatus">
            <template v-slot="{ row, column }">
              {{ $dictCode("DeliveryStatusEnum", row[column.property]).desc }}
            </template>
          </el-table-column>
          <el-table-column
            label="结算状态"
            prop="settleStatus">
            <template v-slot="{ row, column }">
              {{ $dictCode("SettleStatusEnum", row[column.property]).desc }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            class-name="small-padding fixed-width"
            width="200"
          >
            <template v-slot="{row}">
              <el-button
                v-if="row.attachment"
                type="text"
                size="mini"
                @click="previewAttachment(row)">
                查看交付物
              </el-button>
              <div
                class="table-btn-box"
                v-if="row.jobStatus >= 300 && (row.settleStatus == 200 || row.settleStatus == 100)">
                <el-button
                  v-if="row.resultSignatureUrl"
                  size="mini"
                  type="text"
                  @click="downloadJobFile(row, 1)"
                >下载验收单
                </el-button>
                <el-button
                  v-if="row.deliverSignatureUrl"
                  size="mini"
                  type="text"
                  @click="downloadJobFile(row, 2)"
                >下载交付明细表
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div class="page-btn">
      <router-link to="/job/task">
        <el-button>返回</el-button>
      </router-link>
      <template v-if="task && task.jobStatus == 100">
        <el-button
          type="primary"
          @click="audit(true)"> 通过
        </el-button>
        <el-button
          type="danger"
          @click="audit(false)"> 拒绝
        </el-button>
      </template>
    </div>
  </div>
</template>

<script>
import { jobDetail, taskAudit, workerInfo } from "@/api/jobworker";
import { toPromise } from "@/utils";
import { getToken } from "@/utils/loginToken";

export default {
  data() {
    return {
      task: null,
      multiple: true,
      ids: [],
      // 非单个禁用
      single: true,
    };
  },
  computed: {
    id() {
      return this.$route.query.id;
    },
  },
  mounted() {
    if (!this.id) {
      return this.$router.replace("/job/task");
    }
    this.getDetail();
  },
  methods: {
    async getDetail() {
      const {data} = await jobDetail({id: this.id});
      if (data.jobTag) {
        data.tags = JSON.parse(data.jobTag);
      }
      this.task = data;
    },
    goToWorker(row) {
      this.$router.push({
        path: "/job/worker",
        query: {
          workerName: row.workerName,
          workerPhone: row.workerPhone,
          jobId: row.jobId,
        },
      });
    },
    async audit(type) {
      let [err] = await toPromise(
        this.$confirm(
          `此操作不可逆，是否确定${type ? "通过" : "拒绝"}该任务？`,
          "提示",
          {
            type: "warning",
          }
        )
      );
      if (err) return;
      [err] = await toPromise(
        taskAudit(this.id, this.$dictCode("ApproveEvent", type).flag)
      );
      if (err) return;
      this.$router.push("/job/task");
    },
    async workerInfo({workerIdCardMd5}) {
      const {data} = await workerInfo({
        workerIdCardMd5: workerIdCardMd5,
      });
      const idCardBackFileMsg = await this.formatFileMsg(data.idCardBackUrl);
      const idCardFrontFileMsg = await this.formatFileMsg(data.idCardFrontUrl);
      this.$preview([
        idCardBackFileMsg.fileUrl,
        idCardFrontFileMsg.fileUrl,
      ]);
    },
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    selected(row, index) {
      return !!row.resultSignatureUrl && !!row.deliverSignatureUrl;
    },
    batchdownload(flag) {
      const ids = this.ids;
      if (this.ids.length == 0) {
        this.$message.error("请选择雇员");
        return;
      }
      window.open(`${this.baseUrl}/download/jobworker/${flag}/${ids}?x-token=${getToken()}`);
    },
    downloadJobFile(row, flag) {
      let workerName = row.workerName;
      if (!row.workerName) {
        workerName = "未知";
      }
      let suffix = flag == 1 ? "平台成果验收单" : "平台交付明细表";
      let fileName = `${workerName}_${row.workerPhone}_${suffix}.pdf`;
      console.log(fileName);
      let path = flag == 1 ? row.resultSignatureUrl : row.deliverSignatureUrl;
      window.open(`${this.baseUrl}/download/fastdf?path=${path}&fileName=${fileName}&x-token=${getToken()}`);
    },
    previewAttachment(row) {
      if (row.attachment) {
        const list = JSON.parse(row.attachment).map(item => this.fileUrl + item)
        this.$preview(list)
      }
    },
  },
};
</script>

<style
  lang="scss"
  scoped>
.info-box {
  margin: 16px 0 32px;
}

.info-title {
  font-size: 20px;
  font-weight: bolder;
}

.info-wrapper {
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid #ddd;

  .info-item {
    width: 30%;
    margin: 16px 8px;
  }
}

.page-btn {
  text-align: right;

  .el-button {
    margin: 0 8px;
  }
}

.info-tag {
  margin-right: 10px;
}

.btn-box {
  margin-top: 10px;
}
</style>
