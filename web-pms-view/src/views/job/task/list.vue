<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">任务编号：</span>
          <el-input
            v-model="searchForm.jobId"
            clearable placeholder="模糊查询"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">任务名称：</span>
          <el-input
            v-model="searchForm.jobName"
            clearable placeholder="模糊查询"
          ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">岗位：</span>
          <el-cascader
            filterable
            clearable
            data-index="index"
            :options="industryListOptions"
            :props="{
              expandTrigger: 'hover',
              emitPath: false,
              value: 'industryTypeCode',
              label: 'industryTypeName',
              leaf: 'industryTypeCode',
            }"
            v-model="searchForm.industryCode"
          >
            <template v-slot="{ data }">
              <el-tooltip
                :content="data.industryTypeName"
                :disabled="data.industryTypeName.length < 14"
              >
                <span class="cascader-content-item">
                  {{ data.industryTypeName }}
                </span>
              </el-tooltip>
            </template>
          </el-cascader>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">接单模式：</span>
          <el-select
            v-model="searchForm.acceptMode"
            clearable
          >
            <el-option
              v-for="(item, index) in $dict('AcceptModeEnum')"
              :key="index"
              :label="item.desc"
              :value="item.code"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">用工企业编号：</span>
          <el-input v-model="searchForm.employerNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用工企业名称：</span>
          <el-input v-model="searchForm.employerNameLike" placeholder="支持模糊查询"></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">创建时间：</span>
          <date-picker
            ref="datepicker"
            type="datetime"
            picker="separate"
            :start-time.sync="searchForm.createTimeBegin"
            :end-time.sync="searchForm.createTimeEnd"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询</el-button>
        <el-button @click="resetForm">重置</el-button>
        <el-button
          v-show="searchForm.jobStatus == 100"
          :disabled="selection.length == 0"
          @click="audit(true)"
        >批量通过审核</el-button>
      </div>
    </div>
    <el-tabs
      v-model="activeName"
      @tab-click="handleTabChange"
    >
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="index"
        :label="item.desc"
        :name="item.code"
      ></el-tab-pane>
    </el-tabs>

    <el-table
      :data="list"
      @selection-change="handleSelect"
    >
      <el-table-column
        type="selection"
        v-if="searchForm.jobStatus == 100"
      ></el-table-column>
      <el-table-column
        label="任务编号"
        prop="jobId"
        width="180"
      ></el-table-column>
      <el-table-column
        label="任务名称"
        prop="jobName"
        width="120"
      ></el-table-column>
      <el-table-column
        label="用工企业"
        width="120"
      >
        <template v-slot="{row}">
          {{ row.employerNo }}<br>
          {{ row.employerName }}
        </template>
      </el-table-column>
      <el-table-column
        label="行业/岗位"
        prop="industryName"
        width="150"
      ></el-table-column>
      <el-table-column label="任务日期">
        <template v-slot="{ row }">
          {{
            row.jobAvalDateType == 100
              ? "长期"
              : `${row.jobStartDate}~${row.jobFinishDate}`
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="接单模式"
        prop="acceptMode"
      >
        <template v-slot="{ row, column }">
          {{ $dictCode("AcceptModeEnum", row[column.property]).desc }}
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        prop="jobStatus"
      >
        <template v-slot="{ row, column }">
          <el-tag :type="getTagType(row[column.property])">
            {{ $dictCode("JobStatusEnum", row[column.property]).desc }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="招收人数"
        prop="workerNum"
      ></el-table-column>
      <el-table-column
        label="已录用人数"
        prop="employedNum"
      ></el-table-column>
      <el-table-column
        label="创建时间"
        prop="createTime"
        width="120"
      >
        <template v-slot="{ row }">
          <p v-html="renderTime(row.createTime)"></p>
        </template>
      </el-table-column>

      <el-table-column label="操作">
        <template v-slot="{ row }">
          <el-button
            type="text"
            @click="edit(row)"
            v-if="row.jobStatus == 100"
          >审核</el-button>
          <el-button
            type="text"
            @click="edit(row)"
            v-else
          >任务详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="page.total"
        :page-sizes="[10, 50]"
        :current-page.sync="page.current"
        :page-size.sync="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </el-footer>
  </div>
</template>

<script>
  import { taskList, taskAudit } from "@/api/jobworker";
  import { listAllIndustryType } from '@/api/business'
  import { toPromise, convert } from "@/utils";
  export default {
    data(vm) {
      return {
        activeName: "ALL",
        searchForm: {
          jobStatus: vm.$dict("JobStatusEnum")[0].code,
        },
        page: {
          current: 1,
          size: 10,
          total: 0,
        },
        list: [],
        selection: [],
        tabs: [],
        industryList: [],
        industryListOptions: [],
      };
    },
    mounted() {
      this.getTabs();
      this.listAllIndustryType();
      this.search(true);
    },
    methods: {
      getTabs() {
        this.tabs.push({ code: "ALL", desc: "全部" });
        this.$dict("JobStatusEnum").forEach((e) => {
          this.tabs.push({ code: e.code, desc: e.desc });
        });
      },
      async search(init) {
        if (init) {
          this.page.current = 1;
        }
        if (this.activeName == "ALL") {
          this.searchForm.jobStatus = null;
        }
        const {
          data: { records, total },
        } = await taskList({
          ...this.page,
          ...this.searchForm,
        });
        this.list = records;
        this.page.total = total;
      },
      resetForm() {
        this.searchForm = {};
        this.$refs.datepicker.clearTime();
      },
      handleTabChange(tab) {
        this.searchForm.jobStatus = this.activeName;
        this.search(true);
      },
      getTagType(type) {
        switch (type) {
          case 100:
            return "info";
          case 300:
            return "success";
          case 400:
            return "danger";
          default:
            return "";
        }
      },
      create() {
        this.$router.push({
          path: "/job/task/create",
        });
      },
      edit({ id }) {
        this.$router.push({
          path: "/job/task/detail",
          query: {
            id,
          },
        });
      },
      handleSelect(list) {
        this.selection = list;
      },
      async audit(type) {
        const [err] = await toPromise(
          this.$confirm(
            `此操作不可逆，是否确定${type ? "通过" : "拒绝"}该任务？`,
            "提示",
            {
              type: "warning",
            }
          )
        );
        if (err) return;
        const ids = this.selection.map((item) => item.id).join(",");
        const [error, { data }] = await toPromise(taskAudit(ids, this.$dictCode("ApproveEvent", type).flag));
        if (error) return;
        data && this.$message.success(data)
        this.search();
      },
      async listAllIndustryType() {
        const { data } = await listAllIndustryType()
        this.industryList = data;
        this.industryListOptions = convert(data, 0);
      }
    },
  };
</script>

<style></style>
