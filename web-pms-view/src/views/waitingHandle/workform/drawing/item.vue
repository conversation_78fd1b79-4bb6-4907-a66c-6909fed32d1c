<template>
  <div class="drawing-item-box">
    <parse-item
      v-if="item"
      v-bind="$attrs"
      :element="item.element"
      @change="onChange"
    ></parse-item>
    <div class="flex-box item-btn-block">
      <div
        class="item-btn-box item-btn-delete"
        @click="onDelete"
      >
        <i class="el-icon-delete"></i>
      </div>
    </div>
  </div>
</template>

<script>
  import parseItem from '../parse/item.vue'
  export default {
    name: 'DrawingItem',
    components: {
      parseItem,
    },
    props: {
      item: {
        type: Object,
        default: null
      }
    },
    methods: {
      onChange({ value, field }) {
        this.$emit('change', this.item, field, value)
      },
      onDelete() {
        this.$emit('delete', this.item)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .drawing-item-box {
    position: relative;
    padding: 0 100px 0 0;
    .item-btn {
      &-block {
        position: absolute;
        right: 10px;
        top: 0;
        color: #f00;
        font-size: 15px;
      }
      &-box {
        &:hover {
          background: #f00;
          color: #fff;
        }
        padding: 2px;
        border: 1px solid currentColor;
        border-radius: 10px;
      }
    }
  }
</style>