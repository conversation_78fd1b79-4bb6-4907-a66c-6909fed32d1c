import FileUpload from '@/components/FileUpload'
const customRenderFn = {
  'sign-upload': function (h, element) {
    const { value, urls, tips, title, uploadText, signFileUrl } = element
    return (
      <div>
        <el-form-item label={`${title}：`} prop={element.field[0]}>
          <el-radio-group value={value} onInput={(value) => this.signUploadChange(element.field[0], value, true)}>
            <el-radio label="1">线下签约</el-radio>
            <el-radio label="2">线上签约</el-radio>
          </el-radio-group>
        </el-form-item>
        {
          value == 1 ?
            <el-form-item label={`${uploadText}：`} prop={element.field[1]} ref="upload-sign">
              <div>
                请下载<span type="text" class="func-content" onClick={this.downLoadSignFile}>《{element.templateFileName}》</span>后盖章上传，点击提交完成签约
                <file-upload
                  urls={urls}
                  list-type="text"
                  max={20}
                  options={{ pdf: true, word: true }}
                  onChange={(value) => this.signUploadChange(element.field[1], value)}
                >
                  <p class="color-gray" slot='tip'>支持扩展名.pdf.doc.docx.bmp.png.jpeg.jpg</p>
                  <el-button
                    type="primary"
                    size="mini"
                  >
                    点击上传
                  </el-button>
                </file-upload>
              </div>
            </el-form-item> :
            <el-form-item style="margin-top: -20px;" ref="sign">
              {
                value == 2 ?
                  (signFileUrl
                    ? <div class="func-content" onClick={this.downloadRecordFile}>归档文件</div>
                    : <div>{tips}<div type="text" class="func-content" onClick={this.downLoadSignFile}>点击下载《{element.templateFileName}》</div></div>
                  )
                  : null
              }
            </el-form-item>
        }
      </div >
    )
  }
}


export default {
  name: 'renderCustom',
  props: ['element'],
  components: {
    FileUpload
  },
  render(h) {
    return customRenderFn[this.element.type].call(this, h, this.element)
  },
  methods: {
    signUploadChange(field, value, clear) {
      clear && this.$refs['upload-sign'] && this.$refs['upload-sign'].clearValidate()
      this.$emit('change', { value, field })
    },
    downLoadSignFile(e) {
      e.stopPropagation();
      if (this.element.templateFileUrl) {
        this.downloadFile(this.fileUrl + this.element.templateFileUrl, this.element.templateFileName || '模板文件');
      }
    },
    downloadRecordFile(e) {
      e.stopPropagation();
      this.downloadFile(this.fileUrl + this.element.signFileUrl, '归档文件');
    }
  },
}