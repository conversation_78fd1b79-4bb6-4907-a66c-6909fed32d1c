<script>
  /**
   * 渲染form-item
   * 表单项的属性会通过$attrs传递给表单组件
   */
  import renderItem from './render'
  import renderUpload from './renderUpload'
  import renderCustom from './renderCustom'
  export default {
    name: 'parseItem',
    components: {
      renderItem,
      renderUpload,
      renderCustom,
    },
    props: {
      element: {
        type: Object,
        default: () => ({})
      },
      form: {
        type: Object,
        default: null,
      }
    },
    render(h) {
      return (
        <div class="parse-item-box">
          {
            this.element.elemType === 'custom'
              ? <render-custom element={this.element} onChange={this.onCustomChange}></render-custom>
              : <el-form-item label={this.element.title + ':'} prop={this.element.field}>
                {
                  this.element.type == 'el-upload'
                    ? <render-upload
                      element={this.element}
                      onChange={this.onChange(this.element.field)}
                      prop={this.$attrs}
                    ></render-upload>
                    : <render-item
                      prop={this.$attrs}
                      element={this.element}
                      onChange={this.onChange(this.element.field)}
                    ></render-item>
                }
              </el-form-item>
          }
        </div>
      )
    },
    methods: {
      onChange(field) {
        return (value) => {
          this.emitFormImteChange(field, value)
        }
      },
      onCustomChange({ field, value }) {
        this.emitFormImteChange(field, value)
      },
      emitFormImteChange(field, value) {
        if (!this.form) return
        this.$emit('change', {
          field,
          value
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .parse-item-box {
    padding: 10px 0;

    .el-form-item {
      margin-bottom: 0;
    }
  }
</style>
