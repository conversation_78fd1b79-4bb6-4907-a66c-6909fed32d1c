<template>
  <div class="page-container">
    <div class="box-container">
      <div class="flex-container">
        <h2>单号：{{ processInfo.id }}</h2>
        <div class="flex-wrapper">
          <div class="flex-vertical-item">
            <span class="flex-vertical-label">发起人：</span>
            <span class="flex-item__content">{{processInfo.initiatorName}}</span>
          </div>
          <div class="flex-vertical-item">
            <span class="flex-vertical-label">流程主题：</span>
            <span class="flex-item__content">
              {{ $dictCode('FlowTopicType', processInfo.flowTopicType).desc}}
            </span>
          </div>
        </div>
        <div class="flex-wrapper">
          <div class="flex-vertical-item">
            <span class="flex-vertical-label">创建时间：</span>
            <span class="flex-item__content">
              {{processInfo.createTime}}
            </span>
          </div>
          <div class="flex-vertical-item">
            <span class="flex-vertical-label">最后跟进时间：</span>
            <span class="flex-item__content">
              {{processInfo.updateTime}}
            </span>
          </div>
        </div>
        <div class="flex-wrapper">
          <div class="flex-vertical-item flex-vertical-main">
            <span class="flex-vertical-label">备注：</span>
            <span class="flex-item__content">
              {{form.remark}}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="box-container">
      <div class="subTitle">流程进度 {{ $dictCode('FlowStatus', processInfo.status).desc }}
        <div class="btn-group">
          <el-button type="text">变更审批人</el-button>
        </div>
      </div>
      <div class="process-container">
        <div class="step-container">
          <scroll-panel wrapper-height="60px" ref="process">
            <div class="step-item" v-for="(item, index) in nodeGroup" :class="(index == (nodeGroup.length - 1) && !isFinish) ? 'active' : ''" :key="index">
              <div class="step-num">{{ index + 1 }}</div>
              <el-tooltip :content="item.handleNameList.toString()">
                <div class="step-handler float-l">{{ item.handleNameList.toString() }}</div>
              </el-tooltip>
              <div class="step-topic">
                {{$dictCode('FlowHandleType', item.stepName).desc}}
              </div>
              <div class="step-time">{{item.time}}</div>
            </div>
            <div class="step-item" :class="isFinish ? 'active' : ''" >
              <div class="step-num" v-if="nodeGroup.length > 0">{{ isFinish ? nodeGroup.length + 1 : '' }}</div>
              <div class="step-topic">{{ isFinish ? $dictCode('FlowStatus', processInfo.status).desc : '完成' }}</div>
              <div class="step-time"></div>
            </div>
          </scroll-panel>
        </div>
        <div
          class="btn-group"
          v-if="$dictCode('FlowStatus', processInfo.status).desc != '已完成'"
        >
          <el-button
            v-if="canConfirm"
            type="primary"
            @click="handleProcess('confirm')"
          >确认</el-button>
          <el-button
            v-if="canDisagree"
            type="danger"
            @click="handleProcess('disagree')"
          >驳回</el-button>
          <el-button
            v-if="isMyProcess"
            type="primary"
            @click="cancelProcess"
          >撤回审批</el-button>
        </div>
      </div>
    </div>
    <!--入网-->
    <div class="box-container" v-if="processInfo.flowTopicType == 1">
      <el-button class="tabs-edit-btn" v-if="editAccess && activeName == 'first'" type="text" @click="editMainInfo">编辑</el-button>
      <el-tabs class="tabs-container" v-model="activeName">
        <el-tab-pane label="合作信息表" name="first">
          <div class="flex-container">
            <div class="flex-wrapper">
              <div class="flex-vertical-item"><span class="flex-vertical-label">商户编号：</span>
                {{ form.mchNo }}
              </div>
              <div class="flex-vertical-item"><span class="flex-vertical-label">商户名称：</span>
                {{ form.mchName }}
              </div>
              <div class="flex-vertical-item"><span class="flex-vertical-label">品牌名称：</span>
                {{ form.branchName }}
              </div>
              <div class="flex-vertical-item"><span class="flex-vertical-label">企业行业类别：</span>
                {{ form.industryTypeName }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item"><span class="flex-vertical-label">预计用工人数：</span>
                {{ form.workerNum }}
              </div>
              <div class="flex-vertical-item"><span class="flex-vertical-label">预期可实现C端签约率区间：</span>
                {{ $dictCode('SignRateLevelEnum', form.signRateLevel).desc }}
              </div>
              <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者单人月经营所得9.7万以下占比：</span>
                {{ form.workerMonthIncomeRate }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item"><span class="flex-vertical-label">月流水预估：</span>
                {{ form.monthMoneySlip }}万元
              </div>
              <div class="flex-vertical-item"><span class="flex-vertical-label">是否可提供服务记录及经营所得计算明细：</span>
                {{ $dictCode('ProvideIncomeDetailTypeEnum', form.signRateLevel).desc }}
              </div>
              <div class="flex-vertical-item"><span class="flex-vertical-label">公司自有业务平台名称：</span>
                {{ form.bizPlatformName }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item flex-vertical-main"><span class="flex-vertical-label">公司网站：</span>
                {{ form.companyWebsite }}
              </div>
            </div>

            <div class="flex-wrapper" v-for="(item, index) in form.positionVoList" :key="index">
              <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者的工作场所：</span>
                {{ $dictCode('WorkPlaceEnum', item.workplaceCode).desc }}
              </div>
              <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者服务类型：</span>
                {{ item.workCategoryName }}
              </div>
              <div class="flex-vertical-item"><span class="flex-vertical-label">建议发票类目：</span>
                <div>
                  <el-tag type="info" v-for="(cate, key) in item.invoiceCategoryList" :key="key">
                    {{ cate.invoiceCategoryName }}
                  </el-tag>
                </div>
              </div>
              <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者服务描述：</span>
                {{ item.serviceDesc }}
              </div>
              <div class="flex-vertical-item"><span class="flex-vertical-label">自由职业者服务所得计算规则：</span>
                {{ item.chargeRuleDesc }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">商户负责人姓名：</span>
                {{ form.contactName }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">商户负责人手机号：</span>
                {{ form.contactPhone }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">销售：</span>
                {{ selectSaler(form.salerId) }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">合伙人：</span>
                {{ form.agentName }}<br>{{ form.agentNo }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item flex-vertical-main">
                <span class="flex-vertical-label">补充信息：</span>
                <div class="clearfix">
                  <span
                    class="flex-vertical-content flex-func-link"
                    v-for="(item, index) in form.supplementFileUrls"
                    :key="index"
                    v-preview="fileUrl + item"
                  >点击查看
                  </span>
                </div>
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item flex-vertical-main">
                <span class="flex-vertical-label">公司宣传文件：</span>
                <div>
                  <el-link
                    class="flex-vertical-content"
                    v-for="(item, index) in form.companyLeafletFileUrls"
                    :key="index"
                    :href="fileUrl + item"
                    type="primary"
                    target="_blank">点击查看</el-link>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="产品报价单" name="second">
          <div class="product-price-list">
            <el-table :data="form.quoteVoList">
              <el-table-column label="id" prop="mainstayMchNo">

              </el-table-column>
              <el-table-column label="代征主体" prop="mainstayMchName">

              </el-table-column>
              <el-table-column label="商户费率" prop="rate">

              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!--主体认证-->
    <div class="box-container" v-if="processInfo.flowTopicType == 2">
      <div class="box-wrapper">
        <div class="subTitle">主体信息 <el-button type="text" @click="editMainInfo" v-if="editAccess">编辑</el-button></div>
        <div class="flex-container">
          <div class="flex-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">企业名称</span>
              {{ form.mchNo}}<br>
              {{ form.mchName }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">统一社会信用代码</span>
              {{ form.taxNo }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">注册地址</span>
              {{ form.registerAddrProvince + ' ' + form.registerAddrCity + ' ' + form.registerAddrTown + ' ' + form.registerAddrDetail }}
            </div>
          </div>
          <div class="flex-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">注册资本（万）</span>
              {{ form.registerAmount }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">经营范围</span>
              {{ form.managementScope }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">营业期限</span>
              <span v-if="$dictCode('ValidityDateTypeEnum', form.managementValidityDateType).desc == '长期有效'">长期有效<br/>{{ form.managementTermBegin }}</span>
              <span v-else>{{ form.managementTermBegin }} 到 {{ form.managementTermEnd }}</span>
            </div>
          </div>
          <div class="flex-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">营业执照</span>
              <template v-if="isPdf(form.businessLicenseFileUrl)">
                <span
                  class="flex-vertical-content func-content"
                  @click="openFile"
                >点击查看</span>
              </template>
              <img
                v-else
                v-preview
                class="showImg"
                :src="businessLicenseFileUrlFormat"
                :origin="form.businessLicenseFileUrl"
                alt=""
              >
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">证件照</span>
              <div v-if="form.idCardCopyFileUrl">
                <img
                  v-preview
                  class="showImg"
                  :src="idCardCopyFileUrlFormat"
                  :origin="form.idCardCopyFileUrl"
                  alt=""
                >
              </div>
              <div v-else>
                <img
                    v-preview
                    class="showImg"
                    :src="idCardHeadFileUrlFormat"
                    :origin="form.idCardHeadFileUrl"
                    alt=""
                  >
                  <img
                    v-preview
                    class="showImg"
                    :src="idCardEmblemFileUrlFormat"
                    :origin="form.idCardEmblemFileUrl"
                    alt=""
                  >
              </div>
            </div>
            <div class="flex-vertical-item">
              <span class="flex-vertical-label">证件类型</span>
              <span>{{ $dictCode('CertificateTypeEnum', form.certificateType).desc }}</span>
            </div>
          </div>
          <div class="flex-wrapper">
            <div class="flex-vertical-item">
              <span class="flex-vertical-label">银行卡号</span>
              <div>{{ form.accountNo }}</div>
            </div>
            <div class="flex-vertical-item">
              <span class="flex-vertical-label">联行号</span>
              <div>{{ form.bankChannelNo }}</div>
            </div>
            <div class="flex-vertical-item">
              <span class="flex-vertical-label">开户银行</span>
              <div>{{ form.bankName }}</div>
            </div>
          </div>
          <div class="flex-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">法定代表人姓名</span>
              {{ form.legalPersonName }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">证件号码</span>
              {{ form.certificateNumber }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">证件有效期</span>

              <span v-if="$dictCode('ValidityDateTypeEnum', form.certificateValidityDateType).desc == '长期有效'">长期有效<br/>{{ form.certificateTermBegin }}</span>
              <span v-else>{{ form.certificateTermBegin }} 到 {{ form.certificateTermEnd }}</span>
            </div>
          </div>
          <div class="flex-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">负责人姓名</span>
              {{ form.contactName }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">负责人手机号</span>
              {{ form.contactPhone }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">常用邮箱</span>
              {{ form.contactEmail }}
            </div>
          </div>
          <div class="flex-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">客服电话</span>
              {{ form.servicePhone }}
            </div>
          </div>

          <div class="flex-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">企业简称</span>
              {{ form.shortName }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">实际经营地址</span>
              {{ form.managementAddrProvince + ' ' + form.managementAddrCity + ' ' + form.managementAddrTown + ' ' + form.managementAddrDetail }}
            </div>
          </div>
          <div class="flex-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">门头照片</span>
              <img
                v-preview
                class="showImg"
                :src="doorPhotoFileUrlFormat"
                :origin="form.doorPhotoFileUrl"
                alt=""
              >
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">办公内景照片</span>
              <img
                v-preview
                class="showImg"
                :src="workIndoorFileUrlFormat"
                :origin="form.workIndoorFileUrl"
                alt=""
              >
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">前台照片</span>
              <img
                v-preview
                class="showImg"
                :src="receptionFileUrlFormat"
                :origin="form.receptionFileUrl"
                alt=""
              >
            </div>
          </div>
          <div class="flex-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">纳税人类型</span>
              {{ $dictCode('TaxPayerTypeEnum', form.taxPayerType).desc }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">开票地址</span>
              {{ form.invoiceAddress }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">联系电话</span>
              {{ form.invoicePhone }}
            </div>
          </div>
          <div class="flex-wrapper">
            <div class="flex-vertical-item"><span class="flex-vertical-label">开票银行</span>
              {{ form.invoiceBankName }}
            </div>
            <div class="flex-vertical-item"><span class="flex-vertical-label">开票账户</span>
              {{ form.invoiceAccountNo }}
            </div>
            <div class="flex-vertical-item" v-if="form.merchantType == 100"><span class="flex-vertical-label">默认开票类目</span>
              {{ form.defaultInvoiceCategoryName }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 创建合伙人 -->
    <div class="box-container" v-if="processInfo.flowTopicType == 3">
      <el-button class="tabs-edit-btn" v-if="editAccess" type="text" @click="editMainInfo">编辑</el-button>
      <el-tabs class="tabs-container" v-model="activeName">
        <el-tab-pane label="合伙人信息" name="first">
          <div class="flex-container">
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">合伙人名称</span>
                {{ form.agentNo }}<br>
                {{ form.agentName }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">邀请人名称</span>
                {{ form.inviterNo }}<br>
                {{ form.inviterName }}
              </div>
              <div class="flex-vertical-item" v-if="form.agentType == 101">
                <span class="flex-vertical-label">注册地址</span>
                {{ form.registerAddrProvince + form.registerAddrCity + form.registerAddrTown + ' ' + form.registerAddrDetail }}
              </div>
            </div>
            <div class="flex-wrapper" v-if="form.agentType == 101">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">经营范围</span>
                {{ form.managementScope }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">营业执照</span>
                <img
                  v-preview
                  class="showImg"
                  :src="businessLicenseFileUrlFormat"
                  :origin="form.businessLicenseFileUrl"
                  alt=""
                >
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">营业期限</span>
                {{ form.managementTermBegin + (form.managementValidityDateType == 1 ? '至' + form.managementTermEnd : '') }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">银行名称</span>
                {{ form.bankName }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">银行账户</span>
                {{ form.accountNo }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">联行号</span>
                {{ form.bankChannelNo }}
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">证件类型</span>
                {{ $dictCode('CertificateTypeEnum', form.certificateType).desc }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">证件期限</span>
                {{ form.certificateTermBegin + (form.certificateValidityDateType == 1 ? '至' + form.certificateTermEnd : '') }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">证件照</span>
                <div v-if="form.idCardCopyFileUrl">
                  <img
                    v-preview
                    class="showImg"
                    :src="idCardCopyFileUrlFormat"
                    :origin="form.idCardCopyFileUrl"
                    alt=""
                  >
                </div>
                <div v-else>
                  <img
                    v-if="form.idCardHeadFileUrl"
                    v-preview
                    class="showImg"
                    :src="idCardHeadFileUrlFormat"
                    :origin="form.idCardHeadFileUrl"
                    alt=""
                  >
                  <img
                    v-if="form.idCardEmblemFileUrl"
                    v-preview
                    class="showImg"
                    :src="idCardEmblemFileUrlFormat"
                    :origin="form.idCardEmblemFileUrl"
                    alt=""
                  >
                </div>
              </div>
            </div>
            <div class="flex-wrapper">
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">联系人</span>
                {{ form.contactName || form.agentName }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">联系方式</span>
                {{ form.contactPhone }}
              </div>
              <div class="flex-vertical-item">
                <span class="flex-vertical-label">邮箱</span>
                {{ form.contactEmail }}
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="产品报价单" name="second">
          <div class="product-price-list">
            <el-table :data="form.quoteVoList">
              <el-table-column label="id" prop="mainstayNo"></el-table-column>
              <el-table-column label="代征主体" prop="mainstayName"></el-table-column>
              <el-table-column label="公式类型" prop="formulaType">
                <template v-slot="{row}">
                  {{ $dictCode('FormulaEnum', row.formulaType).desc }}
                </template>
              </el-table-column>
              <el-table-column label="固定金额" prop="fixedFee">
              </el-table-column>
              <el-table-column label="费率（%）" prop="feeRate">
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="box-container">
      <p class="subTitle">
        操作日志
      </p>
      <div class="table-container">
        <el-table :data="nodeList">
          <el-table-column label="操作类型" prop="handlerType" width="100">
            <template slot-scope="scope">
              {{ $dictCode('FlowHandleType', scope.row.handlerType).desc }}
            </template>
          </el-table-column>
          <el-table-column label="操作员" prop="handlerName" width="100">

          </el-table-column>
          <el-table-column label="执行结果" prop="status" width="100">
            <template v-slot="{row}">
              <el-tag :type="getTagType(row.status)">
                {{ $dictCode('HandleStatus', row.status).desc }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="审批意见/操作行为">
            <template v-slot="{row}">
              <div v-if="row.handlerType == 4 && row.extInfo.editDiffInfo">
                <div
                  v-for="(value, prop) in row.extInfo.editDiffInfo"
                  :key="prop"
                  class="diff-content-container">
                  <el-tooltip :content="explainDiff(value, prop)">
                    <p class="diff-content">{{ explainDiff(value, prop) }}</p>
                  </el-tooltip>
                </div>
              </div>
              <div v-else>
                {{ row.approvalOpinion }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作时间" prop="updateTime" width="200">
          </el-table-column>
          <el-table-column label="耗时" width="200">
            <template slot-scope="scope">
              {{ formatDuration(scope.row.spentTime) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <opnion-dialog
      :visible.sync="visible"
      @close="closeOpnion"></opnion-dialog>
  </div>
</template>

<script>
  import { cancelProcess, getDetailProcessInfo, getDetailProcessNode, handleProcess } from '@/api/waitingHandle'
  import { getStaffListByDepartment } from '@/api/system'
  import { formatDuration } from '@/utils'
  import opnionDialog from './opinionDialog'

  export default {
    components: {
      opnionDialog
    },
    name: 'detailProcess',
    data() {
      return {
        processId: '',
        activeStep: 0,
        activeName: 'first',
        processInfo: {},
        nodeList: [],
        form: {},
        processNodeId: '', // 当前操作人结点的id

        visible: false, // 审核意见输入弹窗控制
        approvalOpinion: '',
        platform : '',
        handleType: '',

        staffList: [],

        nodeGroup: [],

        processInstanceId: '', // 当前流程实例ID
        processInstanceImg: '',

        businessLicenseFileUrlFormat: "",
        idCardCopyFileUrlFormat: "",
        idCardHeadFileUrlFormat: "",
        idCardEmblemFileUrlFormat: "",
        doorPhotoFileUrlFormat: "",
        workIndoorFileUrlFormat: "",
        receptionFileUrlFormat: "",
      }
    },
    watch: {
      "form.businessLicenseFileUrl": {
        async handler (val) {
          const fileMsg = await this.formatFileMsg(val)
          this.businessLicenseFileUrlFormat = fileMsg.fileUrl
        },
        immediate: true,
      },
      "form.idCardCopyFileUrl": {
        async handler (val) {
          const fileMsg = await this.formatFileMsg(val)
          this.idCardCopyFileUrlFormat = fileMsg.fileUrl
        },
        immediate: true,
      },
      "form.idCardHeadFileUrl": {
        async handler (val) {
          const fileMsg = await this.formatFileMsg(val)
          this.idCardHeadFileUrlFormat = fileMsg.fileUrl
        },
        immediate: true,
      },
      "form.idCardEmblemFileUrl": {
        async handler (val) {
          const fileMsg = await this.formatFileMsg(val)
          this.idCardEmblemFileUrlFormat = fileMsg.fileUrl
        },
        immediate: true,
      },
      "form.doorPhotoFileUrl": {
        async handler (val) {
          const fileMsg = await this.formatFileMsg(val)
          this.doorPhotoFileUrlFormat = fileMsg.fileUrl
        },
        immediate: true,
      },
      "form.workIndoorFileUrl": {
        async handler (val) {
          const fileMsg = await this.formatFileMsg(val)
          this.workIndoorFileUrlFormat = fileMsg.fileUrl
        },
        immediate: true,
      },
      "form.receptionFileUrl": {
        async handler (val) {
          const fileMsg = await this.formatFileMsg(val)
          this.receptionFileUrlFormat = fileMsg.fileUrl
        },
        immediate: true,
      }
    },
    computed: {
      isHandlerByMe() {
        if (this.lastOperateNode) {
          return this.lastOperateNode['pendingIdList'].indexOf(this.userID) > -1;
        } else {
          return false
        }
      },
      isMyProcess() {
        return this.isAdmin || this.processInfo.initiatorId == this.userID;
      },
      isFinish() {
        return this.$dictCode('FlowStatus', this.processInfo.status).desc == '已完成';
      },
      editAccess() {
        return !this.isFinish && this.isHandlerByMe;
      },
      isSingleSign() {
        // 当前流程是否或签
        let result = false;
        for (let i = this.nodeGroup.length - 1; i >= 0; i--) {
          if (this.nodeGroup[i].handlerType == 2) {
            result = true
            break;
          }
        }
        return result;
      },
      currentPendIdList() {
        // 当前流程（第一个非编辑节点）的审批人ID
        let result = [];
        for (let i = this.nodeGroup.length - 1; i >= 0; i--) {
          if (this.nodeGroup[i].handlerType == 2 || this.nodeGroup[i].handlerType == 1) {
            result = this.nodeGroup[i].pendingIdList;
            break
          }
        }
        return result;
      },
      canConfirm() {
        if (this.isAdmin) {
          if (this.isSingleSign) {
            return this._canOperate;
          } else {
            return this.isHandlerByMe
          }
        } else {
          return this.isHandlerByMe
        }
      },
      canDisagree() {
        return this.isAdmin ? this._canOperate : this.processInfo.status == 101 && this.isHandlerByMe;
      },
      _canOperate() {
        // admin情况进一步判断能否操作
        if (!this.lastOperateNode) return false;
        let node = this.lastOperateNode;
        if (node.handlerType == 3 || node.handlerType == 5) {
          return false
        }
        return true;
      },
      lastOperateNode() {
        if (!this.nodeGroup.length) return false;
        let node = {};
        for(let i = this.nodeGroup.length - 1; i >= 0; i--) {
          if (this.nodeGroup[i].handlerType != 4) {
            node = this.nodeGroup[i];
            break;
          }
        }
        return node
      }
    },
    mounted() {
      this.processId = this.$route.query.processId
      this.getProcessInfo()
      this.getNodeInfo()
      getStaffListByDepartment({ departmentId: 0 }).then(
        (response) => {
          this.staffList = response.data;
        }
      );
    },
    methods: {
      formatDuration,
      getProcessInfo() {
        getDetailProcessInfo({
          approvalFlowId: this.processId
        }).then(response => {
          this.processInfo = response.data
          this.activeStep = response.data.stepNum

          // 获取创建的信息
          this.form = Object.assign({}, JSON.parse(response.data.extInfo))
        })
      },
      async getNodeInfo() {
        const response = await getDetailProcessNode({
          approvalFlowId: this.processId
        })
        response.data.forEach(item => {
          item.extInfo = JSON.parse(item.extInfo);
          if (item.extInfo.editDiffInfo) {
            item.extInfo.editDiffInfo = JSON.parse(item.extInfo.editDiffInfo);
          }
        })
        this.nodeList = response.data
        // 对步骤结点进行分组
        const nodeGroup = [];
        let isGetProcessNodeId = false; // 是否已经获取了用户操作结点的id
        let order = 0;
        for (let i = 0; i < this.nodeList.length; i++) {
          !this.activeStep && (this.activeStep = this.nodeList[i].stepNum);
          // 获取当前用户操作结点的id
          if (!isGetProcessNodeId
              && this.nodeList[i].handlerId == this.$store.state.user.userData.id
              && this.$dictCode('HandleStatus', this.nodeList[i].status).desc !== '同意') {
            isGetProcessNodeId = true;
            this.processNodeId = this.nodeList[i].id;
          }

          if (i == 0 || this.nodeList[i].handlerType !== nodeGroup[nodeGroup.length - 1].handlerType) {
            nodeGroup.push({
              stepNum: this.nodeList[i].stepNum,
              stepName: this.nodeList[i].handlerType,
              handleNameList: [],
              handleIdList: [],
              pendingIdList: [], // 该节点未审批的id
              handlerType: this.nodeList[i].handlerType,
              time: this.nodeList[i].updateTime.replace(/\:\d{2}/, '')
            })
            order++;
          }
          const nowNode = this.nodeList[i];
          const lastNode = nodeGroup[nodeGroup.length - 1];
          // 判断当前组是否包含该审批人，不包含才添加进去
          if (!lastNode['handleIdList'].includes(nowNode.handlerId)) {
            lastNode['handleNameList'].push(nowNode.handlerName);
            lastNode['handleIdList'].push(nowNode.handlerId);
          }
          // 若当前审批人的审批状态为未审批则加入
          if (nowNode['status'] === 103 && !lastNode['pendingIdList'].includes(nowNode.handlerId)) {
            lastNode['pendingIdList'].push(nowNode.handlerId);
          }
        }
        this.nodeGroup = nodeGroup.reverse();

        await this.$nextTick();
        this.$refs.process.scrollTo('.active')
      },
      handleProcess(result) {
        if (this.handleType !== result) {
          this.handleType = result;
          this.visible = true;
          return;
        }
        let processNodeId = this.processNodeId;
        if (this.isAdmin && this.currentPendIdList.indexOf(this.userID) == -1) {
          // 管理员审批
          for (let i = 0; i < this.nodeList.length; i++) {
            if (this.nodeList[i].status == 103) {
              processNodeId = this.nodeList[i].id
              break
            }
          }
        }
        handleProcess({
          approvalDetailId: processNodeId,
          handleStatus: result == 'confirm' ? 100 : 101,
          flowTopicType: this.processInfo.flowTopicType,
          approvalOpinion: this.approvalOpinion,
          platform : this.processInfo.platform
        }).then(response => {
          this.$message.success('处理成功');
          this.getNodeInfo()
          this.getProcessInfo()
        })
      },
      cancelProcess() {
        this.$confirm('确定要撤回审批吗？', {
          type: 'warning'
        }).then(() => {
          cancelProcess({
            approvalFlowId: this.processInfo.id
          }).then(response => {
            this.$message.success('操作成功');
            this.getNodeInfo()
            this.getProcessInfo()
          })
        })
      },
      editMainInfo() {
        // 跳去主体认证
        if (this.processInfo.flowTopicType == 2) {
          let form = JSON.parse(JSON.stringify(this.form));
          sessionStorage.setItem('mainInfo', JSON.stringify(form));
          this.$router.push({
            path: '/merchant/merchantAuth',
            query: {
              mchNo: this.form.mchNo,
              processId: this.processId
            }
          });
        } else if (this.processInfo.flowTopicType == 1) {
          // 跳去创建商户
          let form = JSON.parse(JSON.stringify(this.form));
          form.processId = this.processId
          for(let key in form.positionVoList) {
            // 主动触发选择下拉框
            form.positionVoList[key].workplaceCode += '';
            form.positionVoList[key].workCategoryCode += '';
          }
          sessionStorage.setItem('cooperationInfo', JSON.stringify(form));
          this.$router.push({
            path: '/merchant/merchantManagement/createMerchant',
            query: {
              actionType: 'EDIT',
              platform: this.processInfo.platform
            }
          });
        } else if (this.processInfo.flowTopicType == 3) {
          let form = JSON.parse(JSON.stringify(this.form));
          form.processId = this.processId;
          sessionStorage.setItem('agentInfo', JSON.stringify(form));
          let url = this.form.agentType == 100 ? '/agent/createPerson' : '/agent/createCompany'
          this.$router.push({
            path: url,
            query: {
              actionType: 'EDIT',
              processId: this.processId,
            }
          })
        }
      },
      closeOpnion(data) {
        this.approvalOpinion = data && data.opnion;
        if (data && data.action == 'confirm') {
          this.handleProcess(this.handleType);
        } else {
          this.handleType = '';
        }
      },
      selectSaler(id) {
        if (!this.staffList.length) {
          return '';
        }
        for (let item of this.staffList) {
          if (item.id == this.form.salerId) {
            return item.realName
          }
        }
      },
      getTagType(status) {
        switch(Number(status)) {
          case 100:
            return 'success';
          case 101:
            return 'danger';
          case 103:
            return 'info';
          default:
            return ''
        }
      },
      checkFile(file) {
        window.open(this.fileUrl + file);
      },
      explainDiff(data, prop) {
        let result = '';
        if (Array.isArray(data)) {
          result = `${prop} 中`
          data.forEach((item, index) => {
            result += this.explainDiff(item, index + 1)
          })
        } else {
          if (!data) return '';
          if (data.newValue || data['新']) {
            result = `将${typeof prop == 'number' ? '' : prop + '由'} ${data.oldValue || data['旧'] || '空'} 改成 ${data.newValue || data['新'] || '空'}`
          } else if (typeof prop == 'number') {
            result = `第${prop}项`
            let keys = Object.keys(data);
            keys.forEach(key => {
              result += this.explainDiff(data[key], key);
            })
          } else {
            result = `将 ${prop}由 ${data.oldValue || data['旧' || '空']} 改成 ${data.newValue || data['新'] || '空'}`
          }
        }
        return result;
      },
      openFile() {
        window.open(this.fileUrl + this.form.businessLicenseFileUrl)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .page-container {
    background: transparent;
    box-shadow: none;
    border: none;

    h2 {
      margin-bottom: 0;
      font-size: 24px;
    }

    ::v-deep .el-tabs__nav {
      margin-left: 20px;

      .el-tabs__item {
        padding-top: 10px;
        padding-bottom: 10px;
        box-sizing: content-box;
      }
    }

    .subTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 20px;
      font-size: 16px;
    }
  }

  .process-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 40px 30px;
    .step-container {
      width: 600px;
      margin-left: 10px;
      .step-item {
        display: inline-block;
        position: relative;
        margin-right: 16px;
        padding: 8px 50px;
        border: 1px solid $deepGray;
        border-radius: 25px;
        line-height: 1.3;
        vertical-align: bottom;

        &.active {
          color: #20a0ff;
        }
        .step-handler {
          width: 60px;
          height: 20px;
          line-height: 20px;
          @include text-ellipsis;
        }
        .step-num {
          @include position-tb-center(absolute);
          left: 8px;
          width: 30px;
          height: 30px;
          line-height: 30px;
          margin: 0 auto;
          border: 1px solid #ccc;
          border-radius: 50%;
          text-align: center;
        }
        .step-topic, .step-time {
          height: 20px;
          line-height: 20px;
        }
      }
    }
  }

  .el-tabs .flex-container{
    padding-top: 0;
  }

  .tabs-container {
    .el-tag {
      margin: 0 5px 5px 0;
    }
  }
  .tabs-edit-btn {
    position: absolute;
    top: 32px;
    right: 32px;
    z-index: 99;
  }

  .table-container {
    margin-bottom: 20px;
  }
  .el-table {
    width: calc(100% - 40px);
    margin: 0 20px;
  }

  .showImg {
    width: 80px;
    height: 80px;
    &+.showImg {
      margin-left: 20px;
    }
  }

  .btn-group .el-button {
    margin: 8px;
  }
  .flex-vertical-content {
    margin-right: 8px;
    margin-bottom: 8px;
  }
  .diff-content-container {
    padding: 8px 0;
    border-bottom: 1px solid #ccc;
    &:last-child {
      border-bottom: none;
    }
    .diff-content {
      max-width: 250px;
      overflow-x: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
</style>
