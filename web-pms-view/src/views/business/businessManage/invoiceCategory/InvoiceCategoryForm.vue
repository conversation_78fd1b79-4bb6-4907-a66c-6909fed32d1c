<template>
  <el-dialog :visible.sync="show" :close-on-click-modal="false" :before-close="closeForm" :title="(actionType==='ADD'&&'新增发票类目')||(actionType==='EDIT'&&'编辑发票类目')||''"
             append-to-body width="600px">
    <el-form ref="form" :inline="true" :model="invoiceCategory" :rules="rules" size="small" label-width="120px">
      <el-form-item label="发票类目编码" prop="invoiceCategoryCode">
        <el-input v-model="invoiceCategory.invoiceCategoryCode" :disabled="actionType!=='ADD'" placeholder="编码" style="width: 400px;"/>
      </el-form-item>
      <el-form-item label="发票类目名称" prop="invoiceCategoryName">
        <el-input v-model="invoiceCategory.invoiceCategoryName" placeholder="名称" style="width: 400px;"/>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="closeForm">取消</el-button>
      <el-button :loading="loading" type="primary" @click="doSubmit">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { addInvoiceCategory } from '../../../../api/business'
  import { editInvoiceCategory } from '../../../../api/business'

  export default {
    data() {
      return {
        actionType: undefined,
        loading: false,
        show: false,
        invoiceCategory:{
          invoiceCategoryCode: '',
          invoiceCategoryName: ''
        },
        rules: {
          invoiceCategoryCode: [{required: true, message: '请输入发票类目编码', trigger: 'blur'}],
          invoiceCategoryName: [{required: true, message: '请输入发票类目名称', trigger: 'blur'}],
        }
      }
    },
    methods: {
      doSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.loading = true
            if(this.actionType==='ADD'){
              this.doAddInvoiceCategory();
            } else if (this.actionType==='EDIT'){
              this.doEditInvoiceCategory()
            }
          }
        })
      },
      doAddInvoiceCategory() {
        addInvoiceCategory({
          ...this.invoiceCategory
        }).then(({data}) => {
          this.$message.success(data)
          this.closeForm()
          this.$emit('success')
        }) .finally(() => this.loading = false)
      },
      doEditInvoiceCategory() {
        editInvoiceCategory({
          ...this.invoiceCategory
        }).then(({data}) => {
          this.$message.success(data)
          this.closeForm()
          this.$emit('success')
        }) .finally(() => this.loading = false)
      },
      closeForm() {
        this.show = false
        this.loading = false
        this.$refs.form.resetFields()
      }
    }
  }
</script>
