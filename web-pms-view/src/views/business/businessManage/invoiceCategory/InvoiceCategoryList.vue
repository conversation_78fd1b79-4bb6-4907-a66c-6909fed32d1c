<template>
  <div class="box-container" v-loading="loading">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">类目编码：</span>
            <el-input v-model="searchParam.invoiceCategoryCode"/>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">类目名称：</span>
            <el-input v-model="searchParam.invoiceCategoryNameLike"/>
          </div>
        </div>
      </div>
      <div class="search-wrapper">
        <div class="search-btn-group">
          <el-button @click="refreshList" type="primary"><i class="el-icon-search"/>查询</el-button>
          <el-button @click="addInvoiceCategory" v-permission="'config:invoiceCategory:update'"><i class="el-icon-plus"/>添加发票类目</el-button>
        </div>
      </div>
    </div>

    <el-main class="main-page-content">
      <el-table :data="pageResult.data" row-key="id">
        <el-table-column type="index" :index="getIndex"/>
        <el-table-column label="发票类目编码" prop="invoiceCategoryCode"/>
        <el-table-column label="发票类目名称" prop="invoiceCategoryName"/>

        <el-table-column label="创建时间" prop="createTime">
          <p slot-scope="{row, column}" v-html="renderTime(row[column['property']])"></p>
        </el-table-column>

        <el-table-column label="更新人" prop="updator"/>
        <el-table-column label="操作" v-slot="{row}">
          <el-button type="text" size="small" @click="editInvoiceCategory(row)" v-permission="'config:invoiceCategory:update'">编辑</el-button>
          <el-button type="text" size="small" @click="deleteInvoiceCategory(row)" v-permission="'config:invoiceCategory:delete'">删除</el-button>
        </el-table-column>
      </el-table>
    </el-main>
    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10,50,100]"
        :total="pageResult.totalRecord"
        :page-size.sync="pageParam.pageSize"
        :current-page.sync="pageParam.pageCurrent"
        @size-change="refreshList"
        @current-change="refreshList"/>
    </div>
    <invoice-category-form ref="invoiceCategoryForm" v-on:success="refreshList"/>
  </div>
</template>

<script>
  import InvoiceCategoryForm from "./InvoiceCategoryForm"
  import { listInvoiceCategoryPage } from '../../../../api/business'
  import { deleteInvoiceCategory } from '../../../../api/business'

  export default {
    name: "PmsInvoiceCategoryList",
    components: {
      InvoiceCategoryForm,
    },
    data() {
      return {
        loading: false,
        searchParam: {},
        pageParam: {pageCurrent: 1, pageSize: 10},
        pageResult: {}
      };
    },
    methods: {
      refreshList() {
        this.loading = true;
        listInvoiceCategoryPage({
          pageCurrent: this.pageParam.pageCurrent,
          pageSize: this.pageParam.pageSize,
          ...this.searchParam
        }).then(response => {
          this.pageResult = response.data;
          this.loading = false;
        })
      },

      editInvoiceCategory(row) {
        this.$refs.invoiceCategoryForm.actionType = "EDIT"
        this.$refs.invoiceCategoryForm.show = true;
        this.$refs.invoiceCategoryForm.invoiceCategory.invoiceCategoryCode = row.invoiceCategoryCode;
        this.$refs.invoiceCategoryForm.invoiceCategory.invoiceCategoryName = row.invoiceCategoryName;
      },

      addInvoiceCategory() {
        this.$refs.invoiceCategoryForm.actionType = "ADD"
        this.$refs.invoiceCategoryForm.show = true;
      },
      deleteInvoiceCategory(row) {
        this.$confirm(`确定删除当前发票类目吗？`)
          .then(() =>
            deleteInvoiceCategory({
              id:row.id
            }).then(response => {
              this.$message.success(response.data)
              this.$emit('success')
              this.refreshList()
            })
          );
      },
      getIndex(index) {
        return (this.pageParam.pageCurrent - 1) * this.pageParam.pageSize + index + 1;
      },
    },
    mounted() {
      this.refreshList();
    }
  };
</script>
