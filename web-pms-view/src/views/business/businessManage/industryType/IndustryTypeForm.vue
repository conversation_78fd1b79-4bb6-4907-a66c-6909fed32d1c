<template>
  <el-dialog :visible.sync="show" :close-on-click-modal="false" :before-close="closeForm" :title="(actionType==='ADD'&&'新增类型') || (actionType==='EDIT'&&'编辑类型')||''" append-to-body
             width="600px">
    <el-form ref="form" :inline="true" :model="industryType" :rules="rules" size="small" label-width="120px">
      <el-form-item label="父类型" v-if="actionType==='ADD'">
        <el-input v-model="industryType.parentName" :disabled="true" style="width: 400px;"/>
      </el-form-item>
      <el-form-item label="行业类型编号" prop="industryTypeCode">
        <el-input v-model="industryType.industryTypeCode" :disabled="actionType==='EDIT'" placeholder="编号" style="width: 400px;"/>
      </el-form-item>
      <el-form-item label="行业类型名称" prop="industryTypeName">
        <el-input v-model="industryType.industryTypeName" placeholder="名称" style="width: 400px;"/>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="closeForm">取消</el-button>
      <el-button :loading="loading" type="primary" @click="doSubmit">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { addIndustryType } from '../../../../api/business'
  import { editIndustryType } from '../../../../api/business'

  export default {
    data() {
      return {
        loading: false,
        show: false,
        actionType: undefined,
        industryType: {
          parentId: 0,
          parentName: '',
          industryTypeCode: '',
          industryTypeName: '',
        },
        invoiceCategoryList: [],
        rules: {
          industryTypeName: [{required: true, message: '请输入行业类型名称', trigger: 'blur'}],
          industryTypeCode: [{required: true, message: '请输入行业类型编号', trigger: 'blur'}],
          parentId: [{required: true, message: '请选择父行业类型', trigger: 'blur'}],
        }
      }
    },
    mounted(){
    },
    methods: {
      doSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.loading = true
            this.actionType === 'ADD' && this.doAdd();
            this.actionType === 'EDIT' && this.doEdit();
          }
        })
      },
      doAdd() {
        addIndustryType({
          ...this.industryType
        }).then(({data}) => {
          this.$message.success(data)
          this.closeForm()
          this.$emit('success')
        }) .finally(() => this.loading = false)
      },
      doEdit() {
        editIndustryType({
          ...this.industryType
        }).then(({data}) => {
          this.$message.success(data)
          this.closeForm()
          this.$emit('success')
        }) .finally(() => this.loading = false)
      },
      closeForm() {
        this.show = false
        this.loading = false
        this.form = {}
        this.$refs.form.resetFields()
      }
    }
  }
</script>
