<template>
  <div class="box-container">
    <div class="search-container">
      <div class="function-bar">
        <el-button
          type="primary"
          size="small"
          @click="refreshList"
        ><i class="el-icon-search" />刷新</el-button>
        <el-button
          size="small"
          @click="add"
          v-permission="'config:industryType:save'"
        ><i class="el-icon-plus" />添加顶级类型</el-button>
      </div>
    </div>
    <el-main class="content-container">
      <el-table
        :row-class-name="rowClassHandler"
        :data="treeData"
        row-key="id"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      >
        <el-table-column
          label="行业类型名称"
          prop="industryTypeName"
        />
        <el-table-column
          label="行业类型编号"
          prop="industryTypeCode"
        />
        <el-table-column
          label="操作"
          v-slot="{row}"
        >
          <el-button
            type="text"
            size="small"
            @click="edit(row)"
            v-permission="'config:industryType:update'"
          >编辑</el-button>
          <el-button
            type="text"
            size="small"
            @click="del(row)"
            v-permission="'config:industryType:delete'"
          >删除</el-button>
          <el-button
            v-if="row.parentId == 0"
            type="text"
            size="small"
            @click="add(row)"
            v-permission="'config:industryType:save'"
          >添加子类型</el-button>
        </el-table-column>
      </el-table>
    </el-main>
    <industry-type-form
      ref="form"
      v-on:success="refreshList"
    />
  </div>
</template>

<script>
  import IndustryTypeForm from './IndustryTypeForm'
  import { listAllIndustryType } from '@/api/business'
  import { deleteIndustryType } from '@/api/business'


  const buildTreeData = function (pid, pidGroup, resultArr) {
    if (!pidGroup[pid]) {
      return;
    }
    pidGroup[pid].forEach(f => {
      resultArr.push(f)
      if (pidGroup[f.id]) {
        f.children = []
        buildTreeData(f.id, pidGroup, f.children)
      }
    })
  }

  export default {
    name: 'PmsIndustryTypeList',
    components: {
      IndustryTypeForm
    },
    data() {
      return {
        functions: []
      }
    },
    methods: {
      refreshList() {
        this.loading = true;
        listAllIndustryType().then(response => {
          this.functions = response.data;
          this.loading = false;
        })
      },
      add(row) {
        this.$refs.form.actionType = 'ADD';
        this.$refs.form.industryType.parentId = (row && row.id) || 0;
        this.$refs.form.industryType.parentName = row && row.industryTypeName;
        this.$refs.form.show = true
      },
      edit(row) {
        this.$refs.form.actionType = 'EDIT'
        this.$refs.form.industryType.industryTypeCode = row.industryTypeCode;
        this.$refs.form.industryType.industryTypeName = row.industryTypeName;
        this.$refs.form.show = true;
      },
      del(row) {
        this.$confirm(`确定删除当前行业类型吗？`)
          .then(() =>
            deleteIndustryType({
              id: row.id
            }).then(response => {
              this.$message.success(response.data)
              this.$emit('success')
              this.refreshList()
            })
          );
      },
      rowClassHandler({row}) {
        if (row.parentId > 0) {
          return 'child-row';
        }
        return '';
      },
    },
    computed: {
      treeData() {
        let pidGroup = []
        this.functions.forEach(f => {
          if (!pidGroup[f.parentId]) {
            pidGroup[f.parentId] = [f]
          } else {
            pidGroup[f.parentId].push(f)
          }
        })
        let resultArr = []
        buildTreeData(0, pidGroup, resultArr)
        return resultArr
      }
    },
    mounted() {
      this.refreshList()
    }
  }
</script>

