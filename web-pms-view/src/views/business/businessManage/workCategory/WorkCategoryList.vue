<template>
  <div class="box-container">
    <div class="search-container">
      <div class="function-bar">
        <el-button
          type="primary"
          size="small"
          @click="refreshList"
        ><i class="el-icon-search" />刷新</el-button>
        <el-button
          size="small"
          @click="add"
          v-permission="'config:workCategory:save'"
        ><i class="el-icon-plus" />添加顶级类目</el-button>
      </div>
    </div>
    <el-main class="content-container">
      <el-table
        :data="treeData"
        row-key="id"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        :row-class-name="rowClassHandler"
      >
        <el-table-column
          label="岗位类目名称"
          prop="workCategoryName"
        />
        <el-table-column
          label="岗位类目编号"
          prop="workCategoryCode"
        />

        <el-table-column label="发票类目名称">
          <template v-slot="{row}">
            <el-tag
              type="info"
              v-for="(item, index) in row.jsonEntity.invoiceCategoryList"
              :key="index"
            >
              {{ item.invoiceCategoryName }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          label="操作"
          v-slot="{row}"
        >
          <el-button
            type="text"
            size="small"
            @click="edit(row)"
            v-permission="'config:workCategory:update'"
          >编辑</el-button>
          <el-button
            type="text"
            size="small"
            @click="del(row)"
            v-permission="'config:workCategory:delete'"
          >删除</el-button>
          <el-button
            v-if="row.parentId == 0"
            type="text"
            size="small"
            @click="add(row)"
            v-permission="'config:workCategory:save'"
          >添加子类目</el-button>
        </el-table-column>
      </el-table>
    </el-main>
    <work-category-form
      ref="form"
      v-on:success="refreshList"
    />
  </div>
</template>

<script>
  import WorkCategoryForm from './WorkCategoryForm'
  import { listAllWorkCategory } from '@/api/business'
  import { deleteWorkCategory } from '@/api/business'


  const buildTreeData = function (pid, pidGroup, resultArr) {
    if (!pidGroup[pid]) {
      return;
    }
    pidGroup[pid].forEach(f => {
      resultArr.push(f)
      if (pidGroup[f.id]) {
        f.children = []
        buildTreeData(f.id, pidGroup, f.children)
      }
    })
  }

  export default {
    name: 'PmsWorkCategoryList',
    components: {
      WorkCategoryForm
    },
    data() {
      return {
        functions: []
      }
    },
    methods: {
      refreshList() {
        this.loading = true;
        listAllWorkCategory().then(response => {
          this.functions = response.data;
          this.loading = false;
        })
      },
      add(row) {
        this.$refs.form.actionType = 'ADD';
        this.$refs.form.workCategory.parentId = (row && row.id) || 0;
        this.$refs.form.workCategory.parentName = row && row.workCategoryName;
        this.$refs.form.show = true
      },
      edit(row) {
        // 直接设置表单值
        this.$refs.form.actionType = 'EDIT';
        this.$refs.form.workCategory.id = row.id;
        this.$refs.form.workCategory.parentId = row.parentId;
        this.$refs.form.workCategory.parentName = row.parentName;
        this.$refs.form.workCategory.workCategoryCode = row.workCategoryCode;
        this.$refs.form.workCategory.workCategoryName = row.workCategoryName;
        this.$refs.form.workCategory.workDesc = row.workDesc || '';
        this.$refs.form.workCategory.businessDesc = row.businessDesc || ''; // 添加企业从事业务
        this.$refs.form.workCategory.chargeRuleDesc = row.chargeRuleDesc || '';

        // 处理发票类目列表
        if (row.jsonEntity && row.jsonEntity.invoiceCategoryList) {
          this.$refs.form.workCategory.invoiceCategoryList = row.jsonEntity.invoiceCategoryList.map(item => {
            return {
              value: item.invoiceCategoryCode,
              invoiceCategoryName: item.invoiceCategoryName,
              invoiceCategoryCode: item.invoiceCategoryCode,
            }
          });
        } else {
          this.$refs.form.workCategory.invoiceCategoryList = [];
        }

        this.$refs.form.show = true;
      },
      del(row) {
        this.$confirm(`确定删除当前岗位类目吗？`)
          .then(() =>
            deleteWorkCategory({
              id: row.id
            }).then(response => {
              this.$message.success(response.data)
              this.$emit('success')
              this.refreshList()
            })
          );
      },
      rowClassHandler({row}) {
        if (row.parentId > 0) {
          return 'child-row';
        }
        return '';
      }
    },
    computed: {
      treeData() {
        let pidGroup = []
        this.functions.forEach(f => {
          if (!pidGroup[f.parentId]) {
            pidGroup[f.parentId] = [f]
          } else {
            pidGroup[f.parentId].push(f)
          }
        })
        let resultArr = []
        buildTreeData(0, pidGroup, resultArr)
        return resultArr
      }
    },
    mounted() {
      this.refreshList()
    }
  }
</script>

<style lang="scss" scoped>
  .box-container {
    .el-tag {
      margin-right: 5px;
      margin-bottom: 5px;
    }
  }
</style>

