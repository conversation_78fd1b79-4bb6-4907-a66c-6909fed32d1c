<template>
  <el-dialog :visible.sync="show" :close-on-click-modal="false" :before-close="closeForm" :title="(actionType==='ADD'&&'新增类目') || (actionType==='EDIT'&&'编辑类目')||''" append-to-body
             width="600px">
    <el-form ref="form" :inline="true" :model="workCategory" :rules="rules" size="small" label-width="120px">
      <el-form-item label="父类目" v-if="actionType==='ADD'">
        <el-input v-model="workCategory.parentName" :disabled="true" style="width: 400px;"/>
      </el-form-item>
      <el-form-item label="岗位类目编号" prop="workCategoryCode">
        <el-input v-model="workCategory.workCategoryCode" :disabled="actionType==='EDIT'" placeholder="编号" style="width: 400px;"/>
      </el-form-item>
      <el-form-item label="岗位类目名称" prop="workCategoryName">
        <el-input v-model="workCategory.workCategoryName" placeholder="名称" style="width: 400px;"/>
      </el-form-item>
      <el-form-item label="发票类目" prop="invoiceCategoryList">
        <el-select
          clearable
          multiple
          ref="invoiceSelect"
          v-model="workCategory.invoiceCategoryList"
          style="width: 400px;"
          placeholder="请选择发票类目">
          <el-option
            v-for="invoice in invoiceCategoryList"
            :label="invoice.invoiceCategoryName"
            :key="invoice.invoiceCategoryCode"
            :value="{
              invoiceCategoryCode: invoice.invoiceCategoryCode,
              invoiceCategoryName: invoice.invoiceCategoryName
            }"/>
        </el-select>
      </el-form-item>
      <el-form-item label="企业从事业务" prop="businessDesc">
        <el-input v-model="workCategory.businessDesc" placeholder="请描述用工企业具体从事业务" type="textarea" style="width: 400px;"/>
      </el-form-item>
      <el-form-item label="岗位服务描述" prop="workDesc">
        <el-input v-model="workCategory.workDesc" placeholder="岗位描述" type="textarea" style="width: 400px;"/>
      </el-form-item>
      <el-form-item label="所得计算规则" prop="chargeRuleDesc">
        <el-input v-model="workCategory.chargeRuleDesc" placeholder="计算规则描述" type="textarea" style="width: 400px;"/>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="closeForm">取消</el-button>
      <el-button :loading="loading" type="primary" @click="doSubmit">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { listInvoiceCategoryPage } from '@/api/business'
  import { addWorkCategory } from '@/api/business'
  import { editWorkCategory } from '@/api/business'
  const lengthValid = (rule, value, callback) => {
    if (value.length >= 1) {
      callback();
    } else {
      callback(new Error('请选择发票类目'));
    }
  }
  export default {
    data() {
      return {
        loading: false,
        show: false,
        actionType: undefined,
        workCategory: {
          parentId: 0,
          parentName: '',
          invoiceCategoryCode: '',
          workCategoryName: '',
          workDesc: '',
          businessDesc: '',
          chargeRuleDesc: '',
          invoiceCategoryList: [],
        },
        invoiceCategoryList: [],
      }
    },
    computed: {
      rules() {
        return {
          workCategoryName: [{required: true, message: '请输入岗位类目名称', trigger: 'blur'}],
          workCategoryCode: [{required: this.actionType === 'ADD', message: '请输入岗位类目编号', trigger: 'blur'}],
          invoiceCategoryList: [
            {required: true, message: '请选择发票类目', trigger: 'blur', validator: lengthValid},
            {validator: lengthValid, trigger: 'blur'},
          ],
          parentId: [{required: true, message: '请选择父岗位类目', trigger: 'blur'}],
          workDesc: [{ required: true, message: '请输入岗位服务描述', trigger: 'blur'}],
          chargeRuleDesc: [{ required: true, message: '请输入所得计算规则', trigger: 'blur'}]
        };
      }
    },
    mounted(){
      this.listInvoiceCategory()
    },
    methods: {
      async open(data, type = 'ADD') {
        // 先获取发票类目数据
        await this.listInvoiceCategory()
        
        this.show = true
        this.actionType = type

        if (type === 'EDIT' && data) {
          // 先清空表单
          this.workCategory = {
            parentId: 0,
            parentName: '',
            invoiceCategoryCode: '',
            workCategoryName: '',
            workDesc: '',
            businessDesc: '',
            chargeRuleDesc: '',
            invoiceCategoryList: [],
          }

          // 延迟设置数据，确保DOM更新完成
          setTimeout(() => {
            // 手动设置每个字段
            this.workCategory.id = data.id
            this.workCategory.parentId = data.parentId
            this.workCategory.parentName = data.parentName
            this.workCategory.workCategoryCode = data.workCategoryCode
            this.workCategory.workCategoryName = data.workCategoryName
            this.workCategory.workDesc = data.workDesc || ''
            this.workCategory.businessDesc = data.businessDesc || ''
            this.workCategory.chargeRuleDesc = data.chargeRuleDesc || ''

            // 处理发票类目列表 - 从jsonEntity中获取
            if (data.jsonEntity && data.jsonEntity.invoiceCategoryList) {
              this.workCategory.invoiceCategoryList = data.jsonEntity.invoiceCategoryList.map(item => {
                return {
                  invoiceCategoryCode: item.invoiceCategoryCode,
                  invoiceCategoryName: item.invoiceCategoryName,
                  value: item.invoiceCategoryCode  // 确保有value字段
                }
              });
            }

            // 再次检查设置后的数据
            console.log('设置后的数据:', this.workCategory)
          }, 100)
        } else if (type === 'ADD' && data) {
          this.workCategory = {
            parentId: data.id || 0,
            parentName: data.workCategoryName || '根类目',
            workCategoryCode: '',
            workCategoryName: '',
            workDesc: '',
            businessDesc: '',
            chargeRuleDesc: '',
            invoiceCategoryList: [],
          }
        }
      },
      doSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.loading = true
            this.actionType === 'ADD' && this.doAdd();
            this.actionType === 'EDIT' && this.doEdit();
          }
        })
      },
      doAdd() {
        let invoiceCategoryList = [...this.workCategory.invoiceCategoryList];
        invoiceCategoryList.forEach(item => {
          delete item.value;
        })
        addWorkCategory({
          invoiceCategoryName: this.workCategory.invoiceCategoryCode && this.$refs.invoiceSelect.selectedLabel,
          ...this.workCategory,
          invoiceCategoryList,
        }).then(({data}) => {
          this.$message.success(data)
          this.closeForm()
          this.$emit('success')
        }) .finally(() => this.loading = false)
      },
      doEdit() {
        let invoiceCategoryList = [...this.workCategory.invoiceCategoryList];
        invoiceCategoryList.forEach(item => {
          delete item.value;
        })
        editWorkCategory({
          invoiceCategoryName: this.workCategory.invoiceCategoryCode && this.$refs.invoiceSelect.selectedLabel,
          ...this.workCategory,
          invoiceCategoryList,
        }).then(({data}) => {
          this.$message.success(data)
          this.closeForm()
          this.$emit('success')
        }) .finally(() => this.loading = false)
      },
      listInvoiceCategory(){
        return listInvoiceCategoryPage({
          pageCurrent: 1,
          pageSize: 100
        }).then(response => {
          if (response.data && response.data.data) {
            this.invoiceCategoryList = response.data.data;
          } else {
            this.invoiceCategoryList = [];
          }
        }).catch(error => {
          console.error('获取发票类目失败:', error);
          this.$message.error('获取发票类目失败');
        })
      },
      closeForm() {
        this.show = false
        this.loading = false
        this.workCategory = {
          parentId: 0,
          parentName: '',
          invoiceCategoryCode: '',
          workCategoryName: '',
          workDesc: '',
          businessDesc: '',
          chargeRuleDesc: '',
          invoiceCategoryList: [],
        }
        this.$refs.form.resetFields()
      }
    }
  }
</script>
