<template>
  <el-dialog :visible.sync="show" :close-on-click-modal="false" :before-close="closeForm" :title="(actionType==='ADD'&&'新增供应商') || (actionType==='EDIT'&&'编辑供应商')||''"
             width="600px">
    <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
      <el-form-item label="供应商编号" prop="vendorNo">
        <el-input v-model="form.vendorNo" :disabled="actionType==='Edit'"/>
      </el-form-item>
      <el-form-item label="供应商名称" prop="vendorName">
        <el-input v-model="form.vendorName"/>
      </el-form-item>
      <el-form-item label="所属产品" prop="productNo">
        <el-radio-group v-model="form.productNo">
          <el-radio v-for="item in productList" :key="item.productNo" :label="item.productNo" @change="onProductChange">{{item.productName}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" type="textarea"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="closeForm">取消</el-button>
      <el-button :loading="loading" type="primary" @click="doSubmit">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { addVendor, editVendor,getProductList } from '../../../api/product'
  export default {
  name: "staffForm",
  data() {
    return {
      loading: false,
      show: false,
      actionType: undefined,
      productList: [],
      rules: {
        vendorNo: [{ required: true, message: '请输入供应商编号', trigger: 'blur' }],
        vendorName: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
        productNo : [{required: true, message: '请选择产品',trigger: 'blur'}],
      },
      form: {
        vendorNo: '',
        vendorName: '',
        productNo: '',
        productName: '',
        description: '',
        // TODO 后端需要默认传个100过去
        status: '100'
      },
      departmentList: [],
      roleList: [],
    };
  },
  mounted() {
    this.type = this.$route.query.type;
    if (this.actionType == "EDIT") {
      this.load = true;
    }
    //只查出来产品类型
    getProductList({ productType : 0,pageSize: 100}).then(res => {
      this.productList = res.data.data;
    })
  },
  methods: {
    selectDepartment(name, id) {
      this.form.departmentId = id;
      this.form.departmentName = name;
    },
    doSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.actionType == "ADD") {
            addVendor(this.form).then((response) => {
              this.$message.success("创建成功");
              this.closeForm();
              this.$emit('success');
            });
          } else {
            editVendor(this.form).then((response) => {
              this.$message.success("编辑成功");
              this.closeForm();
              this.$emit('success');
            });
          }
        }
      });
    },
    closeForm() {
      this.show = false
      this.loading = false
      this.form = {}
      this.$refs.form.resetFields()
    },
    onProductChange(val){
      for (let i = 0; i < this.productList.length; i++) {
          if (val === this.productList[i].productNo) {
            this.form.productName = this.productList[i].productName;
            break;
          }
        }
    }
  },
};
</script>

<style lang="scss" scoped>
.box-container {
  padding: 20px;
  background: #fff;
  display: flex; // 调试用
  .el-form {
    width: 400px;
    margin-left: 30px;

    ::v-deep .el-form-item__label {
      font-weight: normal;
    }
  }

  .footer-container {
    padding-left: 120px;
  }
}
</style>
