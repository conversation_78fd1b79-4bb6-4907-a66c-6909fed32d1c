<template>
  <div class="box-container">
    <el-button class="create-btn" type="primary" @click="showDialog('ADD')">新建</el-button>
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label oversize">产品编号/功能编号：</span>
            <el-input v-model="form.productNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label oversize">产品名称/功能名称：</span>
            <el-input v-model="form.productNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">类型：</span>
            <el-select clearable v-model="form.productType">
              <el-option v-for="item in $dict('ProductTypeEnum')" :key="item.code" :value="item.code" :label="item.desc"></el-option>
            </el-select>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="getProductList(true)">查询</el-button>
            <el-button type="text" @click="resetForm">清空筛选条件</el-button>
          </div>

        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table class="content-main" :data="productObj.data">
        <el-table-column type="index" :index="getIndex" label="序号" width="80"></el-table-column>
        <el-table-column label="产品名称/功能名称" prop="productName">

        </el-table-column>
        <el-table-column label="产品编号/功能编号" prop="productNo">

        </el-table-column>
        <el-table-column label="类型" prop="productType">
          <template slot-scope="scope">
            {{ $dictCode('ProductTypeEnum',scope.row.productType).desc }}
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status">
          <template slot-scope="scope">
            {{ $dictCode('OpenCloseEnum',scope.row.status).desc }}
          </template>
        </el-table-column>
        <el-table-column label="最后修改时间" prop="updateTime">
          <p slot-scope="{row, column}" v-html="renderTime(row[column['property']])"></p>
        </el-table-column>
        <el-table-column label="操作" prop="data">
          <template slot-scope="scope">
            <el-button type="text" @click="showDialog('EDIT',scope.row)">修改</el-button>
            <el-button type="text" @click="handleConfirm('删除', scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="productObj"
          ref="pagination"
          :total="productObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>

      <productForm ref="dialog" @success="getProductList"></productForm>
    </div>
  </div>
</template>

<script>
  import { deleteProduct, editProduct, getProductList } from '../../../api/product'
  import productForm from './productForm'

  export default {
    name: 'PmsProductManagement',
    components: {
      productForm
    },
    data() {
      return {
        form: {
          productNo: '',
          productNameLike: '',
          productType: '',
        },
        activeTab: 'first',
        pageCurrent: 1,
        pageSize: 10,
        productObj: {
          data: [{}]
        }
      }
    },
    mounted() {
      this.getProductList();
    },
    methods: {
      getProductList(initFlag) {
        if (initFlag === true) {
          this.pageCurrent = 1;
        }
        getProductList({
          ...this.form,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        }).then(response => {
          this.productObj = response.data;
        })
      },
      getTimeRange(val) {
        this.form.timeRange = val;
      },
      resetForm() {
        this.form = {
          productNo: '',
          productNameLike: '',
          productType: '',
        }
      },
      handleCurrentChange(val) {
        this.pageCurrent = val;
        this.getProductList();
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getProductList(true);
      },
      showDialog(status, data) {
        this.$refs.dialog.show = true;
        this.$refs.dialog.actionType = status;
        data && (this.$refs.dialog.form = {
          id: data.id,
          productName: data.productName,
          productNo: data.productNo,
          productType: data.productType + '',
          status: data.status + '',
          version: data.version,
          description: data.description
        });
      },
      triggerStatus(data) {
        this.$confirm(`确定要${this.$dictCode('OpenCloseEnum', data.status).desc == '启用' ? '禁用' : '启用'}该条数据吗`, {
          type: 'warning'
        }).then(res => {
          editProduct({
            id: data.id,
            version: data.version,
            status: this.$dictCode('OpenCloseEnum', data.status).desc == '启用' ? '101' : '100'
          }).then(response => {
            this.$message.success('修改成功');
            this.getProductList();
          })
        }).catch(() => {})

      },
      handleConfirm(text, data) {
        this.$confirm(`确定要${text}该条数据吗`, {
          type: 'warning'
        }).then(res => {
          deleteProduct({
            id: data.id
          }).then(response => {
            this.$message.success('删除成功');
            this.getProductList();
          })
        }).catch(() => {})
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
    }
  }
</script>

