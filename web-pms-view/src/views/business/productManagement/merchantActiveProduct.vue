<template>
  <div class="page-container">
    <div class="step-container">
      <el-steps :active="activeStep" align-center>
        <el-step title="选择商户"></el-step>
        <el-step title="选择产品"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>
    <div class="content-container">
        <!--step_1-->
        <div class="step_1" v-show="activeStep == 1">
          <div class="search-box">
            <el-select clearable v-model="merchantKey" @change="changeMerchantKey">
              <el-option label="商户编号" value="mchNo"></el-option>
              <el-option label="商户名称" value="mchNameLike"></el-option>
              <el-option label="所属销售" value="salerId"></el-option>
            </el-select>
            <el-select clearable v-if="merchantKey == 'salerId'" v-model="merchantValue">
              <el-option v-for="item in staffList" :key="item.id" :label="item.realName" :value="item.id"></el-option>
            </el-select>
            <el-input v-else placeholder="请输入" v-model="merchantValue"></el-input>
            <el-button type="primary" @click="getMerchantList(true)">搜索</el-button>
          </div>
          <el-table :data="merchantObj.data" @selection-change="selectMerchant" :row-key="getMerchantRowKeys">
            <el-table-column type="selection" :reserve-selection="true"></el-table-column>
            <el-table-column label="商户编号" prop="mchNo"></el-table-column>
            <el-table-column label="商户名称" prop="mchName"></el-table-column>
            <el-table-column label="商户类型" prop="merchantType">
              <template slot-scope="scope">
                {{ $dictCode('MerchantTypeEnum', scope.row.merchantType).desc }}
              </template>
            </el-table-column>
            <el-table-column label="负责销售" prop="salerName"></el-table-column>
            <el-table-column label="操作">
            </el-table-column>
          </el-table>
          <el-pagination
            v-if="merchantObj"
            ref="pagination"
            :total="merchantObj.totalRecord"
            :current-page.sync="pageCurrent"
            :page-sizes="[10,50]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          ></el-pagination>
          <div class="fixed-footer">
            已选择商户{{ selectedMerchant.length }}个
            <el-button type="primary" @click="nextStep">下一步</el-button>
          </div>
        </div>

        <!--step_2-->
        <div class="step_2" v-show="activeStep == 2">
          <div class="search-box">
            <el-select clearable v-model="productKey">
              <el-option label="产品编号" value="productNo"></el-option>
              <el-option label="产品名称" value="productNameLike"></el-option>
            </el-select>
            <el-input placeholder="请输入" v-model="productValue"></el-input>
            <el-button type="primary" @click="getProductList(true)">搜索</el-button>
          </div>
          <!--产品报价单-->
          <div class="product-price-list">
            <el-table :data="productObj.data" @selection-change="selectProduct" :row-key="getProductRowKeys">
              <el-table-column type="selection" :reserve-selection="true"></el-table-column>
              <el-table-column label="产品编号" prop="productNo">
              </el-table-column>
              <el-table-column label="产品名称" prop="productName">
              </el-table-column>
            </el-table>
            <el-pagination
              v-if="productObj"
              ref="pagination"
              :total="productObj.totalRecord"
              :current-page.sync="pageCurrent_2"
              :page-sizes="[10,50]"
              :page-size="pageSize_2"
              layout="total, sizes, prev, pager, next, jumper"
              background
              @size-change="handleSizeChange_2"
              @current-change="handleCurrentChange_2"
            ></el-pagination>
          </div>
          <div class="fixed-footer">
            已选择产品{{ selectedProduct.length }}个
            <el-button type="primary" @click="nextStep">确定开通</el-button>
          </div>
        </div>

        <!--step_3-->
        <div class="step_3" v-show="activeStep == 3">
          <img src="@/assets/success.png" alt="">
          <p class="result-title">开通成功</p>
          <p class="result-tip">已为{{selectedMerchant.length}}个商户，开通{{selectedProduct.length}}个产品</p>
          <div class="result-detail">
            <p>
              选择商户：<span v-for="(item, index) in selectedMerchant" :key="item.id">{{ index > 0 ? '， ' : ''}}{{item.mchName}}</span>
            </p>
            <p>
              选择产品：<span v-for="(item, index) in selectedProduct" :key="item.id">{{ index > 0 ? '， ' : ''}}{{item.productName}}</span>
            </p>
            <p>
              操作人：{{ $store.state.user.userData.name }}
            </p>
          </div>
          <div class="footer-container">
            <el-button type="primary" @click="$router.push('/business/merchantProductManagement')">返回商户产品管理</el-button>
          </div>
        </div>
    </div>
  </div>
</template>

<script>
  import { getMerchantList } from '@/api/merchant'
  import { getStaffListByDepartment } from '@/api/system'
  import { addMerchantProduct, getProductList } from '@/api/product'

  export default {
    name: 'merchantActiveProduct',
    data() {
      return {
        activeStep: 1,
        // step 1
        selectedMerchant: [],
        merchantObj: {},
        merchantKey: '',
        merchantValue: '',
        pageSize: 10,
        pageCurrent: 1,

        // step 2
        selectedProduct: [],
        productObj: {},
        productKey: '',
        productValue: '',
        pageSize_2: 10,
        pageCurrent_2: 1,
        // step 3
        result: '',

        staffList: []
      }
    },
    mounted() {
      this.getMerchantList();
    },
    methods: {
      getMerchantList(initFlag) {
        if (initFlag) {
          this.pageCurrent = 1;
        }
        getMerchantList({
          [this.merchantKey]: this.merchantValue,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        }).then(response => {
          this.merchantObj = response.data;
        })
      },
      getMerchantRowKeys(row) {
        return row.mchNo
      },
      selectMerchant(selection, row) {
        this.selectedMerchant = selection;
      },
      handleCurrentChange(val) {
        this.pageCurrent = val;
        this.getMerchantList();
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getMerchantList(true);
      },

      getProductList(initFlag) {
        if (initFlag) {
          this.pageCurrent_2 = 1;
        }
        getProductList({
          [this.productKey]: this.productValue,
          pageSize: this.pageSize_2,
          pageCurrent: this.pageCurrent_2
        }).then(response => {
          this.productObj = response.data;
        })
      },
      getProductRowKeys(row) {
        return row.productNo
      },
      selectProduct(selection, row) {
        this.selectedProduct = selection;
      },
      handleCurrentChange_2(val) {
        this.pageCurrent_2 = val;
        this.getProductList();
      },
      handleSizeChange_2(val) {
        this.pageSize_2 = val;
        this.getProductList(true);
      },
      nextStep() {
        if(this.activeStep == 1) {
          if(this.selectedMerchant.length == 0) {
            this.$message.error('请选择商户');
            return;
          }
          this.getProductList();
        }
        if(this.activeStep == 2) {
          if(this.selectedProduct.length == 0) {
            this.$message.error('请选择产品');
            return;
          }
          addMerchantProduct({
            merchants: this.selectedMerchant,
            products: this.selectedProduct
          }).then(response => {
            this.activeStep++;
            this.result = response.data;
          }).catch(err => {
          })
        } else {
          this.activeStep++;
        }
      },
      goDetailProcess() {
        this.$router.push('/waitingHandle/detailProcess?processId=' + this.result.id);
      },
      changeMerchantKey(val) {
        this.merchantValue = '';
        if (this.merchantKey === 'salerId') {
          this.staffList.length == 0 && this.getStaffList();
        }
      },
      async getStaffList() {
        const { data } = await getStaffListByDepartment({ departmentId: 0 })
        this.staffList = data;
      }
    }
  }
</script>

<style lang="scss" scoped>
  .page-container {
    .step-container {
      margin-bottom: 40px;
      padding: 20px 0;
      background: #fff;
    }
    .content-container {
      padding: 20px;
      background: #fff;
      .search-box {
        float: right;
        .el-input {
          width: 250px;
        }
      }
      .fixed-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
    .step_3 {
      padding: 50px 0;
      text-align: center;
      p {
        margin-bottom: 24px;
      }
      .result-detail {
        width: 40%;
        margin: 0 auto 24px;
        padding: 16px;
        background: rgba(238, 238, 238, .3);
      }
    }
  }
</style>
