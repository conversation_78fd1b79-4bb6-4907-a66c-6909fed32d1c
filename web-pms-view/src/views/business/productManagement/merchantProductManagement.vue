<template>
  <div class="box-container">
    <el-button class="create-btn" type="primary" @click="$router.push('/business/merchantActiveProduct')">商户产品开通</el-button>
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户名称：</span>
            <el-input v-model="form.mchNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">商户编号：</span>
            <el-input v-model="form.mchNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">产品名称：</span>
            <el-input v-model="form.productNameLike"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">产品编号：</span>
            <el-input v-model="form.productNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">开通状态：</span>
            <el-select clearable v-model="form.status">
              <el-option v-for="item in $dict('OpenCloseEnum')" :key="item.code" :value="item.code" :label="item.desc"></el-option>
            </el-select>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="getMerchantProductList(true)">查询</el-button>
            <el-button type="text" @click="resetForm">清空筛选条件</el-button>
          </div>

        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table class="content-main" :data="merchantProductObj.data">
        <el-table-column type="index" label="序号" :index="getIndex" width="80"></el-table-column>
        <el-table-column label="商户编号/商户名称">
          <template slot-scope="scope">
            {{ scope.row.mchNo }} <br>
            {{ scope.row.mchName }}
          </template>
        </el-table-column>
        <el-table-column label="产品编号/产品名称">
          <template slot-scope="scope">
            {{ scope.row.productNo }} <br>
            {{ scope.row.productName }}
          </template>
        </el-table-column>
        <el-table-column label="开通状态" prop="status">
          <template slot-scope="scope">
            {{ $dictCode('OpenCloseEnum', scope.row.status).desc }}
          </template>
        </el-table-column>
        <el-table-column label="修改人/修改时间" prop="updateBy" width="200">
          <template slot-scope="scope">
            {{ scope.row.updateBy }} <br> {{scope.row.updateTime}}
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="description">

        </el-table-column>
        <el-table-column label="操作" prop="data">
          <template slot-scope="scope">
            <el-button type="text" @click="goDetail(scope.row)">查看产品计费</el-button>
            <el-button type="text" @click="triggerStatus(scope.row)">{{ $dictCode('OpenCloseEnum', scope.row.status).desc == '启用' ? '禁用' : '启用' }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="merchantProductObj"
          ref="pagination"
          :total="merchantProductObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>

    </div>
  </div>
</template>

<script>

  import { updateStatus, getMerchantProductList } from '../../../api/product'

  export default {
    name: 'PmsMerchantProductManagement',
    data() {
      return {
        form: {
          productNo: '',
          productNameLike: '',
          mchNo: '',
          mchNameLike: '',
          status: ''
        },
        pageCurrent: 1,
        pageSize: 10,
        merchantProductObj: {}
      }
    },
    mounted() {
      this.getMerchantProductList();
    },
    methods: {
      getMerchantProductList(initFlag) {
        if (initFlag === true) {
          this.pageCurrent = 1;
        }
        getMerchantProductList({
          ...this.form,
          pageCurrent: this.pageCurrent,
          pageSize: this.pageSize
        }).then(response => {
          this.merchantProductObj = response.data;
        })
      },
      resetForm() {
        this.form = {
          productNo: '',
          productNameLike: '',
          mchNo: '',
          mchNameLike: '',
          status: ''
        }
      },
      handleCurrentChange(val) {
        this.pageCurrent = val;
        this.getMerchantProductList();
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getMerchantProductList(true);
      },
      triggerStatus(data) {
        this.$confirm(`确定要${this.$dictCode('OpenCloseEnum', data.status).desc == '启用' ? '禁用' : '启用'}该商户的产品吗`, {
          type: 'warning'
        }).then(res => {
          updateStatus({
            id: data.id,
            status: this.$dictCode('OpenCloseEnum', data.status).desc == '启用' ? '101' : '100'
          }).then(response => {
            this.$message.success('修改成功');
            this.getMerchantProductList();
          })
        }).catch(() => {})
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1
      },
      goDetail(data) {
        sessionStorage.setItem('merchant', JSON.stringify(data));
        this.$router.push('/business/merchantProductCostManagement');
      }
    }
  }
</script>

<style scoped>
</style>
