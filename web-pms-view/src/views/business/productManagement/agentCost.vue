<template>
  <div class="box-container">
    <el-button class="create-btn" type="primary" @click="$router.push('/business/agentCostForm')">新建</el-button>
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">合伙人名称：</span>
          <el-input v-model="searchForm.agentNameLike"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">合伙人编号：</span>
          <el-input v-model="searchForm.agentNo"></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">产品名称：</span>
          <el-input v-model="searchForm.productNameLike"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">产品编号：</span>
          <el-input v-model="searchForm.productNo"></el-input>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询</el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件</el-button>
      </div>
    </div>
    <div class="content-container">
      <el-table :data="list">
        <el-table-column type="index" :index="getIndex" label="序号"></el-table-column>
        <el-table-column label="合伙人编号/合伙人名称" width="180">
          <template v-slot="{row}">
            {{ row.agentNo }}<br>
            {{ row.agentName }}
          </template>
        </el-table-column>

        <el-table-column label="产品名称" prop="productName">
          <template v-slot="{row}">
            {{ row.productName }}<br>
            {{ row.productNo }}
          </template>
        </el-table-column>
        <el-table-column label="规则类型" prop="ruleType">
          <template v-slot="{row}">
            {{ $dictCode('RuleTypeEnum', row.ruleType).desc }}
          </template>
        </el-table-column>

        <el-table-column label="规则参数" prop="ruleParam">
          <template v-slot="{row}">
            <div v-html="explainRuleParam(row.ruleParam)"></div>
          </template>
        </el-table-column>
        <el-table-column label="分润比例">
          <template v-slot="{row}">
            {{row.realProfitRatio}}%
          </template>
        </el-table-column>
        <el-table-column label="合伙人成本（1级佣金）" prop="firstFormulaType" width="180">
          <template v-slot="{row}">
            <p>{{ $dictCode('FormulaEnum', row.firstFormulaType).desc }}</p>
            <p>{{ explainFormula(row, 'first') }}</p>
          </template>
        </el-table-column>
        <el-table-column label="邀请奖励（2级佣金）" prop="secondFormulaType" width="180">
          <template v-slot="{row}">
            <p>{{ $dictCode('FormulaEnum', row.secondFormulaType).desc }}</p>
            <p>{{ explainFormula(row, 'second') }}</p>
          </template>
        </el-table-column>

        <el-table-column label="修改人/修改时间" prop="productName" width="200">
          <template v-slot="{row}">
            {{ row.updateBy }}<br>{{ row.updateTime }}
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="description"></el-table-column>

        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button type="text" @click="handleView(row)">查看</el-button>
            <!--<el-button type="text" @click="handleEdit(row)">修改</el-button>-->
            <!--<el-button type="text" @click="handleDelete(row)">删除</el-button>-->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10, 50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </el-footer>
  </div>
</template>

<script>
  import { getAgentRule, deleteFeeRule } from '@/api/agent';
  export default {
    name: 'PmsAgentCost',
    data() {
      return {
        searchForm: {
          agentNo: '',
          agentNameLike: '',
          productNo: '',
          productNameLike: '',
        },
        pageSize: 10,
        pageCurrent: 1,

        list: [],
        totalRecord: 0
      }
    },
    mounted() {
      if (sessionStorage.getItem('agent')) {
        let agent = JSON.parse(sessionStorage.getItem('agent'));
        for (let p in this.searchForm) {
          if (agent[p]) {
            this.searchForm[p] = agent[p] + '';
          }
        }
        this.searchForm.agentNameLike = agent.agentName || "";
        this.searchForm.productNameLike = agent.productName || "";
      }
      this.search()
    },
    beforeDestroy() {
      sessionStorage.removeItem('agent')
    },
    methods: {
      async search(init) {
        if (init) {
          this.pageCurrent = 1;
        }
        const { data } = await getAgentRule({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent,
        });
        this.list = data.data;
        this.totalRecord = data.totalRecord;
      },
      resetForm() {
        this.searchForm = {
          agentNo: '',
          agentNameLike: '',
          productNo: '',
          productNameLike: '',
          status: '',
        };
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
      explainRuleParam(param) {
        param = JSON.parse(param);
        let result = ''
        param.forEach(item => {
          result += this.$dictCode('AgentSpecialRuleTypeEnum', item.specialRuleType).desc + ' ' + this.$dictCode('CompareTypeEnum', item.compareType).desc + ' ' +  item.value + '；<br/>'
        })
        return result
      },
      explainFormula(row, level) {
        switch (Number(row[`${level}FormulaType`])) {
          case 0:
            return row[`${level}FeeRate`] + '%';
          case 1:
            return row[`${level}FixedFee`];
          case 2:
            return row[`${level}FixedFee`] + '+' + row[`${level}FeeRate`] + '%';
        }
      },
      handleView(row) {
        sessionStorage.setItem('agentCost', JSON.stringify(row));
        this.$router.push({
          path: '/business/agentCostForm',
          query: {
            actionType: 'VIEW',
            id: row.id,
          }
        });
      },
      handleEdit(row) {
        sessionStorage.setItem('agentCost', JSON.stringify(row));
        this.$router.push({
          path: '/business/agentCostForm',
          query: {
            actionType: 'EDIT',
            id: row.id,
          }
        });
      },
      async handleDelete(row) {
        let res = await this.$confirm(`确定要删除该条数据吗`, {
          type: 'warning'
        }).catch(_ => false)
        if (!res) return;
        await deleteFeeRule({id: row.id});
        this.$message.success('删除成功');
        this.search();
      }
    },
  }
</script>

<style scoped lang="scss">

</style>
