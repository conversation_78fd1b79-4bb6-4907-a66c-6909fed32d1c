<template>
  <div class="box-container">
    <el-button class="create-btn" type="primary" @click="showDialog('ADD')">新建</el-button>
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">供应商名称：</span>
            <el-input v-model="form.vendorNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">供应商编号：</span>
            <el-input v-model="form.vendorNo"></el-input>
          </div>
          <div class="flex-item"></div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="getMainstayList(true)">查询</el-button>
            <el-button type="text" @click="resetForm">清空筛选条件</el-button>
          </div>

        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table class="content-main" :data="mainstayObj.data ? mainstayObj.data : mainstayObj">
        <el-table-column type="index" :index="getIndex" label="序号" width="80"></el-table-column>

        <el-table-column label="供应商编号" prop="vendorNo">

        </el-table-column>
        <el-table-column label="供应商名称" prop="vendorName">

        </el-table-column>
        <el-table-column label="所属产品">
          <template v-slot="{row}">
            {{ row.productNo }}<br>
            {{ row.productName }}
          </template>
        </el-table-column>
        <el-table-column label="描述" prop="description">

        </el-table-column>
        <el-table-column label="操作" prop="data">
          <template slot-scope="scope">
            <el-button type="text" @click="showDialog('EDIT', scope.row)">修改</el-button>
            <el-button type="text" @click="$router.push('/business/mainstayCost')">设置成本计费</el-button>
            <el-button type="text" @click="handleConfirm('删除', scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="mainstayObj"
          ref="pagination"
          :total="mainstayObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>

      <mainstayForm ref="dialog" @success="getMainstayList"></mainstayForm>
    </div>
  </div>
</template>

<script>
  import axios from 'axios'
  import mainstayForm from './mainstayForm'
  import { deleteVendor, getVendorList } from '../../../api/product'

  export default {
    name: 'PmsMainstayManagement',
    components: {
      mainstayForm
    },
    data() {
      return {
        form: {
          vendorNameLike: '',
          vendorNo: '',
          mainstayType: '',
          timeRange: []
        },
        activeTab: 'first',
        pageCurrent: 1,
        pageSize: 10,
        mainstayObj: {
          data: []
        }
      }
    },
    mounted() {
      this.getMainstayList();
    },
    methods: {
      getMainstayList(initFlag) {
        if (initFlag === true) {
          this.pageCurrent = 1;
        }
        getVendorList({
          vendorNameLike: this.form.vendorNameLike,
          vendorNo: this.form.vendorNo,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        }).then(response => {
          this.mainstayObj = response.data;
        })
      },
      getTimeRange(val) {
        this.form.timeRange = val;
      },
      resetForm() {
        this.form = {
          vendorNameLike: '',
          vendorNo: '',
          mainstayType: '',
          timeRange: []
        }
      },
      handleCurrentChange(val) {
        this.pageCurrent = val;
        this.getMainstayList();
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getMainstayList(true);
      },
      showDialog(type, data) {
        this.$refs.dialog.show = true;
        this.$refs.dialog.actionType = type;
        data && (this.$refs.dialog.form = {
          id: data.id,
          version: data.version,
          vendorNo: data.vendorNo,
          vendorName: data.vendorName,
          productNo: data.productNo,
          productName: data.productName,
          description: data.description,
          status: '100'
        });
      },
      handleConfirm(text, data) {
        this.$confirm(`确定要${text}该条数据吗`, {
          type: 'warning'
        }).then(res => {
          deleteVendor({
            id: data.id
          }).then(response => {
            this.$message.success('删除成功');
            this.getMainstayList();
          })
        }).catch(() => {})
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1
      }
    }
  }
</script>

<style scoped>
</style>
