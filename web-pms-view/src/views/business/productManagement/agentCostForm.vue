<template>
  <div class="page-container">
    <el-form
      label-width="180px"
      :rules="rules"
      :model="form"
      ref="form"
      v-loading="load"

    >
      <el-form-item
        label="合伙人编号："
        prop="agentNo"
      >
        <el-select
          clearable
          filterable
          :disabled="actionType == 'VIEW'"
          v-model="form.agentNo"
          @change="selectMerchant"
        >
          <el-option
            v-for="item in merchantList"
            :key="item.agentNo"
            :value="item.agentNo"
            :label="item.agentName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="所属产品："
        prop="productNo"
      >
        <el-select
          clearable
          :disabled="actionType == 'VIEW'"
          v-model="form.productNo"
          @change="selectProduct"
        >
          <el-option
            v-for="item in productList"
            :key="item.productNo"
            :value="item.productNo"
            :label="item.productName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="minFee">
        <span slot="label">
          单笔最低服务费
          <el-tooltip content="即单笔最低手续费，举例如下：当费率6%，实发金额为0.1元/笔时，按比例计费该笔服务费为0.006，但因该项设置为0.01元，实际收取服务费为0.01元">
            <i class="el-icon-question"></i>：
          </el-tooltip>
        </span>
        <el-input
          :value="form.minFee"
          :disabled="actionType == 'VIEW'"
          @input="handleRateInput(form, 'minFee', $event)"
        >
          <template slot="append">元</template>
        </el-input>
      </el-form-item>
      <el-form-item prop="maxFee">
        <span slot="label">
          单笔最高服务费金额
          <el-tooltip content="即单笔最高手续费，即该项设置金额为手续费封顶金额">
            <i class="el-icon-question"></i>：
          </el-tooltip>
        </span>
        <el-input
          :value="form.maxFee"
          :disabled="actionType == 'VIEW'"
          @input="handleRateInput(form, 'maxFee', $event)"
        >
          <template slot="append">元</template>
        </el-input>
      </el-form-item>
      <el-form-item
        prop="realProfitRatio"
        class="form-item-main">
        <span slot="label">
          真实分润比例
          <el-tooltip content="用户控制合伙人佣金（包括二级佣金）实际分润金额">
            <i class="el-icon-question"></i>：
          </el-tooltip>
        </span>
        <el-input
          type="number"
          v-model.number="form.realProfitRatio">
          <template slot="append">%</template>
        </el-input>
        <el-button
          type="primary"
          v-on:click="submitRealProfitRatioValue()"
          style="margin-left: 20px">保存
        </el-button>
      </el-form-item>
      <el-form-item
        label="邀请奖励计费方式："
        prop="calculateMode"
      >
        <el-radio-group v-model="form.calculateMode">
          <el-radio
            :disabled="actionType == 'VIEW'"
            v-for="item in $dict('CalculateModeEnum')"
            :key="item.code"
            :label="item.code"
          >{{ item.desc }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="成本及奖励设置："
        prop="firstSecond"
        class="form-item-main"
      >
        <el-table :data="firstSecond">
          <el-table-column
            prop="type"
            label="佣金类型"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="formulaType"
            label="公式类型"
            align="center"
          >
            <template v-slot="{row}">
              <el-select
                :disabled="actionType == 'VIEW'"
                v-model="row.FormulaType"
                size="small"
              >
                <el-option
                  v-for="item in $dict('FormulaEnum')"
                  :key="item.code"
                  :value="Number(item.code)"
                  :label="item.desc"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column
            prop="fixedFee"
            label="固定金额"
            align="center"
          >
            <template v-slot="{row}">
              <el-input
                type="text"
                :value="row.FixedFee"
                @input="handleRateInput(row, 'FixedFee', $event)"
                size="small"
                :disabled="actionType == 'VIEW' || row.FormulaType == 0"
              >
                <span slot="append">元</span>
              </el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="feeRate"
            label="费率"
            align="center"
          >
            <template v-slot="{row}">
              <el-input
                type="text"
                size="small"
                :value="row.FeeRate"
                :disabled="actionType == 'VIEW' || row.FormulaType == 1"
                @input="handleRateInput(row, 'FeeRate', $event, 0, 100)"
              >
                <span slot="append">%</span>
              </el-input>
            </template>
          </el-table-column>
        </el-table>
        <!-- <el-button type="text" class="func-content" @click="() => { visible = true }">奖励试算&gt;</el-button> -->
      </el-form-item>

      <el-form-item
        label="规则类型："
        prop="ruleType"
      >
        <el-radio-group v-model="form.ruleType">
          <el-radio
            :disabled="actionType == 'VIEW'"
            v-for="item in $dict('RuleTypeEnum')"
            :key="item.code"
            :label="item.code"
          >{{ item.desc }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        class="form-item-main"
        label="特殊计费-规则参数："
        v-if="form.ruleType == 1"
        prop="specialRule"
      >
        <el-button
          :disabled="actionType == 'VIEW'"
          @click="addRuleList">添加
        </el-button>
        <el-table :data="form.ruleParam">
          <el-table-column width="50">
            <template v-slot="{$index}">
              <span>{{ $index > 0 ? '且' : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column>
            <template v-slot="{row, $index}">
              <el-select
                clearable
                v-model="row.specialRuleType"
                @change="selectRuleType($index)"
              >
                <el-option
                  v-for="item in $dict('AgentSpecialRuleTypeEnum')"
                  :key="item.code"
                  :value="Number(item.code)"
                  :label="item.desc"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column>
            <template v-slot="{row}">
              <div v-if="row.specialRuleType">
                <el-select
                  clearable
                  v-model="row.compareType"
                  :disabled="row.specialRuleType == 1 || row.specialRuleType == 3"
                >
                  <el-option
                    :value="1"
                    label="等于"
                  ></el-option>
                  <el-option
                    :value="2"
                    label="小于"
                  ></el-option>
                  <el-option
                    :value="3"
                    label="大于"
                  ></el-option>
                </el-select>
              </div>
            </template>
          </el-table-column>
          <el-table-column>
            <template slot-scope="scope">
              <el-select
                clearable
                v-if="scope.row.specialRuleType == 1"
                v-model="scope.row.value"
              >
                <el-option
                  v-for="item in vendorList"
                  :key="item.vendorNo"
                  :value="item.vendorNo"
                  :label="item.vendorName"
                ></el-option>
              </el-select>
              <el-select
                v-else-if="scope.row.specialRuleType === 3"
                v-model="scope.row.value"
              >
                <el-option
                  v-for="m in agentMerchant"
                  :key="m.mchNo"
                  :value="m.mchNo"
                  :label="m.mchName"></el-option>
              </el-select>
              <el-input
                v-else
                v-model="scope.row.value"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template v-slot="{$index}">
              <el-button
                type="text"
                @click="deleteRule($index)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item
        label="优先级"
        prop="priority"
      >
        <el-input
          :disabled="actionType == 'VIEW'"
          :value="form.priority"
          @input="changePriority"
        ></el-input>
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          type="textarea"
          :disabled="actionType == 'VIEW'"
          v-model="form.description"
          maxlength="50"
          rows="5"
        ></el-input>
      </el-form-item>
    </el-form>
    <div class="footer-container">
      <el-button
        v-if="actionType !== 'VIEW'"
        type="primary"
        @click="submit"
      >确定
      </el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
    <RewardCalcDialog :visible.sync="visible"></RewardCalcDialog>
  </div>
</template>

<script>
import { getProductList, getVendorList } from '@/api/product'
import { getAllAgentSimple, addFeeRule, editFeeRule, updateRealProfitRatio, getBusinessRelation } from '@/api/agent'
import RewardCalcDialog from '@/views/agent/Component/RewardCalcDialog.vue';

export default {
  name: "agentProductCostForm",
  components: {
    RewardCalcDialog
  },
  data() {
    const self = this;

    return {
      load: false,
      productInfo: '',
      merchantInfo: '',
      rules: {
        agentNo: [{ required: true, message: '请选择合伙人', trigger: 'change' }],
        productNo: [{ required: true, message: '请选择所属产品', trigger: 'change' }],
        minFee: [{ required: true, message: '请输入单笔最低手续费' }],
        maxFee: [{ required: true, message: '请输入单笔最高手续费' }],
        ruleType: [{ required: true, message: '请选择规则类型', trigger: 'change' }],
        priority: [{ required: true, message: '请输入优先级', trigger: 'blur' }],
        realProfitRatio: [
          { required: true, message: '请输入真实分润比例' }
        ],
        firstSecond: [
          {
            required: true, validator: function (rule, value, callback) {
              let result = true;
              for (let i = 0; i < self.firstSecond.length; i++) {
                if (self.firstSecond[i].FormulaType == 0) {
                  result = (self.firstSecond[i].FeeRate !== '')
                } else if (self.firstSecond[i].FormulaType == 1) {
                  result = (self.firstSecond[i].FixedFee !== '')
                } else if (self.firstSecond[i].FormulaType == 2) {
                  result = (self.firstSecond[i].FeeRate !== '' && self.firstSecond[i].fixedFee !== '')
                }
                if (!result) {
                  break;
                }
              }
              if (result) {
                callback();
              } else {
                callback(new Error('请设置规则'))
              }
            }, trigger: 'blur'
          }
        ]
      },
      form: {
        agentNo: '',
        agentName: '',
        productNo: "",
        productName: '',
        minFee: "0.01",
        maxFee: "99999999.00",
        ruleType: "",
        ruleParam: [],
        priority: '',
        description: '',
        calculateMode: '',
        realProfitRatio: null,
        firstFormulaType: '',
        firstFixedFee: '',
        firstFeeRate: '',
        secondFormulaType: '',
        secondFixedFee: '',
        secondFeeRate: '',
      },
      merchantList: [],
      productList: [],
      roleList: [],
      vendorList: [],

      visible: false,

      agentMerchant: [], // agent 下商户
    };
  },
  computed: {
    actionType() {
      return this.$route.query.actionType || 'ADD'
    },
    id() {
      return this.$route.query.id || '';
    },
    firstSecond() {
      let arr = [{ type: '合伙人成本（1级佣金）', FormulaType: '', FixedFee: '', FeeRate: '' }]
      if (this.form.calculateMode == 100) {
        arr.push({ type: '邀请奖励（2级佣金）', FormulaType: '', FixedFee: '', FeeRate: '' })
      }
      return arr
    }
  },
  mounted() {
    this.getAllAgentSimple();
    this.getProductList();
    this.getVendorList();

    if (sessionStorage.getItem('agent')) {
      const form = JSON.parse(sessionStorage.getItem('agent'));
      for (let p in this.form) {
        if (p in form && form[p] !== null) {
          this.form[p] = form[p] + '';
        }
      }
    }
    if (sessionStorage.getItem('agentCost')) {
      const form = JSON.parse(sessionStorage.getItem('agentCost'));
      for (let p in this.form) {
        if (p in form && form[p] !== null) {
          this.form[p] = form[p] + '';
        }
      }
      this.form.ruleParam = JSON.parse(this.form.ruleParam) || [];
      this.firstSecond[0].FormulaType = Number(this.form.firstFormulaType);
      this.firstSecond[0].FixedFee = this.form.firstFixedFee;
      this.firstSecond[0].FeeRate = this.form.firstFeeRate;
      this.firstSecond[1].FormulaType = Number(this.form.secondFormulaType);
      this.firstSecond[1].FixedFee = this.form.secondFixedFee;
      this.firstSecond[1].FeeRate = this.form.secondFeeRate;
    }

  },
  watch: {
    'form.agentNo': {
      handler(val) {
        if (val) {
          this.getAgentMerchant()
        } else {
          this.agentMerchant = []
        }
      }
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('agentCost');
    sessionStorage.removeItem('agent');
  },
  methods: {
    async getProductList() {
      const response = await getProductList({
        pageSize: 1000,
        pageCurrent: 1
      });
      this.productList = response.data.data;
    },
    async getAllAgentSimple() {
      const response = await getAllAgentSimple();
      this.merchantList = response.data;
    },
    async getVendorList() {
      const response = await getVendorList({
        pageSize: 200,
        pageCurrent: 1
      })
      this.vendorList = response.data.data;
    },
    selectMerchant(val) {
      this.merchantList.forEach(item => {
        if (item.agentNo == val) {
          this.form.agentName = item.agentName;
        }
      })
    },
    selectProduct(val) {
      this.productList.forEach(item => {
        if (item.productNo == val) {
          this.form.productName = item.productName;
        }
      })
    },
    addRuleList() {
      this.form.ruleParam.push({
        specialRuleType: '',
        compareType: '',
        value: ''
      })
    },
    selectRuleType(index) {
      this.form.ruleParam[index].compareType = 1;
    },
    async submit() {
      const valid = await this.$refs.form.validate().catch(_ => false);
      if (valid) {
        if (this.form.ruleType == 0) {
          this.form.ruleParam = [];
        }
        ['first', 'second'].forEach((item, index) => {
          for (let p in this.firstSecond[index]) {
            let prop = item + p;
            if (prop in this.form) {
              this.form[prop] = this.firstSecond[index][p];
            }
          }
        });
        if (this.actionType == "ADD") {
          await addFeeRule(this.form);
          this.$message.success("创建成功");
          this.$refs.form.resetFields();
          this.$router.push("/business/agentCost");
        } else if (this.actionType == 'EDIT') {
          await editFeeRule({
            ...this.form,
            id: this.id,
          })
          this.$message.success("编辑成功");
          this.$refs.form.resetFields();
          this.$router.push("/business/agentCost");
        }
      }
    },
    cancel() {
      this.$router.push("/business/agentCost");
    },
    changePriority(value) {
      value = value.replace(/[^\d]/g, '');
      this.form.priority = value;
    },
    deleteRule(index) {
      this.form.ruleParam.splice(index, 1)
    },
    async submitRealProfitRatioValue() {
      if (this.form.realProfitRatio == null || this.form.realProfitRatio <= 0 || this.form.realProfitRatio > 100) {
        this.$message.error("真实分润比例只能为1-100之间的整数");
        this.form.realProfitRatio = null;
        return;
      }

      console.log(this.id)

      const { data } = await updateRealProfitRatio(this.id, this.form.realProfitRatio)
      this.$message.success("操作成功")
    },
    async getAgentMerchant() {
      const { data: { data } } = await getBusinessRelation({
        relationType: '101',
        agentNo: this.form.agentNo,
        pageSize: 200,
        pageCurrent: 1,
      })
      this.agentMerchant = data
    }
  },


};
</script>


<style
  lang="scss"
  scoped>
.page-container {
  padding: 20px;
  background: #fff;

  .el-form {
    width: 800px;
    margin-left: 30px;

    .el-form-item {
      width: 500px;
    }

    .form-item-main {
      width: 110%;
    }

    ::v-deep .el-form-item__label {
      font-weight: normal;
    }
  }

  .footer-container {
    padding-left: 120px;
  }
}
</style>
