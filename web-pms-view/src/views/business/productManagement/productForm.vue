<template>
  <el-dialog :visible.sync="show" :close-on-click-modal="false" :before-close="closeForm" :title="(actionType==='ADD'&&'新增产品及功能') || (actionType==='EDIT'&&'编辑产品及功能')||''"
             width="600px">
    <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
      <el-form-item label="产品名称" prop="productName">
        <el-input v-model="form.productName"/>
      </el-form-item>
      <el-form-item label="产品编号" prop="productNo">
        <el-input v-model="form.productNo" placeholder="编号"  :disabled="actionType==='EDIT'"/>
      </el-form-item>
      <el-form-item label="产品描述" prop="description">
        <el-input v-model="form.description" placeholder="产品描述"/>
      </el-form-item>
      <el-form-item label="类型" prop="productType">
        <el-radio-group v-model="form.productType">
          <el-radio v-for="item in $dict('ProductTypeEnum')" :key="item.code" :label="item.code">{{item.desc}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio v-for="item in $dict('OpenCloseEnum')" :key="item.code" :label="item.code">{{item.desc}}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="closeForm">取消</el-button>
      <el-button :loading="loading" type="primary" @click="doSubmit">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>

  import { addProduct, editProduct } from '../../../api/product'

  export default {
    data() {
      return {
        loading: false,
        show: false,
        actionType: undefined,
        form: {
          productName: '',
          productNo: '',
          productType: '',
          status: '',
          description: '',
        },
        invoiceCategoryList: [],
        rules: {
          productName: [{required: true, message: '请输入产品名称', trigger: 'blur'}],
          productNo: [{required: true, message: '请输入产品编号', trigger: 'blur'}],
          productType: [{required: true, message: '请选择类型', trigger: 'blur'}],
          status: [{required: true, message: '请选择状态', trigger: 'blur'}],
        }
      }
    },
    mounted(){
    },
    methods: {
      doSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.loading = true
            this.actionType === 'ADD' && this.doAdd();
            this.actionType === 'EDIT' && this.doEdit();
          }
        })
      },
      doAdd() {
        addProduct(this.form).then(response => {
          this.$message.success('创建成功');
          this.closeForm()
          this.$emit('success')
        }) .finally(() => this.loading = false)
      },
      doEdit() {
        editProduct(this.form).then(response => {
          this.$message.success('修改成功')
          this.closeForm()
          this.$emit('success')
        }) .finally(() => this.loading = false)
      },
      closeForm() {
        this.show = false
        this.loading = false
        this.form = {}
        this.$refs.form.resetFields()
      }
    }
  }
</script>
