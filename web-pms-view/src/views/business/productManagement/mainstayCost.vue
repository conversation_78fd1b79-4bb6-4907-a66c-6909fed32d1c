<template>
  <div class="box-container">
    <el-button class="create-btn" type="primary" @click="$router.push('/business/mainstayCostForm')">新建</el-button>
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">供应商编号：</span>
            <el-input v-model="form.vendorNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">供应商名称：</span>
            <el-input v-model="form.vendorNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">规则类型：</span>
            <el-select clearable v-model="form.ruleType">
              <el-option v-for="item in $dict('RuleTypeEnum')" :key="item.code" :value="item.code" :label="item.desc"></el-option>
            </el-select>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="getMainstayCostList(true)">查询</el-button>
            <el-button type="text" @click="resetForm">清空筛选条件</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table class="content-main" :data="mainstayCostObj.data">
        <el-table-column type="index" :index="getIndex" label="序号"></el-table-column>
        <el-table-column label="供应商编号" prop="vendorNo">

        </el-table-column>
        <el-table-column label="供应商名称" prop="vendorName">

        </el-table-column>
        <el-table-column label="规则类型" prop="ruleType">
          <template slot-scope="scope">
            {{ $dictCode('RuleTypeEnum', scope.row.ruleType).desc }}
          </template>
        </el-table-column>
        <el-table-column label="规则参数" prop="ruleParam">
          <template v-slot="{row}">
            <div v-html="explainRuleParam(row.ruleParam)"></div>
          </template>
        </el-table-column>
        <el-table-column label="公式类型" prop="formulaType">
          <template slot-scope="scope">
            {{ $dictCode('FormulaEnum', scope.row.formulaType).desc }}
          </template>
        </el-table-column>
        <el-table-column label="固定金额手续费" prop="fixedFee">

        </el-table-column>
        <el-table-column label="比例手续费（%）" prop="feeRate">
        </el-table-column>
        <el-table-column label="优先级" prop="priority">
        </el-table-column>
        <el-table-column label="操作" prop="data">
          <template slot-scope="scope">
            <el-button type="text" @click="handleView(scope.row)">查看</el-button>
            <el-button type="text" @click="handleEdit(scope.row)">修改</el-button>
            <el-button type="text" @click="handleConfirm('删除', scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="mainstayCostObj"
          ref="pagination"
          :total="mainstayCostObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
  import { deleteVendorFee, getVendorFeeList } from '../../../api/product'

  export default {
    name: 'PmsMainstayCost',
    data() {
      return {
        form: {
          vendorNo: '',
          vendorNameLike: '',
          ruleType: ''
        },
        activeTab: 'first',
        pageCurrent: 1,
        pageSize: 10,
        mainstayCostObj: {}
      }
    },
    mounted() {
      this.getMainstayCostList();
    },
    methods: {
      getMainstayCostList(initFlag) {
        if (initFlag === true) {
          this.pageCurrent = 1;
        }
        getVendorFeeList({
          vendorNo: this.form.vendorNo,
          vendorNameLike: this.form.vendorNameLike,
          ruleType: this.form.ruleType,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        }).then(response => {
          this.mainstayCostObj = response.data;
        })
      },
      getTimeRange(val) {
        this.form.timeRange = val;
      },
      resetForm() {
        this.form = {
          vendorNo: '',
          vendorNameLike: '',
          ruleType: ''
        }
      },
      handleCurrentChange(val) {
        this.pageCurrent = val;
        this.getMainstayCostList();
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getMainstayCostList(true);
      },
      handleEdit(data) {
        sessionStorage.setItem('mainstayCost', JSON.stringify(data));
        this.$router.push({
          path: '/business/mainstayCostForm',
          query: {
            actionType: 'EDIT'
          }}
        );
      },
      handleConfirm(text, data) {
        this.$confirm(`确定要${text}该条数据吗`, {
          type: 'warning'
        }).then(res => {
          deleteVendorFee({
            id: data.id
          }).then(resposne => {
            this.$message.success('删除成功');
            this.getMainstayCostList();
          })
        }).catch(() => {})
      },
      handleView(data) {
        sessionStorage.setItem('mainstayCost', JSON.stringify(data));
        this.$router.push({
          path: '/business/mainstayCostForm',
          query: {
            actionType: 'VIEW'
          }
        });
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
      explainRuleParam(param) {
        param = JSON.parse(param);
        let result = ''
        param.forEach(item => {
          result += this.$dictCode('CostFeeSpecialRuleTypeEnum', item.specialRuleType).desc + ' ' + this.$dictCode('CompareTypeEnum', item.compareType).desc + ' ' +  item.value + '；<br/>'
        })
        return result
      }
    }
  }
</script>

<style scoped>
</style>
