<template>
  <div class="box-container">
    <el-button class="create-btn" type="primary" @click="newProduct">合伙人产品开通</el-button>
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">合伙人编号：</span>
          <el-input v-model="searchForm.agentNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">合伙人名称：</span>
          <el-input v-model="searchForm.agentNameLike"></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">产品名称：</span>
          <el-input v-model="searchForm.productNameLike"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">产品编号：</span>
          <el-input v-model="searchForm.productNo"></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">开通状态：</span>
          <el-select v-model="searchForm.status">
            <el-option
              v-for="item in $dict('PublicStatusEnum')"
              :key="item.code"
              :label="item.desc"
              :value="item.code"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button
          type="primary"
          @click="search(true)"
        >查询</el-button>
        <el-button
          type="text"
          @click="resetForm"
        >清空筛选条件</el-button>
      </div>
    </div>

    <div class="content-container">
      <el-table
        ref="table"
        :data="list"
      >
        <el-table-column label="序号" type="index" :index="getIndex"></el-table-column>
        <el-table-column
          width="120"
        >
          <template slot="header">
            <span>合伙人编号/</span><br>
            <span>合伙人名称</span>
          </template>
          <template v-slot="{row}">
            {{ row.agentNo }} <br>
            {{ row.agentName }}
          </template>
        </el-table-column>

        <el-table-column label="产品/功能">
          <template v-slot="{row}">
            {{ row.productNo }} <br>
            {{ row.productName }}
          </template>
        </el-table-column>

        <el-table-column label="开通状态">
          <template v-slot="{row}">
            <el-tag :type="getPubStatusTagType(row.status)">
              {{ $dictCode('PublicStatusEnum', row.status).desc }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="修改人/修改时间" width="200">
          <template v-slot="{row}">
            {{ row.updateBy }} <br> {{ row.updateTime }}
          </template>
        </el-table-column>

        <el-table-column label="备注" prop="description"></el-table-column>

        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button type="text" @click="goDetail(row)">查看设置</el-button>
            <el-button type="text" @click="toggleProduct(row)">
              {{ row.status == 100 ? '禁用' : '启用'}}
            </el-button>
          </template>
        </el-table-column>

      </el-table>

      <el-footer class="pagination-container">
        <el-pagination
          ref="pagination"
          key="agent"
          :total="totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10, 50]"
          :page-size.sync="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="search(true)"
          @current-change="search()"
        >
        </el-pagination>
      </el-footer>
    </div>

    <el-dialog :visible.sync="showCloseDialog" title="禁用" :show-close="false">
      <p>禁用后，该合伙人将不能为其直属商户开通该产品，且不能获得该产品的佣金奖励，但不影响商户正常使用产品。确定要禁用该合伙人的产品吗？</p>
      <div slot="footer">
        <el-button type="primary" @click="toggleConfirm(101)">确定</el-button>
        <el-button @click="showCloseDialog = false">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="showOpenDialog" title="启用" width="500px" :show-close="false">
      <p>确认启用该产品吗？</p>
      <div slot="footer">
        <el-button type="primary" @click="toggleConfirm(100)">确定</el-button>
        <el-button @click="showOpenDialog = false">取消</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>
  import { getAgentProduction, updateStatus} from '@/api/agent'

  export default {
    name: 'PmsAgentProduct',
    data() {
      return {
        searchForm: {
          agentNo: '',
          agentNameLike: '',
          productNo: '',
          productNameLike: '',
          status: '',
        },
        pageSize: 10,
        pageCurrent: 1,

        list: [],
        totalRecord: 0,

        showCloseDialog: false,
        showOpenDialog: false,
      }
    },
    mounted() {
      this.searchForm.agentNo = this.$route.query.agentNo || '';
      this.searchForm.agentNameLike = this.$route.query.agentName || '';

      this.search();
    },
    methods: {
      async search(init) {
        if (init) {
          this.pageCurrent = 1;
        }
        const { data } = await getAgentProduction({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        })
        this.list = data.data;
        this.totalRecord = data.totalRecord
      },
      resetForm() {
        this.searchForm = {
          agentNo: '',
          agentNameLike: '',
          productNo: '',
          productNameLike: '',
          status: '',
        }
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1
      },
      toggleProduct(row) {
        this.operateRow = row;
        if (row.status == 100) {
          this.showCloseDialog = true;
        } else if (row.status == 101) {
          this.showOpenDialog = true;
        }
      },
      async toggleConfirm(status) {
        const { data } = await updateStatus({
          id: this.operateRow.id,
          status,
        })
        data && this.$message.success('修改成功');
        this.showOpenDialog = this.showCloseDialog = false;
        this.search();
      },
      newProduct() {
        this.$router.push({
          path: '/business/agentActiveProduct'
        })
      },
      goDetail(row) {
        sessionStorage.setItem('agent', JSON.stringify(row));
        this.$router.push({
          path: '/business/agentCost'
        })
      }
    }
  }
</script>

<style>
</style>
