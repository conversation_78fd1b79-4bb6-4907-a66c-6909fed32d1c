<template>
  <div class="box-container">
    <el-button class="create-btn" type="primary" @click="$router.push('/business/merchantProductCostForm?actionType=ADD')">新建</el-button>
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户名称：</span>
            <el-input v-model="form.mchNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">商户编号：</span>
            <el-input v-model="form.mchNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">供应商编号：</span>
            <el-select v-model="form.mainstayNo" placeholder="请选择" filterable clearable>
              <el-option v-for="item in mainstayList" :key="item.id" :label="item.vendorName" :value="item.vendorNo"></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">产品名称：</span>
            <el-input v-model="form.productNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">产品编号：</span>
            <el-input v-model="form.productNo"></el-input>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="getMerchantProductCostList(true)">查询</el-button>
            <el-button type="text" @click="resetForm">清空筛选条件</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table class="content-main" :data="merchantProductCostObj.data">
        <el-table-column label="序号" width="80" :index="getIndex" type="index"></el-table-column>
        <el-table-column label="商户编号/商户名称" prop="createTime" width="150">
          <template slot-scope="scope">
            {{ scope.row.mchNo }} <br>
            {{ scope.row.mchName }}
          </template>
        </el-table-column>
        <el-table-column label="产品名称" prop="productName">
          <template v-slot="{row}">
            <p>{{ row.productNo }}</p>
            <p>{{ row.productName }}</p>
          </template>
        </el-table-column>
        <el-table-column label="规则类型" prop="ruleType">
          <template slot-scope="scope">
            {{ $dictCode('RuleTypeEnum', scope.row.ruleType).desc }}
          </template>
        </el-table-column>
        <el-table-column label="规则参数" prop="ruleParam">
          <template v-slot={row}>
            <div v-html="explainRuleParam(row.ruleParam)"></div>
          </template>
        </el-table-column>
        <el-table-column label="公式类型" prop="formulaType">
          <template slot-scope="scope">
            {{ $dictCode('FormulaEnum', scope.row.formulaType).desc }}
          </template>
        </el-table-column>
        <el-table-column label="固定金额手续费" prop="fixedFee">

        </el-table-column>
        <el-table-column label="比例手续费（%）" prop="feeRate">
        </el-table-column>
        <el-table-column label="优先级" prop="priority">
        </el-table-column>
        <el-table-column label="修改人/修改时间" prop="mchNo" width="190">
          <template slot-scope="scope">
            {{ scope.row.updateBy }} <br> {{ scope.row.updateTime }}
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="description">

        </el-table-column>
        <el-table-column label="操作" prop="data">
          <template slot-scope="scope">
            <el-button type="text" @click="handleEdit(scope.row, 'VIEW')">查看</el-button>
            <!--<el-button type="text" @click="handleEdit(scope.row, 'EDIT')">修改</el-button>-->
            <!--<el-button type="text" @click="handleConfirm('删除', scope.row)">删除</el-button>-->
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="merchantProductCostObj"
          ref="pagination"
          :total="merchantProductCostObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
  import { deleteMerchantProductFee, getMerchantProductFeeList } from '../../../api/product'
  import { getVendorSimpleList } from '../../../api/product'

  export default {
    name: 'PmsMerchantProductCostManagement',
    data() {
      return {
        form: {
          productNo: '',
          productNameLike: '',
          mchNo: '',
          mchNameLike: '',
          status: ''
        },
        activeTab: 'first',
        pageCurrent: 1,
        pageSize: 10,
        merchantProductCostObj: {},
        mainstayList: []
      }
    },
    mounted() {
      if (sessionStorage.getItem('merchant')) {
        this.form.mchNo = JSON.parse(sessionStorage.getItem('merchant')).mchNo;
        this.form.productNo = JSON.parse(sessionStorage.getItem('merchant')).productNo;
      }
      this.getMerchantProductCostList();
      this.getVendorList()
    },
    // beforeDestroy() {
    //   sessionStorage.removeItem('merchant');
    // },
    methods: {
      getMerchantProductCostList(initFlag) {
        if (initFlag === true) {
          this.pageCurrent = 1;
        }
        getMerchantProductFeeList({
          ...this.form,
          pageCurrent: this.pageCurrent,
          pageSize: this.pageSize
        }).then(response => {
          this.merchantProductCostObj = response.data;
        })
      },
      getVendorList() {
        getVendorSimpleList().then(response => {
          this.mainstayList = response.data;
        })
      },
      getTimeRange(val) {
        this.form.timeRange = val;
      },
      resetForm() {
        this.form = {
          mchNo: '',
          mchNameLike: '',
          productNo: '',
          productNameLike: '',
          status: ''
        }
      },
      handleCurrentChange(val) {
        this.pageCurrent = val;
        this.getMerchantProductCostList();
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getMerchantProductCostList(true);
      },
      handleEdit(data, type) {
        sessionStorage.setItem('merchantProductCost', JSON.stringify(data));
        this.$router.push({
          path: '/business/merchantProductCostForm',
          query: {
            actionType: type
          }
        });
      },
      handleConfirm(text, data) {
        this.$confirm(`确定要${text}该条数据吗`, {
          type: 'warning'
        }).then(res => {
          deleteMerchantProductFee({
            id: data.id
          }).then(resposne => {
            this.$message.success('删除成功');
            this.getMerchantProductCostList();
          })
        }).catch(() => {})
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
      explainRuleParam(param) {
        param = JSON.parse(param);
        let result = ''
        param.forEach(item => {
          result += this.$dictCode('ProductFeeSpecialRuleTypeEnum', item.specialRuleType).desc + this.$dictCode('CompareTypeEnum', item.compareType).desc +  item.value + '；<br/>'
        })
        return result
      }
    }
  }
</script>

<style scoped>

</style>
