<template>
  <div class="box-container">
    <el-form label-width="180px" :rules="rules" :model="form" ref="form" v-loading="load" :disabled="actionType == 'VIEW'">
      <el-form-item label="商户编号：" prop="mchNo">
        <el-select clearable filterable v-model="form.mchName" @change="selectMerchant">
          <el-option v-for="item in merchantList" :key="item.mchNo" :value="[item.mchNo, item.mchName]" :label="item.mchName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属产品：" prop="productNo">
        <el-select clearable v-model="form.productName" @change="selectProduct">
          <el-option v-for="item in productList" :key="item.productNo" :value="[item.productNo, item.productName]" :label="item.productName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="minFee">
        <span slot="label">
          单笔最低服务费
          <el-tooltip content="即单笔最低手续费，举例如下：当费率6%，实发金额为0.1元/笔时，按比例计费该笔服务费为0.006，但因该项设置为0.01元，实际收取服务费为0.01元">
            <i class="el-icon-question"></i>：
          </el-tooltip>
        </span>
        <el-input v-model="form.minFee">
          <template slot="append">元</template>
        </el-input>
      </el-form-item>
      <el-form-item prop="maxFee">
        <span slot="label">
          单笔最高服务费金额
          <el-tooltip content="即单笔最高手续费，即该项设置金额为手续费封顶金额">
            <i class="el-icon-question"></i>：
          </el-tooltip>
        </span>
        <el-input v-model="form.maxFee">
          <template slot="append">元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="计费类型：" prop="chargeType">
        <el-radio-group v-model="form.chargeType">
          <el-radio v-for="item in $dict('ChargeTypeEnum')" :key="item.code" :label="item.code">{{item.desc}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="默认公式类型：" prop="formulaType">
        <el-select clearable v-model="form.formulaType">
          <el-option v-for="item in $dict('FormulaEnum')" :key="item.code" :value="item.code" :label="item.desc"></el-option>
        </el-select>
        <p class="color-gray" v-show="$dictCode('FormulaEnum', form.formulaType).desc === '比例'">
          即每笔订单按“实发流水 * 服务费比例”的公式计费
        </p>
        <p class="color-gray" v-show="$dictCode('FormulaEnum', form.formulaType).desc === '固定金额'">
          即每笔订单按“固定服务费金额”的公式计费，灵活用工场景下请谨慎适用该公式类型
        </p>
        <p class="color-gray" v-show="$dictCode('FormulaEnum', form.formulaType).desc === '固定金额+比例'">
          即每笔订单按“固定服务费金额 + 实发流水 * 服务费比例”的公式计费，通常适用于商户同时承担不同类目手续费的场景
        </p>
      </el-form-item>
      <el-form-item label="固定金额：" prop="fixedFee" v-if="form.formulaType != '0'">
        <el-input v-model="form.fixedFee">
          <template slot="append">元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="比例：" prop="feeRate" v-if="form.formulaType != '1'">
        <el-input v-model="form.feeRate">
          <template slot="append">%</template>
        </el-input>
      </el-form-item>
      <el-form-item label="规则类型：" prop="ruleType">
        <el-radio-group v-model="form.ruleType">
          <el-radio v-for="item in $dict('RuleTypeEnum')" :key="item.code" :label="item.code">{{item.desc}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item class="form-item-main" label="特殊计费-规则参数：" v-if="form.ruleType == 1" prop="specialRule">
        <el-button @click="addRuleList">添加</el-button>
        <el-table :data="form.ruleParam">
          <el-table-column width="50">
            <template v-slot="{$index}">
              <span>{{ $index > 0 ? '且' : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column>
            <template slot-scope="scope">
              <el-select clearable v-model="scope.row.specialRuleType" @change="selectRuleType(scope.$index)">
                <el-option v-for="item in $dict('ProductFeeSpecialRuleTypeEnum')" :key="item.code" :value="item.code" :label="item.desc"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column>
            <template slot-scope="scope" v-if="scope.row.specialRuleType">
              <el-select clearable v-model="scope.row.compareType" :disabled="scope.row.specialRuleType == '1'">
                <el-option
                  v-for="item in $dict('CompareTypeEnum')"
                  :key="item.code"
                  :label="item.desc"
                  :value="item.code"
                ></el-option> 
              </el-select>
            </template>
          </el-table-column>
          <el-table-column>
            <template slot-scope="scope">
              <el-select clearable v-if="scope.row.specialRuleType == '1'" v-model="scope.row.value">
                <el-option v-for="item in vendorList" :key="item.vendorNo" :value="item.vendorNo" :label="item.vendorName"></el-option>
              </el-select>
              <el-input v-if="scope.row.specialRuleType != '1'" v-model="scope.row.value"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template v-slot="{$index}">
              <el-button type="text" @click="deleteRule($index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-input :value="form.priority" @input="changePriority"></el-input>
      </el-form-item>
      <el-form-item label="描述">
        <el-input type="textarea" v-model="form.description" maxlength="50" rows="5"></el-input>
      </el-form-item>
    </el-form>
    <div class="footer-container">
      <el-button v-if="actionType !== 'VIEW'" type="primary" @click="submit">确定</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>
  </div>
</template>

<script>
  import { addMerchantProductFee, editMerchantProductFee, getProductList } from '../../../api/product'
  import { getVendorList } from '../../../api/product';
  import { getMerchantList } from '../../../api/merchant'

  export default {
    name: "merchantProductCostForm",
    data() {
      return {
        load: false,
        productInfo: '',
        merchantInfo: '',
        rules: {
          mchNo: [{required: true, message: '请选择商户', trigger: 'change'}],
          productNo: [{required: true, message: '请选择所属产品', trigger: 'change'}],
          minFee: [{required: true, message: '请输入单笔最低手续费'}],
          maxFee: [{required: true, message: '请输入单笔最高手续费'}],
          chargeType: [{required: true, message: '请选择计费类型', trigger: 'change'}],
          formulaType: [{required: true, message: '请选择默认公式类型', trigger: 'change'}],
          fixedFee: [{required: true, message: '请输入固定金额'}],
          feeRate: [{required: true, message: '请输入费率'}],
          ruleType: [{required: true, message: '请选择规则类型', trigger: 'change'}],
          priority: [{required: true, message: '请输入优先级', trigger: 'blur'}],
        },
        form: {
          mchNo: '',
          mchName: '',
          productNo: "",
          productName: '',
          minFee: "0.01",
          maxFee: "99999999.00",
          ruleType: "",
          formulaType: "0",
          chargeType: '',
          ruleParam: [],
          fixedFee: '',
          status: '100',
          feeRate: '',
          priority: '',
          description: ''
        },
        merchantList: [],
        productList: [],
        roleList: [],
        vendorList: [],
      };
    },
    computed: {
      actionType() {
        return this.$route.query.actionType || ''
      }
    },
    mounted() {
      this.getMerchantList();
      this.getProductList();
      this.getVendorList();
      if(sessionStorage.getItem('merchantProductCost')) {
        this.form = JSON.parse(sessionStorage.getItem('merchantProductCost'));
        this.form.ruleType += '';
        this.form.chargeType += '';
        this.form.formulaType += '';
        this.productInfo = this.form.productName;
        this.merchantInfo = this.form.mchName;
        this.form.ruleParam = JSON.parse(this.form.ruleParam) || [];
        this.form.ruleParam.forEach(item => {
          for (let p in item) {
            item[p] += ''
          }
        })
      }
      if (sessionStorage.getItem('merchant')) {
        this.form.mchNo = JSON.parse(sessionStorage.getItem('merchant')).mchNo;
        this.form.mchName = JSON.parse(sessionStorage.getItem('merchant')).mchName;
        this.form.productNo = JSON.parse(sessionStorage.getItem('merchant')).productNo;
        this.form.productName = JSON.parse(sessionStorage.getItem('merchant')).productName;
      }
    },
    beforeDestroy() {
      sessionStorage.removeItem('merchantProductCost');
      sessionStorage.removeItem('merchant');
    },
    methods: {
      getVendorList() {
        getVendorList({
          pageSize: 200,
          pageCurrent: 1
        }).then(response => {
          this.vendorList = response.data.data;
        })
      },
      getProductList() {
        getProductList({
          pageSize: 1000,
          pageCurrent: 1
        }).then(response => {
          this.productList = response.data.data;
        })
      },
      getMerchantList() {
        getMerchantList({
          pageSize: 1000,
          pageCurrent: 1
        }).then(response => {
          this.merchantList = response.data.data;
        })
      },
      selectMerchant(val) {
        this.form.mchNo = val[0];
        this.form.mchName = val[1];
      },
      selectProduct(val) {
        this.form.productNo = val[0];
        this.form.productName = val[1];
      },
      addRuleList() {
        this.form.ruleParam.push({
          specialRuleType: '',
          compareType: '',
          value: ''
        })
      },
      selectRuleType(index) {
        this.form.ruleParam[index].compareType = '1';
      },
      submit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            if (this.$dictCode('RuleTypeEnum', this.form.ruleType).desc == '通用') {
              this.form.ruleParam = [];
            }
            if (this.actionType == "ADD") {
              addMerchantProductFee(this.form).then((response) => {
                this.$message.success("创建成功");
                this.$refs.form.resetFields();
                this.$router.push("/business/merchantProductCostManagement");
              });
            } else if (this.actionType == 'EDIT') {
              editMerchantProductFee(this.form).then((response) => {
                this.$message.success("编辑成功");
                this.$refs.form.resetFields();
                this.$router.push("/business/merchantProductCostManagement");
              });
            }
          }
        });
      },
      cancel() {
        this.$router.push("/business/merchantProductManagement");
      },
      changePriority(value) {
        value = value.replace(/[^\d]/g, '');
        this.form.priority = value;
      },
      deleteRule(index) {
        this.form.ruleParam.splice(index, 1)
      }
    },


  };
</script>



<style lang="scss" scoped>
.box-container {
  padding: 20px;
  background: #fff;
  .el-form {
    width: 800px;
    margin-left: 30px;
    .el-form-item {
      width: 500px;
    }
    .form-item-main {
      width: 100%;
    }

      ::v-deep .el-form-item__label {
        font-weight: normal;
      }
    }

    .footer-container {
      padding-left: 120px;
    }
  }
</style>
