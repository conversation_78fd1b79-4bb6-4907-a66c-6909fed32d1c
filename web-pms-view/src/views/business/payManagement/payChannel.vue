<template>
  <div class="box-container">
    <el-button class="create-btn" type="primary" @click="showDialog('ADD')">新建</el-button>
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">通道编号：</span>
            <el-input v-model="form.payChannelNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">通道名称：</span>
            <el-input v-model="form.payChannelNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">通道类型：</span>
            <el-select clearable v-model="form.channelType">
              <el-option v-for="item in $dict('ChannelTypeEnum')" :key="item.code" :value="item.code" :label="item.desc"></el-option>
            </el-select>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="getPayChannelList(true)">查询</el-button>
            <el-button type="text" @click="resetForm">清空筛选条件</el-button>
          </div>

        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table class="content-main" :data="payChannelObj.data">
        <el-table-column type="index" :index="getIndex" label="序号"/>
        <el-table-column label="支付通道编号" prop="payChannelNo">

        </el-table-column>
        <el-table-column label="支付通道名称" prop="payChannelName">

        </el-table-column>
        <el-table-column label="通道类型" prop="channelType">
          <template v-slot="{row}">
            {{ row.channelType && row.channelType.map(item => $dictCode('ChannelTypeEnum', item).desc).join(', ') }}
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="status">
          <template slot-scope="scope">
            {{ $dictCode('OpenCloseEnum',scope.row.status).desc }}
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="description">
        </el-table-column>
        <el-table-column label="操作人" prop="updateOperator">
        </el-table-column>
        <el-table-column label="最后修改时间" prop="updateTime">
          <p slot-scope="{row, column}" v-html="renderTime(row[column['property']])"></p>
        </el-table-column>
        <el-table-column label="操作" prop="data">
          <template slot-scope="scope">
            <el-button type="text" @click="triggerStatus(scope.row)">{{ $dictCode('OpenCloseEnum', scope.row.status).desc == '启用' ? '禁用' : '启用' }}</el-button>
            <el-button type="text" @click="showDialog('EDIT',scope.row)">修改</el-button>
            <el-button type="text" @click="handleConfirm('删除', scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="payChannelObj"
          ref="pagination"
          :total="payChannelObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>

      <payChannelForm ref="dialog" @success="getPayChannelList"></payChannelForm>
    </div>
  </div>
</template>

<script>
  import { changePayChannelStatus, deletePayChannel, getPayChannelList } from '../../../api/levy'
  import payChannelForm from './payChannelForm'

  export default {
    name: 'PmsPayChannel',
    components: {
      payChannelForm
    },
    data() {
      return {
        form: {
          payChannelNo: '',
          payChannelNameLike: '',
          channelType: '',
        },
        activeTab: 'first',
        pageCurrent: 1,
        pageSize: 10,
        payChannelObj: {
          data: [{}]
        }
      }
    },
    mounted() {
      this.getPayChannelList();
    },
    methods: {
      getPayChannelList(reset) {
        if (reset === true) {
          this.pageCurrent = 1;
        }
        getPayChannelList({
          ...this.form,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        }).then(response => {
          this.payChannelObj = response.data;
        })
      },
      getTimeRange(val) {
        this.form.timeRange = val;
      },
      resetForm() {
        this.form = {
          payChannelNo: '',
          payChannelNameLike: '',
          channelType: '',
        }
      },
      handleCurrentChange(val) {
        this.pageCurrent = val;
        this.getPayChannelList();
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getPayChannelList(true);
      },
      showDialog(status, data) {
        this.$refs.dialog.show = true;
        this.$refs.dialog.actionType = status;
        data && (this.$refs.dialog.form = {
          id: data.id,
          payChannelName: data.payChannelName,
          payChannelNo: data.payChannelNo,
          channelTypeList: data.channelType || [],
          status: data.status + '',
          description: data.description
        });
      },
      triggerStatus(data) {
        this.$confirm(`确定要${this.$dictCode('OpenCloseEnum', data.status).desc == '启用' ? '禁用' : '启用'}该条数据吗`, {
          type: 'warning'
        }).then(res => {
          changePayChannelStatus({
            id: data.id,
            status: this.$dictCode('OpenCloseEnum', data.status).desc == '启用' ? '101' : '100'
          }).then(response => {
            this.$message.success('修改成功');
            this.getPayChannelList();
          })
        }).catch(() => {})

      },
      handleConfirm(text, data) {
        this.$confirm(`确定要${text}该条数据吗`, {
          type: 'warning'
        }).then(res => {
          deletePayChannel({
            id: data.id
          }).then(response => {
            this.$message.success('删除成功');
            this.getPayChannelList();
          })
        }).catch(() => {})
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
    }
  }
</script>

<style scoped>
</style>

