<template>
  <el-main>
    <p class="title-container">{{ merchantInfo.mchName }}</p>
    <div class="header-container">
      <div class="header-title">资产总览</div>
      <el-divider></el-divider>
      <div class="header-content">
        <div>总可用金额（元）</div>
        <div class="account-balance">{{ totalAmount }}</div>
      </div>
    </div>

    <div class="content-container">
      <el-tabs
        v-model="activeTab"
        @tab-click="onTabChange">
        <el-tab-pane
          v-for="(item, index) in tabList"
          :key="index"
          :name="String(index)"
          :label="item"
        ></el-tab-pane>
      </el-tabs>
      <template
        v-if="activeTab === '0'">
        <el-select
          clearable
          filterable
          v-model="mainstayNo"
          @change="checkBalance"
        >
          <el-option
            v-for="(item, index) in mainstayList"
            :key="index"
            :label="item.mainstayName"
            :value="item.mainstayNo"
          ></el-option>
        </el-select>
        <el-table
          :data="list"
          :span-method="spanMethod"
          key="paychannel"
        >
          <el-table-column label="账户">
            <template v-slot="{row}">
              {{ row.mainstayNo }}<br>{{ row.mainstayName }}
            </template>
          </el-table-column>

          <el-table-column
            label="银行卡通道|可用余额（元）"
            width="200"
            prop="bankAmount"
          >
            <template v-slot="{row, column}">
              <div :class="{'text-center': row.mainstayNo === yishui.code }">
                <p
                  class="color-warning"
                  v-if="!isNaN(row[column['property']])"
                >
                  {{ row['bankAmount'] | moneyFormat }}
                  <span v-if="row.mainstayNo === yishui.code">（已扣除服务费）</span>
                </p>
                <p
                  v-else-if="row[column['property']] == '异常'"
                  style="color: #f00"
                >
                  {{ row[column['property']] }}
                </p>
                <p
                  v-else
                  style="color: #ccc"
                >
                  {{ row[column['property']] }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="支付宝通道|可用余额（元）"
            width="200"
            prop="aliPayAmount"
          >
            <template v-slot="{row, column}">
              <p
                class="color-warning"
                v-if="!isNaN(row[column['property']])"
              >
                {{ row['aliPayAmount'] | moneyFormat }}
              </p>
              <p
                v-else-if="row[column['property']] == '异常'"
                style="color: #f00"
              >
                {{ row[column['property']] }}
              </p>
              <p
                v-else
                style="color: #ccc"
              >
                {{ row[column['property']] }}
              </p>

            </template>
          </el-table-column>
          <el-table-column
            label="微信通道|可用余额（元）"
            width="200"
            prop="weixinAmount"
          >
            <template v-slot="{row, column}">
              <p
                class="color-warning"
                v-if="!isNaN(row[column['property']])"
              >
                {{ row['weixinAmount'] | moneyFormat }}
              </p>
              <p
                v-else-if="row[column['property']] == '异常'"
                style="color: #f00"
              >
                {{ row[column['property']] }}
              </p>
              <p
                v-else
                style="color: #ccc"
              >
                {{ row[column['property']] }}
              </p>

            </template>
          </el-table-column>

          <el-table-column label="可抵扣金额（元）"></el-table-column>

          <el-table-column label="操作">
            <template v-slot="{row}">
              <el-button
                type="text"
                v-permission="'wx:balance:adjustment'"
                @click="wechatChange(row)"
              >微信调账
              </el-button>
              <el-button
                type="text"
                @click="goWechatAdjust(row)"
              >微信资金明细
              </el-button>
              <!-- <el-button type="text">账单下载</el-button> -->
              <!-- <el-button type="text">余额提醒</el-button> -->

              <el-button
                v-if="row.jxhOpen && row.jxhOpen === 'ON'"
                v-permission="'ac:balance:adjustment'"
                type="text"
                @click="junxianghuiChange(row)"
              >君享汇调账
              </el-button>
              <el-button
                v-else
                type="text"
                disabled
              >未开通君享汇通道
              </el-button>
              <el-button
                type="text"
                @click="goJXHAdjust(row)"
              >君享汇资金明细
              </el-button>

              <el-button
                type="text"
                v-permission="'cmb:balance:adjustment'"
                @click="cmbChange(row)"
              >招行通道调账
              </el-button>
              <el-button
                type="text"
                @click="goCmbAdjust(row)"
              >招行资金明细
              </el-button>

            </template>
          </el-table-column>
        </el-table>
      </template>
      <template v-else>
        <div class="flex-wrapper">
          <div class="flex-item-main">
              <span
                class="flex-item__label"
                v-width="120">请选择支付通道：</span>
            <el-select
              v-model="searchForm.payChannelNo"
              v-width="100"
              placeholder="选择支付通道"
              clearable
              filterable>
              <el-option
                v-for="item in payChannelList"
                :key="item.payChannelNo"
                :value="item.payChannelNo"
                :label="item.payChannelName"></el-option>
            </el-select>
            <el-select
              v-model="searchForm.channelType"
              placeholder="选择通道类型"
              clearable
              filterable>
              <el-option
                v-for="item in selectedChannelTypeList"
                :key="item"
                :value="item"
                :label="$dictCode('ChannelTypeEnum', item).desc"></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <el-input
              v-model="searchForm.trxNo"
              placeholder="请输入流水号"
              clearable></el-input>
          </div>
          <div class="flex-item">
            <el-select
              v-model="searchForm.accountNo"
              placeholder="全部账户"
              clearable
              filterable>
              <el-option
                v-for="(item, index) in mainstayList"
                :key="index"
                :label="item.mainstayName"
                :value="item.mainstayNo"
              ></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <el-select
              v-model="searchForm.altSource"
              placeholder="全部收支来源"
              clearable
              filterable>
              <el-option
                v-for="(item, index) in $dict('AltSourceEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"
              ></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <el-select
              v-model="searchForm.altType"
              placeholder="全部收支类型"
              clearable
              filterable>
              <el-option
                v-for="(item, index) in $dict('AltTypeEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <date-picker
              type="datetime"
              picker="separate"
              :start-time.sync="searchForm.createTimeBegin"
              :end-time.sync="searchForm.createTimeEnd"
            >
              <template v-slot:append>
                <el-button
                  type="primary"
                  @click="search(true)">查询
                </el-button>
                <el-button @click="exportDetail">导出明细</el-button>
                <el-button @click="getExportRecord">查看导出</el-button>
              </template>
            </date-picker>
          </div>
        </div>
        <el-table
          :data="list"
          key="detail">
          <el-table-column
            label="创建时间"
            prop="createTime"
            width="180"
          ></el-table-column>
          <el-table-column
            label="平台流水号/通道流水号"
            width="180">
            <template v-slot="{row}">
              <div>
                {{ row.trxNo }}
              </div>
              <div style="margin-top: 16px;">
                {{ row.channelTrxNo }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="账户"
            width="180">
            <template v-slot="{row}">
              {{ row.accountNo }}<br>{{ row.accountName }}
            </template>
          </el-table-column>
          <el-table-column
            label="支付通道"
            prop="payChannelName"
            width="150"
          >
            <template v-slot="{row}">
              {{ row.payChannelName }}({{ $dictCode('ChannelTypeEnum', row.channelType).desc }})
            </template>
          </el-table-column>
          <el-table-column
            label="动账金额">
            <template v-slot="{row}">
              {{ row.altAmount | moneyFormat }}
            </template>
          </el-table-column>
          <el-table-column label="收支来源">
            <template v-slot="{row}">
              {{ $dictCode('AltSourceEnum', row.altSource).desc }}
            </template>
          </el-table-column>
          <el-table-column
            label="来源说明"
            prop="altDesc"
            width="120">
          </el-table-column>
          <el-table-column label="收支类型">
            <template v-slot="{row}">
              {{ $dictCode('AltTypeEnum', row.altType).desc }}
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination-container">
          <el-pagination
            ref="pagination"
            :total="page.total"
            :current-page.sync="page.current"
            :page-sizes="[10,50]"
            :page-size.sync="page.size"
            layout="total, sizes, prev, pager, next, jumper"
            background
            @size-change="search(true)"
            @current-change="search()"
          >
          </el-pagination>
        </div>
      </template>
    </div>

    <!-- 微信调账弹窗 -->
    <el-dialog
      title="微信调账"
      :visible="showDialog"
      :before-close="() => showDialog = false"
      :close-on-click-modal="false"
    >
      <el-form
        :model="wxAdjustForm"
        label-width="80px"
        :rules="rules"
        ref="form"
      >
        <el-form-item
          label="商户"
          prop="mchName"
        >
          <el-input
            v-model="wxAdjustForm.mchName"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item
          label="供应商"
          prop="mainstayName"
        >
          <el-input
            v-model="wxAdjustForm.mainstayName"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item
          label="调账方式"
          prop="type"
        >
          <el-select
            clearable
            v-model="wxAdjustForm.type"
          >
            <el-option
              v-for="(item, index) in $dict('AdjustmentEnum')"
              :key="index"
              :label="item.desc"
              :value="Number(item.code)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="金额"
          prop="amount"
        >
          <el-input
            clearable
            :value="wxAdjustForm.amount"
            @input="handleRateInput(wxAdjustForm, 'amount', $event, {
              len: 13,
              decLen: 2,
            })"
          ></el-input>
        </el-form-item>
      </el-form>

      <template v-slot:footer>
        <el-button
          type="primary"
          @click="confirmWxAdjust"
        >确认
        </el-button>
        <el-button @click="showDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 君享汇调账弹窗 -->
    <el-dialog
      title="君享汇调账"
      :visible="jxhShowDialog"
      :before-close="() => jxhShowDialog = false"
      :close-on-click-modal="false"
    >
      <el-form
        :model="jxhAdjustForm"
        label-width="80px"
        :rules="rules"
        ref="jxhForm"
      >
        <el-form-item
          label="商户"
          prop="mchName"
        >
          <el-input
            v-model="jxhAdjustForm.mchName"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item
          label="供应商"
          prop="mainstayName"
        >
          <el-input
            v-model="jxhAdjustForm.mainstayName"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item
          label="调账方式"
          prop="type"
        >
          <el-select
            clearable
            v-model="jxhAdjustForm.type"
          >
            <el-option
              v-for="(item, index) in $dict('AdjustmentEnum')"
              :key="index"
              :label="item.desc"
              :value="Number(item.code)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="金额"
          prop="amount"
        >
          <el-input
            clearable
            :value="jxhAdjustForm.amount"
            @input="handleRateInput(jxhAdjustForm, 'amount', $event, {
              len: 13,
              decLen: 2,
            })"
          ></el-input>
        </el-form-item>
      </el-form>

      <template v-slot:footer>
        <el-button
          type="primary"
          @click="confirmJXHAdjust"
        >确认
        </el-button>
        <el-button @click="jxhShowDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 招行通道调账弹窗 -->
    <el-dialog
      title="招行通道调账"
      :visible="cmbShowDialog"
      :before-close="() => cmbShowDialog = false"
      :close-on-click-modal="false"
    >
      <el-form
        :model="cmbAdjustForm"
        label-width="80px"
        :rules="rules"
        ref="cmbForm"
      >
        <el-form-item
          label="商户"
          prop="mchName"
        >
          <el-input
            v-model="cmbAdjustForm.mchName"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item
          label="供应商"
          prop="mainstayName"
        >
          <el-input
            v-model="cmbAdjustForm.mainstayName"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item
          label="调账方式"
          prop="type"
        >
          <el-select
            clearable
            v-model="cmbAdjustForm.type"
          >
            <el-option
              v-for="(item, index) in $dict('AdjustAccountTypeEnum')"
              :key="index"
              :label="item.desc"
              :value="Number(item.code)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="金额"
          prop="amount"
        >
          <el-input
            clearable
            :value="cmbAdjustForm.amount"
            @input="handleRateInput(cmbAdjustForm, 'amount', $event, {
              len: 13,
              decLen: 2,
            })"
          ></el-input>
        </el-form-item>
      </el-form>

      <template v-slot:footer>
        <el-button
          type="primary"
          @click="confirmCmbAdjust"
        >确认
        </el-button>
        <el-button @click="cmbShowDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!--导出dialog-->
    <export-record ref="exportRecord"></export-record>
  </el-main>
</template>

<script>
import {
  getAmountInMainstay,
  getMerchantList,
  getMainstayListByMchNo,
  listAcctDetail,
  exportCkAcctDetail
} from '@/api/merchant';
import { wechatAdjust, jxhAdjust, cmbAdjust } from '@/api/wechat'
import { getPayChannelList } from "@/api/levy";
import ExportRecord from "@/components/ExportRecord/index.vue";

export default {
  name: 'merchantPayAccountCheck',
  components: {
    ExportRecord,
  },
  data(vm) {
    return {
      searchForm: {},
      activeTab: '0',
      tabList: ['账户余额', '收支明细'],
      payChannelList: [],
      list: [],

      mainstayNo: '',
      mainstayList: [],

      merchantInfo: {},

      totalAmount: 0,

      yishui: vm.$dictFlag('SpecialSupplier', 'YISHUI'),

      editRow: null,
      showDialog: false,
      wxAdjustForm: {},
      rules: {
        mchName: [{ required: true, message: "请输入商户账户", trigger: "blur" }],
        mainstayName: [{ required: true, message: "请输入供应商账户", trigger: "blur" }],
        type: [{ required: true, message: "请选择调账方式", trigger: "change" }],
        amount: [
          { required: true, message: "请输入金额", trigger: "blur" },
          {
            validator: (rule, val, cb) => {
              if (val == 0) {
                return cb(new Error('金额不能为0'))
              }
              cb()
            }, trigger: 'blur'
          }
        ],
      },
      page: {
        current: 1,
        total: 0,
        size: 10,
      },

      jxhShowDialog: false,
      jxhAdjustForm: {},

      cmbShowDialog: false,
      cmbAdjustForm: {},
    }
  },
  mounted() {
    this.search()
    if (this.employerNo) {
      getMerchantList({
        mchNo: this.employerNo,
        pageCurrent: 1,
        pageSize: 100
      }).then(res => {
        this.merchantInfo = res.data.data[0]
      });
      getMainstayListByMchNo({
        employerNo: this.employerNo
      }).then(res => {
        this.mainstayList = res.data
      })
    }

    this.getPayChannelList()
  },
  computed: {
    employerNo() {
      return this.$route.query.employerNo
    },
    selectedChannelTypeList() {
      const res = this.payChannelList.find(item => {
        return item.payChannelNo === this.searchForm.payChannelNo
      })
      return res?.channelType || []
    }
  },
  methods: {
    search(init) {
      if (init === true) {
        this.page.current = 1
      }
      if (this.activeTab === '0') {
        this.checkBalance()
      } else {
        this.checkDetail()
      }
    },
    async checkBalance() {
      const { data } = await getAmountInMainstay({
        employerNo: this.employerNo,
        mainstayNo: this.mainstayNo,
      });
      if (this.activeTab === '0') {
        this.list = data.amountList;
        this.calculateAmount()
      }
    },
    calculateAmount() {
      let temp = 0;
      for (let i = 0; i < this.list.length; i++) {
        if (this.list[i]['mainstayNo'] === this.yishui.code) {
          temp += isNaN(this.list[i]['aliPayAmount']) ? 0 : parseFloat(this.list[i]['aliPayAmount']) * 100
        } else {
          ['bankAmount', 'aliPayAmount', 'weixinAmount'].forEach(item => {
            let money = isNaN(this.list[i][item]) ? 0 : parseFloat(this.list[i][item]) * 100;
            temp += money;
          })
        }
      }
      this.totalAmount = temp / 100
    },
    spanMethod({ row, column }) {
      if (row.mainstayNo === this.yishui.code) {
        // 易税跨表处理
        if (column.property === 'bankAmount') {
          return {
            rowspan: 1,
            colspan: 2
          }
        } else if (column.property === 'aliPayAmount') {
          return [0, 0]
        }
      }
    },
    async wechatChange(row) {
      this.editRow = JSON.parse(JSON.stringify(row))
      this.showDialog = true
      this.wxAdjustForm = {
        mchNo: this.merchantInfo.mchNo,
        mchName: this.merchantInfo.mchName,
        mainstayNo: this.editRow.mainstayNo,
        mainstayName: this.editRow.mainstayName,
        amount: 0,
        type: ''
      }
      await this.$nextTick()
      this.$refs.form.resetFields()
    },
    async confirmWxAdjust() {
      try {
        await this.$refs.form.validate()
      } catch {
        return;
      }
      try {
        await this.$confirm('此操作将直接改动账户资金情况，请谨慎操作！ 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch {
        return
      }
      this.wxAdjustForm.amount = Number(this.wxAdjustForm.amount)
      const { data } = await wechatAdjust(this.wxAdjustForm)
      data && this.$message.success(data)
      this.showDialog = false
      this.checkBalance()
    },
    goWechatAdjust(row) {
      this.$router.push({
        path: '/trade/account/wechatAccount',
        query: {
          mchNo: this.merchantInfo.mchNo,
          mchName: this.merchantInfo.mchName,
          mainstayNo: row.mainstayNo,
          mainstayName: row.mainstayName,
        }
      })
    },
    async getPayChannelList() {
      const { data: { data } } = await getPayChannelList({
        pageCurrent: 1,
        pageSize: 100
      })
      this.payChannelList = data
    },
    async checkDetail() {
      const { data: { records, total } } = await listAcctDetail({
        ...this.searchForm,
        size: this.page.size,
        current: this.page.current,
        employerNo: this.employerNo,
      })
      if (this.activeTab === '1') {
        this.list = records
        this.page.total = total
      }
    },
    async exportDetail() {
      await exportCkAcctDetail({ ...this.searchForm, employerNo: this.employerNo, })
      this.$message.success('导出成功')
    },
    getExportRecord() {
      this.$refs.exportRecord.open('60')
    },
    onTabChange() {
      this.list = []
      this.search(true)
    },

    // 君享汇调账弹窗
    async junxianghuiChange(row) {
      this.editRow = JSON.parse(JSON.stringify(row))
      this.jxhShowDialog = true
      this.jxhAdjustForm = {
        mchNo: this.merchantInfo.mchNo,
        mchName: this.merchantInfo.mchName,
        mainstayNo: this.editRow.mainstayNo,
        mainstayName: this.editRow.mainstayName,
        amount: 0,
        type: ''
      }
      await this.$nextTick()
      this.$refs.jxhForm.resetFields()
    },
    // 君享汇调账
    async confirmJXHAdjust() {
      try {
        await this.$refs.jxhForm.validate()
      } catch {
        return;
      }
      try {
        await this.$confirm('此操作将直接改动账户资金情况，请谨慎操作！ 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch {
        return
      }
      this.jxhAdjustForm.amount = Number(this.jxhAdjustForm.amount)
      const { data } = await jxhAdjust(this.jxhAdjustForm)
      data && this.$message.success(data)
      this.jxhShowDialog = false
      this.checkBalance()
    },

    // 君享汇资金明细
    goJXHAdjust(row) {
      this.$router.push({
        path: '/trade/account/jxhAccount',
        query: {
          mchNo: this.merchantInfo.mchNo,
          mchName: this.merchantInfo.mchName,
          mainstayNo: row.mainstayNo,
          mainstayName: row.mainstayName,
        }
      })
    },
    // 招行通道调账弹窗
    async cmbChange(row) {
      this.editRow = JSON.parse(JSON.stringify(row))
      this.cmbShowDialog = true
      this.cmbAdjustForm = {
        mchNo: this.merchantInfo.mchNo,
        mchName: this.merchantInfo.mchName,
        mainstayNo: this.editRow.mainstayNo,
        mainstayName: this.editRow.mainstayName,
        amount: 0,
        type: ''
      }
      await this.$nextTick()
      this.$refs.cmbForm.resetFields()
    },
    // 招行通道调账
    async confirmCmbAdjust() {
      try {
        await this.$refs.cmbForm.validate()
      } catch {
        return;
      }
      try {
        await this.$confirm('此操作将直接改动账户资金情况，请谨慎操作！ 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch {
        return
      }
      this.cmbAdjustForm.amount = Number(this.cmbAdjustForm.amount)
      const { data } = await cmbAdjust(this.cmbAdjustForm)
      data && this.$message.success(data)
      this.cmbShowDialog = false
      this.checkBalance()
    },
    // 招行资金明细
    goCmbAdjust(row) {
      this.$router.push({
        path: '/trade/account/cmbAccount',
        query: {
          mchNo: this.merchantInfo.mchNo,
          mchName: this.merchantInfo.mchName,
          mainstayNo: row.mainstayNo,
          mainstayName: row.mainstayName,
        }
      })
    }
  },
}
</script>

<style
  scoped
  lang="scss">
.title-container {
  font-size: 25px;
}

.header-container {
  background: #fff;

  .header-title {
    padding: 24px 24px 0 24px;
    font-size: 20px;
    font-weight: bolder;
  }

  .header-content {
    padding: 0 24px 40px;
  }
}

.content-container {
  margin-top: 32px;

  .el-table {
    margin-top: 16px;
  }
}

.account-balance {
  font-size: 30px;
  color: #ffa500;
}
</style>
