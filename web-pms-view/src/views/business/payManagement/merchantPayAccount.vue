<template>
  <div class="box-container">
    <el-button
      type="primary"
      class="create-btn"
      @click="visible = true">查询余额
    </el-button>
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">企业名称：</span>
            <el-input v-model="form.mchNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">企业编号：</span>
            <el-input v-model="form.employerNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">支付通道：</span>
            <el-select
              v-model="form.payChannelNo"
              clearable>
              <el-option
                v-for="(item, index) in payChannelList"
                :key="index"
                :label="item.payChannelName"
                :value="item.payChannelNo"></el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">供应商名称：</span>
            <el-input v-model="form.mainstayNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">供应商编号：</span>
            <el-input v-model="form.mainstayNo"></el-input>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="getMerchantPayAccountList(true)">查询
            </el-button>
            <el-button
              type="text"
              @click="resetForm">清空筛选条件
            </el-button>
          </div>

        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table
        class="content-main"
        :data="payAccountObj.data">
        <el-table-column
          type="index"
          :index="getIndex"
          label="序号"
          width="100px" />
        <el-table-column
          label="用工企业名称"
          prop="payAccountName">
          <template slot-scope="scope">
            {{ scope.row.employerNo }}<br>
            {{ scope.row.mchName }}
          </template>
        </el-table-column>
        <el-table-column
          label="所属供应商"
          prop="payAccountName">
          <template slot-scope="scope">
            {{ scope.row.mainstayNo }}<br>
            {{ scope.row.mainstayName }}
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in $dict('ChannelTypeEnum')"
          :label="'是否开通' + item.desc"
          :key="item.code">
          <template
            slot-scope="scope"
            v-if="scope.row.employerChannels">
            {{ (scope.row.employerChannels[index] && scope.row.employerChannels[index].status) == '100' ? '是' : '否' }}
            <br>
            {{ (scope.row.employerChannels[index] && scope.row.employerChannels[index].payChannelName) }}
            {{ (scope.row.employerChannels[index] && scope.row.employerChannels[index].payChannelNo) }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          prop="data">
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="showDialog('REPORT', row)">报备
            </el-button>
            <el-button
              type="text"
              v-permission="'report:employerAccountInfo:edit'"
              @click="showDialog('EDIT',row)">编辑
            </el-button>
            <el-button
              type="text"
              @click="openAccountSetting(row)">
              账户配置
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="payAccountObj"
          ref="pagination"
          :total="payAccountObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>

    <check-balance :visible.sync="visible"></check-balance>

    <!--  账户设置弹窗-->
    <account-setting
      ref="settingDialog"
      :account-info="editRow"></account-setting>
  </div>
</template>

<script>
import { getMerchantPayAccountList, getPayChannelList } from '@/api/levy'
import CheckBalance from './Components/CheckBalance'
import AccountSetting from "@/views/business/payManagement/Components/AccountSetting.vue";

export default {
  name: 'PmsMerchantPayAccount',
  components: {
    AccountSetting,
    CheckBalance
  },
  data() {
    return {
      form: {},
      pageCurrent: 1,
      pageSize: 10,
      payAccountObj: {
        data: [{}]
      },

      visible: false,
      editRow: {},
      payChannelList: []
    }
  },
  mounted() {
    this.getMerchantPayAccountList();
    this.getPayChannelList()
  },
  methods: {
    getMerchantPayAccountList(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      getMerchantPayAccountList({
        ...this.form,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent
      }).then(response => {
        this.payAccountObj = response.data;
      })
    },
    getTimeRange(val) {
      this.form.timeRange = val;
    },
    resetForm() {
      this.form = {
        payAccountNo: '',
        payAccountName: '',
        payAccountType: '',
      }
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.getMerchantPayAccountList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getMerchantPayAccountList(true);
    },
    showDialog(status, data) {
      this.$router.push({
        path: '/business/merchantPayAccountForm',
        query: {
          actionType: status,
          employerNo: data.employerNo,
          mainstayNo: data.mainstayNo,
        }
      })
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    openAccountSetting(row) {
      this.editRow = row
      this.$refs.settingDialog.open()
    },
    getPayChannelList() {
      getPayChannelList({
        pageSize: 1000,
        pageCurrent: 1
      }).then(response => {
        this.payChannelList = response.data.data;
      })
    },
  }
}
</script>

<style scoped>

</style>

