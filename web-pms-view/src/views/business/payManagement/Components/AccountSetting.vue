<template>
  <el-dialog
    :visible.sync="visible"
    title="账户配置"
    width="50%">
    <el-tabs
      v-model="searchForm.channelType"
      @tab-click="getRecordByChannelType">
      <el-tab-pane
        v-for="item in $dict('ChannelTypeEnum')"
        :key="item.code"
        :name="item.code"
        :label="item.desc">
        <template
          v-for="setting in settingList">
          <el-form
            :model="setting"
            :key="setting.id"
            label-width="100px">
            <el-form-item :label="setting.payChannelName + ':'">
              <el-switch
                v-model="setting.isShow"
                :active-value="1"
                :inactive-value="0"
                @change="changeStatus({id: setting.id, isShow: $event})"></el-switch>
              <span class="setting-merchant-no">{{ setting.channelMerchantNo }}</span>
            </el-form-item>
            <el-form-item v-show="setting.isShow">
              <template v-slot:label>
                <el-tooltip
                  content="变更商户通道账户描述文字"
                  effect="dark"
                  placement="top">
                  <div>
                    <i class="el-icon-warning-outline"></i>账户标题:
                  </div>
                </el-tooltip>
              </template>
              <div class="account-title-box flex-box">
                <el-input
                  v-model="setting.title"
                  clearable>
                </el-input>
                <el-button
                  type="primary"
                  :disabled="loading"
                  @click="saveTitle(setting)">保存
                </el-button>
              </div>
            </el-form-item>
          </el-form>
        </template>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>


<script>
import { changeAccountStatus, changeAccountTitle, getRecordByChannelType } from "@/api/account";

export default {
  name: 'AccountSetting',
  props: {
    accountInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: false,
      settingList: [],
      searchForm: {
        employerNo: '',
        mainstayNo: '',
        channelType: '1',
      },
      loading: false,
    }
  },
  watch: {
    accountInfo: {
      handler({ employerNo = '', mainstayNo = '' }) {
        this.searchForm = {
          employerNo,
          mainstayNo,
          channelType: '1'
        }
      }
    }
  },
  methods: {
    async getRecordByChannelType() {
      const { data } = await getRecordByChannelType(this.searchForm)
      this.settingList = data
    },
    open() {
      this.toggle(true)
      this.$nextTick(this.getRecordByChannelType)
    },
    close() {
      this.toggle(false)
      this.searchForm = {
        employerNo: '',
        mainstayNo: '',
        channelType: '1',
      }
    },
    toggle(status = false) {
      this.visible = status
    },
    async changeStatus({ id, isShow }) {
      this.loading = true
      const { data } = await changeAccountStatus({
        id,
        isShow
      })
      this.loading = false
      data && this.$message.success(data)
      this.getRecordByChannelType()
    },
    async saveTitle({ id, title }) {
      this.loading = true
      const { data } = await changeAccountTitle({
        id,
        title
      })
      this.loading = false
      data && this.$message.success(data)
      this.getRecordByChannelType()
    }
  }
}
</script>

<style
  lang="scss"
  scoped>
.setting-merchant-no {
  margin: 0 16px;
  color: gray;
}

.el-form-item {
  margin-bottom: 8px;
}

</style>
