<template>
  <el-dialog
    :visible="visible"
    :title="title"
    :before-close="beforeClose">
    <el-button
      type="primary"
      @click="exportList"
      v-show="resultVisible">
      导出列表
    </el-button>
    <el-button
      type="text"
      @click="getExportList">查看已导出列表
    </el-button>
    <el-row v-show="!resultVisible">
      <el-col :span="12">
        <el-select
          v-model="form.mchType"
          style="width: 100%">
          <template
            v-for="item in $dict('MerchantTypeEnum')"
          >
            <el-option
              v-if="item.code != 102"
              :key="item.code"
              :value="item.code"
              :label="'按' + item.desc + '查'">
            </el-option>
          </template>
        </el-select>
      </el-col>
      <el-col :span="12">
        <el-input
          clearable
          v-model="form.mchLike"
          :placeholder="form.mchType == '101' ? '请输入代征主体编号/名称' :'请输入用工企业编号/名称' "
        ></el-input>
      </el-col>
    </el-row>

    <div v-show="resultVisible">
      <ExportRecord ref="exportRecord"></ExportRecord>
      <el-table
        border
        :data="result"
      >
        <el-table-column label="用工企业名称">
          <template v-slot="{row}">
            {{ row.employerNo }}<br>
            {{ row.employerName }}
          </template>
        </el-table-column>
        <el-table-column label="帐户总可用金额">
          <template v-slot="{row}">
            ¥ {{ row.totalAmount }}
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="goDetail(row)">查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        style="text-align: right"
        :page-size.sync="page.pageSize"
        :current-page.sync="page.currentPage"
        :page-sizes="[10, 50]"
        :total="totalRecord"
        @current-change="getAmount()"
        @size-change="getAmount(true)"
      ></el-pagination>
    </div>

    <div slot="footer">
      <el-button @click="beforeClose">取消</el-button>
      <el-button
        :loading="loading"
        v-if="!resultVisible"
        type="primary"
        @click="confirm">确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getAmountWithMerchantInfo, exportAmountWithMerchantInfo } from "@/api/merchant";
import { toPromise } from "@/utils";
import ExportRecord from '@/components/ExportRecord'

export default {
  components: {
    ExportRecord
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      form: {},
      page: {
        pageSize: 10,
        currentPage: 1,
      },
      totalRecord: 0,
      result: [],
      resultVisible: false,
      loading: false,
    }
  },
  computed: {
    title() {
      return this.resultVisible ? '查询余额' : '查询结果'
    }
  },
  methods: {
    beforeClose() {
      this.resultVisible = false
      this.form = {}
      this.page = {
        pageSize: 10,
        currentPage: 1,
      }
      this.loading = false
      this.totalRecord = 0
      this.close();
    },
    close() {
      this.$emit('update:visible', false)
    },
    confirm() {
      this.getAmount(true)
    },
    async getAmount(init) {
      if (init === true) {
        this.page.currentPage = 1
      }
      this.loading = true
      const [err, resp] = await toPromise(getAmountWithMerchantInfo({ ...this.form, ...this.page }))
      this.loading = false
      if (err) return
      const { data: { data, totalRecord } } = resp
      this.result = data
      this.resultVisible = true
      this.totalRecord = totalRecord
    },
    goDetail({ employerNo }) {
      this.$router.push({
        path: '/business/merchantPayAccountCheck',
        query: {
          employerNo
        }
      })
    },
    async exportList() {
      await exportAmountWithMerchantInfo({
        ...this.form,
      });
      this.$message.success('导出成功，请到导出列表查看')
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord("59");
    },
  },
}
</script>

<style
  scoped
  lang="scss">

</style>
