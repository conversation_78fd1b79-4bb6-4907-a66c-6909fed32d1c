<template>
  <div>
    <transition
      name="topBottom"
    >
      <div v-show="showSuccess">
        <div class="success-block">
          <div class="success-img">
            <img
              src="@/assets/success.png"
              alt="success">
          </div>
          <i
            class="el-icon-close"
            @click="showSuccess = false"></i>
          <div class="success-title">已提交!</div>
          <div class="success-content color-gray">已提交信息，请在通道报备记录处跟进报备状况。</div>
          <div class="success-content">
            <router-link
              to="/business/channelRecord"
              class="func-content">查看通道报备记录
            </router-link>
          </div>
        </div>
      </div>
    </transition>

    <div class="mainstay-payAccount-form page-container">
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="small"
        label-width="180px"
        :disabled="actionType === 'VIEW'">
        <el-form-item
          label="供应商"
          prop="status">
          <el-select
            clearable
            v-model="form.mainstayNo"
            @change="selectMainstay">
            <el-option
              v-for="item in mainstayList"
              :key="item.mchNo"
              :value="item.mchNo"
              :label="item.mchName">
            </el-option>
          </el-select>
        </el-form-item>
        <div
          v-for="(info, index) in form.channelInfos"
          :key="info.payChannelNo">
          <template v-if="info.payChannelNo !== 'ALIPAY' && info.payChannelNo !== 'WXPAY'">
            <el-form-item
              :label="'是否开通' + info.payChannelName"
              prop="status">
              <el-radio-group v-model="info.status">
                <el-radio
                  v-for="item in $dict('OpenCloseEnum')"
                  :key="item.code"
                  :label="item.code">{{ item.desc }}
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <template v-if="info.status === '100'">
              <el-form-item
                label="通道商户编号"
                prop="channelMchNo">
                <el-input v-model="info.channelMchNo"></el-input>
              </el-form-item>
              <el-form-item
                v-if="info.payChannelNo === 'CMB'"
                label="外部系统登录号"
                prop="channelLoginUser">
                <el-input
                  v-model="info.channelLoginUser"
                  placeholder="部分通道选填"></el-input>
              </el-form-item>
              <el-form-item
                v-if="info.payChannelNo === 'CMB'"
                label="平台编号"
                prop="channelPlatNo">
                <el-input
                  v-model="info.channelPlatNo"
                  placeholder="部分通道选填"></el-input>
              </el-form-item>
              <!--              <el-form-item v-if="info.payChannelNo == 'CMB'" label="银行名称" prop="bankName">
                              <el-input v-model="info.bankName" placeholder="请输入银行名称"></el-input>
                            </el-form-item>
                            <el-form-item v-if="info.payChannelNo == 'CMB'" label="支行名称" prop="subBankName">
                              <el-input v-model="info.subBankName" placeholder="请输入支行名称"></el-input>
                            </el-form-item> -->
              <el-form-item
                label="通道密钥"
                prop="channelKey">
                <el-input v-model="info.channelKey"></el-input>
              </el-form-item>
              <el-form-item
                label="商户私钥"
                prop="privateKey">
                <el-input v-model="info.privateKey"></el-input>
              </el-form-item>
              <el-form-item
                label="商户公钥"
                prop="publicKey">
                <el-input v-model="info.publicKey"></el-input>
              </el-form-item>
            </template>
          </template>
          <template v-else>
            <el-form-item
              :label="'是否开通' + info.payChannelName"
              prop="status">
              <el-radio-group v-model="info.status">
                <el-radio
                  v-for="item in $dict('OpenCloseEnum')"
                  :key="item.code"
                  :label="item.code">{{ item.desc }}
                </el-radio>
              </el-radio-group>
              <template v-if="info.status === '100'">
                <p
                  v-if="info.payChannelNo === 'ALIPAY'"
                  class="form-tip color-gray">{{ info.payChannelName }}支持接口创建商户，商户在完成用户授权（扫码签约）后即可创建，点击
                  <span
                    class="func-content"
                    @click="submitChannel(info.channelType, info.payChannelNo, info.payChannelName)">报备账户</span>
                  <el-button @click="unsign(info)">解除签约</el-button>
                </p>
                <p
                  v-else
                  class="form-tip color-gray">报备后将在系统生成对应的虚拟账户，请先填写特约商户号并进行报备后再发放
                  <span
                    class="func-content"
                    @click="submitChannel(info.channelType, info.payChannelNo, info.payChannelName)">报备账户</span>
                </p>
              </template>
            </el-form-item>
            <el-form-item
              label="通道商户编号："
              v-if="info.status === '100'">
              <el-input
                :disabled="info.payChannelNo === 'ALIPAY'"
                placeholder="请输入"
                v-model="info.channelMchNo"></el-input>
            </el-form-item>

            <template v-if="info.payChannelNo === 'WXPAY' && info.status === '100'">
              <el-form-item
                label="企业直收户名"
                :prop="'channelInfos.' + index + '.accountName'"
              >
                <el-input
                  v-model="info.accountName"
                  placeholder="请输入"
                  maxlength="50"
                ></el-input>
              </el-form-item>

              <el-form-item
                label="企业直收账户"
                :prop="'channelInfos.' + index + '.accountNo'"
              >
                <el-input
                  v-model="info.accountNo"
                  placeholder="请输入"
                  maxlength="30"
                ></el-input>
              </el-form-item>
            </template>
          </template>

        </div>
      </el-form>
      <div class="form-btn-group">
        <el-button
          v-if="actionType !== 'VIEW'"
          :loading="loading"
          type="primary"
          @click="doSubmit">确认
        </el-button>
        <el-button @click="closeForm">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  createMainstayPayAccount,
  getPayChannelList,
  getSingerMainstayPayAccount,
  updateMainstayPayAccount,
  submitChannel,
  unsign
} from '@/api/levy'
import {getMainstayList} from '@/api/merchant'

export default {
  data() {
    // 验证特殊字符
    const validateSpecialChar = (rule, value, callback) => {
      if (!value) {
        callback();
        return;
      }
      const specialCharPattern = /[`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：""【】、；''，。、]/;
      if (specialCharPattern.test(value)) {
        callback(new Error('不能包含特殊字符'));
      } else {
        callback();
      }
    };
    return {
      loading: false,
      show: false,
      form: {
        mainstayNo: '',
        mainstayName: '',
        channelInfos: [],
      },
      payChannelMap: {},  // key：通道编号，value：支付通道
      mainstayList: [],
      rules: {
        productName: [{ required: true, message: '请输入行业类型名称', trigger: 'blur' }],
        productNo: [{ required: true, message: '请输入行业类型编号', trigger: 'blur' }],
        productType: [{ required: true, message: '请选择父行业类型', trigger: 'blur' }],
        productStatus: [{ required: true, message: '请选择父行业类型', trigger: 'blur' }],
      },
      showSuccess: false,
      validateRules: {
        accountName: [
          { required: false, message: '请输入企业直收户名', trigger: 'blur' },
          { validator: validateSpecialChar, trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        accountNo: [
          { required: false, message: '请输入企业直收账户', trigger: 'blur' },
          { validator: validateSpecialChar, trigger: 'blur' },
          { min: 1, max: 30, message: '长度在 1 到 30 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    mainstayNo() {
      return this.$route.query.mainstayNo;
    },
    actionType() {
      return this.$route.query.actionType;
    },
  },
  async mounted() {
    const res = await getPayChannelList({ pageSize: 1000, pageCurrent: 1 })
    for (let [index, item] of res.data.data.entries()) {
      item.status = String(item.status);
      this.payChannelMap[item.payChannelNo] = item;
      this.form.channelInfos.push(item);

      // 动态添加验证规则
      this.rules[`channelInfos.${index}.accountName`] = this.validateRules.accountName;
      this.rules[`channelInfos.${index}.accountNo`] = this.validateRules.accountNo;
    }
    if (this.mainstayNo) {
      this.getSingerMainstayPayAccount();
    }
    getMainstayList().then(res => {
      this.mainstayList = res.data;
    })
  },
  methods: {
    async getSingerMainstayPayAccount() {
      const { data } = await getSingerMainstayPayAccount({
        mainstayNo: this.mainstayNo
      });
      this.form.mainstayNo = data.mainstayNo;
      this.form.mainstayName = data.mainstayName;
      for (let item of data.channelInfos) {
        if (this.payChannelMap[item.payChannelNo]) {
          item.payChannelName = this.payChannelMap[item.payChannelNo].payChannelName;
          item.status = item.status + '';
        }
      }
      this.form.channelInfos = data.channelInfos;
    },
    selectMainstay(val) {
      this.mainstayList.forEach(item => {
        if (val === item.mchNo) {
          this.form.mainstayName = item.mchName;
        }
      })
    },
    doSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.actionType === 'ADD' && this.doAdd();
          this.actionType === 'EDIT' && this.doEdit();
        }
      })
    },
    doAdd() {
      createMainstayPayAccount(this.form).then(response => {
        this.$message.success('创建成功');
        this.closeForm()
        this.$emit('success')
      }).finally(() => this.loading = false)
    },
    doEdit() {
      updateMainstayPayAccount(this.form).then(response => {
        this.$message.success('修改成功')
        this.closeForm()
        this.$emit('success')
      }).finally(() => this.loading = false)
    },
    closeForm() {
      this.show = false
      this.loading = false
      this.form = {
        mainstayNo: '',
        mainstayName: '',
        channelInfos: []
      }
      this.$router.push('/business/mainstayPayAccount');
    },
    submitChannel(channelType, payChannelNo, payChannelName) {
      submitChannel({
        employerNo: this.form.employerNo,
        employerName: this.form.employerName,
        mainstayNo: this.form.mainstayNo,
        mainstayName: this.form.mainstayName,
        payChannelNo,
        payChannelName,
        // channelType,
        merchantType: '101',
      }).then(res => {
        // this.$message.success(res.data);
        this.showSuccess = true;
      })
    },
    async unsign(info) {
      const { data } = await unsign({
        mainstayNo: this.form.mainstayNo,
        mainstayName: this.form.mainstayName,
        payChannelNo: info.payChannelNo,
        payChannelName: info.payChannelName,
        merchantType: 101,
      });
      data && this.$message.success(data);
      this.getSingerMainstayPayAccount();
    }
  }
}
</script>

<style
  lang="scss"
  scoped>
.mainstay-payAccount-form {
  padding: 50px;
  background: #fff;
}

.success-block {
  position: relative;
  margin-bottom: 24px;
  padding: 24px 24px 24px 60px;
  border: 1px solid #008000;
  border-radius: 5px;
  background: #F6FFED;

  .success-img {
    position: absolute;
    top: 12px;
    left: 12px;
    width: 30px;
  }

  .success-content {
    margin-top: 12px;
  }

  .el-icon-close {
    position: absolute;
    right: 12px;
    top: 12px;
    z-index: 10;
  }
}

.topBottom-enter, .topBottom-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.topBottom-enter-active, .topBottom-leave-active {
  transition: all linear .1s;
}
</style>
