<template>
  <div class="box-container">
    <el-button class="create-btn" type="primary" @click="showDialog('ADD')">新建</el-button>
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">供应商名称：</span>
            <el-input v-model="form.mainstayNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">供应商编号：</span>
            <el-input v-model="form.mainstayNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">支付通道：</span>
            <el-select clearable v-model="form.payChannelNoList" multiple>
              <el-option v-for="item in payChannelList" :key="item.payChannelNo" :value="item.payChannelNo" :label="item.payChannelName"></el-option>
            </el-select>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="getMainstayPayAccountList(true)">查询</el-button>
            <el-button type="text" @click="resetForm">清空筛选条件</el-button>
          </div>

        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table class="content-main" :data="payAccountObj.data">
        <el-table-column type="index" :index="getIndex" label="序号"/>
        <el-table-column label="供应商名称" prop="payAccountName">
          <template slot-scope="scope">
            {{ scope.row.mainstayNo }}<br>
            {{ scope.row.mainstayName }}
          </template>
        </el-table-column>
        <template v-for="(item) in payChannelList">
          <el-table-column :label="'是否开通' + item.payChannelName" :key="item.payChannelNo">
            <template slot-scope="scope">
              <p>{{ chooseChannels(item.payChannelNo, scope.row.mainstayChannels, 'payChannelNo', 'status') | statusFilter }}</p>
              <span v-if="chooseChannels(item.payChannelNo, scope.row.mainstayChannels, 'payChannelNo', 'status') == '100'">
                {{ chooseChannels(item.payChannelNo, scope.row.mainstayChannels, 'payChannelNo', 'channelMchNo') }}
              </span>
            </template>
          </el-table-column>
        </template>
        <el-table-column label="操作" prop="data">
          <template slot-scope="scope">
            <!--<el-button type="text" @click="showDialog('VIEW', scope.row)">查看</el-button>-->
            <el-button type="text" @click="showDialog('EDIT',scope.row)">编辑</el-button>
            <el-button type="text" @click="handleConfirm('删除', scope.row)">删除</el-button>
            <el-button type="text" @click="goBalance(scope.row)">查看余额</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="payAccountObj"
          ref="pagination"
          :total="payAccountObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
  import { deleteMainstayPayAccount, getMainstayPayAccountList, getPayChannelList } from '../../../api/levy'

  export default {
    name: 'PmsMainstayPayAccount',
    data() {
      return {
        form: {
          mainstayNo: '',
          mainstayNameLike: '',
          payChannelNoList: [],
        },
        pageCurrent: 1,
        pageSize: 10,
        payChannelList: [],
        payAccountObj: {
          data: []
        }
      }
    },
    filters: {
      statusFilter(val) {
        return val == '100' ? '是' : '否'
      }
    },
    mounted() {
      this.getPayChannelList();
      this.getMainstayPayAccountList();
    },
    methods: {
      getPayChannelList() {
        getPayChannelList({
          pageSize: 1000,
          pageCurrent: 1
        }).then(response => {
          this.payChannelList = response.data.data;
        })
      },
      getMainstayPayAccountList(initFlag) {
        if (initFlag === true) {
          this.pageCurrent = 1;
        }
        getMainstayPayAccountList({
          ...this.form,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        }).then(response => {
          this.payAccountObj = response.data;
        })
      },
      resetForm() {
        this.form = {
          mainstayNo: '',
          mainstayNameLike: '',
          payChannelNoList: [],
        }
      },
      handleCurrentChange(val) {
        this.pageCurrent = val;
        this.getMainstayPayAccountList();
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.getMainstayPayAccountList(true);
      },
      showDialog(status, data) {
        this.$router.push('mainstayPayAccountForm?actionType=' + status + (data ? '&mainstayNo=' + data.mainstayNo : ""));
      },
      handleConfirm(text, data) {
        this.$confirm(`确定要${text}该条数据吗`, {
          type: 'warning'
        }).then(res => {
          deleteMainstayPayAccount({
            mainstayNo: data.mainstayNo
          }).then(response => {
            this.$message.success('删除成功');
            this.getMainstayPayAccountList();
          })
        }).catch(() => {})
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
      chooseChannels(val, list, prop, retprop) {
        for (let i = 0; i < list.length; i++) {
          if (list[i][prop] == val) {
            return list[i][retprop];
          }
        }
      },
      goBalance(row) {
        this.$router.push({
          path: '/business/mainstayBalance',
          query: {
            mainstayNo: row.mainstayNo,
            mainstayName: row.mainstayName
          }
        })
      }
    }
  }
</script>

<style scoped>

</style>

