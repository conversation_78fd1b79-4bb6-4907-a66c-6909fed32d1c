<template>
  <div>
    <transition name="topBottom">
      <div v-show="showSuccess">
        <div class="success-block">
          <div class="success-img">
            <img
              src="@/assets/success.png"
              alt="success">
          </div>
          <i
            class="el-icon-close"
            @click="showSuccess = false"></i>
          <div class="success-title">已提交!</div>
          <div class="success-content color-gray">已提交信息，请在通道报备记录处跟进报备状况。</div>
          <div class="success-content">
            <router-link
              to="/business/channelRecord"
              class="func-content">查看通道报备记录
            </router-link>
          </div>
        </div>
      </div>
    </transition>

    <div class="merchant-payAccount-form page-container">
      <el-button
        v-if="actionType == 'VIEW'"
        type="primary"
        @click="getBalance">查询余额
      </el-button>
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        size="small"
        label-width="150px"
        :disabled="actionType === 'VIEW'">
        <el-form-item
          label="用工企业"
          prop="employerName"
          class="form-item-inline">
          <el-input
            :disabled="actionType == 'EDIT'"
            v-model="form.mchName"></el-input>
        </el-form-item>
        <el-form-item
          label="供应商"
          prop="status"
          class="form-item-inline">
          <el-input
            :disabled="actionType == 'EDIT'"
            v-model="form.mainstayName"></el-input>
        </el-form-item>

        <div
          v-for="(item, index) in form.channelInfos"
          :key="item.channelType">
          <el-form-item
            :label="'是否开通' + $dictCode('ChannelTypeEnum', Number(item.channelType)).desc"
            prop="status">
            <el-radio-group v-model="item.status">
              <el-radio
                v-for="item in $dict('OpenCloseEnum')"
                :key="item.code"
                :label="item.code">{{ item.desc }}
              </el-radio>
            </el-radio-group>
            <span
              class="merchant-balance"
              v-if="(actionType == 'VIEW') && item.status == '100'">余额{{ balance[($dictCode('ChannelTypeEnum', item.channelType).flag).toLowerCase() + 'Amount'] | moneyFormat }}</span>
          </el-form-item>
          <template v-if="item.status == '100'">
            <el-form-item
              :label="$dictCode('ChannelTypeEnum', Number(item.channelType)).desc + '通道'"
              prop="channelMchNo">
              <el-input
                class="el-input-inline"
                v-if="actionType == 'VIEW'"
                v-model="item.payChannelName"></el-input>
              <template v-else>
                <el-select
                  clearable
                  v-model="item.payChannelNo"
                  @change="selectChannel($event, index)"
                >
                  <el-option
                    v-for="channel in channelList[item.channelType]"
                    :key="channel.payChannelNo"
                    :value="channel.payChannelNo"
                    :label="channel.payChannelName"
                  ></el-option>
                </el-select>
                <template v-if="actionType == 'REPORT'">
                  <el-button
                    :disabled="channelMap[item.channelType].btnContext !== '报备'"
                    :type="channelMap[item.channelType].btnContext == '报备' ? 'primary' : 'info'"
                    @click="submitChannel(item.channelType, item.payChannelNo, item.payChannelName)"
                    style="margin-left: 3px"
                  >
                    {{ channelMap[item.channelType].btnContext }}
                  </el-button>
                  <el-button
                    v-if="item.payChannelNo === 'JOINPAY' || item.payChannelNo === 'JOINPAY_JXH'"
                    type="primary"
                    @click="syncJoinpayInfo(item.payChannelNo)"
                  >
                    同步
                  </el-button>
                </template>
                <el-button
                  v-if="actionType == 'REPORT' && item.payChannelNo == 'ALIPAY'"
                  @click="unsign(item)"
                >
                  解除签约
                </el-button>

                <el-button
                  v-if="item.channelType == 1 && item.payChannelNo === 'JOINPAY'"
                  type="primary"
                  @click="cheackMchSignAndPicUpload"
                >校验签约与图片
                </el-button>

                <el-button
                  v-if="item.channelType == 1 && item.payChannelNo === 'JOINPAY'"
                  type="primary"
                  @click="uploadPic"
                >上传分账方图片
                </el-button>
              </template>

            </el-form-item>
            <el-form-item
              v-if="item.payChannelNo === 'ALIPAY'"
              label="出款户名："
              prop="employerName">
              <el-input
                v-model="item.employerName"
                clearable></el-input>
            </el-form-item>
            <el-form-item
              :label="item.payChannelNo === 'JOINPAY_JXH' ? '虚拟收款账号' : '子商户编号'"
              prop="channelKey">
              <el-input v-model="item.subMerchantNo"></el-input>
            </el-form-item>
            <el-form-item
              label="父商户编号"
              prop="channelKey">
              <el-input v-model="item.parentMerchantNo"></el-input>
            </el-form-item>
            <el-form-item
              label="通道密钥"
              prop="channelKey">
              <el-input v-model="item.employerKey"></el-input>
            </el-form-item>
          </template>
        </div>
      </el-form>
      <div class="form-btn-group">
        <el-button
          :loading="loading"
          type="primary"
          @click="doSubmit"
          v-if="actionType == 'EDIT'">提交
        </el-button>
        <el-button @click="closeForm">取消</el-button>
      </div>
    </div>

    <el-dialog title="校验结果" :visible.sync="dialogFormVisible" style="width: 80%">
      <el-card >
        <div>
          <span>图片审核状态：{{checkAltMchResult.altMchPicUploadInfo? checkAltMchResult.altMchPicUploadInfo.approveStatusDesc:null}}</span>
        </div>
        <div>
          <span>图片审核备注：{{checkAltMchResult.altMchPicUploadInfo? checkAltMchResult.altMchPicUploadInfo.approveNote:null}}</span>
        </div>
        <div>
          <span style="color: red">错误描述：{{checkAltMchResult.altMchPicUploadInfo? checkAltMchResult.altMchPicUploadInfo.errorMsg:null}}</span>
        </div>
      </el-card>

      <el-card style="margin-top: 20rpx">
        <div>
          <span>协议签约状态：{{checkAltMchResult.altMchSignInfo?
            checkAltMchResult.altMchSignInfo.altMchSignStatus=="100"?"签约成功":"签约失败"
            :null}}</span>
        </div>
        <div>
          <span>协议签约备注：{{checkAltMchResult.altMchSignInfo? checkAltMchResult.altMchSignInfo.errorMsg:null}}</span>
        </div>
      </el-card>
    </el-dialog>
  </div>
</template>

<script>
import {
  getPayChannelList,
  getSingerMerchantPayAccount,
  submitChannel,
  updateMerchantPayAccount,
  getBalance,
  unsign, syncJoinpayInfo,
  uploadPic,
  cheackMchSignAndPicUpload
} from '@/api/levy'

export default {
  data() {
    let channelMap = {}
    this.$dict('ChannelTypeEnum').forEach(item => {
      channelMap[item.code] = {
        value: item,
        btnContext: '报备'
      }
    })


    return {
      loading: false,
      show: false,
      form: {
        mchName: '',
        employerNo: '',
        mainstayName: '',
        mainstayNo: '',
        channelInfos: [{}, {}, {}]
      },
      channelList: [[], [], [], []],
      pageInfo: {},
      rules: {
        productName: [{ required: true, message: '请输入行业类型名称', trigger: 'blur' }],
        productNo: [{ required: true, message: '请输入行业类型编号', trigger: 'blur' }],
        productType: [{ required: true, message: '请选择父行业类型', trigger: 'blur' }],
        productStatus: [{ required: true, message: '请选择父行业类型', trigger: 'blur' }],
      },
      balance: {},

      showSuccess: false,
      channelMap,
      dialogFormVisible:false,
      checkAltMchResult:{}
    }
  },
  computed: {
    actionType() {
      return this.$route.query.actionType;
    },
    employerNo() {
      return this.$route.query.employerNo;
    },
    mainstayNo() {
      return this.$route.query.mainstayNo;
    },
  },
  mounted() {
    this.getPayChannelList();
    if (this.employerNo) {
      this.getPageInfo();
    }
  },
  methods: {
    selectMerchant(val) {
      this.form.employerNo = val[0];
      this.form.employerName = val[1];
    },
    selectMainstay(val) {
      this.form.mainstayNo = val[0];
      this.form.mainstayName = val[1];
    },
    selectChannel(val, index) {
      this.$set(this.form.channelInfos[index], 'payChannelNo', val)
      let item = this.form.channelInfos[index];
      for (let i = 0; i < this.channelList[item.channelType].length; i++) {
        if (val === this.channelList[item.channelType][i].payChannelNo) {
          this.$set(this.form.channelInfos[index], 'payChannelName', this.channelList[item.channelType][i].payChannelName)
          break;
        }
      }
    },
    getPayChannelList() {
      getPayChannelList({
        pageSize: 1000,
        pageCurrent: 1
      }).then(res => {
        let data = res.data.data;
        for (let item of data) {
          item.channelType && item.channelType.forEach(i => {
            this.channelList[i].push(item)
          })
        }
      })
    },
    getPageInfo() {
      getSingerMerchantPayAccount({
        mainstayNo: this.mainstayNo,
        employerNo: this.employerNo
      }).then(res => {
        res.data.channelInfos.forEach(item => {
          item.status += '';
        })
        this.$set(this, 'form', res.data);
        this.$delete(this.form, 'employerName')
      })
    },
    getBalance() {
      getBalance({
        mainstayNo: this.mainstayNo,
        employerNo: this.employerNo,
      }).then(({ data }) => {
        let balanceData = {};
        for (let item in data) {
          if (isNaN(parseInt(data[item]))) {
            this.$message.error(data[item])
          } else {
            balanceData[item] = data[item];
          }
        }
        this.balance = balanceData;
      })
    },
    submitChannel(channelType, payChannelNo, payChannelName) {
      submitChannel({
        employerNo: this.form.employerNo,
        mchName: this.form.mchName,
        mainstayNo: this.form.mainstayNo,
        mainstayName: this.form.mainstayName,
        payChannelNo,
        payChannelName,
        channelType,
        merchantType: '100',
      }).then(res => {
        this.showSuccess = true;
        this.setLoading(channelType);
      })
    },
    doSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.doEdit();
        }
      })
    },
    doEdit() {
      updateMerchantPayAccount(this.form).then(response => {
        this.$message.success('修改成功')
        this.closeForm()
        this.$emit('success')
      }).finally(() => this.loading = false)
    },
    closeForm() {
      this.show = false
      this.loading = false
      this.form = {}
      this.$refs.form.resetFields();
      this.$router.push('/business/merchantPayAccount');
    },
    setLoading(type) {
      this.setLoadingContext(type, 59);
    },
    setLoadingContext(type, sec) {
      if (sec <= 0) {
        clearTimeout(this[type + 'BtnTimer'])
        this.channelMap[type].btnContext = '报备';
      } else {
        this.channelMap[type].btnContext = sec + 's';
        setTimeout(() => {
          this.setLoadingContext(type, --sec)
        }, 1000)
      }
    },
    async unsign(info) {
      const { data } = await unsign({
        employerNo: this.form.employerNo,
        employerName: this.form.employerName,
        mainstayNo: this.form.mainstayNo,
        mainstayName: this.form.mainstayName,
        payChannelNo: info.payChannelNo,
        payChannelName: info.payChannelName,
        merchantType: 100,
        channelType: info.channelType,
      });
      data && this.$message.success(data);
      this.getPageInfo();
    },
    async syncJoinpayInfo(payChannelNo) {
      const msg = payChannelNo === 'JOINPAY_JXH' ? '确定将用工企业名设置为出款账户名并同步到君享汇通道吗？' : '同步用工企业信息到汇聚通道吗？'
      await this.$confirm(msg, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        await syncJoinpayInfo({
          mainstayNo: this.form.mainstayNo,
          employerNo: this.form.employerNo,
          payChannelNo: payChannelNo
        })
        this.$message.success('操作成功')
      })
    },
    async uploadPic() {
      const { employerNo, mainstayNo } = this.form
      const { data } = await uploadPic({
        employerNo,
        mainstayNo,
      })
      data && this.$message.success(data)
    },
    async cheackMchSignAndPicUpload() {
      const { employerNo, mainstayNo } = this.form
      const { data } = await cheackMchSignAndPicUpload({
        employerNo,
        mainstayNo,
      })

      if (data) {
        this.checkAltMchResult=data
        this.dialogFormVisible=true
      }
    }
  }
}
</script>

<style
  lang="scss"
  scoped>
.merchant-payAccount-form {
  padding: 50px;
  background: #fff;

  .form-item-inline {
    width: 500px;
  }

  .merchant-balance {
    margin-left: 20px;
    color: $errorColor;
  }
}

.success-block {
  position: relative;
  margin-bottom: 24px;
  padding: 24px 24px 24px 60px;
  border: 1px solid #008000;
  border-radius: 5px;
  background: #F6FFED;

  .success-img {
    position: absolute;
    top: 12px;
    left: 12px;
    width: 30px;
  }

  .success-content {
    margin-top: 12px;
  }

  .el-icon-close {
    position: absolute;
    right: 12px;
    top: 12px;
    z-index: 10;
  }

}

.topBottom-enter, .topBottom-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.topBottom-enter-active, .topBottom-leave-active {
  transition: all linear .1s;
}
</style>
