<template>
  <el-dialog
    :visible.sync="show"
    :close-on-click-modal="false"
    :before-close="closeForm"
    :title="(actionType==='ADD'&&'新增支付通道') || (actionType==='EDIT'&&'编辑产品及功能')||''"
    width="600px">
    <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
      <el-form-item label="支付通道编号" prop="payChannelNo">
        <el-input v-model="form.payChannelNo" :disabled="actionType==='EDIT'"/>
      </el-form-item>
      <el-form-item label="支付通道名称" prop="payChannelName">
        <el-input v-model="form.payChannelName" :disabled="actionType==='EDIT'"/>
      </el-form-item>
      <el-form-item label="通道类型" prop="channelTypeList">
        <el-checkbox-group v-model="form.channelTypeList">
          <el-checkbox
            v-for="item in $dict('ChannelTypeEnum')"
            :key="item.code"
            :label="Number(item.code)"
          >{{item.desc}}</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio v-for="item in $dict('OpenCloseEnum')" :key="item.code" :label="item.code">{{item.desc}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="description">
        <el-input type="textarea" v-model="form.description">
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="closeForm">取消</el-button>
      <el-button :loading="loading" type="primary" @click="doSubmit">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>

  import { createPayChannel, updatePayChannel } from '../../../api/levy'

  export default {
    data() {
      return {
        loading: false,
        show: false,
        actionType: undefined,
        form: {
          payChannelName: '',
          payChannelNo: '',
          channelTypeList: [],
          status: '',
          description: ''
        },
        invoiceCategoryList: [],
        rules: {
          payChannelName: [{required: true, message: '请输入支付通道名称', trigger: 'blur'}],
          payChannelNo: [{required: true, message: '请输入支付通道编号', trigger: 'blur'}],
          channelTypeList: [{required: true, message: '请选择通道类型', trigger: 'blur'}],
          status: [{required: true, message: '请选择状态', trigger: 'blur'}],
        }
      }
    },
    methods: {
      doSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.loading = true;
            this.actionType === 'ADD' && this.doAdd();
            this.actionType === 'EDIT' && this.doEdit();
          }
        })
      },
      doAdd() {
        createPayChannel(this.form).then(response => {
          this.$message.success('创建成功');
          this.closeForm()
          this.$emit('success')
        }).finally(() => this.loading = false)
      },
      doEdit() {
        updatePayChannel(this.form).then(response => {
          this.$message.success('修改成功')
          this.closeForm()
          this.$emit('success')
        }).finally(() => this.loading = false)
      },
      closeForm() {
        this.show = false;
        this.loading = false;
        this.$refs.form.resetFields();
        this.form = {
          payChannelName: '',
          payChannelNo: '',
          channelTypeList: [],
          status: '',
          description: ''
        }
      }
    }
  }
</script>
