<template>
  <el-main>
    <div class="header-block">
      <div class="header-container header-overview">
        <div class="header-title">资产总览</div>
        <el-divider></el-divider>
        <div class="header-content">
          <div>总可用金额（元）</div>
          <div class="account-balance">{{ totalAmount | moneyFormat }}</div>
        </div>
      </div>
      <div class="header-container header-account">
        <div class="header-title">账户信息</div>
        <el-divider></el-divider>
        <div class="account-content-mch">
          <p>商户名：{{ mainstayName }}</p>
        </div>
      </div>
    </div>

    <div class="content-container">
      <p class="title-container">账户余额查询</p>
      <el-select
        v-model="channelType"
        clearable
        @change="changeChannelType"
      >
        <el-option
          v-for="(item, index) in searchList"
          :key="index"
          :value="item.code"
          :label="item.desc"
        ></el-option>
      </el-select>
      <el-table :data="list">
        <el-table-column
          label="通道"
          width="200"
        >
          <template v-slot="{row}">
            {{ row.payChannelName }}
          </template>
        </el-table-column>

        <el-table-column
          label="可用余额（元）"
          width="250"
        >
          <template v-slot="{row}">
            <p class="color-warning">
              {{ row['balance'] | moneyFormat }}
            </p>
          </template>
        </el-table-column>

        <el-table-column
          label="不可用余额（元）"
          width="250"
        >
          <template v-slot="{row}">
            <p class="color-warning">
              {{ row['frozenAmount'] | moneyFormat }}
            </p>
          </template>
        </el-table-column>

        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button
              v-if="row.payChannelNo === 'JOINPAY' && $store.state.user.userData.type == 1"
              type="text"
              class="red-btn"
              @click="withdrawAll(row)">一键提取</el-button>

            <!-- <el-button
              v-if="row.payChannelNo === 'JOINPAY'"
              type="text"
              class="red-btn"
              @click="handleSync(row)">同步</el-button> -->

            <template v-if="row.payChannelNo === 'WXPAY'">
              <el-button
                v-if="row.balance !== '未开通'"
                v-permission="'wx:mainstay:balance:adjustment'"
                type="text"
                @click="wechatChange(row)">微信调账</el-button>
              <el-button
                v-else
                type="text"
                disabled
              >未开通微信通道</el-button>
            </template>

            <template v-if="row.payChannelNo === 'JOINPAY_JXH'">
              <el-button
                v-if="row.balance !== '未开通'"
                v-permission="'ac:mainstay:balance:adjustment'"
                type="text"
                @click="junxianghuiChange(row)">君享汇调账</el-button>
              <el-button
                v-else
                type="text"
                disabled
              >未开通君享汇通道</el-button>
            </template>

            <template v-if="row.payChannelNo === 'CMB'">
              <el-button
                v-if="row.balance !== '未开通'"
                v-permission="'cmb:mainstay:balance:adjustment'"
                type="text"
                @click="cmbChange(row)">招行通道调账</el-button>
              <el-button
                v-else
                type="text"
                disabled
              >未开通招行通道</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>


    <!-- 微信调账弹窗 -->
    <el-dialog
      title="微信调账"
      :visible="showDialog"
      :before-close="() => showDialog = false"
      :close-on-click-modal="false"
    >
      <el-form
        :model="wxAdjustForm"
        label-width="80px"
        :rules="rules"
        ref="form"
      >
        <el-form-item
          label="供应商"
          prop="mainstayName"
        >
          <el-input
            v-model="wxAdjustForm.mainstayName"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item
          label="调账方式"
          prop="type"
        >
          <el-select
            clearable
            v-model="wxAdjustForm.type"
          >
            <el-option
              v-for="(item, index) in $dict('AdjustmentEnum')"
              :key="index"
              :label="item.desc"
              :value="Number(item.code)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="金额"
          prop="amount"
        >
          <el-input
            clearable
            :value="wxAdjustForm.amount"
            @input="handleRateInput(wxAdjustForm, 'amount', $event, {
              len: 13,
              decLen: 2,
            })"
          ></el-input>
        </el-form-item>
      </el-form>

      <template v-slot:footer>
        <el-button
          type="primary"
          @click="confirmWxAdjust"
        >确认
        </el-button>
        <el-button @click="showDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 君享汇调账弹窗 -->
    <el-dialog
      title="君享汇调账"
      :visible="jxhShowDialog"
      :before-close="() => jxhShowDialog = false"
      :close-on-click-modal="false"
    >
      <el-form
        :model="jxhAdjustForm"
        label-width="80px"
        :rules="rules"
        ref="jxhForm"
      >
        <el-form-item
          label="供应商"
          prop="mainstayNo"
        >
          <el-input
            v-model="jxhAdjustForm.mainstayNo"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item
          label="调账方式"
          prop="type"
        >
          <el-select
            clearable
            v-model="jxhAdjustForm.type"
          >
            <el-option
              v-for="(item, index) in $dict('AdjustmentEnum')"
              :key="index"
              :label="item.desc"
              :value="Number(item.code)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="金额"
          prop="amount"
        >
          <el-input
            clearable
            :value="jxhAdjustForm.amount"
            @input="handleRateInput(jxhAdjustForm, 'amount', $event, {
              len: 13,
              decLen: 2,
            })"
          ></el-input>
        </el-form-item>
      </el-form>

      <template v-slot:footer>
        <el-button
          type="primary"
          @click="confirmJXHAdjust"
        >确认
        </el-button>
        <el-button @click="jxhShowDialog = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 招行通道调账弹窗 -->
    <el-dialog
      title="招行通道调账"
      :visible="cmbShowDialog"
      :before-close="() => cmbShowDialog = false"
      :close-on-click-modal="false"
    >
      <el-form
        :model="cmbAdjustForm"
        label-width="80px"
        :rules="rules"
        ref="cmbForm"
      >
        <el-form-item
          label="供应商"
          prop="mainstayName"
        >
          <el-input
            v-model="cmbAdjustForm.mainstayName"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item
          label="调账方式"
          prop="type"
        >
          <el-select
            clearable
            v-model="cmbAdjustForm.type"
          >
            <el-option
              v-for="(item, index) in $dict('AdjustAccountTypeEnum')"
              :key="index"
              :label="item.desc"
              :value="Number(item.code)"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="金额"
          prop="amount"
        >
          <el-input
            clearable
            :value="cmbAdjustForm.amount"
            @input="handleRateInput(cmbAdjustForm, 'amount', $event, {
              len: 13,
              decLen: 2,
            })"
          ></el-input>
        </el-form-item>
      </el-form>

      <template v-slot:footer>
        <el-button
          type="primary"
          @click="confirmCmbAdjust"
        >确认
        </el-button>
        <el-button @click="cmbShowDialog = false">取消</el-button>
      </template>
    </el-dialog>
  </el-main>
</template>

<script>
import { checkSupplierBalance, withdrawAll, mainstayBalanceSyncOne } from '@/api/supplier';
import { toPromise } from '@/utils'
import { wxMerchantBalance_mainstayAdjustment, acMerchantBalance_mainstayAdjustment, cmbMerchantBalance_mainstayAdjustment } from '@/api/wechat'

export default {
  name: 'CheckSupplierBalance',
  data() {
    return {
      channelType: '',
      totalAmount: 0,

      list: [],
      originList: [],

      searchList: [],


      rules: {
        mchName: [{ required: true, message: "请输入商户账户", trigger: "blur" }],
        mainstayName: [{ required: true, message: "请输入供应商账户", trigger: "blur" }],
        type: [{ required: true, message: "请选择调账方式", trigger: "change" }],
        amount: [
          { required: true, message: "请输入金额", trigger: "blur" },
          {
            validator: (rule, val, cb) => {
              if (val == 0) {
                return cb(new Error('金额不能为0'))
              }
              cb()
            }, trigger: 'blur'
          }
        ],
      },

      showDialog: false,
      wxAdjustForm: {},

      jxhShowDialog: false,
      jxhAdjustForm: {},

      cmbShowDialog: false,
      cmbAdjustForm: {}
    }
  },
  computed: {
    mainstayNo() {
      return this.$route.query.mainstayNo || ''
    },
    mainstayName() {
      return this.$route.query.mainstayName || ''
    },
  },
  async mounted() {
    this.checkSupplierBalance();
  },
  methods: {
    // 查询通道下的账户信息
    async checkSupplierBalance() {
      const { data } = await checkSupplierBalance({ mainstayNo: this.mainstayNo });
      this.list = this.originList = data;

      let totalAmount = 0
      this.list.forEach((i, key) => {
        this.searchList.push({
          code: key,
          desc: i.payChannelName
        })

        let balance = isNaN(i.balance) ? 0 : i.balance;
        let frozenAmount = isNaN(i.frozenAmount) ? 0 : i.frozenAmount;
        totalAmount = (totalAmount * 100 + balance * 100 + frozenAmount * 100) / 100;
      })
      this.totalAmount = totalAmount
    },
    changeChannelType(val) {
      if (val === undefined || val === '') {
        this.list = this.originList;
        return;
      }
      this.list = this.originList.filter((item, index) => {
        return index == val
      })
    },
    async withdrawAll({ mainstayNo }) {
      const [err] = await toPromise(this.$confirm(
        '是否确认提取余额',
        '提示',
        {
          type: 'warning'
        }
      ))
      if (err) return;
      const { data } = await withdrawAll({ mainstayNo })
      data && this.$message.success(data)
    },

    // 同步汇聚供应商余额
    async handleSync({ mainstayNo }) {
      await mainstayBalanceSyncOne(mainstayNo)
      this.checkSupplierBalance()
    },

    // 微信调账弹窗
    async wechatChange(row) {
      this.editRow = JSON.parse(JSON.stringify(row))
      this.showDialog = true
      this.wxAdjustForm = {
        // mchNo: this.merchantInfo.mchNo,
        // mchName: this.merchantInfo.mchName,
        // mainstayNo: this.editRow.mainstayNo,
        // mainstayName: this.editRow.mainstayName,

        mainstayNo: this.mainstayNo,
        mainstayName: this.mainstayName,
        amount: 0,
        type: ''
      }
      await this.$nextTick()
      this.$refs.form.resetFields()
    },
    // 微信调账
    async confirmWxAdjust() {
      try {
        await this.$refs.form.validate()
      } catch {
        return;
      }
      try {
        await this.$confirm('此操作将直接改动账户资金情况，请谨慎操作！ 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch {
        return
      }
      this.wxAdjustForm.amount = Number(this.wxAdjustForm.amount)
      const { data } = await wxMerchantBalance_mainstayAdjustment(this.wxAdjustForm)
      data && this.$message.success(data)
      this.showDialog = false
      this.checkSupplierBalance()
    },

    // 君享汇调账弹窗
    async junxianghuiChange(row) {
      this.editRow = JSON.parse(JSON.stringify(row))
      this.jxhShowDialog = true
      this.jxhAdjustForm = {
        // mchNo: this.merchantInfo.mchNo,
        // mchName: this.merchantInfo.mchName,
        // mainstayNo: this.editRow.mainstayNo,
        // mainstayName: this.editRow.mainstayName,

        mainstayNo: this.mainstayNo,
        mainstayName: this.mainstayName,

        amount: 0,
        type: ''
      }
      await this.$nextTick()
      this.$refs.jxhForm.resetFields()
    },
    // 君享汇调账
    async confirmJXHAdjust() {
      try {
        await this.$refs.jxhForm.validate()
      } catch {
        return;
      }
      try {
        await this.$confirm('此操作将直接改动账户资金情况，请谨慎操作！ 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch {
        return
      }
      this.jxhAdjustForm.amount = Number(this.jxhAdjustForm.amount)
      const { data } = await acMerchantBalance_mainstayAdjustment(this.jxhAdjustForm)
      data && this.$message.success(data)
      this.jxhShowDialog = false
      this.checkSupplierBalance()
    },
    // 招行通道调账弹窗
    async cmbChange(row) {
      this.editRow = JSON.parse(JSON.stringify(row));
      this.cmbShowDialog = true;
      this.cmbAdjustForm = {
        mainstayNo: this.mainstayNo,
        mainstayName: this.mainstayName,
        amount: 0,
        type: ''
      };
      await this.$nextTick();
      this.$refs.cmbForm.resetFields();
    },
    // 招行通道调账
    async confirmCmbAdjust() {
      try {
        await this.$refs.cmbForm.validate();
      } catch {
        return;
      }
      try {
        await this.$confirm('此操作将直接改动账户资金情况，请谨慎操作！ 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch {
        return;
      }
      this.cmbAdjustForm.amount = Number(this.cmbAdjustForm.amount);
      const { data } = await cmbMerchantBalance_mainstayAdjustment(this.cmbAdjustForm);
      data && this.$message.success(data)
      this.cmbShowDialog = false;
      this.checkSupplierBalance();
    }
  },
}
</script>

<style scoped lang="scss">
.title-container {
  font-size: 25px;
}
.header-block {
  display: flex;
}
.header-container {
  background: #fff;

  .header-title {
    padding: 24px 24px 0 24px;
    font-size: 20px;
    font-weight: bolder;
  }

  .header-content {
    padding: 0 24px 40px;
  }

  &.header-overview {
    flex: 0.7;
  }
  &.header-account {
    flex: 0.3;
    margin-left: 16px;
  }
}

.header-account {
  .account-content-mch {
    padding: 0 24px;
  }

  .account-content {
    display: flex;
  }
  .account-content-item {
    flex: 1;
    width: calc(100% / 3);
    margin-bottom: 8px;
  }
  .account-content-icon {
    display: inline-block;
    background: $mainColor;
    margin-bottom: 4px;
    border-radius: 8px;

    .svg-icon {
      width: 50px;
      height: 45px;
      padding: 5px;
    }
  }
}
.header-overview {
  .header-btn {
    padding: 0 24px 24px;
  }
}
.content-container {
  margin-top: 32px;

  .el-table {
    margin-top: 16px;
  }
}
.account-balance {
  font-size: 30px;
  color: #ffa500;
}
</style>
