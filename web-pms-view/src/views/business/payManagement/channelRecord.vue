<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">报备状态：</span>
            <el-select clearable v-model="form.status">
              <el-option v-for="item in $dict('ReportStatusEnum')" :key="item.code" :value="item.code" :label="item.desc"></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label middle">报备流水号：</span>
            <el-input v-model="form.serialNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label middle">支付通道编号：</span>
            <el-input v-model="form.payChannelNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">用工企业名称：</span>
            <el-input v-model="form.employerNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label middle">用工企业编号：</span>
            <el-input v-model="form.employerNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">供应商名称：</span>
            <el-input v-model="form.mainstayNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label middle">供应商商户编号：</span>
            <el-input v-model="form.mainstayNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker ref="datePicker" v-model="timeRange" @change="getTimeRange" fastTime="today"></date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button type="primary" @click="getChannelRecordList(true)">查询</el-button>
            <el-button type="text" @click="resetForm">清空筛选条件</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table class="content-main" :data="payChannelObj.data">
        <el-table-column type="index" :index="getIndex" label="序号"/>
        <el-table-column label="创建时间" prop="createTime" width="120">
          <p slot-scope="{row, column}" v-html="renderTime(row[column['property']])"></p>
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime" width="120">
          <p slot-scope="{row, column}" v-html="renderTime(row[column['property']])"></p>
        </el-table-column>
        <el-table-column label="报备流水号" prop="serialNo">

        </el-table-column>
        <el-table-column label="所属供应商" prop="status" width="150">
          <template slot-scope="scope">
            {{ scope.row.mainstayNo }} <br>
            {{ scope.row.mainstayName }}
          </template>
        </el-table-column>
        <el-table-column label="用工企业名称" prop="description" width="150">
          <template slot-scope="scope">
            {{ scope.row.employerNo }} <br>
            {{ scope.row.employerName }}
          </template>
        </el-table-column>
        <el-table-column label="支付通道编号" prop="payChannelNo">
        </el-table-column>
        <el-table-column label="支付通道名称" prop="payChannelName">

        </el-table-column>
        <el-table-column label="通道类型" prop="channelType">
          <template slot-scope="scope">
            {{ $dictCode('ChannelTypeEnum', scope.row.channelType).desc }}
          </template>
        </el-table-column>
        <el-table-column label="报备状态" prop="status">
          <template slot-scope="scope">
            {{ $dictCode('ReportStatusEnum', scope.row.status).desc }}
          </template>
        </el-table-column>
        <el-table-column label="通道返回信息" prop="respData" width="200"></el-table-column>

        <el-table-column label="错误信息" prop="errMsg" width="150"></el-table-column>

        <el-table-column label="操作" fixed="right">
          <template v-slot="{row}">
            <el-button type="text" v-if="row.status == 101">重新报备</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="payChannelObj"
          ref="pagination"
          :total="payChannelObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
  import { getChannelRecordList, submitChannel } from '@/api/levy'

  export default {
    name: 'PmsChannelRecord',
    data() {
      return {
        form: {
          mainstayNo: '',
          mainstayNameLike: '',
          employerNo: '',
          employerNameLike: '',
          payChannelNo: '',
          payChannelName: '',
          status: '',
          beginTime: '',
          endTime: ''
        },
        timeRange: [],
        pageCurrent: 1,
        pageSize: 10,
        payChannelObj: {
          data: [{}]
        }
      }
    },
    mounted() {
      this.getChannelRecordList()
    },
    methods: {
      getChannelRecordList(initFlag) {
        if (initFlag === true) {
          this.pageCurrent = 1;
        }
        getChannelRecordList({
          ...this.form,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        }).then(response => {
          this.payChannelObj = response.data;
        })
      },
      getTimeRange(val) {
        this.timeRange = val
        this.form.beginDate = val && val[0]
        this.form.endDate = val && val[1]
      },
      resetForm() {
        this.form = {
          payChannelNo: '',
          payChannelName: '',
          channelType: ''
        }
        this.$refs.datePicker.resetTime();
      },
      handleCurrentChange(val) {
        this.pageCurrent = val
        this.getChannelRecordList()
      },
      handleSizeChange(val) {
        this.pageSize = val
        this.getChannelRecordList(true)
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
      async submitChannel(row) {
        await submitChannel({
          employerNo: row.employerNo,
          employerName: row.employerName,
          mainstayNo: row.mainstayNo,
          mainstayName: row.mainstayName,
          payChannelNo: row.payChannelNo,
          payChannelName: row.payChannelName,
          channelType: row.channelType,
          merchantType: row.employerNo ? '100' : '101',
        })
      }
    }
  }
</script>

<style scoped>

</style>

