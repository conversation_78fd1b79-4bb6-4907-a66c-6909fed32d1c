<template>
  <el-dialog :visible.sync="show" :close-on-click-modal="false" :before-close="closeForm" :title="(actionType==='ADD'&&'新建代征关系') ||''"
             width="600px">
    <el-form ref="form" :model="form" :rules="rules" size="small" label-width="120px">
      <el-form-item label="用工企业" prop="productName">
        <el-select clearable filterable v-model="form.employerName" @change="selectMerchant" :disabled="actionType === 'EDIT'">
          <el-option v-for="item in merchantList" :key="item.mchNo" :value="[item.mchNo, item.mchName]" :label="item.mchName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="所属供应商" prop="productNo">
        <el-select clearable filterable v-model="form.mainstayName" @change="selectMainstay" :disabled="actionType === 'EDIT'">
          <el-option v-for="item in mainstayList" :key="item.mchNo" :value="[item.mchNo, item.mchName]" :label="item.mchName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio v-for="item in $dict('OpenCloseEnum')" :key="item.code" :label="item.code">{{item.desc}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="外部接口" prop="hasExternalSystem">
        <el-radio-group v-model="form.hasExternalSystem">
          <el-radio v-for="item in $dict('TrueFalseEnum')" :key="item.code" :label="item.code === 'true'">{{item.desc}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="外部接口密码" prop="externalPassword" v-if="form.hasExternalSystem">
        <el-input
          :readonly="readonly"
          type="password"
          v-model="form.externalPassword"
          show-password
          autocomplete="new-password"
          @focus="() => {readonly = false}"
        ></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="description">
          <el-input v-model="form.description"></el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="text" @click="closeForm">取消</el-button>
      <el-button :loading="loading" type="primary" @click="doSubmit">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>

  import { createLevyRelation, updateLevyRelation } from '../../../api/levy'
  import { getMainstayList, getMerchantList } from '../../../api/merchant'

  export default {
    data() {
      const self = this;
      return {
        loading: false,
        show: false,
        actionType: undefined,
        mainstayList: [],
        merchantList: [],
        readonly: true,
        form: {
          employerName: '',
          employerNo: '',
          mainstayName: '',
          mainstayNo: '',
          status: '',
          hasExternalSystem: false,
          externalPassword: '',
        },
        rules: {
          employerName: [{required: true, message: '请输入行业类型名称', trigger: 'blur'}],
          mainstayName: [{required: true, message: '请输入行业类型编号', trigger: 'blur'}],
          status: [{required: true, message: '请选择状态', trigger: 'blur'}],
          externalPassword: [{
            validator: (rule, val, cb) => {
              if (self.form.hasExternalSystem) {
                if (!self.form.externalPassword) {
                  cb(new Error('请输入外部地址接口密码'))
                } else {
                  cb()
                }
              } else {
                cb();
              }
            }, trigger: 'blur'
          }]
        }
      }
    },
    mounted(){
      getMerchantList({
        pageSize: 1000,
        pageCurrent: 1,
        merchantType: 100,
      }).then(res => {
        this.merchantList = res.data.data;
      })
      getMainstayList().then(res => {
        this.mainstayList = res.data;
      })
    },
    methods: {
      selectMerchant(value) {
        this.form.employerNo = value[0];
        this.form.employerName = value[1];
      },
      selectMainstay(value) {
        this.form.mainstayNo = value[0];
        this.form.mainstayName = value[1];
      },
      doSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.loading = true;
            this.readonly = true;
            this.actionType === 'ADD' && this.doAdd();
            this.actionType === 'EDIT' && this.doEdit();
          }
        })
      },
      doAdd() {
        createLevyRelation(this.form).then(response => {
          this.$message.success('创建成功');
          this.closeForm()
          this.$emit('success')
        }) .finally(() => this.loading = false)
      },
      doEdit() {
        updateLevyRelation(this.form).then(response => {
          this.$message.success('更新成功');
          this.closeForm();
          this.$emit('success');
        }).finally(() => this.loading = false)
      },
      closeForm() {
        this.show = false
        this.loading = false
        this.form = {
          employerName: '',
          employerNo: '',
          mainstayName: '',
          mainstayNo: '',
          status: '',
          hasExternalSystem: false,
          externalPassword: '',
        },
        this.$refs.form.resetFields()
      }
    }
  }
</script>
