<template>
  <div class="box-container">
    <el-button
      class="create-btn"
      type="primary"
      @click="showDialog('ADD')">新建
    </el-button>
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">企业名称：</span>
            <el-input v-model="form.employerNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label oversize">用工企业商户编号：</span>
            <el-input v-model="form.employerNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label oversize">状态：</span>
            <el-select
              clearable
              v-model="form.status">
              <el-option
                v-for="{code,desc} in $dict('OpenOffEnum')"
                :label="desc"
                :key="parseInt(code)"
                :value="parseInt(code)" />
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">供应商名称：</span>
            <el-input v-model="form.mainstayNameLike"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label oversize">供应商商户编号：</span>
            <el-input v-model="form.mainstayNo"></el-input>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="getLevyList(true)">查询
            </el-button>
            <el-button @click="exportData">导出</el-button>
            <el-button
              type="primary"
              :disabled="selection.length === 0"
              @click="multiOperate(100)">批量启用
            </el-button>
            <el-button
              type="danger"
              :disabled="selection.length === 0"
              @click="multiOperate(101)">批量禁用
            </el-button>
            <el-button
              type="text"
              @click="getExportList">查看已导出列表
            </el-button>
            <el-button
              type="text"
              @click="resetForm">清空筛选条件
            </el-button>
          </div>

        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table
        class="content-main"
        :data="levyObj.data"
        @selection-change="onSelectChange">
        <el-table-column type="selection"></el-table-column>
        <el-table-column
          type="index"
          :index="getIndex"
          label="序号" />
        <el-table-column
          label="用工企业名称/商户编号"
          prop="levyName">
          <template slot-scope="scope">
            {{ scope.row.employerNo }}<br>
            {{ scope.row.employerName }}
          </template>
        </el-table-column>
        <el-table-column
          label="所属供应商/商户编号"
          prop="levyNo">
          <template slot-scope="scope">
            {{ scope.row.mainstayNo }}<br>
            {{ scope.row.mainstayName }}
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          prop="description">
        </el-table-column>
        <el-table-column
          label="最后修改时间"
          prop="updateTime">
          <p
            slot-scope="{row, column}"
            v-html="renderTime(row[column['property']])"></p>
        </el-table-column>
        <el-table-column
          label="开户状态"
          prop="accountStatus">
          <template v-slot="{row}">
            <p>{{ $dictCode('MerchantChannelTypeEnum', row.accountStatus).desc }}</p>
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          prop="status">
          <template slot-scope="scope">
            {{ $dictCode('OpenCloseEnum', scope.row.status).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          prop="data">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="triggerStatus(scope.row)">{{ $dictCode('OpenCloseEnum', scope.row.status).desc == '启用' ? '禁用' : '启用' }}
            </el-button>
            <el-button
              type="text"
              @click="handleConfirm('删除', scope.row)">删除
            </el-button>
            <el-button
              type="text"
              @click="handleEdit(scope.row)">编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="levyObj"
          ref="pagination"
          :total="levyObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
      <ExportRecord ref="exportRecord"></ExportRecord>
      <levyForm
        ref="dialog"
        @success="getLevyList"></levyForm>
    </div>
  </div>
</template>

<script>
import ExportRecord from '@/components/ExportRecord'
import {
  changeLevyRelationStatus,
  deleteLevyRelation,
  getLevyList,
  exportEmployerMainstayRelation, batchUpdateLevy
} from '../../../api/levy'
import levyForm from './levyForm'
import { toPromise } from "@/utils";

export default {
  name: 'PmsLevyTable',
  components: {
    levyForm,
    ExportRecord
  },
  data() {
    return {
      form: {
        employerNo: '',
        employerNameLike: '',
        mainstayNo: '',
        mainstayNameLike: '',
        status: ''
      },
      pageCurrent: 1,
      pageSize: 10,
      levyObj: {
        data: []
      },
      selection: []
    }
  },
  mounted() {
    this.getLevyList();
  },
  methods: {
    getLevyList(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      getLevyList({
        ...this.form,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent
      }).then(response => {
        this.levyObj = response.data;
      })
    },
    resetForm() {
      this.form = {
        levyNo: '',
        levyName: '',
        levyType: '',
      }
    },
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getLevyList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getLevyList(true);
    },
    async exportData() {
      const { data } = await exportEmployerMainstayRelation(this.form);
      this.$message.success(data);
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(38);
    },
    showDialog(status, data) {
      this.$refs.dialog.show = true;
      this.$refs.dialog.actionType = status;
      data && (this.$refs.dialog.form = {
        id: data.id,
        employerName: data.employerName,
        employerNo: data.employerNo,
        mainstayName: data.mainstayName,
        mainstayNo: data.mainstayNo,
        status: data.status + '',
        version: data.version,
        description: data.description,
        hasExternalSystem: data.hasExternalSystem,
        externalPassword: data.externalPasswordDecrypt,
      });
    },
    triggerStatus(data) {
      this.$confirm(`确定要${ this.$dictCode('OpenCloseEnum', data.status).desc == '启用' ? '禁用' : '启用' }该条数据吗`, {
        type: 'warning'
      }).then(res => {
        changeLevyRelationStatus({
          id: data.id,
          status: this.$dictCode('OpenCloseEnum', data.status).desc == '启用' ? '101' : '100'
        }).then(response => {
          this.$message.success('修改成功');
          this.getLevyList();
        })
      }).catch(() => {
      })

    },
    handleConfirm(text, data) {
      this.$confirm(`确定要${ text }该条数据吗`, {
        type: 'warning'
      }).then(res => {
        deleteLevyRelation({
          id: data.id
        }).then(response => {
          this.$message.success('删除成功');
          this.getLevyList();
        })
      }).catch(() => {
      })
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    handleEdit(data) {
      this.showDialog('EDIT', data);
    },
    onSelectChange(e) {
      this.selection = e
    },
    async multiOperate(type) {
      const [err] = await toPromise(this.$confirm('是否确认批量操作？', '提示'))
      if (err) return
      const { data } = await batchUpdateLevy({
        status: type,
        ids: this.selection.map(i => i.id)
      })
      data && this.$message.success(data)
      this.getLevyList()
    }
  }
}
</script>

<style scoped>
</style>
