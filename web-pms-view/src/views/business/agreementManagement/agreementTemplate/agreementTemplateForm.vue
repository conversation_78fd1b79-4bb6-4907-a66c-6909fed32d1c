<template>
  <div class="page-container">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="small"
      label-width="140px">
      <p class="subTitle">
        基本信息
      </p>
      <el-form-item
        label="协议名称"
        prop="topic">
        <el-input v-model="form.topic" />
      </el-form-item>
      <p class="subTitle">
        上传文件
      </p>
      <el-form-item
        label="协议文件"
        prop="fileVoList">
        <el-upload
          class="upload-demo"
          :action="baseUrl + '/file/upload'"
          :headers="uploadHeader"
          accept=".doc,.docx"
          :file-list="fileList"
          list-type="picture-card"
          :before-upload="beforeAvatarUpload"
          :on-remove="handleRemove"
          :on-success="handSuccess">
          <div
            slot="file"
            slot-scope="{file}"
            class="fileBg">
            <span class="file-name">{{ file.name || file.fileName || '协议文件' }}</span>
            <span class="el-upload-list__item-actions">
              <span
                class="el-upload-list__item-delete"
                @click="handleFileRemove(file)"
              >
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </div>
          <i class="el-icon-plus avatar-uploader-icon"></i>
          <div
            slot="tip"
            class="el-upload__tip">
            如使用标准模板修改协议，请上传修订模式的协议，以便审核人了解修改点<br />
            只能上传doc/docx文件，且不超过15M
          </div>
        </el-upload>
      </el-form-item>
      <p class="subTitle">
        签署人
      </p>
      <el-table :data="form.signerVoList">
        <el-table-column label="签署人">
          <template slot-scope="scope">
            <div class="signer-row">
              <el-form-item
                :prop="'signerVoList.' + scope.$index + '.signerType'"
                :rules="{required: true, message: '签署人类型不能为空', trigger: 'change'}"
                label-width="10px"
                class="signer-type">
                <el-select
                  clearable
                  v-model="scope.row.signerType">
                  <el-option
                    v-for="item in $dict('AgreementSignerTypeEnum')"
                    :value="item.code"
                    :label="item.desc"
                    :key="item.code"></el-option>
                </el-select>
              </el-form-item>

              <span
                v-if="$dictCode('AgreementSignerTypeEnum', scope.row.signerType).desc == '使用模板时指定'"
                class="color-orange">使用模板时，该角色由发起人指定</span>
              <el-form-item
                v-if="$dictCode('AgreementSignerTypeEnum', scope.row.signerType).desc == '固定成员'"
                class="signer-name"
                label-width="0px"
                :prop="'signerVoList.' + scope.$index + '.signerName'"
                :rules="{required: true, message: '固定成员不能为空', trigger: 'change'}">
                <el-select
                  clearable
                  filterable
                  placeholder="选择企业"
                  v-model="scope.row.signerName"
                  @change="selectSinger($event, scope.$index)">
                  <el-option
                    v-for="item in activeMchList"
                    :key="item.mchNo"
                    :value="[item.mchNo, item.mchName]"
                    :label="item.mchName"></el-option>
                </el-select>
              </el-form-item>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          prop="data"
          width="100px">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="deleteRow(scope.row, scope.$index)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button
        class="add-btn"
        @click="addList">添加签署人
      </el-button>
      <p class="subTitle">
        其他
      </p>
      <el-form-item label="签署默认截止日期">
        <el-select
          clearable
          v-model="form.deadlineType">
          <el-option
            v-for="item in $dict('AgreementTempTimeTypeEnum')"
            :key="item.code"
            :value="item.code"
            :label="item.desc"></el-option>
        </el-select>
        <p class="color-gray">
          须在截止日期前完成签署归档
        </p>
      </el-form-item>
      <el-form-item label="协议默认到期日期">
        <el-select
          clearable
          v-model="form.expireTimeType">
          <el-option
            v-for="item in $dict('AgreementTempTimeTypeEnum')"
            :key="item.code"
            :value="item.code"
            :label="item.desc"></el-option>
        </el-select>
        <p class="color-gray">
          协议到期前，系统将提醒
        </p>
      </el-form-item>
    </el-form>
    <div class="form-btn-group">
      <el-button
        :loading="loading"
        type="primary"
        @click="doSubmit">保存
      </el-button>
      <el-button @click="$router.push('/business/agreementTemplate')">取消</el-button>
    </div>
  </div>
</template>

<script>
import { createAgreementTemplate, editAgreementTemplate, getAgreementTemplateById } from '../../../../api/agreement'
import { getActiveMchList } from '../../../../api/merchant'

const SIGNER = { // 签署人信息
  signerType: '',
  signerNo: '',
  signerName: ''
}
export default {
  data() {
    return {
      loading: false,
      show: false,
      actionType: undefined,
      fileList: [],
      form: {
        topic: '',
        signerVoList: [],
        fileVoList: [],
        expireTimeType: '100',
        deadlineType: '100'
      },
      rules: {
        topic: [{ required: true, message: '请输入协议名称', trigger: 'blur' }],
        fileVoList: [{ required: true, message: '请上传协议文件', trigger: 'blur' }],
      },
      activeMchList: [],
    }
  },

  computed: {
    templateID() {
      return this.$route.query.id;
    },
  },
  mounted() {
    this.init();
    this.getActiveMchList();
  },
  methods: {
    init() {
      if (this.templateID) {
        getAgreementTemplateById({
          id: this.templateID
        }).then(response => {
          let res = response.data;
          this.form.topic = res.topic;
          // el-select回显需要将number转为string
          for (let item of res.signerVoList) {
            item.signerType += '';
            this.form.signerVoList.push(item);
          }
          this.form.fileVoList = res.fileVoList;
          this.fileList = res.fileVoList;
          this.form.expireTimeType = res.expireTimeType + '';
          this.form.deadlineType = res.deadlineType + '';
        })
      }
    },
    getActiveMchList() {
      getActiveMchList().then(response => {
        this.activeMchList = response.data;
      })
    },
    selectSinger(val, index) {
      this.form.signerVoList[index].signerNo = val[0];
      this.form.signerVoList[index].signerName = val[1];
    },
    beforeAvatarUpload(file) {
      this.loading = true;
      const isTrueType = ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.type);
      const isLt15M = file.size / 1024 / 1024 < 15;

      if (!isTrueType) {
        this.loading = false;
        this.$message.error('上传文件只能是 doc 和 docx 格式!');
      }
      if (!isLt15M) {
        this.loading = false;
        this.$message.error('上传文件大小不能超过 15MB!');
      }
      return isTrueType && isLt15M;
    },
    handleRemove(file, fileList) {
      this.fileList = fileList;
      this.form.fileVoList = fileList;
      this.loading = false;
    },
    handSuccess(response, file, fileList) {
      this.fileList = fileList;
      this.form.fileVoList = fileList;
      this.loading = false;
    },
    // 增加一行签署人
    addList() {
      this.form.signerVoList.push({ ...SIGNER });
    },
    deleteRow(row, index) {
      this.form.signerVoList.splice(index, 1);
    },
    doSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.form.fileVoList = [];
          for (let item of this.fileList) {
            // 判断该文件是重新上传的还是原本有的
            if (item.response) {
              this.form.fileVoList.push({
                fileName: item.name,
                fileUrl: item.response.data
              })
            } else {
              this.form.fileVoList.push({
                fileName: item.fileName,
                fileUrl: item.fileUrl
              })
            }
          }
          this.loading = true
          this.templateID ? this.doEdit() : this.doAdd();
        }
      })
    },
    doAdd() {
      createAgreementTemplate(this.form).then((response) => {
        this.$message.success('创建成功');
        this.$router.push('/business/agreementTemplate');
      })
    },
    doEdit() {
      editAgreementTemplate({
        id: this.templateID,
        ...this.form
      }).then((response) => {
        this.$message.success('修改成功');
        this.$router.push('/business/agreementTemplate');
      }).finally(() => this.loading = false)
    },
    handleFileRemove(file) {
      let fileUrl = file.response ? file.response.data : file.fileUrl;
      this.fileList = this.fileList.filter(item => {
        let itemUrl = item.response ? item.response.data : item.fileUrl;
        return fileUrl !== itemUrl
      })
    }
  }
}
</script>

<style
  lang="scss"
  scoped>
.color-orange {
  color: rgb(245, 154, 35);
}

.page-container {
  .add-btn {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    border: 1px dotted #ccc;
    background: #fff;
    cursor: pointer;

    &:active {
      border: 1px dotted #ccc;
    }

    &:focus {
      border: 1px dotted #ccc;
      outline: none;
    }
  }

  .signer-row {
    display: flex;

    .el-form-item {
      margin-right: 8px;
    }
  }
}
</style>
