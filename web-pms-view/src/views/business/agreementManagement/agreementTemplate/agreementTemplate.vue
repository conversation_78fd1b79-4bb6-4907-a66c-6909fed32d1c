<template>
  <div class="box-container">
    <el-button
      class="create-btn"
      type="primary"
      @click="syncTemplate"
    >同步
    </el-button>
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <!--          <div class="flex-item">-->
          <!--            <span class="flex-item__label">签署人：</span>-->
          <!--            <el-select clearable v-model="form.signerNoList" multiple filterable>-->
          <!--              <el-option v-for="item in activeMchList" :key="item.mchNo" :value="item.mchNo" :label="item.mchName"></el-option>-->
          <!--            </el-select>-->
          <!--          </div>-->
          <div class="flex-item">
            <span class="flex-item__label">模板名称：</span>
            <el-input
              v-model="form.topicLike"
              clearable
            ></el-input>
          </div>
          <div class="flex-item"></div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-2 search-btn-group">
            <el-button
              type="primary"
              @click="getAgreementTemplateList(true)"
            >查询
            </el-button>
            <el-button
              @click="resetForm"
            >重置
            </el-button>
          </div>

        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table
        class="content-main"
        :data="templateObj.data"
      >
        <el-table-column
          label="序号"
          type="index"
          :index="getIndex"
        >
        </el-table-column>
        <el-table-column
          label="模板名称"
          prop="topic"
        ></el-table-column>
        <el-table-column
          label="流程模板ID"
          prop="flowTemplateId"
        ></el-table-column>
        <el-table-column
          label="文件模板ID"
          prop="fileTemplateId"
        ></el-table-column>
        <!--        <el-table-column-->
        <!--          label="签约默认截止日期"-->
        <!--          prop="deadlineType"-->
        <!--        >-->
        <!--          <template v-slot="scope">-->
        <!--            {{ $dictCode('AgreementTempTimeTypeEnum', scope.row.deadlineType).desc }}-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <!--        <el-table-column-->
        <!--          label="协议默认到期日期"-->
        <!--          prop="expireTimeType"-->
        <!--        >-->
        <!--          <template v-slot="scope">-->
        <!--            {{ $dictCode('AgreementTempTimeTypeEnum', scope.row.expireTimeType).desc }}-->
        <!--          </template>-->
        <!--        </el-table-column>-->
        <!--        <el-table-column-->
        <!--          label="签署人"-->
        <!--          prop="signerName"-->
        <!--        >-->
        <!--        </el-table-column>-->
        <el-table-column label="操作">
          <template v-slot="{ row }">
            <!--            <el-button type="text">预览</el-button>-->
            <!--            <el-button-->
            <!--              type="text"-->
            <!--              @click="handleEdit(scope.row)"-->
            <!--            >编辑-->
            <!--            </el-button>-->
            <el-button
              type="text"
              @click="downloadTemp(row)"
            >下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="templateObj"
          ref="pagination"
          :total="templateObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10,50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import {getAgreementTemplateList, syncTemplate} from '@/api/agreement'
import {getActiveMchList} from '@/api/merchant'

export default {
  name: 'PmsAgreementTemplate',
  data() {
    return {
      form: {
        // signerNoList: [],
        topicLike: '',
      },
      pageCurrent: 1,
      pageSize: 10,
      activeMchList: [],
      templateObj: {},
      isShowUploadDialog: false
    }
  },
  mounted() {
    // this.getActiveMchList();
    this.getAgreementTemplateList();
  },
  methods: {
    getActiveMchList() {
      getActiveMchList().then(response => {
        this.activeMchList = response.data;
      })
    },
    getAgreementTemplateList(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      getAgreementTemplateList({
        pageCurrent: this.pageCurrent,
        pageSize: this.pageSize,
        ...this.form
      }).then(response => {
        this.templateObj = response.data;
      });
    },
    resetForm() {
      this.form = {
        // signerNoList: [],
        topicLike: '',
      }
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.getAgreementTemplateList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getAgreementTemplateList(true);
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    handleEdit(data) {
      this.$router.push('/business/agreementTemplateForm?id=' + data.id);
    },
    // 同步
    async syncTemplate() {
      const {data} = await syncTemplate()
      data && this.$message.success(data)
    },
    async downloadTemp({templateFileUrl}) {
      const fileMsg = await this.formatFileMsg(templateFileUrl)
      const fileNameArr = templateFileUrl.split('.')
      this.downloadFile(fileMsg.fileUrl, '文件模板', fileNameArr[1])
    }
  }
}
</script>

<style scoped>
.create-btn {
  margin-bottom: 20px;
}
</style>
