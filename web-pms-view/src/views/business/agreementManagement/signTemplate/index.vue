<template>
  <div class="box-container">
    <el-button type="primary" class="create-btn" @click="createNew">新建</el-button>
    <div class="search-container flex-container">
      <div class="flex-wrapper search-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">签署人：</span>
          <el-select clearable v-model="searchForm.mchNo" multiple filterable>
            <el-option v-for="item in activeMchList" :key="item.mchNo" :value="item.mchNo" :label="item.mchName"></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">模板名称：</span>
          <el-input v-model="searchForm.templateName"></el-input>
        </div>
      </div>
      <div class="flex-wrapper search-wrapper">
        <div class="search-btn-group">
          <el-button type="primary" @click="search(true)">查询</el-button>
          <el-button type="text" @click="resetForm">清空筛选条件</el-button>
        </div>
      </div>
    </div>
    <div class="content-container">
      <el-table :data="list">
        <el-table-column label="序号" type="index" :index="getIndex"></el-table-column>
        <el-table-column label="模板名称" prop="templateName"></el-table-column>
        <el-table-column label="更新时间" prop="updateTime">
          <template v-slot="{row}">
            <p v-html="renderTime(row.updateTime)"></p>
          </template>
        </el-table-column>

        <el-table-column label="适用类型">
          <template v-slot="{row}">
            {{ $dictCode('SignTemplateTypeEnum', row.signTemplateType).desc }}
          </template>
        </el-table-column>

        <el-table-column label="适用代征主体">
          <template v-slot="{row}">
            <span v-if="row.merchantList">
              {{ row.merchantList.map(item => item.mchName).join(', ') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="适用用工企业">
          <template v-slot="{row}">
            <span v-if="row.employerList">
              {{ row.employerList.map(item => item.mchName).join(', ') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button type="text" @click="goForm(row)">编辑</el-button>
            <el-button type="text" @click="deleteSign(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-sizes="[10,50]"
        :page-size.sync="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </el-footer>
  </div>
</template>

<script>
  import { getMainstayList } from '@/api/merchant'
  import { getTemplateList, deleteSignTemplate } from '@/api/sign';

  export default {
    name: 'SignTemplate',
    data() {
      return {
        searchForm: {
          templateName: '',
          mchNo: [],
        },

        pageCurrent: 1,
        pageSize: 10,
        totalRecord: 0,

        activeMchList: [],

        list: [],
      }
    },
    mounted() {
      this.getMainstayList();
      this.search();
    },
    methods: {
      async search(init) {
        if (init) {
          this.pageCurrent = 1;
        }
        const { data } = await getTemplateList({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        });
        this.list = data.data;
        this.totalRecord = data.totalRecord;
      },
      resetForm() {
        this.searchForm = {
          templateName: '',
        }
      },
      async getMainstayList() {
        const { data } = await getMainstayList()
        this.activeMchList = data;
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1
      },
      createNew() {
        this.$router.push({
          path: '/business/signTemplateForm',
          query: {
            actionType: 'ADD'
          }
        })
      },
      goForm(row) {
        sessionStorage.setItem('signInfo', JSON.stringify(row));
        this.$router.push({
          path: '/business/signTemplateForm',
          query: {
            actionType: 'EDIT',
          }
        })
      },
      async deleteSign(row) {
        const result = await this.$confirm('确定删除该模板吗？', '删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).catch(_ => false);
        if (result) {
          await deleteSignTemplate({ id: row.id });
          this.search();
        }
      }
    },
  }
</script>

<style scoped lang="scss">

</style>
