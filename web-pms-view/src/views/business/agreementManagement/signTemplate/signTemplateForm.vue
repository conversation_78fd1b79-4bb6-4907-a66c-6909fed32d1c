<template>
  <div class="page-container">
    <el-form :model="form" :rules="rules" label-width="150px" ref="form">
      <p class="subTitle">基本配置</p>
      <el-form-item label="协议名称：" prop="protocolName">
        <el-input style="width: 400px;" v-model="form.templateName" clearable></el-input>
      </el-form-item>
      <el-form-item label="协议文件：" prop="file" ref="file">
        <el-upload
          ref="upload"
          v-toggle="fileList"
          class="upload-demo"
          action=""
          accept=".doc,.docx,.pdf"
          :auto-upload="false"
          list-type="picture-card"
          :headers="uploadHeader"
          :limit="1"
          :file-list="fileList"
          :before-upload="validateUploadFile({pdf: true, word: true, size: 6})"
          :on-change="handleFileChange"
        >
          <div style="display: inline-block;line-height: 14px;vertical-align: middle;"><i class="el-icon-upload"></i><br>上传本地文件<br>支持扩展名：.doc .docx .pdf</div>
          <template v-slot:file="{file}">
            <div class="fileBg">
              <span class="file-name">{{ file.name }}</span>
              <span class="el-upload-list__item-actions">
                <span
                  v-if="file.status == 'success'"
                  class="el-upload-list__item-preview"
                  @click="handlePreview(file)"
                >
                  <i class="el-icon-zoom-in"></i>
                </span>
                <span
                  class="el-upload-list__item-delete"
                >
                  <i class="el-icon-delete" @click="deleteFile(file)"></i>
                </span>
              </span>
            </div>
          </template>

        </el-upload>
      </el-form-item>

      <el-form-item label="适用类型：" prop="signTemplateType">
        <el-select
          v-model="form.signTemplateType"
          clearable
        >
          <el-option
            v-for="item in $dict('SignTemplateTypeEnum')"
            :value="Number(item.code)"
            :key="item.code"
            :label="item.desc"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="适用用工企业属于：" prop="employerList" v-if="form.signTemplateType == 100">
        <el-select
          v-model="form.employerList"
          clearable
          filterable
          multiple
          style="width: 300px;"
          value-key="mchNo"
        >
          <el-option
            v-for="mch in merchantList"
            :key="mch.mchNo"
            :label="mch.mchName"
            :value="mch"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="适用代征主体属于：" prop="merchantList" v-if="form.signTemplateType == 101">
        <el-select
          v-model="form.merchantList"
          clearable
          filterable
          multiple
          style="width: 300px;"
          value-key="mchNo"
        >
          <el-option
            v-for="mch in mainstayList"
            :key="mch.mchNo"
            :label="mch.mchName"
            :value="mch"
          ></el-option>
        </el-select>
      </el-form-item>

      <p class="subTitle">签署人</p>
      <el-table :data="list" border>
        <el-table-column label="签署人" prop="signer" width="100"></el-table-column>
        <el-table-column label="参与主体">
          <template v-slot="{row}">
            <el-tag type="info">使用模板时指定</el-tag>
            <span style="color: #FBB84E">{{ row.desc }}</span>
          </template>
        </el-table-column>
      </el-table>

      <div class="form-btn-group">
        <el-button type="primary" @click="nextStep" :loading="loading">下一步</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </el-form>


    <el-dialog title="等待控件设置" :visible.sync="nextStepDialogVisible">
      <div>
        正在跳转“E签宝”完成控件设置...
        <div style="display: flex; justify-content: center;margin: 32px 0;">
          <el-button @click="nextStepDialogVisible = false">返回</el-button>
          <el-button type="primary" @click="handleSignDown">设置完成</el-button>
        </div>
        <el-link type="primary" :href="signUrl" target="_blank" >未跳转时点这里复制到新窗口打开</el-link>
      </div>
    </el-dialog>

  </div>
</template>

<script>
  import { uploadSignFile, cacheSignForm, getCacheForm, getEmployer, getMainstay, clearCacheForm, addSignTemplate, editSignTemplate } from '@/api/sign';

  let apiDataTemp = {} // 纪录接口原始数据

  export default {
    name: "CreateSignTemplate",
    data() {
      let self = this;
      return {
        loading: false,
        form: {
          templateName: '',
          file: null,
          signTemplateType: '',
          sourceFileUrl: '',
          targetFileUrl: '',
          employerList: [],
          merchantList: [],
        },

        rules: {
          templateName: [
            { required: true, message: '请输入协议名称', trigger: 'blur' },
          ],
          signTemplateType: [
            { required: true, message: '请选择适用类型', trigger: 'blur' },
          ],
          merchantList: [
            { required: true, message: '请输入适用代征主体', trigger: 'change' },
          ],
          employerList: [
            { required: true, message: '请输入适用商户', trigger: 'change' },
          ],
          file: [
            { required: true, message: '请上传协议文件', trigger: 'change' },
            { validator: (rule, value, cb) => {
                if (self.actionType === 'EDIT') {
                  if (self.form.file) {
                    cb();
                  } else if (!self.form.sourceFileUrl || !self.form.targetFileUrl) {
                    cb(new Error('请上传协议文件'))
                  } else {
                    cb()
                  }
                } else if (self.actionType === 'ADD') {
                  if (!value) {
                    cb(new Error('请上传协议文件'))
                  } else {
                    cb()
                  }
                }
              }, trigger: 'blur' },
          ]
        },

        list: [
          {
            signer: '甲方',
            desc: '使用模板时，该角色由发起人指定（含本模板中的适用代征主体）'
          },
          {
            signer: '乙方',
            desc: '使用模板时，该角色由发起人指定（指自由职业者）'
          }
        ],

        fileList: [],

        // 代征主体列表
        mainstayList: [],
        // 商户列表
        merchantList: [],
        // 是否去下一步
        isToSetting: false,

        // 签约弹窗
        nextStepDialogVisible: false,
        // 签约 url
        signUrl: '',
      }
    },
    computed: {
      actionType() {
        return this.$route.query.actionType || 'ADD';
      }
    },
    async mounted() {
      Promise.all([
        this.getMainstayList(),
        this.getEmployer(),
      ]);

      if (this.actionType === 'EDIT' && sessionStorage.getItem('signInfo')) {
        this.form = Object.assign({}, this.form, JSON.parse(sessionStorage.getItem('signInfo')));
        apiDataTemp = JSON.parse(JSON.stringify(Object.assign({}, this.form, JSON.parse(sessionStorage.getItem('signInfo')))))
      } else {
        await this.getCacheForm()
      }
      if (this.form.targetFileUrl) {
        this.form.file = {fileName: '电子签约协议文件.pdf'};
        this.fileList = [{name: '电子签约协议文件.pdf', url: this.form.targetFileUrl}]
      }

    },
    beforeDestroy() {
      sessionStorage.removeItem('signInfo');
      if (!this.isToSetting) {
        clearCacheForm();
      }
    },
    methods: {
      async getMainstayList() {
        const { data } = await getMainstay();
        this.mainstayList = data
      },
      async getEmployer() {
        const { data } = await getEmployer();
        this.merchantList = data
      },
      handleFileChange(file) {
        this.form.file = file.raw;
        this.fileList = [file];
        this.$refs.file.clearValidate();
      },
      // async nextStep() {
      //   let result = await this.$refs.form.validate().catch(_=> false);
      //   if (!result) return;
      //   this.loading = true;
      //   await this.uploadFile().finally(() => this.loading = false);
      //   await this.cacheSignForm().finally(() => this.loading = false);
      //   this.loading = false;
      //   this.goSetting();
      // },

      // 点击下一步
      async nextStep() {
        let result = await this.$refs.form.validate().catch(_=> false);
        if (!result) return;
        this.loading = true;
        this.nextStepDialogVisible = true
        await this.uploadFile().finally(() => this.loading = false);
        await this.cacheSignForm().finally(() => this.loading = false);
        this.loading = false;

        let data = null
        if (this.actionType === 'ADD') {
          data = await addSignTemplate({ ...this.form })
        } else {
          const isChangeFile = this.form.targetFileUrl !== apiDataTemp.targetFileUrl
          data = await editSignTemplate({
            isChangeFile,
            ...this.form
          })
        }
        console.log({ data })
        this.signUrl = data.data.docTemplateCreateUrl
        window.open(data.data.docTemplateCreateUrl)
      },

      // 签约完成
      handleSignDown() {
        this.nextStepDialogVisible = false
        this.signUrl = ''
        this.$router.back()

        this.$message.success(this.actionType == 'ADD' ? '创建成功' : '修改成功');
      },

      async uploadFile() {
        if (this.fileList[0].status == 'success') return;
        let form = new FormData();
        form.append('file', this.form.file);
        const { data } = await uploadSignFile(form);
        this.form = Object.assign({}, this.form, data);
      },
      async cacheSignForm() {
        await cacheSignForm(this.form);
      },
      goSetting() {
        this.isToSetting = true;
        this.$router.push({
          path: '/business/settingSignTemplate',
          query: {
            actionType: this.actionType
          }
        })
      },
      cancel() {
        this.$router.push({
          path: '/business/signTemplate'
        })
      },
      deleteFile(file) {
        this.fileList = this.fileList.filter(item => file.uid !== item.uid);
        this.form.file = null;
        this.form.targetFileUrl = this.form.sourceFileUrl = '';
      },
      async handlePreview(file) {
        const fileMsg = await this.formatFileMsg(file.url)
        this.downloadFile(fileMsg.fileUrl, '电子签约协议文件', 'pdf');
      },
      async getCacheForm() {
        const { data } = await getCacheForm();
        if (data) {
          this.form = Object.assign({}, this.form, data);
          apiDataTemp = JSON.parse(JSON.stringify(Object.assign({}, this.form, JSON.parse(sessionStorage.getItem('signInfo')))))
        }
      }
    },
  }
</script>

<style lang="scss" scoped>

</style>
