class DragManage {
  constructor() {
    this.dragList = {}; // 已生成拖动组件, key=page, value=dragElmList
    this.activeConfig = null; // 选中拖拽的配置
    this.activeDrag = null; // 活跃组件（已经创建的）
  }
  setActiveConfig(config) {
    this.activeConfig = config;
  }
  clearActiveConfig() {
    this.activeConfig = null;
  }
  getActiveConfig() {
    return this.activeConfig;
  }
  save(page, drag) {
    if (!drag) return;
    if (!(this.dragList[page])) {
      this.dragList[page] = [];
    }
    if (Array.isArray(drag)) {
      this.dragList[page] = drag;
    } else {
      this.dragList[page].push(drag);
    }
  }
  getPageDrag(page) {
    if (!(this.dragList[page])) {
      return [];
    }
    return this.dragList[page];
  }
  getActiveDrag() {
    return this.activeDrag || {};
  }
  onActivated(elm) {
    this.activeDrag = elm
  }
  onDeactivated() {
    this.activeDrag = null;
  }
  getAllDragElm() {
    return this.dragList;
  }
  destroy() {
    this.dragList = {}
  }

}

export default DragManage;
