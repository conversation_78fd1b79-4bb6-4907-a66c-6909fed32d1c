<template>
  <div class="sign-setting-container">
    <drag-bar
      :manager="manager"
      :drag-item-list="dragItemList"
    ></drag-bar>
    <edit-bar
      :manager="manager"
      @delete-item="deleteItem"
    ></edit-bar>


    <pdf-reader
      ref="reader"
      :src="pdf"
      width="700"
      @init-finish="onInitFinish"
      @vp-change="onVpChange"
      showFunc
    >
      <template
        v-for="item in numPages"
      >
        <div
          :key="item"
          :slot="'mark-' + item"
          class="drag-canvas"
          @drop="(e) => onDrop(e, item)"
          @dragover="onDragOver"
        >
          <vue-drag-resize
            v-for="(elm) in dragList[item]"
            class="drag-elm"
            :class="{'sign-elm': elm.type === 6}"
            :key="elm.uid"
            :w="elm.style.width"
            :h="elm.style.height"
            :x="elm.pos.x || 0"
            :y="elm.pos.y || 0"
            :minWidth="20"
            :minHeight="20"
            :parent="true"
            :active="elm.uid === activeDragElm.uid"
            :prevent-deactivation="elm.uid === activeDragElm.uid"
            :resizable="elm.resizable"
            :axis="elm.axis || 'both'"
            :handles="elm.handles"
            @activated="onActivated(elm)"
            @dragstop="(x, y) => onDragStop(x, y, item)"
            @resizestop="(l, t, w, h) => onResizeStop(l, t, w, h, item)"
          >
            <template>
              <span :style="getItemStyle(elm.style)">
              {{ elm.label }}
              </span>
            </template>
          </vue-drag-resize>
        </div>
      </template>
    </pdf-reader>
    <div class="sign-setting-button text-center">
      <el-button type="primary" @click="submitSignForm" :loading="loading">提交</el-button>
      <el-button @click="back">上一步</el-button>
    </div>
  </div>
</template>

<script>
  import { getCacheForm, addSignTemplate, editSignTemplate, getDragComponent, clearCacheForm } from "@/api/sign";

  import VueDragResize from 'vue-draggable-resizable'
  import 'vue-draggable-resizable/dist/VueDraggableResizable.css'
  import DragBar from './DragBar'
  import EditBar from './EditBar'
  import DragManage from './DragManage'

  // pdf-reader
  import PdfReader from '@/components/PDFReader'

  // test pdf
  // import testPdf from '@/assets/test.pdf'


  let uid = 0;

  export default {
    name: 'SignTemplateSetting',
    components: {
      VueDragResize,
      DragBar,
      EditBar,
      PdfReader,
    },
    data() {
      return {
        loading: false,
        inputFocus: false,
        activeDragElm: -1,
        manager: new DragManage(),
        dragList: {},

        pdf: '',
        pdfPage: 0,
        pdfVp: {}, // pdf 视图窗口对象
        numPages: 0, // pdf 页数

        form: {}, // 提交表单

        dragItemList: [],

      }
    },
    computed: {
      actionType() {
        return this.$route.query.actionType;
      }
    },
    mounted() {
      this.getDragComponent();
      this.getCacheForm();
    },
    beforeDestroy() {
      this.manager.destroy()
    },
    methods: {
      async getDragComponent() {
        const { data } = await getDragComponent();
        this.dragItemList = data;
      },
      async getCacheForm() {
        const { data } = await getCacheForm();
        if (data) {
          this.form = Object.assign({}, this.form, data);
          this.pdf = this.fileUrl + this.form.targetFileUrl; // pdf 渲染异步
        }
      },
      setAllStructComponent() {
        // 缓存组件转换数据结构
        if (this.form.structComponent) {
          const structComponent = this.form.structComponent;
          structComponent.forEach(item => {
            let page = item.context.pos.page;
            let dragItem = {
              style: JSON.parse(JSON.stringify(item.context.style)),
              pos: {
                ...item.context.pos,
                y: this.pdfVp[page].height - item.context.pos.y - item.context.style.height,
              },
              label: item.context.label,
              type: item.type,
              uid: ++uid,
              axis: item.type === 11 ? 'y' : 'both',
              handles: item.handles || ['tl', 'tm', 'tr', 'mr', 'br', 'bm', 'bl', 'ml'],
              resizable: !(item.resizable === 0)
            };
            this.manager.save(page, dragItem);
          })
        }
        // 获取全部组件
        let allDrag = this.manager.getAllDragElm();
        for (let p in allDrag) {
          this.$set(this.dragList, p, allDrag[p])
        }
      },
      onActivated(elm){
        this.activeDragElm = elm;
        this.manager.onActivated(this.activeDragElm);
      },
      onDrop(e, page) {
        let elm = this.manager.getActiveConfig();
        let x = e.offsetX;
        let y = e.offsetY;
        if (e.target.className.includes('vdr')) {
          // hack 落点在另一个框内 其实还可以落点的pageX减去父元素的offsetLeft求得
          x = e.target.__vue__.x + x;
          y = e.target.__vue__.y + y;
        }
        // 落点为中心点
        x -= (elm.style.width / 2);
        y -= (elm.style.height / 2);

        // x y 越界行为
        if (x < 0) {
          x = 0;
        } else if (x > this.pdfVp[page].width - elm.style.width) {
          x = this.pdfVp[page].width - elm.style.width
        }

        if (y < 0) {
          y = 0;
        } else if (y > this.pdfVp[page].height - elm.style.height) {
          y = this.pdfVp[page].height - elm.style.height
        }

        if (elm.type === 11) {
          // 骑缝章靠右
          x = this.pdfVp[page].width - elm.style.width
        }

        const config = {
          ...elm,
          pos: {
            x,
            y,
            page,
          },
          uid: ++uid,
          axis: elm.type === 11 ? 'y' : 'both',
          handles: elm.handles || ['tl', 'tm', 'tr', 'mr', 'br', 'bm', 'bl', 'ml'],
          resizable: !(elm.resizable === 0)
        };

        if (!this.dragList[page]) {
          this.$set(this.dragList, page, []);
        }
        this.dragList[page].push(config);
        this.activeDragElm = config;
        this.dragSave(config, page);
      },
      onDragOver(e) {
        e.preventDefault();
      },
      onDragStop(x, y, page) {
        if (isNaN(x) || isNaN(y)) {
          x = this.activeDragElm.pos.x;
          y = this.activeDragElm.pos.y;
        }
        this.dragSave({pos: {x, y, page}}, page)
      },
      onResizeStop(l, t, w, h, page) {
        let oldStyle = this.activeDragElm.style || {};
        const style = {
          ...oldStyle,
          width: w,
          height: h,
        };
        const pos = {
          x: l,
          y: t,
          page,
        };
        this.dragSave({style, pos}, page)
      },
      dragSave(config, page) {
        // 插入新组件、拖动或resize后的保存
        let index = this.dragList[page].findIndex(item => item.uid === this.activeDragElm.uid);
        if (index === -1) return;
        this.activeDragElm = Object.assign({}, this.activeDragElm, config);
        this.dragList[page].splice(index, 1, this.activeDragElm);
        this.manager.onActivated(this.activeDragElm); // 拖动后重新更新active对象
        this.saveDrag(page);
      },
      saveDrag(page) {
        // 保存指定页当前组件到 manager
        this.manager.save(page, this.dragList[page]);
      },
      async deleteItem(elm) {
        let page = elm.pos.page;
        let index = this.dragList[page].findIndex(item => item.uid === elm.uid);
        this.dragList[page].splice(index, 1);
        this.manager.onDeactivated();
      },
      onInitFinish({ numPages }) {
        this.numPages = numPages;
        window.requestAnimationFrame(this.setAllStructComponent);
      },
      onVpChange({ vp, page }) {
        this.pdfVp[page] = vp;
      },
      submitSignForm() {
        this.loading = true;
        let temp = { ...(JSON.parse(JSON.stringify(this.manager.getAllDragElm()))) };

        // 组装输入组件的信息
        let tempConfig = [];
        let config = [];
        for (let p in temp) {
          tempConfig = tempConfig.concat(temp[p])
        }
        tempConfig.forEach(item => {
          let vpHeight = this.pdfVp[item.pos.page].height;
          config.push({
            type: item.type,
            context: {
              label: item.label,
              required: item.required,
              style: item.style,
              pos: {
                x: item.pos.x,
                y: vpHeight - item.pos.y - item.style.height,
                page: item.pos.page,
              },
            }
          })
        });
        this.submit(config)
      },
      async submit(structComponent) {
        let api = this.actionType == 'ADD' ? addSignTemplate : editSignTemplate;
        const { data } = await api({
          ...this.form,
          structComponent,
        }).finally(() => {
          this.loading = false;
        });
        data && this.$message.success(data);
        this.clearCacheForm();
        this.$router.push('/business/signTemplate');
      },
      clearCacheForm() {
        clearCacheForm();
      },
      getItemStyle(style) {
        return {
          fontSize: style.fontSize + 'px',
          color: style.textColor,
        }
      },
      back() {
        this.$router.push({
          path: '/business/signTemplateForm',
          query: {
            actionType: this.actionType
          }
        })
      },
    },
  }
</script>

<style scoped lang="scss">
  .sign-setting-container {
    background: #ddd;
    padding-bottom: 80px;
    .canvas-container {
      position: relative;
      margin: 0 auto;
      background: #fff;
    }

    .drag-canvas {
      height: 100%;
    }
    .drag-elm {
      text-align: left;
      background: rgba(135, 206, 235, 0.5);
      font-family: SimSun;
      &.sign-elm {
        background: rgba(173, 255, 47, .5);
      }
    }

    // 提交按钮
    .sign-setting-button {
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0;
      background: #fff;
      padding: 16px 0;
      z-index: 10;
    }
  }
</style>
