<template>
  <div class="drag-bar-container">
    <h2>添加控件</h2>
    <p>请将控件拖动到文件区域</p>
    <div class="drag-type-list">
      <div
        class="drag-type-item"
        v-for="item in normalItems"
        :key="item.id"
        draggable
        @dragstart="(e) => onDragStart(item, e)"
        @dragend="(e) => onDragEnd(item, e)"
      >
        <span
          :style="getItemStyle(item.style)"
        >
          {{ item.label }}
        </span>
      </div>
    </div>
    <p>快速插入控件</p>
    <div class="drag-type-list">
      <div
        class="drag-type-item"
        v-for="item in presetItems"
        :key="item.id"
        draggable
        @dragstart="(e) => onDragStart(item, e)"
        @dragend="(e) => onDragEnd(item, e)"
      >
        <span
          :style="getItemStyle(item.style)"
        >
          {{ item.label }}
        </span>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'DragBar',
    props: {
      manager: {
        type: Object,
        default: () => ({})
      },
      dragItemList: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        dragging: null,
      }
    },
    computed: {
      normalItems() {
        return this.dragItemList.filter(item => item.preset == 0)
      },
      presetItems() {
        return this.dragItemList.filter(item => item.preset == 1)
      }
    },
    methods: {
      onDragStart(dragElm, e) {
        console.log(">>>>>>>drag start<<<<<<<<<");
        this.manager.setActiveConfig(dragElm);
      },
      onDragEnd(dragElm, e) {
        this.manager.clearActiveConfig();
        console.log(">>>>>>>>>>drag end<<<<<<<<<<")
      },
      getItemStyle(style) {
        return {
          fontSize: style.fontSize + 'px',
          color: style.textColor,
        }
      }
    },
  }
</script>

<style scoped lang="scss">
  .drag-bar-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 20;
    width: 250px;
    height: 100vh;
    padding: 16px 8px;
    overflow-y: auto;
    background: #fff;


    .drag-type-item {
      margin-top: 8px;
      border: 1px dashed #444;
      padding: 8px 0;
      text-align: center;
      font-family: SimSun;
    }
  }
</style>
