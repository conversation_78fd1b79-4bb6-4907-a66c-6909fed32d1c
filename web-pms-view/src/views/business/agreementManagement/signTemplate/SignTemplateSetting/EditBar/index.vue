<template>
  <div class="edit-bar-box">
    <div class="edit-bar-container" v-show="activeDrag.uid">
      <div class="edit-item">
        <div>名称</div>
        <!--各组件库输入-->
        <el-input
          v-if="activeDrag.type == 1"
          v-model="activeDrag.label"
          style="margin-top: 8px;"
          :disabled="activeDrag.preset == 1"
          maxlength="15"
        ></el-input>

        <el-input
          v-if="activeDrag.type == 2"
          :value="activeDrag.label"
          style="margin-top: 8px;"
          :disabled="activeDrag.preset == 1"
          @input="handleRateInput(activeDrag, 'label', $event)"
        ></el-input>

        <el-date-picker
          v-if="activeDrag.type == 3"
          v-model="activeDrag.label"
          type="date"
          style="margin-top: 8px;"
          :disabled="activeDrag.preset == 1"
          placeholder="选择日期"
          format="yyyy-MM-dd"
        >
        </el-date-picker>

        <el-input
          v-if="activeDrag.type == 8"
          v-model="activeDrag.label"
          style="margin-top: 8px;"
          type="textarea"
          :rows="5"
          :disabled="activeDrag.preset == 1"
          maxlength="120"
        ></el-input>

      </div>
      <div class="edit-item">
        <div>填写人设置</div>
        <el-tag type="info" style="width: 100%; margin-top: 8px;">自由职业者</el-tag>
      </div>
      <div class="edit-item">
        <div>字体字号</div>
        <el-tag type="info" style="width: 100%; margin-top: 8px;">宋体 - {{activeDrag.uid && activeDrag.style.fontSize}}</el-tag>
      </div>
      <div class="edit-item">
        <el-button @click="deleteItem" style="width: 100%">删除</el-button>
      </div>
    </div>
    <div v-show="!activeDrag.uid" class="text-center" style="margin-top: 200px;">
      <i class="el-icon-menu"></i>
      <p>请将控件拖动到文件区域或选中已经添加到文件上的控件</p>
    </div>
  </div>
</template>

<script>
  export default {
    name: "EditBar",
    props: {
      manager: {
        type: Object,
        default: () => ({}),
      },
    },
    computed: {
      activeDrag() {
        return this.manager.getActiveDrag();
      }
    },
    methods: {
      deleteItem() {
        this.$emit('delete-item', this.activeDrag)
      }
    },
  }
</script>

<style lang="scss" scoped>
  .edit-bar-box {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    width: 250px;
    z-index: 20;
    padding: 16px;
    background: #fff;
    overflow-y: auto;

    .edit-item {
      margin-top: 32px;
    }
  }

</style>
