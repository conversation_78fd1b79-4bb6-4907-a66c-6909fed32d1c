<template>
  <el-container class="layout">
    <el-header class="layout-header">
      <div class="logo-container">
        <img
          src="@/assets/joinpay_logo.png"
          alt="汇聚智享"
          class="logo"
        />
      </div>
    </el-header>

    <el-main class="layout-container">
      <div class="layout-main">
        <div class="login-container">
          <div class="login-type-select">
            <span class="login-type">密码登录</span>
          </div>

          <el-form
            class="login-form"
            ref="loginForm"
            :model="loginForm"
            :rules="loginRules"
          >
            <el-form-item prop="username">
              <el-input
                class="login-input-phone"
                v-model.trim="loginForm.username"
                placeholder="注册时填写的邮箱"
                name="username"
                autocomplete="off"
              >
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input
                class="login-input-pwd"
                v-model.trim="loginForm.password"
                placeholder="请输入登录密码"
                type="password"
                name="password"
                show-password
                maxlength="16"
                autocomplete="off"
              ></el-input>
            </el-form-item>
            <el-form-item
              class="captcha-item"
              prop="captchaValue"
            >
              <el-input
                class="captcha-input"
                v-model="loginForm.captchaValue"
                placeholder="请输入图片验证码"
                @keyup.enter.native="doLogin"
              >
                <template slot="append">
                  <div
                    class="captcha-container"
                    @click="getCaptcha"
                  >
                    <img
                      :src="captchaSrc"
                      class="captcha"
                    >
                  </div>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item
              prop="emailCode"
              v-if="shouldUseEmail"
            >
              <el-input
                v-model="loginForm.emailCode"
                placeholder="请输入邮箱验证码"
                @keyup.enter.native="doLogin"
              >
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                :loading="loading"
                class="submit-btn"
                type="primary"
                @click="doLogin"
              >登录
              </el-button>
            </el-form-item>
          </el-form>

          <div class="login-footer clearfix">
            <span
              class="func-content float-l"
              @click="goReset"
            >忘记密码</span>
            <span class="float-r">还没有账号？请联系客服</span>
          </div>
        </div>
        <div class="login-pic">
          <img
            src="@/assets/login_pic.jpg"
            alt="login"
          >
        </div>
      </div>
    </el-main>
    <p class="login-web-isp">&copy; 2020 - 2021 Hjzxh.com <a
      target="_blank"
      href="https://beian.miit.gov.cn/">粤ICP备2020096931号</a>
    </p>
  </el-container>

</template>

<script>
import { toPromise } from "@/utils";
import { setToken, getToken } from '@/utils/loginToken'
import { encryptParam } from '@/utils/jsencrypt'
import { getCaptcha, doLogin, emailValid } from '@/api/user';

export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        username: '',
        password: '',
        captchaValue: '',
        captchaId: '',
        emailCode: '',
      },
      loginRules: {
        username: [{ required: true, message: '请输入邮箱账号', trigger: 'blur' }],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
        ],
        captchaValue: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
        emailCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined,
      captchaSrc: '',

      shouldValid: true, // 是否验证过
      shouldUseEmail: false,
    }
  },
  mounted() {
    this.getCaptcha()
    if (getToken()) {
      this.goToIndex()
    }
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    async doLogin() {
      const [err] = await toPromise(this.$refs.loginForm.validate())
      if (err) return
      if (this.shouldValid) {
        this.loading = true
        const [_, result] = await toPromise(this.emailValid())
        this.loading = false
        if (result) return this.shouldValid = false
      }
      this.loading = true;
      const [error, res] = await toPromise(doLogin({
        username: this.loginForm.username,
        password: encryptParam(this.loginForm.password),
        emailCode: this.loginForm.emailCode,
        captchaId: this.loginForm.captchaId,
        captchaValue: this.loginForm.captchaValue,
      }))

      this.loading = false;
      if (error) {
        return this.getCaptcha()
      }
      const { token } = res.data
      setToken(token);
      // this.$router.push({path: '/index'});
      this.goToIndex();
    },
    async getCaptcha() {
      const { data } = await getCaptcha();
      this.captchaSrc = "data:image/png;base64, " + data.img;
      this.loginForm.captchaValue = '';
      this.loginForm.captchaId = data.id;
    },
    goToIndex() {
      let url = `${ location.href.replace(/login.html(#\/)?$/, '') }`
      location.replace(url);
    },
    goReset() {
      let url = `${ location.href.replace(/login.html(#\/)?$/, '') }` + '#/retrievePwd'
      location.href = url
    },
    async emailValid() {
      const { data } = await emailValid({
        email: this.loginForm.username
      })
      this.shouldUseEmail = data
      if (this.shouldUseEmail) {
        this.$message.success('验证码已发送到邮箱，请注意查看')
      }
      return data
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #fff;
$cursor: #000;

/* reset element-ui css */
.login-container {
  .el-input {
    height: 47px;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      height: 47px;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }
}
</style>

<style
  lang="scss"
  scoped
>
.layout {
  position: relative;
  width: 100%;
  min-height: 100vh;
  text-align: center;
  background: #f2f3f5;

  .layout-header {
    display: flex;
    align-items: center;
    padding-top: 16px;

    .logo-container .logo {
      height: 50px;
      box-sizing: border-box;
    }
  }

  .layout-main {
    text-align: center;
    white-space: nowrap;
  }

  .login-pic {
    display: inline-block;
    width: 328px;
    margin-left: 16px;
    vertical-align: middle;

    img {
      vertical-align: bottom;
    }
  }

  .login-web-isp {
    position: fixed;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    color: #999;
    white-space: nowrap;
  }

  .login-container {
    display: inline-block;
    width: 520px;
    padding: 50px 56px 60px;
    background: #fff;
    vertical-align: middle;
  }

  .login-form {
    margin-top: 24px;

    .el-input-group__append {
      padding: 0px 0px;
    }

    .submit-btn {
      width: 100%;
      height: 40px;
    }

    .el-button--primary.is-disabled,
    .el-button--primary.is-disabled:active,
    .el-button--primary.is-disabled:focus,
    .el-button--primary.is-disabled:hover {
      background: transparent;
      color: #c0c4cc;
      border-color: transparent transparent #ebeef5;
    }

    .captcha-container {
      width: 95px;
      height: 38px;
      cursor: pointer;

      .captcha {
        height: 38px;
      }
    }

    .captcha-item::v-deep .el-input-group__append {
      padding: 0;
      background: transparent;
      border: none;
    }
  }

  ::v-deep .el-input {
    .el-input__inner {
      padding: 0;
      border-bottom: 1px solid #dcdee0;

      &:focus {
        border-bottom-color: $mainColor;
      }
    }
  }

  .login-form-footer {
    display: flex;
    justify-content: space-between;
  }

  .login-type-select {
    text-align: left;

    .login-type {
      margin-right: 24px;
      color: #323233;
      font-weight: 500;
      font-size: 26px;
    }
  }
}
</style>
