<template>
  <el-container class="layout">
    <el-header class="layout-header">
      <div class="logo-container">
        <img src="@/assets/joinpay_logo.png" alt class="logo"/>
      </div>
    </el-header>

    <el-main class="layout-container">
      <el-form class="pwd-form" ref="form" :model="form" :rules="rules">
        <div class="form-title">{{ type == 'edit' ? '修改密码' : '找回密码'}}</div>
        <el-form-item prop="email">
          <el-input
            v-model="form.email"
            placeholder="请输入邮箱账号"
            autocomplete="new-password"
            :disabled="type == 'edit'"
          ></el-input>
        </el-form-item>

        <el-form-item prop="pwd">
          <el-input
            v-model="form.pwd"
            placeholder="设置密码（8 - 16位密码，包含字母和数字，区分大小写）"
            type="password"
            autocomplete="new-password"
            show-password
          ></el-input>
        </el-form-item>

        <el-form-item prop="emailCode" class="form-captcha-item">
          <el-input
            v-model="form.emailCode"
            placeholder="请输入验证码"
            autocomplete="new-password"
          ></el-input>
          <span class="func-content captcha-btn" @click="getValidCode" :class="{'loading': loading}">
            {{ btnText }}
          </span>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" class="submit-btn" @click="submit">
            提交
          </el-button>
        </el-form-item>

        <div class="clearfix" v-if="type == 'reset'">
          <p class="float-r">已有账号？<span @click="goLogin" class="func-content">登录</span></p>
        </div>
      </el-form>
    </el-main>
    <p class="login-web-isp">&copy; 2020 - 2021 Hjzxh.com 粤ICP备2020096931号</p>
  </el-container>
</template>

<script>
  import { encryptParam } from '@/utils/jsencrypt'
  import { getEmailCode, resetPwd } from '@/api/user';
  import { removeToken } from '@/utils/loginToken';

  export default {
    name: 'RetrievePwd',
    data() {
      return {
        form: {
          email: '',
          emailCode: '',
          pwd: '',
        },
        rules: {
          email: [{required: true, message: '请输入邮箱账号', trigger: 'blur'}],
          pwd: [
            {required: true, message: '请输入密码', trigger: 'blur'},
            {validator: (rule, value, cb) => {
                let lengthValid = (value.length >= 8) && (value.length <= 16);
                let formatValid = !(/[^(0-9a-zA-Z)]/g.test(value)); // 非数字和英文
                if (formatValid) {
                  formatValid = /\d+/.test(value) && /[a-z]/ig.test(value)
                }
                if (!lengthValid || !formatValid) {
                  cb(new Error('请输入正确格式的密码，8 - 16位长度，包含字母和数字，区分大小写'))
                } else {
                  cb();
                }
              }, trigger: 'blur' }
          ],
          emailCode: [{required: true, message: '请输入验证码', trigger: 'blur'}]
        },

        loading: false,
        btnText: '获取验证码',
      }
    },
    computed: {
      type() {
        return this.$route.query.type || 'reset';
      }
    },
    mounted() {
      if (this.type == 'edit') {
        this.form.email = (this.$store.getters.userData && this.$store.getters.userData.name) || ''
      }
    },
    methods: {
      async getValidCode() {
        if (this.loading) return;
        if (!this.form.email) {
          return this.$message.error('请输入邮箱账号')
        }
        await getEmailCode({
          email: this.form.email
        });
        this.$message.success('发送成功');
        this.loading = true;
        this.changeBtnText(59)
      },
      changeBtnText(sec) {
        if (sec < 0) {
          this.timer && clearTimeout(this.timer);
          this.btnText = '获取验证码';
          this.loading = false;
        } else {
          this.btnText = `${sec} 秒后再获取`;
          this.timer = setTimeout(() => {
            this.changeBtnText(--sec)
          }, 1000)
        }
      },
      async submit() {
        const valid = await this.$refs.form.validate().catch(_ => false);
        if (!valid) return;
        let form = { ...this.form };
        const { data } = await resetPwd({
          email: form.email,
          emailCode: form.emailCode,
          pwd: encryptParam(form.pwd),
        });
        this.$message.success(data);
        removeToken();
        location.replace('./login.html');
      },
      goLogin() {
        location.replace('./login.html');
      }
    },
  }
</script>

<style scoped lang="scss">
  .layout {
    position: relative;
    width: 100%;
    height: 100vh;
    text-align: center;
    background: #f2f3f5;

    .layout-header {
      display: flex;
      align-items: center;

      .logo-container .logo {
        box-sizing: border-box;
        width: 130px;
        margin-top: 32px;
      }
    }

    .layout-container {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate3d(-50%, -50%, 0);

      .pwd-form {
        width: 500px;
        padding: 50px;
        background: #fff;
      }

      .submit-btn {
        width: 100%;
      }

      .form-title {
        margin-right: 24px;
        margin-bottom: 18px;
        text-align: left;
        font-weight: 500;
        font-size: 26px;
        color: #323233;
      }

      .form-captcha-item {
        position: relative;

        & >>> .el-input__inner {
          padding-right: 126px;
        }
      }
      .captcha-btn {
        @include position-tb-center(absolute);
        right: 8px;

        &.loading {
          color: #999
        }
      }

      .el-input >>> .el-input__inner {
        border-width: 0 0 1px 0;
        border-radius: 0;
      }
    }

    .login-web-isp {
      position: fixed;
      bottom: 8px;
      left: 50%;
      transform: translateX(-50%);
      color: #999;
    }

  }

</style>
