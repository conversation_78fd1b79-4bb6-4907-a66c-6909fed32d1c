<template>
  <el-container class="layout">
    <el-header class="layout-header">
      <div class="layout-header-left">
        <div class="logo-container">
          <img src="@/assets/joinpay_logo.png" alt class="logo"/>
        </div>
      </div>
      <div class="layout-header-right">
        <span>{{ userData && userData.realName }}&nbsp;</span>
        <el-button type="text" @click="onLogout()">退出</el-button>
      </div>
    </el-header>

    <el-main class="layout-main">
      <div class="item">
        <span class="text">登录账号：</span>
        <span>{{ userData && userData.name }}&nbsp;</span>
        <el-button type="text" @click="$router.push('/retrievePwd?type=edit')">修改密码</el-button>
      </div>
      <div class="item">
        <span class="text">姓名：</span>
        <span>{{ userData && userData.realName }} </span>
      </div>
      <div class="item">
        <span class="text">头像：</span>
        <svg-icon icon-class="avatar"></svg-icon>
      </div>
    </el-main>
  </el-container>
</template>

<script>
  import { mapGetters } from "vuex"
  import { removeToken } from "../../utils/loginToken";
  export default {
    name: "accountManagement",
    computed: {
      ...mapGetters(["userData"]),
      baseUrl() {
        return process.env.VUE_APP_BASE_API;
      },
    },
    data() {
      return {
        statusEnum: {
          '1': '激活',
          '-1': '冻结'
        },
        name: '',
        fileList: []
      }
    },
    mounted() {
      this.name = this.userData.name;
    },
    methods: {
      onLogout() {
        removeToken();
        this.$router.push({path: "/login"});
      },
      updateInfo() {
      },
      goBack() {
        this.$router.back();
      }
    }
  }
</script>

<style lang="scss" scoped>
  .layout {
    width: 100%;
    background: $lightGray;

    .layout-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .layout-header-left {
        display: flex;
        justify-self: start;
        align-items: center;

        .logo-container .logo {
          width: 150px;
          padding: 15px 15px 15px 15px;
          box-sizing: border-box;
        }
      }

      .layout-header-right {
        display: flex;
        justify-self: start;
        align-items: center;
      }
    }

    .layout-main {
      width: 500px;
      margin: 0 auto;
      .item {
        margin-bottom: 10px;
        line-height: 1.5;
        .el-input {
          width: 200px;
        }
      }
      .text {
        display: inline-block;
        width: 120px;
        margin-right: 50px;
        text-align: right;
      }
      .btn-group {
        margin-top: 30px;
        text-align: center;
      }
      .svg-icon {
        width: 50px;
        height: 50px;
        vertical-align: middle;
      }
    }
    .user-avatar {
      width: 150px;
      height: 150px;
    }
    .upload-demo {
      display: inline-block;
      margin-left: 10px;
      text-align: center;
    }
  }
</style>
