<template>
  <div class="box-container">
    <el-button
      class="create-btn"
      type="primary"
      @click="$router.push('/riskControl/ruleForm?actionType=ADD')"
    >新建
    </el-button
    >
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">规则名称：</span>
            <el-input
              v-model="form.name"
              placeholder="模糊查询"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">规则ID：</span>
            <el-input v-model="form.id"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">规则状态：</span>
            <el-select
              clearable
              v-model="form.status">
              <el-option
                v-for="item in $dict('OpenOffEnum')"
                :key="item.code"
                :value="item.code"
                :label="item.desc"
              ></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">供应商：</span>
            <el-select
              clearable
              v-model="form.supplierNo"
              filterable>
              <el-option
                v-for="item in supplier"
                :key="item.mchNo"
                :value="item.mchNo"
                :label="item.mchName"
              ></el-option>
            </el-select>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">用工企业：</span>
            <el-select
              clearable
              v-model="form.employerNo"
              filterable>
              <el-option
                v-for="item in employer"
                :key="item.mchNo"
                :value="item.mchNo"
                :label="item.mchName"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="search-btn-group">
            <el-button
              type="primary"
              @click="getRiskControlList(true)"
            >查询
            </el-button
            >
            <el-button
              type="text"
              @click="resetForm">清空筛选条件
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <div
        v-if="tips"
        type="danger"
        class="block-tag"
        v-html="tips"></div>
      <el-tabs
        v-model="activeName"
        class="tabs"
        type="card"
        @tab-click="handleClick"
      >
        <el-tab-pane
          v-for="(item, index) in tabs"
          :key="index"
          :label="item.label"
          :name="item.value"
        ></el-tab-pane>
      </el-tabs>
      <el-table
        class="content-main"
        :data="riskControlObj.data">
        <el-table-column
          label="规则ID"
          prop="id"></el-table-column>
        <el-table-column
          label="规则名称"
          prop="name"></el-table-column>
        <el-table-column
          label="风控接入点"
          prop="accessPoint">
          <template slot-scope="scope">
            {{ $dictCode("RiskAccessPointEnum", scope.row.accessPoint).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="适用供应商"
          prop="supplierNames">
        </el-table-column>
        <el-table-column
          label="适用用工企业"
          prop="employerNames"
          :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column
          label="规则类型"
          prop="type">
          <template v-slot="{ row }">
            <el-tag :type="getTagType(row.type)">
              {{ $dictCode("RiskRuleTypeEnum", row.type).desc }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="规则状态"
          prop="fixedFee">
          <template v-slot="{ row }">
            {{ $dictCode("OpenOffEnum", row.status).desc }}
          </template>
        </el-table-column>
        <el-table-column
          label="修改人/修改时间"
          prop="mchNo"
          width="150">
          <template slot-scope="scope">
            {{ scope.row.updator }} <br />
            {{ scope.row.updateTime }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          prop="data">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleEdit('VIEW', scope.row)"
            >查看
            </el-button>
            <el-button
              type="text"
              @click="handleEdit('EDIT', scope.row)"
            >修改
            </el-button>
            <el-button
              type="text"
              @click="handleConfirm('删除', scope.row)"
            >删除
            </el-button>
            <el-button
              type="text"
              @click="handleEdit('COPY', scope.row)"
            >复制内部内容
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-if="riskControlObj"
          ref="pagination"
          :total="riskControlObj.totalRecord"
          :current-page.sync="pageCurrent"
          :page-sizes="[10, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import {
  deleteRiskControl,
  getRiskControlList,
  getNoConfigSuppliers,
} from "@/api/riskControl";
import { getAllSuppliers, getAllMerchant } from "@/api/merchant";

export default {
  name: "PmsRiskControl",
  data() {
    return {
      form: {
        id: "",
        name: "",
        status: "",
        type: null,
      },
      pageCurrent: 1,
      pageSize: 10,
      riskControlObj: {},
      supplier: [],
      employer: [],
      tips: null,
      tabs: [],
      activeName: "all",
    };
  },
  mounted() {
    this.getRiskControlList();
    this.getAllSuppliers();
    this.getNoConfigSuppliers();
    this.getTags();
    this.getAllMerchant();
  },
  methods: {
    handleClick() {
      let type = this.activeName;
      if (this.activeName == "all") {
        type = null;
      }
      this.form.type = type;
      this.getRiskControlList();
    },
    getTags() {
      let list = [];
      list.push({
        label: "全部",
        value: "all",
      });
      this.$dict("RiskRuleTypeEnum").forEach((e) => {
        list.push({
          label: e.desc,
          value: e.code,
        });
      });
      this.tabs = list;
    },
    getRiskControlList(initFlag) {
      if (initFlag === true) {
        this.pageCurrent = 1;
      }
      getRiskControlList({
        ...this.form,
        pageCurrent: this.pageCurrent,
        pageSize: this.pageSize,
      }).then((response) => {
        this.riskControlObj = response.data;
      });
    },
    getTimeRange(val) {
      this.form.timeRange = val;
    },
    resetForm() {
      this.form = {
        mchNo: "",
        mchName: "",
        productNo: "",
        productName: "",
        status: "",
      };
    },
    handleCurrentChange(val) {
      this.pageCurrent = val;
      this.getRiskControlList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.getRiskControlList(true);
    },
    handleEdit(status, data) {
      sessionStorage.setItem("riskControl", JSON.stringify(data));
      this.$router.push("/riskControl/ruleForm?actionType=" + status);
    },
    handleConfirm(text, data) {
      this.$confirm(`确定要${text}该条数据吗`, {
        type: "warning",
      })
        .then((res) => {
          deleteRiskControl({
            id: data.id,
          }).then((resposne) => {
            this.$message.success("删除成功");
            this.getRiskControlList();
          });
        })
        .catch(() => {
        });
    },
    async getAllMerchant() {
      const {data} = await getAllMerchant({merchantType: 100});
      this.employer = data.filter((item) => item.mchNo != "-1");
    },
    async getAllSuppliers() {
      const {data} = await getAllSuppliers();
      this.supplier = data.filter((item) => item.mchNo != "-1");
    },
    getTagType(type) {
      return type === 1 ? "primary" : "info";
    },
    async getNoConfigSuppliers() {
      const {data} = await getNoConfigSuppliers();
      let text = data.map((e) => e.supplierName).join(",");
      text = `<span style="color: #f00">${text}</span>`;
      this.tips = `供应商${text}仍未配置风控规则，请尽快新建规则`;
    },
  },
};
</script>

<style
  scoped
  lang="scss">
.tips {
  color: #ff452c;
}

.tabs {
  margin-top: 1rem;
}

.block-tag {
  display: block;
  height: auto;
  white-space: normal;
  line-height: 1.2;
}
</style>
