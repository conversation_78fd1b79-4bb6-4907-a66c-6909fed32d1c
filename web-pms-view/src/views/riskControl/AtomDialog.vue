<template>
  <el-dialog
    :visible.sync="show"
    :close-on-click-modal="false"
    :before-close="closeForm"
    :title="(actionType === 'ADD' &&'新增') || (actionType==='EDIT'&&'编辑')||''"
    width="800px"
  >
    <el-form
      ref="form"
      :model="form"
      label-width="120px"
      :rules="rules"
    >
      <el-form-item
        label="策略原子组"
        prop="strategyAtomList">
        <el-button @click="addAtom">添加</el-button>
        <el-table :data="form.strategyAtomList">
          <el-table-column
            v-slot="{$index}"
            width="50"
          >
            <span v-if="$index > 0">且</span>
          </el-table-column>
          <el-table-column>
            <template v-slot="{row, $index}">
              <el-select
                clearable
                v-model="row.variable"
                @change="handelVarChange($event, $index)">
                <el-option
                  v-for="item in $dict('StrategyAtomVariableEnum')"
                  :key="item.code"
                  :value="item.code"
                  :label="item.desc"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column>
            <template slot-scope="scope">
              <el-select
                clearable
                v-model="scope.row.operator">
                <el-option
                  v-for="item in $dict('StrategyAtomOperatorEnum')"
                  :key="item.code"
                  :value="item.code"
                  :label="item.desc"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column>
            <template v-slot="{row}">
              <el-input
                v-model="row.constant"
                v-if="row.variable == 400"
                @change="handleContentChange($event, row)"></el-input>
              <el-input
                v-model="row.constant"
                v-else
                :disabled="row.variable == 500 || row.variable == 600 || row.variable == 700 || row.variable == 800"></el-input>
            </template>
          </el-table-column>
          <el-table-column width="50">
            <template v-slot="{$index}">
              <el-button
                type="text"
                @click="deleteAtom($index)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item
        label="管控原子"
        prop="controlAtom">
        <el-radio-group v-model="form.controlAtom">
          <el-radio
            v-for="item in $dict('ControlAtomEnum')"
            :key="item.code"
            :label="item.code"
          >{{ item.desc }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="权重"
        prop="weight">
        <el-input v-model="form.weight"></el-input>
      </el-form-item>
      <el-form-item
        label="描述"
        prop="remark">
        <el-input v-model="form.remark"></el-input>
      </el-form-item>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        type="text"
        @click="closeForm"
      >取消
      </el-button>
      <el-button
        :loading="loading"
        type="primary"
        @click="doSubmit"
      >确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { toCDB } from '@/utils';
import { createAtom, updateAtom } from '@/api/riskControl'

export default {
  props: {
    actionType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ruleId: '',
      loading: false,
      show: false,
      form: {
        weight: '',
        controlAtom: '',
        remark: '',
        strategyAtomList: [],
      },
      invoiceCategoryList: [],
      rules: {
        weight: [{ required: true, message: '请输入权重', trigger: 'blur' }],
        remark: [{ required: true, message: '请输入描述', trigger: 'blur' }],
        controlAtom: [{ required: true, message: '请选择管控因子', trigger: 'change' }],
      }
    }
  },
  methods: {
    addAtom() {
      this.form.strategyAtomList.push({
        variable: '',
        operator: '',
        constant: ''
      })
    },
    doSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.actionType === 'ADD' && this.doAdd();
          this.actionType === 'EDIT' && this.doEdit();
        }
      })
    },
    doAdd() {
      if (this.ruleId) {
        createAtom({
          ...this.form,
          ruleId: this.ruleId
        }).then(response => {
          this.$message.success('创建成功');
          this.closeForm()
          this.$emit('success')
        }).finally(() => this.loading = false)
      } else {
        this.doSave('add');
      }
    },
    doEdit() {
      if (this.ruleId) {
        updateAtom(this.form).then(response => {
          this.$message.success('修改成功')
          this.closeForm()
          this.$emit('success')
        }).finally(() => this.loading = false)
      } else {
        this.doSave('edit');
      }
    },
    doSave(type) {
      // 若为新建的风控规则，先在前端缓存，后续提交
      if (this.form.strategyAtomList.length === 0
        || !this.form.controlAtom
        || !this.form.weight) {
        this.loading = false;
        return this.$message.error('请正确填写策略原子组的内容');
      }
      for (let i = 0; i < this.form.strategyAtomList.length; i++) {
        const item = this.form.strategyAtomList[i];
        if (item.variable === ''
          || item.operator === ''
          || (item.constant === ''
            && item.variable !== '500'
            && item.variable !== '600'
            && item.variable !== '700'
            && item.variable !== '800')) {
          this.loading = false;
          this.$message.error('请正确填写策略原子组的内容');
          return;
        }
      }

      this.$emit('save', {
        data: { ...this.form },
        type,
      });
      this.loading = false;
      this.closeForm();
    },
    closeForm() {
      this.show = false
      this.loading = false
      this.form = {
        weight: '',
        controlAtom: '',
        remark: '',
        strategyAtomList: [],
      }
      this.$refs.form.resetFields()
    },
    deleteAtom(index) {
      this.form.strategyAtomList.splice(index, 1);
    },
    handleContentChange(val, row) {
      row.constant = toCDB(val)
    },
    handelVarChange(val, index) {
      if (val == 500 || val == 600) {
        this.form.strategyAtomList[index].constant = ''
      }
    },
  }
}
</script>
