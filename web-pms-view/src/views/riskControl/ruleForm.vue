<template>
  <div class="box-container">
    <el-form
      label-width="150px"
      :model="form"
      ref="form"
      v-loading="load"
      :rules="rules"
      :disabled="actionType === 'VIEW'"
    >
      <el-form-item
        label="规则ID："
        prop="id"
        v-if="actionType === 'VIEW'"
      >
        <el-input
          v-model="form.id"
          disabled
        ></el-input>
      </el-form-item>
      <el-form-item
        label="规则名称："
        prop="name"
      >
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <el-form-item
        label="规则状态："
        prop="status"
      >
        <el-radio-group v-model="form.status">
          <el-radio
            v-for="item in $dict('OpenOffEnum')"
            :key="item.code"
            :label="item.code"
          >{{ item.desc }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        label="风控接入点："
        prop="accessPoint"
      >
        <el-select
          clearable
          v-model="form.accessPoint"
        >
          <el-option
            v-for="item in $dict('RiskAccessPointEnum')"
            :key="item.code"
            :value="item.code"
            :label="item.desc"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="规则类型："
        prop="type"
      >
        <el-radio-group v-model="form.type">
          <el-radio
            v-for="item in $dict('RuleTypeEnum')"
            :key="item.code"
            :label="item.code"
          >{{ item.desc }}
          </el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        label="特殊商户："
        prop="merchantList"
        v-if="$dictCode('RuleTypeEnum', form.type).desc === '特殊'"
      >
        <el-select
          clearable
          v-model="form.merchantList"
          multiple
          filterable
        >
          <el-option
            v-for="(supply, index) in merchantList"
            :key="index"
            :label="supply.mchName"
            :value="index"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        label="选择供应商："
        prop="supplierList"
      >
        <el-select
          clearable
          v-model="form.supplierList"
          multiple
          filterable
        >
          <el-option
            v-for="(supply, index) in suppliers"
            :key="index"
            :label="supply.mchName"
            :value="index"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item
        label="风控规则编辑："
        prop="strategyAtomList"
      >
        <el-button
          @click="addAtom"
          v-if="actionType !== 'VIEW'"
        >添加
        </el-button>
        <el-table :data="atomTable">
          <el-table-column label="策略原子组列表">
            <template slot-scope="scope">
              <span
                v-for="item in scope.row.strategyAtomList"
                :key="item.id"
              >
                {{ $dictCode('StrategyAtomVariableEnum', item.variable).desc }} {{ $dictCode('StrategyAtomOperatorEnum', item.operator).desc }} {{ item.constant }}；
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="权重"
            prop="weight"
          ></el-table-column>
          <el-table-column label="管控原子">
            <template v-slot="{row}">
              {{ $dictCode('ControlAtomEnum', row.controlAtom).desc }}
            </template>
          </el-table-column>
          <el-table-column
            label="描述"
            prop="remark"
          ></el-table-column>
          <el-table-column
            label="操作"
            v-if="actionType !== 'VIEW'"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                @click="editAtom(scope.row, scope.$index)"
              >编辑
              </el-button>
              <el-button
                type="text"
                @click="deleteAtom(scope.row, scope.$index)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
    </el-form>
    <div class="footer-container">
      <el-button
        v-if="actionType != 'VIEW'"
        type="primary"
        @click="submit"
      >确定
      </el-button>
      <el-button @click="cancel">取消</el-button>
    </div>

    <AtomDialog
      ref="atomDialog"
      :action-type="atomActionType"
      @success="getAtomListById"
      @save="saveAtomData"
    ></AtomDialog>
  </div>
</template>

<script>
import { getAllMerchant } from '@/api/merchant'
import { addRiskControl, deleteAtom, getAtomListById, updateRiskControl, getAllSuppliers, addStrategyAtomGroupBatch } from '../../api/riskControl'
import AtomDialog from './AtomDialog'

export default {
  name: "ruleForm",
  components: {
    AtomDialog
  },
  data() {
    return {
      load: false,
      productInfo: '',
      merchantInfo: '',
      rules: {
        name: [{required: true, message: '请输入规则名称', trigger: 'blur'}],
        status: [{required: true, message: '请选择规则状态', trigger: 'blur'}],
        accessPoint: [{required: true, message: '请选择风控接入点', trigger: 'change'}],
        type: [{required: true, message: '请选择规则类型', trigger: 'change'}],
        supplierList: [{required: true, message: '请选择适用供应商', trigger: 'change'}],
        merchantList: [{required: true, message: '请选择适用用工企业', trigger: 'change'}],
      },
      form: {
        name: '',
        status: '',
        accessPoint: '',
        type: '',
        supplierNos: '',
        supplierNames: '',
        supplierList: [], // 记录的是选择的下标，提交之前进行拼装
        merchantList: [],
        weight: '',
        employerNos: '',
        atomTable: [], // 原子策略列表
      },
      atomTable: [], // 原子策略列表

      suppliers: [], // 供应商列表
      merchantList: [],

      ruleIndex: -1, // 正在编辑的风控规则序号

      atomActionType: ''
    };
  },
  computed: {
    actionType() {
      return this.$route.query.actionType;
    }
  },
  mounted() {
    if (sessionStorage.getItem('riskControl')) {
      this.form = JSON.parse(sessionStorage.getItem('riskControl'));
      this.form.accessPoint += '';
      this.form.type += '';
      this.form.status += '';
      if (!this.form.supplierNames) {
        this.form.supplierNames = '';
      }
      if (!this.form.supplierNos) {
        this.form.supplierNos = '';
      }
      if (!this.form.employerNames) {
        this.form.employerNames = '';
      }
      if (!this.form.employerNos) {
        this.form.employerNos = '';
      }
    }
    if (this.form.id) {
      this.getAtomListById();
    }
    this.getAllSuppliers()
    this.getAllMerchant()

    // 复制过程删除ruleid
    if (this.actionType === 'COPY') {
      this.form.id = undefined
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('riskControl');
  },
  methods: {
    getAtomListById() {
      getAtomListById({
        ruleId: this.form.id
      }).then(res => {
        this.atomTable = res.data;
      })
    },
    getAllSuppliers() {
      getAllSuppliers().then(response => {
        this.suppliers = response.data;
        // 匹配对应的供应商
        const mchNoList = this.form.supplierNos.split(',');
        const resultList = [];
        mchNoList.forEach(item => {
          this.suppliers.forEach((supply, index) => {
            if (supply.mchNo === item) {
              resultList.push(index);
            }
          })
        })
        this.$set(this.form, 'supplierList', resultList);
      })
    },
    async getAllMerchant() {
      const {data} = await getAllMerchant({merchantType: 100});
      this.merchantList = data;
      const mchNoList = this.form.employerNos.split(',');
      const resultList = [];
      mchNoList.forEach(item => {
        this.merchantList.forEach((supply, index) => {
          if (supply.mchNo === item) {
            resultList.push(index);
          }
        })
      })
      this.$set(this.form, 'merchantList', resultList);
    },
    addAtom() {
      this.atomActionType = 'ADD'
      this.$refs.atomDialog.ruleId = this.form.id;
      this.$refs.atomDialog.show = true;
    },
    editAtom(data, index) {
      let newData = JSON.parse(JSON.stringify(data));
      newData.controlAtom += '';
      for (let item of newData.strategyAtomList) {
        item.variable += '';
        item.operator += '';
      }
      if (this.form.id) {
        this.$refs.atomDialog.ruleId = this.form.id;
      }
      this.ruleIndex = index;
      this.atomActionType = 'EDIT'
      this.$refs.atomDialog.form = newData;
      this.$refs.atomDialog.show = true;
    },
    deleteAtom(data, index) {
      this.$confirm(`确定要刪除该条数据吗`, {
        type: 'warning'
      }).then(res => {
        if (this.actionType === 'ADD' || this.actionType === 'COPY') {
          this.atomTable.splice(index, 1);
        } else {
          deleteAtom({
            groupId: data.id
          }).then(res => {
            this.$message.success(res.data);
            this.getAtomListById();
          })
        }
      }).catch(() => {
      })
    },
    saveAtomData({type, data}) {
      if (type === 'add') {
        this.atomTable.push(data);
      } else if (type === 'edit') {
        this.atomTable.splice(this.ruleIndex, 1, data);
      }
    },
    async submit() {
      const valid = await this.$refs.form.validate();
      if (valid) {
        // 拼装供应商字段
        this.form.supplierNames = '';
        this.form.supplierNos = '';
        this.form.supplierList && this.form.supplierList.forEach(item => {
          if (this.suppliers[item]) {
            this.form.supplierNames += `,${this.suppliers[item]['mchName']}`;
            this.form.supplierNos += `,${this.suppliers[item]['mchNo']}`;
          }
        })
        this.form.employerNames = '';
        this.form.employerNos = '';
        if (this.$dictCode('RuleTypeEnum', this.form.type).desc === '特殊') {
          this.form.merchantList && this.form.merchantList.forEach(item => {
            if (this.merchantList[item]) {
              this.form.employerNames += `,${this.merchantList[item]['mchName']}`;
              this.form.employerNos += `,${this.merchantList[item]['mchNo']}`;
            }
          })
        }
        this.form.supplierNames = this.form.supplierNames.slice(1); // 去除第一个逗号
        this.form.supplierNos = this.form.supplierNos.slice(1);
        this.form.employerNames = this.form.employerNames.slice(1); // 去除第一个逗号
        this.form.employerNos = this.form.employerNos.slice(1);
        //策略原子列表，用于校验
        this.form.atomTable = this.atomTable;
        if (this.actionType === "ADD" || this.actionType === "COPY") {
          const response = await addRiskControl(this.form)
          this.$message.success("创建成功");
          // 通过创建后的风控ID进行策略原子组的提交
          if (this.atomTable.length > 0) {
            let ruleId = response.data.ruleId;
            this.atomTable.forEach(atom => {
              atom.ruleId = ruleId;
            })
            await addStrategyAtomGroupBatch({vos: this.atomTable})
          }
        } else {
          const response = await updateRiskControl(this.form)
          this.$message.success("编辑成功");
        }
        this.$refs.form.resetFields();
        this.$router.push("/riskControl/rule");
      }
    },
    cancel() {
      this.$router.push("/riskControl/rule");
    },
  },


};
</script>


<style
  lang="scss"
  scoped>
.box-container {
  padding: 20px;
  background: #fff;

  .el-form {
    width: 800px;
    margin-left: 30px;

    ::v-deep .el-form-item__label {
      font-weight: normal;
    }
  }

  .footer-container {
    padding-left: 120px;
  }
}
</style>
