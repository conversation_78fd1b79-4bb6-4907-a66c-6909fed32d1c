<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户订单号：</span>
            <el-input v-model="searchForm.orderNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">平台流水号：</span>
            <el-input v-model="searchForm.platTrxNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">商户编号：</span>
            <el-input v-model="searchForm.employerNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户名称：</span>
            <el-input v-model="searchForm.employerName" placeholder="模糊查询"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">风控接入点：</span>
            <el-select
              clearable
              v-model="searchForm.accessPoint"
            >
              <el-option
                v-for="(item, index) in $dict('RiskAccessPointEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="flex-item search-btn-group">
            <el-button
              type="primary"
              @click="search(true)"
            >查询</el-button>
            <el-button
              type="text"
              @click="clearField"
            >清空搜索条件</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <div class="button-group">
        <el-button
          type="primary"
          @click="multiResolve"
        >通过</el-button>
        <el-button
          type="danger"
          @click="multiReject"
        >拒绝</el-button>
      </div>
      <el-table
        :data="response.data"
        @selection-change="handleSelectionChange"
        @expand-change="handleExpand"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column type="expand">
          <template v-slot="{row}">
            <el-form
              inline
              label-width="100px"
              v-if="row.hangInfo"
            >
              <div>
                <el-form-item label="姓名：">
                  {{ row.hangInfo.receiveName }}
                </el-form-item>
                <el-form-item label="身份证号：">
                  {{ row.hangInfo.receiveIdCardNo }}&nbsp;&nbsp;
                  <span
                    class="func-content"
                    @click="goOrder(row.hangInfo.receiveIdCardNo)"
                  >查询该用户本月订单</span>
                </el-form-item>
                <el-form-item label="手机号：">
                  {{ row.hangInfo.receivePhoneNo }}
                </el-form-item>
                <el-form-item label="查看身份证：">
                  <el-button
                    v-if="row.hangInfo.idCardBackUrl"
                    type="text"
                    @click="previewIdCard(row.hangInfo.idCardBackUrl)"
                  >身份证背面照</el-button>
                  <el-button
                    v-if="row.hangInfo.idCardFrontUrl"
                    type="text"
                    @click="previewIdCard(row.hangInfo.idCardFrontUrl)"
                  >身份证正面照</el-button>
                  <el-button
                    v-if="row.hangInfo.idCardCopyUrl"
                    type="text"
                    @click="previewIdCard(row.hangInfo.idCardCopyUrl)"
                  >身份证复印件</el-button>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="发放方式：">
                  {{ $dictCode('ChannelTypeEnum', row.hangInfo.channelType).desc }}
                </el-form-item>
                <el-form-item label="收款账户：">
                  {{ row.hangInfo.receiveAccountNo }}<br />{{ row.hangInfo.bankName }}
                </el-form-item>
              </div>
              <div>
                <el-form-item label="挂单原因：">
                  {{ row.failedReason }}
                </el-form-item>
              </div>
            </el-form>
          </template>
        </el-table-column>
        <el-table-column
          label="序号"
          type="index"
          :index="getIndex"
          width="50"
        ></el-table-column>
        <el-table-column
          label="商户订单号 / 平台流水号"
          prop="mchOrderNo"
          width="170"
        >
          <template v-slot="{row}">
            {{ row.mchOrderNo }} <br>{{ row.platTrxNo }}
          </template>
        </el-table-column>

        <el-table-column
          label="创建时间"
          prop="createTime"
          width="150"
        >
          <p
            slot-scope="{row, column}"
            v-html="renderTime(row[column['property']])"
          ></p>
        </el-table-column>

        <el-table-column
          label="商户编号"
          prop="employerNo"
        >
          <template v-slot="{row}">
            {{ row.employerNo }} <br> {{ row.employerName }}
          </template>
        </el-table-column>

        <el-table-column
          label="当前年龄"
          prop="age"
        ></el-table-column>
        <el-table-column
          label="实发金额"
          prop="orderAmount"
        ></el-table-column>
        <el-table-column
          label="挂单原因"
          prop="failedReason"
          v-slot="{row}"
        >
          <el-tooltip>
            <span slot="content">{{ row.failedReason }}</span>
            <div class="no-wrap">
              {{ row.failedReason }}
            </div>
          </el-tooltip>
        </el-table-column>

        <el-table-column
          label="风控接入点"
          prop="accessPoint"
        >
          <template v-slot="{row}">
            {{ $dictCode('RiskAccessPointEnum', row.accessPoint).desc }}
          </template>
        </el-table-column>

        <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button
              type="text"
              @click="resolveOrder(row.id)"
            >通过</el-button>
            <el-button
              type="text"
              @click="rejectOrder(row.id)"
            >拒绝</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="pagination-container">
      <el-pagination
        background
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 50]"
        :total="response.totalRecord"
        @size-change="handleSizeChange"
        @current-change="handleCurrnetChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
  import { getPendingOrderList, operatePending } from '@/api/riskControl'
  import { getOrderItemByPlatTrxNo } from '@/api/order';
  export default {
    name: 'PmsRiskHang',
    data() {
      return {
        searchForm: { // 搜索表单
          orderNo: '',
          platTrxNo: '',
          employerNo: '',
          employerName: '',
          accessPoint: '',
        },
        response: { // 查询结果
          data: [],
          totalRecord: 0,
        },
        selection: [],
        pageSize: 10,
        pageCurrent: 1,
      }
    },
    mounted() {
      this.search();
    },
    methods: {
      clearField() {
        this.searchForm = {
          orderNo: '',
          platTrxNo: '',
          employerNo: '',
          employerName: '',
          accessPoint: '',
        }
      },
      getIndex(index) {
        return (this.pageCurrent - 1) * this.pageSize + index + 1;
      },
      handleSizeChange(val) {
        this.pageSize = val;
        this.search(true);
      },
      handleCurrnetChange(val) {
        this.pageCurrent = val;
        this.search();
      },
      async search(initFlag) {
        if (initFlag) {
          this.pageCurrent = 1;
        }
        const { data } = await getPendingOrderList({
          ...this.searchForm,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent,
          status: this.$dictFlag('PendingOrderOpeEnum', 'PENDING').code, // 未处理
        });
        data.data.forEach(i => i.hangInfo = null);
        this.response = data;
      },
      resolveOrder(ids) {
        this.$confirm('确定通过订单', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async (value) => {
          if (value) {
            const { data } = await operatePending({
              ids,
              operate: this.$dictFlag('PendingOrderOpeEnum', 'PASS').code
            });
            this.$message.success(data);
            this.search();
          }
        }).catch(() => { })
      },
      rejectOrder(ids) {
        this.$confirm('确定拒绝订单', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async (value) => {
          if (value) {
            const { data } = await operatePending({
              ids,
              operate: this.$dictFlag('PendingOrderOpeEnum', 'REJECT').code
            });
            this.$message.success(data);
            this.search();
          }
        }).catch(() => { })
      },
      multiResolve() {
        if (!this.selection.length) {
          return this.$message.error('请选择需要操作的挂单')
        }
        let ids = this.selection.map(item => item.id);
        ids = ids.join(',');
        this.resolveOrder(ids);
      },
      multiReject() {
        if (!this.selection.length) {
          return this.$message.error('请选择需要操作的挂单')
        }
        let ids = this.selection.map(item => item.id);
        ids = ids.join(',');
        this.rejectOrder(ids);
      },
      handleSelectionChange(val) {
        this.selection = val;
      },
      async handleExpand(row) {
        if (!row.hangInfo) {
          row.hangInfo = await this.getOrderInfo(row.platTrxNo)
        }
      },
      async getOrderInfo(platTrxNo) {
        const { data } = await getOrderItemByPlatTrxNo({ platTrxNo });
        return data
      },
      goOrder(receiveIdCardNo) {
        let newLink = `${process.env.VUE_APP_URL}#/trade/order/detail?receiveIdCardNo=${receiveIdCardNo}`;
        window.open(newLink);
      },
      async previewIdCard(url) {
        const fileMsg = await this.formatFileMsg(url)
        this.$preview(fileMsg.fileUrl)
      }
    }
  }
</script>

<style lang="scss" scoped>
  .box-container {
    padding: 20px 20px;

    .content-container {
      .button-group {
        margin: 16px 0;
      }
    }
  }
</style>
