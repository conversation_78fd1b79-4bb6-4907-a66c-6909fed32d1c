<template>
  <div class="box-container">
    <div class="search-container">
      <div class="flex-container">
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户订单号：</span>
            <el-input v-model="searchForm.orderNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">平台流水号：</span>
            <el-input v-model="searchForm.platTrxNo"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">商户编号：</span>
            <el-input v-model="searchForm.employerNo"></el-input>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item">
            <span class="flex-item__label">商户名称：</span>
            <el-input v-model="searchForm.employerName" placeholder="模糊查询"></el-input>
          </div>
          <div class="flex-item">
            <span class="flex-item__label">风控接入点：</span>
            <el-select
              clearable
              v-model="searchForm.accessPoint"
            >
              <el-option
                v-for="(item, index) in $dict('RiskAccessPointEnum')"
                :key="index"
                :label="item.desc"
                :value="item.code"
              >
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="flex-wrapper search-wrapper">
          <div class="flex-item-main">
            <span class="flex-item__label">创建时间：</span>
            <date-picker
              ref="datepicker"
              picker="separate"
              type="datetime"
              :start-time.sync="searchForm.beginDate"
              :end-time.sync="searchForm.endDate"
            ></date-picker>
          </div>
        </div>
        <div class="search-wrapper">
          <div class="flex-item search-btn-group">
            <el-button
              type="primary"
              @click="search(true)"
            >查询
            </el-button>
            <el-button @click="exportRecord">导出</el-button>
            <el-button
              type="text"
              @click="getExportList"
            >查看已导出列表
            </el-button>
            <el-button
              type="text"
              @click="clearField"
            >清空搜索条件
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content-container">
      <!-- <div class="button-group">
        <el-button type="primary">通过</el-button>
        <el-button type="danger">拒绝</el-button>
      </div> -->
      <el-table
        :data="response.data"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          label="序号"
          type="index"
          :index="getIndex"
          width="50"
        ></el-table-column>
        <el-table-column
          label="商户订单号 / 平台流水号"
          prop="mchOrderNo"
          width="170"
        >
          <template v-slot="{row}">
            {{ row.mchOrderNo }} <br>{{ row.platTrxNo }}
          </template>
        </el-table-column>

        <el-table-column
          label="商户编号"
          prop="employerNo"
        >
          <template v-slot="{row}">
            {{ row.employerNo }} <br> {{ row.employerName }}
          </template>
        </el-table-column>

        <el-table-column
          label="代征主体"
          prop="mainstayName"
        ></el-table-column>
        <el-table-column
          label="当前年龄"
          prop="age"
        ></el-table-column>

        <el-table-column
          label="实发金额"
          prop="orderAmount"
          width="150"
        >
          <template v-slot="{row}">
            {{ row.orderAmount | moneyFormat }}
          </template>
        </el-table-column>

        <el-table-column
          label="挂单原因"
          prop="failedReason"
          show-overflow-tooltip
        ></el-table-column>

        <el-table-column
          label="风控接入点"
          prop="accessPoint"
        >
          <template v-slot="{row}">
            {{ $dictCode('RiskAccessPointEnum', row.accessPoint).desc }}
          </template>
        </el-table-column>

        <el-table-column
          label="创建时间"
          prop="createTime"
          width="150"
        >
          <p
            slot-scope="{row, column}"
            v-html="renderTime(row[column['property']])"
          ></p>
        </el-table-column>
        <el-table-column
          label="更新时间"
          prop="updateTime"
          width="150"
        >
          <p
            slot-scope="{row, column}"
            v-html="renderTime(row[column['property']])"
          ></p>
        </el-table-column>
        <el-table-column
          label="操作人"
          prop="updateUser"
        ></el-table-column>

        <el-table-column
          label="订单状态"
          prop="operation"
        >
          <template v-slot="{row}">
            {{ $dictCode('PendingOrderOpeEnum', row.operation).desc }}
          </template>
        </el-table-column>

        <!-- <el-table-column label="操作">
          <template v-slot="{row}">
            <el-button type="text" @click="resolveOrder(row)">通过</el-button>
            <el-button type="text" @click="rejectOrder(row)">拒绝</el-button>
          </template>
        </el-table-column> -->
      </el-table>
    </div>

    <export-record ref="exportRecord"></export-record>

    <div class="pagination-container">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 50]"
        :total="response.totalRecord"
        @size-change="handleSizeChange"
        @current-change="handleCurrnetChange"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import { getPendingOrderList, exportRiskControl } from '@/api/riskControl'
import ExportRecord from "@/components/ExportRecord";

export default {
  name: 'PmsRiskResolve',
  components: {
    ExportRecord
  },
  data() {
    return {
      searchForm: { // 搜索表单
        orderNo: '',
        platTrxNo: '',
        employerNo: '',
        employerName: '',
        accessPoint: '',
      },
      response: { // 查询结果
        data: [],
        totalRecord: 0,
      },
      selection: [],
      pageSize: 10,
      pageCurrent: 1,
    }
  },
  mounted() {
    this.search();
  },
  methods: {
    clearField() {
      this.searchForm = {
        orderNo: '',
        platTrxNo: '',
        employerNo: '',
        employerName: '',
        accessPoint: '',
      }
      this.$refs.datepicker.clearTime()
    },
    getIndex(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.search(true);
    },
    handleCurrnetChange(val) {
      this.pageCurrent = val;
      this.search();
    },
    async search(initFlag) {
      if (initFlag) {
        this.pageCurrent = 1;
      }
      const {data} = await getPendingOrderList({
        ...this.searchForm,
        pageSize: this.pageSize,
        pageCurrent: this.pageCurrent,
        status: this.$dictFlag('PendingOrderOpeEnum', 'PROCESSED').code, // 已处理
      });
      this.response = data;
    },
    handleSelectionChange(val) {
      this.selection = val;
    },
    async exportRecord() {
      await exportRiskControl({
        ...this.searchForm,
        status: this.$dictFlag('PendingOrderOpeEnum', 'PROCESSED').code, // 已处理
      })
      this.$message.success('导出成功，请到导出列表查看');
    },
    getExportList() {
      this.$refs.exportRecord.isShow = true;
      this.$refs.exportRecord.getExportRecord(50)
    }
  }
}
</script>

<style
  lang="scss"
  scoped
>
.box-container {
  padding: 20px 20px;

  .content-container {
    .button-group {
      margin: 16px 0;
    }
  }
}
</style>
