<template>
  <div class="box-container">
    <el-form label-width="150px" :model="form" ref="form" :rules="rules">
      <el-form-item label="商户编号：" prop="mchNo">
        <el-select v-model="form.mchNo" @change="handleChange" filterable>
          <el-option
            v-for="item in merchant"
            :key="item.mchNo"
            :value="item.mchNo"
            :label="item.mchName"
            clearable
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="用户纬度：" prop="userLatitude">
        <el-select v-model="form.userLatitude" @change="handleUseChange">
          <el-option
            v-for="item in $dict('UserLatitudeEnum')"
            :key="item.code"
            :value="Number(item.code)"
            :label="item.desc"
            clearable
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item ref="username" label="用户姓名：" prop="username" v-if="form.userLatitude === 100 || form.userLatitude === 300">
        <el-input
          v-width="300"
          v-model="form.username"
          clearable></el-input>
      </el-form-item>
      <el-form-item ref="idCardNumber" label="用户身份证号：" prop="idCardNumber" v-if="form.userLatitude === 200 || form.userLatitude === 300 ">
        <el-input
          v-width="300"
          v-model="form.idCardNumber"
          clearable></el-input>
      </el-form-item>
      <el-form-item label="入库原因：" prop="warehousingReason">
        <el-input
          v-width="300"
          v-model="form.warehousingReason"
          type="textarea"
          :rows="5"></el-input>
      </el-form-item>
      <el-form-item label="灰名单规则编辑：" prop="nameStrategyAtomGroups">
        <el-table :data="form.nameStrategyAtomGroups">
          <el-table-column label="策略原子组列表" width="200">
            <template v-slot="{row}">
              <div>{{ row.supplierName }}</div>
              <template v-if="row.nameStrategyAtoms">
                <div v-for="(item, index) in row.nameStrategyAtoms" :key="index">
                  {{ $dictCode('StrategyAtomVariableEnum', item.variable).desc }}
                  {{ $dictCode('StrategyAtomOperatorEnum', item.operator).desc }}
                  {{ item.constant }};
                </div>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="权重" prop="weight" width="100"></el-table-column>
          <el-table-column label="管控原子" prop="controlAtom" width="100">
            <template v-slot="{row}">
              {{ $dictCode('ControlAtomEnum', row.controlAtom).desc }}
            </template>
          </el-table-column>
          <el-table-column label="描述" prop="remark"></el-table-column>
          <el-table-column label="操作" prop="controlAtom">
            <template v-slot="{$index}">
              <el-button type="text" @click="onEdit($index)">编辑</el-button>
              <el-button type="text" @click="onDelete($index)">删除</el-button>
            </template>
          </el-table-column>

          <template v-slot:append>
            <div class="text-center">
              <el-button type="text" @click="addAtom">+ 新建</el-button>
            </div>
          </template>
        </el-table>
      </el-form-item>
    </el-form>

    <div class="text-center">
      <el-button type="primary" @click="submit">确定</el-button>
      <el-button @click="cancel">取消</el-button>
    </div>

    <atom-dialog :type="viewType" ref="atom" :atom="editItem" @save="saveAtomData"></atom-dialog>
  </div>
</template>

<script>
  import { validateParams } from '@/utils/validate'
  import { createPeople, updatePeople } from '@/api/riskControl'
  import { getAllMerchant } from '@/api/merchant';
  import AtomDialog from './AtomDialog'
  export default {
    name: 'risk-people-form',
    components: {
      AtomDialog,
    },
    data() {
      return {
        form: {
          idCardNumber: '',
          username: '',
          nameStrategyAtomGroups: [],
        },
        merchant: [],
        rules: {
          mchNo: [{ required: true, message: '请选择商户', trigger: 'change' }],
          userLatitude: [{ required: true, message: '请选择用户纬度', trigger: 'change' }],
          username: [{ required: true, message: '请输入用户名称', trigger: 'blur' }],
          idCardNumber: [
            { required: true, message: '请输入身份证号', trigger: 'blur' },
            { validator: validateParams({ type: 'IdCard', msg: '请输入正确格式身份证'}), trigger: 'blur'}
          ],
          nameStrategyAtomGroups: [
            { required: true, message: '请补充策略原子组', trigger: 'change' },
            { type: 'array', message: '请补充策略原子组'},
          ],
        },
        editItem: null,
        editIndex: -1,
        viewType: 'add'
      }
    },
    computed: {
      type() {
        return this.$route.query.type || 'add'
      }
    },
    mounted() {
      this.getAllMerchant()

      if (this.type === 'edit') {
        this.form = JSON.parse(sessionStorage.getItem('riskPeople'))
      }
    },
    methods: {
      async getAllMerchant() {
        const {data} = await getAllMerchant({
          merchantType: '100'
        });
        this.merchant = data
      },
      addAtom() {
        this.viewType = 'add';
        this.editIndex = -1;
        this.$refs.atom.open()
      },
      handleChange(val) {
        for(let i = 0; i < this.merchant.length; i++) {
          if (val === this.merchant[i].mchNo) {
            this.form.mchName = this.merchant[i].mchName;
            break;
          }
        }
      },
      async handleUseChange(val) {
        if (val === 100) {
          this.form.idCardNumber = ''
        } else if (val === 200) {
          this.form.username = ''
        }
        await this.$nextTick();
        this.$refs.username && this.$refs.username.clearValidate();
        this.$refs.idCardNumber && this.$refs.idCardNumber.clearValidate();
      },
      saveAtomData(data) {
        if (this.editIndex < 0) {
          if (!this.form.nameStrategyAtomGroups) {
            this.form.nameStrategyAtomGroups = [];
          }
          this.form.nameStrategyAtomGroups.push(data)
        } else {
          this.form.nameStrategyAtomGroups.splice(this.editIndex, 1, data)
        }
      },
      onEdit(index) {
        this.viewType = 'edit';
        this.editIndex = index;
        this.editItem = this.form.nameStrategyAtomGroups[index];
        this.$refs.atom.open();
      },
      onDelete(index) {
        this.editIndex = -1;
        this.form.nameStrategyAtomGroups.splice(index, 1)
      },
      cancel() {
        this.$router.back()
      },
      async submit() {
        try {
          await this.$refs.form.validate()
          let api = this.type === 'add' ? createPeople : updatePeople;
          const { data } = await api(this.form);
          data && this.$message.success(data);
          this.$router.replace('/riskControl/people/list')
        } catch { return }
      },
    },
  }
</script>

<style scoped lang="scss">

</style>
