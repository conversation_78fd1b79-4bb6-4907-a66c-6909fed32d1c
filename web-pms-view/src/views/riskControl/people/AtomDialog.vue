<template>
  <el-dialog
    :visible.sync="show"
    :close-on-click-modal="false"
    :before-close="closeForm"
    :title="(type==='add'&&'新增') || (type==='edit'&&'编辑')||''"
    width="800px"
    top="0"
  >
    <el-form
      ref="form"
      :model="form"
      label-width="120px"
      :rules="rules"
    >
      <el-form-item
        label="供应商"
        prop="supplierNo"
      >
        <el-select
          clearable
          v-model="form.supplierNo"
          @change="handleChange"
        >
          <el-option
            v-for="item in supplier"
            :key="item.mchNo"
            :value="item.mchNo"
            :label="item.mchName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="策略原子组"
        prop="nameStrategyAtoms"
      >
        <el-button @click="addAtom">添加</el-button>
        <el-table :data="form.nameStrategyAtoms">
          <el-table-column
            v-slot="{$index}"
            width="50"
          >
            <span v-if="$index > 0">且</span>
          </el-table-column>
          <el-table-column>
            <template v-slot="{$index, row}">
              <el-select
                clearable
                v-model="row.variable"
                @change="handelVarChange($event, $index)"
              >
                <el-option
                  v-for="item in $dict('StrategyAtomVariableEnum')"
                  :key="item.code"
                  :value="Number(item.code)"
                  :label="item.desc"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column>
            <template slot-scope="scope">
              <el-select
                clearable
                v-model="scope.row.operator"
              >
                <el-option
                  v-for="item in $dict('StrategyAtomOperatorEnum')"
                  :key="item.code"
                  :value="Number(item.code)"
                  :label="item.desc"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column>
            <template v-slot="{row}">
              <el-input
                v-model="row.constant"
                v-if="row.variable == 400"
                @change="handleContentChange($event, row)"
              ></el-input>
              <el-input
                v-model="row.constant"
                v-else
                :disabled="row.variable == 500 || row.variable == 600"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column width="50">
            <template v-slot="{$index}">
              <el-button
                type="text"
                @click="deleteAtom($index)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>
      <el-form-item
        label="管控原子"
        prop="controlAtom"
      >
        <el-radio-group v-model="form.controlAtom">
          <el-radio
            v-for="item in $dict('ControlAtomEnum')"
            :key="item.code"
            :label="Number(item.code)"
          >{{ item.desc }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="权重"
        prop="weight"
      >
        <el-input v-model="form.weight"></el-input>
      </el-form-item>
      <el-form-item
        label="描述"
        prop="remark"
      >
        <el-input v-model="form.remark"></el-input>
      </el-form-item>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        type="text"
        @click="closeForm"
      >取消
      </el-button>
      <el-button
        :loading="loading"
        type="primary"
        @click="doSubmit"
      >确认
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getAllSuppliers } from '@/api/merchant';
import { toCDB } from '@/utils';

export default {
  props: {
    atom: {
      type: Object,
      default: () => ({})
    },
    type: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      ruleId: '',
      loading: false,
      show: false,
      form: {
        supplierNo: '',
        supplierName: '',
        weight: '',
        controlAtom: '',
        remark: '',
        nameStrategyAtoms: [],
      },
      rules: {
        supplierNo: [{ required: true, message: '请选择供应商', trigger: 'change' }],
        weight: [
          { required: true, message: '请输入权重', trigger: 'blur' },
          {
            validator: (rule, val, cb) => {
              if (!val) {
                cb(new Error('请输入正确格式权重'))
              } else {
                cb()
              }
            }, trigger: 'blur'
          }
        ],
        remark: [{ required: true, message: '请输入描述', trigger: 'blur' }],
        controlAtom: [{ required: true, message: '请选择管控因子', trigger: 'change' }],
        nameStrategyAtoms: [
          { required: true, message: '请完善策略原子组', trigger: 'change' },
          {
            validator: (rule, val, cb) => {
              for (let i = 0; i < val.length; i++) {
                const item = val[i];
                if (item.variable === ''
                  || item.operator === ''
                  || (item.constant === ''
                    && item.variable !== '500'
                    && item.variable !== '600'
                    && item.variable !== '700'
                    && item.variable !== '800')) {
                  return cb(new Error('请正确填写策略原子组的内容'))
                }
              }
              cb()
            }, trigger: 'blur'
          }
        ],
      },

      supplier: [],
    }
  },
  methods: {
    open() {
      this.show = true;
      this.getSupplier();
      this.$nextTick(() => {
        if (this.type === 'edit') {
          this.form = { ...this.atom }
        }
      })
    },
    async getSupplier() {
      const { data } = await getAllSuppliers()
      this.supplier = data;
    },
    addAtom() {
      this.form.nameStrategyAtoms.push({
        variable: '',
        operator: '',
        constant: ''
      })
    },
    async doSubmit() {
      try {
        await this.$refs.form.validate();
        this.doSave()
      } catch (e) {
      }
    },
    doSave() {
      this.$emit('save', { ...this.form });
      this.closeForm();
    },
    closeForm() {
      this.show = false
      this.loading = false
      this.$refs.form.resetFields()
    },
    deleteAtom(index) {
      this.form.nameStrategyAtoms.splice(index, 1);
    },
    handleChange(val) {
      for (let i = 0; i < this.supplier.length; i++) {
        if (val === this.supplier[i].mchNo) {
          this.form.supplierName = this.supplier[i].mchName;
          break;
        }
      }
    },
    handleContentChange(val, row) {
      row.constant = toCDB(val)
    },
    handelVarChange(val, index) {
      if (val == 500 || val == 600) {
        this.form.nameStrategyAtoms[index].constant = ''
      }
    },
  }
}
</script>
