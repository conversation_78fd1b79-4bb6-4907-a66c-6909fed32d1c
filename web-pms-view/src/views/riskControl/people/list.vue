<template>
  <div class="box-container">
    <div class="search-container flex-container">
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">商户名称：</span>
          <el-select
            v-model="form.mchNo"
            clearable
            filterable
          >
            <el-option
              v-for="item in merchant"
              :key="item.mchNo"
              :label="item.mchName"
              :value="item.mchNo"
            ></el-option>
          </el-select>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">供应商名称：</span>
          <el-select
            v-model="form.supplierName"
            clearable
            filterable
          >
            <el-option
              v-for="item in supplier"
              :key="item.mchNo"
              :label="item.mchName"
              :value="item.mchName"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item">
          <span class="flex-item__label">用户名称：</span>
          <el-input clearable v-model="form.username" placeholder="模糊查询" ></el-input>
        </div>
        <div class="flex-item">
          <span class="flex-item__label">用户身份证：</span>
          <el-input clearable v-model="form.idCardNumber"></el-input>
        </div>
      </div>
      <div class="search-wrapper flex-wrapper">
        <div class="flex-item-main">
          <span class="flex-item__label">入库时间：</span>
          <date-picker
            picker="separate"
            type="datetime"
            ref="date"
            :start-time.sync="form.createTimeBegin"
            :end-time.sync="form.createTimeEnd"
          ></date-picker>
        </div>
      </div>
      <div class="search-wrapper search-btn-group">
        <el-button type="primary" @click="search(true)">查询</el-button>
        <el-button type="text" @click="clearForm">清空筛选条件</el-button>
      </div>

    </div>

    <div class="func-container">
      <el-button
        type="primary"
        @click="onAdd"
      >添加用户</el-button>
      <el-button
        type="danger"
        :disabled="selection.length === 0"
        @click="onMultiDelete"
      >批量移除
      </el-button>
    </div>

    <div class="content-container">
      <el-table
        :data="list"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column label="序号" type="index" :index="getIndex"></el-table-column>
        <el-table-column label="商户编号" width="150" prop="mchNo"></el-table-column>
        <el-table-column label="商户名称" width="150" prop="mchName"></el-table-column>
        <el-table-column label="用户名称" width="150" prop="username"></el-table-column>
        <el-table-column label="身份证号" width="150" prop="idCardNumber"></el-table-column>
        <el-table-column label="入库时间" width="150" prop="createTime">
          <template v-slot="{row}">
            <div v-html="renderTime(row.createTime)"></div>
          </template>
        </el-table-column>
        <el-table-column label="入库原因" width="120" prop="warehousingReason"></el-table-column>
        <el-table-column label="供应商" width="150" prop="supplierName"></el-table-column>
        <el-table-column label="管控原子" prop="controlAtomName"></el-table-column>
        <el-table-column label="操作" fixed="right">
          <template v-slot="{row}">
            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" @click="handleDelete(row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-footer class="pagination-container">
      <el-pagination
        ref="pagination"
        :total="totalRecord"
        :current-page.sync="pageCurrent"
        :page-size.sync="pageSize"
        :page-sizes="[10, 50]"
        layout="total, sizes, prev, pager, next, jumper"
        background
        @size-change="search(true)"
        @current-change="search()"
      ></el-pagination>
    </el-footer>

  </div>
</template>

<script>
  import { getAllMerchant } from '@/api/merchant';
  import { peopleList, deletePeople } from '@/api/riskControl';

  export default {
    name: 'risk-people-list',
    data() {
      return {
        form: {},
        merchant: [],
        supplier: [],

        list: [],
        totalRecord: 0,
        pageSize: 10,
        pageCurrent: 1,

        selection: [],
      }
    },
    mounted() {
      Promise.all([
        this.getAllMerchant(),
        this.getAllSupplier(),
      ])
      this.search();
    },
    methods: {
      async getAllMerchant() {
        const {data} = await getAllMerchant({
          merchantType: '100'
        })
        this.merchant = data
      },
      async getAllSupplier() {
        const {data} = await getAllMerchant({
          merchantType: '101'
        })
        this.supplier = data
      },
      async search(init) {
        if (init === true) {
          this.pageCurrent = 1
        }
        const {data: {data, totalRecord}} = await peopleList({
          ...this.form,
          pageSize: this.pageSize,
          pageCurrent: this.pageCurrent
        });
        this.list = data;
        this.totalRecord = totalRecord;
      },
      clearForm() {
        this.form = {};
        this.$refs.date.clearTime()
      },
      getIndex(index) {
        return this.pageSize * (this.pageCurrent - 1) + index + 1
      },
      handleSelectionChange(val) {
        this.selection = val;
      },
      async handleDelete(row) {
        let length = typeof row === 'string' ? this.selection.length : 1;
        let ids = typeof row === 'string' ? row : row.id;
        try {
          await this.$confirm(`删除操作不可逆？确定删除这 ${length} 条数据`, '删除');
          const { data } = await deletePeople({
            ids
          });
          data && this.$message.success(data)
          this.search();
        } catch (e) {
          return;
        }
      },
      async handleEdit(row) {
        sessionStorage.setItem('riskPeople', JSON.stringify(row));
        this.goForm('edit')
      },
      onMultiDelete() {
        let idList = this.selection.map(item => item.id).join(',');
        this.handleDelete(idList)
      },
      onAdd() {
        this.goForm('add')
      },
      goForm(type) {
        this.$router.push({
          path: '/riskControl/people/form',
          query: {
            type
          }
        })
      },

    },
  }
</script>

<style scoped lang="scss">

</style>
