import request from '@/utils/request.js';

export function listGlobalLock(data, params) {
  return request({
    url: '/globalLock/listGlobalLock',
    method: 'post',
    data,
    params
  })
}

export function unlockForce(data) {
  return request({
    url: '/globalLock/unlockForce',
    method: 'post',
    data
  })
}

export function unlockAndDelete(data) {
  return request({
    url: '/globalLock/unlockAndDelete',
    method: 'post',
    data
  })
}
