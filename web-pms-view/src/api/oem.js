import request from "@/utils/request";

export function listOem(data) {
  return request({
    url: '/tenant/listPage',
    method: 'post',
    data
  })
}

export function addOem(data) {
  return request({
    url: '/tenant/add',
    method: 'post',
    data
  })
}

export function updateOem(data) {
  return request({
    url: '/tenant/update',
    method: 'post',
    data
  })
}

export function changeOemStatus(data) {
  return request({
    url: '/tenant/updateStatus',
    method: 'post',
    data
  })
}

export function deleteOem(id) {
  return request({
    url: `/tenant/delete/${ id }`,
    method: 'post',
  })
}
