import request from '@/utils/request.js';

// 分页查询代征关系
export function getLevyList(data) {
  return request({
    url: '/employerMainstayRelation/listPage',
    method: 'post',
    data
  })
}

// 创建代征关系
export function createLevyRelation(data) {
  return request({
    url: '/employerMainstayRelation/create',
    method: 'post',
    data
  })
}

// 删除代征关系
export function deleteLevyRelation(data) {
  return request({
    url: '/employerMainstayRelation/delete',
    method: 'post',
    data
  })
}

// 更新代征关系
export function updateLevyRelation(data) {
  return request({
    url: '/employerMainstayRelation/update',
    method: 'post',
    data
  })
}

// 更新代征关系状态
export function changeLevyRelationStatus(data) {
  return request({
    url: '/employerMainstayRelation/changeStatus',
    method: 'post',
    data
  })
}

// 获取支付通道
export function getPayChannelList(data) {
  return request({
    url: '/payChannel/listPage',
    method: 'post',
    data
  })
}

// 创建支付通道
export function createPayChannel(data) {
  return request({
    url: '/payChannel/create',
    method: 'post',
    data
  })
}

// 更新支付通道
export function updatePayChannel(data) {
  return request({
    url: '/payChannel/update',
    method: 'post',
    data
  })
}

// 修改支付通道状态
export function changePayChannelStatus(data) {
  return request({
    url: '/payChannel/changeStatus',
    method: 'post',
    data
  })
}

// 删除支付通道
export function deletePayChannel(data) {
  return request({
    url: '/payChannel/delete',
    method: 'post',
    data
  })
}

// 获取供应商支付账户
export function getMainstayPayAccountList(data) {
  return request({
    url: '/mainstayChannelRelation/listPage',
    method: 'post',
    data
  })
}

export function exportEmployerMainstayRelation(data) {
  return request({
    url: '/employerMainstayRelation/exportEmployerMainstayRelation',
    method: 'post',
    data
  })
}

// 获取单个供应商支付账户
export function getSingerMainstayPayAccount(data) {
  return request({
    url: '/mainstayChannelRelation/getByMainstayNo',
    method: 'get',
    params: data
  })
}

// 创建供应商支付账户
export function createMainstayPayAccount(data) {
  return request({
    url: '/mainstayChannelRelation/create',
    method: 'post',
    data
  })
}

// 更新供应商支付账户
export function updateMainstayPayAccount(data) {
  return request({
    url: '/mainstayChannelRelation/update',
    method: 'post',
    data
  })
}

// 删除供应商支付账户
export function deleteMainstayPayAccount(data) {
  return request({
    url: '/mainstayChannelRelation/deleteByMainstayNo',
    method: 'post',
    data
  })
}

// 获取用工企业支付账户
export function getMerchantPayAccountList(data) {
  return request({
    url: '/employerAccountInfo/listPage',
    method: 'post',
    data
  })
}

// 获取单个用工企业支付账户
export function getSingerMerchantPayAccount(data) {
  return request({
    url: '/employerAccountInfo/getByMainstayNoAndEmployerNo',
    method: 'post',
    data
  })
}

// 修改用工企业支付账户
export function updateMerchantPayAccount(data) {
  return request({
    url: '/employerAccountInfo/update',
    method: 'post',
    data
  })
}

// 查询余额
export function getBalance(data) {
  return request({
    url: '/employerAccountInfo/getAmountByMainstayNoAndEmployerNo',
    method: 'post',
    data
  })
}

// 报备通道
export function submitChannel(data) {
  return request({
    url: '/report/report',
    method: 'post',
    data
  })
}

// 获取通道报备记录
export function getChannelRecordList(data) {
  return request({
    url: '/reportChannelRecord/listPage',
    method: 'post',
    data
  })
}

// 解除签约
export function unsign(data) {
  return request({
    url: '/report/unsign',
    method: 'post',
    data
  })
}

//同步商户信息至汇聚
export function syncJoinpayInfo(data) {
  return request({
    url: '/report/modify',
    method: 'post',
    data
  })
}

// 上传分帐方图片
export function uploadPic(data) {
  return request({
    url: '/report/uploadPic',
    method: 'post',
    data
  })
}

// 批量操作代征关系
export function batchUpdateLevy(data) {
  return request({
    url: '/employerMainstayRelation/batchUpdate',
    method: 'post',
    data
  })
}

export function cheackMchSignAndPicUpload(data) {
  return request({
    url: '/report/cheackMchSignAndPicUpload',
    method: 'post',
    data
  })
}
