import request from '@/utils/request.js';

export function getSaleCostRuleList(data) {
  return request({
    url: '/salesCostRule/listPage',
    method: 'post',
    data
  })
}

export function adjustAccount(data) {
  return request({
    url: '/accountInvoice/adjustAccount',
    method: 'post',
    data
  })
}

// 发票账户查询列表
export function getAccountInvoiceList(data, params) {
  return request({
    url: '/accountInvoice/listAccountInvoice',
    method: 'post',
    data,
    params
  })
}

// 发票账户统计
export function statisticAccountInvoice(data) {
  return request({
    url: '/accountInvoice/statisticsAccountInvoice',
    method: 'post',
    data,
  })
}

// 待处理账户发票列表
export function getPendingAccountInvoiceList(data, params) {
  return request({
    url: '/accountInvoice/listProcessPending',
    method: 'post',
    data,
    params
  })
}

// 待处理账户发票信息
export function viewProcessPending(data, params) {
  return request({
    url: '/accountInvoice/viewProcessPending',
    method: 'post',
    data,
    params
  })
}

// 账务明细列表
export function getAccountInvoiceDetail(data, params) {
  return request({
    url: '/accountInvoice/listProcessDetail',
    method: 'post',
    data,
    params
  })
}

// 账务结果列表
export function getAccountInvoiceResult(data, params) {
  return request({
    url: '/accountInvoice/listProcessResult',
    method: 'post',
    data,
    params
  })
}

// 账务结果详情
export function viewProcessResult(data, params) {
  return request({
    url: '/accountInvoice/viewProcessResult',
    method: 'post',
    data,
    params
  })
}

export function resendProcessResultCallback(data, params) {
  return request({
    url: '/accountInvoice/resendProcessResultCallback',
    method: 'post',
    data,
    params
  })
}

export function auditProcessResult(params) {
  return request({
    url: '/accountInvoice/auditProcessResult',
    method: 'get',
    params
  })
}

export function exportAccountInvoiceList(data, params) {
  return request({
    url: '/accountInvoice/exportAccountInvoice',
    method: 'post',
    data,
    params
  })
}
