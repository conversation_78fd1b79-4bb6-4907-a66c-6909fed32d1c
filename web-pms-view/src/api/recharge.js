import request from '@/utils/request.js';

export function getRechargeRecord(data) {
  return request({
    url: '/recharge/listRecharge',
    method: 'post',
    data
  })
}

// 导出充值记录列表
export function getRecordList(data) {
  return request({
    url: '/recharge/exportRechargeRecord',
    method: 'post',
    data
  })
}

// 批量导出
export function getMultiRecordList(data) {
  return request({
    url: '/recharge/exportRechargeRecordZip',
    method: 'post',
    data
  })
}

// 银行卡充值记录操作
export function editRecord(data) {
  return request({
    url: '/recharge/editRecord',
    method: 'post',
    data
  })
}

export function sumRechargeRecord(data) {
  return request({
    url: '/recharge/sumRechargeRecord',
    method: 'post',
    data
  })
}

