import request from '@/utils/request'

export function exportMerchantStatistics(data) {
  return request({
    url: '/merchantFeeOrder/exportMerchantStatistics',
    method: 'post',
    data,
  })
}

export function merchantStatistics(data) {
  return request({
    url: '/merchantFeeOrder/merchantStatistics',
    method: 'post',
    data,
  })
}

// 查询商户计费订单列表
export function getMerchantFeeOrderList(data) {
  return request({
    url: '/merchantFeeOrder/listPage',
    method: 'post',
    data,
  })
}

// 导出商户计费订单明细
export function exportMerchantFeeOrder(data) {
  return request({
    url: '/merchantFeeOrder/exportMerchantFeeOrder',
    method: 'post',
    data,
  })
}

// 查询成本计费订单列表
export function getVendorFeeOrderList(data) {
  return request({
    url: '/vendorFeeOrder/listPage',
    method: 'post',
    data,
  })
}

export function vendorStatistics(data) {
  return request({
    url: '/vendorFeeOrder/vendorStatistics',
    method: 'post',
    data,
  })
}

// 导出成本计费订单明细
export function exportVendorFeeOrder(data) {
  return request({
    url: '/vendorFeeOrder/exportVendorFeeOrder',
    method: 'post',
    data,
  })
}

// 导出供应商订单汇总
export function exportVendorStatistics(data) {
  return request({
    url: '/vendorFeeOrder/exportVendorStatistics',
    method: 'post',
    data,
  })
}

// 查询销售计费订单列表
export function getSalesFeeOrderList(data) {
  return request({
    url: '/salesFeeOrder/listPage',
    method: 'post',
    data,
  })
}

// 导出销售计费订单明细
export function exportSalesFeeOrder(data) {
  return request({
    url: '/salesFeeOrder/exportSalesFeeOrder',
    method: 'post',
    data,
  })
}

// 导出销售订单汇总
export function exportSalerStatistics(data) {
  return request({
    url: '/data/exportSalerStatistics',
    method: 'post',
    data
  })
}

// 销售成本订单统计
export function sumSalesFeeOrder(data) {
  return request({
    url: '/salesFeeOrder/sumSalesFeeOrder',
    method: 'post',
    data
  })
}

// 供应商成本订单统计
export function sumVendorFeeOrder(data) {
  return request({
    url: '/vendorFeeOrder/sumVendorFeeOrder',
    method: 'post',
    data
  })
}

// 产品计费订单统计
export function sumMerchantFeeOrder(data) {
  return request({
    url: '/merchantFeeOrder/sumMerchantFeeOrder',
    method: 'post',
    data
  })
}
