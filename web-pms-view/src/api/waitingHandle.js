import request from '@/utils/request.js';

/* 审批流程接口 */
export function getReceived(data) {
  return request({
    url: '/approval/listReceived',
    method: 'get',
    params: data
  })
}

export function getSend(data) {
  return request({
    url: '/approval/listSend',
    method: 'get',
    params: data
  })
}

export function getPending(data) {
  return request({
    url: '/approval/listPending',
    method: 'get',
    params: data
  })
}

export function getDetailProcessInfo(data) {
  return request({
    url: '/approval/getApproval',
    method: 'get',
    params: data
  })
}

export function getDetailProcessNode(data) {
  return request({
    url: '/approvalDetail/getDetail',
    method: 'get',
    params: data
  })
}

export function handleProcess(data) {
  return request({
    url: '/approvalDetail/handleDetail',
    method: 'post',
    data
  })
}

export function cancelProcess(data) {
  return request({
    url: '/approval/cancelApproval',
    method: 'post',
    data
  })
}

// 更新附加信息
export function updateExtInfo(data) {
  return request({
    url: '/approval/updateExtInfo',
    method: 'post',
    data
  })
}
