import request from '@/utils/request'

export function getProcDefList(data) {
  return request({
    url: '/procDef/listProcDef',
    method: 'post',
    data
  })
}

export function toggleProcDefStatus(data) {
  return request({
    url: '/procDef/updateState',
    method: 'post',
    data
  })
}

export function deleteProcDef(data) {
  return request({
    url: '/procDef/deleteProcDef',
    method: 'post',
    data
  })
}

export function deployProcDef(data) {
  return request({
    url: '/procDef/deployByUpload',
    method: 'post',
    data,
  })
}

export function updateProcDef(data) {
  return request({
    url: '/procDef/update',
    method: 'post',
    data
  })
}

export function getProcById(params) {
  return request({
    url: '/procDef/getById',
    method: 'get',
    params
  })
}

// 部署
export function deployByUpload(data) {
  return request({
    url: '/procDef/deployByUpload',
    method: 'post',
    data,
  })
}
