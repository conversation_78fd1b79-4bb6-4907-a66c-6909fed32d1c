import request from '@/utils/request.js';

/* 电子签约记录 */
export function getSignList(data) {
  return request({
    url: '/sign/listPage',
    method: 'post',
    data
  })
}

// 导出电子签约
export function exportSignRecord(data) {
  return request({
    url: '/sign/exportSignRecord',
    method: 'post',
    data,
  })
}

// 获取模板列表
export function getTemplateList(data) {
  return request({
    url: '/signTemplate/list',
    method: "post",
    data
  })
}

// 添加
export function addSignTemplate(data) {
  return request({
    url: '/signTemplate/add',
    method: 'post',
    data
  })
}

// 修改
export function editSignTemplate(data) {
  return request({
    url: '/signTemplate/modify',
    method: 'post',
    data
  })
}

// 删除
export function deleteSignTemplate(data) {
  return request({
    url: '/signTemplate/del',
    method: 'post',
    data,
  })
}

// 上传模板文件
export function uploadSignFile(data) {
  return request({
    url: '/signTemplate/upload',
    method: 'post',
    data,
  })
}

// 上传前缓存
export function cacheSignForm(data) {
  return request({
    url: '/signTemplate/cache',
    method: 'post',
    data
  })
}

// 取出缓存
export function getCacheForm() {
  return request({
    url: '/signTemplate/get',
    method: 'get'
  })
}

// 清除缓存
export function clearCacheForm() {
  return request({
    url: '/signTemplate/remove',
    method: 'post'
  })
}

// 组件库
export function getDragComponent(data) {
  return request({
    url: '/signTemplate/component',
    method: 'get',
    data
  })
}

// 电子签约专用
export function getEmployer() {
  return request({
    url: '/merchant/listActiveEmployer',
    method: 'get',
  })
}

export function getMainstay() {
  return request({
    url: '/merchant/templateActiveMainstayList',
    method: 'get',
  })
}

// 上传图片
export function addSignImage(data) {
  return request({
    url: '/sign/addSignImages',
    method: 'post',
    data
  })
}

export function deleteById(data) {
  return request({
    url: '/sign/delete/' + data,
    method: 'get'
  })
}

// 上传协议文件
export function uploadSignFileUrl(data) {
  return request({
    url: '/sign/uploadFile',
    method: 'post',
    data
  })
}
