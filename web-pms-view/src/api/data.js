import request from '@/utils/request'

// 企业发单数据
export function analyzeMerchant(data) {
  return request({
    url: '/analyze/merchantList',
    method: 'post',
    data,
  })
}
// 自由职业者发单数据
export function analyzeFreelance(data) {
  return request({
    url: '/analyze/freelanceList',
    method: 'post',
    data,
  })
}

// 企业统计数据
export function countMerchant(data) {
  return request({
    url: '/analyze/countMerchant',
    method: 'post',
    data,
  })
}

// 自由职业者统计数据
export function countFreelance(data) {
  return request({
    url: '/analyze/countFreelance',
    method: 'post',
    data,
  })
}

// 上传身份证
export function uploadIdCard(data) {
  return request({
    url: '/analyze/uploadIdCard',
    method: 'post',
    data,
  })
}

// 签约
export function sign(data) {
  return request({
    url: '/analyze/sign',
    method: 'post',
    data,
  })
}

// 批量签约
export function signList(data) {
  return request({
    url: '/analyze/signList',
    method: "post",
    data
  })
}

// 导出用工企业数据
export function merchantInfoExport(data) {
  return request({
    url: '/analyze/merchantInfoExport',
    method: 'post',
    data,
  })
}

// 导出自由职业数据
export function freelanceExport(data) {
  return request({
    url: '/analyze/freelanceExport',
    method: 'post',
    data,
  })
}

// 月度概览
export function getMonthlyOverview(params) {
  return request({
    url: '/overview/getMonthlyOverview',
    method: 'get',
    params
  })
}
// 核心指标
export function getCoreIndex(data) {
  return request({
    url: '/data/coreIndex',
    method: 'post',
    data
  })
}
// 主体指标
export function salerData(params) {
  return request({
    url: '/data/salerData',
    method: 'get',
    params,
  })
}

export function supplyData(params) {
  return request({
    url: '/data/supplyData',
    method: 'get',
    params,
  })
}

export function merchantData(params) {
  return request({
    url: '/data/merchantData',
    method: 'get',
    params,
  })
}

export function agentData(params) {
  return request({
    url: '/data/agentData',
    method: 'get',
    params,
  })
}

export function listSalerOrdersGroupedByMch(data) {
  return request({
    url: '/data/listSalerOrdersGroupedByMch',
    method: 'post',
    data
  })
}

// 销售数据
export function listSalerOrdersDaily(data) {
  return request({
    url: '/data/listSalerOrdersGroupedByMchDaily',
    method: 'post',
    data
  })
}
export function listSalerOrdersMonthly(data) {
  return request({
    url: '/data/listSalerOrdersGroupedByMchMonthly',
    method: 'post',
    data
  })
}
// 销售数据统计
export function salerOrdersStatistics(data) {
  return request({
    url: '/data/salerOrdersStatistics',
    method: 'post',
    data
  })
}
// 销售成本订单合并
export function salerstatistics(data) {
  return request({
    url: '/data/salerstatistics',
    method: 'post',
    data,
  })
}