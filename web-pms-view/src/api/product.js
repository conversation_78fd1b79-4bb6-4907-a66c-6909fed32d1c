import request from '@/utils/request.js';
import axios from 'axios'

export function getProductList(data) {
  return request({
    url: '/product_function_manager/list',
    method: 'post',
    data
  })
}

export function getAllProduct() {
  return request({
    url: '/product_function_manager/listAll',
    method: 'get'
  })
}

export function addProduct(data) {
  return request({
    url: '/product_function_manager/add',
    method: 'post',
    data
  })
}

export function editProduct(data) {
  return request({
    url: '/product_function_manager/edit',
    method: 'post',
    data
  })
}

export function deleteProduct(data) {
  return request({
    url: `/product_function_manager/delete/${ data.id }`,
    method: 'post',
  })
}

export function getProductByMch(params) {
  return request({
    url: '/merchantEmployer/listMchProduct',
    method: 'get',
    params
  })
}

// 获取供应商列表（只用在计费这块，别的请使用getMainstayList）
export function getVendorList(data) {
  return request({
    url: '/vendor_manager/list',
    method: 'post',
    data
  })
}

// 获取供应商列表（只用在计费这块，别的请使用getMainstayList）
export function getVendorSimpleList(data) {
  return request({
    url: '/vendor_manager/listSimple',
    method: 'post',
    data
  })
}

export function addVendor(data) {
  return request({
    url: '/vendor_manager/add',
    method: 'post',
    data
  })
}

export function editVendor(data) {
  return request({
    url: '/vendor_manager/edit',
    method: 'post',
    data
  })
}

export function deleteVendor(data) {
  return request({
    url: `/vendor_manager/delete/${ data.id }`,
    method: 'post',
  })
}

export function getVendorFeeList(data) {
  return request({
    url: '/vendor_fee_manager/list',
    method: 'post',
    data
  })
}

export function addVendorFee(data) {
  return request({
    url: '/vendor_fee_manager/add',
    method: 'post',
    data
  })
}

export function editVendorFee(data) {
  return request({
    url: '/vendor_fee_manager/edit',
    method: 'post',
    data
  })
}

export function deleteVendorFee(data) {
  return request({
    url: `/vendor_fee_manager/delete/${ data.id }`,
    method: 'post',
  })
}

export function getMerchantProductList(data) {
  return request({
    url: '/mch_prod_manager/list',
    method: 'post',
    data
  })
}

export function addMerchantProduct(data) {
  return request({
    url: '/mch_prod_manager/add',
    method: 'post',
    data
  })
}

export function editMerchantProduct(data) {
  return request({
    url: '/mch_prod_manager/edit',
    method: 'post',
    data
  })
}

export function deleteMerchantProduct(data) {
  return request({
    url: `/mch_prod_manager/delete/${ data.id }`,
    method: 'post',
  })
}

export function getMerchantProductFeeList(data) {
  return request({
    url: '/mch_fee_manager/list',
    method: 'post',
    data
  })
}

export function addMerchantProductFee(data) {
  return request({
    url: '/mch_fee_manager/add',
    method: 'post',
    data
  })
}

export function editMerchantProductFee(data) {
  return request({
    url: '/mch_fee_manager/edit',
    method: 'post',
    data
  })
}

export function deleteMerchantProductFee(data) {
  return request({
    url: `/mch_fee_manager/delete/${ data.id }`,
    method: 'post',
  })
}

// 商户产品状态更新
export function updateStatus(params) {
  return request({
    url: '/mch_prod_manager/updateStatus',
    method: 'get',
    params
  })
}
