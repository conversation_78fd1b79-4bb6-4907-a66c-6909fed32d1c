import request from '@/utils/request.js';

export function getAgreementTemplateList(data) {
  return request({
    url: '/agreement/listTemplatePage',
    method: 'post',
    data
  })
}

export function getAgreementTemplateById(data) {
  return request({
    url: `/agreement/getTemplateById/${data.id}`,
    method: 'get',
  })
}

export function createAgreementTemplate(data) {
  return request({
    url: '/agreement/createTemplate',
    method: 'post',
    data
  })
}

export function editAgreementTemplate(data) {
  return request({
    url: '/agreement/editTemplate',
    method: 'post',
    data
  })
}

export function getAgreementList(data) {
  return request({
    url: '/agreement/listAgreementPage',
    method: 'post',
    data
  })
}

export function getAgreementPage(data) {
  return request({
    url: '/agreement/getAgreementPage',
    method: 'post',
    data
  })
}

export function getAgreementById(data) {
  return request({
    url: `/agreement/getAgreementById/${data.id}`,
    method: 'get',
  })
}

export function createAgreement(data) {
  return request({
    url: '/agreement/createAgreement',
    method: 'post',
    data
  })
}

export function editAgreement(data) {
  return request({
    url: '/agreement/editAgreement',
    method: 'post',
    data
  })
}

export function cancelAgreement(data) {
  return request({
    url: '/agreement/cancelAgreement',
    method: 'post',
    data
  })
}

export function finishAgreement(data) {
  return request({
    url: '/agreement/archiveAgreement',
    method: 'post',
    data
  })
}

export function downloadArchiveFile(data) {
  return request({
    url: '/agreement/downloadArchiveFile',
    method: 'post',
    data
  })
}

export function syncTemplate() {
  return request({
    url: "/agreement/syncTemplate",
    method: 'post',
  })
}

// 批量撤回
export function retreatAgreement(data) {
  return request({
    url: '/agreement/cancel',
    method: 'post',
    data
  })
}

// 批量删除
export function deleteAgreement(data) {
  return request({
    url: '/agreement/delete',
    method: 'post',
    data
  })
}

// 批量转线下
export function turnToOffline(data) {
  return request({
    url: '/agreement/turnToOffline',
    method: 'post',
    data
  })
}

// 延期
export function delayAgreement(data) {
  return request({
    url: '/agreement/delay',
    method: 'post',
    data
  })
}

// 批量导出协议列表
export function exportExcel(data) {
  return request({
    url: '/agreement/exportExcel',
    method: 'post',
    data
  })
}

// 批量下载归档文件
export function exportFile(data) {
  return request({
    url: '/agreement/exportFile',
    method: 'post',
    data
  })
}

// 替换归档协议
export function replaceAgreement(data) {
  return request({
    url: '/agreement/replace',
    method: 'post',
    data,
  })
}
