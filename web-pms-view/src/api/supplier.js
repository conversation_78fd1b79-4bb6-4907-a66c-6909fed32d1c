import request from '@/utils/request.js';

// 获取合伙人预置角色
export function getPresetRole(data) {
  return request({
    url: '/pmsSupplierRole/listPage',
    method: 'post',
    data
  })
}

// 删除合伙人预置角色
export function deletePresetRole(data) {
  return request({
    url: '/pmsSupplierRole/delete',
    method: 'post',
    data,
  })
}

// 编辑合伙人预置角色
export function editPresetRole(data) {
  return request({
    url: '/pmsSupplierRole/edit',
    method: 'post',
    data
  })
}

// 创建合伙人预置角色
export function addPresetRole(data) {
  return request({
    url: '/pmsSupplierRole/add',
    method: 'post',
    data
  })
}

// 查询合伙人角色权限
export function getRoleFunction(params) {
  return request({
    url: '/pmsSupplierRole/listRoleFunction',
    method: 'get',
    params
  })
}

// 分配合伙人角色功能
export function assignPermission(data) {
  return request({
    url: '/pmsSupplierRole/assignFunction',
    method: 'post',
    data,
  })
}

// 功能导入
export function importFunc(data) {
  return request({
    url: '/supplierFunction/import',
    method: 'post',
    data,
  })
}

// 功能导出
export function exportFunc() {
  return request({
    url: '/supplierFunction/export',
    method: 'get'
  })
}


// 查询所有供应商后台功能
export function listFunction() {
  return request({
    url: '/supplierFunction/listAll',
    method: 'get'
  });
}

// 删除用工企业后台功能
export function deleteSupplierFunction(data) {
  return request({
    url: '/supplierFunction/delete',
    method: 'get',
    params: data
  })
}


// 新增供应商后台功能
export function createSupplierFunction(data) {
  return request({
    url: '/supplierFunction/add',
    method: 'post',
    data
  })
}

// 修改供应商后台功能
export function editSupplierFunction(data) {
  return request({
    url: '/supplierFunction/edit',
    method: 'post',
    data
  })
}

// 查询供应商余额
export function checkSupplierBalance(data) {
  return request({
    url: '/mainstayChannelRelation/getBalanceByMainstayNo',
    method: 'post',
    data,
  })
}


// 余额提取
export function withdrawAll(data) {
  return request({
    url: '/withdraw/withdrawAll',
    method: 'post',
    data
  })
}

// 同步汇聚供应商余额接口
export function mainstayBalanceSyncOne(mainStayNo) {
  return request({
    url: `/acMerchantBalance/mainstayBalanceSyncOne/${mainStayNo}`,
    method: 'post',
  })
}