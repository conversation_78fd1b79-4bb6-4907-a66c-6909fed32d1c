import request from '@/utils/request'

// 文件推送列表
export function filePushList(data) {
  return request({
    url: '/pushManager/listPage', method: 'post', data
  })
}

export function deleteFilePush({ id }) {
  return request({
    url: `/pushManager/delete/${ id }`,
    method: 'post',
  })
}

export function addFilePush(data) {
  return request({
    url: '/pushManager/add',
    method: 'post',
    data
  })
}

export function updateFilePush(data) {
  return request({
    url: '/pushManager/update',
    method: 'post',
    data
  })
}
