import request from '@/utils/request'

// 查询发放名单列表
export function getOrderList(data) {
  return request({
    url: '/order/listOrderPage',
    method: 'post',
    data,
  })
}

// 查询订单明细列表
export function getOrderItem(data) {
  return request({
    url: '/order/listOrderItemPage',
    method: 'post',
    data,
  })
}

// 查询打款流水列表
export function getRecordList(data) {
  return request({
    url: '/order/listRecordItemPage',
    method: 'post',
    data,
  })
}

// 导出订单明细
export function exportOrderItem(data) {
  return request({
    url: '/order/exportOrderItem',
    method: 'post',
    data,
  })
}

export function exportRecordItem(data) {
  return request({
    url: '/order/exportRecordItem',
    method: 'post',
    data
  })
}

// 重新发放
export function grantAgain(data) {
  return request({
    url: '/order/grantAgain',
    method: 'post',
    data
  })
}

// 重新受理
export function acceptAgain(data) {
  return request({
    url: '/order/acceptAgain',
    method: 'post',
    data
  })
}

// 反查通道
export function reverseQuery(data) {
  return request({
    url: '/order/reverseQuery',
    method: 'post',
    data
  })
}

// 发放订单表格总条数
export function countOrder(data) {
  return request({
    url: '/order/countOrder',
    method: 'post',
    data,
  })
}

// 订单明细总条数
export function countOrderItem(data) {
  return request({
    url: '/order/countOrderItem',
    method: 'post',
    data,
  })
}

// 打款流水条数
export function countRecordItem(data) {
  return request({
    url: '/order/countRecordItem',
    method: 'post',
    data
  })
}

// 发放订单明细统计
export function sumOrderItem(data) {
  return request({
    url: '/order/sumOrderItem',
    method: 'post',
    data
  })
}

// 批量下载凭证
export function batchDownloadCertificateFile(data, type) {
  return request({
    url: `/certificate/batchDownloadCertificateFile/${ type }`,
    method: 'post',
    data,
  })
}

// 提现记录
export function getWithdrawRecord(data) {
  return request({
    url: '/order/listWithdrawRecordPage',
    method: 'post',
    data,
  })
}

export function sumWithdrawRecord(data) {
  return request({
    url: '/order/sumWithdrawRecord',
    method: 'post',
    data,
  })
}

// 导出提现记录
export function exportWithdrawRecord(data) {
  return request({
    url: '/order/exportWithRecord',
    method: 'post',
    data,
  })
}

// 通过流水号查订单详情
export function getOrderItemByPlatTrxNo(data) {
  return request({
    url: '/order/getOrderItemByPlatTrxNo',
    method: 'post',
    data
  })
}

// 退汇操作
export function reexchange(data) {
  return request({
    url: `/orderReexchange/reexchange/${ data }`,
    method: 'post',
  })
}

// 导出
export function exportExcel(data, type) {
  return request({
    url: `/order/exportExcel/${ type }`,
    method: 'post',
    data
  })
}

export function refundFrozenAmount(platTrxNo) {
  return request({
    url: `/order/refundFrozenAmount?platTrxNo=${platTrxNo}`,
    method: 'get',
  })
}

// 外部订单发放名单列表
export function offlineOrderList(data) {
  return request({
    url: '/offlineOrder/listOrderPage',
    method: 'post',
    data
  })
}

// 外部订单总条数
export function offlineCountOrderItem(data) {
  return request({
    url: '/offlineOrder/countOrderItem',
    method: 'post',
    data
  })
}

// 外部订单明细列表
export function offlineOrderItemList(data) {
  return request({
    url: '/offlineOrder/listOrderItemPage',
    method: 'post',
    data,
  })
}

// 外部订单明细总金额
export function offlineSumOrder(data) {
  return request({
    url: '/offlineOrder/sumOrderItem',
    method: 'post',
    data
  })
}

// 外部导出订单明细
export function exportOfflineOrderList(data) {
  return request({
    url: '/offlineOrder/exportOrderItem',
    method: 'post',
    data
  })
}

// 外部订单重新受理
export function acceptOfflineAgain(data) {
  return request({
    url: '/offlineOrder/acceptAgain',
    method: 'post',
    data
  })
}
