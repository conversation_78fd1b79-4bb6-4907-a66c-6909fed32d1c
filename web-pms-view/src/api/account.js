import request from '@/utils/request.js';

// 获取商户账户记录
export function getRecordByChannelType(params) {
  return request({
    url: '/accountRecord/getRecordByChannelType',
    method: 'get',
    params
  })
}

//修改账户可视状态
export function changeAccountStatus(data) {
  return request({
    url: '/accountRecord/changeStatus',
    method: 'post',
    data
  })
}

export function changeAccountTitle(data) {
  return request({
    url: '/accountRecord/changeTitle',
    method: 'post',
    data
  })
}
