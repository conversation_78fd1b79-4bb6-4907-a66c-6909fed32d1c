import request from '@/utils/request.js';

// 分页查询风控规则
export function getRiskControlList(data) {
  return request({
    url: '/riskcontrol/listRule',
    method: 'post',
    data
  })
}

// 新增风控规则
export function addRiskControl(data) {
  return request({
    url: '/riskcontrol/addRule',
    method: 'post',
    data
  })
}

// 更新风控规则
export function updateRiskControl(data) {
  return request({
    url: '/riskcontrol/updateRule',
    method: 'post',
    data
  })
}

// 删除风控规则
export function deleteRiskControl(data) {
  return request({
    url: '/riskcontrol/deleteRuleById',
    method: 'get',
    params: data
  })
}

// 查询策略原子组列表
export function getAtomListById(data) {
  return request({
    url: '/riskcontrol/listStrategyAtomGroup',
    method: 'get',
    params: data
  })
}

// 查询策略原子组
export function getSingerAtomById(data) {
  return request({
    url: '/riskcontrol/getStrategyAtomGroupById',
    method: 'get',
    params: data
  })
}

// 创建策略原子组
export function createAtom(data) {
  return request({
    url: '/riskcontrol/addStrategyAtomGroup',
    method: 'post',
    data
  })
}

// 批量创建策略原子组
export function addStrategyAtomGroupBatch(data) {
  return request({
    url: '/riskcontrol/addStrategyAtomGroupBatch',
    method: 'post',
    data
  })
}

// 更新策略原子组
export function updateAtom(data) {
  return request({
    url: '/riskcontrol/updateStrategyAtomGroup',
    method: 'post',
    data
  })
}

// 删除策略原子组
export function deleteAtom(data) {
  return request({
    url: '/riskcontrol/deleteStrategyAtomGroupById',
    method: 'get',
    params: data
  })
}

// 获取所有供应商
export function getAllSuppliers() {
  return request({
    url: '/riskcontrol/getAllSuppliers',
    method: 'get'
  })
}

// 风控挂单列表
export function getPendingOrderList(data) {
  return request({
    url: '/riskcontrol/listPendingOrder',
    method: 'post',
    data
  })
}

// 处理挂单
export function operatePending(data) {
  return request({
    url: '/riskcontrol/pendingOperate',
    method: 'post',
    data
  })
}

// 名单列表
export function peopleList(data) {
  return request({
    url: '/nameManagement/listPage',
    method: 'post',
    data
  })
}

export function createPeople(data) {
  return request({
    url: '/nameManagement/create',
    method: 'post',
    data
  })
}

export function updatePeople(data) {
  return request({
    url: '/nameManagement/update',
    method: 'post',
    data
  })
}

export function deletePeople(data) {
  return request({
    url: '/nameManagement/delete',
    method: 'post',
    data
  })
}


// 获取所有供应商列表
export function getSupplierNos() {
  return request({
    url: '/riskcontrol/getSupplierNos',
    method: 'get'
  })
}


// 获取没有配置的供应商
export function getNoConfigSuppliers() {
  return request({
    url: '/riskcontrol/getNoConfigSuppliers',
    method: 'get'
  })
}

// 导出已处理
export function exportRiskControl(data) {
  return request({
    url: '/riskcontrol/exportExcel',
    method: 'post',
    data
  })
}
