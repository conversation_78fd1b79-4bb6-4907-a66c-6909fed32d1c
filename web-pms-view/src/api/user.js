import request from '@/utils/request.js';

export function getCaptcha() {
  return request({
    url: '/user/captcha',
    method: 'post'
  })
}

export function getEmailCode(params) {
  return request({
    url: '/user/emailCode',
    method: 'get',
    params
  })
}

export function resetPwd(data) {
  return request({
    url: '/user/resetPwd',
    method: 'post',
    data
  })
}

export function doLogin(data) {
  return request({
    url: '/user/login',
    method: 'post',
    data
  })
}

export function emailValid(params) {
  return request({
    url: '/user/emailVerification',
    method: 'get',
    params
  })
}
