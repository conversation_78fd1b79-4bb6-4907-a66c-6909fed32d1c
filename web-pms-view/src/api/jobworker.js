import request from '@/utils/request.js';

// 任务列表
export function taskList(data) {
  return request({
    url: '/job/page',
    method: 'post',
    data,
  })
}

export function jobDetail({ id }) {
  return request({
    url: `/job/getById/${ id }`,
    method: 'get'
  })
}

export function taskAudit(ids, event) {
  return request({
    url: `/job/approved/${ ids }?event=${ event }`,
    method: 'POST'
  })
}

// 雇员身份信息
export function workerInfo(params) {
  return request({
    url: '/worker/getIdCardByIDCardNoMd5',
    method: 'post',
    params
  })
}

export function taskListOnGrant(data) {
  return request({
    url: '/job/listJobListOnGrant',
    method: 'post',
    data,
  })
}
