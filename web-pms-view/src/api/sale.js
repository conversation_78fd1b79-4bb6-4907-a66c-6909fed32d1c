import request from '@/utils/request.js';

export function getSaleCostRuleList(data) {
  return request({
    url: '/salesCostRule/listPage',
    method: 'post',
    data
  })
}

export function createSaleCostRule(data) {
  return request({
    url: '/salesCostRule/insert',
    method: 'post',
    data
  })
}

export function editSaleCostRule(data) {
  return request({
    url: '/salesCostRule/update',
    method: 'post',
    data
  })
}

export function deleteSaleCostRule(data) {
  return request({
    url: '/salesCostRule/delete',
    method: 'post',
    data
  })
}
