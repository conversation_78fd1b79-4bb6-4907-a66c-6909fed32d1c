import request from '@/utils/request.js';

// 增加通知
export function addNotify(data) {
  return request({
    url: '/notification/createNotification',
    method: 'post',
    data
  });
}

// 通知列表
export function getNotifyList(data) {
  return request({
    url: '/notification/listNotifications',
    method: 'post',
    data
  });
}

// 获取通知
export function getNotify(params) {
  return request({
    url: '/notification/notificationInfoById',
    method: 'get',
    params
  });
}

// 删除通知
export function delNotify(data) {
  return request({
    url: '/notification/delete',
    method: 'post',
    data
  });
}