import request from '@/utils/request.js';

// 微信来帐列表
export function getWxIncomeRecord(data) {
  return request({
    url: '/wxIncomeRecord/listPage',
    method: 'post',
    data
  })
}

// 审核通过微信来帐
export function confirmWxIncome(data) {
  return request({
    url: '/wxIncomeRecord/confirm',
    method: 'post',
    data
  })
}

// 导出微信记录
export function expotWxRecord(data) {
  return request({
    url: '/wxIncomeRecord/export',
    method: 'post',
    data
  })
}

// 微信调账
export function wechatAdjust(data) {
  return request({
    url: '/wxMerchantBalance/adjustment',
    method: 'post',
    data,
  })
}

// 微信明细列表
export function wechatChangeList(data) {
  return request({
    url: '/changesFunds/listPage',
    method: 'post',
    data
  })
}

// 君享汇调账
export function jxhAdjust(data) {
  return request({
    url: '/acMerchantBalance/adjustment',
    method: 'post',
    data,
  })
}

// 君享汇明细列表
export function jxhChangeList(data) {
  return request({
    url: '/acChangesFunds/listPage',
    method: 'post',
    data
  })
}

// 招行调账
export function cmbAdjust(data) {
  return request({
    url: '/cmbMerchantBalance/adjustment',
    method: 'post',
    data,
  })
}

// 供应商微信调账
export function wxMerchantBalance_mainstayAdjustment(data) {
  return request({
    url: '/wxMerchantBalance/mainstayAdjustment',
    method: 'post',
    data
  })
}

// 供应商君享汇调账
export function acMerchantBalance_mainstayAdjustment(data) {
  return request({
    url: '/acMerchantBalance/mainstayAdjustment',
    method: 'post',
    data
  })
}

// 供应商招行调账
export function cmbMerchantBalance_mainstayAdjustment(data) {
  return request({
    url: '/cmbMerchantBalance/mainstayAdjustment',
    method: 'post',
    data
  })
}

// 招行来帐列表
export function getCmbIncomeRecord(data) {
  return request({
    url: '/cmbIncomeRecord/listPage',
    method: 'post',
    data
  })
}

// 审核通过招行来帐
export function confirmCmbIncome(data) {
  return request({
    url: '/cmbIncomeRecord/audit',
    method: 'post',
    data
  })
}

// 导出招行记录
export function exportCmbRecord(data) {
  return request({
    url: '/cmbIncomeRecord/exportCmbIncomeRecord',
    method: 'post',
    data
  })
}

// 招行明细列表
export function cmbChangeList(data) {
  return request({
    url: '/cmbChangesFunds/listPage',
    method: 'post',
    data
  })
}
