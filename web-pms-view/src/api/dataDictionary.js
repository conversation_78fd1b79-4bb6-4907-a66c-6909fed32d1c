import request from '@/utils/request.js';

// 新增数据字典
export function addDataDictionary(data) {
  return request({
    url: '/dataDictionary/addDataDictionary',
    method: 'post',
    data
  })
}

// 修改
export function editDataDictionary(data) {
  return request({
    url: '/dataDictionary/editDataDictionary',
    method: 'post',
    data
  })
}

// 列表
export function listDataDictionary(data, params) {
  return request({
    url: '/dataDictionary/listDataDictionary',
    method: 'post',
    data,
    params
  })
}

// 查看详细
export function getDataDictionaryVOById(params) {
  return request({
    url: '/dataDictionary/getDataDictionaryVOById',
    method: 'get',
    params
  })
}

// 删除
export function deleteDataDictionary(data) {
  return request({
    url: '/dataDictionary/deleteDataDictionary',
    method: 'post',
    data
  })
}

// 导出
export function exportDataDictionary(data) {
  return request({
    url: '/dataDictionary/export',
    method: 'post',
    data,
  })
}

// 导入
export function importDataDictionary(data) {
  return request({
    url: '/dataDictionary/import',
    method: 'post',
    data,
  })
}
