import request from '@/utils/request.js';

export function getScheduleJob(data, params) {
  return request({
    url: '/timer/listScheduleJob',
    method: 'post',
    data,
    params
  })
}

export function getScheduleJobInfo(data) {
  return request({
    url: '/timer/getScheduleJobInfo',
    method: 'post',
    data,
  })
}

export function deleteScheduleJob(data) {
  return request({
    url: '/timer/deleteScheduleJob',
    method: 'post',
    data,
  })
}

export function triggerScheduleJob(data) {
  return request({
    url: '/timer/triggerScheduleJob',
    method: 'post',
    data,
  })
}

export function notifyScheduleJob(data) {
  return request({
    url: '/timer/notifyScheduleJob',
    method: 'post',
    data,
  })
}

export function changeScheduleJobStatus(data) {
  return request({
    url: '/timer/changeScheduleJobStatus',
    method: 'post',
    data
  })
}

export function listOpLog(params) {
  return request({
    url: '/timer/listOpLog',
    method: 'post',
    params
  })
}

export function listInstance() {
  return request({
    url: '/timer/listInstance',
    method: 'post',
  })
}

export function changeNamespaceStatus(params) {
  return request({
    url: '/timer/changeNamespaceStatus',
    method: 'post',
    params
  })
}

export function addScheduleJob(data) {
  return request({
    url: '/timer/addScheduleJob',
    method: 'post',
    data
  })
}

export function editScheduleJob(data) {
  return request({
    url: '/timer/editScheduleJob',
    method: 'post',
    data
  })
}


