import request from "@/utils/request";

export function listProxyOrderSupply(data) {
  return request({
    url: '/individualproxy/listPage',
    method: 'post',
    data
  })
}

export function addSupply(data) {
  return request({
    url: '/individualproxy/addIndividualProxyMainstay',
    method: 'post',
    data
  })
}

export function editSupply(data) {
  return request({
    url: '/individualproxy/updateIndividualProxyMainstay',
    method: 'post',
    data
  })
}

export function updateSupplyStatus(data) {
  return request({
    url: '/individualproxy/updateStatus',
    method: 'post',
    data
  })
}

export function addQuote(data) {
  return request({
    url: '/individualproxy/addIndividualProxyQuote',
    method: 'post',
    data
  })
}

export function editQuote(data) {
  return request({
    url: '/individualproxy/updateIndividualProxyQuote',
    method: 'post',
    data
  })
}

export function deleteQuote(data) {
  return request({
    url: '/individualproxy/deleteIndividualProxyQuote',
    method: 'post',
    data
  })
}


// 订单
export function listProxyOrder(data) {
  return request({
    url: '/proxyOrder/listPage',
    method: 'post',
    data
  })
}

export function confirmInvoice(data) {
  return request({
    url: '/proxyOrder/confirmInvoice',
    method: 'post',
    data
  })
}
