import Vue from 'vue'
import 'normalize.css/normalize.css' // A modern alternative to CSS resets
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/reset.css';
import 'element-ui/lib/theme-chalk/index.css'
// import mixin from '@/mixin'

import '@/styles/index.scss' // global css

import Login from '@/views/common/Login'

Vue.use(ElementUI)
// Vue.mixin(mixin)
Vue.config.productionTip = false


new Vue({
  el: '#login',
  render: h => h(Login)
})


window.__app_version = APP_VERSION
