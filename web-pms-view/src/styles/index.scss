@import './variables.scss';
@import './mixin.scss';
@import './extend.scss';
@import './transition.scss';
@import './element-ui.scss';
@import "./main-page.scss";
@import "./font.scss";
@import "./sidebar1.scss";

body {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

img {
  max-width: 100%;
}

// main-container global css
.app-container {
  padding: 20px;
}

.color-gray {
  color: rgba(0, 0, 0, 0.447)
}

.color-warning {
  color: $warningColor;
}


// 弹性容器只负责弹性盒子相关属性
.flex-container {
  position: relative;
  overflow: hidden;
}

.flex-wrapper { // 整一行的容器（一行时可不用）
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
}

.flex-item { // 某一项的容器
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  flex: 1;
  margin-right: 8px;
  max-width: 350px;

  .el-input, .el-select {
    flex: 1;
  }
}

.flex-item-main {
  @extend .flex-item;
  max-width: 100% !important;
}

.flex-item-half {
  @extend .flex-item;
  max-width: 50% !important;
}

.flex-item__label {
  display: inline-block;
  width: 100px;
  text-align: right;
  font-size: $fontSizeMini;

  &.middle {
    width: 120px;
  }

  &.oversize {
    width: 140px;
  }

  &.text-left {
    text-align: left;
  }
}


.flex-vertical-item {
  display: flex;
  flex: 1;
  flex-direction: column;
  margin-top: 32px;
  margin-right: 8px;
  max-width: 30%;

  .flex-vertical-label {
    margin-bottom: 8px;
    font-size: $fontSizeMini;
    color: $fontGray;
  }

  .flex-func-link {
    color: $mainColor;
    cursor: pointer;
  }
}

.flex-vertical-main {
  max-width: 100% !important;
}

.openSidebar .flex-item {
  max-width: 300px;
}

// 搜索栏
.search-container {
  padding: 16px 0;
  background: $lightGray;
}

.search-wrapper {
  margin-top: 16px;
  @extend .clearfix;

  &:first-of-type {
    margin-top: 0;
  }
}

.search-btn-group {
  float: left;
  text-align: right;
  padding-left: 100px;
}

.box-container {
  position: relative;
  width: 100%;
  padding: 16px;
  margin-bottom: 16px;
  background: #fff;
  @include boxShadow;

  .create-btn {
    margin-bottom: 16px;
  }

  .func-container {
    margin-top: 32px;
    margin-left: 10px;
  }

  .menu-tab-container {
    margin-bottom: 16px;
  }
}

// 页面底部保存
.form-btn-group {
  position: fixed;
  left: 106px;
  right: 0;
  bottom: 0;
  padding: 8px;
  background: #fff;
  text-align: center;
  box-shadow: 0px -3px 3px 1px #ddd;
  z-index: 10;
  text-align: center;
}

.openSidebar .form-btn-group {
  left: 258px;
}

// 搜索表格内容
.content-container {
  padding-top: 32px;
}

.el-table {
  margin-bottom: 20px;

  ::v-deep .el-table__header > th {
    background: $lightGray;
  }

  .child-row {
    background: #ddd;
  }
}

.pagination-container {
  overflow: hidden;

  .el-pagination {
    float: right;
  }

  .force-next-btn {
    padding: 0 4px;
    cursor: pointer;
  }
}

.fileBg {
  position: relative;
  width: 100%;
  height: 100%;
  background: url("../assets/document.png") no-repeat;
  background-size: 100% 100%;
}

.fileBg .file-name {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 5px;
  border-top: 1px solid #999;
  background: #fff;
}

// 用于表格不折行
.no-wrap {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.example-image {
  width: 250px;
}

// 级联面板的宽度
.cascader-content-item {
  display: block;
  max-width: 200px;
  overflow-x: hidden;
  text-overflow: ellipsis;
}

// 一些功能型文字
.func-content {
  cursor: pointer;
  color: $mainColor;
}

// 空白内容提示
.empty-tip {
  color: $fontGray;
}

.subTitle {
  position: relative;
  padding-left: 30px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;

  &:before {
    position: absolute;
    content: '';
    left: 10px;
    width: 4px;
    height: calc(100% - 10px);
    background: #20a0ff;
    max-height: 36px;
  }
}

// 特定样式
.float-l {
  float: left
}

.float-r {
  float: right;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-hidden {
  overflow: hidden;
}

.flex-box {
  display: flex;

  & > .flex-item {
    flex: 1 0;
  }
}


// tinymce 上传图片弹窗层级调整
.tox-tinymce-aux {
  z-index: 99999 !important;
}


