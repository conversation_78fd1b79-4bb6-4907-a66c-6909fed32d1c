// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}


// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// table
.el-table {
  .el-button {
    margin-left: 8px;
  }

  .el-button:first-child {
    margin-right: 0;
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}

.search-btn-group, .form-btn-group {
  .el-button + .el-button {
    margin-left: 8px;
  }
}

.el-steps-container {
  .el-step__title.is-finish {
    font-weight: 700;
    color: #303133;
  }

  .el-step__head.is-finish {
    color: #fff;

    .el-step__icon {
      background: $mainColor;
    }
  }

}


// upload组件提示信息样式
.el-upload__tip {
  line-height: 1.8;
  @extend .color-gray;
}

// upload-tip
.upload-tip__weight {
  font-weight: bolder;
}

.sidebar-container-inner .el-scrollbar__bar {
  opacity: 0 !important;
}

.el-tooltip__popper {
  max-width: 90%;
}

.el-link + .el-link {
  margin-left: 4px;
}

.el-input-inline {
  width: 500px;
}

.el-cascader > .el-input {
  width: 100% !important;
}
