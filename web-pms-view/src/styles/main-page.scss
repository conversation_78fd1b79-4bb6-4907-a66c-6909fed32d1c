// 表单页面样式
.page-container {
  position: relative;
  padding: 32px 0 50px;
  background: #fff;

  .header-container {
    padding: 32px 0;
  }
  .content-container {
    position: relative;
    margin-top: 32px;
    padding: 0 16px 64px;
  }
  .hgroup {
    text-align: center;
  }
  .el-form {
    text-align: left;
  }
  .el-input, .el-textarea {
    width: 300px;
  }
  .big-input .el-input {
    width: 500px !important;
  }
  .el-table {
    .el-input, .el-textarea {
      width: auto;
    }
  }
  .subTitle {
    margin-top: 60px;
    margin-left: 30px;
    margin-bottom: 20px;
  }
  .el-form-item {
    margin-bottom: 24px;
  }
  .el-form-item__label {
    text-align: right;
  }
  .form-tip {
    margin-top: 4px;
    margin-bottom: 0;
    font-size: 12px;
  }
  .el-upload-list {
    height: 100%;
  }

  .flex-container {
    padding: 32px;
  }
}
.result-container {
  text-align: center;
}
.result-detail {
  margin: 0 0 20px;
  padding: 20px;
  padding-left: 270px;
  text-align: left;
}
.result-title {
  font-size: 30px;
  font-weight: 600;
}
.result-detail-title {
  font-weight: 600;
}
.result-tip {
  color: #ccc;
}

.result-wrapper {
  display: flex;
  justify-content: space-between;
  .result-item {
    flex: 1;
  }
}

