let LOING_RSA_PUBLIC_KEY = '';
if (process.env.VUE_APP_FLAG === 'production') {
  LOING_RSA_PUBLIC_KEY = 
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCL21LRYmH+X1LKjVvV7dJ9JGlJa0XyTt43dxfGCiUFm2nR6SPuxw3ptfURVTBqIdZKZK7JWwIwa/pFOMCxu+pGHIpVcuDY4x6bq6bnxqZ7xlHNtsMIJqhmtQSSlIGopAB/E9ll1sWtsvcdeHy7SGT6sXpTwpyJVvH8DNN60fjwEwIDAQAB';
} else {
  LOING_RSA_PUBLIC_KEY = 
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDTblnwQUsNtKRUEElWT+jqvfR3K+qp+vgVd2hMIHMStNRjKfa1oZu7Obqn7f12SDeTuOjSEankor5yv6GfuE30fMXLAB+82UcYWgwKgx1SWscPotGT8z2xtFz+lafqFzAvLA+5wVbL0Ung24Zqi40ueW2qXIU4SvWUJwslN8404wIDAQAB';
}


import { JSEncrypt } from 'jsencrypt';

export const encryptParam = param => {
  const jsencrypt = new JSEncrypt();
  jsencrypt.setPublicKey(LOING_RSA_PUBLIC_KEY);
  const RSA_encrypt = jsencrypt.encrypt(param);
  return RSA_encrypt;
};
