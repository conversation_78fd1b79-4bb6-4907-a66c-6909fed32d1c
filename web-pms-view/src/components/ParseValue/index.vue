<template>
  <div style="display: inline-block">
    <div v-if="obKey == 'authStatus'">
      {{ $dictCode('AuthStatusEnum', value).desc }}
    </div>
    <div v-else-if="obKey == 'mchStatus'">
      {{ $dictCode('MchStatusEnum', value).desc }}
    </div>
    <div
      v-else-if="isImage"
    >
      <el-image
       fit
       :src="imagePreviewUrl"
       style="width: 100px; height: 100px; cursor: pointer;"
       @click="onPreview"
      ></el-image>
    </div>
    <div v-else-if="obKey !== 'productQuote'">{{ value || alt }}</div>
  </div>
</template>

<script>
  export default {
    name: 'parse-value',
    props: {
      value: {
        type: [Number, String],
        default: ''
      },
      obKey: {
        type: String,
        default: '',
      },
      alt: {
        type: String,
        default: '',
      }
    },
    data () {
      return {
        imagePreviewUrl: "",
        imagePreview: null
      }
    },
    computed: {
      isImage() {
        return /(\.jpg|\.png|\.jpeg|\.bmp|\.gif)$/i.test(this.value);
      },
    },
    watch: {
      isImage: {
        handler(val) {
          if (val) {
            this.formatImageUrl()
          }
        },
        immediate: true
      }
    },
    methods: {
      async formatImageUrl () {
        const fileMsg = await this.formatFileMsg(this.value);
        this.imagePreviewUrl = fileMsg.fileUrl
        this.imagePreview = fileMsg
      },
      async onPreview () {
        const fileMsg = await this.formatFileMsg(this.imagePreview.origin)
        this.$preview(fileMsg.fileUrl)
      }
    }
  }
</script>

<style scoped lang="scss">

</style>
