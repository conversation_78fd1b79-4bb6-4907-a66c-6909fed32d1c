<template>
  <el-dialog
    title="提交"
    :close-on-click-modal="false"
    :show-close="false"
    :before-close="close"
    :visible.sync="visible"
    append-to-body
  >
    {{ content }}
    <div style="margin-bottom: 8px; font-weight: bold;">确定提交审批吗？如有审批备注请在下框输入（注意确认该备注所有审批人可见）</div>
    <el-input
      type="textarea"
      v-model="remark"
      :rows="5"
      show-word-limit
      maxlength="150"
    ></el-input>

    <template v-slot:footer>
      <el-button
        type="primary"
        @click="confirm"
      >确定</el-button>
      <el-button @click="close">取消</el-button>
    </template>
  </el-dialog>
</template>

<script>
  export default {
    name: "FlowOpnion",
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
      content: {
        type: String,
        default: '',
      }
    },
    data() {
      return {
        remark: '',
      }
    },
    methods: {
      close() {
        this.$emit('update:visible', false)
      },
      confirm() {
        this.$emit('change', this.remark);
        setTimeout(() => {
          this.close();
        }, 0)
      }
    },
  }
</script>

<style lang="scss" scoped>
</style>
