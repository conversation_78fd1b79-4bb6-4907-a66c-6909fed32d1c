<template>
  <el-dialog
    :visible="visible"
    title="更改负责人"
    :before-close="close"
    append-to-body
  >
    <el-form
      :model="form"
      :rules="rules"
      ref="form"
      label-width="150px"
    >
      <el-form-item label="负责人姓名：" prop="contactName">
        <el-input
          clearable
          v-model="form.contactName"
        ></el-input>
      </el-form-item>
      <el-form-item label="联系方式：" prop="contactPhone">
        <el-input
          clearable
          :value="form.contactPhone"
          @input="handleNumInput(form, 'contactPhone', $event)"
        ></el-input>
      </el-form-item>
      <el-form-item label="联系邮箱：" prop="contactEmail" v-if="showEmail">
        <el-input
          clearable
          v-model="form.contactEmail"
        ></el-input>
      </el-form-item>
      <el-form-item label="审批意见：" prop="remark">
        <el-input
          type="textarea"
          v-model="form.remark"
        ></el-input>
      </el-form-item>
    </el-form>
    <template v-slot:footer>
      <el-button type="primary" @click="changeLeader">确定</el-button>
      <el-button @click="close">取消</el-button>
    </template>
  </el-dialog>
</template>

<script>
  export default {
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      showEmail: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        form: {
          contactPhone: '',
          contactName: '',
          contactEmail: '',
          remark: '',
        },
        rules: {
          contactPhone: [{required: true, message: '请输入联系方式', trigger: 'blur'}],
          contactName: [{required: true, message: '请输入负责人姓名', trigger: 'blur'}],
          contactEmail: [{required: true, message: '请输入联系邮箱', trigger: 'blur'}],
        },
      }
    },
    methods: {
      close(done) {
        this.$emit('update:visible', false)
      },
      async changeLeader() {
        const valid = await this.$refs.form.validate().catch(e => e);
        if (!valid) return;
        this.$emit('change-leader', {...this.form});
        this.$refs.form.resetFields();
        this.close()
      }
    }
  }
</script>

<style scoped lang="scss">

</style>
