<template>
  <el-upload
    v-toggle:[limit]="uploadFileList"
    v-bind="$attrs"
    class="upload-demo"
    :class="{'text-center': drag}"
    ref="uploader"
    :action="remoteAction"
    :headers="uploadHeader"
    :list-type="uploadListType"
    :accept="totalAccept"
    :limit="maxLimit"
    :file-list="uploadFileList"
    :drag="drag"
    :before-upload="beforeUpload"
    :on-remove="(file, fileList) => onRemove(fileList)"
    :on-success="(response, file, fileList) => onSuccess(response, file, fileList)"
    :on-preview="handlePreview">
    <template
      v-if="!drag"
      v-slot:file="{ file }"
    >
      <div
        class="fileBg"
        v-if="listType === 'picture-card' && !isImageFile(file)"
      >
        <span class="file-name">{{ file.name || name }}</span> <span class="el-upload-list__item-actions">
           <span
             v-if="showPreview"
             class="el-upload-list__item-preview"
             @click="handlePreview(file)">
            <i class="el-icon-zoom-in"></i>
          </span>
          <span
            class="el-upload-list__item-delete"
            @click="handleFileRemove(file)">
            <i class="el-icon-delete"></i>
          </span>
        </span>
      </div>
    </template>
    <slot
      v-if="showTip"
      slot="tip"
      name="tip">
      <div class="file-tip-content">
        支持扩展名：{{ totalAcceptFormat }}。
        <slot name="tip-append"></slot>
      </div>
    </slot>
    <slot
      name="trigger"
      slot="trigger"
    >
      <template v-if="drag">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </template>
      <i
        v-else-if="listType === 'picture-card'"
        class="el-icon-plus avatar-uploader-icon"> </i>
      <el-button
        v-else
        type="primary"
        size="mini">点击上传
      </el-button>
    </slot>
  </el-upload>
</template>

<script>
export default {
  beforeUpdate() {
    // debugger
  },
  name: 'FileUpload',
  props: {
    listType: {
      type: String,
      default: 'picture-card'
    },
    max: {
      type: Number,
      default: undefined,
    },
    options: {
      type: Object,
      default: () => ({})
    },
    url: {
      type: String,
      default: '',
    },
    urls: {
      type: Array,
      default: () => []
    },
    accept: {
      type: String,
      default: '',
    },
    name: {
      type: String,
      default: '文件'
    },
    showPreview: {
      type: Boolean,
      default: true,
    },
    drag: {
      type: Boolean,
      default: false
    },
    action: {
      type: String,
      default: ''
    },
    showTip: {
      type: Boolean,
      default: true,
    },
    multiUpload: {
      // 标识外部组件用urls还是url
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      uploadFileList: [],
      singleFileName: '',
    }
  },
  computed: {
    validateOption() {
      return Object.assign({}, {
        img: true,
        size: 6,
      }, this.options)
    },
    totalAccept() {
      let accept = ''
      if (this.validateOption.img) {
        accept += ',.jpg,.jpeg,.png'
      }
      if (this.validateOption.pdf) {
        accept += ',.pdf'
      }
      if (this.validateOption.word) {
        accept += ',.doc,.docx'
      }
      if (this.validateOption.excel) {
        accept += ',.xls,.xlsx'
      }
      accept = this.accept + accept
      return accept
    },
    totalAcceptFormat() {
      return this.totalAccept.split(',').filter(i => !!i).join(' ')
    },
    maxLimit() {
      // 不传max，默认限制只有1个文件上传
      return this.max || 1
    },
    limit() {
      // 默认是0，不控制按钮显示
      return this.max === undefined ? 0 : this.max
    },
    isMulti() {
      // 标识能否上传多个
      return this.max !== 0 && (this.max > 1 || !this.max)
    },
    remoteAction() {
      return this.baseUrl + (this.action || '/file/upload')
    },
    uploadListType() {
      if (this.drag) {
        return null
      } else {
        return this.listType
      }
    }
  },
  watch: {
    url: {
      immediate: true,
      handler(val) {
        if (this.maxLimit === 1) {
          let fileList = []
          if (val) {
            fileList = [val]
          }
          this.handleFileUrlsChange(fileList)
        }
      },
    },

    urls: {
      immediate: true,
      handler(val) {
        if (this.maxLimit > 1 || this.multiUpload) {
          let fileList = []
          if (val && val.length > 0) {
            fileList = val
          } 
          this.handleFileUrlsChange(fileList)
        }
      }
    },
  },
  methods: {
    beforeUpload() {
      this.$emit('beforeUpload')
      return this.validateUploadFile(this.validateOption)
    },
    async onSuccess(response, file, fileList) {
      this.singleFileName = ''
      if (this.isMulti) {
        let urls = fileList.map(item => {
          if (item.response) {
            return item.response.data
          } else {
            return item.origin
          }
        })
        this.$emit('update:urls', urls)
        this.$emit('change', urls)
      } else {
        this.$emit('update:url', file.response.data)
        this.$emit('change', file.response.data)
        this.singleFileName = file.name
      }
      this.$emit('update:name', file.name)
      this.$emit('success')
    },
    async onRemove(fileList) {
      if (this.isMulti) {
        let urls = fileList.map(item => {
          if (item.response) {
            return item.response.data
          } else {
            return item.origin
          }
        })
        this.$emit('update:urls', urls)
        this.$emit('change', urls)
      } else {
        this.$emit('update:url', '')
        this.$emit('change', '')
        this.singleFileName = ''
      }
      this.$emit('remove')
    },
    isImageFile(file) {
      let url = file.response ? file.response.data : file.origin;
      return this.isImg(url)
    },
    async handlePreview(file) {
      let url = file.response ? file.response.data : file.origin;
      const fileMsg = await this.formatFileMsg(url)
      if (this.isImg(url)) {
        this.$preview(fileMsg.fileUrl)
      } else {
        const suffix = url.split('.').pop()
        this.downloadFile(fileMsg.fileUrl, this.name, suffix);
      }
    },
    handleFileRemove(file) {
      this.uploadFileList = this.uploadFileList.filter(item => {
        return item.uid !== file.uid
      })
      this.onRemove(this.uploadFileList)
    },
    clearFiles() {
      this.$refs.uploader.clearFiles()
    },
    async handleFileUrlsChange(urls) {
      let ret = []
      for (let i = 0; i < urls.length; i++) {
        let index = this.uploadFileList.findIndex(item => {
          if (item.response) {
            return (item.response.data) === urls[i]
          } else {
            return (item.url === urls[i])
          }
        })
        if (index > -1) {
          // 寻找原队列中相同图片，避免重复渲染
          ret[i] = this.uploadFileList[index].origin
        } else {
          ret[i] = urls[i]
        }
      }
      this.uploadFileList = await this.formatFileUrls(ret)
    },
    async formatFileUrls (urls) {
      const fileUrls = [];
      for (let i = 0; i < urls.length; i++) {
        const targetFileUrl = urls[i]
        const fileMsg = await this.formatFileMsg(targetFileUrl)
        fileUrls.push({
          url: fileMsg.fileUrl,
          name: this.maxLimit === 1 ? (this.singleFileName || this.name) : this.name,
          origin: targetFileUrl
        })
      }
      return fileUrls
    }
  },
}
</script>

<style
  scoped
  lang="scss">
.file-tip-content {
  font-size: 12px;
  color: gray;
  line-height: 1.8;
}
</style>
