<template>
  <div class="bpmn-designer-container">
    <div class="bpmn-designer-header">

      <div>
        <el-button-group key="align-control">
          <el-button :size="headerButtonSize" type="primary" @click="elementsAlign('left')">向左对齐</el-button>
          <el-button :size="headerButtonSize" type="primary" @click="elementsAlign('right')">向右对齐</el-button>
          <el-button :size="headerButtonSize" type="primary" @click="elementsAlign('top')">向上对齐</el-button>
          <el-button :size="headerButtonSize" type="primary" @click="elementsAlign('bottom')">向下对齐</el-button>
          <el-button :size="headerButtonSize" type="primary" @click="elementsAlign('center')">水平居中</el-button>
          <el-button :size="headerButtonSize" type="primary" @click="elementsAlign('middle')">垂直居中</el-button>
        </el-button-group>

        <el-button-group key="operate-control">
          <el-button :size="headerButtonSize" type="primary" @click="processUndo">撤销</el-button>
          <el-button :size="headerButtonSize" type="primary" @click="processRedo">恢复</el-button>
          <el-button :size="headerButtonSize" type="primary" @click="processRestart">重新绘制</el-button>
        </el-button-group>

        <el-tooltip effect="light">
          <div slot="content">
            <el-button :size="headerButtonSize" type="text" @click="downloadProcessAsXml()">下载为XML文件</el-button>
            <br />
            <el-button :size="headerButtonSize" type="text" @click="downloadProcessAsSvg()">下载为SVG文件</el-button>
            <br />
            <el-button :size="headerButtonSize" type="text" @click="downloadProcessAsBpmn()">下载为BPMN文件</el-button>
          </div>
          <el-button :size="headerButtonSize" type="primary" icon="el-icon-download">下载文件</el-button>
        </el-tooltip>
      </div>

      <div style="margin-top: 16px;">
        <el-button-group key="scale-control">
          <el-tooltip content="缩小视图">
            <el-button :size="headerButtonSize" :disabled="defaultZoom < 0.2" icon="el-icon-zoom-out" @click="processZoomOut()" />
          </el-tooltip>
          <el-button :size="headerButtonSize">{{ Math.floor(defaultZoom * 10 * 10) + "%" }}</el-button>
          <el-tooltip content="放大视图">
            <el-button :size="headerButtonSize" :disabled="defaultZoom > 4" icon="el-icon-zoom-in" @click="processZoomIn()" />
          </el-tooltip>
          <el-tooltip content="重置视图并居中">
            <el-button :size="headerButtonSize" icon="el-icon-c-scale-to-original" @click="processReZoom()" />
          </el-tooltip>
        </el-button-group>

        <el-button-group key="dev-control">
          <el-button :size="headerButtonSize" @click="previewProcessJSON">预览json</el-button>
          <el-button :size="headerButtonSize" @click="previewProcessXML">预览xml</el-button>
        </el-button-group>
      </div>

    </div>

    <el-dialog title="预览" width="60%" top="16px" :visible.sync="previewModelVisible" append-to-body destroy-on-close>
      <highlightjs :language="previewType" :code="previewResult" />
    </el-dialog>

    <div id="canvas" ref="canvas"></div>
  </div>
</template>

<script>
  import { getDefaultEmpty } from './plugins/defaultEmptyXML';
  import BpmnModeler from 'bpmn-js/lib/Modeler'
  // 翻译方法
  import customTranslate from "./plugins/translate/customTranslate";
  import translationsCN from "./plugins/translate/zh";

  // 标签解析 Moddle
  import camundaModdleDescriptor from "./plugins/descriptor/camundaDescriptor.json";
  // 标签解析 Extension
  import camundaModdleExtension from "./plugins/extension-moddle/camunda/index";
  // 自定义元素选中时的弹出菜单（修改 默认任务 为 用户任务）
  import CustomContentPadProvider from "./plugins/content-pad/index";
  // 自定义左侧菜单（修改 默认任务 为 用户任务）
  import CustomPaletteProvider from "./plugins/palette/index";
  // 引入json转换与高亮
  import './plugins/hightlight';
  import convert from "xml-js";


  export default {
    props: {
      events: {
        type: Array,
        default: () => ["element.click", "element.hover", "element.out"]
      },
      headerButtonSize: {
        type: String,
        default: "small",
      },
      value: {
        type: String,
        default: '',
      },
      processName: {
        type: String,
        default: '',
      },
      processId: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        bpmnModeler: null,

        recoverable: false,
        revocable: false,

        defaultZoom: 1,

        previewModelVisible: false,
        previewResult: '',
        previewType: '',
      }
    },
    computed: {
      additionalModules() {
        let Modules = [];

        // 翻译模块
        const TranslateModule = {
          translate: ["value", customTranslate(translationsCN)]
        };
        Modules.push(TranslateModule);

        // camunda
        Modules.push(camundaModdleExtension);

        Modules.push(CustomContentPadProvider);
        Modules.push(CustomPaletteProvider);

        return Modules
      },
      moddleExtensions() {
        let Extensions = {};
        Extensions.camunda = camundaModdleDescriptor;
        return Extensions;
      }
    },
    mounted() {
      this.initModeler();
      this.createNewDiagram(this.value);
    },
    beforeDestroy() {
      if (this.bpmnModeler) this.bpmnModeler.destroy();
      this.$emit("destroy", this.bpmnModeler);
      this.bpmnModeler = null;
    },
    methods: {
      initModeler() {
        const canvas = this.$refs.canvas;
        this.bpmnModeler = new BpmnModeler({
          container: canvas,
          additionalModules: this.additionalModules,
          moddleExtensions: this.moddleExtensions
        });
        this.$emit('init-finish', this.bpmnModeler);
        this.initModelListeners();
      },
      initModelListeners() {
        const EventBus = this.bpmnModeler.get("eventBus");
        const that = this;
        // 注册需要的监听事件, 将. 替换为 - , 避免解析异常
        this.events.forEach(event => {
          EventBus.on(event, function(eventObj) {
            let eventName = event.replace(/\./g, "-");
            let element = eventObj ? eventObj.element : null;
            that.$emit(eventName, element, eventObj);
          });
        });
        // 监听图形改变返回xml
        EventBus.on("commandStack.changed", async (event) => {
          try {
            this.recoverable = this.bpmnModeler.get("commandStack").canRedo();
            this.revocable = this.bpmnModeler.get("commandStack").canUndo();
            let { xml } = await this.bpmnModeler.saveXML({ format: true });
            this.$emit("input", xml);
          } catch (e) {
            console.error(`[Process Designer Warn]: ${e.message || e}`);
          }
        });
      },
      async createNewDiagram(xml) {
        let newId = this.processId || `Process_${new Date().getTime()}`;
        let newName = this.processName || `业务流程_${new Date().getTime()}`;
        let defaultXML = xml || getDefaultEmpty(newId, newName);
        this.$emit('input', defaultXML);
        try {
          let { warnings } = await this.bpmnModeler.importXML(defaultXML);
          if (warnings && warnings.length) {
            warnings.forEach(warn => console.warn(warn));
          }
        } catch (e) {
          console.error(`[Process Designer Warn]: ${e.message || e}`);
        }
      },

      /* -----------  设计器的内部操作所需要的方法  ------------------ */
      elementsAlign(align) {
        const Align = this.bpmnModeler.get("alignElements");
        const Selection = this.bpmnModeler.get("selection");
        const SelectedElements = Selection.get();
        if (!SelectedElements || SelectedElements.length <= 1) {
          this.$message.warning("请选择多个元素对齐");
          return;
        }
        this.$confirm("自动对齐可能造成图形变形，是否继续？", "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => Align.trigger(SelectedElements, align));
      },
      processRedo() {
        this.bpmnModeler.get("commandStack").redo();
      },
      processUndo() {
        this.bpmnModeler.get("commandStack").undo();
      },
      processZoomIn(zoomStep = 0.1) {
        let newZoom = Math.floor(this.defaultZoom * 100 + zoomStep * 100) / 100;
        if (newZoom > 4) {
          throw new Error("[Process Designer Warn ]: The zoom ratio cannot be greater than 4");
        }
        this.defaultZoom = newZoom;
        this.bpmnModeler.get("canvas").zoom(this.defaultZoom);
      },
      processZoomOut(zoomStep = 0.1) {
        let newZoom = Math.floor(this.defaultZoom * 100 - zoomStep * 100) / 100;
        if (newZoom < 0.2) {
          throw new Error("[Process Designer Warn ]: The zoom ratio cannot be less than 0.2");
        }
        this.defaultZoom = newZoom;
        this.bpmnModeler.get("canvas").zoom(this.defaultZoom);
      },
      processZoomTo(newZoom = 1) {
        if (newZoom < 0.2) {
          throw new Error("[Process Designer Warn ]: The zoom ratio cannot be less than 0.2");
        }
        if (newZoom > 4) {
          throw new Error("[Process Designer Warn ]: The zoom ratio cannot be greater than 4");
        }
        this.defaultZoom = newZoom;
        this.bpmnModeler.get("canvas").zoom(newZoom);
      },
      processReZoom() {
        this.defaultZoom = 1;
        this.bpmnModeler.get("canvas").zoom("fit-viewport", "auto");
      },
      processRestart() {
        this.recoverable = false;
        this.revocable = false;
        this.createNewDiagram(null);
        this.bpmnModeler.get("canvas").zoom(1, "auto")
      },
      previewProcessXML() {
        this.bpmnModeler.saveXML({ format: true }).then(({ xml }) => {
          this.previewResult = xml;
          this.previewType = "xml";
          this.previewModelVisible = true;
        });
      },
      previewProcessJSON() {
        this.bpmnModeler.saveXML({ format: true }).then(({ xml }) => {
          this.previewResult = convert.xml2json(xml, { spaces: 2 });
          this.previewType = "json";
          this.previewModelVisible = true;
        });
      },
      downloadProcessAsXml() {
        this.downloadProcess("xml");
      },
      downloadProcessAsBpmn() {
        this.downloadProcess("bpmn");
      },
      downloadProcessAsSvg() {
        this.downloadProcess("svg");
      },
      // 下载流程图到本地
      async downloadProcess(type, name) {
        try {
          const _this = this;
          // 按需要类型创建文件并下载
          if (type === "xml" || type === "bpmn") {
            const { err, xml } = await this.bpmnModeler.saveXML();
            // 读取异常时抛出异常
            if (err) {
              console.error(`[Process Designer Warn ]: ${err.message || err}`);
            }
            let { href, filename } = _this.setEncoded(type.toUpperCase(), name, xml);
            downloadFunc(href, filename);
          } else {
            const { err, svg } = await this.bpmnModeler.saveSVG();
            // 读取异常时抛出异常
            if (err) {
              return console.error(err);
            }
            let { href, filename } = _this.setEncoded("SVG", name, svg);
            downloadFunc(href, filename);
          }
        } catch (e) {
          console.error(`[Process Designer Warn ]: ${e.message || e}`);
        }
        // 文件下载方法
        function downloadFunc(href, filename) {
          if (href && filename) {
            let a = document.createElement("a");
            a.download = filename; //指定下载的文件名
            a.href = href; //  URL对象
            a.click(); // 模拟点击
            URL.revokeObjectURL(a.href); // 释放URL 对象
          }
        }
      },
      // 根据所需类型进行转码并返回下载地址
      setEncoded(type, filename = "diagram", data) {
        const encodedData = encodeURIComponent(data);
        return {
          filename: `${filename}.${type}`,
          href: `data:application/${type === "svg" ? "text/xml" : "bpmn20-xml"};charset=UTF-8,${encodedData}`,
          data: data
        };
      },
    },
  }
</script>

<style scoped lang="scss">

  @import '~bpmn-js/dist/assets/diagram-js.css';
  @import '~bpmn-js/dist/assets/bpmn-font/css/bpmn.css';

  .bpmn-designer-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 8px;
    #canvas {
      height: 100%;
      flex: 1;
    }
  }
</style>
