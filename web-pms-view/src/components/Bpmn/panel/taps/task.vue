<template>
  <!--用户任务信息面板-->
  <div class="base-panel-container">
    <el-form label-width="100px">
      <el-form-item label="处理人：">
        <el-select
          v-model="businessObject.assignee"
          clearable
          multiple
          @change="handleChange"
        >
          <!--<el-option v-for="staff in staffList" :key="staff.id" :label="staff.realName" :value="staff.id"/>-->
          <el-option label="诸葛亮" value="zgl" />
          <el-option label="张良" value="zhangliang" />
          <el-option label="墨子" value="mozi" />
          <el-option label="孙悟空" value="swk" />
          <el-option label="花木兰" value="hml" />
          <el-option label="唐僧" value="ts" />
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'TaskPanel',
    props: {
      businessObject: {
        type: Object,
        default: () => ({})
      },
      staffList: {
        type: Array,
        default: () => []
      }
    },
    methods: {
      handleChange(val) {
        this.$emit('change', { assignee: val })
      }
    },
  }
</script>

<style scoped lang="scss">

</style>
