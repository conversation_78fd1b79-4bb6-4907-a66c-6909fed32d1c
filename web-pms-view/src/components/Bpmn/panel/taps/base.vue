<template>
  <!--流程基本信息面板-->
  <div class="base-panel-container">
    <el-form label-width="100px">
      <el-form-item label="ID：">
        <el-input disabled v-model="businessObject.id"></el-input>
      </el-form-item>
      <el-form-item label="名称：">
        <el-input v-model="businessObject.name" clearable @change="handleChange"></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  export default {
    name: 'BasePanel',
    props: {
      businessObject: {
        type: Object,
        default: () => ({})
      },
    },
    methods: {
      handleChange(val) {
        this.$emit('change', { name: val })
      }
    },
  }
</script>

<style scoped lang="scss">

</style>
