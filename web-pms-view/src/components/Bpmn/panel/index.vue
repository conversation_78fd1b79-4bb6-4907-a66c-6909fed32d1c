<template>
  <el-main class="bpmn-panel-container">
    <base-panel :business-object="activeElementBusinessObject" @change="updateBaseInfo"/>
    <task-panel :business-object="activeElementBusinessObject" @change="updateBaseInfo" v-show="elementType == 'bpmn:UserTask'"/>
  </el-main>
</template>

<script>
  import Base from './taps/base'
  import Task from './taps/task'

  export default {
    components: {
      'base-panel': Base,
      'task-panel': Task
    },
    props: {
      bpmnModeler: {
        type: Object,
      },
      // 用于用户任务的人员指定
      userList: {
        type: Array,
        default: () => []
      },
    },
    data() {
      return {
        activeElementBusinessObject: {},
      }
    },
    computed: {
      elementId() {
        return this.activeElementBusinessObject.id || '';
      },
      elementType() {
        return this.activeElementBusinessObject.$type || '';
      },
    },
    created() {
      this.initModels();
    },
    methods: {
      initModels() {
        // 初始化 modeler 以及其他 moddle
        if (!this.bpmnModeler) {
          // 避免加载时 流程图 并未加载完成
          this.timer = setTimeout(() => this.initModels(), 10);
          return;
        }
        if (this.timer) clearTimeout(this.timer);
        this.modeling = this.bpmnModeler.get("modeling");
        this.moddle = this.bpmnModeler.get("moddle");
        this.eventBus = this.bpmnModeler.get("eventBus");
        this.bpmnFactory = this.bpmnModeler.get("bpmnFactory");
        this.elementRegistry = this.bpmnModeler.get("elementRegistry");
        this.replace = this.bpmnModeler.get("replace");
        this.selection = this.bpmnModeler.get("selection");
        this.$nextTick(() => this.getActiveElement());
      },
      getActiveElement() {
        // 初始第一个选中元素 bpmn:Process
        const processElement = this.elementRegistry.find(el => el.type === "bpmn:Process");
        this.activeElementBusinessObject = { ...processElement.businessObject };
        // 监听选择事件，修改当前激活的元素以及表单
        this.bpmnModeler.on("selection.changed", ({ newSelection }) => {
          const shape = newSelection[0] || this.elementRegistry.find(el => el.type === "bpmn:Process");
          this.initFormOnChanged(shape.id);
        });
        this.bpmnModeler.on("element.changed", ({ element }) => {
          console.log(`
            =========================
            select element:
              id:  ${element.id}
            type:  ${element.businessObject.$type}
            =========================
           `);
          // 保证 修改 "默认流转路径" 类似需要修改多个元素的事件发生的时候，更新表单的元素与原选中元素不一致。
          if (element && element.id === this.activeElementBusinessObject.id) {
            this.initFormOnChanged(element.id);
          }
        });
      },
      // 选中元素时更新表单
      initFormOnChanged(id) {
        const element = this.elementRegistry.get(id);
        if (!element) return;
        this.activeElementBusinessObject = element.businessObject;
      },
      // 更新常规信息
      updateBaseInfo(attrObj) {
        const shape = this.elementRegistry.get(this.elementId);
        this.modeling.updateProperties(shape, attrObj);
      },
    },
  }
</script>

<style scoped lang="scss">
  .bpmn-panel-container {
    width: 480px;
    background: #fff;
    box-shadow: 0 0 8px 1px #ccc;
  }

</style>
