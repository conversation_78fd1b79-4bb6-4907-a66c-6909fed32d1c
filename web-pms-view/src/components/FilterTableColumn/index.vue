<script>
export default {
  name: 'FilterTableColumn',
  props: {
    label: {
      type: String,
      default: 'label'
    },
    width: {
      type: [String, Number],
      default: undefined,
    },
    prop: {
      type: String,
    },
    fixed: {
      type: String,
      default: '',
    },
    renderFn: {
      type: Function,
      default: (h, row, column) => (row[column.property])
    }
  },
  render(h) {
    const { renderFn } = this
    let templateRender = function ({ row, column }) {
      return renderFn(h, row, column)
    }
    const renderScopedSlots = {
      default: templateRender
    }
    return (
      <el-table-column
        label={ this.label }
        minWidth={ this.width }
        prop={ this.prop }
        scopedSlots={ renderScopedSlots }
        props={ { ...this.$attrs } }
      >
      </el-table-column>
    )
  }
}
</script>

<style
  lang="scss"
  scoped>

</style>
