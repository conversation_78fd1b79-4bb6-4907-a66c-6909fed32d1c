<template>
  <div>
    <el-input v-model="filterText" placeholder="输入关键字进行过滤"></el-input>
    <el-tree
      ref="tree"
      :data="tableData"
      :props="defaultProps"
      :filter-node-method="filterNode"
      accordion
      @node-click="selectDepartment"
    ></el-tree>
  </div>
</template>

<script>
import { getDepartmentList } from "@/api/system.js";
import { convert } from "../../utils";

export default {
  name: "DepartmentTree",
  props: {
    canChooseAll: {
      type: <PERSON><PERSON>an,
      default: false,
    },
  },
  data() {
    return {
      flag: "", // 请求接口的标志
      filterText: "",
      tableData: [],
      defaultProps: {
        children: "children",
        label: "departmentName",
      },
    };
  },
  watch: {
    flag(newValue) {
      this.getDepartmentList();
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  mounted() {
    this.getDepartmentList();
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.departmentName.indexOf(value) !== -1;
    },
    selectDepartment(data, node, self) {
      if (!this.canChooseAll && !node.isLeaf) {
        // 权限分配只能选择叶子
        return;
      } else if (this.canChooseAll && data.type === 2) {
        // 菜单关联父菜单不能选择按钮菜单
        this.$message.error("按钮菜单不能选为父级菜单");
        return;
      }
      this.$emit("selectDepartment", data.departmentName, data.id);
      document.body.click();
    },
    getDepartmentList() {
      getDepartmentList()
        .then((response) => {
          if (response.code === 20000) {
            this.tableData = convert(response.data, null);
          } else {
            this.$message.error(response.msg);
          }
        })
        .catch((err) => {
          this.$message.error(err);
        });
    },
  },
};
</script>

<style scoped>
</style>
