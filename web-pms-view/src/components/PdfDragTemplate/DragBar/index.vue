<template>
  <div class="drag-bar-container">
    <slot name="header"></slot>
    <h2>添加控件</h2>
    <p>请将控件拖动到文件区域</p>
    <div class="drag-type-list">
      <template
        v-for="item in componentList"
      >
        <div
          v-if="filter(item)"
          class="drag-type-item"
          :key="item.id"
          draggable
          @dragstart="(e) => onDragStart(item, e)"
          @dragend="(e) => onDragEnd(item, e)"
        >
        <span
          :style="getItemStyle(item.style)"
        >
          {{ item.label }}
        </span>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DragBar',
  props: {
    componentList: {
      type: Array,
      default: () => []
    },
    filter: {
      type: Function,
      default: () => {
        return true
      }
    }
  },
  data() {
    return {
      dragging: null,
    }
  },
  methods: {
    onDragStart(dragElm, e) {
      this.emit('dragstart', JSON.parse(JSON.stringify(dragElm)))
    },
    onDragEnd(dragElm, e) {
      this.emit('dragend')
    },
    getItemStyle(style) {
      return {
        fontSize: style.fontSize + 'px',
        color: style.textColor,
      }
    },
    emit(event, data) {
      this.$emit(event, data)
    }
  },
}
</script>

<style
  scoped
  lang="scss">
.drag-bar-container {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 20;
  width: 250px;
  height: 100vh;
  padding: 16px 8px;
  overflow-y: auto;
  background: #fff;
  box-shadow: 0px 6px 16px 0px rgba(209, 209, 209, 0.35);

  .drag-type-item {
    margin-top: 8px;
    border: 1px dashed #444;
    padding: 8px 0;
    text-align: center;
    font-family: SimSun;
  }
}
</style>
