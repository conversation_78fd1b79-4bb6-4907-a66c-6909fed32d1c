<template>
  <div class="edit-bar-box">
    <div
      class="edit-bar-container"
      v-show="activeDrag.uid">
      <div class="edit-item">
        <div>名称</div>
        <!--各组件库输入-->
        <el-input
          v-model="activeDrag.label"
          style="margin-top: 8px;"
          :disabled="activeDrag.preset === 1"
          clearable
        ></el-input>
      </div>
      <div
        class="edit-item"
        v-if="activeDrag.type === 7 || activeDrag.type === 9">
        <div class="edit-page-input">
          <div>
            <el-input
              placeholder="开始页码"
              v-model="activeDrag.pos.startPage"
              clearable></el-input>
          </div>
          至
          <div>
            <el-input
              placeholder="结束页码"
              v-model="activeDrag.pos.endPage"
              clearable></el-input>
          </div>
        </div>
      </div>
      <div class="edit-item">
        <el-button
          @click="deleteItem"
          style="width: 100%">删除
        </el-button>
      </div>
    </div>
    <div
      v-show="!activeDrag.uid"
      class="text-center"
      style="margin-top: 200px;">
      <i class="el-icon-menu"></i>
      <p>请将控件拖动到文件区域或选中已经添加到文件上的控件</p>
    </div>
  </div>
</template>

<script>
export default {
  name: "EditBar",
  props: {
    activeDrag: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    deleteItem() {
      this.$emit('delete', this.activeDrag)
    }
  },
}
</script>

<style
  lang="scss"
  scoped>
.edit-bar-box {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  width: 250px;
  height: 100vh;
  z-index: 20;
  padding: 16px;
  background: #fff;
  overflow-y: auto;
  box-shadow: 0px -6px 16px 0px rgba(209, 209, 209, 0.35);

  .edit-item {
    margin-top: 32px;
  }

  .edit-page-input {
    display: flex;
    align-items: center;
  }
}

</style>
