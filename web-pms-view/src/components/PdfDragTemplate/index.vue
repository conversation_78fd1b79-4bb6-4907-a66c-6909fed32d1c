<template>
  <div class="pdf-drag-container">
    <pdf-reader
      :src="file"
      @init-finish="onInitFinish"
      @vp-change="onVpChange"
      showFunc
    >
      <template
        v-for="item in pdf.pageNum"
        v-slot:[pdf.markPre+item]
      >
        <div
          :key="item"
          class="drag-stage"
          @drop="(e) => onDrop(e, item)"
          @dragover="onDragOver"
          @click="clearActive"
        >
          <vue-drag-resize
            v-for="(elm) in dragPageMap[item]"
            :data-page="item"
            :data-uid="elm.uid"
            :ref="'drag-' + elm.uid"
            class="drag-elm vdr"
            classNameActive="drag-elm-active"
            :key="elm.uid"
            :w="elm.style.width"
            :h="elm.style.height"
            :x="elm.pos.x || 0"
            :y="elm.pos.y || 0"
            :minWidth="20"
            :minHeight="20"
            :parent="true"
            :active="elm.uid === activeDragElm.uid"
            :resizable="elm.resizable"
            :axis="elm.axis || 'both'"
            :handles="elm.handles"
            @activated="onActivated(elm)"
            @dragstop="(x, y) => onDragStop(x, y, elm)">
            <template>
              <span
                :style="elm.style"
                class="drag-elm-label"
              >
              {{ elm.label }}
              </span>
            </template>
          </vue-drag-resize>
        </div>
      </template>
    </pdf-reader>

    <drag-bar
      :component-list="componentList"
      @dragstart="dragConfig = $event"
      @dragend="dragConfig = null"
    >
    </drag-bar>
    <edit-bar
      :active-drag="activeDragElm"
      @delete="onDeleteDrag"
    ></edit-bar>

    <div
      v-if="$slots.footer"
      class="drag-footer-container"
    >
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script>
import PDFReader from "@/components/PDFReader";
import request from '@/utils/request.js';
import DragBar from "./DragBar";
import EditBar from "./EditBar";
import VueDragResize from 'vue-draggable-resizable'
import 'vue-draggable-resizable/dist/VueDraggableResizable.css'

let uid = 0; // 组件id
export default {
  props: {
    file: {
      type: String,
      default: ''
    },
    getDragApi: {
      // 获取组件的地址
      type: String,
      default: ''
    },
    getDragMethod: {
      type: String,
      default: 'get'
    },
    getDragQuery: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    'pdf-reader': PDFReader,
    DragBar,
    EditBar,
    VueDragResize
  },
  data() {
    return {
      dragConfig: null,
      activeDragElm: {},
      componentList: [],
      pdf: {
        pageNum: 0,
        vp: {},
        markPre: 'mark-'
      },
      dragPageMap: {}
    }
  },
  mounted() {
    this.getDragComponent()
  },
  methods: {
    async getDragComponent() {
      if (!this.getDragApi) return
      const form = {
        url: this.getDragApi,
        method: this.getDragMethod,
      }
      if (this.getDragMethod === 'post') {
        form.data = this.getDragQuery
      } else {
        form.params = this.getDragQuery
      }
      const { data } = await request(form)
      this.componentList = data || []
    },
    normalizeConfig(config) {
      config.style = JSON.parse(config.style)
      return config
    },
    onDrop(e, page) {
      let config = this.normalizeConfig(this.dragConfig)
      let x = e.offsetX;
      let y = e.offsetY;
      let target = e.target;
      let otherVdr = null;

      // 落点其他框内
      if (!target.className.includes('drag-stage')) {
        otherVdr = target
        while (!otherVdr.className.includes('vdr') || otherVdr.className.includes('drag-stage')) {
          otherVdr = otherVdr.parentNode
        }
      }
      if (otherVdr) {
        let uid = otherVdr.getAttribute('data-uid')
        let dragInst = this.$refs[`drag-${ uid }`][0]
        x += dragInst.x
        y += dragInst.y
      }

      // x, y 越界
      if (x < 0) {
        x = 0;
      } else if (x > this.pdf.vp[page].width - config.style.width) {
        x = this.pdf.vp[page].width - config.style.width
      }
      if (y < 0) {
        y = 0;
      } else if (y > this.pdf.vp[page].height - config.style.height) {
        y = this.pdf.vp[page].height - config.style.height
      }

      // 骑缝章靠边处理
      if (this.isSeamBadge(config)) {
        x = this.pdf.vp[page].width - config.style.width
      }

      this.activeDragElm = this.generateElm(config, x, y, page)
      if (!this.dragPageMap[page]) {
        this.$set(this.dragPageMap, page, [])
      }
      this.dragPageMap[page].push(this.activeDragElm)
    },
    onDragOver(e) {
      e.preventDefault()
    },
    onInitFinish({ numPages }) {
      this.pdf.pageNum = numPages;
    },
    onVpChange({ vp, page }) {
      this.pdf.vp[page] = vp;
    },
    generateElm(rawConfig, x, y, page) {
      let elm = Object.assign({}, rawConfig, {
        uid: ++uid,
        resizable: rawConfig.resizable !== 0
      })
      let posX = x, posY = y
      if (elm.axis === 'both' || !elm.axis) {
        posX = x - elm.style.width / 2
        posY = y - elm.style.height / 2
      }
      let pos = {
        x: posX,
        y: posY,
        page: page + '',
      }
      if (this.isSeamBadge(elm)) {
        pos.startPage = 1
        pos.endPage = this.pdf.pageNum
      }
      elm.pos = pos
      elm.handles = rawConfig.handles || ['tl', 'tm', 'tr', 'mr', 'br', 'bm', 'bl', 'ml']
      return elm
    },
    // 拖拽组件相关
    onActivated(elm) {
      this.activeDragElm = elm || {}
    },
    clearActive(e) {
      if (e.target.className.includes('drag-stage')) {
        this.onActivated()
      }
    },
    onDragStop(x, y, elm) {
      elm.pos.x = x
      elm.pos.y = y
    },
    onDeleteDrag({ uid, pos }) {
      const { page } = pos
      let index = this.dragPageMap[page].findIndex((item) => item.uid === uid)
      if (index >= 0) {
        this.dragPageMap[page].splice(index, 1)
      }
      this.activeDragElm = {}
    },
    // 获取所有拖拽组件信息
    getAllDragInst() {
      let res = [], page = 1
      while (page <= this.pdf.pageNum) {
        let rawComps = this.dragPageMap[page]
        if (rawComps) {
          for (let i = 0; i < rawComps.length; i++) {
            res.push(this.generateResInst(rawComps[i]))
          }
        }
        page++
      }
      return res
    },
    generateResInst(config) {
      const { label, limit, required, style, pos } = JSON.parse(JSON.stringify(config))
      // 以页面左下角为坐标轴原点计算 y
      pos.x = pos.x + style.width / 2
      pos.y = this.pdf.vp[pos.page].height - style.height - pos.y + style.height / 2
      return {
        type: config.type,
        context: {
          label,
          limit,
          required,
          style,
          pos,
        }
      }
    },
    isSeamBadge(config) {
      // 判断是否骑缝章
      return config.axis === 'y' || config.type === 7 || config.type === 9
    }
  }
}
</script>


<style
  lang="scss"
  scoped
>
.pdf-drag-container {
  position: relative;
  background: #ddd;
}

.drag-stage {
  position: relative;
  height: 100%;
}

.drag-elm {
  box-sizing: border-box;
  border: 2px dashed #ccc;
  background: rgba(221, 221, 221, .5);

  &-active {
    background: rgba(135, 207, 235, .5);
  }

  &-label {
    white-space: nowrap;
  }
}

.drag-footer-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 100;
  padding: 8px 0;
  box-shadow: 6px 0px 16px 0px rgba(209, 209, 209, 0.35);
  background: #fff;
  text-align: center;
}
</style>
