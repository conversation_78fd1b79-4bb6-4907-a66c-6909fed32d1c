<template>
  <div class="height-wrapper-box">
    <div class="height-wrapper-container" :class="{'collapsed': localCollapsed}" ref="wrapper">
      <slot></slot>
    </div>
    <div class="text-center height-wrapper-btn" :class="{'btn-collapsed': localCollapsed}">
      <span class="func-content" @click="handleCollapse">
        {{ localCollapsed ? '展开' : '收起'}}
        <i v-show="localCollapsed" class="el-icon-caret-bottom"></i>
        <i v-show="!localCollapsed" class="el-icon-caret-top"></i>
      </span>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'HeightWrapper',
    props: {
      max: {
        type: [Number, String],
        default: 1000
      },
      min: {
        type: [Number, String],
        default: 100
      },
      collapsed: {
        type: Boolean,
        default: false,
      }
    },
    data() {
      return {
        localCollapsed: this.collapsed
      }
    },
    watch: {
      localCollapsed: {
        handler(val) {
          this.onCollapse(val)
        }
      }
    },
    mounted() {
      this.onCollapse(this.localCollapsed)
    },
    methods: {
      handleCollapse() {
        this.localCollapsed = !this.localCollapsed;
      },
      onCollapse(collapse) {
        if (collapse) {
          this.$refs.wrapper.style.maxHeight = this.min + 'px';
        } else {
          const maxHeight = this.max == 'auto' ? this.max : this.max + 'px';
          this.$refs.wrapper.style.maxHeight = maxHeight;
        }
      },
    },
  }
</script>

<style scoped lang="scss">
  .height-wrapper-container {
    box-sizing: content-box;
    position: relative;
    padding-bottom: 20px;
    overflow-y: hidden;
    transition: all .3s linear;

    &.collapsed {
      box-shadow: 0 -12px 12px -12px #ccc inset;
    }
  }
</style>
