<template>
  <el-dialog
    align="center"
    :visible="visible"
    :beforeClose="close"
    :append-to-body="true">
    <template v-if="prviewSrc">
      <div v-if="isOneImg">
        <img :src="prviewSrc" />
      </div>
      <div v-else>
        <el-carousel
          arrow="always"
          :autoplay="false"
        >
          <el-carousel-item
            v-for="(i, key) in prviewSrc"
            :key="key"
          >
            <img :src="i" style='height: 100%; object-fit: scale-down'/>
          </el-carousel-item>
        </el-carousel>
      </div>
    </template>
  </el-dialog>
</template>

<script>
  export default {
    data() {
      return {
        visible: false,
        src: null,
        initialIndex: 0,
      }
    },
    computed: {
      isOneImg() {
        return typeof this.src === 'string' || !Array.isArray(this.src)
      },
      prviewSrc() {
        if (this.src && typeof this.src === 'object' && !Array.isArray(this.src) && this.src.url) {
          // 若是对象，返回对象中的url属性
          return this.src.url
        }
        return this.src
      }
    },
    methods: {
      close() {
        this.visible = false;
        this.$nextTick(() => {this.src = null})
      },
      open() {
        this.visible = true;
      }
    },
  }
</script>

<style scoped lang="scss">

</style>
