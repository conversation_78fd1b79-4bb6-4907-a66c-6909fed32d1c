/* 浮动 */
.clearfix::after, .clearfix::before {
  content: '';
  display: block;
  clear: both;
}
.fl {
  float: left;
}
.fr {
  float: right;
}

/* 文字样式 */
.bold {
  font-weight: bold;
}
.bolder {
  font-weight: bolder;
}
.line-pass {
  text-decoration: line-through;
}
.red {
  color: #f00;
}

/* 行内文字省略 */
.text-hidden {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.text-hidden-line2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.text-inline {
  display: inline-block;
  vertical-align: baseline;
}

/* 行内居中 */
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}

/* 垂直居中 */
.tb-center {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
/* 水平居中 */
.lr-center {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}
/* 水平垂直居中 */
.all-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
}

/* 横向弹性布局 */
.flex-box {
  display: flex;
  align-items: center;
}
.flex-item {
  flex: 1 0;
}
.flex-col {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 阴影 */
.shadow {
  box-shadow: 0px 5px 13px 0px rgba(0,0,0,0.05);
}
.shadow-gray {
  box-shadow: 0px 6px 16px 0px rgba(209,209,209,0.35);
}
.shadow-shallow {
  box-shadow: 0px 4px 32px 0px rgba(240,183,210,0.12);
}

/* dashed */
.dashed {
  border-bottom: 1px dashed #EBEBEB;
}
.solid {
  border-bottom: 1px solid;
}
/* button 重置 */
button {
  margin: 0 !important;
  padding: 0 !important;
  font-weight: normal !important;
  line-height: 48rpx !important;
  width: auto !important;
  background: transparent;
  font-size: inherit !important;
}

/* mask */
.block-mask {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, .5);
  z-index: 10;
}

image {
  max-width: 100%;
}

.func {
  color: var(--theme-func);
}

/* border */
.border-4 {
  border-radius: 4rpx;
}
.border-10 {
  border-radius: 10rpx;
}
.border-14 {
  border-radius: 14rpx;
}
.border-20 {
  border-radius: 20rpx;
}

.linear-gradient {
  background: linear-gradient(
    135deg, 
    var(--gradient-start) 0%, 
    var(--gradient-start) 25%, 
    var(--gradient-end) 64%);
}