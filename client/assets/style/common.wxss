/* 任务列表样式 */
.task-info-page {
  padding: 30rpx;
}
.task-head-box {
  padding: 25rpx 32rpx 22rpx 38rpx;
  background: #fff;
}
.task-info-box {
  margin-bottom: 20rpx;
  padding: 40rpx;
  background: #fff;
}
.task-header-container {
  padding-bottom: 24rpx;
}
.task-header {
  font-weight: 500;
  font-size: 30rpx;
}
.task-title {
  font-size: 30rpx;
  color: var(--theme-black);
}
.task-people-num {
  font-size: 24rpx;
  color: var(--theme-tooltip-gray);
}
.task-price {
  color: var(--theme-red);
  font-weight: 500;
}
.task-info {
  padding-top: 18rpx;
  font-size: 26rpx;
  color: #444;
}
.task-label {
  margin: 10rpx 0 0;
  height: 50rpx;
  overflow-y: hidden;
}
.task-label-text {
  margin-right: 14rpx;
  padding: 4rpx 14rpx;
  border-radius: 6rpx;
  font-size: 18rpx;
  border: 1px solid currentColor;
}
.task-label-text:nth-child(1) {
  color: #4E91FF;
}
.task-label-text:nth-child(2) {
  color: #6ADEA0;
}
.task-label-text:nth-child(3) {
  color: #FFC05C;
}
.task-footer {
  margin: 20rpx 0;
  color: #444;
}
/* detail */
.task-detail-card {
  margin: 20rpx 0;
  overflow: hidden;
  background: #fff;
}
.task-detail-box {
  padding: 0 20rpx;
  overflow: hidden;
}
.task-info-item {
  margin: 40rpx 0 30rpx;
  color: var(--theme-black);
}
/* desc */
.task-desc-box {
  margin: 28rpx 10rpx;
  color: var(--theme-black);
}
.task-desc-title {
  margin-bottom: 10rpx;
  font-size: 26rpx;
}
.task-detail-content {
  font-size: 26rpx;
  white-space: pre-line;
}