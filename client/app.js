import { getToken, setToken } from "./utils/token"
import {store} from './store/index'
import { getUserInfo } from "./api/user";
import { buildParams, toPromise } from "./utils/util";
import { fileUrl } from "./setting/index";
import { normalizeDict } from "./utils/dict";
import Stack from './utils/stack'
import getVersion from "./utils/version";

const pageStack = new Stack()

const APP_NAME = ''
// 不用登录也可以进的页面白名单
const whiteList = [
  'pages/home/<USER>',
  'pages/index/index',
  'pages/index/phone', 
  'pages/index/login', 
  'pages/auth/index', 
  'pages/auth/result', 
  'pages/file/service', 
  'pages/file/privacy'
]

// 进入后需要重新获取用户信息
const loginOnShowList = [
  'pages/task/detail'
]

// app.js
let OriginPage = Page
Page = function (options) {
  let defaultPageSetting = {
    onShareAppMessage() {
      return {
        title: APP_NAME,
      }
    },
    navTo(options, isSwitch = false) {
      if (isSwitch) {
        wx.switchTab(options)
      } else {
        if (options.redirect) {
          delete options.redirect
          wx.redirectTo(options)
        } else {
          wx.navigateTo(options)
        }
      }
    },
    tapNav(e) {
      const {url = '', redirect = false, isswitch = false} = e.currentTarget.dataset
      this.navTo({ url, redirect }, isswitch)
    }
  }

  async function onShow () {
    let oldFn = options.onShow
    if (loginOnShowList.indexOf(getCurrentPages()[0].route) > -1 && getToken()) {
      const app = getApp()
      await app.getUserInfo()
    }
    oldFn && oldFn.call(this)
  }


  return OriginPage({
    ...defaultPageSetting,
    ...options,
    onShow,
  })
}

App({
  async onLaunch() {
    this.appRouteHook()
  },
  onShow(options) {
    if (!/104(7|8|9)/.test(options.scene)) pageStack.deactive()
    wx.nextTick(() => {
      setTimeout(() => {
        this.checkLogin()
      }, 300);
      this.getMenuBtnStyle()
    })
  },
  appRouteHook () {
    // 全局app分享设置
    wx.onAppRoute(async (res) => {
      const pages = getCurrentPages();
      const view = pages[pages.length - 1];
      console.log('on app route >>>>>', view)
      // 页面路径入栈
      pageStack.pushStack(view.route + buildParams(view.options))
      if (view.data.noInject) return;
      if (view) {
        // 分享注入
        view.onShareAppMessage = () => {
          return {
            title: APP_NAME,
            path: res.path,
            imageUrl: '/assets/image/share.png'
          }
        }
        view.goTo = (options, ...args) => {
          if (typeof options === 'string') {
            options = {
              url: options,
            }
          }
          const routeOptions = Object.assign({}, options, ...args)
          wx.navigateTo(routeOptions)
        }
        // 检查是否有用户信息
        let viewIndex = whiteList.indexOf(view.route)
        let info = store.userInfo
        if (!info.phone) {
          if (getToken() || viewIndex === -1) {
            // 已登陆
            // 不在白名单
            info = (await this.getUserInfo()) || info
          }
        }

        if (viewIndex === -1 && !info.phone && view.route !== 'pages/index/index') {
          wx.setStorage({
            key: 'login_redirect',
            data: '/' + view.route + buildParams(view.options)
          }).then(() => {
            wx.redirectTo({
              url: '/pages/index/index',
            })
          })
          return
        }

        view.setData({
          dict: store.userInfo.dictMap,
          fileUrl: fileUrl
        })
        
        if (viewIndex === -1) {
          // 不在白名单，需要校验实名认证
          if (!info.verify && (getVersion() !== 'develop')) {
            this.showVerifyDialog()
          }
        }

      }
    })
  },
  checkLogin() {
    let currentRoute = getCurrentPages()[0].route
    let token = getToken()
    if (token) return
    if (whiteList.indexOf(currentRoute) > -1) {
      return
    }
    if (currentRoute !== 'pages/index/index') {
      setToken()
      wx.navigateTo({
        url: '/pages/index/index',
      })
    }
  },
  async getUserInfo() {
    console.log('>>>>>>>>>UserInfo<<<<<<<<')
    const [{ data }, err] = await toPromise(getUserInfo())
    if (!data) return null
    normalizeDict(data)
    store.setUserInfo(data)
    console.log('userInfo' , data)
    return data
  },
  dictCode(dict, code) {
    return store.userInfo.dictMap[dict][code] || ''
  },
  showVerifyDialog() {
    wx.showModal({
      content: '您还没有完成实名认证，实名认证(上传身份证)后才可以接单',
      showCancel: false,
      confirmText: '去认证',
      success({ confirm }) {
        if (confirm) {
          wx.redirectTo({
            url: '/pages/auth/index',
          })
        }
      }
    })
  },
  getMenuBtnStyle() {
    const res = wx.getMenuButtonBoundingClientRect()
    this.globalData.menuStyle = res
    return res
  },
  globalData: {
      store,
      menuStyle: {},
      pageStack
    }
})
