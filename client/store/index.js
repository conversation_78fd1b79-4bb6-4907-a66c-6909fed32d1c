import { observable, action } from 'mobx-miniprogram'

const data = {
  userInfo: {
    avatarUrl: null,
    code: null,
    gender: null,
    inviteCode: null,
    nickName: null,
    parentId: null,
    phone: null,
    userId: null,
    verify: false,
    dict: [],
    dictMap: {},
  },

  statusBarInfo: {}
}

// !! actions 不能用箭头函数作定义
const actions = {
  setUserInfo: action(function (info) {
    if (info) {
      this.userInfo = Object.assign({}, this.userInfo, info)
    } else {
      this.userInfo = {};
    }
  }),
  setScreenType: action(function (info) {
    const { rect = {} } = info
    this.statusBarInfo = {...rect}
    this.statusBarInfo.bottom += 10
  })
}

export const store = observable(Object.assign({}, data, actions))