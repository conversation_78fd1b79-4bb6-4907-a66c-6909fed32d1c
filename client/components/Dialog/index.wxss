/* components/Dialog/index.wxss */
.dialog-container {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, .5);
}
.dialog-box {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  text-align: center;
  padding: 40rpx;
  border-radius: 20rpx;
  /* background: #fff; */
  z-index: 10;
}
.dialog-title {
  font-size: 40rpx;
  font-weight: bolder;
}
.dialog-content-container {
  margin-top: 40rpx;
  font-size: 28rpx;
  color: var(--theme-tooltip-gray);
}
.dialog-footer-container {
  margin-top: 48rpx;
}

