// components/Dialog/index.js
Component({
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多 slot 支持
  },
  /**
   * 组件的属性列表
   */
  properties: {
    title: {
      type: String,
      value: ''
    },
    width: {
      type: String,
      value: '60%'
    },
    noBack: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    visible: false,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    toggle(status = false) {
      this.setData({
        visible: status
      })
    },
  }
})
