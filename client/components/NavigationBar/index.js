// components/NavigationBar/index.js
const app = getApp()
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    height: {
      type: String,
      value: '176', 
    },
    title: {
      type: String,
      value: '智享汇助手'
    },
    showback: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    marginTop: 0,
    imgClass: '',
  },
  lifetimes: {
    attached() {
      wx.nextTick(() => {
        this.getMarginTop()
      })
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    back() {
      wx.navigateBack({
        delta: 1,
      })
    },
    getMarginTop() {
      const { top } = app.globalData.menuStyle
      const imgClass = Number(this.data.height) < 350 ? 'center-img' : 'full-img'
      this.setData({
        marginTop: top + 4,
        imgClass,
      })
    }
  }
})
