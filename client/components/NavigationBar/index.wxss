/* components/NavigationBar/index.wxss */
.navigation-bar-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  overflow: hidden;
  z-index: 100;
}
.center-img {
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  width: 100%;
  z-index: -1;
}
.full-img {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: -1;
}
.navigation-bar-title {
  position: relative;
  text-align: center;
  color: #fff;
  font-size: 32rpx;
  z-index: 100;
}

/* back */
.navigation-back {
  padding: 30rpx;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);

}