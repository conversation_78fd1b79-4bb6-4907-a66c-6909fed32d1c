// components/hj-icon/hj-icon.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    icon: {
      type: String,
      value: ''
    },
    size: {
      type: Number,
      value: 40
    },
    height: {
      type: Number,
      value: 0
    },
    type: {
      type: String,
      value: 'png'
    }
  },
  observers: {
    'icon': function(icon, s) {
      this.setData({
        iconPath: `/assets/icon/${icon}.${this.data.type}`,
      })
    },
  },
  /**
   * 组件的初始数据
   */
  data: {
    iconPath: '',
  },

  /**
   * 组件的方法列表
   */
  methods: {

  }
})
