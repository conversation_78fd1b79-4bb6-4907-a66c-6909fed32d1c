/* components/Upload/image.wxss */
.upload-image-item {
  display: inline-block;
  position: relative;
  width: 140rpx;
  height: 140rpx;
  overflow: hidden;
  margin: 0rpx 20rpx 20rpx 0;
}
.upload-image-block {
  position: relative;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.upload-image-mask {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background: #000;
  opacity: .3;
}
.upload-image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 1;
}
.upload-image-icon {
  transform-origin: center center;
  animation: cirleLoading .5s infinite;
}
.upload-image-add {
  height: 100%;
  text-align: center;
}
.upload-add-handler {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  height: 100%;
  border: 1px dashed #ddd;
  color: #8E8E8E;
}
.upload-handler-img {
  width: 55rpx;
}
.upload-image-delete {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 5;
  line-height: normal;
}

@keyframes cirleLoading {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* image */
.upload-image-inner {
  height: 100% !important;
}