<!--components/Upload/image.wxml-->
<view class="upload-image-comp">
  <view class="upload-image-list">
    <view
      class="{{fileType === 'pic-list' ? 'upload-image-item' : 'upload-image-block'}}"
      wx:for="{{localList}}"
      wx:key="index"
      bindtap="previewImg"
      style="height: {{height}}rpx;"
    >
      <!-- loading -->
      <view
        class="upload-image-mask"
        wx:if="{{item.loading}}"
      >
        <view class="upload-image-loading">
          <view class="upload-image-icon">
            <hj-icon icon="loading"></hj-icon>
          </view>
        </view>
      </view>
      <!-- image -->
      <image
        wx:if="{{item.url}}"
        src="{{item.status === 'loading' ? item.url : (fileUrl + item.url)}}"
        mode="widthFix"
        class="upload-image-inner"
      ></image>
      <!-- close -->
      <view
        class="upload-image-delete"
        catchtap="onDelImage"
        wx:if="{{ showClose }}"
        data-index="{{index}}"
      >
        <mp-icon
          icon="close2"
          size="{{25}}"
          class="upload-image-delete"
          data-index="{{index}}"
        ></mp-icon>
      </view>
    </view>
    <view
      class="{{fileType === 'pic-list' ? 'upload-image-item' : ''}}"
      wx:if="{{showAdd}}"
    >
      <view
        wx:if="{{defaultTrigger}}"
        class="upload-image-add"
        bindtap="onAddImage"
      >
        <view class="upload-add-handler">
          <image
            class="upload-handler-img"
            src="/assets/image/upload.png"
            mode="widthFix"
          ></image>
          <view>添加图片</view>
        </view>
      </view>
      <view wx:else bindtap="onAddImage">
        <slot name="trigger"></slot>
      </view>
    </view>
  </view>
</view>