// components/Upload/image.js
import { upload } from '../../api/common'
import { fileUrl } from '../../setting/index'
import { previewImage } from '../../utils/util'
Component({
  options: {
    multipleSlots: true
  },
  /**
   * 组件的属性列表
   */
  properties: {
    list: {
      type: String,
      optionalTypes: [Array],
      value: '',
    },
    showAdd: {
      type: Boolean,
      value: true,
    },
    showClose: {
      type: Boolean,
      value: true,
    },
    defaultTrigger: {
      type: Boolean,
      value: true,
    },
    fileType: {
      // 行内图片卡片样式
      type: String,
      value: 'pic-list'
    },
    height: {
      type: String,
      value: '180'
    },
    disabled: {
      type: Boolean,
      value: false,
    }
  },

  observers: {
    list(val) {
      if (val) {
        let newList
        if (typeof val === 'string') {
          newList = [{url: val, uid: Date.now() + '0', loading: false, status: 'success'}]
        } else if (Array.isArray(val)) {
          newList = val.map((i,k) => ({url: val, uid: Date.now() + '' + i, loading: false, status: 'success'}))
        }
        if (newList) {
          this.setData({
            localList: newList
          })
        }
      } else {
        this.setData({
          localList: []
        })
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    localList: [],
    fileUrl,
  },

  /**
   * 组件的方法列表
   */
  methods: {
    previewImg(e) {
      let {index} = e.currentTarget.dataset
      previewImage(this.data.localList.map(item => fileUrl + item.url), index)
    },
    onAddImage(e) {
      if (this.data.disabled) return
      let vm = this;
      wx.chooseMedia({
        mediaType: ['image'],
        async success(res) {
          let tmpPath = res.tempFiles[0].tempFilePath
          let file = {
            url: tmpPath, 
            loading: true, 
            uid: Date.now() + '' + vm.data.localList.length, 
            status: 'loading'
          }
          let index = vm.data.localList.length
          vm.setData({
            localList: [...vm.data.localList, file]
          })
          const { data } = await upload(tmpPath)
          vm.setData({
            [`localList[${index}].url`]:  data,
            [`localList[${index}].loading`]: false,
            [`localList[${index}].status`]:  'success',
          })
          vm.changeList()
        }
      })
    },
    onDelImage(e) {
      if (this.data.disabled) return
      let { index } = e.currentTarget.dataset
      this.data.localList.splice(index, 1)
      this.setData({
        localList: this.data.localList
      })
      this.changeList()
    },
    changeList() {
      this.triggerEvent('change', {
        value: this.data.localList,
      })
    },
    toggleLoading(index, status) {
      this.data.localList[index].loading = status
      this.setData({
        localList: this.data.localList
      })
    }
  }
})
