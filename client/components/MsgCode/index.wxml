<!--components/MsgCode/index.wxml-->
<view class="valid-code-box">
  <view class="valid-code-list">
    <view
      wx:for="{{length}}"
      wx:key="index"
      class="valid-code-item"
      bindtap="onTabItem">
      <view class="valid-code-num {{ index == active ? 'active' : ''}}">
        {{ inputStr[item] }}
      </view>
      </view>
  </view>
</view>

<input 
  type="number"
  focus="{{focus}}"
  hold-keyboard="{{true}}" 
  bindinput="onInput"
  bindblur="onBlur"
  class="hidden-input" 
  value="{{inputVal}}"/>