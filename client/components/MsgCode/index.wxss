/* components/MsgCode/index.wxss */
.valid-code-list {
  display: flex;
  align-items: center;
  justify-content: center;
}
.valid-code-item {
  width: 100rpx;
  height: 180rpx;
  line-height: 180rpx;
  text-align: center;
  padding: 0 40rpx;
  font-size: 40rpx;
}
.valid-code-num {
  height: 100%;
  border-bottom: 2px solid var(--theme-gray);
}
.valid-code-num.active {
  border-bottom: 2px solid var(--theme-black);
}

/* hidden input */
.hidden-input {
  transform: translate3d(-99999px, -99999px, 0);
}