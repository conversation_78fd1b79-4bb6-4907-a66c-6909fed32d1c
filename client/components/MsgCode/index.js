// components/MsgCode/index.js
const computedBehavior = require('miniprogram-computed').behavior
Component({
  behaviors: [computedBehavior],
  computed: {
    inputStr(data) {
      return String(data.inputVal)
    }
  },
  /**
   * 组件的属性列表
   */
  properties: {
    length: {
      type: Number,
      value: 4,
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    active: 0,
    inputVal: '',
    focus: true
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onInput(e) {
      let {value} = e.detail;
      if (value.length > this.data.length) return;
      this.setData({
        inputVal: value,
        active: value.length
      })
      if (value.length == this.data.length) {
        this.triggerEvent('change', value)
      }
    },
    onBlur(e) {
      this.setData({
        focus: false,
      })
    },
    onTabItem(e) {
      this.setData({
        focus: true,
      })
    },
  }
})
