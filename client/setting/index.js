import getVersion from '../utils/version';

const baseUrls = {
  // develop: 'https://mini-dev.hjzxh.com/gateway/dev/trade', // 开发环境
  develop: 'https://mini-dev.hjzxh.com/gateway/test/trade', // 开发接测试
  trial: 'https://mini-dev.hjzxh.com/gateway/test/trade', // 体验版
  release: 'https://api.hjzxh.com/trade', // 生产环境
}
const baseUrl = baseUrls[getVersion()];

const taskBaseUrls = {
  // develop: 'https://mini-dev.hjzxh.com/gateway/dev', // 开发环境
  develop: 'https://mini-dev.hjzxh.com/gateway/test', // 开发接测试
  trial: 'https://mini-dev.hjzxh.com/gateway/test', // 体验版
  release: 'https://api.hjzxh.com', // 生产环境
}
const taskBaseUrl = taskBaseUrls[getVersion()];

// 文件服务器地址
const fileUrls = {
  develop: 'https://static.hjzx.tech/',
  trial: 'https://static.hjzx.tech/', // 体验版
  release: 'https://static.hjzxh.com/', // 生产环境
}
const fileUrl = fileUrls[getVersion()]


const uploadUrls = {
  develop: 'https://mini-dev.hjzxh.com/weChat',
  trial: 'https://mini-dev.hjzxh.com/weChat', // 体验版
  release: 'https://api-backend.hjzxh.com/portal/weChat', // 生产环境
}
const uploadUrl = uploadUrls[getVersion()]
console.log('******version***********', getVersion(), baseUrl)
export {
  baseUrl,
  fileUrl,
  uploadUrl,
  taskBaseUrl,
}
