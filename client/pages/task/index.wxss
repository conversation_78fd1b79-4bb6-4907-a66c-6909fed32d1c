/* pages/task/index.wxss */
page {
  background: var(--theme-page-back);
}
.tab-box {
  padding: 0 30rpx;
  background: #fff;
  justify-content: space-evenly;
  font-size: 28rpx;
  color: var(--theme-black);
}
.tab-item {
  padding: 15rpx 45rpx;
}
.tab-item.active {
  color: var(--font-primary);
  border-bottom: 4rpx solid currentColor;
}

.list-box {
  margin: 20rpx 30rpx;
}
.list-item {
  position: relative;
  margin-top: 20rpx;
  padding: 30rpx;
  border-radius: 20rpx;
  background: #fff;
  overflow: hidden;
}
.task-title {
  font-size: 28rpx;
}
.task-info {
  padding: 20rpx 0;
}


.task-status {
  position: absolute;
  right: 0;
  bottom: 0;
}
.task-btn {
  width: 200rpx;
  height: 70rpx;
  border-radius: 20rpx  0 20rpx 0;
  line-height: 70rpx;
  text-align: center;
  white-space: nowrap;
  background: var(--primary);
  color: #fff;
}

.task-settle-status {
  position: absolute;
  right: 20rpx;
  bottom: 40rpx;
  z-index: 1;
  width: 128rpx;
  height: 114rpx;
  overflow: hidden;
  line-height: 0;
}
.task-settle-icon {
  height: 100%;
}

/* empty */
.list-empty-box {
  font-size: 24rpx;
  color: var(--theme-deep-gray);
}
.list-empty-img {
  margin: 100rpx 200rpx 0;
}
