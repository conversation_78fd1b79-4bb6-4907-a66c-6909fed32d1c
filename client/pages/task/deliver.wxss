/* pages/task/deliver.wxss */
page {
  background: var(--theme-page-back);
}
/* desc */
.task-info-page {
  padding-bottom: 100rpx;
}
.task-info-box {
  padding: 0;
}
.task-desc-box {
  margin: 0;
  margin: 25rpx 30rpx 20rpx;
  font-size: 26rpx;
}
.task-desc-content {
  color: var(--theme-black);
  white-space: pre-line;  
}
.task-desc {
  font-size: 26rpx;
  color: #ccc;
}
.task-deliver-box {
  border-top: 1px dashed var(--theme-back-gray);
}

/* result */
.task-result-box {
  padding: 25rpx 30rpx;
}
.task-result-content {
  margin: 18rpx 0;
  font-size: 24rpx;
}

.task-desc-input {
  display: block;
  width: 100%;
  height: 80rpx;
  font-size: 24rpx;
}
.task-submit-btn {
  margin: 93rpx 0 0;
  padding: 20rpx 40rpx;
  z-index: 999;
  font-size: 28rpx;
}
.primary-btn {
  width: 100%;
  color: #fff;
}