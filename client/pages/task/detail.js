// pages/task/detail.js
import {
  createStoreBindings
} from "mobx-miniprogram-bindings";
import {
  taskDetail,
  acceptJob
} from '../../api/task'
import {
  buildParams,
  toPromise
} from "../../utils/util";
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    show: false,
    toSignList: ['无记录', '已签约', '未签约'],
    halfConfirm: false,

    id: '',
    task: {},
    isLoad:false,

    showBack: true, // 扫码进入返回按钮显示
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // if (options.confirm) {
    //   this.setData({
    //     show: true,
    //     halfConfirm: true,
    //   })
    // }
    

    if (options.id) {
      this.setData({
        id: options.id
      })
    } else if(options.scene) {
      this.setData({
        id: decodeURIComponent(options.scene),
        showBack: false
      })
    } else {
      wx.showToast({
        title: '找不到任务ID',
        icon: 'none'
      })
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.storeBindings = createStoreBindings(this, {
      store: app.globalData.store,
      fields: ["userInfo"],
    });

    if (this.data.id) {
      console.log('task id = ', this.data.id)
      this.getDetail()
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    this.storeBindings.destroyStoreBindings();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {

  },
  openHalf() {
    // this.setData({
    //   show: true,
    // })
    // this.onClickHalf()
    this.selectComponent('#dialog').toggle(true)
  },
  async onClickHalf() {
    // if (!this.data.halfConfirm) {
    //   wx.showToast({
    //     title: '请先查看操作指引并完成税务认证',
    //     icon: 'none'
    //   })
    // } else {
    //   const [res, err] = await toPromise(acceptJob({
    //     id: this.data.id
    //   }))
    //   if (err) return;
    //   wx.navigateTo({
    //     url: '/pages/task/receive',
    //   })
    // }
    const [res, err] = await toPromise(acceptJob({
      id: this.data.id
    }))
    if (err) return;
    wx.navigateTo({
      url: '/pages/task/receive',
    })
  },
  goToGuide() {
    wx.navigateTo({
      url: '/pages/task/guide' + buildParams({
        id: this.data.id
      }),
    })
  },
  async getDetail() {
    const {
      data
    } = await taskDetail({
      id: this.data.id
    })
    if (data.jobTag) {
      data["tags"] = JSON.parse(data.jobTag)
    }
    let sexText = this.data.dict['WorkerGenderEnum'][data.workerGender]
    let ageText = "年龄" + this.data.dict['WorkerAgeLimitTypeEnum'][data.workerAgeLimitType]
    let eduBackgroundText = this.data.dict['EduBackgroundEnum'][data.eduBackground]
    if (data.workerGender == 2) {
      sexText = "性别不限"
    }
    if (data.workerAgeLimitType == 100) {
      ageText = `${data.workerAgeLimitMin} ~ ${data.workerAgeLimitMax}岁`
    }
    if (data.eduBackground == -1) {
      eduBackgroundText = "不限学历"
    }

    let requirementText=`${sexText}|${ageText}|${eduBackgroundText}`
    data.requirementText=requirementText
    this.setData({
      task: data,
      isLoad:true,
    })
  },
  closeDialog() {
    this.selectComponent('#dialog').toggle(false)
  },
  acceptTask() {
    this.onClickHalf()
    wx.nextTick(() => this.closeDialog())
  }
})