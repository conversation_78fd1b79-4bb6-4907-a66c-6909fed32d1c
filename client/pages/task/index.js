// pages/task/index.js
import {
  jobList
} from '../../api/task'
import {
  buildParams
} from '../../utils/util';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    active: 0,
    tabList: [{
      label: '待录用',
      value: 100,
    }, {
      label: '进行中',
      value: 300,
    }, {
      label: '已完成',
      value: 400,
    }, ],

    list: [],
    page: 1,
    end: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {},

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getList(true)
  },

  onReachBottom() {
    if (this.data.end || this.loading) return;
    this.getList()
  },


  changeTab(e) {
    if (this.data.loading) return
    let {
      index
    } = e.currentTarget.dataset;
    this.setData({
      active: index,
    })
    this.getList(true)
  },
  clickItem(e) {
    let {
      id
    } = e.currentTarget.dataset;
    wx.navigateTo({
      url: '/pages/task/deliver' + buildParams({
        id
      }),
    })
    // switch (this.data.active) {
    //   case 0:
    //   case 1:
    //     wx.navigateTo({
    //       url: '/pages/task/deliver' + buildParams({ id }),
    //     })
    //     break
    //   default:
    //     break;
    // }
  },

  async getList(init) {
    if (init) {
      this.setData({
        page: 1,
        end: false,
        list: [],
      })
    }

    this.loading = true;
    const {
      data: {
        totalRecord,
        rows
      }
    } = await jobList({
      pageCurrent: this.data.page,
      pageSize: 10,
      jobStatus: this.data.tabList[this.data.active]['value'],
    })

    this.loading = false;
    let list = init ? rows : this.data.list.concat(rows)
    list.forEach(e => {
      if (e.jobTag) {
        e["tags"] = JSON.parse(e.jobTag)
      }
    })
    this.setData({
      list,
      end: totalRecord <= list.length,
    })

  }
})