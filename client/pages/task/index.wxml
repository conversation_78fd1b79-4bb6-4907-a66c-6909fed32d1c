<!--pages/task/index.wxml-->
<wxs
  src="/wxs/money.wxs"
  module="money"
></wxs>
<navigation-bar></navigation-bar>
<view class="tab-box flex-box">
  <view
    class="{{'tab-item text-center ' + (active === index ? 'active' : '')}}"
    data-value="{{ item.value }}"
    data-index="{{index}}"
    bindtap="changeTab"
    wx:for="{{tabList}}"
    wx:key="index"
  >
    {{ item.label }}
  </view>
</view>

<view class="list-box" wx:if="{{list.length}}">
  <view
    class="list-item shadow"
    bindtap="clickItem"
    wx:for="{{list}}"
    wx:key="index"
    data-id="{{item.id}}"
  >
    <view class="task-header flex-box">
      <view class="task-tiitle flex-item">{{ item.jobName }}</view>
      <view
        class="task-price"
        wx:if="{{item.rewardType==200}}"
      >面议</view>
      <view
        class="task-price"
        wx:else
      >{{ money.format(item.rewardAmount) }}元/{{ dict['PayTypeEnum'][item.payType] }}</view>
    </view>
    <view class="task-info dashed">
      {{ item.jobCityName}} {{ item.jobAreaName }}
      <text
        class="fr"
        wx:if="{{active == 1}}"
      >{{ dict['DeliveryStatusEnum'][item.deliveryStatus] }}</text>
    </view>
    <view class="task-label">
      <text
        class="task-label-text"
        wx:for="{{ item.tags }}"
        wx:key="index"
      >{{ item }}</text>
    </view>
    <view
      class="task-status"
      data-id="{{item.id}}"
      catchtap="clickItem"
      wx:if="{{ active == 1 }}"
    >
      <view
        class="task-btn"
        wx:if="{{item.deliveryStatus == 100}}"
      >
        任务交付
      </view>
      <view
        class="task-btn"
        wx:else
      >
        查看交付物
      </view>
    </view>
    <view
      class="task-settle-status"
      wx:if="{{item.workerJobStatus == 400}}"
    >
      <image
        class="task-settle-icon"
        src="/assets/image/settled.svg"
        alt=""
        wx:if="{{item.settleStatus == 100}}"
      />
      <image
        class="task-settle-icon"
        src="/assets/image/unsettled.svg"
        alt=""
        wx:if="{{item.settleStatus == 200}}"
      />
    </view>
  </view>
</view>

<view class="list-box text-center list-empty-box" wx:else>
  <view class="list-empty-img">
    <image src="/assets/image/empty.svg" mode="widthFix"></image>
  </view>
  <view>暂无{{tabList[active].label}}任务</view>
</view>