<!--pages/task/deliver.wxml-->
<wxs src="/wxs/money.wxs" module="money"></wxs>
<navigation-bar title="任务详情" showback></navigation-bar>
<view class="task-info-page">
  <view class="task-head-box border-10 shadow">
    <view class="task-header flex-box">
      <view class="flex-item task-title">{{ task.jobName }}</view>
      <view class="task-people-num">招收 {{ task.workerNum }} 人</view>
    </view>
      <view class="task-price dashed" wx:if="{{task.rewardType==200}}">面议</view>
      <view class="task-price dashed" wx:else>{{ money.format(task.rewardAmount) }}元/{{ dict['PayTypeEnum'][task.payType] }}</view>
    <view class="task-label">
      <text class="task-label-text" wx:for="{{ task.tags }}" wx:key="index">{{ item }}</text>
    </view>
  </view>

  <view class="task-detail-card border-10 shadow">
    <view class="task-info-box">
      <view class="task-desc-box">
        <view class="task-desc-title bold">任务描述</view>
        <view class="task-desc-content">{{ task.jobDescribe || '-' }}</view>
      </view>
      <view class="task-desc-box">
        <view class="task-desc-title bold">交付标准</view>
        <view class="task-desc-content">{{ task.deliveryStandard || '-'}}</view>
      </view>
    </view>
    <view 
      class="task-info-box task-deliver-box" 
      wx:if="{{task.workerJobStatus >= 300}}">
      <view class="task-desc-box">
        <view class="task-desc-title bold">工作结果</view>
        <textarea 
          class="task-desc-input" 
          placeholder="请填写实际工作结果，如有需要，请上传工作证明图片～" 
          data-field="deliveryContent" bindinput="onChangeField" 
          value="{{task.deliveryContent}}"></textarea>
        <upload-image 
          bindchange="onChangeField" 
          data-field="attachment" 
          height="140"
          list="{{task.attachment}}" 
          showAdd="{{ task.deliveryStatus == 100 }}"
          showClose="{{ task.deliveryStatus == 100 }}">
        </upload-image>
      </view>
    </view>

  </view>

  <view 
    class="task-info-box task-result-box border-10 shadow" 
    wx:if="{{task.deliveryStatus != 100}}">
    <view>
      <view class="task-desc-title bold">审核结果</view>
      <view class="task-result-content">{{ dict['DeliveryStatusEnum'][task.deliveryStatus] || '-' }}</view>
    </view>
    <view>
      <view class="task-desc-title bold">备注</view>
      <view class="task-result-content">{{ task.failReason || '-'}}</view>
    </view>
  </view>
  <!-- btn -->
  <view 
    class="task-btn-wrapper" 
    wx:if="{{ (task.workerJobStatus == 300 && task.deliveryStatus == 100) || task.deliveryStatus == 300 }}">
    <view class="task-submit-btn linear-gradient border-20" bindtap="onSubmit">
      <button  hover-class="none" class="primary-btn" wx:if="{{task.deliveryStatus == 100}}">
        提交审核
      </button>
      <button  hover-class="none" class="primary-btn" wx:if="{{task.deliveryStatus == 300}}">
        重新提交凭证
      </button>
    </view>
</view>

</view>