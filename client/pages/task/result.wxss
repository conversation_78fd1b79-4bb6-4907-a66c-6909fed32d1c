/* pages/task/result.wxss */
page  {
  background: var(--theme-page-back);
}
.res-box {
  margin: 176rpx 0 0;
}
.res-img {
  width: 160rpx;
  margin: 0 auto;
}
.res-title {
  margin: 55rpx 0 35rpx;
  font-size: 34rpx;
  color: var(--theme-black);
}
.res-desc {
  font-size: 24rpx;
  color: var(--theme-tooltip-gray);
}
.res-btn-box {
  --btn-color: #2A5DD1;
  width: 300rpx;
  margin: 120rpx auto;
  padding: 25rpx 0;
  border: 1px solid var(--btn-color);
}
.res-btn {
  color: var(--btn-color);
}

