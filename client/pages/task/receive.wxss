/* pages/task/receive.wxss */
page {
  background: var(--theme-page-back);
}
.res-box {
  margin: 73rpx 0 0;
}
.res-img {
  margin: 0 auto;
  width: 300rpx;
}
.res-title {
  margin: 15rpx 0;
  font-size: 34rpx;
  color: var(--theme-black);
}
.res-desc {
  margin: 30rpx 0 0;
  font-size: 24rpx;
  color: var(--theme-deep-gray);
}
.res-btn-box {
  margin: 120rpx 60rpx;
}
.res-btn {
  margin: 0 10rpx;
  padding: 24rpx 0;
  font-size: 26rpx;
}
.res-detail-btn {
  border: 1px solid var(--theme-black);
  color: var(--theme-black);
}
.res-task-btn {
  color: #fff;
}