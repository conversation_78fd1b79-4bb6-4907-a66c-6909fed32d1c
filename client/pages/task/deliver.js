import { toPromise } from "../../utils/util"
import { submitJob, jobDetail } from '../../api/task'

// pages/task/deliver.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: '',
    task: {},
    form: {},
    type: 1, // 标识新提交(1)/重新提交(2)
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (!options.id) {
      return wx.showToast({
        title: '找不到任务ID',
        icon: 'none'
      })
    }
    this.setData({
      id: options.id,
      type: options.type || 1
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    this.getDetail()
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
  },
  async getDetail() {
    if (!this.data.id) return;
    const { data } = await jobDetail({ id: this.data.id })
    if (data.jobTag) {
      data["tags"] = JSON.parse(data.jobTag)
    }
    this.setData({
      task: data
    })
  },
  async onSubmit() {
    if (this.data.task.deliveryStatus == 300) {
      this.setData({
        'task.deliveryStatus': 100
      })
      return
    }
    if (!this.data.form.deliveryContent 
        || this.data.form.attachment.length == 0
        || this.data.form.deliveryContent.length == 0) {
      wx.showToast({
        title: '请输入交付信息',
        icon: 'error'
      })  
      return;
    }
    wx.showLoading({
      title: '提交中',
    })
    const [_, err] = await toPromise(submitJob({
      jobId: this.data.task.jobId,
      ...this.data.form,
    }))
    wx.hideLoading();
    if (err) return;
    wx.navigateTo({
      url: '/pages/task/result',
    })
  },
  onChangeField(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`form.${field}`]: e.detail.value.map(item => item.url)
    })
  }
})