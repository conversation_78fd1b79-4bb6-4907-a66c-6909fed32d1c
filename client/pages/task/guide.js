const { buildParams } = require("../../utils/util")

// pages/task/guide.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: '',
    show: true,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      id: options.id,
      show: options.show === undefined ? true : false
    })
  },
  confirm() {
    wx.redirectTo({
      url: '/pages/task/detail' + buildParams({confirm: true, id: this.data.id}),
    })
  }
})