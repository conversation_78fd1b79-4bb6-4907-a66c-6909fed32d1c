/* pages/task/detail.wxss */
page {
  background: var(--theme-page-back);
}
.task-price {
  margin-top: 10rpx;
  padding-bottom: 22rpx;
}
hj-icon {
  line-height: 0;
}
.info-header {
  align-items: baseline;
}
.task-info-title {
  margin-left: 20rpx;
  font-size: 26rpx;
}
.task-info-content {
  margin-left: 45rpx;
  font-size: 26rpx;
}

/* btn */
.task-btn-box {
  margin: 30rpx 0;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}
.task-btn {
  width: 100% !important;
  color: #fff;
}

/* half */
.user-status-item {
  padding: 20rpx 0;
}
.user-status-item:not(:first-child) {
  border-top: 1px solid var(--theme-gray);
}
.user-status {
  color: var(--theme-tooltip-gray);
}

.user-status-btn {
  margin-left: 20rpx;
  padding: 3rpx 20rpx;
  border: 1px solid var(--theme-green);
  border-radius: 20rpx;
  color: var(--theme-green);
}
.user-confirm-btn {
  width: 500rpx;
  margin: 61rpx auto;
  padding: 24rpx 0;
  color: #fff;
  font-size: 28rpx;
}
.user-confirm-btn.disabled {
  background: var(--theme-deep-gray);
}
mp-icon {
  margin-right: 10rpx;
  line-height: 0;
}


/* employer */
.task-employer-card {
  padding: 20rpx 15rpx;
}
.employer-img {
  width: 90rpx;
}
.employer-detail {
  font-size: 24rpx;
  color: var(--theme-tooltip-gray);
}
.employer-name {
  font-size: 28rpx;
}
.employer-info {
  margin-left: 20rpx;
}

/* dialog */
.dialog-btn {
  margin: 0 20rpx;
  padding: 20rpx 0;
  border-radius: 20rpx;
}
.dialog-btn-cancel {
  border: 1px solid currentColor;
  color: #333;
}
.dialog-btn-primary {
  background: #FFAE38;
  color: #fff;
}