<!--pages/task/detail.wxml-->
<wxs src="/wxs/money.wxs" module="money"></wxs>
<navigation-bar title="任务详情" showback="{{showBack}}"></navigation-bar>
<view class="task-info-page">
  <view class="task-head-box border-10 shadow">
    <view class="task-header flex-box">
      <view class="flex-item task-title">{{ task.jobName }}</view>
      <view class="task-people-num">招收 {{ task.workerNum }} 人</view>
    </view>
    <view class="task-price dashed" wx:if="{{task.rewardType==200}}">面议</view>
    <view class="task-price dashed" wx:else>{{ money.format(task.rewardAmount) }}元/{{ dict['PayTypeEnum'][task.payType] }}</view>
    <view class="task-label">
      <text class="task-label-text" wx:for="{{ task.tags }}" wx:key="index">{{ item }}</text>
    </view>
  </view>

  <view class="task-detail-card border-10 shadow">
    <view class="task-detail-box dashed">
      <view class="task-info-item">
        <view class="flex-box info-header">
          <hj-icon icon="date" size="27" height="28"></hj-icon>
          <view class="task-info-title">工作日期</view>
        </view>
        <view class="task-info-content">
          <view wx:if="{{task.jobAvalDateType==100}}">{{ dict['JobAvalDateTypeEnum'][task.jobAvalDateType] }}</view>
          <view wx:if="{{task.jobAvalDateType==200}}">{{ task.jobStartDate }} 至 {{ task.jobFinishDate }}</view>
        </view>
      </view>
      <view class="task-info-item">
        <view class="flex-box info-header">
          <hj-icon icon="time" size="27"></hj-icon>
          <view class="task-info-title">工作时段</view>
        </view>
        <view class="task-info-content">
          <view>{{ task.jobTimeStart }} ~ {{ task.jobTimeEnd }}</view>
        </view>
      </view>
      <view class="task-info-item">
        <view class="flex-box info-header">
          <hj-icon icon="location" size="23" height="30"></hj-icon>
          <view class="task-info-title">工作地点</view>
        </view>
        <view class="task-info-content">
          <view>{{ task.jobProvinceName }} {{ task.jobCityName }} {{ task.jobAreaName }} {{ task.jobAddress }}</view>
        </view>
      </view>
    </view>
    <view class="task-detail-box dashed">
      <view class="task-info-item">
        <view class="flex-box info-header">
          <hj-icon icon="graduate" size="32" height="26"></hj-icon>
          <view class="task-info-title">人员要求</view>
        </view>
        <view class="task-info-content">
          <view>{{task.requirementText }}</view>
        </view>
      </view>
      <view class="task-info-item">
        <view class="flex-box info-header">
          <hj-icon icon="penruler" size="22" height="29"></hj-icon>
          <view class="task-info-title">技能要求</view>
        </view>
        <view class="task-info-content">
          <view>
            {{ task.professionalSkill || '-' }}
          </view>
        </view>
      </view>
    </view>

    <view class="task-detail-box">
      <view class="task-desc-box">
        <view class="task-desc-title bold">任务描述</view>
        <view class="task-detail-content">{{ task.jobDescribe || '-' }}</view>
      </view>
      <view class="task-desc-box">
        <view class="task-desc-title bold">交付标准</view>
        <view class="task-detail-content">{{ task.deliveryStandard || '-' }}</view>
      </view>
    </view>
  </view>

  <view class="task-detail-card border-10 shadow task-employer-card">
    <view class="flex-box">
      <view class="employer-img flex-box">
        <image src="../../assets/image/employer.png" alt="商户" mode="widthFix" class="flex-item"></image>
      </view>
      <view class="employer-info flex-item">
        <view class="employer-name">{{ task.employerName }}</view>
        <view class="employer-detail">{{ task.jobProvinceName }} | {{ task.industryName }}</view>
      </view>
    </view>
  </view>
  <!-- btn -->
  <view wx:if="{{isLoad&&!(task.workerJobStatus&&task.workerJobStatus>0)}}" class="task-btn-wrapper">
    <view class="task-btn-box linear-gradient border-20">
      <button  hover-class="none" class="task-btn" bindtap="openHalf">接受任务</button>
    </view>
  </view>
</view>


<mp-half-screen-dialog show="{{show}}" title="接单前请确认完成以下内容" closabled="{{false}}">
  <view class="user-status-box" slot="desc">
    <view class="user-status-item flex-box">
      <view class="flex-item">实名认证</view>
      <view class="user-status">
        {{ userInfo.verify ? '已认证' : '未认证'}}
      </view>
    </view>
    <view class="user-status-item flex-box">
      <view class="flex-item">电子签约</view>
      <view class="user-status">
        {{ toSignList[userInfo.toSign] }}
      </view>
    </view>
    <view class="user-status-item flex-box">
      <view class="flex-item flex-box">
        <mp-icon icon="star" type="field" size="{{18}}"></mp-icon>
        <view>税务认证</view>
      </view>
      <view class="user-status flex-box">
        <text class="func" bindtap="goToGuide">查看操作指引</text>
        <view class="user-status-btn" bindtap="goToGuide">去认证</view>
      </view>
    </view>

    <view class="user-confirm-btn text-center border-20 linear-gradient {{!halfConfirm ? 'disabled' : ''}}" bindtap="onClickHalf">
      我已确认完成以上步骤
    </view>
  </view>
</mp-half-screen-dialog>


<dialog id="dialog" title="接受任务须知" width="75%">
  <view slot="content">
    请确认您理解并认可您所承接的任务与页面所示相符，您在实际完成任务时不得违反法律法规及社会公序良俗（包括但不限于涉嫌洗钱、逃税、漏税、传销、赌博、暴力、淫秽、恐怖、非法集资等行为）
  </view>
  <view slot="footer" class="flex-box">
    <view class="flex-item dialog-btn dialog-btn-cancel" bindtap="closeDialog">取消</view>
    <view class="flex-item dialog-btn dialog-btn-primary" bindtap="acceptTask">我接受</view>
  </view>
</dialog>