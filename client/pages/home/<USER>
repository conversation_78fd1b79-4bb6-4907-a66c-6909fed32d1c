import { createStoreBindings } from "mobx-miniprogram-bindings";
import { store } from '../../store/index'
import { buildParams, debounce, toPromise } from "../../utils/util"
import { getUserInfo } from "../../api/user";
import { taskList } from '../../api/task'
import { getToken } from "../../utils/token"
// pages/home/<USER>
Page({

  /**
   * 页面的初始数据
   */
  data: {
    toSignList: ['无记录', '已签约', '未签约'],
    btnList: ['身份核验', '业务分包', '收入核算', '税款代缴', '保险保障', '账户管理'],
    activeTab: 100,
    tabList: [{
      value: 100,
      label: '指派给我',
    }, {
      value: 200,
      label: '任务广场',
    }],

    form: {},
    list: [],
    page: 1,
    end: true,
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.storeBindings = createStoreBindings(this, {
      store,
      fields: ["userInfo"],
      actions: ["setUserInfo"]
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getUserInfo()
    this.debGetList = debounce(this.getList)
    this.getList(true)
  },
  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    this.storeBindings.destroyStoreBindings();
  },
  async getUserInfo() {
    if (!getToken()) return
    const [{ data }] = await toPromise(getUserInfo())
    this.setUserInfo(data)
  },
  checkLoginFirst (redirectUrl) {
    if (!getToken() || !this.data.userInfo || !this.data.userInfo.phone) {
      wx.setStorage({
        key: 'login_redirect',
        data: redirectUrl || '/pages/home/<USER>'
      }).then(() => {
        wx.redirectTo({
          url: '/pages/index/index',
        })
      })
      return false
    }
    return true
  },
  onTabVerify() { 
    if (!this.checkLoginFirst('/pages/auth/index')) return
    wx.navigateTo({
      url: '/pages/auth/index',
    })
  },
  onTabSign() {
    if (!this.checkLoginFirst('/pages/user/sign')) return
    let self = this;
    if (!this.data.userInfo.verify) {
      return wx.showModal({
        content: '您尚未完成实名认证，请先进行实名认证',
        cancelText: '取消',
        confirmText: '去认证',
        confirmColor: '#1890FF',
        success(res) {
          if (res.confirm) {
            self.onTabVerify()
          }
        }
      })
    }
    wx.navigateTo({
      url: '/pages/user/sign',
    })
  },
  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.loading || this.data.end) return;
    this.debGetList()
  },
  changeTab(e) {
    let { value } = e.currentTarget.dataset;
    this.setData({
      activeTab: value,
      end: false,
      loading: false,
    })
    this.getList(true)
  },
  goDetail(e) {
    const { id } = e.currentTarget.dataset;
    wx.navigateTo({
      url: '/pages/task/detail' + buildParams({ id }),
    })
  },
  onInput(e) {
    this.setData({
      'form.keyword': e.detail.value
    })
  },
  onConfirm(e) {
    this.getList(true)
  },
  async getList(init) {
    if (!getToken()) {
      return this.setData({
        end: true
      })
    }
    if (init === true) {
      this.setData({
        page: 1,
        end: false,
        list: [],
      })
    }
    this.loading = true;
    let params={}
    if(this.data.activeTab==100){
      params.isAssign=100
    }else if(this.data.activeTab==200){
      params.scope=200
    }
    const { data: { rows, totalRecord } } = await taskList({
      ...this.data.form,
      ...params,
      pageCurrent: this.data.page,
      pageSize: 10,
    })
    let list = this.data.list.concat(rows)
    list.forEach(e=>{
      if(e.jobTag){
        e["tags"]=JSON.parse(e.jobTag)
      }
    })

    this.setData({
      list,
      end: totalRecord <= list.length,
      page: this.data.page + 1,
    })
    this.loading = false;
  },
  goToAuth() {
    this.goTo({
      url: '/pages/task/guide' + buildParams({ show: false }),
    })
  }
})