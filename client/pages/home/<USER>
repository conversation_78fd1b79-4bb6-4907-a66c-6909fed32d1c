<!--pages/home/<USER>
<wxs src="/wxs/money.wxs" module="money"></wxs>
<navigation-bar></navigation-bar>
<view class="page-container">
  <!-- <view class="home-banner-box">
    <image src="../../assets/image/banner.png" alt="banner" mode="widthFix" bindtap="goToAuth"></image>
  </view> -->
  <view class="home-banner-box">
    <image src="../../assets/image/home-top-banner.png" alt="banner" mode="widthFix"></image>
  </view>
  <view class="user-status-box flex-box shadow">
    <view
      class="user-status-item flex-item text-center"
      bindtap="onTabVerify"
    >
      <view class="user-status-icon">
        <hj-icon
          icon="user-name"
          size="53"
          height="57"
        ></hj-icon>
      </view>
      <view class="user-status">
        实名认证({{ userInfo.verify ? '已认证' : '未认证'}})
      </view>
    </view>
    <view
      class="user-status-item flex-item text-center"
      bindtap="onTabSign"
    >
      <view class="user-status-icon">
        <hj-icon
          icon="user-sign"
          size="48"
          height="57"
        ></hj-icon>
      </view>
      <view class="user-status">
        电子签约({{ toSignList[userInfo.toSign] }})
      </view>
    </view>
  </view>

  <view class="home-notice-box">
    <view class="home-notice-title">关于智享汇</view>
    <view class="home-notice-content">智享汇致力于为企业提供互联网支付、劳动力相关的整体科技解决方案，帮助企业一站式解决在市场营销、流量管理、资金收付、账户管理、财税合规等方面问题，助力企业全面提升流量管理、中后台管理、财务管理、资金管理效率，为企业提供持续的增长价值。</view>
    <view class="home-label-content">
      <view class="home-label-item" wx:for="{{btnList}}" wx:key="idx">
        <view class="home-label-icon">
          <hj-icon
            icon="{{'user-icon' + index}}"
            size="106"
            height="106"
          ></hj-icon>
        </view>
        <view class="home-label">
          {{ item }}
        </view>
      </view>
    </view>
  </view>

  <view class="home-input-box shadow">
    <view class="home-input-placeholder" wx:if="{{!form.keyword}}">
      <hj-icon size="28" icon="search" class="home-input-icon"></hj-icon>
      <text>请输入关键字搜索</text>
    </view>
    <input type="text" bindinput="onInput" bindconfirm="onConfirm" />
  </view>

  <view class="home-tab-box">
    <view class="home-tab-list flex-box">
      <view class="{{'home-tab-item ' + (item.value == activeTab ? 'active' : '')}}" wx:for="{{tabList}}" wx:key="index" data-value="{{item.value}}" bindtap="changeTab">
        {{ item.label }}
      </view>
    </view>
  </view>

  <view class="home-list-box">
    <view class="home-list-item border-10" bindtap="goDetail" data-id="{{item.id}}" wx:for="{{list}}" wx:key="index">
      <view class="task-header-container dashed">
        <view class="task-header flex-box">
          <view class="task-header-title flex-item">{{ item.jobName }}</view>
          <view class="task-price" wx:if="{{item.rewardType==200}}">面议</view>
          <view class="task-price" wx:else>{{ money.format(item.rewardAmount) }}元/{{ dict['PayTypeEnum'][item.payType] }}</view>
        </view>
        <view class="task-info">
          {{ item.jobCityName}} {{ item.jobAreaName }}
        </view>
      </view>
      <view class="task-label">
        <text class="task-label-text" wx:for="{{ item.tags }}" wx:key="index">{{ item }}</text>
      </view>
    </view>
    <view class="task-footer text-center" wx:if="{{end}}">
      {{ end ? '没有更多了' : ''}}
    </view>
  </view>
</view>