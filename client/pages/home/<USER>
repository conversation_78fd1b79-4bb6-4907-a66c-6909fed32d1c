/* pages/home/<USER>/
/* input */
page {
  background: var(--theme-page-back);
}

.page-container {
  margin: 22rpx 30rpx 20rpx;
}

/* statusbox */
.user-status-box {
  position: relative;
  margin: 15rpx 20rpx 0;
  padding: 20rpx 0 15rpx;
  justify-content: center;
  background: #fff;
  border-radius: 70rpx;
}
.user-status-box::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  width: 2rpx;
  height: 44rpx;
  background: #D8D8D8;
}
.user-status {
  margin: 10rpx 0 0;
  color: #6C6C6C;
  font-size: 24rpx;
}

/* notice */
.home-notice-box {
  width: 690rpx;
  margin: 30rpx auto;
  border-radius: 14rpx;
  background: #FFFFFF;
  box-shadow: 0rpx 4rpx 32rpx 0rpx rgba(240,183,210,0.12);
}
.home-notice-box .home-notice-title {
  padding: 25rpx 20rpx 18rpx;
  font-size: 30rpx;
  color: #333333;
  font-weight: 500;
  border-bottom: 1rpx solid #DDDDDD;
}
.home-notice-box .home-notice-content {
  padding: 32rpx 23rpx;
  font-size: 26rpx;
  color: #333333;
  line-height: 1.6;
  letter-spacing: 1.6px;
}
.home-notice-box .home-label-content {
  padding: 20rpx 50rpx 30rpx;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.home-label-content .home-label-item {
  flex-shrink: 0;
  width: 150rpx;
  height: 180rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.home-label-item .home-label-icon {
  width: 106rpx;
  height: 106rpx;
}
.home-label-item .home-label {
  width: 100%;
  text-align: center;
  height: 23rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
}

.home-input-box {
  position: relative;
  margin-top: 20rpx;
  padding: 10rpx 50rpx;
  font-size: 26rpx;
  background: #fff;
  border-radius: 24rpx;
}

.home-input-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  font-size: 24rpx;
  color: #858484;
}

.home-input-icon {
  margin-right: 14rpx;
}

/* tab */
.home-tab-box {
  margin: 45rpx 17rpx 0;
}

.home-tab-item {
  position: relative;
  margin-right: 60rpx;
  font-size: 28rpx;
  color: var(--theme-black);
}

.home-tab-item.active {
  font-size: 32rpx;
  font-weight: 500;
}

.home-tab-item.active::after {
  content: '';
  display: block;
  position: absolute;
  bottom: 6rpx;
  left: 0;
  right: 0;
  height: 7rpx;
  background: linear-gradient(-45deg, #FFBE40 0%, #F6D016 99%);
  border-radius: 3px;
  opacity: 0.8;
  z-index: -1;
}

/* list */
.home-list-box {
  margin: 20rpx 0;
  overflow: hidden;
}

.home-list-item {
  margin: 0 0 20rpx;
  padding: 28rpx 33rpx;
  background: #fff;
}

/* banner */
.home-banner-box image {
  width: 100%;
}