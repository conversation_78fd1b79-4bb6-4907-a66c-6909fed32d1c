/* pages/user/sign.wxss */
page {
  background: var(--theme-page-back);
}
.sign-tab-item {
  color: var(--theme-black);
  font-size: 28rpx;
  background: #fff;
}
.sign-tab-inner {
  padding: 20rpx 0;
  display: inline-block;
  margin: 0 auto; 
  border-bottom: 1px solid transparent;
}
.sign-tab-item.active .sign-tab-inner {
  border-bottom: 2px solid var(--primary);
  color: var(--primary);
}
.sign-list {
  padding: 0 20rpx;
}
.sign-list-item {
  margin: 20rpx 0;
  padding: 20rpx 25rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  background: #fff;
}
.sign-status-sign {
  color: #4E91FF;
}
.sign-status-tosign {
  color: #FA2B02;
}
.sign-list-info {
  padding: 15rpx 0;
  border-bottom: 1px solid #ddd;
  font-size: 22rpx;
  color: #666;
}
/* btn */ 
.sign-list-btn {
  margin-top: 12rpx;
}
.sign-btn-item {
  display: inline-block;
  width: 120rpx;
  height: 50rpx;
  margin-left: 22rpx;
  line-height: 50rpx;
  border: 1px solid #D3D3D3;
  font-size: 24rpx;
  border-radius: 30rpx;
  text-align: center;
}
.sign-btn {
  border: none;
  color: #fff;
}
/* empty */
.sign-empty {
  margin: 0 30rpx;
}
.sign-empty-text {
  margin: 100rpx 0;
  color: var(--theme-deep-gray);
  font-size: 30rpx;
}
.sign-empty-btn {
  padding: 20rpx 0;
  border-radius: 40rpx;
  font-size: 24rpx;
  color: #fff;
}