// pages/user/sign.js
import { toSignList, signList, listSign } from "../../api/user";
import { fileUrl } from "../../setting/index";
import {  toPromise } from "../../utils/util";
Page({

  /**
   * 页面的初始数据
   */
  data: {
    active: -1,
    list: [],
    end: false,

    pageInfo: {
      pageCurrent: 1,
      pageSize: 20,
    },
    signMap: {
      400: "未签约",
      300: "已创建",
      200: "签约失败",
      100: "已签约",
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    this.loadMore(true)
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.end || this.loading) return;
    this.loadMore()
  },

  async loadMore(init) {
    if (init) {
      this.setData({
        'pageInfo.pageCurrent': 1,
        list: [],
        end: false,
      })
    }
    this.loading = true;
    let api = this.data.active == -1 ? listSign : this.data.active == 0 ? toSignList : signList
    const [{ data: { data, totalRecord: total } }] = await toPromise(api(this.data.pageInfo))
    if (init) {
      this.setData({
        list: data,
      })
    } else {
      this.setData({
        list: this.data.list.concat(list)
      })
    }
    this.setData({
      end: this.data.list.length >= total
    })
    this.loading = false
  },
  changeTab(e) {
    let { field = '' } = e.currentTarget.dataset
    this.setData({
      active: Number(field),
    })
    this.loadMore(true)
  },
  onSignTab(e) {
    let { index } = e.currentTarget.dataset
    let { signUrl } = this.data.list[index]
    if (signUrl) {
      wx.setStorage({
        key: 'url',
        data: signUrl
      })
      wx.navigateTo({
        url: '/pages/common/web',
      })
    }
  },
  async downloadSignFile(e) {
    const { index } = e.currentTarget.dataset
    const item = this.data.list[index]
    const filePath = `${wx.env.USER_DATA_PATH}/sign_${item.employerNo}_${item.id}.pdf`
    // 判断是否有文件
    const res = await this.checkFileAccess(filePath)
    if (res) {
      this.openFile(filePath)
      return
    }
    wx.showLoading({
      title: '下载中',
    })
    const vm = this
    wx.downloadFile({
      url: fileUrl+item.fileUrl,
      filePath,
      success(res){
        vm.openFile(res.filePath)
      },
      complete() {
        wx.hideLoading()
      }
    })
  },
  checkFileAccess(filepath) {
    return new Promise((resolve, reject) => {
      const fs = wx.getFileSystemManager()
      fs.access({
        path: filepath,
        success() {
          resolve(true)
        },
        fail() {
          resolve(false)
        }
      })
    })
  },
  openFile(filePath) {
    wx.openDocument({
      filePath,
      fileType: 'pdf'
    })
  },
  backToUser() {
    wx.switchTab({
      url: '/pages/user/info'
    })
  }
})