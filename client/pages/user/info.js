// pages/user/info.js
import { createStoreBindings } from "mobx-miniprogram-bindings";
import { store } from '../../store/index'
import { codeLogin } from "../../api/login";
import { getUserInfo } from "../../api/user";
import { toPromise } from "../../utils/util";
import { getToken } from "../../utils/token";
Page({

  /**
   * 页面的初始数据
   */
  data: {
    canUseOpen: true,
    toSignList: ['无记录', '已签约', '未签约'],
    operateList: [
      { name: '我的收入', url: '/pages/user/income/index' },
      { name: '关于智享汇', url: '/pages/user/about' },
      
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      canUseOpen: wx.canIUse('open-data')
    })
    this.storeBindings = createStoreBindings(this, {
      store,
      fields: ["userInfo"],
      actions: ["setUserInfo"]
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
  },
  
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.getUserInfo()
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    this.storeBindings.destroyStoreBindings();
  },

  onTabVerify() {
    wx.navigateTo({
      url: '/pages/auth/index',
    })
  },
  onTabSign() {
    let self = this;
    if (!this.data.userInfo.verify) {
      return wx.showModal({
        content: '您尚未完成实名认证，请先进行实名认证',
        cancelText: '取消',
        confirmText: '去认证',
        confirmColor: '#1890FF',
        success(res) {
          if (res.confirm) {
            self.onTabVerify()
          }
        }
      })
    }
    wx.navigateTo({
      url: '/pages/user/sign',
    })
  },
  async codeLogin() {
    wx.login({
      timeout: 3000,
      async success(res) {
        if (res.code) {
          await codeLogin({ code: res.code })
        }
      }
    })
  },
  async getUserInfo() {
    if (!getToken()) return
    const [{ data }] = await toPromise(getUserInfo())
    this.setUserInfo(data)
  },
  goToProfile() {
    wx.navigateTo({
      url: '/pages/user/profile',
    })
  }
})