<!--pages/user/income/detail.wxml-->
<wxs
  src="/wxs/money.wxs"
  module="money"
></wxs>

<view class="income-detail-box">
  <view class="income-detail-head text-center">
    <view class="income-detail-avatar"></view>
    <view class="income-detail-title">啊的肌肤i啊节点佛埃及佛啊大家佛</view>
    <view class="income-detail-num">+ {{money.format(info.orderNetAmount)}}</view>
  </view>
  <view class="income-detail-form">
    <view class="income-detail-item flex-box">
      <view class="income-detail-label">当前状态</view>
      <view class="income-detail-value flex-item">
        {{ dict['RecordItemStatusEnum'][info.processStatus]}}
      </view>
    </view>
    <view class="income-detail-item flex-box">
      <view class="income-detail-label">发放方式</view>
      <view class="income-detail-value flex-item">
        {{ dict['ChannelTypeEnum'][info.channelType]}}
      </view>
    </view>
    <view class="income-detail-item flex-box">
      <view class="income-detail-label">收款账户</view>
      <view class="income-detail-value flex-item">{{ info.receiveAccountNo }}</view>
    </view>
    <view class="income-detail-item flex-box">
      <view class="income-detail-label">打款流水号</view>
      <view class="income-detail-value flex-item">{{ info.remitPlatTrxNo }}</view>
    </view>
    <view class="income-detail-item flex-box">
      <view class="income-detail-label">备注</view>
      <view class="income-detail-value flex-item">{{ info.remark }}</view>
    </view>
    <view class="income-detail-item flex-box">
      <view class="income-detail-label">转账时间</view>
      <view class="income-detail-value flex-item">{{ info.createTime }}</view>
    </view>
    <view class="income-detail-item flex-box">
      <view class="income-detail-label">收款时间</view>
      <view class="income-detail-value flex-item">{{ info.completeTime }}</view>
    </view>
  </view>
</view>

<view
  class="income-apply-box flex-box"
  bindtap="applyInvoice"
>
  <view class="income-apply-icon">
    <image
      src="/assets/icon/apply.png"
      mode="widthFix"
    />
  </view>
  <view class="income-apply-title">申请电子回单</view>
</view>