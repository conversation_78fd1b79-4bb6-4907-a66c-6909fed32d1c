/* pages/user/income/apply.wxss */
.income-apply-box {
  margin: 170rpx 30rpx;
}

.income-apply-title {
  color: #333333;
  font-size: 40rpx;
}

.income-apply-tip {
  margin: 44rpx 70rpx 85rpx;
  font-size: 26rpx;
  color: #999999;
}

.income-apply-input {
  margin-bottom: 35rpx;
}

.income-input-item {
  padding-left: 37rpx;
  border-radius: 8rpx;
  border: 2rpx solid #FF7D34;
  line-height: 86rpx;
  height: 86rpx;
}

.income-apply-btn {
  font-size: 34rpx;
  text-align: center;
  color: #fff;
  line-height: 86rpx;
  height: 86rpx;
  background: #FF7D34;
  border-radius: 8rpx;
}

/* result */
.income-apply-result {
  margin: 200rpx 30rpx;
}

.income-apply-result-img {
  margin: 0 auto 50rpx;
  width: 207rpx;
  height: 112rpx;
}

.income-result-btn {
  position: fixed;
  bottom: 140rpx;
  left: 30rpx;
  right: 30rpx;
}


/* dialog */
.dialog-back {
  position: relative;
  z-index: 100
}

.dialog-content {
  position: absolute;
  top: 80rpx;
  left: 0;
  right: 0;
  padding: 0 80rpx;
}

.dialog-content-title {
  margin-bottom: 40rpx;
  color: #333333;
  font-size: 36rpx;
}

.dialog-content-tip {
  color: #666666;
  font-size: 28rpx;
}
.dialog-content-email {
  color: #333;
  font-weight: bold;
}

.dialog-content-btn {
  justify-content: space-between;
  margin-top: 30rpx;
}
.dialog-cancel-btn {
  color: #999999;
  border: 2rpx solid #999999;
}

.dialog-confirm-btn {
  color: #FFFFFF;
  background: #FF7D34;
}

.dialog-btn {
  box-sizing: border-box;
  width: 210rpx;
  height: 74rpx;
  line-height: 74rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
}