/* pages/user/income/detail.wxss */
page {
  background: var(--theme-page-back);
}

.income-detail-box {
  padding: 60rpx 60rpx 0;
  color: #333333;
  background: #fff;
}

.income-detail-avatar {
  width: 90rpx;
  height: 90rpx;
}

.income-detail-title {
  margin-top: 30rpx;
  font-size: 32rpx;
  font-weight: 400;
}

.income-detail-num {
  margin-top: 30rpx;
  font-size: 54rpx;
  font-weight: 500;
  padding-bottom: 100rpx;
  border-bottom: 1px solid #E0E0E0;
}
/* form */
.income-detail-form {
  padding: 50rpx 0 60rpx;
}
.income-detail-label {
  width: 160rpx;
  color: #848484;
  font-size: 26rpx;
}
.income-detail-value {
color: #333333;
font-size: 26rpx;
}
.income-detail-item {
  margin-bottom: 20rpx;
}
.income-detail-item:last-child {
  margin-bottom: 0;
}
/* apply */
.income-apply-box {
  margin-top: 20rpx;
  padding: 30rpx 60rpx;
  background: #fff;
  color: #496390;
}
.income-apply-icon {
  width: 28rpx;
  height: 30rpx;
  margin-right: 15rpx;
}