<!--pages/user/income/index.wxml-->
<wxs
  src="/wxs/money.wxs"
  module="money"
></wxs>

<view class="income-head-box">
  <view class="flex-box income-head-wrapper">
    <view class="income-box income-total-box flex-item">
      <view class="income-head-title">累计收入</view>
      <view class="income-head-tip">Cumulative revenue</view>
      <view class="income-head-num">¥ {{totalIncome}}</view>
      <view class="income-head-back">
        <image
          src="/assets/image/income-back-left.png"
          mode="widthFix"
        ></image>
      </view>
    </view>
    <view class="income-box income-month-box flex-item">
      <view class="income-head-title">近一月</view>
      <view class="income-head-tip">Nearly a month</view>
      <view class="income-head-num">¥ {{monthIncome}}</view>
      <view class="income-head-back">
        <image
          src="/assets/image/income-back-right.png"
          mode="widthFix"
        ></image>
      </view>
    </view>
  </view>
</view>

<view class="income-list-box">
  <view
    wx:for="{{list}}"
    wx:for-item="item"
    wx:key="index"
  >
    <view class="income-list-head flex-box">
      <view class="income-list-time flex-item flex-box">
        {{item.year}}年{{item.month}}月
        <image
          data-index="{{index}}"
          class="income-list-arrow"
          src="/assets/icon/arrow.png"
          alt="arrow"
          mode="widthFix"
          bindtap="openCloseList"
        />
      </view>
      <view class="income-list-total">合计{{money.format(item.totalAmount)}}</view>
    </view>
    <view
      class="income-list-content"
      style="height: {{ openClose[index] ? 'auto' : '0'}}"
    >
      <view
        class="income-list-item flex-box"
        bindtap="goItem"
        wx:for="{{item.detail}}"
        wx:for-item="detail"
        wx:for-index="idx"
        wx:key="idx"
        data-no="{{detail.remitPlatTrxNo}}"
      >
        <view class="income-list-avatar">
          <image
            src="/assets/image/channel-{{detail.channelType}}.png"
            mode="widthFix"
          />
        </view>
        <view class="income-list-detail flex-item">
          <view class="income-list-title flex-box">
            <view class="income-list-name text-hidden">{{ detail.employerName }}</view>
            <view class="income-list-num flex-item text-right">
              + {{ money.format(detail.orderNetAmount)}}
            </view>
          </view>
          <view class="income-list-time">{{ detail.completeTime }}</view>
        </view>
      </view>
    </view>
  </view>
</view>

<view class="income-end-tip text-center">仅展示近6个月收款记录</view>