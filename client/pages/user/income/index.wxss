/* pages/user/income/index.wxss */
.income-head-wrapper {
  justify-content: center;
  margin: 30rpx 22.5rpx 0;
}

.income-box {
  position: relative;
  color: #fff;
  margin: 0 7.5rpx;
  padding: 30rpx;
  height: 195rpx;
}

.income-head-back {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -10;
}

.income-head-title {
  font-size: 28rpx;
  font-weight: 500;
}

.income-head-tip {
  font-size: 16rpx;
}

.income-head-num {
  margin-top: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
}

/* list */
.income-list-head {
  padding: 40rpx 30rpx;
  background: #F4F4F4;
}

.income-list-arrow {
  width: 22rpx;
  height: 13rpx;
  padding: 15rpx;
}

.income-list-time {
  font-size: 32rpx;
  color: #333333;
}

.income-list-total {
  font-size: 26rpx;
  color: #A4A4A4;
}

/* item */
.income-list-item {
  padding: 30rpx 0 0 30rpx;
}

.income-list-avatar {
  width: 90rpx;
  height: 90rpx;
  margin-right: 30rpx;
}

.income-list-detail {
  padding-bottom: 30rpx;
  border-bottom: 1px solid #E0E0E0;
}

.income-list-name {
  width: 395rpx;
  font-size: 28rpx;
  color: #333333;
}

.income-list-num {
  margin-right: 30rpx;
  font-size: 30rpx;
  color: #70D06F;
}

.income-list-time {
  margin-top: 10rpx;
  color: #999999;
  font-size: 24rpx;
}

/* end */
.income-end-tip {
  margin-top: 20rpx;
  font-size: 22rpx;
  color: #999999;
}