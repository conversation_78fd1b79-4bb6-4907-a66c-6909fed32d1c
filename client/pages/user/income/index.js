import { buildParams } from "../../../utils/util"
import {getIncome} from '../../../api/income'
// pages/user/income/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: [],
    totalIncome: 0,
    monthIncome: 0,
    openClose: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getList()
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.getList()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  async getList() {
    const {data: {totalAmount, recently, list}} = await getIncome()
    this.setData({
      list,
      totalIncome: totalAmount,
      monthIncome: recently,
      openClose: new Array(list.length).fill(true)
    })
  },
  openCloseList(e) {
    const {index} = e.currentTarget.dataset
    this.setData({
      [`openClose.${index}`]: !this.data.openClose[index]
    })
  },
  goItem(e) {
    let {no} = e.currentTarget.dataset
    this.goTo('/pages/user/income/detail' + buildParams({remitPlatTrxNo: no}))
  }
})