// pages/user/income/apply.js
import { getReceipt } from '../../../api/income'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    remitPlatTrxNo: '',
    form: {
      email: ""
    },
    showPop: false,
    showResult: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      remitPlatTrxNo: options.remitPlatTrxNo
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  handleInput(e) {
    const { field } = e.currentTarget.dataset
    this.setData({
      [`form.${field}`]: e.detail.value
    })
  },
  handleSubmit() {
    if (!this.data.form.email) return
    this.open()
  },
  async submit() {
    wx.showLoading({
      title: '正在提交',
    })
    await getReceipt({
      email: this.data.form.email,
      remitPlatTrxNo: this.data.remitPlatTrxNo,
    })
    this.close()
    this.setData({
      showResult: true
    })
    wx.hideLoading()
  },
  open() {
    this.dialog = this.selectComponent('#dialog')
    this.dialog.toggle(true)
    this.setData({
      showPop: true
    })
  },
  close() {
    this.dialog.toggle(false)
    wx.nextTick(() => {
      this.dialog = null
      this.setData({
        showPop: false
      })
    })
  },
  handleFinish() {
    wx.switchTab({
      url: '/pages/user/info',
    })
  },
})