// pages/user/income/detail.js
import { getRecordItem} from '../../../api/income'
import { buildParams } from '../../../utils/util'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    remitPlatTrxNo: '',
    info: {},
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
     this.setData({
      remitPlatTrxNo: options.remitPlatTrxNo
     })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getItem()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.getItem()
  },

  async getItem() {
    const {data} = getRecordItem(this.data.remitPlatTrxNo)
    this.setData({
      info: data
    })
  },
  applyInvoice() {
    this.goTo('/pages/user/income/apply' + buildParams({remitPlatTrxNo: this.data.remitPlatTrxNo}))
  },
})