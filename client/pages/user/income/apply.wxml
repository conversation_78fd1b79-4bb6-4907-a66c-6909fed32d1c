<!--pages/user/income/apply.wxml-->
<view class="{{'income-apply-box' +  showResult ? 'income-apply-result' : ''}}">
  <view
    class="income-apply-result-img"
    wx:if="{{showResult}}"
  >
    <image
      src="/assets/image/apply-result.png"
      mode="widthFix"
    />
  </view>
  <view class="income-apply-title text-center">{{ showResult ? '邮件发送申请已提交' : '填写电子邮箱'}}</view>
  <view class="income-apply-tip text-center">
    {{showResult ? '我们将发送到您指定的邮箱，请注意查收' :'我们会给您的邮箱发送电子回单文件，请在收到邮件后自行下载'}}
  </view>
  <view
    class="income-apply-form"
    wx:if="{{!showResult}}"
  >
    <view class="income-apply-input">
      <input
        wx:if="{{!showPop}}"
        data-field="email"
        value="{{form.email}}"
        bindinput="handleInput"
        type="text"
        class="income-input-item"
        placeholder="输入电子邮箱地址"
      />
      <view wx:else class="income-input-item">{{ form.email }}</view>
    </view>
    <view class="income-apply-input">
      <view
        class="income-apply-btn"
        bindtap="handleSubmit"
      >发送</view>
    </view>
  </view>


</view>

<view
  class="income-apply-btn income-result-btn"
  wx:if="{{showResult}}"
  bindtap="handleFinish"
>完成</view>

<dialog
    id="dialog"
    width="80%"
    no-back
  >
    <view
      slot="content"
      class="apply-dialog-content"
    >
      <view class="dialog-back">
        <image
          src="/assets/image/apply-dialog.png"
          mode="widthFix"
        />
        <view class="dialog-content text-center">
          <view class="dialog-content-title">确定邮箱地址</view>
          <view class="dialog-content-tip">文件即将发送至：</view>
          <view class="dialog-content-email">{{form.email}}</view>
          <view class="dialog-content-tip">电子回单包含个人隐私信息，请确定邮箱是否正确</view>

          <view class="dialog-content-btn flex-box">
            <view
              class="dialog-btn dialog-cancel-btn"
              bindtap="close"
            >返回修改</view>
            <view
              class="dialog-btn dialog-confirm-btn"
              bindtap="submit"
            >确定发送</view>
          </view>
        </view>
      </view>
    </view>
  </dialog>
  