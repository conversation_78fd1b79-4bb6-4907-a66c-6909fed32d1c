// pages/user/profile.js
import { logout } from "../../api/login";
import { store } from '../../store/index'
import { createStoreBindings } from "mobx-miniprogram-bindings";
import { toPromise} from '../../utils/util'
Page({

  /**
   * 页面的初始数据
   */
  data: {

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.storeBindings = createStoreBindings(this, {
      store,
      fields: ["userInfo"],
      actions: ["setUserInfo"]
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  async logout() {
    const { confirm } = await wx.showModal({
      content: '是否确认退出登录',
      cancelText: '取消',
      confirmText: '确认'
    })
    if (!confirm) return
    wx.showLoading({
      title: '正在退出登录',
    })
    this.setUserInfo({ phone: false })
    await toPromise(logout())
    wx.clearStorage({
      success: (res) => {
        wx.redirectTo({
          url: '/pages/index/index',
        })
      },
    })
    wx.hideLoading()
  }
})