/* pages/user/info.wxss */
page {
  background: var(--theme-page-back);
}

.user-info-item {
  padding: 40rpx 46rpx 15rpx;
  background: #fff;
  background: transparent;
  color: #fff;
}

.user-name {
  flex: 1;
  font-size: 32rpx;
  font-weight: 500;
}

.user-avatar {
  width: 110rpx;
  height: 110rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 32rpx;
}

/* statusbox */
.user-status-box {
  position: relative;
  margin: 0 50rpx;
  padding: 20rpx 0 15rpx;
  justify-content: center;
  background: #fff;
  border-radius: 70rpx;
}
.user-status-box::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
  width: 2rpx;
  height: 44rpx;
  background: #D8D8D8;
}
.user-status {
  margin: 15rpx 0 0;
  color: #6C6C6C;
  font-size: 24rpx;
}

/* operate box */
.user-operate-box {
  margin: 24rpx 46rpx;
  background: #fff;
  border-radius: 20rpx;
}
.user-operate-item {
  height: 140rpx;
  margin: 0 40rpx;
  border-bottom: 2rpx solid #eee;
}
.user-operate-item:last-child {
  border-bottom: none;
}
.user-operate-sub-box {
  margin-top: 48rpx;
}