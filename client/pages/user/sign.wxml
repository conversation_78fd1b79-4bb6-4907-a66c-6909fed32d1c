<!--pages/user/sign.wxml-->
<view class="user-sign-page">
  <view class="sign-tab-box flex-box">
    <view
      class="sign-tab-item text-center flex-item {{ active === -1 ? 'active' : ''}}"
      data-field="-1"
      bindtap="changeTab"
    >
      <view class="sign-tab-inner">
        全部
      </view>
    </view>
    <view
      class="sign-tab-item text-center flex-item {{ active === 0 ? 'active' : ''}}"
      data-field="0"
      bindtap="changeTab"
    >
      <view class="sign-tab-inner">
        未签约
      </view>
    </view>
    <view
      class="sign-tab-item text-center flex-item {{ active === 1 ? 'active' : ''}}"
      data-field="1"
      bindtap="changeTab"
    >
      <view class="sign-tab-inner">
        已签约
      </view>
    </view>
  </view>

  <block wx:if="{{list.length}}">
    <view class="sign-list">
      <view
        wx:for="{{list}}"
        wx:key="index"
        class="sign-list-item"
      >
        <view class="flex-box">
          <view class="flex-item sign-list-name text-hidden-line2">{{ item.cooperateName }}</view>
          <view class="sign-list-status {{ item.signStatus === 400 ? 'sign-status-tosign' : 'sign-status-sign'}}">
            {{ signMap[item.signStatus] }}
          </view>
        </view>
        <view class="sign-list-info">
          <view class="sign-employer">
            用工企业：{{ item.employerName }}
          </view>
          <view class="sign-supplier">
            签约服务商：{{ item.mainstayName}}
          </view>
        </view>
        <view class="sign-list-btn text-right">
          <view
            wx:if="{{item.signStatus === 400}}"
            class="sign-btn-item linear-gradient sign-btn"
            data-index="{{index}}"
            bindtap="onSignTab"
          >
            签署
          </view>
          <view wx:else>
            <view
              class="sign-btn-item "
              data-index="{{index}}"
              bindtap="onSignTab"
            >
              预览
            </view>
            <view class="sign-btn-item " bindtap="downloadSignFile" data-index="{{index}}">
              下载
            </view>
          </view>
        </view>
      </view>
    </view>
  </block>

  <view
    wx:else
    class="text-center sign-empty"
  >
    <view class="sign-empty-text">无签约文件</view>
    <view
      class="linear-gradient sign-empty-btn"
      bindtap="backToUser"
    >返回</view>
  </view>
</view>