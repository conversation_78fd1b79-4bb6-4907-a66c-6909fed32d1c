<!--pages/user/info.wxml-->
<navigation-bar
  height="484"
  title="个人信息"
>
  <view class="user-info-box">
    <view class="user-info-item flex-box" bindtap="goToProfile">
      <view class="user-avatar img-box">
        <open-data type="userAvatarUrl"></open-data>
      </view>
      <view class="user-name">
        {{ userInfo.name || '用户名' }}
      </view>
      <view class="user-arrow">
        <mp-icon icon="arrow" size="{{14}}" color="#ffffff"></mp-icon>
      </view>
    </view>
    <view class="user-status-box flex-box shadow">
      <view
        class="user-status-item flex-item text-center"
        bindtap="onTabVerify"
      >
        <view class="user-status-icon">
          <hj-icon
            icon="user-name"
            size="53"
            height="57"
          ></hj-icon>
        </view>
        <view class="user-status">
          实名认证({{ userInfo.verify ? '已认证' : '未认证'}})
        </view>
      </view>
      <view
        class="user-status-item flex-item text-center"
        bindtap="onTabSign"
      >
        <view class="user-status-icon">
          <hj-icon
            icon="user-sign"
            size="48"
            height="57"
          ></hj-icon>
        </view>
        <view class="user-status">
          电子签约({{ toSignList[userInfo.toSign] }})
        </view>
      </view>
    </view>
  </view>
</navigation-bar>

<view class="user-operate-box user-operate-sub-box">
  <view
    class="user-operate-item flex-box"
    bindtap="tapNav"
    data-url="{{ item.url }}"
    wx:for="{{operateList}}"
    wx:key="index"
  >
    <view class="flex-item">{{ item.name }}</view>
    <mp-icon
      size="{{10}}"
      icon="arrow"
      color="#444444"
      class="user-operate-icon"
    ></mp-icon>
  </view>
</view>