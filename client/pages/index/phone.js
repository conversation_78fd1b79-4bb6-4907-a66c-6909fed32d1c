// index.js
import { buildParams } from "../../utils/util";
Page({
  data: {
    phone: '',
    loginFlag: '',
  },
  onLoad(options) {
    this.setData({
      loginFlag: options.loginFlag
    })
    
  },
  onShow() {
  },
  onInput(e) {
    const { value } = e.detail
    this.setData({
      phone: value
    })
  },
  async sendMessageCode() {
    wx.navigateTo({
      url: '/pages/index/login' + buildParams({ phone: this.data.phone, loginFlag: this.data.loginFlag }),
    })
  },
})
