// pages/index/phone.js
import { codeLogin, getWechatPhone } from "../../api/login";
import { normalizeDict } from "../../utils/dict";
import { getToken } from "../../utils/token";
import { buildParams, redirectAfterLogin, toPromise } from "../../utils/util";
import { store } from '../../store/index'
import { createStoreBindings } from "mobx-miniprogram-bindings";

Page({

  /**
   * 页面的初始数据
   */
  data: {
    check: false,
    btnType: '',
    loginFlag: '',
  },

  onLoad() {
    this.storeBindings = createStoreBindings(this, {
      store,
      actions: ["setUserInfo"],
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onShow: function (options) {
    this.checkLogin()
  },


  checkLogin() {
    let self = this
    return new Promise((resolve, rej) => {
      if (getToken()) {
        wx.switchTab({
          url: '/pages/home/<USER>',
          success: resolve
        })
      } else {
        wx.login({
          timeout: 3000,
          async success(res) {
            if (res.code) {
              const { data: { loginFlag }} = await codeLogin({ code: res.code })
              self.setData({
                loginFlag,
              })
              resolve()
            }
          },
        })
      };
    })
  },

  phoneLogin() {
    if (this.checkFile()) {
      wx.navigateTo({
        url: '/pages/index/phone' + buildParams({ loginFlag: this.data.loginFlag }),
      })
    }
  },
  checkFile() {
    if (!this.data.check) {
      wx.showToast({
        icon: 'none',
        title: '请阅读并同意隐私政策、服务协议等协议'
      })
    }
    return this.data.check
  },
  async getWechatPhone(e) {
    if (!this.checkFile()) return
    wx.showLoading({
      title: '正在登录',
    })
    if (!e.detail.iv || !e.detail.encryptedData) {
      return wx.showToast({
        title: '登录失败',
        icon: 'error'
      })
    }
    const [{ data }] = await toPromise(getWechatPhone({ 
      encryptedData: e.detail.encryptedData,
      iv: e.detail.iv,
      loginFlag: this.data.loginFlag
    }))

    normalizeDict(data)
    this.setUserInfo(data)

    if (!redirectAfterLogin()) {
      wx.switchTab({
        url: '/pages/home/<USER>',
      })
    }
    wx.hideLoading()
  },
  goFile(e) {
    let url = `/pages/file/${e.currentTarget.dataset.file}`
    wx.navigateTo({
      url,
    })
  },
  changeCheck() {
    this.setData({
      check: !this.data.check,
    }, () => {
      this.setData({
        btnType: this.data.check ? 'getPhoneNumber' : ''
      })
    })
  },
})