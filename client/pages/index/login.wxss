/* pages/index/login.wxss */
.index-page {
  padding: 80rpx 50rpx 0;
}
.page-header {
  font-size: 60rpx;
  color: var(--theme-black);
}
.page-header-tooltip {
  margin: 32rpx 0 0;
  color: var(--theme-deep-gray);
}
.page-header-tooltip text {
  color: var(--theme-black);
}
.page-input {
  margin: 32rpx 0 0;
}
.page-input-item {
  padding: 32rpx 0;
}
.page-timer text, .page-resend {
  color: var(--theme-black);
}
.page-button {
  margin: 64rpx auto 0;
}
.page-button-item {
  height: 90rpx;
  line-height: 90rpx !important;
  border-radius: 450rpx;
  background: var(--primary);
  color: #fff;
}