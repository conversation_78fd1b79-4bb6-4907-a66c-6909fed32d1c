// pages/index/login.js
import { createStoreBindings } from "mobx-miniprogram-bindings";
import { store } from "../../store/index";
import { sendMessageCode, login } from "../../api/login";
import { redirectAfterLogin, toPromise } from "../../utils/util";
import { normalizeDict } from "../../utils/dict";
Page({

  /**
   * 页面的初始数据
   */
  data: {
    form: {
      phone: '',
      code: '',
      loginFlag: '',
    },

    count: 60,
    wait: 60,

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      'form.phone': options.phone,
      'form.loginFlag': options.loginFlag,
      count: this.data.wait,
    }, () => {
      this.sendMessageCode()
    })

    this.storeBindings = createStoreBindings(this, {
      store,
      actions: ["setUserInfo"],
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    this.timer && clearTimeout(this.timer)
    this.storeBindings.destroyStoreBindings();
  },

  startCount() {
    if (this.data.count > 0) {
      this.timer = setTimeout(() => {
        this.setData({ count: --this.data.count }, this.startCount)
      }, 1000);
    } else {
      this.timer && clearTimeout(this.timer)
    }
  },
  async sendMessageCode() {
    if (this.loading) return
    this.loading = true
    wx.showLoading({
      title: '正在发送',
    })
    await toPromise(sendMessageCode({ phone: this.data.form.phone }))
    this.startCount()
    this.loading = false
    wx.hideLoading()
  },
  onCodeChange(e) {
    this.setData({
      'form.code': e.detail
    })
  },
  async login() {
    if (this.loading) return
    this.loading = true
    wx.showLoading({
      title: '正在登录',
    })
    const [{ data } = {}] = await toPromise(login(this.data.form))
    normalizeDict(data)
    this.setUserInfo(data)

    if (!redirectAfterLogin()) {
      wx.switchTab({
        url: '/pages/home/<USER>',
      })
    }
    this.loading = false
    wx.hideLoading()
  },
})