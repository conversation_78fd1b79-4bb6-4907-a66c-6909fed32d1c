<!--pages/auth/result.wxml-->
<view class="auth-result-page text-center">
  <view class="page-img">
    <image wx:if="{{status == 200}}" src="/assets/image/auth-success.png" mode="widthFix"></image>
    <image wx:elif="{{status == 100 || status == 300}}" src="/assets/image/auth-fail.png" mode="widthFix"></image>
  </view>

  <view class="page-title {{ status != 200 ? 'fail' : ''}}">
    {{ statusMap[status] }}
  </view>

  <view class="page-button linear-gradient border-20 {{ status != 200 ? 'fail-btn' : ''}}" bindtap="onBtnTab">
    <button hover-class="none" class=" page-button-item">
      {{ status == 100 ? '重新认证' : '返回' }}
    </button>
  </view>
  <view class="page-button page-border-btn border-20" wx:if="{{ status != 200}}" bindtap="goToIndex">
    <button hover-class="none">返回首页</button>
  </view>
</view>
