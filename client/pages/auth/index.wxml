<!--pages/auth/index.wxml-->
<view class="auth-page">
  <mp-form
    models="{{form}}"
    rules="{{rules}}"
    id="form"
  >
    <view class="auth-upload-box border-14 shadow-shallow">
      <view class="page-title">身份证</view>
      <view class="page-form-item flex-box form-upload-box">
        <view
          class="page-form-upload border-4 shadow-gray"
        >
          <upload-image
            id="uploadBack"
            data-field="idCardBackUrl"
            defaultTrigger="{{false}}"
            fileType="pic-block"
            showClose="{{!disabled}}"
            bindchange="onInputImage"
            showAdd="{{!form.idCardBackUrl}}"
            list="{{form.idCardBackUrl}}"
            disabled="{{disabled}}"
          >
            <view
              class="upload-operate-inner  text-center flex-col"
              slot="trigger"
            >
              <image
                class="operate-img"
                src="/assets/image/id-back.png"
                mode="widthFix"
              ></image>
              <view class="operate-tooltip">点击上传 <text class="operate-name">人像面</text></view>
            </view>
          </upload-image>
        </view>
        <view
          class="page-form-upload border-4 shadow-gray"
        >
          <upload-image
            data-field="idCardFrontUrl"
            defaultTrigger="{{false}}"
            fileType="pic-block"
            showClose="{{!disabled}}"
            showAdd="{{!form.idCardFrontUrl}}"
            bindchange="onInputImage"
            list="{{form.idCardFrontUrl}}"
            disabled="{{disabled}}"
          >
            <view
              class="upload-operate-inner  text-center flex-col"
              slot="trigger"
            >
              <image
                class="operate-img"
                src="/assets/image/id-front.png"
                mode="widthFix"
              ></image>
              <view class="operate-tooltip">点击上传 <text class="operate-name">国徽面</text></view>
            </view>
          </upload-image>
        </view>
        <!-- <view
          class="page-form-upload border-4 shadow-gray"
          bindtap="chooseImg"
          data-field="idCardBackUrl">
          <view
            class="upload-img-inner flex-col "
            wx:if="{{ form.idCardBackUrl || disabled}}"
          >
            <image
              class="upload-inner"
              src="{{ fileUrl + form.idCardBackUrl }}"
              mode="widthFix"
            ></image>
          </view>
          <view
            wx:else
            class="upload-operate-inner  text-center flex-col"
          >
            <image
              class="operate-img"
              src="/assets/image/id-back.png"
              mode="widthFix"
            ></image>
            <view class="operate-tooltip">点击上传 <text class="operate-name">人像面</text></view>

          </view>
        </view> -->
        <!-- <view
          class="page-form-upload border-4 shadow-gray"
          bindtap="chooseImg"
          data-field="idCardFrontUrl"
        >
          <view
            class="upload-img-inner flex-col"
            wx:if="{{form.idCardFrontUrl || disabled}}"
          >
            <image
              class="upload-inner"
              src="{{ fileUrl + form.idCardFrontUrl }}"
              mode="widthFix"
            ></image>
          </view>
          <view
            wx:else
            class="upload-operate-inner text-center flex-col"
          >
            <image
              class="operate-img"
              src="/assets/image/id-front.png"
              mode="widthFix"
            ></image>
            <view class="operate-tooltip">点击上传 <text class="operate-name">国徽面</text></view>
          </view>
        </view> -->
      </view>
      <view class="upload-tooltip">
        <view>* 请清晰拍摄身份证信息，拍摄完整，不可有遮盖物遮挡</view>
        <view>* 请确保图片光源充足，文字清晰可辨</view>
      </view>
    </view>
    <view class="auth-input-box border-14 shadow-shallow">
      <view class="page-title">实名认证</view>
      <mp-cells ext-class="auth-cells">
        <mp-cell
          title="姓名"
          prop="name"
          show-error
          ext-class="auth-cell-item"
        >
          <input
            disabled="{{disabled}}"
            value="{{form.name}}"
            class="form-input-item"
            data-field="name"
            placeholder="请输入您的姓名"
            bindinput="onInput"
          />
        </mp-cell>
        <mp-cell
          title="身份证号"
          prop="idCardNo"
          show-error
          ext-class="auth-cell-item"
        >
          <input
            disabled="{{disabled}}"
            value="{{form.idCardNo}}"
            class="form-input-item"
            data-field="idCardNo"
            placeholder="请输入您的身份证号"
            bindinput="onInput"
          />
        </mp-cell>
        <mp-cell
          title="手机号"
          prop="phone"
          show-error
          ext-class="auth-cell-item"
        >
          <input
            style="color: #ddd"
            value="{{userInfo.phone}}"
            disabled
            class="form-input-item"
            data-field="phone"
            placeholder="请填写手机号"
            bindinput="onInput"
          />
        </mp-cell>
      </mp-cells>
    </view>
    <view class="page-button linear-gradient border-20">
      <button
        hover-class="none"
        class="page-button-item text-center"
        bindtap="submitForm"
      >{{ disabled ? '修改实名信息' : '提交认证'}}</button>
    </view>
  </mp-form>
</view>