/* pages/auth/index.wxss */
page {
  background: var(--theme-page-back);
}
.auth-page {
  padding: 30rpx;
}
.auth-upload-box, .auth-input-box {
  margin-bottom: 30rpx;
  padding: 35rpx 25rpx;
  background: #fff;
}
.page-title {
  color: #222;
  font-size: 30rpx;
}

.form-upload-box {
  margin-top: 32rpx;
  justify-content: space-between;
}

.page-form-upload {
  width: 313rpx;
  height: 180rpx;
  white-space: nowrap;
  overflow: hidden;
}
.upload-img-inner {
  background: var(--theme-deep-gray);
}
.upload-operate-inner {
  padding: 22rpx 0;
}
.operate-img {
  width: 131rpx;
}
.operate-tooltip {
  margin: 15rpx 0 0;
  font-size: 26rpx;
  color: var(--theme-tooltip-gray);
}
.operate-name {
  color: #FF4527;
}
.upload-tooltip {
  margin: 45rpx 0 0;
  font-size: 24rpx;
  color: var(--theme-tooltip-gray);
}

.auth-cell-item {
  padding: 24rpx 0;
  font-size: 26rpx !important;
}
.auth-cell-item .weui-label {
  width: 137rpx;
}
.auth-cell-item.weui-cell::before {
  left: 138rpx !important;
}
.auth-cells .weui-cells::before {
  display: none;
}
.auth-cells .weui-cells::after {
  left: 138rpx !important;
}
.auth-cells {
  padding: 0 15rpx;
}

/* button */
.page-button {
  margin: 114rpx auto;
}