// pages/auth/result.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    status: 0,

    statusMap: {
      100: '认证失败',
      200: '认证通过',
      300: '操作过于频繁，24小时内不能超过3次',
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.setData({
      status: options.status
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  onBtnTab() {
    if (this.data.status == 200 || this.data.status == 300) {
      let url = ''
      const pageStack = getApp().globalData.pageStack
      if (pageStack.isActive) {
        if (url = pageStack.find(/pages\/task\/detail/)) {
          url = '/' + url
          pageStack.destroy()
        }
      }
      if (!url) url = '/pages/user/sign'
      console.log('auth resutl url >>>>', url)
      wx.navigateTo({
        url
      })
    } else {
      wx.redirectTo({
        url: '/pages/auth/index',
      })
    }
  },
  goToIndex() {
    wx.switchTab({
      url: "/pages/home/<USER>"
    })
  }
})