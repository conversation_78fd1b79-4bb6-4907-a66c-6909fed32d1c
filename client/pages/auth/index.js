// pages/auth/index.js
import { auth, getIdCard } from "../../api/user";
import { buildParams, toPromise } from "../../utils/util";
import { fileUrl } from "../../setting/index";
import { createStoreBindings } from "mobx-miniprogram-bindings";
import { store } from '../../store/index'
import { idCardOcr } from "../../api/ocr";

Page({

  /**
   * 页面的初始数据
   */
  data: {
    disabled: false,
    form: {
      idCardBackUrl: '',
      idCardFrontUrl: '',
      name: '',
      idCardNo: '',
    },
    fileUrl,
    rules: [
      {
        name: 'name',
        rules: {
          required: true, 
          message: '请输入姓名'
        }
      },
      {
        name: 'idCardNo',
        rules: {
          required: true, 
          message: '请输入身份证号'
        }
      },
      {
        name: 'idCardBackUrl',
        rules: {
          required: true, 
        }
      },
      {
        name: 'idCardFrontUrl',
        rules: {
          required: true, 
        }
      },
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.getId()

    this.storeBindings = createStoreBindings(this, {
      store,
      actions: ["setUserInfo"],
      fields: ["userInfo"],
    })
    wx.nextTick(() => {
      this.setData({
        disabled: this.data.userInfo.verify,
      })
      if (this.data.userInfo.verify) {
        this.setData({
          'form.name': this.data.userInfo.name,
          'form.idCardNo': this.data.userInfo.idcard,
        })
      }
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {

  },
  onUnload() {
    this.storeBindings.destroyStoreBindings();
  },

  async getId() {
    const { data } = await getIdCard()
    if (!data) return;
    if (data.idCardBackUrl) {
      this.setData({
        'form.idCardBackUrl': data.idCardBackUrl
      })
    }
    if (data.idCardFrontUrl) {
      this.setData({
        'form.idCardFrontUrl': data.idCardFrontUrl
      })
    }
  },

  async submitForm() {
    if (this.data.disabled) {
      return this.setData({
        disabled: false,
      })
    }
    const self = this;
    const form = this.selectComponent('#form')
    form.validate(function(isValid) {
      if (!isValid) {
        return wx.showToast({
          title: '请补充信息',
          icon: 'error'
        })
      }
      wx.showLoading({
        title: '认证中',
      })
      self.submit()
    })
  },

  async submit() {
    const [{ data: {verifyStatus} }, err] = await toPromise(auth(this.data.form))
    if (err) return
    this.setUserInfo({ verify: true })
    wx.redirectTo({
      url: '/pages/auth/result' + buildParams({ status: verifyStatus }),
    })
    wx.hideLoading()
  },

  onInput(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      ['form.' + field]: e.detail.value
    })
  },

  onInputImage(e) {
    const { field } = e.currentTarget.dataset;
    const val = e.detail.value[0]?.url || ''
    this.setData({
      ['form.' + field]: val
    })
    if (field === 'idCardBackUrl' && val) {
      let uploadRef = this.selectComponent('#uploadBack')
      uploadRef.toggleLoading(0, true)
      try {
        setTimeout(() => {
          uploadRef.toggleLoading(0, false)
        }, 3000);
      } catch {
        wx.showToast({
          title: 'not ok',
        })
      }
      // ocr
      this.getOcr(val)
    }
  },

  async getOcr(url) {
    this.setData({
      disabled: true
    })
    wx.showLoading({
      title: '识别中',
    })
    const [{data}, err] = await toPromise(idCardOcr({fileUrl: url}))
    wx.hideLoading().then(() => {
      if (err) {
        wx.showToast({
          title: err,
          icon: 'none',
        })
      }
    })
    this.setData({
      disabled: false,
    })
    if (err) return
    if (data) {
      this.setData({
        'form.name': data.name,
        'form.idCardNo': data.idcardNo,
      })
    }
  },

})