import { uploadUrl } from "../setting/index";
import { getToken } from "../utils/token";
export function upload(data) {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: uploadUrl + '/upload',
      filePath: data,
      name: 'file',
      header: {
        Authorization: getToken(),
      },
      success (res){
        resolve(JSON.parse(res.data))
      },
      fail: reject
    })
  })
}