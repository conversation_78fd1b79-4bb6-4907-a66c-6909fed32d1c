import { post } from "../utils/request";

export function getIdCard() {
  return post({
    url: 'weChat.getIdCard'
  })
}

export function auth(data) {
  return post({
    url: 'weChat.verify',
    data
  })
}

export function toSignList(data) {
  return post({
    url: 'weChat.toSign',
    data,
  })
}

export function signList(data) {
  return post({
    url: 'weChat.hasSign',
    data,
  })
}

export function getUserInfo() {
  return post({
    url: 'weChat.getUserInfo',
  })
}

export function listSign(data) {
  return post({
    url: 'weChat.listSign',
    data,
  })
}