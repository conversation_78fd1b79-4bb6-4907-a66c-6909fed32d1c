import { post } from "../utils/taskRequest";

export function taskList(data) {
  return post({
    url: '/employee/job/list',
    data
  })
}

export function taskDetail({ id }) {
  return post({
    url: '/employee/job/getJob/' + id
  })
}

export function jobList(data) {
  return post({
    url: '/employee/jobWorker/selectJobAndWorker',
    data,
  })
}

export function jobDetail({ id }) {
  return post({
    url: '/employee/jobWorker/selectJobAndWorkerDetail/' + id
  })
}

export function acceptJob({ id }) {
  return post({
    url: '/employee/jobWorker/acceptJob/' + id
  })
}

export function submitJob(data) {
  return post({
    url: '/employee/jobWorker/submit',
    data,
  })
}
