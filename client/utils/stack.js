class Stack {
  // 模拟堆栈
  constructor(options = {}) {
    this.isActive = true
    this.list = []
    this.blackList = options.blackList || []
  }

  pushStack(data) {
    if (!this.isActive || this.blackList.findIndex(item => item === data) !== -1) return
    this.list.push(data)
  }

  popStack() {
    if (!this.isActive) return
    this.list.pop()
  }

  find(data) {
    if (typeof data === 'string') {
      return this.list.find(item => item === data)
    } else if (data instanceof RegExp) {
      return this.list.find(item => data.test(item))
    }
  }

  clear() {
    this.list = []
  }

  deactive() {
    this.isActive = false
  }

  destroy() {
    this.clear() && this.deactive()
  }
}

export default Stack