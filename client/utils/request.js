import { baseUrl } from '../setting/index'
import { getToken, setToken } from './token'

export default function request (options) {
  let {
    url = '',
    method = 'POST',
    data = {},
    timeout = 10000,
    silence = false,
  } = options
  method = method.toUpperCase();
  let header = {
    Authorization: getToken(),
  }
  if (method === 'GET') {
    header['content-type'] = 'application/x-www-form-urlencoded'
  }
  return new Promise((resolve, reject) => {
    wx.request({
      url: baseUrl,
      method,
      data: {
        ...data,
        method: url
      },
      header,
      timeout,
      success (res) {
        if (res.data && res.data.data && res.data.data.accessToken) {
          setToken(res.data.data.accessToken)
        }
        if (res.statusCode !== 200) {
          wx.showToast({
            title: '网络出错',
            icon: 'none'
          })
          return reject()
        }
        let {code, message} = res.data;
        console.log('code >>>>>> ', code )
        if (code === 20000) {
          resolve(res.data)
        } else {
          if (!silence) {
            wx.showToast({
              title: res.data.data?.biz_err_msg || message || '网络出错',
              icon: 'none',
              duration: 3000
            })
          }
          if (message && message.match(/token失效/i)) {
            setToken()
            // wx.redirectTo({
            //   url: '/pages/index/index',
            // })
          }
          reject(res.data.data?.biz_err_msg || message || '网络出错')
        }
      },
      fail: reject,
    })
  })
}

export function get(options) {
  return request({
    ...options,
    method: 'get'
  })
}

export function post(options) {
  return request({
    ...options,
    method: 'post'
  })
}