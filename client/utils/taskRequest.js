import { taskBaseUrl } from '../setting/index'
import { getToken, setToken } from './token'

const request = makeRequest({baseUrl: taskBaseUrl})

export default request

export function get(options) {
  return request({
    ...options,
    method: 'get'
  })
}

export function post(options) {
  return request({
    ...options,
    method: 'post'
  })
}

export function makeRequest(props) {
  return function (options) {
    let {
      url = '',
      method = 'POST',
      data = {},
      timeout = 10000,
    } = options
    method = method.toUpperCase();
    let header = {
      Authorization: getToken(),
    }
    if (method === 'GET') {
      header['content-type'] = 'application/x-www-form-urlencoded'
    }
    return new Promise((resolve, reject) => {
      wx.request({
        url: props.baseUrl + url,
        method,
        data,
        header,
        timeout,
        success (res) {
          if (res.data && res.data.data && res.data.data.accessToken) {
            setToken(res.data.data.accessToken)
          }
          if (res.statusCode !== 200) {
            wx.showToast({
              title: '网络出错',
              icon: 'none'
            })
            return reject()
          }
          let {code, message} = res.data;
          if (code === 20000) {
            resolve(res.data)
          } else {
            wx.showToast({
              title: message || '网络出错',
              icon: 'none',
            })
            if (message && message.match(/token失效/i)) {
              // wx.redirectTo({
              //   url: '/pages/index/index',
              // })
              setToken()
            }
            reject(message || '网络出错')
          }
        },
        fail: reject,
      })
    })
  }
}