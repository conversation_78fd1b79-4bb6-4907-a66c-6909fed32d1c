const formatTime = date => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}

const formatNumber = n => {
  n = n.toString()
  return n[1] ? n : `0${n}`
}
function buildParams(params) {
  let res = []
  Object.keys(params).forEach(item => {
    res.push(`${item}=${params[item]}`)
  })
  res = res.join('&')
  return res ? '?' + res : ''
}

function toPromise(promise) {
  return new Promise(resolve => {
    promise
      .then(res => resolve([res, null]))
      .catch(error => resolve([{}, error]))
  })
}

function debounce(func, delay = 300) {
  let timer = null;
  return function () {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      func.apply(this, arguments)
    }, delay);
  }
}

function previewImage(urls, currentIndex) {
  wx.previewImage({
    urls,
    current: urls[currentIndex]
  })
}

function redirectAfterLogin() {
  let url = wx.getStorageSync('login_redirect')
  if (url) {
    wx.redirectTo({
      url
    }).then(() => {
      // 
    }).catch(() => {
      wx.switchTab({
        url,
      })
    }).finally(() => {
      wx.setStorage({
        key: 'login_redirect',
        data: ''
      })
    })
    return true
  }
  return false
}

module.exports = {
  formatTime,
  buildParams,
  toPromise,
  debounce,
  previewImage,
  redirectAfterLogin,
}
