{"name": "client", "lockfileVersion": 2, "requires": true, "packages": {"": {"dependencies": {"miniprogram-computed": "^4.3.6", "mobx-miniprogram": "^4.13.2", "mobx-miniprogram-bindings": "^2.1.5"}}, "node_modules/@types/wechat-miniprogram": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@types/wechat-miniprogram/-/wechat-miniprogram-3.4.0.tgz", "integrity": "sha512-7d7KDwoV42/ANx/0C8C4ctBv1q9XnXYD4Fz0C+3ii0Tm+HRIwwDcinv8S88Ut/Y80czlI0shGpTgLFe0GbrXWQ=="}, "node_modules/fast-deep-equal": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz", "integrity": "sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk="}, "node_modules/miniprogram-computed": {"version": "4.3.6", "resolved": "https://registry.npmjs.org/miniprogram-computed/-/miniprogram-computed-4.3.6.tgz", "integrity": "sha512-X157D3YuX4yaC/yW6qn1zsCtLZuoaQPSj+cT9HZDiDOtrKuv51MyIHlWpnN1jihZ2N2cM7a1V6Oo7g3UJB3Fnw==", "dependencies": {"fast-deep-equal": "^2.0.1", "rfdc": "^1.1.4"}}, "node_modules/mobx-miniprogram": {"version": "4.13.2", "resolved": "https://registry.npmjs.org/mobx-miniprogram/-/mobx-miniprogram-4.13.2.tgz", "integrity": "sha512-C3<PERSON>tkqHCLKp35N2gXA80yFD6PNfSqwioPFmnjTQpBLrEqYm1F6yiWZ57CW+8FqgmR306mw6ZfsM5q7hSTHw8BQ=="}, "node_modules/mobx-miniprogram-bindings": {"version": "2.1.5", "resolved": "https://registry.npmjs.org/mobx-miniprogram-bindings/-/mobx-miniprogram-bindings-2.1.5.tgz", "integrity": "sha512-2<PERSON>uiQL5FPgoYJ8XIMdw5iD6XT61LtsujaGyizbN845iZcsLmrKZiF0dKd4jQTSuC/6rGozqqhcy0sDc7qcpjYQ==", "dependencies": {"@types/wechat-miniprogram": "^3.4.0"}, "peerDependencies": {"mobx-miniprogram": "^4.0.0"}}, "node_modules/rfdc": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/rfdc/-/rfdc-1.3.0.tgz", "integrity": "sha512-V2hovdzFbOi77/WajaSMXk2OLm+xNIeQdMMuB7icj7bk6zi2F8GGAxigcnDFpJHbNyNcgyJDiP+8nOrY5cZGrA=="}}, "dependencies": {"@types/wechat-miniprogram": {"version": "3.4.0", "resolved": "https://registry.npmjs.org/@types/wechat-miniprogram/-/wechat-miniprogram-3.4.0.tgz", "integrity": "sha512-7d7KDwoV42/ANx/0C8C4ctBv1q9XnXYD4Fz0C+3ii0Tm+HRIwwDcinv8S88Ut/Y80czlI0shGpTgLFe0GbrXWQ=="}, "fast-deep-equal": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-2.0.1.tgz", "integrity": "sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk="}, "miniprogram-computed": {"version": "4.3.6", "resolved": "https://registry.npmjs.org/miniprogram-computed/-/miniprogram-computed-4.3.6.tgz", "integrity": "sha512-X157D3YuX4yaC/yW6qn1zsCtLZuoaQPSj+cT9HZDiDOtrKuv51MyIHlWpnN1jihZ2N2cM7a1V6Oo7g3UJB3Fnw==", "requires": {"fast-deep-equal": "^2.0.1", "rfdc": "^1.1.4"}}, "mobx-miniprogram": {"version": "4.13.2", "resolved": "https://registry.npmjs.org/mobx-miniprogram/-/mobx-miniprogram-4.13.2.tgz", "integrity": "sha512-C3<PERSON>tkqHCLKp35N2gXA80yFD6PNfSqwioPFmnjTQpBLrEqYm1F6yiWZ57CW+8FqgmR306mw6ZfsM5q7hSTHw8BQ=="}, "mobx-miniprogram-bindings": {"version": "2.1.5", "resolved": "https://registry.npmjs.org/mobx-miniprogram-bindings/-/mobx-miniprogram-bindings-2.1.5.tgz", "integrity": "sha512-2<PERSON>uiQL5FPgoYJ8XIMdw5iD6XT61LtsujaGyizbN845iZcsLmrKZiF0dKd4jQTSuC/6rGozqqhcy0sDc7qcpjYQ==", "requires": {"@types/wechat-miniprogram": "^3.4.0"}}, "rfdc": {"version": "1.3.0", "resolved": "https://registry.npmjs.org/rfdc/-/rfdc-1.3.0.tgz", "integrity": "sha512-V2hovdzFbOi77/WajaSMXk2OLm+xNIeQdMMuB7icj7bk6zi2F8GGAxigcnDFpJHbNyNcgyJDiP+8nOrY5cZGrA=="}}}