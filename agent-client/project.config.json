{"description": "项目配置文件", "miniprogramRoot": "miniprogram/", "compileType": "miniprogram", "setting": {"useCompilerPlugins": ["typescript"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false, "minified": true, "postcss": true, "uglifyFileName": true, "es6": true, "enhance": true, "packNpmManually": true, "coverView": false, "showShadowRootInWxmlPanel": false, "packNpmRelationList": [], "ignoreUploadUnusedFiles": true}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "miniprogram/", "appid": "wxbbed3ed61a6ce129", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "packNpmManually": true, "packNpmRelationList": [{"packageJsonPath": "./package.json", "miniprogramNpmDistDir": "./miniprogram/"}], "libVersion": "3.0.1", "packOptions": {"ignore": [], "include": []}}