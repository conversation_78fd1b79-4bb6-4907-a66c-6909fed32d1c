/* pages/main/me/index.wxss */
.mainContainer{
  background-color: #f5f5f9;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.headContainer{
  height: 300rpx;
  background: -webkit-linear-gradient(90deg, #7ce0e6,#5becfc,#34a1cf);/* Chrome 10-25, Safari 5.1-6 */                          background: linear-gradient(90deg, #ace7eb,#70e8f5,#84d3f5);/* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */                                             
  width: 100%;
  display: flex;
}

.headImg{
  margin-top: 80rpx;
  margin-left: 40rpx;
  background-color: inherit !important;
}

.headText{
  margin-left: 20rpx;
  width: 100%;
  margin-top: 120rpx;
  background-color: inherit;
}

.titleClass{
  margin-right: 160rpx !important;
  background-color: inherit !important;
  font-size: 38rpx !important;
  border: none !important;
}

.naviCellClass{
  background-color: white;
  border-radius: 10rpx;
  width: 96%;
  height: 270rpx;
  margin-top: 20rpx;
  align-self: center;
}

.navClass{
  
}

button::after{
  border: none;
}