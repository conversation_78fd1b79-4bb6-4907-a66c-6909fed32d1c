// pages/main/me/index.ts
import { wechatLogin,getMiniUserByOpenIdAndAppId } from "../../../api/common"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    nickName:"微信用户",
    avatarUrl:"https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132"
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    var app = getApp()
    let sessionInfo = wx.getStorageSync(app.globalData.sessionStorageKey)
    let userInfo = wx.getStorageSync(getApp().globalData.userInfoStorageKey)
    if (sessionInfo && userInfo) {
      console.log(userInfo)
      this.setData({
        avatarUrl:userInfo.avatar==null?this.data.avatarUrl:userInfo.avatar,
        nickName:userInfo.nickname==null?this.data.nickName:userInfo.nickname
      })
    } else {
      this.login()
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  login(){
    wx.login({
      success: (res) => {
        console.log("登录成功")
        var code = res.code
        wechatLogin({code:code}).then(res=>{
          if(res.data){
            getMiniUserByOpenIdAndAppId({
              openId:res.data.openid
            }).then(resp=>{
              wx.setStorageSync(getApp().globalData.sessionStorageKey,res.data)
              if(resp.data){
                wx.setStorageSync(getApp().globalData.userInfoStorageKey,resp.data)
                this.setData({
                  avatarUrl:resp.data.avatar==null?this.data.avatarUrl:resp.data.avatar,
                  nickName:resp.data.nickname==null?this.data.nickName:resp.data.nickname
                })
              }else{
                wx.showModal({
                  title:"是否完善个人信息？",
                  success:(res)=>{
                    if(res.confirm){
                      this.modifyMobile()
                    }else{
                      wx.navigateTo({url:'../home/<USER>'})
                    }
                  }
                })
              }
            })
          }else{
            wx.showToast({
              title:"登录失败",
              duration:2000
            })
          }
        })
      },
      fail: (res) => {
        console.log("登录失败")
        wx.showToast({
          title:"登录失败:"+res.errMsg,
          duration:2000
        })
      }
    })
  },
  logout(){
    var that = this
    wx.showModal({
      title:"退出",
      content:"确定退出登录吗？",
      success (res) {
        if (res.confirm) {
          wx.removeStorageSync(getApp().globalData.userInfoStorageKey)
          wx.removeStorageSync(getApp().globalData.sessionStorageKey)
          wx.removeStorageSync("access_token")
          that.setData({
            nickName:"微信用户",
            avatarUrl:"https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132"
          })
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      }
    })
  },

  loginAgentSystem(){
    let access_token = wx.getStorageSync("access_token")
    if(access_token){
      wx.navigateTo({
        url: '../../../subpackages/pages/account/select'
      })
    }else{
      wx.navigateTo({
        url: '../../../subpackages/pages/login/index'
      })
    }

    // loginAgentSystem({
    //   openId:wx.getStorageSync(getApp().globalData.sessionStorageKey).openid
    // }).then(res=>{
    //   getApp().globalData.accountInfo = {
    //     operator:res.data.agentOperatorVO,
    //     agentNos:res.data.agentNos
    //   }
    //   setToken(res.data.token)
    //   wx.navigateTo({
    //     url: '../../../subpackages/pages/account/select'
    //   })
    // })
  },
  modifyMobile(){
    wx.navigateTo({
      url:'../profile/editmobile'
    })
  },
  registAgent(){
    wx.navigateTo({
      url: '../../../subpackages/pages/login/register'
    })
  }
})