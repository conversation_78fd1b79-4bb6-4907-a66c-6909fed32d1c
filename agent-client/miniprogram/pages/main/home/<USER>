/* pages/main/home/<USER>/
.mainContainer{
  background-color: #f5f5f9;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.businessCard {
  background-color: white;
  min-height: 400rpx;
  width: 98%;
  align-self: center;
  border-radius: 30rpx;
  margin-top: 20rpx;
  padding-bottom: 20rpx;
  box-shadow:1rpx 1rpx 1rpx rgb(167, 166, 166);
}

.card{
  background-color: white;
  height: auto;
  width: 96%;
  align-self: center;
  border-radius: 30rpx;
  margin-top: 20rpx;
}

.title{
  display: flex;
  padding-top: 100rpx;
  flex-direction: column;
}

.title-font{
  font-size: 46rpx;
  font-weight: bold;
  align-self: center;
}

.nameInfo{
  padding-top: 20rpx;
  width: 82%;
}

.name-font{
  font-weight: bold;
  font-size: 42rpx;
  padding-left: 50rpx;
}

.name-sub-font{
  margin-left: 20rpx;
  font-size: 28rpx;
}

.company-sub-font{
  margin-top: 40rpx;
  font-size: 32rpx;
}

.company-address-font{
  margin-top: 40rpx;
  font-size: 30rpx;
}
.exchange{
  color: #465CFF  !important;
}
.contactInfo{
  margin-top: 30rpx;
  display: flex;
  flex-direction: column;
}

.contact-item{
   margin-left: 50rpx;
   margin-top: 6rpx;
}
.section{
  margin-top: 40rpx;
  display: flex;
  flex-direction: column;
}

.groupItem{
  height: 200rpx;
  width: 47%;
  border-radius: 30rpx;
  background-color: white;
  display: flex;
  box-shadow:1rpx 1rpx 1rpx rgb(167, 166, 166);
}
.groupInfo{
  display: flex;
  margin-top: 30rpx;
  /* flex-direction: row; */
  flex-wrap: nowrap;
  justify-content: space-around;
  padding-bottom: 40rpx;
  width: 96%;
  align-self: center;
}

.groupItem-font{
  margin-left: 80rpx;
  margin-top: 60rpx;
  font-size: 42rpx;
  font-weight: bold;
}

.bussinessInfo{
  display: flex;
  margin-top: 30rpx;
  /* flex-direction: row; */
  flex-wrap: nowrap;
  justify-content: space-around;
  padding-bottom: 40rpx;
  width: 96%;
  align-self: center;
}

.bussinessItem{
  height: 140rpx;
  width: 30%;
  border-radius: 10rpx;
  background-color: white;
  box-shadow:1rpx 1rpx 1rpx rgb(167, 166, 166);
}

.bussinessItem-font{
  margin-left: 14rpx;
  padding-top: 14rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.bussinessItem-sub-font{
  margin-left: 24rpx;
  padding-top: 14rpx;
  font-size: 28rpx;
}

.bussinessScopeInfo{
  display: flex;
  margin-top: 30rpx;
  flex-wrap: nowrap;
  justify-content: space-around;
  padding-bottom: 40rpx;
  width: 96%;
  align-self: center;
}
.bussinessScopItem{
  height: 120rpx;
  width: 23%;
  border-radius: 10rpx;
  background-color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-shadow:1rpx 1rpx 1rpx rgb(167, 166, 166);
}
.bussinessScopItem-font{
  font-size: 28rpx;
  font-weight: bold;
  text-align: center;
}
