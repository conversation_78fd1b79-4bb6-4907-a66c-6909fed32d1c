<!--pages/main/home/<USER>
<view class="mainContainer">
  <view class="businessCard" wx:if="{{loginStatus==false || ecardName==null}}">
    <view class="title">
      <text class="title-font">暂未创建电子名片</text>
      <van-button icon="add-o" size='large' type="primary" color="#7332dd" style="margin-top: 40rpx;width: 90%; align-self: center;"
      bind:tap="creatEcard">创建我的电子名片</van-button>
    </view>
  </view>

  <view class="businessCard" wx:if="{{loginStatus && ecardName}}">
    <view style="display: flex;flex-direction: row;">
      <view class="nameInfo">
        <view>
          <text wx:if="{{ecardName}}" class="name-font">{{ecardName}}</text>
          <text wx:if="{{position}}" class="name-sub-font">{{position}}</text>
        </view>
        <view style="padding-left: 50rpx;margin-top: 10rpx;">
          <fui-text wx:if="{{company}}" userSelect size="30" text='{{ company }}' class="company-sub-font"></fui-text>
        </view>
        <view style="padding-left: 50rpx;">
          <fui-text wx:if="{{address}}" userSelect size="28"  text="{{address}}" class="company-address-font"></fui-text>
        </view>
      </view>
      <view style="margin-top: 40rpx; width: 28%;display: flex; flex-direction: column;">
        <view style="padding-right: 18rpx; height: fit-content; align-self: flex-end;">
          <fui-avatar  shape='square' size="large" src="{{ avatarUrl }}"></fui-avatar>
        </view>
      </view>
    </view>

    <view style="display: flex;flex-direction: row;width: 98%;">
      <view style="display: flex;width: 100%;" wx:if="shareOpenId==null">
      <view class="contactInfo" style="width: 100%;">
        <view wx:if="{{ecardMobile}}" class="contact-item" style="display: flex;flex-direction: row;width: 100%;">
          <view style="margin-right: 10rpx;width: 6%;">
            <van-icon style="padding-top: 14rpx;" name="phone-circle" color="#4587fc" />
          </view>
          <view style="width: 85%;" class="fui-wrap">
            <fui-text wordBreak userSelect type="mobile" text="{{ecardMobile}}" size="36" call></fui-text>
          </view>
        </view>
        <view wx:if="{{email}}" class="contact-item" style="display: flex;flex-direction: row;width: 100%;">
          <view style="margin-right: 10rpx;width: 6%;height: 10rpx;">
            <van-icon style="padding-top: 14rpx;" color="#4587fc" name="envelop-o"  />
          </view>
          <view style="width: 85%;" class="fui-wrap">
            <fui-text wordBreak userSelect text="{{email}}" block size="36"></fui-text>
          </view>
        </view>
        <view wx:if="{{wechatId}}" class="contact-item" style="display: flex;flex-direction: row;width: 100%;">
          <view style="margin-right: 10rpx;width: 6%;">
            <van-icon style="padding-top: 14rpx;" color="#4587fc" name="wechat"/>
          </view>
          <view style="width: 85%;" class="fui-wrap">
            <fui-text wordBreak userSelect text="{{wechatId}}" block size="36"></fui-text>
          </view>
        </view>
      </view>
    </view>
  </view>
    <view  style="display: flex;flex-direction: row;justify-content: flex-end;">
      <view wx:if="{{shareOpenId==null}}" style="width: 40%;margin-top: auto;margin-bottom: 10rpx;">
        <van-cell value="修改" is-link border="{{false}}" bind:tap="editEcard"/>
      </view>
      <view wx:else style="width: 40%;margin-top: auto;margin-bottom: 10rpx;">
        <van-cell custom-class="exchange" value-class="exchange" value="我的名片"  border="{{false}}" bind:tap="exchangeEcard">
          <van-icon slot="right-icon" name="exchange" class="custom-icon" />
        </van-cell>
      </view>
    </view>
  </view>

  <view wx:if="{{ecardName && shareOpenId==null}}" style="width: 98%;background-color: white;margin-top: 20rpx;align-self: center;border-radius: 30rpx;">
    <van-button size="large" type="default" icon="guide-o" open-type="share">
      分享名片
    </van-button>
    <!-- <button data-name="shareBtn" open-type="share">转发</button> -->
  </view>

  <view wx:if="{{ecardName && shareOpenId}}" style="width: 98%;background-color: white;margin-top: 20rpx;align-self: center;border-radius: 30rpx;display: flex;justify-content: space-between;">
    <van-button wx:if="{{ecardMobile}}" custom-style="border:none"  type="default" icon="phone-o" bind:tap="callPhone">
      拨打电话
   </van-button>
   <van-button wx:if="{{wechatId}}" custom-style="border:none" type="default" icon="friends-o" bind:tap="copyWechatId">
    加好友
   </van-button>
   <van-button wx:if="{{email}}" custom-style="border:none"  type="default" icon="envelop-o" bind:tap="copyEmail">
    邮件
   </van-button>
  </view>

  <view class="section">
    <fui-section title="集团介绍" isLine></fui-section>
    <view class="groupInfo">
      <view class="groupItem">
        <text class="groupItem-font" data-imgSource="https://static.hjzxh.com/hjzx/M00/0A/73/Ch62CmWDsiCAHRXVACgGRdF2Zfs518.png" data-navTitle="正佳集团" bind:tap="naviTo">正佳集团</text>
      </view>
      <view class="groupItem">
        <text class="groupItem-font" data-imgSource="https://static.hjzxh.com/hjzx/M00/0A/73/Ch62CmWDskSARhCaABaXVIOjWU0982.png" data-navTitle="正佳科技" bind:tap="naviTo">正佳科技</text>
      </view>
    </view>
  </view>

  <view class="section">
    <fui-section title="业务介绍" isLine></fui-section>
    <view class="bussinessInfo">
      <view class="bussinessItem" data-imgSource="https://static.hjzxh.com/hjzx/M00/0A/73/Ch62CmWDsl-Ad2C8ABglNJWEcdk286.png" data-navTitle="智享汇" bind:tap="naviTo">
        <view class="bussinessItem-font">
          <text>智享汇</text>
        </view>
        <view class="bussinessItem-sub-font">
          <text>灵活用工</text>
        </view>
      </view>
      <view class="bussinessItem" data-imgSource="https://static.hjzxh.com/hjzx/M00/0A/73/Ch62CmWDsZeAZjMkAAjrz78tFK0983.png" data-navTitle="创客汇" bind:tap="naviTo">
        <view class="bussinessItem-font">
          <text>创客汇</text>
        </view>
        <view class="bussinessItem-sub-font">
          <text>临登普票代开</text>
        </view>
      </view>
      <view class="bussinessItem">
        <view class="bussinessItem-font">
          <text>聚客汇</text>
        </view>
        <view class="bussinessItem-sub-font">
          <text>个体工商户</text>
        </view>
      </view>
    </view>
  </view>

  <view class="section">
    <fui-section title="行业解决方案" isLine></fui-section>
    <view class="bussinessScopeInfo">
      <view class="bussinessScopItem" data-imgSource="https://static.hjzxh.com/hjzx/M00/0A/73/Ch62CmWDsdyAGdy-AARDAi7YKUQ674.png" data-navTitle="电商行业" bind:tap="naviTo">
        <view class="bussinessScopItem-font">
          <text>电商行业</text>
        </view>
      </view>
      <view class="bussinessScopItem" data-imgSource="https://static.hjzxh.com/hjzx/M00/0A/73/Ch62CmWDsgWAbOBXAAQZLpu9PJU527.png" data-navTitle="文娱传媒" bind:tap="naviTo">
        <view class="bussinessScopItem-font">
          <text>文娱传媒</text>
        </view>
      </view>
      <view class="bussinessScopItem" data-imgSource="https://static.hjzxh.com/hjzx/M00/0A/73/Ch62CmWDsfOASKLOAAcR7D81xcU029.png" data-navTitle="生活服务" bind:tap="naviTo">
        <view class="bussinessScopItem-font">
          <text>生活服务</text>
        </view>
      </view>
      <view class="bussinessScopItem" data-imgSource="https://static.hjzxh.com/hjzx/M00/0A/73/Ch62CmWDsb2ANoHKAAa6Twjzarw692.png" data-navTitle="教育培训" bind:tap="naviTo">
        <view class="bussinessScopItem-font">
          <text>教育培训</text>
        </view>
      </view>
    </view>
  </view>
</view>