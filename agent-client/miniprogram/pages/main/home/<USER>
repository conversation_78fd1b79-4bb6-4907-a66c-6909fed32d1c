// pages/main/home/<USER>
import { wechatLogin,getMiniUserByOpenIdAndAppId,getEcardByOpenId } from "../../../api/common"
Page({

  /**
   * 页面的初始数据
   */
  data: {
    avatarUrl:null,
    loginStatus:false,
    ecardName:null,
    ecardMobile:null,
    company:null,
    position:null,
    email:null,
    wechatId:null,
    address:null,
    shareOpenId:null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    let option=wx.getStorageSync(getApp().globalData.shareOpenIdKey)
    if(option && option.shareOpenId){
      this.setData({
        shareOpenId:option.shareOpenId,
        avatarUrl:option.avatarUrl,
      })
      this.getEcardByOpenId("share")
    }else{
      let sessionInfo = wx.getStorageSync(getApp().globalData.sessionStorageKey)
      if (sessionInfo) {
        this.getEcardByOpenId(null)
        this.setData({
          avatarUrl:wx.getStorageSync(getApp().globalData.userInfoStorageKey).avatar
        })

        getMiniUserByOpenIdAndAppId({
          openId:sessionInfo.openid
        }).then(resp=>{
          if(resp.data){
            wx.setStorageSync(getApp().globalData.userInfoStorageKey,resp.data)
            this.setData({
              avatarUrl:resp.data.avatar==null?this.data.avatarUrl:resp.data.avatar,
            })
          }
        })
      }
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
  this.setData({
    avatarUrl:null,
    loginStatus:false,
    ecardName:null,
    ecardMobile:null,
    company:null,
    position:null,
    email:null,
    wechatId:null,
    address:null
  })
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    console.log("佳友合伙人给您发来一张名片");
    return {
      title: '佳友合伙人给您发来一张名片',
      path: '/pages/main/home/<USER>'+wx.getStorageSync(getApp().globalData.sessionStorageKey).openid+"&avatarUrl="+this.data.avatarUrl
    }
  },

  creatEcard() {
    var app = getApp()
    let sessionInfo = wx.getStorageSync(app.globalData.sessionStorageKey)
    if (sessionInfo) {
      this.getEcardByOpenId("btnClick")
    } else {
      this.login()
    }
  },
  editEcard(){
    let param={
      ecardName:this.data.ecardName,
      ecardMobile:this.data.ecardMobile,
      company:this.data.company,
      position:this.data.position,
      email:this.data.email,
      wechatId:this.data.wechatId,
      address:this.data.address
    }
    wx.navigateTo({
      url:"../ecard/ecard?data="+JSON.stringify(param)
    })
  },
  callPhone(){
    if(this.data.ecardMobile){
      wx.makePhoneCall({
        'phoneNumber':this.data.ecardMobile
      })
    }else{
      wx.showToast({
        title:"无可拨打电话"
      })
    }

  },
  copyWechatId(){
    wx.setClipboardData({
      data: this.data.wechatId,
      success (res) {
        wx.showToast({
          title: "微信号码已复制"
        })
      }    
    })
  },
  copyEmail(){
    wx.setClipboardData({
      data: this.data.email,
      success (res) {
        wx.showToast({
          title: "邮箱号码已复制"
        })
      }  
    })
  },
  checkRegist(){
    
  },
  login(){
    wx.login({
      success: (res) => {
        console.log("登录成功")
        var code = res.code
        console.log(code)
        wechatLogin({code:code}).then(res=>{
          if(res.data){
            getMiniUserByOpenIdAndAppId({
              openId:res.data.openid
            }).then(resp=>{
              if(resp.data){
                wx.setStorageSync(getApp().globalData.sessionStorageKey,res.data)
                wx.setStorageSync(getApp().globalData.userInfoStorageKey,resp.data)
                this.setData({
                  avatarUrl:resp.data.avatar,
                  nickName:resp.data.nickname
                })
                this.getEcardByOpenId("btnClick")
              }else{
                wx.showModal({
                  title:"创建名片需要先完善个人信息？",
                  success:()=>{
                    wx.navigateTo({
                      url:'../profile/editmobile'
                    })
                  }
                })
              }
            })
          }else{
            wx.showToast({
              title:"登录失败",
              duration:2000
            })
          }

        })
      },
      fail: (res) => {
        console.log("登录失败")
        wx.showToast({
          title:"登录失败:"+res.errMsg,
          duration:2000
        })
      }
    })
  },
  naviTo(e){
    console.log(e)
    wx.navigateTo({
      url:'../../../subpackages/pages/artical/artical?imgSource='+e.currentTarget.dataset.imgsource+"&navTitle="+e.currentTarget.dataset.navtitle
    })
  },
  getEcardByOpenId(optype){
    let sessionInfo = wx.getStorageSync(getApp().globalData.sessionStorageKey)
    var openId = null;
    if(optype=="share"){
      openId=this.data.shareOpenId
    }else{
      openId=sessionInfo.openid
    }
    getEcardByOpenId({
      openId:openId
    }).then(res=>{
      console.log(res)
      if(res.data){
        this.setData({
          ecardName:res.data.ecardName,
          ecardMobile:res.data.ecardMobile,
          company:res.data.company,
          position:res.data.position,
          email:res.data.email,
          wechatId:res.data.wechatId,
          address:res.data.address,
          "loginStatus":true
        })
      }else{
        if(optype=="btnClick"){
          wx.navigateTo({
            url:"../ecard/ecard"
          })
        }
      }
    })
  },
  exchangeEcard(){
    wx.removeStorageSync(getApp().globalData.shareOpenIdKey)
    let sessionInfo = wx.getStorageSync(getApp().globalData.sessionStorageKey)
    if (sessionInfo) {
      this.getEcardByOpenId(null)
      this.setData({
        avatarUrl:wx.getStorageSync(getApp().globalData.userInfoStorageKey).avatar,
        shareOpenId:null
      })
    }else{
      this.setData({
        loginStatus:false,
        ecardName:null,
        shareOpenId:null
      })
      
    }
  }
})