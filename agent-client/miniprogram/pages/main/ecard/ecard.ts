import { modifyAgentEcard } from "../../../api/common"
Page({
    data: {
      ecardName:"",
      ecardMobile:"",
      company:"",
      position:"",
      email:"",
      wechatId:"",
      address:"",

      nameErrorMsg:'',
      mobileErrorMsg:'',
      emailErrorMsg:'',
      companyErrorMsg:'',
      positionErrorMsg:''
    },
    onLoad: function (option) {
        if(option.data){
          let data = JSON.parse(option.data)
          this.setData({
            ecardName:data.ecardName,
            ecardMobile:data.ecardMobile,
            company:data.company,
            position:data.position,
            email:data.email,
            wechatId:data.wechatId,
            address:data.address
          })
        }
    },

    fieldFocusAndBlur(e:any){
      const fieldname = e.currentTarget.dataset['fieldname']
      console.log(fieldname)
      if(e.type=='focus'){
        switch(fieldname){
          case 'ecardName': {
            this.setData({nameErrorMsg:''})
            break
          }
          case 'ecardMobile':{
            this.setData({mobileErrorMsg:''})
            break
          }
          case 'email':{
            this.setData({emailErrorMsg:''})
            break
          }
          case 'company':{
            this.setData({companyErrorMsg:''})
            break
          }
          case 'position':{
            this.setData({positionErrorMsg:''})
            break
          }

        }
      }else{

        switch(fieldname){
          case 'ecardName': {
            if(this.data.ecardName==''){
              this.setData({nameErrorMsg:'姓名不能为空'})
            }
            break
          }
          case 'ecardMobile':{
            if(this.data.ecardMobile==''){
              this.setData({mobileErrorMsg:'手机号不能为空'})
            }
            break
          }
          case 'email':{
            if(this.data.email==''){
              this.setData({emailErrorMsg:'邮箱不能为空'})
            }
            break
          }
          case 'company':{
            if(this.data.company==''){
              this.setData({companyErrorMsg:'公司不能为空'})
            }
            break
          }
          case 'position':{
            if(this.data.position==''){
              this.setData({positionErrorMsg:'职位不能为空'})
            }
            break
          }
          default:{
            break
          }

        }
      }



    },
    submit(){

      if(this.data.ecardName==''
      || this.data.ecardMobile==''
      || this.data.company==''
      || this.data.position==''
      || this.data.email==''
      ){
        const fieldNames = ['ecardName','ecardMobile','company','position','email']
        fieldNames.forEach(fieldName=>{
          this.fieldFocusAndBlur({
            type:'blur',
            currentTarget:{
              dataset:{fieldname:fieldName}
            }
          })
        })
        return
      }

      const param = {
        ecardName:this.data.ecardName,
        ecardMobile:this.data.ecardMobile,
        company:this.data.company,
        position:this.data.position,
        email:this.data.email,
        wechatId:this.data.wechatId,
        address:this.data.address,
        openId:wx.getStorageSync(getApp().globalData.sessionStorageKey).openid
      }
      modifyAgentEcard(param).then(res=>{
        if(res.code == 20000){
          wx.navigateBack()
        }
      })
    }
});