<view  class="container">

        <van-cell-group inset border>
                <van-field required
                           size="large"
                           title-width="200rpx"
                           clearable
                           maxlength="30"
                           autosize
                        label="姓名"
                        label-class="formField"
                        input-class="formField"
                        model:value="{{ ecardName }}"
                        error-message="{{nameErrorMsg}}"
                        data-fieldName="ecardName"
                        bind:focus="fieldFocusAndBlur"
                        bind:blur='fieldFocusAndBlur'
                        placeholder="请输入"
                        border="{{ true }}"
                />
                <van-field
                        required
                        clearable
                        maxlength="11"
                        size="large"
                        title-width="200rpx"
                        type="number"
                        label="手机号"
                        label-class="formField"
                        input-class="formField"
                        error-message="{{mobileErrorMsg}}"
                        data-fieldName="ecardMobile"
                        bind:focus="fieldFocusAndBlur"
                        bind:blur='fieldFocusAndBlur'
                        model:value="{{ ecardMobile }}"
                        placeholder="请输入"
                        border="{{ true }}"
                />

                <van-field
                        required
                        clearable
                        maxlength="100"
                        size="large"
                        title-width="200rpx"
                        label="所在公司"
                        label-class="formField"
                        input-class="formField"
                        model:value="{{ company }}"
                        error-message="{{companyErrorMsg}}"
                        data-fieldName="company"
                        bind:focus="fieldFocusAndBlur"
                        bind:blur='fieldFocusAndBlur'
                        placeholder="请输入"
                        border="{{ true }}"
                />

                <van-field
                        required
                        clearable
                        maxlength="100"
                        size="large"
                        title-width="200rpx"
                        label="公司职位"
                        label-class="formField"
                        input-class="formField"
                        model:value="{{ position }}"
                        error-message="{{positionErrorMsg}}"
                        data-fieldName="position"
                        bind:focus="fieldFocusAndBlur"
                        bind:blur='fieldFocusAndBlur'
                        placeholder="请输入"
                        border="{{ true }}"
                />

                <van-field
                        required
                        clearable
                        maxlength="50"
                        size="large"
                        title-width="200rpx"
                        label="邮箱"
                        label-class="formField"
                        input-class="formField"
                        model:value="{{ email }}"
                        error-message="{{emailErrorMsg}}"
                        data-fieldName="email"
                        bind:focus="fieldFocusAndBlur"
                        bind:blur='fieldFocusAndBlur'
                        placeholder="请输入"
                        border="{{ true }}"
                />

                <van-field
                        clearable
                        maxlength="50"
                        size="large"
                        title-width="200rpx"
                        label="微信号"
                        label-class="formField"
                        input-class="formField"
                        model:value="{{ wechatId }}"
                        data-fieldName="wechatId"
                        placeholder="请输入"
                        border="{{ true }}"
                />

                <van-field
                        clearable
                        maxlength="50"
                        size="large"
                        title-width="200rpx"
                        label="地址"
                        label-class="formField"
                        input-class="formField"
                        model:value="{{ address }}"
                        data-fieldName="address"
                        placeholder="请输入"
                        border="{{ true }}"
                />

                <van-button  type="info" custom-style="width:100%;margin-top:40rpx;font-size:40rpx" bind:tap="submit">保存名片</van-button>
        </van-cell-group>
        
</view>