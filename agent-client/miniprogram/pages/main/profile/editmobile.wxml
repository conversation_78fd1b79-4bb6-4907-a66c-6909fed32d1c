<!--pages/login/register.wxml-->
<!-- <text style="font-size: 26rpx; color: red;margin-left: 40rpx;">如果您是智享汇合伙人，请绑定合伙人操作员手机号
</text> -->
<mp-form
  id="form"
>
  <mp-cells>
    <mp-cell
      show-error
      title="昵称"
    >
      <input
        type="nickname"
        placeholder="请选择微信昵称或者自定义昵称"
        value="{{ nickname }}"
        data-field="nickname"
        bindblur="namereview"
        bindinput="onFieldInput"
      />
    </mp-cell>
    <mp-cell
      show-error
      title="手机号"
    >
      <input
        placeholder="请填写手机号码"
        type="number"
        value="{{ mobile }}"
        data-field="mobile"
        bindinput="onFieldInput"
      />
    </mp-cell>
    <mp-cell
      title="验证码"
      show-error
    >
      <view style="display: flex;">
        <input
          style="flex: 1 1 auto;"
          placeholder="请填写短信验证码"
          type="number"
          model:value="{{smsCode}}"
          data-field="smsCode"
        bindinput="onFieldInput"
        />
        <view
          class="func-text sms-btn {{ disabled ? 'disabled' : ''}}"
          bind:tap="sendCode"
        >{{msgText}}</view>
      </view>
    </mp-cell>
  </mp-cells>
</mp-form>

<view class="register-btn-container">
  <button
    class="register-btn"
    open-type="chooseAvatar"
    bind:chooseavatar="onChooseAvatar"
    type="primary"
  >提交</button>
</view>