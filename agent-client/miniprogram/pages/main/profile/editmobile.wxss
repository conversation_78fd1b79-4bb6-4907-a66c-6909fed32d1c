/* pages/home/<USER>/
.register-page {
  margin: 30rpx;
}
.register-box {
  margin: 40rpx 0;
  padding: 60rpx 0;
  background: #fff;
}
.register-header {
  border-left: 4rpx solid var(--main-purple);
  padding-left: 40rpx;
}
.register-content {
  margin-top: 40rpx;
  padding: 0 44rpx;
}

.register-url-content {
  display: flex;
  align-items: center;
}
.register-url {
  flex: 1 0;
  padding: 10rpx 20rpx;
  background: var(--main-gray);
  border-radius: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.register-copy-btn {
  padding: 0 40rpx;
  color: var(--main-blue);
  text-align: center;
}
.register-qrcode-content {
  width: 300rpx;
  height: 300rpx;
  margin: 40rpx auto;
}


.register-url-btn {
  margin: 0 30rpx;
  padding: 40rpx 0;
  text-align: center;
  background: var(--main-purple);
  color: #fff;
}
.register-btn{
  align-self: center;
  width: 90%;
}
.register-btn-container{
  margin-top: 20rpx;
  display: flex;
}