// pages/main/profile/editmobile.ts
import { getPhoneByOpenId,sendSmsCode,bindMobile,getMiniUserByOpenIdAndAppId} from "../../../api/common"
import { baseUrl,fileUrl } from '../../../setting/index'
let  timer:any
Page({

  /**
   * 页面的初始数据
   */
  data: {
    nickname:null,
    mobile:null,
    smsCode:null,
    disabled: false,
    msgText: '获取验证码',
    avatarUrl:null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    if(this.data.mobile==null){
      this.getPhoneByOpenId()
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  onChooseAvatar(e){
    console.log(e)
    const { avatarUrl } = e.detail 
    wx.uploadFile({
      url:baseUrl+"/wxmini/biz/upload",
      filePath:avatarUrl,
      name:"file",
      success:(result)=>{
        if(JSON.parse(result.data).code ==20000 &&
        JSON.parse(result.data).data){
          this.setData(
            {avatarUrl: fileUrl+JSON.parse(result.data).data}
          )
          this.onSubmit()
        }
      }
    })
  },
  getPhoneByOpenId(){
    var that=this
    getMiniUserByOpenIdAndAppId({
      openId:wx.getStorageSync(getApp().globalData.sessionStorageKey).openid
    }).then(res=>{
      if(res.data){
        that.setData({
          nickname:res.data.nickname,
          avatarUrl:res.data.avatar
        })
        getPhoneByOpenId({
          openId:wx.getStorageSync(getApp().globalData.sessionStorageKey).openid
        }).then(res=>{
          console.log(res.data);
          that.setData({
            mobile:res.data
          })
        })
      }
    })
  },

  async sendCode() {
    if (this.data.disabled) {
      return
    }
    console.log(this.data.mobile)
    if (this.data.mobile==null || this.data.mobile == '') {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }
    this.setData({
      disabled: true
    })
    await sendSmsCode({ phone: this.data.mobile, type: 2 })
    this.changeCount(60)

  },
  changeCount(second: number) {
    if (second > 0) {
      this.setData({
        count: second,
        msgText: `${second}秒后重新获取`
      })
      timer = setTimeout(() => {
        this.changeCount(second - 1)
      }, 1000);
    } else {
      this.resetTimer()
      this.setData({
        msgText: '重新获取',
        disabled: false,
      })
    }
  },
  resetTimer() {
    timer && clearTimeout(timer) && (timer = null)
  },
  async onSubmit() {
    console.log("提交"+this.data.nickname)
    if(this.data.nickname == null ||this.data.mobile==null 
      || this.data.smsCode==null || this.data.avatarUrl == null){
      wx.showToast({
        title:"请填写信息"
      })
      return
    }
    bindMobile({
      openId: wx.getStorageSync(getApp().globalData.sessionStorageKey).openid,
      mobile: this.data.mobile,
      nickName: this.data.nickname,
      smsCode: this.data.smsCode,
      avatarUrl: this.data.avatarUrl
    }).then(res=>{
      getMiniUserByOpenIdAndAppId({
        openId:wx.getStorageSync(getApp().globalData.sessionStorageKey).openid
      }).then(resp=>{
        if(resp.data){
          wx.setStorageSync(getApp().globalData.userInfoStorageKey,resp.data)
          this.setData({
            avatarUrl:resp.data.avatar,
            nickName:resp.data.nickname
          })
        }
      })
      wx.navigateBack()
    })
    
  },
  onFieldInput(e: WechatMiniprogram.Input) {
    const { field } = e.target.dataset
    const value = e.detail.value
    this.setData({
      [`${field}`]: value
    })
  },
  namereview(e){
    if(e.detail.value){
      this.setData({
        nickname:e.detail.value
      })
      
    }
  }
})