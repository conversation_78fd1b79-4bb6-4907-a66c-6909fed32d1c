import { get, post } from "../utils/request"

export const selectAgent = (data: any) => {
  return post('/wx/user/selectAgent', data)
}

export const queryProfit = (data: any) => {
  return get('/wx/query/profit', data)
}

export const queryRelation = (data: any) => {
  return post('/wx/query/merchantRelation', data)
}

export const listAgent = (data: any) =>{ 
  return post('/wx/query/listAgentPage', data)
}

export const queryMonthBillList = () => {
  return get('/wx/query/monthBillList')
}

export const createUrl = () => {
  return get('/wx/merchant/generateCreateURL')
}