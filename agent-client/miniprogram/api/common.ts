import { get, post } from "../utils/request"

export const getUserKey = (data: object) => {
  return get('/wx/user/getUserPublickey', data)
}

export const userLogin = (data: object) => {
  return post('/wx/user/login', data)
}

export const sendSmsCode = (data: any) => {
  return post('/wx/user/sendSmsCode', data)
}

export const userLogout = () => {
  return post('/wx/user/loginOut', {})
}

export const userRegister = (data: any) => {
  return post('/wx/user/register', data)
}

export const wechatLogin = (data: any) => {
  return post('/wx/user/wechatLogin', data)
}

export const getWxUserInfo = (data: any) => {
  return post('/wx/user/getWxUserInfo', data)
}

export const getEcardByOpenId = (data: any) => {
  return get('/wxmini/biz/getEcardByOpenId', data)
}

export const modifyAgentEcard = (data: any) => {
  return post('/wxmini/biz/modifyAgentEcard', data)
}

export const loginAgentSystem = (data: any) => {
  return post('/wxmini/biz/loginAgentSystem', data)
}
export const getPhoneByOpenId = (data: any) => {
  return post('/wxmini/biz/getPhoneByOpenId', data)
}
export const bindMobile = (data: any) => {
  return post('/wxmini/biz/bindMobile', data)
}

export const getMiniUserByOpenIdAndAppId = (data: any) => {
  return post('/wxmini/biz/getMiniUserByOpenIdAndAppId', data)
}
