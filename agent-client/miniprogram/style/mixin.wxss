.text-center {
  text-align: center;
}
.normal-btn {
  width: 100% !important;
  background: transparent !important;
  font-weight: normal;
  padding: 0;
  color: currentColor !important;
}
.box-title {
  position: relative;
}
.box-title::before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  transform: translateX(-400%);
  height: 100%;
  width: 8rpx;
  background: var(--main-blue);
}