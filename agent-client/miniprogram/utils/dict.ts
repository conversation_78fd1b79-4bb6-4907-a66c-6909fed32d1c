
interface DictData {
  [key: string]: any
}

interface Dict {
  [key: string]: DictData
}

const AgentStatusEnum: DictData = {
  100: '已激活',
  101: '已冻结',
  102: '已创建',
  103: '已清退',
}

const AuthStatusEnum: DictData = {
  100: '已认证',
  101: '认证失败',
  102: '未认证',
  103: '认证审核中',
  104: '未成交',
}

const RelationTypeEnum: DictData = {
  100: '全部',
  101: '直接',
  102: '间接',
}

const MchStatusEnum: DictData = {
  100: '已激活',
  101: '已冻结',
  102: '已创建',
}

const AgentTypeEnum: DictData= {
  100: '个人',
  101: '公司',
}

export const DictData: Dict = {
  AgentStatusEnum,
  AuthStatusEnum,
  RelationTypeEnum,
  MchStatusEnum,
  AgentTypeEnum,
}

export function getDict(dict: string): DictData {
  return DictData[dict]
}