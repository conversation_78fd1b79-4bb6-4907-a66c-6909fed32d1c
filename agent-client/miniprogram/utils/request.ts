import { getToken } from "./token"
import { baseUrl } from '../setting/index'

type RequestMethod = 'POST' | 'GET'
type ResponseType = {
  code: number
  message?: string
  data?: any
}

let token = ''

function getRequestToken() {
  if (token) return token
  return getToken()
}

function successHandler(resolve: any, reject: any) {
  return function (response: WechatMiniprogram.RequestSuccessCallbackResult<ResponseType>) {
    const { data: rawData, statusCode } = response
    if (statusCode !== 200) {
      wx.showToast({
        title: '网络请求出错',
        icon: 'none'
      })
      return reject()
    }
    const { code, message } = rawData
    if (code === 20000) {
      resolve(rawData)
    }else if(code === 20003){
      wx.showModal({
        title: "会话超时，请重新登录",
        icon: 'error',
        success:()=>{
          reject(rawData)
        }
      })
    } else {
      wx.showModal({
        title: message || '网络错误',
        icon: 'error',
      })
      reject(rawData || '网络错误')
    }
  }
}

export function httpRequest(url: string, method: RequestMethod, data?: any): Promise<ResponseType> {
  return new Promise((resolve, reject) => {
    const requestOption: WechatMiniprogram.RequestOption = {
      url: baseUrl + url,
      method,
      data,
      success: successHandler(resolve, reject) as WechatMiniprogram.RequestSuccessCallback,
      fail: reject,
      header: {}
    }
    if (method === 'GET') {
      requestOption.header!['content-type'] = 'application/x-www-form-urlencoded'
    }
    if (getRequestToken()) {
      requestOption.header!['x-token'] = getRequestToken()
    }

    wx.request(requestOption)
  })
}


export function get(url: string, data?: any) {
  return httpRequest(url, 'GET', data)
}

export function post(url: string, data?: any) {
  return httpRequest(url, 'POST', data)
}