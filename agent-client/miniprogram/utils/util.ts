export const formatTime = (date: Date) => {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return (
    [year, month, day].map(formatNumber).join('/') +
    ' ' +
    [hour, minute, second].map(formatNumber).join(':')
  )
}

const formatNumber = (n: number) => {
  const s = n.toString()
  return s[1] ? s : '0' + s
}

export function buildParams(params: Record<string, any>) {
  let res: string|string[] = []
  Object.keys(params).forEach(item => {
    (res as string[]).push(`${item}=${params[item]}`)
  })
  res = res.join('&')
  return res ? '?' + res : ''
}

/**
 * 窗口等分750份
 * 比例 (px/rpx)
 */
export function getPixelRate(): number {
  return wx.getSystemInfoSync().windowWidth / 750
}


/**
 * 通过rpx计算px
 * @param source rpx
 */
export function getPx(source: number): number {
  return source * getPixelRate()
}

/**
 * px => rpx
 * @param source px
 */
export function getRpx(source: number): number {
  return source / getPixelRate()
}

/**
 * 格式化金额
 * @param n 金额字符串
 */
export function formatMoney(n: number|string) {
  var s  = n.toString();
  var reg = /(\d)(?=(?:(\d{3})+(\.\d{2})?$))/g
  var formatS = s.replace(reg, '$1,');
  return '¥' + formatS
}
