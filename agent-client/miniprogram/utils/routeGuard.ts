import { getDict, DictData } from "./dict"

const grayBackPagesReg = /(account\/home)|(home\/(?=relation|agent|report|register)|(login\/(?=register|scan)))/g

type PageParams = WechatMiniprogram.Page.Options<Record<string, any>, Record<string, any>>
type AddonParams = {
  [P in keyof PageParams]?: PageParams[P]
}

export function routeGruad(Page: WechatMiniprogram.Page.Constructor) {
  return function WrappedPage(...options: PageParams[]) {
    const rawOptions = options[0]
    const addonOption: AddonParams = {
      async onLoad(query: any) {
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1].route
        // 背景变色
        if (grayBackPagesReg.test(currentPage)) {
          wx.setPageStyle({
            style: {
              background: '#F5F5F9'
            }
          })
        } else {
          wx.setPageStyle({
            style: {
              background: '#ffffff'
            }
          })
        }
        // if (currentPage === 'pages/login/index' && getToken()) {
        //   // TODO 登录状态保持
        //   wx.redirectTo({
        //     url: '/pages/account/select'
        //   })
        //   return
        // }

        this.setData({
          DictData
        })

        // 原 onload
        rawOptions.onLoad?.call(this, query)
      },
      getDict(dictName: string, code: string) {
        const dict = getDict(dictName)
        code += ''
        return dict[code]
      },
      // onShareAppMessage() {
      //   return {
      //     title: '智享汇合伙人',
      //     imageUrl: '/assets/img/share.jpg'
      //   }
      // },
      onShareTimeline() {
        return {
          title: '智享汇合伙人',
          imageUrl: '/assets/img/share.jpg'
        }
      }
    }
    const newOptions = Object.assign({}, rawOptions, addonOption)
    return Page(newOptions)
  }
}