Component({
    properties: {
        agentDataList: {
            type: Array,
            value: []
        },
    },
    data: {
        // "agentDataList": [{"billDate":"2023-10","settlementAmount":-0.01,"agentMonthProfit":-0.01},{"billDate":"2023-09","settlementAmount":-0.02,"agentMonthProfit":-0.02},{"billDate":"2023-08","settlementAmount":-0.27,"agentMonthProfit":-0.27},{"billDate":"2023-07","settlementAmount":0.00,"agentMonthProfit":0.00},{"billDate":"2023-06","settlementAmount":0.00,"agentMonthProfit":0.00},{"billDate":"2023-05","settlementAmount":0.00,"agentMonthProfit":0.00},{"billDate":"2023-04","settlementAmount":-0.01,"agentMonthProfit":-0.01},{"billDate":"2023-03","settlementAmount":-0.01,"agentMonthProfit":-0.01},{"billDate":"2023-02","settlementAmount":-0.01,"agentMonthProfit":-0.01},{"billDate":"2023-01","settlementAmount":0.02,"agentMonthProfit":0.02},{"billDate":"2022-12","settlementAmount":0.00,"agentMonthProfit":0.00},{"billDate":"2022-11","settlementAmount":-0.03,"agentMonthProfit":-0.03}]
    },
    methods: {
        onClick(e) {
            console.log(e)
            this.triggerEvent('click', e.currentTarget.dataset.row);
        },
    }
});
