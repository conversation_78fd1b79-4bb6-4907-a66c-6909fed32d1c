<view class="table-container">
    <view class="tr bg-w">
        <view class="th">账单月份</view>
        <view class="th">交易分润</view>
        <view class="th ">结算金额</view>
        <view class="th ">操作</view>
    </view>

    <view wx:if="{{ agentDataList.length === 0 }}">
        <view class="no-data">暂无数据~</view>
    </view>
    <view wx:else>
            <view class="tr"  wx:for="{{agentDataList}}"    wx:key="index">
                <view class="td">{{item.billDate}}</view>
                <view class="td">{{item.agentMonthProfit}}</view>
                <view class="td">{{item.settlementAmount}}</view>
                <view class="td"
                      data-row="{{item}}"
                      data-item="{{item}}"
                      bindtap="onClick">
                    <van-icon
                        name="arrow"
                        class="van-cell__right-icon-wrap right-icon-class"
                        custom-class="van-cell__right-icon"
                    />
                </view>
            </view>
    </view>

</view>