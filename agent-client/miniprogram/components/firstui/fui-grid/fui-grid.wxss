.fui-grid__wrap {
  width: 100%;
  display: flex;
  box-sizing: border-box;
  flex: 1;
  flex-direction: column;
  transition-property: opacity;
  transition-duration: .2s;
  transition-timing-function: ease-in-out;
}
.fui-grid__wrap-hidden {
  opacity: 0;
}

.fui-grid__wrap-show {
  opacity: 1;
}

.fui-grid {
  display: flex;
  width: 100%;
  flex-direction: row;
  flex-wrap: wrap;
}

.fui-grid__border {
  position: relative;
  z-index: 1;
  border-left: 0;
}

.fui-grid__border::before {
  content: ' ';
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 1px;
  border-top: 1px solid var(--fui-color-border, #EEEEEE);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  z-index: 1;
}

.fui-grid__border::after {
  content: ' ';
  position: absolute;
  left: 0;
  top: 0;
  width: 1px;
  height: 100%;
  border-left: 1px solid var(--fui-color-border, #EEEEEE);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
}