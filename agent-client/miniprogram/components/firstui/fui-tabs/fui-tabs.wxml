<scroll-view class="fui-tabs__scrollbox {{isFixed && !isSticky?'fui-tabs__fixed':''}} {{isSticky?'fui-tabs__sticky':''}}" scroll-with-animation scroll-x="{{scroll}}" scroll-into-view="{{scrollInto}}" style="background:{{background}};z-index:{{isFixed || isSticky?zIndex:1}};top:{{isFixed || isSticky ? top + 'px' : 'auto'}}">
  <view class="fui-scroll__view {{alignLeft?'':'fui-tabs__full'}}">
    <view wx:for="{{vals}}" wx:for-item="tab" wx:key="index" class="fui-tabs__item {{alignLeft?'':'fui-tabs__full'}}" id="{{tab.fui_s_id}}" bindtap="switchTab" data-index="{{index}}">
      <view class="fui-tabs__text-wrap {{tab.disabled?'fui-tabs__wrap-disabled':''}} {{direction==='column' && tab.icon?'fui-tabs__item-column':''}}" style="height:{{height}}rpx">
        <view class="fui-tabs__line-wrap {{center?'fui-tabs__line-center':''}}"	style="bottom:{{bottom}}rpx;left:-{{padding}}rpx;right:-{{padding}}rpx" wx:if="{{isSlider}}">
						<view class="fui-tabs__ac-line {{short?'fui-tabs__line-short':'fui-tabs__full'}} {{!sliderBackground?'fui-tabs__slider-color':''}}" style="height:{{sliderHeight}}rpx;background:{{sliderBackground}};border-radius:{{sliderRadius==-1?sliderHeight:sliderRadius}}rpx;transform:scale({{tabIndex===index?scale:0}})">
						</view>
					</view>
        <!-- <view class="fui-tabs__ac-line {{short?'fui-tabs__line-short':''}} {{sliderBackground?'':'fui-tabs__slider-color'}}" style="height:{{sliderHeight}}rpx;background:{{sliderBackground}};border-radius:{{sliderRadius==-1?sliderHeight+'rpx':sliderRadius+'rpx'}};bottom:{{bottom}}rpx;left:-{{padding}}rpx;right:-{{padding}}rpx;transform: scale({{tabIndex===index?scale:0}})" wx:if="{{isSlider}}">
        </view> -->
        <image class="fui-tabs__icon {{direction==='column'?'fui-tabs__icon-column':''}}" src="{{tabIndex===index && tab.selectedIcon?tab.selectedIcon:tab.icon}}" wx:if="{{tab.icon}}">
        </image>
        <text class="fui-tabs__text {{!selectedColor && tabIndex===index?'fui-tabs__selected-color':''}} {{!color && tabIndex!==index?'fui-tabs__text-color':''}}" style="font-size:{{size}}rpx;color:{{tabIndex===index?selectedColor:color}};font-weight:{{tabIndex===index?selectedFontWeight:fontWeight}};transform:scale({{tabIndex===index?scale:1}})">{{tab.name}}<text class="{{badgeBackground?'':'fui-tabs__badge-color'}} {{isDot?'fui-tabs__badge-dot':'fui-tabs__badge'}}" style="color:{{badgeColor}};background:{{badgeBackground}}" wx:if="{{tab.badge}}">{{isDot?'':tab.badge}}</text></text>
      </view>
    </view>
  </view>
</scroll-view>