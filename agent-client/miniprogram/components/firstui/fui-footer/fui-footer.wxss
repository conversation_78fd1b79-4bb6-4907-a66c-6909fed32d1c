.fui-footer {
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  word-break: break-all;
  overflow: hidden;
  padding-top: 32rpx;
  padding-bottom: 32rpx;
  padding-left: 32rpx;
  padding-right: 32rpx;
}

.fui-footer__fixed-bottom {
  position: fixed;
  z-index: 99;
  bottom: 0;
  left: 0;
  right: 0;
  left: constant(safe-area-inset-left);
  left: env(safe-area-inset-left);
  right: constant(safe-area-inset-right);
  right: env(safe-area-inset-right)
}

.fui-footer__link {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
}

.fui-link__color {
  color: var(--fui-color-link, #465CFF) !important;
}

.fui-link__item {
  position: relative;
  line-height: 1;
}


.fui-link__text {
  padding: 0 18rpx;
  font-weight: 400;
}

.fui-link__text-border {
  border-right-width: 0;
}

.fui-link__item::before {
  content: " ";
  position: absolute;
  right: 0;
  top: 4rpx;
  width: 1px;
  bottom: 4rpx;
  border-right: 1px solid var(--fui-color-label, #B2B2B2);
  -webkit-transform-origin: 100% 0;
  transform-origin: 100% 0;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
}

.fui-link__item:last-child::before {
  border-right: 0 !important
}


.fui-link-hover {
  opacity: 0.5
}

.fui-footer__text {
  flex: 1;
  line-height: 1;
  text-align: center;
  padding-top: 8rpx;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  font-weight: 400;
}