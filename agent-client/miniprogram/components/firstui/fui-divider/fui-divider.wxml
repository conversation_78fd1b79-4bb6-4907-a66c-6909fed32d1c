<view class="fui-divider__wrap" style="height:{{height}}rpx">
  <view class="fui-divider__line" style="width:{{width}};background: {{dividerColor}};top:{{parse.getTop(height)}}">
  </view>
  <view class="fui-divider__text-box" style="background-color:{{backgroundColor}}">
    <slot></slot>
    <text class="fui-divider__text" style="font-weight: {{fontWeight}};color: {{color}};font-size:{{size}}rpx;line-height:{{size}} rpx" wx:if="{{text}}">{{text}}</text>
  </view>
</view>

<wxs module="parse">
  module.exports = {
    getTop: function (height) {
      return parseInt(height) / 2 + 'rpx'
    }
  }
</wxs>