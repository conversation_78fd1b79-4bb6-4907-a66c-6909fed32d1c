.fui-textarea__wrap {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  flex: 1;
  align-items: center;
  position: relative;
}

.fui-textarea__flex-start {
  align-items: flex-start !important;
}

.fui-textarea__border-top {
  position: absolute;
  top: 0;
  height: 1px;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  transform-origin: 0 0;
  z-index: 1;
}

.fui-textarea__border-bottom {
  position: absolute;
  bottom: 0;
  height: 1px;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  transform-origin: 0 100%;
  z-index: 1;
}


.fui-textarea__required {
  position: absolute;
  left: 12rpx;
  height: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  line-height: 1.15;
}

.fui-required__flex-start {
  transform: translateY(0);
}

.fui-textarea__label {
  padding-right: 12rpx;
  flex-shrink: 0;
}

.fui-textarea__self {
  flex: 1;
  width: 100%;
  overflow: visible;
  box-sizing: border-box;
}

.fui-textarea-placeholder {
  color: var(--fui-color-minor, #ccc);
  overflow: visible;
}

.fui-placeholder {
  color: var(--fui-color-minor, #ccc);
  overflow: visible;
}

.fui-textarea__border {
  border-radius: 4rpx;
  position: relative;
  position: absolute;
  height: 200%;
  width: 200%;
  border: 1px solid var(--fui-color-border, #EEEEEE);
  transform-origin: 0 0;
  transform: scale(0.5);
  left: 0;
  top: 0;
  border-radius: 8rpx;
  pointer-events: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

.fui-textarea__bordercolor{
  border-color:var(--fui-color-border, #EEEEEE) !important;
}

.fui-textarea__background{
  background: var(--fui-color-border, #EEEEEE) !important;
}


.fui-textarea__flex-1 {
  flex: 1;
}

.fui-textarea__counter {
  padding-top: 8rpx;
  text-align: right;
}
.fui-text__right{
  text-align: right;
}
.fui-textarea__disabled-styl {
  opacity: .6;
}