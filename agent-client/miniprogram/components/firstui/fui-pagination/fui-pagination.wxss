.fui-pagination__wrap {
  flex: 1;
  width: 100%;
  display: flex;
  position: relative;
  overflow: hidden;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.fui-pagination__btn {
  display: flex;
  box-sizing: border-box;
  position: relative;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  text-align: center;
  border-width: 1rpx;
  border-style: solid;
  flex-shrink: 0;
}

.fui-pagination__btn-ac:active {
  opacity: 0.5;
}

.fui-pagination__num {
  display: flex;
  flex: 1;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.fui-pagination__active-color {
  color: var(--fui-color-primary, #465CFF) !important;
}
.fui-pagination__bg {
  background: var(--fui-color-primary, #465CFF) !important;
}
.fui-pagination__color{
  color: var(--fui-color-section, #333) !important;
}

.fui-pagination__disabled {
  opacity: 0.3;
}
.fui-page__number {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

.fui-page__num-item {
  min-width: 60rpx;
  display: flex;
  transform: translateZ(0);
  height: 60rpx;
  align-items: center;
  justify-content: center;
  margin: 0 4rpx;
}

.fui-page__num-item:active {
  opacity: .5;
}

.fui-page__num-text {
  font-size: 24rpx;
}