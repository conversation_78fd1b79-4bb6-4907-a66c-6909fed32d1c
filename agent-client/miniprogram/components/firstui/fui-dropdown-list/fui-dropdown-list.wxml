<view class="fui-dropdown__list-wrap {{isShow?'fui-dropdown__list-show':''}}" style="background:{{maskBackground}};z-index:{{zIndex}}" bindtap="close">
  <view class="fui-dropdown__list" style="border-radius:{{radius}}rpx;background:{{background}}">
    <scroll-view class="fui-ddl__scroll" scroll-y style="{{parse.getStyles(height, width)}}">
      <view class="fui-dropdown__list-item {{isReverse?'fui-ddl__reverse':''}} {{splitLine && itemList.length-1!==index?'fui-ddl__item-line':''}}" style="background:{{background}};padding:{{padding}};" wx:for="{{itemList}}" wx:for-item="model" wx:key="index" catchtap="itemClick" data-index="{{index}}">
        <view class="fui-ddl__checkbox {{isCheckMark?'fui-is__checkmark':''}} {{(!checkboxColor || checkboxColor=='true') && model.checked && !isCheckMark?'fui-ddl__checkbox-color':''}}" style="background:{{model.checked && !isCheckMark ?checkboxColor:'transparent'}};border-color:{{model.checked && !isCheckMark ?checkboxColor:borderColor}}" wx:if="{{isCheckbox}}">
          <view class="fui-ddl__checkmark" style="border-bottom-color:{{checkmarkColor}};border-right-color:{{checkmarkColor}}" wx:if="{{model.checked}}"></view>
        </view>
        <view class="fui-ddl__flex">
          <view class="fui-ddl__icon-box {{!isReverse && isCheckbox?'fui-ddl__icon-ml':''}} {{isReverse?'fui-ddl__icon-mr':''}}" style="width:{{iconWidth}}rpx;height:{{iconWidth}}rpx" wx:if="{{model.src}}">
            <image src="{{model.src}}" style="width:{{iconWidth}}rpx;height:{{iconWidth}}rpx"></image>
          </view>
          <text class="fui-ddl__item-text {{!isReverse && (isCheckbox || model.src)?'fui-ddl__text-pl':''}} {{isReverse && (isCheckbox || model.src)?'fui-ddl__text-pr':''}}" style="font-size:{{size}}rpx;color:{{selectedColor && model.checked?selectedColor:color}}">{{model.text}}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</view>

<wxs module="parse">
  module.exports = {
    getStyles: function (height, width) {
      var styles = '';
      var height = parseInt(height || 0)
      var width = parseInt(width || 0)
      if (height > 0) {
        styles += "height:" + height + "rpx;"
      }
      if (width > 0) {
        styles += "width:" + width + "rpx;"
      }
      return styles
    }
  }
</wxs>