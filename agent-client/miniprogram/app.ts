import { routeGruad } from "./utils/routeGuard"

// app.ts
Page = routeGruad(Page)

App<IAppOption>({
  globalData: {
    accountInfo: {},
    agentInfo: {},
    sessionStorageKey:"agent_session",
    userInfoStorageKey:"user_info",
    shareOpenIdKey:"share_by_key"
  },
  onLaunch(option) {
    // 登录
    console.log(option);

    if(option && option.query.openId){
      wx.setStorageSync("share_by_key",{
        shareOpenId:option.query.openId,
        avatarUrl:option.query.avatarUrl
      })
    }
  },
})