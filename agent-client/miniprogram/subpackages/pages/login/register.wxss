/* pages/login/register.wxss */
.cell-title {
  background: transparent;
  font-size: 30rpx;
  margin: 20rpx 0 20rpx 30rpx;
}

.top-align-cell {
  align-items: flex-start;
}

.sms-btn {
  flex-shrink: 0;
}

.checkbox-container {
  margin: 60rpx 30rpx;
}

.register-btn-container {
  margin: 100rpx 60rpx;
}
.register-btn {
  background: var(--main-blue);
  color: #fff;
  padding: 30rpx 0;
  text-align: center;
  border-radius: 80rpx;
}