<!--pages/login/index.wxml-->
<view class="login-page-container">
  <view class="login-page-header">
    <view class="login-page-title">
      欢迎登录
    </view>
    <view class="login-page-tip">智享汇合伙人系统</view>
  </view>

  <form class="login-form-container">
    <view class="login-form-input">
      <input
        placeholder="请输入注册时填写的手机号"
        bindinput="onInput"
        data-field="phone"
      />
    </view>
    <view class="login-form-input" wx:if="{{loginType == 0}}">
      <input
        placeholder="请输入密码"
        password
        data-field="pwd"
        bindinput="onInput"
      />
    </view>
    <view class="login-form-input" wx:else>
      <input
        placeholder="请输入验证码"
        data-field="smsCode"
        bindinput="onInput"
      />
      <view
        class="login-form-codebtn {{ disabled ? 'disabled' : ''}}"
        bind:tap="onCodeClick"
      >{{ msgText }}</view>
    </view>
    <view class="login-type-tip" bind:tap="changeLoginType">
      {{ loginType === 0 ? '验证码' : '密码'}}登录
    </view>
    <checkbox-group bindchange="onCheckChange">
      <label class="checkbox">
        <checkbox checked="{{checked}}" class="login-checkbox" />
        <view class="login-file-text">
          我已阅读并同意<text
            class="login-file"
            catch:tap="goFile"
            data-url="../../pages/file/privacy"
          >《智享汇隐私政策》、</text><text
            class="login-file"
            catch:tap="goFile"
            data-url="../../pages/file/service"
          >《智享汇平台服务协议》</text>
        </view>
      </label>
    </checkbox-group>
  </form>
  <view class="login-btn" bind:tap="onLogin">登录</view>
  <view class="register-btn" bind:tap="onRegister">注册</view>
</view>