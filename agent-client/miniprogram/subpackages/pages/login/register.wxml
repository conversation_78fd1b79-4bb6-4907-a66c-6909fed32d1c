<!--pages/login/register.wxml-->
<mp-form
  models="{{form}}"
  id="form"
  rules="{{rules}}"
>
  <view class="cell-title">合伙人主体类型</view>
  <mp-cells>
    <mp-checkbox-group
      prop="type"
      multi="{{false}}"
    >
      <mp-checkbox
        wx:for="{{agentType}}"
        wx:key="value"
        label="{{item.label}}"
        value="{{item.value}}"
        checked="{{item.checked}}"
      ></mp-checkbox>
    </mp-checkbox-group>
  </mp-cells>
  <view class="cell-title">合伙人信息</view>
  <mp-cells>
    <mp-cell
      show-error
      title="姓名"
      prop="name"
    >
      <input
        placeholder="请填写"
        value="{{form.name}}"
        data-field="name"
        bindinput="onFieldInput"
      />
    </mp-cell>
    <mp-cell
      show-error
      title="手机号"
      prop="phone"
    >
      <input
        placeholder="请填写"
        value="{{form.phone}}"
        data-field="phone"
        bindinput="onFieldInput"
      />
    </mp-cell>
    <mp-cell
      title="验证码"
      show-error
      prop="smsCode"
    >
      <view style="display: flex;">
        <input
          style="flex: 1 1 auto;"
          placeholder="请填写"
          value="{{form.smsCode}}"
          data-field="smsCode"
          bindinput="onFieldInput"
        />
        <view
          class="func-text sms-btn {{ disabled ? 'disabled' : ''}}"
          bind:tap="sendCode"
        >{{msgText}}</view>
      </view>
    </mp-cell>
    <mp-cell
      title="邮箱"
      show-error
      prop="email"
    >
      <input
        placeholder="请填写"
        value="{{form.email}}"
        data-field="email"
        bindinput="onFieldInput"
      />
    </mp-cell>
    <mp-cell
      title="备注"
      ext-class="top-align-cell"
    >
      <textarea
        placeholder="请描述您所在的行业或业务场景"
        value="{{form.remark}}"
        data-field="remark"
        bindinput="onFieldInput"
      ></textarea>
    </mp-cell>
  </mp-cells>
</mp-form>

<checkbox-group
  bindchange="onCheckChange"
  class="checkbox-container"
>
  <label class="checkbox-flex">
    <checkbox
      checked="{{checked}}"
      class="flex-checkbox"
    />
    <view class="login-file-text">
      已阅读并同意
      <text
        class="func-text"
        catch:tap="goFile"
        data-url="../../pages/file/privacy"
      >《智享汇隐私政策》、</text><text
        class="func-text"
        catch:tap="goFile"
        data-url="../../pages/file/service"
      >《智享汇平台服务协议》</text>
    </view>
  </label>
</checkbox-group>

<view class="register-btn-container">
  <view
    class="register-btn"
    bind:tap="onSubmit"
  >注册</view>
</view>