import { sendSmsCode, userRegister } from "../../../api/common"
import { setToken } from "../../../utils/token"
let timer: any
// pages/login/register.ts
Page({

  /**
   * 页面的初始数据
   */
  data: {
    agentType: [
      { label: '个人', value: 1, checked: true }
    ],
    form: {
      type: 1,
      phone: '',
    },
    checked: false,
    rules: [
      { name: 'name', rules: { message: '请输入姓名', required: true, } },
      { name: 'phone', rules: { message: '请输入手机号', required: true, } },
      { name: 'smsCode', rules: { message: '请输入验证码', required: true, } },
      { name: 'email', rules: { message: '请输入邮箱', required: true, } },
    ],
    disabled: false,
    msgText: '获取验证码',
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },
  onFieldInput(e: WechatMiniprogram.Input) {
    const { field } = e.target.dataset
    const value = e.detail.value
    this.setData({
      [`form.${field}`]: value
    })
  },
  onCheckChange() {
    this.setData({
      checked: !this.data.checked
    })
  },
  goFile(e: WechatMiniprogram.BaseEvent) {
    const {dataset: {url}} = e.target
    wx.navigateTo({
      url
    })
  },
  async sendCode() {
    if (this.data.disabled) {
      return
    }
    if (!this.data.form.phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }
    this.setData({
      disabled: true
    })
    await sendSmsCode({ phone: this.data.form.phone, type: 2 })
    this.changeCount(60)

  },
  changeCount(second: number) {
    if (second > 0) {
      this.setData({
        count: second,
        msgText: `${second}秒后重新获取`
      })
      timer = setTimeout(() => {
        this.changeCount(second - 1)
      }, 1000);
    } else {
      this.resetTimer()
      this.setData({
        msgText: '重新获取',
        disabled: false,
      })
    }
  },
  resetTimer() {
    timer && clearTimeout(timer) && (timer = null)
  },
  validateForm() {
    return new Promise((resolve, reject) => {
      let form = this.selectComponent('#form')
      form.validate((valid: any, error: any) => {
        if (!valid) {
          wx.showToast({
            title: '请输入正确信息',
            icon: 'error'
          })
          reject(error)
        } else {
          resolve(valid)
        }
      })
    })
  },
  async onSubmit() {
    await this.validateForm()
    if (!this.data.checked) {
      wx.showToast({
        title: '请阅读相关协议',
        icon: 'error'
      })
      return
    }
    this.handleRegister()
  },
  async handleRegister() {
    const {data} = await userRegister({
      ...this.data.form,
      agentType: 100,
    })
    wx.showToast({
      title: data,
      icon: 'success',
      duration: 1500
    })
    setToken()
    setTimeout(() => {
      wx.redirectTo({
        url: '../login/index'
      })
    }, 1500)
  }
})