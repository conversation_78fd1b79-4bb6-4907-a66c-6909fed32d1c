import { getUserKey, sendSmsCode, userLogin } from "../../../api/common"
import encrypt from "../../../utils/encrypt"
import {  setToken } from "../../../utils/token"

// pages/login/index.ts
let timer: any = null
const wait = 60
Page({

  /**
   * 页面的初始数据
   */
  data: {
    msgText: '获取验证码',
    loginType: 0,
    checked: false,
    count: 60,
    disabled: false,

    form: {
      phone: '',
      pwd: '',
      smsCode: '',
    },
    loading: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    this.resetTimer()
  },

  async onCodeClick() {
    if (this.data.disabled) {
      return
    }
    if (!this.data.form.phone) {
      wx.showToast({title: '请输入手机号', icon: 'error'})
      return
    }
    this.setData({
      disabled: true
    })
    await sendSmsCode({phone: this.data.form.phone, type: 1})
    this.changeCount(wait)
  },
  changeCount(second: number) {
    if (second > 0) {
      this.setData({
        count: second,
        msgText: `${second}秒后重新获取`
      })
      timer = setTimeout(() => {
        this.changeCount(second - 1)
      }, 1000);
    } else {
      this.resetTimer()
      this.setData({
        msgText: '重新获取',
        disabled: false,
      })
    }
  },
  resetTimer() {
    timer && clearTimeout(timer) && (timer = null)
  },
  changeLoginType() {
    this.setData({
      count: wait,
      loginType: this.data.loginType === 1 ? 0: 1
    })
  },


  onInput(e: WechatMiniprogram.Input) {
    const {dataset} = e.target
    const formKey = `form.${dataset.field}`
    this.setData({
      [formKey]: e.detail.value
    })
  },
  goFile(e: WechatMiniprogram.BaseEvent) {
    const {dataset: {url}} = e.target
    wx.navigateTo({
      url
    })
  },
  onCheckChange() {
    this.setData({
      checked: !this.data.checked
    })
  },
  async onLogin() {
    if (this.data.loading) {
      wx.showToast({
        title: '登录中',
        icon: 'loading'
      })
      return
    }
    let valid = false
    if (this.data.loginType === 0) {
      valid = Boolean(this.data.form.phone && this.data.form.pwd)
      if (!valid) {
        wx.showToast({
          title: '请输入' + (this.data.form.phone ? '密码' : '账号'),
          icon: 'error'
        })
        return
      }
    } else {
      valid = Boolean(this.data.form.phone && this.data.form.smsCode)
      if (!valid) {
        wx.showToast({
          title: '请输入' + (this.data.form.phone ? '验证码' : '账号'),
          icon: 'error'
        })
        return
      }
    }
    if (!this.data.checked) {
      wx.showToast({
        title: '请阅读协议',
        icon: 'none'
      })
      return
    }
    this.setData({loading: true})
    wx.showLoading({
      title: '登录中'
    })
    setToken()
    // 微信登录
    const { code } = await wx.login()
    // 获取公钥加密
    const publicKey = await this.getUserKey(this.data.form.phone)
    const encryptData = encrypt(publicKey, 
      this.data.loginType == 0 ? this.data.form.pwd : this.data.form.smsCode)
    if (encryptData) {
      const loginForm: any = {
        loginType: this.data.loginType,
        phone: this.data.form.phone,
        code
      }
      if (this.data.loginType == 0) {
        loginForm.pwd = encryptData
      } else {
        loginForm.smsCode = encryptData
      }
      const {data: {token, agentOperatorVO, agentNos}} = await userLogin(loginForm).finally(() => {
        this.setData({
          loading: false
        })
        wx.hideLoading()
      })
      // 设置用户信息
      const app = getApp()
      app.globalData.accountInfo = {
        agentOperatorVO,
        agentNos,
      }
      console.log(app.globalData.accountInfo)
      setToken(token)
      wx.setStorageSync("access_operator", agentOperatorVO)
      wx.navigateTo({
        url: '../../../subpackages/pages/account/select'
      })
    }
  },
  async getUserKey(phone: string) {
    const {data: {publicKey}} = await getUserKey({
      phone
    })
    return publicKey
  },
  onRegister() {
    wx.navigateTo({
      url: '../../../subpackages/pages/login/register'
    })
  },
})