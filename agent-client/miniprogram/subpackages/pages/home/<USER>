import { recommendCustom } from "../../api/recommend"
Page({
    data: {
      customerName:"",
      customerMobile:"",
      customerEmail:"",
      customerCompany:"",
      customerPosition:"",
      createNo:null,
      recBanner:"../../../../subpackages/assets/img/u23.svg",

      nameErrorMsg:'',
      mobileErrorMsg:'',
      emailErrorMsg:'',
      companyErrorMsg:'',
      positionErrorMsg:''
    },
    onLoad: function () {
        console.log()
    },

    fieldFocusAndBlur(e:any){
      console.log(e)
      const fieldname = e.currentTarget.dataset['fieldname']
      console.log(fieldname)
      if(e.type=='focus'){
        switch(fieldname){
          case 'customerName': {
            this.setData({nameErrorMsg:''})
            break
          }
          case 'customerMobile':{
            this.setData({mobileErrorMsg:''})
            break
          }
          case 'customerEmail':{
            this.setData({emailErrorMsg:''})
            break
          }
          case 'customerCompany':{
            this.setData({companyErrorMsg:''})
            break
          }
          case 'customerPosition':{
            this.setData({positionErrorMsg:''})
            break
          }

        }
      }else{

        switch(fieldname){
          case 'customerName': {
            if(this.data.customerName==''){
              this.setData({nameErrorMsg:'姓名不能为空'})
            }
            break
          }
          case 'customerMobile':{
            if(this.data.customerMobile==''){
              this.setData({mobileErrorMsg:'手机号不能为空'})
            }
            break
          }
          case 'customerEmail':{
            if(this.data.customerEmail==''){
              this.setData({emailErrorMsg:'邮箱不能为空'})
            }
            break
          }
          case 'customerCompany':{
            if(this.data.customerCompany==''){
              this.setData({companyErrorMsg:'公司不能为空'})
            }
            break
          }
          case 'customerPosition':{
            if(this.data.customerPosition==''){
              this.setData({positionErrorMsg:'职位不能为空'})
            }
            break
          }
          default:{
            break
          }

        }
      }



    },
    submit(){

      if(this.data.customerName==''
      || this.data.customerMobile==''
      || this.data.customerEmail==''
      || this.data.customerCompany==''
      || this.data.customerPosition==''
      ){
        const fieldNames = ['customerName','customerMobile','customerEmail','customerCompany','customerPosition']
        fieldNames.forEach(fieldName=>{
          this.fieldFocusAndBlur({
            type:'blur',
            currentTarget:{
              dataset:{fieldname:fieldName}
            }
          })
        })
        return
      }

      const param = {
        contactName:this.data.customerName,
        contactMobile:this.data.customerMobile,
        contactEmail:this.data.customerEmail,
        companyName:this.data.customerCompany,
        contactPosition:this.data.customerPosition,
        createNo:this.data.createNo
      }
      const that = this
      recommendCustom(param).then(res=>{
        if(res.code == 20000){
          that.setData({createNo:res.data.createNo})
           wx.showModal({
             title:'提交成功',
             content:'点击确定返回主页，点击取消停留在本页，可修改后再提交',
             icon:'success',
             success (res) {
              if (res.confirm) {
                wx.navigateBack()
              } else if (res.cancel) {
                return
              }
            }
           })
           
        }
      })
    }
});