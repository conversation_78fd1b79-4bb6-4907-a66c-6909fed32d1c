import { queryMonthBillList } from "../../api/account"
import { formatMoney, getPx, getRpx } from "../../../utils/util"

// pages/home/<USER>
Page({

  /**
   * 页面的初始数据
   */
  data: {
    total: {},
    list: [],
    tableHeader: [
      {
        prop: 'billDate',
        label: '账单月份',
        width: 150,
      },
      {
        prop: 'agentMonthProfit',
        label: '交易分润',
        width: 150,
      },
      {
        prop: 'settlementAmount',
        label: '结算金额',
        width: 150,
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    wx.nextTick(this.initWidth)
  },
  
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    
  },
  
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.queryMonthBillList()
  },

  initWidth() {
    const info = wx.getSystemInfoSync()
    const windowWidth = info.windowWidth - Math.floor(getPx(20))
    const itemWidth = Math.floor(windowWidth / this.data.tableHeader.length)
    this.data.tableHeader.forEach((_, index) => {
      this.setData({
        ['tableHeader[' + index + '].width']: getRpx(itemWidth)
      })
    })
  },
  async onRowClick(e){
    console.log(e)
  },
  async queryMonthBillList() {
    const { data: {total, list} } = await queryMonthBillList()
    list.forEach((item:any) => {
      item.agentMonthProfit = formatMoney(item.agentMonthProfit)
      item.settlementAmount = formatMoney(item.settlementAmount)
    })
    this.setData({
      total,
      list
    })
  },
  showMore(e) {
    console.log(e.detail)
  }
})