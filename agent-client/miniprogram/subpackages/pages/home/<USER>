import { createUrl } from "../../api/account"
import { drawWechatQrcode } from "../../../utils/qrcode"
import { buildParams } from "../../../utils/util"

// pages/home/<USER>
Page({

  /**
   * 页面的初始数据
   */
  data: {
    url: '',
    imgUrl: '',
    showCodeCanvas: true,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    await this.createUrl()
    this.drawCode()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },
  async createUrl(){
    const {data} = await createUrl()
    this.setData({
      url: data
    })
  },
  drawCode() {
    const query = wx.createSelectorQuery()
    query.select('#qrcode').fields({
      node: true,
      size: true
    }).exec((res) => {
      const canvas = res[0].node
      drawWechatQrcode({
        canvas,
        width: 150,
        height: 150,
        canvasId: 'qrcode',
        text: this.data.url,
      })
      wx.nextTick(this.codeToImg)
    })
  },
  codeToImg() {
    const query = wx.createSelectorQuery()
    query.select('#qrcode')
      .fields({ node: true, size: true })
      .exec((res) => {
        const canvas = res[0].node
        if (canvas) {
          this.setData({
            imgUrl: canvas.toDataURL(),
            showCodeCanvas: false,
          })
        }
      })
  },

  goToUrl() {
    wx.navigateTo({
      url: '../../../subpackages/pages/common/web' + buildParams({
        url: encodeURIComponent(this.data.url)
      })
    })
  },
  copyUrl() {
    wx.setClipboardData({
      data: this.data.url
    })
  }
})