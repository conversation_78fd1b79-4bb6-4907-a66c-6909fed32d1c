/* pages/home/<USER>/
.merchant-list {
  padding: 30rpx;
}
.merchant-item {
  margin-bottom: 30rpx;
  padding: 30rpx 40rpx;
  border-radius: 20rpx;
  background: #fff;
}
.merchant-item-name {
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
}
.merchant-info-item {
  display: flex;
  margin: 10rpx 0;
}
.merchant-info-label {
  width: 240rpx;
  color: var(--main-text-gray);
}
.merchant-info-content {
  flex: 1 0;
  display: flex;
  color: var(--main-text-black);
}
.func-text {
  flex: 1 0;
}
.merchant-info-detail {
  color: var(--main-text-gray);
}