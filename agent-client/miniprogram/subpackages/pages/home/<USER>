/* pages/home/<USER>/
.relation-type-list {
  display: flex;
  margin: 30rpx;
}
.relation-type-item {
  background: #fff;
  margin-right: 40rpx;
  padding: 10rpx 40rpx;
  border: 2rpx solid currentColor;
  color: #7F7F7F;
  border-radius: 14rpx;
  overflow: hidden;
}
.relation-type-item.active {
  color: var(--main-blue);
}



.relation-merchant-list {
  margin: 30rpx 20rpx;
}
.merchant-item {
  margin-bottom: 30rpx;
  padding: 30rpx 40rpx;
  border-radius: 20rpx;
  background: #fff;
}
.merchant-item-name {
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
}
.merchant-info-item {
  display: flex;
  margin: 10rpx 0;
}
.merchant-info-label {
  width: 200rpx;
  color: var(--main-text-gray);
}
.merchant-info-content {
  color: var(--main-text-black);
}