import { listAgent } from "../../api/account"
import { buildParams } from "../../../utils/util"

// pages/home/<USER>
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.listAgent()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  goAgentDetail(e: WechatMiniprogram.BaseEvent) {
    wx.navigateTo({
      url: '../../../subpackages/pages/home/<USER>' + buildParams({
        isSub: true,
        agentNo: e.currentTarget.dataset.agent || ''
      })
    })
  },
  async listAgent() {
    const { data: { data } } = await listAgent({
      pageCurrent: 1,
      pageSize: 100,
    })
    this.setData({
      list: data
    })
  }
})