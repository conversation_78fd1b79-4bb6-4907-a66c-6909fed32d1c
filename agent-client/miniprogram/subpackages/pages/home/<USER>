<!--pages/home/<USER>
<view class="merchant-list">
  <view wx:if="{{list.length > 0}}">
    <view
      class="merchant-item"
      wx:for="{{list}}"
      wx:key="index"
    >
      <view class="merchant-item-name">{{ item.agentName }}</view>
      <view class="merchant-item-info">
        <view class="merchant-info-item">
          <view class="merchant-info-label">合伙人编号</view>
          <view class="merchant-info-content">{{ item.agentNo }}</view>
        </view>
        <view class="merchant-info-item">
          <view class="merchant-info-label">类型</view>
          <view class="merchant-info-content">
            {{ DictData['AgentTypeEnum'][item.agentType] }}</view>
        </view>
        <view class="merchant-info-item">
          <view class="merchant-info-label">状态</view>
          <view class="merchant-info-content">
            {{ DictData['AgentStatusEnum'][item.agentStatus]}}
          </view>
        </view>
        <view class="merchant-info-item">
          <view class="merchant-info-label">关联商户数量</view>
          <view
            class="merchant-info-content"
            bind:tap="goAgentDetail"
            data-agent="{{item.agentNo}}"
          >
            <view class="func-text">{{ item.merNum}}</view>
            <view class="merchant-info-detail">详情 ></view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <empty wx:else></empty>
</view>