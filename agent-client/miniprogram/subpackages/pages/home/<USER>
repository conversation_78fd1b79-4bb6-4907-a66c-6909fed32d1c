<!--pages/home/<USER>
<view
  class="relation-type-list"
  wx:if="{{!isSub}}"
>
  <view
    class="relation-type-item {{ active === item.type ? 'active' : ''}}"
    wx:for="{{typeList}}"
    wx:key="index"
    data-type="{{item.type}}"
    bind:tap="onChangeType"
  >
    {{item.label}}
  </view>
</view>

<view class="relation-merchant-list" wx:if="{{list.length > 0}}">
  <view
    class="merchant-item"
    wx:for="{{list}}"
    wx:key="index"
  >
    <view class="merchant-item-name">{{ item.mchName }}</view>
    <view class="merchant-item-info">
      <view class="merchant-info-item">
        <view class="merchant-info-label">商户编号</view>
        <view class="merchant-info-content">{{item.mchNo}}</view>
      </view>
      <view class="merchant-info-item">
        <view class="merchant-info-label">所属合伙人</view>
        <view class="merchant-info-content">{{ item.agentName }}</view>
      </view>
      <view class="merchant-info-item">
        <view class="merchant-info-label">邀请关系</view>
        <view class="merchant-info-content">
          {{ DictData['RelationTypeEnum'][item.relationType]}}</view>
      </view>
      <view class="merchant-info-item">
        <view class="merchant-info-label">商户状态</view>
        <view class="merchant-info-content">
          {{ DictData['MchStatusEnum'][item.mchStatus]}}
        </view>
      </view>
      <view class="merchant-info-item">
        <view class="merchant-info-label">创建时间</view>
        <view class="merchant-info-content">
          {{ item.createTime || '-'}}
        </view>
      </view>
      <view class="merchant-info-item">
        <view class="merchant-info-label">激活时间</view>
        <view class="merchant-info-content">
          {{ item.activeTime || '-'}}
        </view>
      </view>
    </view>
  </view>
</view>

<empty wx:else></empty>