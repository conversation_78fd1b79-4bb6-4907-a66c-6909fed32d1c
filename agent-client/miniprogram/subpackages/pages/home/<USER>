import { queryRelation } from "../../api/account"

// pages/home/<USER>
Page({

  /**
   * 页面的初始数据
   */
  data: {
    typeList: [
      {label: '全部', type: 100},
      {label: '直接', type: 101},
      {label: '间接', type: 102},
    ],
    active: 100,
    list: [],

    agentNo: '',
    isSub: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(query) {
    const app = getApp()
    this.setData({
      isSub: Bo<PERSON>an(query.isSub),
      agentNo: query.agentNo || app.globalData.agentInfo.agentNo || ''
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.queryRelation()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },
  
  onChangeType(e: WechatMiniprogram.BaseEvent) {
    const {type} = e.target.dataset
    this.setData({
      active: type
    })
    this.queryRelation()
  },
  async queryRelation() {
    const {data: { data }} = await queryRelation({
      relationType: this.data.active, 
      agentNo: this.data.agentNo,
      pageCurrent: 1,
      pageSize: 100
    })
    this.setData({
      list: data
    })
  }
})