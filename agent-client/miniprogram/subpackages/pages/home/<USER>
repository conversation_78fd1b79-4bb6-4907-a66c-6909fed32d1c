<view  class="container">
        <view class="recBanner">
                <van-image width="96%" height="320rpx" fit="scaleToFill" src="{{recBanner}}" />
        </view>

        <van-cell-group inset border>
                <van-field required
                           size="large"
                           title-width="200rpx"
                           clearable
                           maxlength="30"
                           autosize
                        label="客户姓名"
                        label-class="formField"
                        input-class="formField"
                        model:value="{{ customerName }}"
                        error-message="{{nameErrorMsg}}"
                        data-fieldName="customerName"
                        bind:focus="fieldFocusAndBlur"
                        bind:blur='fieldFocusAndBlur'
                        placeholder="请输入"
                        border="{{ true }}"
                />
                <van-field
                        required
                        clearable
                        maxlength="11"
                        size="large"
                        title-width="200rpx"
                        type="number"
                        label="手机号"
                        label-class="formField"
                        input-class="formField"
                        error-message="{{mobileErrorMsg}}"
                        data-fieldName="customerMobile"
                        bind:focus="fieldFocusAndBlur"
                        bind:blur='fieldFocusAndBlur'
                        model:value="{{ customerMobile }}"
                        placeholder="请输入"
                        border="{{ true }}"
                />

                <van-field
                        required
                        clearable
                        maxlength="50"
                        size="large"
                        title-width="200rpx"
                        label="邮箱"
                        label-class="formField"
                        input-class="formField"
                        model:value="{{ customerEmail }}"
                        error-message="{{emailErrorMsg}}"
                        data-fieldName="customerEmail"
                        bind:focus="fieldFocusAndBlur"
                        bind:blur='fieldFocusAndBlur'
                        placeholder="请输入"
                        border="{{ true }}"
                />

                <van-field
                        required
                        clearable
                        maxlength="100"
                        size="large"
                        title-width="200rpx"
                        label="所在公司"
                        label-class="formField"
                        input-class="formField"
                        model:value="{{ customerCompany }}"
                        error-message="{{companyErrorMsg}}"
                        data-fieldName="customerCompany"
                        bind:focus="fieldFocusAndBlur"
                        bind:blur='fieldFocusAndBlur'
                        placeholder="请输入"
                        border="{{ true }}"
                />

                <van-field
                        required
                        clearable
                        maxlength="100"
                        size="large"
                        title-width="200rpx"
                        label="公司职位"
                        label-class="formField"
                        input-class="formField"
                        model:value="{{ customerPosition }}"
                        error-message="{{positionErrorMsg}}"
                        data-fieldName="customerPosition"
                        bind:focus="fieldFocusAndBlur"
                        bind:blur='fieldFocusAndBlur'
                        placeholder="请输入"
                        border="{{ true }}"
                />
                <van-button  type="primary" custom-style="width:100%;margin-top:40rpx;font-size:40rpx" bind:tap="submit">提交线索</van-button>
        </van-cell-group>
        
</view>