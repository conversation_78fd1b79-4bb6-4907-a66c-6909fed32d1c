<!--pages/account/select.wxml-->

<view class="account-list-page">
  <view wx:if="{{list.length>0}}" class="account-list-title">
    请选择要登录的合伙人账号</view>

  <view class="account-list">
    <view
      class="account-list-item"
      wx:for="{{list}}"
      wx:key="index"
      bind:tap="onSelect"
      data-index="{{index}}"
    >
      <view class="account-list-text account-mchName">{{item.agentName}}</view>
      <view class="account-list-text account-mchNo">合伙人编号：{{item.agentNo}}</view>
      <view class="account-list-text account-status">合伙人状态：
        {{ DictData['AgentStatusEnum'][item.agentStatus] }}
        </view>
      <view class="account-list-text account-validate">
      资质状态：{{ DictData['AuthStatusEnum'][item.authStatus] }}
      </view>
    </view>
  </view>
</view>