/* pages/account/home.wxss */

button:after {
  content: none;
}

button::after {
  border: none;
}

.home-page {
  padding: 30rpx;
}

.home-result-box, .home-subpage-container {
  border-radius: 20rpx;
  background: #fff;
}

.home-title-list {
  display: flex;
  justify-content: space-around;
}

.home-title {
  position: relative;
  flex: 0 0 auto;
  text-align: center;
  padding: 20rpx 0;
}

.home-title.active::after {
  content: '';
  display: block;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 8rpx;
  background: var(--main-red);
}
.home-result-total {
  margin: 80rpx 0 60rpx;
}
.home-result-title {
  text-align: center;
}

.home-result-money {
  text-align: center;
  font-weight: bold;
}
.home-total-money {
  font-size: 40rpx;
}

.home-result-details {
  display: flex;
  justify-content: center;
}
.home-result-detail {
  flex: 1 0 auto;
  margin: 0 20rpx 40rpx;
  padding: 20rpx 0;
  background: var(--main-gray);
}
.home-result-money {
  margin-top: 20rpx;
}


.home-subpage-container {
  margin-top: 20rpx;
}

.home-subpage-item {
  display: flex;
  align-items: center;
  margin: 0 20rpx 0 50rpx;
  padding: 40rpx 0;
  border-bottom: 2rpx solid var(--main-gray);
}

.home-subpage-icon {
  font-size: 40rpx;
  width: 1em;
  height: 1em;
  margin-right: 30rpx;
}


.logout-btn-container {
  margin: 80rpx;
  text-align: center;
}
.logout-btn {
  padding: 20rpx 0;
  color: #f00;
  border: 2rpx solid currentColor;
  border-radius: 20rpx;
}