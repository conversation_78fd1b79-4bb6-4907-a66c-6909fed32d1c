<!--pages/account/home.wxml-->
<wxs
  module="money"
  src="./../../../wxs/money.wxs"
></wxs>

<view class="home-page">
  <view class="home-result-box">
    <view class="home-title-list">
      <view
        class="home-title {{ item.value === active ? 'active' : ''}}"
        wx:for="{{titleList}}"
        wx:key="index"
        data-value="{{item.value}}"
        bind:tap="onChange"
      >
        {{ item.label }}
      </view>
    </view>
    <view class="home-result-container">
      <view class="home-result-total">
        <view class="home-result-title">
          总实发金额
        </view>
        <view class="home-result-money home-total-money">
          {{ money.formatMoney(profit.totalNetAmount) }}</view>
      </view>
      <view class="home-result-details">
        <view class="home-result-detail">
          <view class="home-result-title">
            交易分润</view>
          <view class="home-result-money">
            {{ money.formatMoney(profit.totalTradeProfit)}}
          </view>
        </view>
        <view class="home-result-detail">
          <view class="home-result-title">
            邀请奖励</view>
          <view class="home-result-money">
            {{ money.formatMoney(profit.totalInviteReward)}}
          </view>
        </view>
      </view>
    </view>
  </view>

  <view class="home-subpage-container">
    <view class="home-subpage-list">
      <view
        class="home-subpage-item"
        wx:for="{{ subPageList }}"
        wx:key="index"
        bind:tap="goToSubPage"
        data-url="{{item.url}}"
      >
        <view class="home-subpage-icon">
          <image
            mode="widthFix"
            src="{{item.icon}}"
            alt=""
          />
        </view>
        <text wx:if="{{item.label !='在线客服' }}">{{ item.label }}</text>
        <button wx:else open-type="contact" style="width: 100%;text-align: left;font-size: 16px; background-color: white;border: none;padding-left: 0px;margin-left: 0px;" 	send-message-title="在线客服">
        {{item.label}}
        </button>
      </view>
    </view>
  </view>
</view>


<!-- <view class="logout-btn-container">
  <view class="logout-btn" bind:tap="onLogout">退出登录</view>
</view> -->