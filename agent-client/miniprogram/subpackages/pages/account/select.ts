import { selectAgent,agentList } from "../../api/account"

// pages/account/select.ts
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: [] as any[],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {
    // const app = getApp()
    // const agentNos = app.globalData.accountInfo.agentNos
    // if (agentNos) {
      // this.setData({
        // list: agentNos
      // })
    // }

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.agentList()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    wx.redirectTo({
      url:'/miniprogram/subpackages/pages/main/me/index'
    })
  },
  

  async onSelect(e: WechatMiniprogram.BaseEvent) {
    const { dataset: { index = 0 } } = e.currentTarget
    const item = this.data.list[index]
    if (item) {
      const app = getApp()
      app.globalData.agentInfo = item
      await selectAgent({
        agentNo: item.agentNo
      })
    }

    wx.redirectTo({
      url: '../account/home'
    })
  },

  agentList(){
    agentList({
      phone:wx.getStorageSync("access_operator").phone
    }).catch(res=>{
      if(res.code === 20003){
        wx.redirectTo({url:'../login/index'})
      }
    }).then(res=>{
      this.setData({
        list: res.data
      })
    })
  }
})