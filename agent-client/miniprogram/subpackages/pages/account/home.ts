import { createUrl, queryProfit } from "../../api/account"
import { userLogout } from "../../../api/common"
import { setToken } from "../../../utils/token"

// pages/account/home.ts
Page({

  /**
   * 页面的初始数据
   */
  data: {
    titleList: [
      { value: 1, label: '昨日' },
      { value: 2, label: '上周' },
      { value: 3, label: '上月' },
    ],
    profit: {},
    active: 1,

    subPageList: [
      { label: '推荐客户', icon: '../../../assets/icon/recommend.svg', url: '/subpackages/pages/home/<USER>' },
      { label: '商户关系', icon: '../../../assets/icon/relation.svg', url: '/subpackages/pages/home/<USER>' },
      { label: '合伙人管理', icon: '../../../assets/icon/agent.svg', url: '/subpackages/pages/home/<USER>' },
      { label: '业绩对账', icon: '../../../assets/icon/report.svg', url: '/subpackages/pages/home/<USER>' },
      { label: '商户入驻', icon: '../../../assets/icon/register.svg', url: '/subpackages/pages/home/<USER>' },
      { label: '产品咨询', icon: '../../../assets/icon/u92.svg', url: '/subpackages/pages/home/<USER>' },
      { label: '在线客服', icon: '../../../assets/icon/kefu.svg', url: '' },
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad() {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    wx.hideHomeButton()
    this.queryProfit()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },
  onChange(e: WechatMiniprogram.BaseEvent) {
    this.setData({
      active: e.target.dataset.value ?? 1
    })
    this.queryProfit()
  },
  async goToSubPage(e: WechatMiniprogram.BaseEvent) {
    const { url } = e.target.dataset
    if (url === '/pages/home/<USER>') {
      const res = await this.checkAgentStatus()
      if (!res) {
        wx.showModal({
          title: '合伙人未激活',
          content: '请联系客户经理添加报价单',
          showCancel: false,
        })
        return
      }
    }
    wx.navigateTo({
      url
    })
  },
  async onLogout() {
    const res = await wx.showModal({
      title: '提示',
      content: '是否要退出登录'
    })
    if (res.confirm) {
      this.handleLogout()
    }
  },
  async handleLogout() {
    await userLogout()
    setToken()
    wx.redirectTo({ url: '/miniprogram/subpackages/pages/login/index' })
  },
  /**
   * 代理商激活状态
   */
  async checkAgentStatus() {
    const { data } = await createUrl()
    if (!data) {
      return false
    }
    return true
  },
  async queryProfit() {
    const { data } = await queryProfit({ queryTimeType: this.data.active })
    this.setData({
      profit: data
    })
  }
})