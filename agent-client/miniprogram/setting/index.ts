import getVersion from "../utils/version";

const baseUrls = {
  develop: 'https://app-dev.hjzxh.com', // 开发接测试
  // develop: 'http://127.0.0.1:9093', // 开发接测试
  // develop:'https://api-backend.hjzxh.com/agent',
  trial: 'https://app-dev.hjzxh.com', // 体验版
  // trial: 'https://api-backend.hjzxh.com/agent', // 体验版
  release: 'https://api-backend.hjzxh.com/agent'// 生产环境
}

const fileBaeUrl = {
  develop: 'https://static.hjzx.tech/', // 开发接测试
  trial: 'https://static.hjzxh.com/', // 体验版
  release: 'https://static.hjzxh.com/'// 生产环境
}

export const baseUrl = baseUrls[getVersion()];

export const fileUrl = fileBaeUrl[getVersion()];