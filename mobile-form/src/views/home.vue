<script
  setup
  lang="ts">
import { onMounted, reactive, ref } from "vue";
import { showToast } from "vant";
import type { PickerConfirmEventParams } from 'vant'
import {
  createMch,
  createMchFromWxmp,
  listDict,
  listIndustryType,
  listInvoiceCategory,
  listWorkCategory,
  sendSmsCode, sendSmsCodeWxmp
} from "@/api/mch";
import { useRoute, useRouter } from "vue-router";
import { to } from "@/utils";

const form = reactive<any>({})

const submitLoading = ref(false)
const smsLoading = ref(false)
const smsBtnText = ref('点击发送')
const duration = 60
const timerRef = ref()
const dictList = ref([])
const signRateList = ref([])

const router = useRouter()

const editIndex = ref(0) // active 岗位信息

// 判断是否小程序跳转过来
const { t = '', p = '' } = useRoute().query

const createApi = t ? createMchFromWxmp : createMch

function convert(list: any[], topParentId = 0) {
  const res = [];
  const map = list.reduce((res, v) => {
    res[v.id] = v
    return res
  }, {})
  for (const item of list) {
    if (item.parentId === topParentId) {
      res.push(item)
      continue
    }
    if (item.parentId in map) {
      const parent = map[item.parentId]
      parent.children = parent.children || []
      parent.children.push(item)
    }
  }
  return res
}

const rulesRegister = (message: string, required?: boolean, options?: object): any[] => {
  return [{ required: required || true, message, trigger: 'onSubmit', ...options }]
}
// 行业类别
const showIndustryPicker = ref(false) // 行业类别弹出
const openIndustryPop = () => {
  showIndustryPicker.value = true
}
const onConfirmIndustry = ({ selectedOptions }: PickerConfirmEventParams) => {
  form.industryTypeName = selectedOptions[selectedOptions.length - 1]!.industryTypeName
  form.industryTypeCode = selectedOptions[selectedOptions.length - 1]!.industryTypeCode

  showIndustryPicker.value = false
}
// 工作场所
const showWorkPlacePicker = ref(false)
const openWorkPlacePop = (index: number) => {
  editIndex.value = index
  showWorkPlacePicker.value = true
}
const onConfirmWorkPlace = ({ selectedOptions }: PickerConfirmEventParams) => {
  form.positionVoList[editIndex.value].workplaceCode = selectedOptions[0]!.code
  form.positionVoList[editIndex.value].workplaceName = selectedOptions[0]!.desc
  showWorkPlacePicker.value = false
}

// 岗位类目
const showWorkCategoryPicker = ref(false)
const openWorkCategoryPop = (index: number) => {
  editIndex.value = index
  showWorkCategoryPicker.value = true
}
const onConfirmWorkCategory = ({ selectedOptions }: PickerConfirmEventParams) => {
  form.positionVoList[editIndex.value].workCategoryCode = selectedOptions[selectedOptions.length - 1]!.workCategoryCode
  form.positionVoList[editIndex.value].workCategoryName = selectedOptions[selectedOptions.length - 1]!.workCategoryName
  form.positionVoList[editIndex.value].serviceDesc = selectedOptions[selectedOptions.length - 1]!.workDesc
  form.positionVoList[editIndex.value].chargeRuleDesc = selectedOptions[selectedOptions.length - 1]!.chargeRuleDesc
  showWorkCategoryPicker.value = false
}
// 发票
const showInvoicePicker = ref(false)
const openInvoicePop = (index: number) => {
  editIndex.value = index
  showInvoicePicker.value = true
}
const onConfirmInvoice = ({ selectedOptions }: PickerConfirmEventParams) => {
  form.positionVoList[editIndex.value].invoiceCategoryList = [{
    invoiceCategoryCode: selectedOptions[selectedOptions.length - 1]!.invoiceCategoryCode,
    invoiceCategoryName: selectedOptions[selectedOptions.length - 1]!.invoiceCategoryName,
  }]
  form.positionVoList[editIndex.value]._invoiceCategoryName = selectedOptions[selectedOptions.length - 1]!.invoiceCategoryName
  showInvoicePicker.value = false
}

// 签署区间
const showSignRatePicker = ref(false)
const openSignRatePop = () => {
  showSignRatePicker.value = true
}
const onConfirmSignRate = ({ selectedOptions }: PickerConfirmEventParams) => {
  form.signRateLevel = selectedOptions[0]!.code
  form.signRateLevelName = selectedOptions[0]!.desc
  showSignRatePicker.value = false
}

const getDict = async () => {
  const { data } = await listDict()
  dictList.value = data.find((item: any) => item.dataName === 'WorkPlaceEnum').dataInfo
  signRateList.value = data.find((item: any) => item.dataName === 'SignRateLevelEnum').dataInfo
}
const init = () => {
  getIndustryType()
  getWorkCategory()
  getInvoiceCategory()
  // 获取字典
  getDict()
}

onMounted(() => {
  init()
  form.positionVoList = [{}]
})

const addWork = () => {
  form.positionVoList.push({})
}
const deleteWork = (index: number) => {
  form.positionVoList.splice(index, 1)
}

const onSubmit = async () => {
  submitLoading.value = true
  let createForm: object
  if (t) {
    // 小程序请求
    createForm = { participant: {}, condition: {}, extObj: form }
  } else {
    createForm = form
  }
  const [err, resp] = await to(createApi(createForm, { t, p }))
  submitLoading.value = false
  if (err) return
  const { data: { contactPhone } } = resp
  router.push(
    {
      path: '/result',
      query: {
        phone: contactPhone,
        t
      }
    })
}
const onSubmitFail = () => {
  showToast('请完善信息')
}

const sendSms = async (e: Event) => {
  if (smsLoading.value || !/1\d{10}$/.test(form.contactPhone)) {
    return showToast('请输入格式正确的手机')
  }
  e.stopPropagation()
  if (t) {
    // 小程序
    await sendSmsCodeWxmp({ phone: form.contactPhone, type: 3 })
  } else {
    await sendSmsCode({ contactPhone: form.contactPhone })
  }
  showToast('发送成功')
  changeBtn(duration)
}

const changeBtn = (time: number) => {
  if (time > 0) {
    smsLoading.value = true
    smsBtnText.value = time + '秒后重新发送'
    timerRef.value = setTimeout(() => {
      changeBtn(time - 1)
    }, 1000)
  } else {
    smsLoading.value = false
    timerRef.value && clearTimeout(timerRef.value)
    smsBtnText.value = '重新发送'
  }
}

let industryTypeList: any[], industryOption: any[], workCategoryList: any[], workCategoryOption: any[]
const getIndustryType = async () => {
  const { data } = await listIndustryType()
  industryTypeList = data
  industryOption = convert(industryTypeList, 0)
}

const getWorkCategory = async () => {
  const { data } = await listWorkCategory()
  workCategoryList = data
  workCategoryOption = convert(workCategoryList, 0)
}

let invoiceCategoryList: any[]
const getInvoiceCategory = async () => {
  const { data } = await listInvoiceCategory()
  invoiceCategoryList = data
}

</script>

<template>
  <main>
    <div class="form-banner">
      <img
        src="@/assets/img/banner.png"
        alt="banner">
    </div>
    <van-form
      class="form"
      @submit="onSubmit"
      @failed="onSubmitFail">
      <div class="form-group-header flex">
        <div class="form-group-title flex-main">企业信息</div>
      </div>
      <van-cell-group>
        <van-field
          v-model="form.mchName"
          label="企业名称"
          placeholder="请填写"
          :rules="rulesRegister('请填写企业名称')"></van-field>
        <van-field
          v-model="form.contactName"
          label="负责人姓名"
          placeholder="请填写"
          :rules="rulesRegister('请填写负责人姓名')"></van-field>
        <van-field
          v-model="form.contactPhone"
          label="负责人手机号"
          type="digit"
          placeholder="请填写"
          :rules="rulesRegister('请填写负责人手机号', true, {pattern: /1\d{10}$/})"></van-field>
        <van-field
          v-model="form.smsCode"
          label="验证码"
          placeholder="请填写"
          :rules="rulesRegister('请填写验证码')">
          <template #button>
            <div
              class="func-btn"
              :class="{'inactive': smsLoading}"
              @click="sendSms">{{ smsBtnText }}
            </div>
          </template>
        </van-field>
        <van-field
          v-model="form.contactEmail"
          label="负责人邮箱"
          placeholder="请填写"
          :rules="rulesRegister('请填写负责人邮箱')"></van-field>
      </van-cell-group>
      <template
        v-for="(item, index) in form.positionVoList"
        :key="index">
        <div class="form-group-header flex">
          <div class="form-group-title flex-main">岗位信息</div>
          <span
            v-if="index === form.positionVoList.length - 1"
            class="func-btn flex-shrink"
            @click="addWork">增加岗位</span>
          <span
            v-if="form.positionVoList.length > 1"
            class="warning-btn flex-shrink"
            style="margin-left: 10px;"
            @click="deleteWork(index)">删除</span>
        </div>
        <van-cell-group>
          <van-field
            v-model="item.workplaceName"
            label="工作场所"
            is-link
            placeholder="请选择"
            readonly
            @click="openWorkPlacePop(index)"
            :rules="rulesRegister('请选择工作场所')"></van-field>
          <van-field
            v-model="item.workCategoryName"
            label="岗位类目"
            is-link
            placeholder="请选择"
            readonly
            @click="openWorkCategoryPop(index)"
            :rules="rulesRegister('请选择岗位类目')"></van-field>
          <van-field
            v-model="item._invoiceCategoryName"
            label="开票类目"
            is-link
            placeholder="请选择"
            readonly
            @click="openInvoicePop(index)"
            :rules="rulesRegister('请选择开票类目')"></van-field>
          <van-field
            v-model="item.serviceDesc"
            label="岗位服务描述"
            type="textarea"
            rows="2"
            placeholder="为使落地供应商匹配到合适的自由职业者请按实际描述个人具体需要做什么"
            autosize
            :rules="rulesRegister('请输入岗位服务描述')"></van-field>
          <van-field
            v-model="item.chargeRuleDesc"
            label="岗位计费规则"
            type="textarea"
            rows="2"
            placeholder="为吸引合适的自由职业者进行匹配，请按实际填写个人完成服务后钱怎么算；如规则的数据范围有差异，请列明确定不同规则的依据"
            autosize
            :rules="rulesRegister('请输入岗位计费规则')"></van-field>
        </van-cell-group>
      </template>
      <div class="form-group-header flex">
        <div class="form-group-title flex-main">合作信息</div>
      </div>
      <van-cell-group>
        <van-field
          v-model="form.industryTypeName"
          label="企业行业类别"
          is-link
          placeholder="请选择"
          readonly
          @click="openIndustryPop"
          :rules="rulesRegister('请选择企业行业类别')"></van-field>
        <van-field
          v-model="form.workerNum"
          label="预计用工人数"
          type="digit"
          placeholder="单次项目所需用工人数"
          :rules="rulesRegister('请输入预计用工人数')"></van-field>
        <van-field
          v-model="form.signRateLevelName"
          label="C端签署率"
          is-link
          placeholder="自由职业者可签署协议的比例"
          readonly
          @click="openSignRatePop"
          :rules="rulesRegister('请选择C端签署率')"></van-field>
        <van-field
          v-model="form.monthMoneySlip"
          label="月流水预估（万元）"
          type="number"
          placeholder="请输入"
          :rules="rulesRegister('请输入月流水预估')"></van-field>
        <van-field
          label="是否可提供服务记录及经营所得计算明细"
          :rules="rulesRegister('请选择选项')">
          <template #input>
            <van-radio-group
              direction="horizontal"
              v-model="form.provideIncomeDetailType">
              <van-radio name="100">是</van-radio>
              <van-radio name="101">否</van-radio>
              <van-radio name="102">可接受抽查</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-model="form.companyWebsite"
          label="企业网站"
          placeholder="（选填）"></van-field>
        <van-field
          v-model="form.bizPlatformName"
          type="textarea"
          label="企业业务平台名称（如公众号、小程序、APP名）"
          placeholder="（选填）"></van-field>
      </van-cell-group>

      <div class="form-submit-btn">
        <van-button
          native-type="submit"
          :loading="submitLoading"
          type="primary"
          round
          block
        >提交
        </van-button>
      </div>
    </van-form>

    <!--行业类别弹窗-->
    <van-popup
      v-model:show="showIndustryPicker"
      position="bottom">
      <van-picker
        :columns="industryOption"
        :columns-field-names="{text: 'industryTypeName', value: 'industryTypeCode',children: 'children'}"
        @cancel="showIndustryPicker = false"
        @confirm="onConfirmIndustry"></van-picker>
    </van-popup>

    <!--  工作场所-->
    <van-popup
      v-model:show="showWorkPlacePicker"
      position="bottom">
      <van-picker
        :columns="dictList"
        :columns-field-names="{text: 'desc', value: 'code'}"
        @cancel="showWorkPlacePicker = false"
        @confirm="onConfirmWorkPlace"></van-picker>
    </van-popup>
    <!--岗位类目-->
    <van-popup
      v-model:show="showWorkCategoryPicker"
      position="bottom">
      <van-picker
        :columns="workCategoryOption"
        :columns-field-names="{text: 'workCategoryName', value: 'workCategoryCode', children: 'children'}"
        @cancel="showWorkCategoryPicker = false"
        @confirm="onConfirmWorkCategory"></van-picker>
    </van-popup>
    <!--发票-->
    <van-popup
      v-model:show="showInvoicePicker"
      position="bottom">
      <van-picker
        :columns="invoiceCategoryList"
        :columns-field-names="{text: 'invoiceCategoryName', value: 'invoiceCategoryCode', children: 'children'}"
        @cancel="showInvoicePicker = false"
        @confirm="onConfirmInvoice"></van-picker>
    </van-popup>
    <!--签署区间-->
    <van-popup
      v-model:show="showSignRatePicker"
      position="bottom">
      <van-picker
        :columns="signRateList"
        :columns-field-names="{text: 'desc', value: 'code'}"
        @cancel="showSignRatePicker = false"
        @confirm="onConfirmSignRate"></van-picker>
    </van-popup>

  </main>
</template>

<style
  lang="scss"
  scoped>

main {
  margin: 0 auto;
  width: 100%;
}

@media screen and (min-width: 960px) {
  main {
    width: 800px;
  }
}

.form {
  --gray-color: #f4f4f4;
  background: var(--gray-color);

  &-group-header {
    padding: 8px 16px;
    color: #666666;
  }


  &-submit-btn {
    margin: 16px;
  }
}
</style>
