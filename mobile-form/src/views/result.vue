<script
  setup
  lang="ts">
import { onMounted, ref } from "vue";
import ClipboardJS from "clipboard";
import { showToast } from "vant";
import { useRoute } from "vue-router";
import { getImg } from "@/utils";

const clipText = 'https://b.hjzxh.com/'
const clip = ref<ClipboardJS>()

const initClip = () => {
  clip.value = new ClipboardJS('#copy-btn')
  clip.value.on('success', () => {
    showToast('复制成功')
  })
}

const route = useRoute()
const { phone = '', t = '' } = route.query

onMounted(() => {
  if (!t) {
    initClip()
  }
})
</script>


<template>
  <section>
    <template v-if="!t">
      <div class="result-image">
        <img
          :src="getImg('result')"
          alt="success">
      </div>
      <h3>智享汇综合服务平台帐号开通</h3>
      <h5 class="tip">欢迎贵司使用汇聚智享平台，您的登录帐号已经开通</h5>
      <div class="result-info">
        <div class="result-info-item">综合服务平台地址：<span
          class="func-btn"
          id="copy-btn"
          :data-clipboard-text="clipText">复制链接</span></div>
        <div class="result-info-item">登录用户名（手机号）：{{ phone }}</div>
      </div>
      <h5 class="tip">请在电脑上登录，并前往【设置-商户信息】完成主体资质认证</h5>
    </template>
    <template v-else>
      <div class="result-image">
        <img
          :src="getImg('success')"
          alt="success">
      </div>
      <h5>提交成功</h5>
      <h5 style="margin: 20px 0">请等待商户入驻审批，审批通过后平台会邮件通知商户</h5>
    </template>
  </section>
</template>

<style
  lang="scss"
  scoped>
section {
  margin: 32px 32px 0;
  --gray-color: #999;
  text-align: center;

  .result-image {
    width: 30%;
    margin: 16px auto;
  }

  .title {
    font-weight: bold;
  }

  .tip {
    color: var(--gray-color);
  }

  .result-info {
    margin: 16px 0;
    padding: 12px;
    border: 1px solid var(--gray-color);
    font-size: 13px;
    text-align: left;

    &-item {
      margin: 8px 0;
    }
  }
}
</style>
