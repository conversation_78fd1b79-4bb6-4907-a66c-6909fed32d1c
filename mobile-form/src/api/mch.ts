import { get, post } from "@/utils/request";

export function createMch(data: any, params?: any) {
  return post({ url: "/anonMerchant/appNewMerchant", data, params })
}

export function sendSmsCode(data: any) {
  return post({ url: '/anonMerchant/sendHeadSmsCode', data })
}

export function listIndustryType() {
  return get({ url: '/industryType/listAll' })
}

export function listWorkCategory() {
  return get({ url: '/workCategory/listAll' })
}

export function listInvoiceCategory() {
  return get({ url: '/invoiceCategory/getAll' })
}

export function listDict() {
  return get({ url: '/portalDataDictionary/listAllDataDictionaryVO' })
}

export function createMchFromWxmp(data: any, params?: any) {
  return post({ url: "/wx/merchant/appNewMerchant", data, params, baseURL: import.meta.env.VITE_WX_BASE_API })
}

export function sendSmsCodeWxmp(data: any) {
  return post({ url: '/wx/user/sendSmsCode', data, baseURL: import.meta.env.VITE_WX_BASE_API })
}