import { createRouter, createWebHashHistory } from 'vue-router'
import Home from '@/views/home.vue'
import Result from '@/views/result.vue'

const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Home
    },
    {
      path: '/result',
      name: 'result',
      component: Result
    }
  ]
})

export default router
