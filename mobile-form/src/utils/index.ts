export function getUrlQuery(url: string, key: string): string {
  let query: any = url.split('?')[1]
  if (!query) return ''
  query = query.split('&')
  const reg = new RegExp('^' + key + '=')
  const result = query.filter((item: string) => {
    return reg.test(item)
  })
  if (!result) return ''
  return result[0].split('=')[1]
}

type ToResolveItem = null | any
type ToResolveType = [ToResolveItem, ToResolveItem]

export function to(promise: Promise<any>): Promise<ToResolveType> {
  return new Promise((resolve, reject) => {
    promise
      .then(res => resolve([null, res]))
      .catch(err => resolve([err, null]))
  })
}


export function getImg(name: string) {
  return new URL(`../assets/img/${name}.png`, import.meta.url).href
}