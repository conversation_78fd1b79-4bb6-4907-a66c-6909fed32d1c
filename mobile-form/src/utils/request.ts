import axios from "axios";
import type { AxiosRequestConfig } from "axios"
import { showToast } from "vant";

export interface Response {
  code: number
  message?: string
  data?: any
}

const instance = axios.create({
  baseURL: import.meta.env.VITE_BASE_API,
  timeout: 30000,
})


instance.interceptors.response.use(
  response => {
    const res = response.data

    if (res.code !== 20000) {
      res.message && showToast(res.message)
      return Promise.reject(new Error(res.message || 'Error'))
    } else {
      return res;
    }
  },
  error => {
    console.log('err' + error) // for debug
    showToast(error.message)
    return Promise.reject(error)
  }
)

function request(option: AxiosRequestConfig): Promise<Response> {

  return instance.request(option)
}

export function post(option: AxiosRequestConfig): Promise<Response> {
  return request({
    ...option,
    method: 'post'
  })
}

export function get(option: AxiosRequestConfig): Promise<Response> {
  return request({
    ...option,
    method: 'get'
  })
}