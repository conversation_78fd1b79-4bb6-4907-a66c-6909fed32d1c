{"name": "-mobile-form", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --mode development", "dev:test": "vite --mode test", "dev:prod": "vite --mode production", "preview": "vite preview", "build:dev": "vite build --mode development", "build:test": "vite build --mode test", "build": "vite build --mode production", "type-check": "vue-tsc --noEmit"}, "dependencies": {"axios": "^1.3.5", "clipboard": "^2.0.11", "vant": "^4.1.2", "vue": "^3.2.47", "vue-router": "^4.1.6"}, "devDependencies": {"@types/node": "^18.14.2", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-vue-jsx": "^3.0.0", "@vue/tsconfig": "^0.1.3", "npm-run-all": "^4.1.5", "sass": "^1.62.0", "typescript": "~4.8.4", "unplugin-vue-components": "^0.24.1", "vite": "^4.1.4", "vue-tsc": "^1.2.0"}}